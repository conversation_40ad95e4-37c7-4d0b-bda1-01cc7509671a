{"version": 3, "sources": ["webpack:///src/renderer/view/zczp/childPage/ccdryDj.vue", "webpack:///./src/renderer/view/zczp/childPage/ccdryDj.vue?fba8", "webpack:///./src/renderer/view/zczp/childPage/ccdryDj.vue"], "names": ["ccdryDj", "data", "dialogObj", "zw", "xm", "bm", "showDxList", "spanArr", "reverse", "activities", "showMdmenu", "dwxx", "ryid", "rwid", "djzt", "rwmc", "dlzt", "sfhg", "jcjd", "computed", "components", "mounted", "this", "$route", "query", "console", "log", "getDwxxList", "getRyzcxxjl", "methods", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "Object", "dwzc", "sent", "stop", "ryzcRK", "dxXxList", "zt", "_this2", "_callee3", "del", "list", "params", "_params", "_context3", "zczp", "code", "for<PERSON>ach", "_ref", "_callee2", "item", "_context2", "dwid", "push", "_x", "apply", "arguments", "$message", "success", "$router", "path", "_this3", "_callee4", "data1", "_context4", "item1", "zcxid", "dxmc", "zcxmc", "getSpanArr", "submit", "save", "returnSy", "go", "getCcdryxxByCcdryid", "ccdryxx", "selectCcdryxxByCcdryid", "assign_default", "JSON", "parse", "stringify_default", "i", "length", "pos", "objectSpanMethod", "_ref2", "row", "column", "rowIndex", "columnIndex", "rowspan", "scid", "colspan", "getZD", "zdList", "getRyzcxxjlZD", "nr", "undefined", "sffh", "mouseoverMdMenu", "mouseoutMenu", "watch", "handler", "newVal", "oldVal", "_this4", "dxMdIdArr", "nrIndex", "mdIndex", "indexOf", "children", "href", "scnr", "ykf", "deep", "childPage_ccdryDj", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "width", "height", "staticClass", "float", "attrs", "inline", "size", "_v", "_s", "type", "icon", "on", "click", "clear", "span-method", "border", "label", "scopedSlots", "_u", "key", "fn", "scope", "id", "$index", "model", "value", "callback", "$$v", "$set", "expression", "rows", "trim", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "yNAgHAA,wBACAC,KADA,WAEA,OACAC,WAGAC,GAAA,GACAC,GAAA,GACAC,GAAA,IAGAC,cAEAC,WAEAC,SAAA,EAEAC,cAEAC,YAAA,EACAC,QACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,EACAC,KAAA,KAGAC,YACAC,cAEAC,QAjCA,WAkCAC,KAAAT,KAAAS,KAAAC,OAAAC,MAAAX,KACAS,KAAAR,KAAAQ,KAAAC,OAAAC,MAAAV,KACAQ,KAAAV,KAAAU,KAAAC,OAAAC,MAAAZ,KACAU,KAAAN,KAAAM,KAAAC,OAAAC,MAAAR,KACAM,KAAAJ,KAAAI,KAAAC,OAAAC,MAAAN,KACAO,QAAAC,IAAA,YAAAJ,KAAAT,MACAS,KAAApB,UAAAC,GAAAmB,KAAAC,OAAAC,MAAArB,GACAmB,KAAApB,UAAAE,GAAAkB,KAAAC,OAAAC,MAAApB,GACAkB,KAAApB,UAAAG,GAAAiB,KAAAC,OAAAC,MAAAnB,GACAiB,KAAAP,KAAAO,KAAAC,OAAAC,MAAAT,KACAO,KAAAK,cAUAL,KAAAM,eAOAC,SAEAF,YAFA,WAEA,IAAAG,EAAAR,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAlC,EAAA,OAAA+B,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAvC,EADAoC,EAAAK,KAEAjB,QAAAC,IAAAzB,GACA6B,EAAAnB,KAAAV,EAHA,wBAAAoC,EAAAM,SAAAR,EAAAL,KAAAC,IAMAa,OARA,SAQAC,EAAAC,GAAA,IAAAC,EAAAzB,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAc,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAApB,EAAAC,EAAAG,KAAA,SAAAiB,GAAA,cAAAA,EAAAf,KAAAe,EAAAd,MAAA,cACAU,GACApC,KAAAkC,EAAAlC,KACAD,KAAAmC,EAAAnC,MAHAyC,EAAAd,KAAA,EAKAC,OAAAc,EAAA,EAAAd,CAAAS,GALA,UAMA,KANAI,EAAAX,KAMAa,KANA,CAAAF,EAAAd,KAAA,gBAOAd,QAAAC,IAAAmB,GACApB,QAAAC,IAAAoB,GACAI,KACAL,EAAAW,QAAA,eAAAC,EAAA1B,IAAAC,EAAAC,EAAAC,KAAA,SAAAwB,EAAAC,GAAA,OAAA3B,EAAAC,EAAAG,KAAA,SAAAwB,GAAA,cAAAA,EAAAtB,KAAAsB,EAAArB,MAAA,OACAoB,EAAAE,KAAAd,EAAApC,KAAAkD,KACAF,EAAA/C,KAAAmC,EAAAnC,KACA+C,EAAA9C,KAAAkC,EAAAlC,KACA8C,EAAAvD,GAAA2C,EAAA7C,UAAAE,GACA8C,EAAAY,KAAAH,GALA,wBAAAC,EAAAjB,SAAAe,EAAAX,MAAA,gBAAAgB,GAAA,OAAAN,EAAAO,MAAA1C,KAAA2C,YAAA,IAVAZ,EAAAd,KAAA,GAiBAC,OAAAc,EAAA,EAAAd,CAAAU,GAjBA,WAkBA,KAlBAG,EAAAX,KAkBAa,KAlBA,CAAAF,EAAAd,KAAA,YAmBA,GAAAO,EAnBA,CAAAO,EAAAd,KAAA,gBAqBAQ,EAAAmB,SAAAC,QAAA,UACAhB,GACArC,KAAA,EACAF,KAAAmC,EAAAnC,KACAC,KAAAkC,EAAAlC,MAzBAwC,EAAAd,KAAA,GA2BAC,OAAAc,EAAA,EAAAd,CAAAW,GA3BA,QA4BA,KA5BAE,EAAAX,KA4BAa,OACA,GAAAR,EAAA/B,KACA+B,EAAAqB,QAAAN,MACAO,KAAA,SACA7C,OACAX,KAAAkC,EAAAlC,KACAE,KAAAgC,EAAAhC,KACAG,KAAA6B,EAAA7B,QAGA,GAAA6B,EAAA/B,MACA+B,EAAAqB,QAAAN,MACAO,KAAA,QACA7C,OACAX,KAAAkC,EAAAlC,KACAE,KAAAgC,EAAAhC,KACAG,KAAA6B,EAAA7B,SA5CAmC,EAAAd,KAAA,oBAkDA,GAAAO,EAlDA,CAAAO,EAAAd,KAAA,gBAmDAQ,EAAAmB,SAAAC,QAAA,QACAf,GACAtC,KAAA,EACAF,KAAAmC,EAAAnC,KACAC,KAAAkC,EAAAlC,MAvDAwC,EAAAd,KAAA,GAyDAC,OAAAc,EAAA,EAAAd,CAAAY,GAzDA,QA0DA,KA1DAC,EAAAX,KA0DAa,OACA,GAAAR,EAAA/B,KACA+B,EAAAqB,QAAAN,MACAO,KAAA,SACA7C,OACAX,KAAAkC,EAAAlC,KACAE,KAAAgC,EAAAhC,KACAG,KAAA6B,EAAA7B,QAGA,GAAA6B,EAAA/B,MACA+B,EAAAqB,QAAAN,MACAO,KAAA,QACA7C,OACAX,KAAAkC,EAAAlC,KACAE,KAAAgC,EAAAhC,KACAG,KAAA6B,EAAA7B,SA1EA,yBAAAmC,EAAAV,SAAAK,EAAAD,KAAAhB,IAgHAH,YAxHA,WAwHA,IAAA0C,EAAAhD,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAqC,IAAA,IAAAtE,EAAAuE,EAAArB,EAAA,OAAAnB,EAAAC,EAAAG,KAAA,SAAAqC,GAAA,cAAAA,EAAAnC,KAAAmC,EAAAlC,MAAA,cAAAkC,EAAAlC,KAAA,EACAC,OAAAc,EAAA,EAAAd,GADA,UACAvC,EADAwE,EAAA/B,KAEA8B,KACA,GAAAF,EAAAxD,KAHA,CAAA2D,EAAAlC,KAAA,gBAAAkC,EAAAlC,KAAA,EAIAC,OAAAc,EAAA,EAAAd,GAJA,OAIAgC,EAJAC,EAAA/B,KAAA+B,EAAAlC,KAAA,oBAMA,GAAA+B,EAAAxD,KANA,CAAA2D,EAAAlC,KAAA,gBAOAY,GACAtC,KAAAyD,EAAAzD,KACAD,KAAA0D,EAAA1D,MATA6D,EAAAlC,KAAA,GAWAC,OAAAc,EAAA,EAAAd,CAAAW,GAXA,QAWAqB,EAXAC,EAAA/B,KAAA,QAaA8B,EAAAhB,QAAA,SAAAG,GACA1D,EAAAuD,QAAA,SAAAkB,GACAA,EAAAC,OAAAhB,EAAAgB,QACAhB,EAAAiB,KAAAF,EAAAG,WAIAP,EAAAhE,WAAAkE,EACAF,EAAA/D,QAAA+D,EAAAQ,WAAAN,GACA/C,QAAAC,IAAA4C,EAAAhE,YACAmB,QAAAC,IAAAzB,GACAwB,QAAAC,IAAA8C,GAxBA,yBAAAC,EAAA9B,SAAA4B,EAAAD,KAAAvC,IA2BAgD,OAnJA,WAoJAzD,KAAAsB,OAAAtB,KAAAhB,WAAA,IAEA0E,KAtJA,WAuJA1D,KAAAsB,OAAAtB,KAAAhB,WAAA,IAEA2E,SAzJA,WA0JA3D,KAAA8C,QAAAc,IAAA,IAEAC,oBA5JA,WA6JA,IAAAC,EAAAC,uBAAA/D,KAAApB,WACAuB,QAAAC,IAAA,UAAA0D,GACME,IAANhE,KAAApB,UAAAkF,GACA9D,KAAApB,UAAAqF,KAAAC,MAAAC,IAAAnE,KAAApB,aAGA4E,WAnKA,SAmKA5B,GAEA,IADA,IAAA3C,KACAmF,EAAA,EAAAA,EAAAxC,EAAAyC,OAAAD,IACA,IAAAA,GACAnF,EAAAuD,KAAA,GACAxC,KAAAsE,IAAA,GAGA1C,EAAAwC,GAAAf,OAAAzB,EAAAwC,EAAA,GAAAf,OACApE,EAAAe,KAAAsE,MAAA,EACArF,EAAAuD,KAAA,KAEAvD,EAAAuD,KAAA,GACAxC,KAAAsE,IAAAF,GAIA,OAAAnF,GAEAsF,iBAtLA,SAAAC,GAsLA,IAAAC,EAAAD,EAAAC,IAAAD,EAAAE,OAAAF,EAAAG,SACA,OADAH,EAAAI,YAIA,OACAC,QAFA7E,KAAAf,QAAAwF,EAAAK,KAAA,GAGAC,QAAA,IAOAC,MAnMA,WAoMA7E,QAAAC,IAAA,SAEA,IAAA6E,EAAAC,gBASA,OARA/E,QAAAC,IAAA6E,GACAA,EAAA/C,QAAA,SAAAiD,QACAC,IAAAD,EAAAE,OACAF,EAAAE,MAAA,KAGArF,KAAAf,QAAAe,KAAAwD,WAAAyB,GAEAA,GAGAK,gBAlNA,WAmNAtF,KAAAZ,YAAA,GAGAmG,aAtNA,WAuNAvF,KAAAZ,YAAA,IAGAoG,OAOAxG,YACAyG,QADA,SACAC,EAAAC,GAAA,IAAAC,EAAA5F,KAGAA,KAAAb,cAEA,IAAA0G,KAGAH,EAAAxD,QAAA,SAAAiD,EAAAW,GAGAX,EAAAY,QAAA,KAAAD,EACAX,EAAAE,OACAF,EAAAY,QAAA,KAAAD,GAEA,GAAAD,EAAAG,QAAAb,EAAA9B,QACAwC,EAAArD,KAAA2C,EAAA9B,QAEA,GAAAwC,EAAAG,QAAAb,EAAA9B,SACAuC,EAAAzG,WAAA0G,EAAAG,QAAAb,EAAA9B,SACAuC,EAAAzG,WAAAqD,MACAc,KAAA6B,EAAA7B,KACA2C,cAGAL,EAAAzG,WAAA0G,EAAAG,QAAAb,EAAA9B,QAAA4C,SAAAzD,MACA0D,KAAA,IAAAf,EAAAY,QACAZ,KAAAgB,KACAC,IAAAjB,EAAAiB,WAMAC,MAAA,MC9aeC,GADEC,OAFjB,WAA0B,IAAAC,EAAAxG,KAAayG,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,MAAA,OAAAC,OAAA,UAAgCJ,EAAA,OAAYK,YAAA,SAAmBL,EAAA,WAAgBK,YAAA,mBAAAH,aAA4CI,MAAA,QAAeC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,UAAgBN,EAAA,OAAAH,EAAAa,GAAA,MAAAb,EAAAc,GAAAd,EAAA5H,UAAAE,SAAA0H,EAAAa,GAAA,KAAAV,EAAA,gBAAsFE,aAAaI,MAAA,UAAgBN,EAAA,OAAAH,EAAAa,GAAA,MAAAb,EAAAc,GAAAd,EAAA5H,UAAAG,SAAAyH,EAAAa,GAAA,KAAAV,EAAA,gBAAsFE,aAAaI,MAAA,UAAgBN,EAAA,OAAAH,EAAAa,GAAA,MAAAb,EAAAc,GAAAd,EAAA5H,UAAAC,UAAA,GAAA2H,EAAAa,GAAA,KAAAV,EAAA,WAAqFK,YAAA,mBAAAH,aAA4CI,MAAA,SAAgBC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOK,KAAA,UAAAH,KAAA,SAAAI,KAAA,wBAA+DC,IAAKC,MAAAlB,EAAA/C,UAAoB+C,EAAAa,GAAA,kCAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAA0EE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOK,KAAA,UAAAH,KAAA,SAAAI,KAAA,oBAA2DC,IAAKC,MAAAlB,EAAA9C,QAAkB8C,EAAAa,GAAA,wCAAAb,EAAAa,GAAA,KAAAV,EAAA,OAAuEE,aAAac,MAAA,WAAgB,GAAAnB,EAAAa,GAAA,KAAAV,EAAA,YAAiCO,OAAOvI,KAAA6H,EAAAxH,WAAA4I,cAAApB,EAAAjC,iBAAAsD,OAAA,GAAAd,OAAA,6BAAyGJ,EAAA,mBAAwBO,OAAOY,MAAA,MAAAhB,MAAA,OAA4BiB,YAAAvB,EAAAwB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAxB,EAAA,OAAAA,EAAA,QAA6BO,OAAOkB,GAAA5B,EAAAxH,WAAAmJ,EAAAE,QAAAtC,WAA2CS,EAAAa,GAAAb,EAAAc,GAAAd,EAAAxH,WAAAmJ,EAAAE,QAAA/E,MAAA,yBAA0EkD,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOY,MAAA,QAAeC,YAAAvB,EAAAwB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAxB,EAAA,OAAAA,EAAA,QAA6BO,OAAOkB,GAAA5B,EAAAxH,WAAAmJ,EAAAE,QAAAtC,WAA2CS,EAAAa,GAAAb,EAAAc,GAAAd,EAAAxH,WAAAmJ,EAAAE,QAAAlC,MAAA,uBAAwEK,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOY,MAAA,SAAAhB,MAAA,OAA+BiB,YAAAvB,EAAAwB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAxB,EAAA,kBAA6B2B,OAAOC,MAAA/B,EAAAxH,WAAAmJ,EAAAE,QAAA,KAAAG,SAAA,SAAAC,GAAmEjC,EAAAkC,KAAAlC,EAAAxH,WAAAmJ,EAAAE,QAAA,OAAAI,IAAoDE,WAAA,mCAA6ChC,EAAA,YAAiBO,OAAOY,OAAA,KAActB,EAAAa,GAAA,OAAAb,EAAAa,GAAA,KAAAV,EAAA,YAA2CO,OAAOY,OAAA,KAAetB,EAAAa,GAAA,kBAAyBb,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOY,MAAA,QAAeC,YAAAvB,EAAAwB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAxB,EAAA,YAAuBO,OAAOK,KAAA,WAAAqB,KAAA,GAA2BN,OAAQC,MAAA/B,EAAAxH,WAAAmJ,EAAAE,QAAA,KAAAG,SAAA,SAAAC,GAAmEjC,EAAAkC,KAAAlC,EAAAxH,WAAAmJ,EAAAE,QAAA,wBAAAI,IAAAI,OAAAJ,IAA2FE,WAAA,2CAAoD,QAEnrFG,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEvK,EACA4H,GATF,EAVA,SAAA4C,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/171.ec4029c18ba4ac165c8a.js", "sourcesContent": ["<template>\r\n  <div style=\"width: 100%;height: 100%;\">\r\n    <!---->\r\n    <div class=\"mhcx\">\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:left\">\r\n        <!-- <el-form-item style=\"float: left;\">\r\n          <div>当前审查任务：{{dialogObj.rwmc}}</div>\r\n        </el-form-item> -->\r\n        <el-form-item style=\"float: left;\">\r\n          <div>姓名：{{ dialogObj.xm }}</div>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: left;\">\r\n          <div>部门：{{ dialogObj.bm }}</div>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: left;\">\r\n          <div>职务：{{ dialogObj.zw }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!---->\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:right\">\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"primary\" size=\"medium\" icon=\"el-icon-document-add\" @click=\"submit\">\r\n            提交\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"warning\" size=\"medium\" icon=\"el-icon-document\" @click=\"save\">\r\n            临时保存\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div style=\"clear: both;\"></div>\r\n    </div>\r\n    <!---->\r\n    <el-table :data=\"showDxList\" :span-method=\"objectSpanMethod\" border height=\"calc(100% - 58px - 5px)\">\r\n      <el-table-column label=\"自查类\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <span :id=\"showDxList[scope.$index].mdIndex\"></span>{{ showDxList[scope.$index].dxmc }}\r\n            <!-- {{ showDxList[scope.$index].dxmc }} -->\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"自查内容\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <span :id=\"showDxList[scope.$index].mdIndex\"></span>{{ showDxList[scope.$index].scnr }}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"是否符合要求\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-radio-group v-model=\"showDxList[scope.$index].sffh\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注说明\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input type=\"textarea\" v-model.trim=\"showDxList[scope.$index].bzsm\" :rows=\"3\"></el-input>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!--锚点索引-->\r\n    <!-- <div class=\"md-menu\" @mouseover=\"mouseoverMdMenu\" @mouseout=\"mouseoutMenu\">\r\n      <div class=\"md-left\"></div>\r\n      <transition name=\"el-fade-in-linear\">\r\n        <div v-show=\"showMdmenu\" class=\"md-right\">\r\n          <div class=\"md-article\">\r\n            <el-timeline :reverse=\"reverse\">\r\n              <el-timeline-item v-for=\"(item, index) in activities\" :key=\"index\">\r\n                <div>\r\n                  <h4>{{ item.dxmc }}</h4>\r\n                  <div v-for=\"(xxItem, xxIndex) in item.children\" :key=\"xxIndex\" class=\"md-article-article\">\r\n                    <span v-if=\"xxItem.xxmc\" style=\"color:#409EFF;\">【{{ xxItem.xxmc }}】</span>\r\n                    <a :href=\"xxItem.href\">\r\n                      <span>{{ xxItem.nr }}</span>\r\n                    </a>\r\n                    <span v-if=\"xxItem.ykf\" style=\"color:#F56C6C;\">扣{{ xxItem.ykf }}分<span class=\"el-icon-caret-right\"></span></span>\r\n                  </div>\r\n                </div>\r\n              </el-timeline-item>\r\n            </el-timeline>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n      <div class=\"md-right-margin-div\"></div>\r\n    </div> -->\r\n    <!---->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getRyjczcx,\r\n  getAllRyjczcx,\r\n  getAllRyjcnr,\r\n  getRyjcnr,\r\n  deleteRyjcpfjl,\r\n  addRyjcpfjl,\r\n  updateDjztByRyid,\r\n  getLsRyzcpfjl,\r\n} from '../../../../api/zczp'\r\nimport {\r\n  // 获取注册信息\r\n  getDwxx,\r\n} from '../../../../api/dwzc'\r\nimport { getZczpIdsObj } from '../../../../utils/windowLocation'\r\n\r\nimport { writeOptionsLog } from '../../../../utils/logUtils'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogObj: {\r\n        // rwid: 'D66215EC-1B9A-408A-BEE8-7A9396165EA7',\r\n        // ccdryid: 'F9E1E031-3649-42C4-879E-5297B14B36D6'\r\n        zw: '',\r\n        xm: '',\r\n        bm: '',\r\n      },\r\n      //页面实际操作的评分数据[dx:{scnr:[]}]\r\n      showDxList: [],\r\n      //单元格合并规则\r\n      spanArr: [],\r\n      // 时间线排序方向\r\n      reverse: true,\r\n      // 锚点菜单集合\r\n      activities: [],\r\n      // 锚点菜单显隐\r\n      showMdmenu: false,\r\n      dwxx: {},\r\n      ryid: '',\r\n      rwid: '',\r\n      djzt: '',\r\n      rwmc: '',\r\n      dlzt: '',\r\n      sfhg: true,\r\n      jcjd: '',\r\n    }\r\n  },\r\n  computed: {},\r\n  components: {\r\n  },\r\n  mounted() {\r\n    this.rwid = this.$route.query.rwid\r\n    this.djzt = this.$route.query.djzt\r\n    this.ryid = this.$route.query.ryid\r\n    this.dlzt = this.$route.query.dlzt\r\n    this.jcjd = this.$route.query.jcjd,\r\n      console.log('this.rwid', this.rwid);\r\n    this.dialogObj.zw = this.$route.query.zw\r\n    this.dialogObj.xm = this.$route.query.xm\r\n    this.dialogObj.bm = this.$route.query.bm\r\n    this.rwmc = this.$route.query.rwmc\r\n    this.getDwxxList()\r\n    // let params = this.$route.query\r\n    // let params = getZczpIdsObj()\r\n    // if (params && Object.keys(params).length > 0) {\r\n    //   console.log('抽查的人员登记', params)\r\n    //   this.dialogObj.rwid = params.rwid\r\n    //   this.dialogObj.ccdryid = params.ccdryid\r\n    //   // 获取抽查的人员信息\r\n    //   this.getCcdryxxByCcdryid()\r\n    //   //\r\n    this.getRyzcxxjl()\r\n    //   return\r\n    // }\r\n    // this.$message.warning('未能正确获取抽查的人员ID，请关闭页面重新进入')\r\n    // // 获取字典\r\n    // this.showDxList = this.getZD()\r\n  },\r\n  methods: {\r\n    // 获取单位信息集合（默认选中最后一个，也就是最新的单位信息）\r\n    async getDwxxList() {\r\n      let data = await getDwxx()\r\n      console.log(data);\r\n      this.dwxx = data\r\n    },\r\n    //人员自查入库\r\n    async ryzcRK(dxXxList, zt) {\r\n      let del = {\r\n        rwid: this.rwid,\r\n        ryid: this.ryid,\r\n      }\r\n      let data = await deleteRyjcpfjl(del)\r\n      if (data.code == 10000) {\r\n        console.log(dxXxList);\r\n        console.log(zt);\r\n        let list = []\r\n        dxXxList.forEach(async (item) => {\r\n          item.dwid = this.dwxx.dwid\r\n          item.ryid = this.ryid\r\n          item.rwid = this.rwid\r\n          item.xm = this.dialogObj.xm\r\n          list.push(item)\r\n        })\r\n        let cg = await addRyjcpfjl(list)\r\n        if (cg.code == 10000) {\r\n          if (zt == 1) {\r\n\r\n            this.$message.success('临时保存成功')\r\n            let params = {\r\n              djzt: 1,\r\n              ryid: this.ryid,\r\n              rwid: this.rwid,\r\n            }\r\n            let dj = await updateDjztByRyid(params)\r\n            if (dj.code == 10000) {\r\n              if (this.dlzt == 1) {\r\n                this.$router.push({\r\n                  path: '/ccdry',\r\n                  query: {\r\n                    rwid: this.rwid,\r\n                    rwmc: this.rwmc,\r\n                    jcjd: this.jcjd,\r\n                  }\r\n                })\r\n              } else if (this.dlzt == 2) {\r\n                this.$router.push({\r\n                  path: '/jczj',\r\n                  query: {\r\n                    rwid: this.rwid,\r\n                    rwmc: this.rwmc,\r\n                    jcjd: this.jcjd,\r\n                  }\r\n                })\r\n              }\r\n            }\r\n\r\n          } else if (zt == 2) {\r\n            this.$message.success('提交成功')\r\n            let params = {\r\n              djzt: 2,\r\n              ryid: this.ryid,\r\n              rwid: this.rwid,\r\n            }\r\n            let dj = await updateDjztByRyid(params)\r\n            if (dj.code == 10000) {\r\n              if (this.dlzt == 1) {\r\n                this.$router.push({\r\n                  path: '/ccdry',\r\n                  query: {\r\n                    rwid: this.rwid,\r\n                    rwmc: this.rwmc,\r\n                    jcjd: this.jcjd,\r\n                  }\r\n                })\r\n              } else if (this.dlzt == 2) {\r\n                this.$router.push({\r\n                  path: '/jczj',\r\n                  query: {\r\n                    rwid: this.rwid,\r\n                    rwmc: this.rwmc,\r\n                    jcjd: this.jcjd,\r\n                  }\r\n                })\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      // //插入或更新人员评分记录表\r\n      // let bool = insertUpdateRyjlByCcdnryid(dxXxList, this.dialogObj.ccdryid)\r\n      // /*=====部门详细自查记录入库完成=====*/\r\n      // // 更新 抽查的内设机构表(ccdnsjg_list) 表状态\r\n      // let params = {\r\n      //   ccdryid: this.dialogObj.ccdryid,\r\n      //   zt: zt\r\n      // }\r\n      // bool = updateCcdryById(params)\r\n      // if (bool) {\r\n      //   let logYmngnmc = '人员' + this.dialogObj.xm + '登记信息'\r\n      //   if (zt == 1) {\r\n      //     logYmngnmc += '临时保存'\r\n      //   }\r\n      //   if (zt == 2) {\r\n      //     logYmngnmc += '保存完成'\r\n      //   }\r\n      //   // 写入操作日志\r\n      //   writeOptionsLog('yybs-ccdryDj', logYmngnmc, this.dialogObj)\r\n      //   // 更新数据\r\n      //   this.getRyzcxxjl()\r\n      //   // 更新vuex状态机关闭tag值，让tags组件能够监测到需要关闭的tag\r\n      //   this.$router.app.$options.store.commit('changeCloseTag', { path: this.$route.path })\r\n      //   //\r\n      //   this.$message.success('人员自查自评结果登记成功')\r\n      // }\r\n    },\r\n    /**\r\n     * 获取人员详细自查记录\r\n     */\r\n    async getRyzcxxjl() {\r\n      let data = await getAllRyjczcx()\r\n      let data1 = []\r\n      if (this.djzt == 0) {\r\n        data1 = await getAllRyjcnr()\r\n      }\r\n      else if (this.djzt == 1) {\r\n        let params = {\r\n          rwid: this.rwid,\r\n          ryid: this.ryid\r\n        }\r\n        data1 = await getLsRyzcpfjl(params)\r\n      }\r\n      data1.forEach((item) => {\r\n        data.forEach((item1) => {\r\n          if (item1.zcxid == item.zcxid) {\r\n            item.dxmc = item1.zcxmc;\r\n          }\r\n        })\r\n      })\r\n      this.showDxList = data1\r\n      this.spanArr = this.getSpanArr(data1)\r\n      console.log(this.showDxList);\r\n      console.log(data);\r\n      console.log(data1);\r\n    },\r\n\r\n    submit() {\r\n      this.ryzcRK(this.showDxList, 2)\r\n    },\r\n    save() {\r\n      this.ryzcRK(this.showDxList, 1)\r\n    },\r\n    returnSy() {\r\n      this.$router.go(-1)\r\n    },\r\n    getCcdryxxByCcdryid() {\r\n      let ccdryxx = selectCcdryxxByCcdryid(this.dialogObj)\r\n      console.log('ccdryxx', ccdryxx)\r\n      Object.assign(this.dialogObj, ccdryxx)\r\n      this.dialogObj = JSON.parse(JSON.stringify(this.dialogObj))\r\n    },\r\n    //----------------------------用来返回this.spanArr数组的，定义每一行的 rowspan-----------\r\n    getSpanArr(list) {\r\n      let spanArr = []\r\n      for (var i = 0; i < list.length; i++) {\r\n        if (i === 0) {\r\n          spanArr.push(1)\r\n          this.pos = 0\r\n        } else {\r\n          // 判断当前元素与上一个元素是否相同\r\n          if (list[i].zcxid == list[i - 1].zcxid) {\r\n            spanArr[this.pos] += 1\r\n            spanArr.push(0)\r\n          } else {\r\n            spanArr.push(1)\r\n            this.pos = i\r\n          }\r\n        }\r\n      }\r\n      return spanArr\r\n    },\r\n    objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      if (columnIndex === 0) {\r\n        //\r\n        const _row = this.spanArr[row.scid - 1]\r\n        return {\r\n          rowspan: _row,\r\n          colspan: 1\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 获取部门详细自查记录字典\r\n     */\r\n    getZD() {\r\n      console.log(\"getZD\")\r\n      //\r\n      const zdList = getRyzcxxjlZD()\r\n      console.log(zdList)\r\n      zdList.forEach((nr) => {\r\n        if (nr.sffh === undefined) {\r\n          nr.sffh = true\r\n        }\r\n      });\r\n      this.spanArr = this.getSpanArr(zdList)\r\n      //\r\n      return zdList\r\n    },\r\n    // 锚点菜单鼠标移入事件\r\n    mouseoverMdMenu() {\r\n      this.showMdmenu = true\r\n    },\r\n    // 锚点菜单鼠标移出事件\r\n    mouseoutMenu() {\r\n      this.showMdmenu = false\r\n    }\r\n  },\r\n  watch: {\r\n    // '$route': {\r\n    //   handler (newVal, oldVal) {\r\n    //     console.log('route changed', newVal)\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    showDxList: {\r\n      handler(newVal, oldVal) {\r\n        ///////\r\n        // 清空锚点，防重复\r\n        this.activities = []\r\n        // 大项ID数组，用以对数据进行分组\r\n        let dxMdIdArr = []\r\n        ///////////\r\n        //\r\n        newVal.forEach((nr, nrIndex) => {\r\n          // nr.dxMdIndex = 'dxMd' + nr.zcxid\r\n          //\r\n          nr.mdIndex = 'md' + nrIndex\r\n          if (!nr.sffh) {\r\n            nr.mdIndex = 'md' + nrIndex\r\n            //\r\n            if (dxMdIdArr.indexOf(nr.zcxid) == -1) {\r\n              dxMdIdArr.push(nr.zcxid)\r\n            }\r\n            if (dxMdIdArr.indexOf(nr.zcxid) != -1) {\r\n              if (!this.activities[dxMdIdArr.indexOf(nr.zcxid)]) {\r\n                this.activities.push({\r\n                  dxmc: nr.dxmc,\r\n                  children: []\r\n                })\r\n              }\r\n              this.activities[dxMdIdArr.indexOf(nr.zcxid)].children.push({\r\n                href: '#' + nr.mdIndex,\r\n                nr: nr.scnr,\r\n                ykf: nr.ykf\r\n              })\r\n            }\r\n          }\r\n        })\r\n      },\r\n      deep: true\r\n    },\r\n  },\r\n\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/zczp/childPage/ccdryDj.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"}},[_c('div',{staticClass:\"mhcx\"},[_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"left\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"left\"}},[_c('div',[_vm._v(\"姓名：\"+_vm._s(_vm.dialogObj.xm))])]),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"left\"}},[_c('div',[_vm._v(\"部门：\"+_vm._s(_vm.dialogObj.bm))])]),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"left\"}},[_c('div',[_vm._v(\"职务：\"+_vm._s(_vm.dialogObj.zw))])])],1),_vm._v(\" \"),_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"right\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\",\"icon\":\"el-icon-document-add\"},on:{\"click\":_vm.submit}},[_vm._v(\"\\n          提交\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"medium\",\"icon\":\"el-icon-document\"},on:{\"click\":_vm.save}},[_vm._v(\"\\n          临时保存\\n        \")])],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1),_vm._v(\" \"),_c('el-table',{attrs:{\"data\":_vm.showDxList,\"span-method\":_vm.objectSpanMethod,\"border\":\"\",\"height\":\"calc(100% - 58px - 5px)\"}},[_c('el-table-column',{attrs:{\"label\":\"自查类\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('span',{attrs:{\"id\":_vm.showDxList[scope.$index].mdIndex}}),_vm._v(_vm._s(_vm.showDxList[scope.$index].dxmc)+\"\\n          \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"自查内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('span',{attrs:{\"id\":_vm.showDxList[scope.$index].mdIndex}}),_vm._v(_vm._s(_vm.showDxList[scope.$index].scnr)+\"\\n        \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"是否符合要求\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-radio-group',{model:{value:(_vm.showDxList[scope.$index].sffh),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"sffh\", $$v)},expression:\"showDxList[scope.$index].sffh\"}},[_c('el-radio',{attrs:{\"label\":true}},[_vm._v(\"是\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":false}},[_vm._v(\"否\")])],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"备注说明\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3},model:{value:(_vm.showDxList[scope.$index].bzsm),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"bzsm\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"showDxList[scope.$index].bzsm\"}})]}}])})],1)],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-4b0af22c\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/zczp/childPage/ccdryDj.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-4b0af22c\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ccdryDj.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdryDj.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdryDj.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-4b0af22c\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ccdryDj.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-4b0af22c\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/zczp/childPage/ccdryDj.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}