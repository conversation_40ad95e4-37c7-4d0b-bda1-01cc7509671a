<template>
  <div class="sec-container" v-loading="loading">
    <!-- 标题 -->
    <p class="sec-title">基本信息</p>
    <div class="sec-form-container">
      <el-form ref="formName" :model="tjlist" label-width="225px">
        <!-- 第一部分包括姓名到常住地公安start -->
        <div class="sec-header-section">
          <div class="sec-form-left">
            <el-form-item label="申请人">
              <el-input placeholder="" v-model="tjlist.xqr" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="所在部门">
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.szbm" clearable disabled></el-input>
              </template>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="使用期限">
              <el-date-picker v-model="tjlist.wcqsrq" class="riq" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" disabled>
              </el-date-picker>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="知悉范围">
              <el-input placeholder="" v-model="tjlist.zxfw" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left sec-form-left-textarea">
            <el-form-item label="用途">
              <el-input placeholder="" type="textarea" v-model="tjlist.yt" clearable disabled></el-input>
            </el-form-item>
          </div>

        </div>
        <!-- 载体详细信息start -->
        <p class="sec-title">载体详细信息</p>
        <el-table border class="sec-el-table" :data="ztwcxdWcscScjlList"
          :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="ztmc" label="载体名称"></el-table-column>
          <el-table-column prop="xmbh" label="项目编号"></el-table-column>
          <el-table-column prop="ztbh" label="载体编号"></el-table-column>
          <el-table-column prop="lx" label="载体类型" :formatter="forlx"></el-table-column>
          <el-table-column prop="smmj" label="密级" :formatter="formj"></el-table-column>
          <el-table-column prop="bmqx" label="保密期限"></el-table-column>
          <el-table-column prop="ys" label="页数/大小"></el-table-column>
          <el-table-column prop="fs" label="份数"></el-table-column>
        </el-table>
        <div class="sec-form-left">
          <el-form-item label="携带人">
            <el-input placeholder="" v-model="tjlist.jyr" clearable disabled></el-input>
          </el-form-item>
          <el-form-item label="项目经理">
            <el-input placeholder="" v-model="tjlist.xmjl" clearable disabled></el-input>
          </el-form-item>
        </div>
        <div class="sec-form-left">
          <el-form-item label="归还部门">
              <template slot-scope="scope">
                <el-cascader v-model="tjlist.ghrbm" style="width: 100%;" :options="regionOption" :props="regionParams"
                  filterable clearable ref="cascaderArr" @change="handleChange(1)"></el-cascader>
              </template>
            </el-form-item>
            <el-form-item label="归还人">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.ghr"
                :fetch-suggestions="querySearch" placeholder="请输入申请人" style="width:100%">
              </el-autocomplete>
            </el-form-item>
        </div>
        <div class="sec-form-left">
          <el-form-item label="接收检查人部门">
              <template slot-scope="scope">
                <el-cascader v-model="tjlist.jsjcrbm" style="width: 100%;" :options="regionOption" :props="regionParams"
                  filterable clearable ref="cascaderArr" @change="handleChange(2)"></el-cascader>
              </template>
            </el-form-item>
            <el-form-item label="接收检查人">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.jsjcr"
                :fetch-suggestions="querySearch" placeholder="请输入申请人" style="width:100%">
              </el-autocomplete>
            </el-form-item>
        </div>
        <div class="sec-form-left">
          <el-form-item label="归还时间">
            <el-date-picker v-model="tjlist.ghsj" type="date" placeholder="选择日期" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" class="rip">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="载体情况">
            <el-radio v-model="tjlist.ztqk" label="1">载体无异常</el-radio>
            <el-radio v-model="tjlist.ztqk" label="2">其他情况</el-radio>
          </el-form-item>
        </div>
        <!-- 载体详细信息end -->
        <!-- 底部操作按钮start -->
        <div class="sec-form-six haveBorderTop sec-footer">
          <el-button @click="returnIndex" class="fr ml10" plain>返回</el-button>
          <el-button @click="chooseApproval" class="fr" type="success">保存</el-button>

        </div>
        <!-- 底部操作按钮end -->

      </el-form>
    </div>

  </div>
</template>
<script>
import {
  getLcSLid,
  updateZgfs,
  updateSlzt,
  getZzjgList,
  getSpUserList,
  getCurZgfsjl,
  getFwdyidByFwlx,
  getAllYhxx,
  savaZtqdBatch,
  deleteZtqdByYjlid,
  updateZtgl,
  getLoginInfo,
} from '../../../api/index'
import {
  getDjgwbgInfo,
  submitDjgwbg,
  getDjgwbgInfoByLcsllid,
  updateDjgwbg
} from '../../../api/djgwbg'
import {
  addZtglJy,
  updateZtglJy,
  updateZtglJydj
} from '../../../api/ztjysc'
import { getAllGwxx } from '../../../api/qblist'
import { getAllSmdj,getAllSmsbmj,getSmztlx } from '../../../api/xlxz'
import BaseTable from '../../components/common/baseTable.vue'
import AddLineTable from "../../components/common/addLineTable.vue"; //人工纠错组件
export default {
  components: {
    AddLineTable,
    BaseTable
  },
  props: {},
  data() {
    return {
      value1: '',
      loading: false,
      // 弹框人员选择条件
      ryChoose: {
        'bm': '',
        'xm': ''
      },
      gwmclist: [],
      smdjxz: [],
      smxblxxz: [],
      smsbdjxz: [],
      regionOption: [], // 部门下拉
      page: 1, // 审批人弹框当前页
      pageSize: 10, // 审批人弹框每页条数
      radioIdSelect: '', // 审批人弹框人员单选
      ryDatas: [], // 弹框人员选择
      total: 0, // 弹框人员总数
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true
      }, //地域信息配置参数
      // table 行样式
      headerCellStyle: {
        background: '#EEF7FF',
        color: '#4D91F8'
      },
      // form表单提交数据
      tjlist: {
        xqr: '',
        szbm: '',
        wcqsrq: [],
        zxfw: '',
        yt: '',
        jsdw: '',
        qsdd: '',
        mddd: '',
        fhcs: [],
        jtgj: [],
        jtlx: '',
        xdmmd: '',
        xdr: '',
        xmjl: '',
        jsjcrbm:[],
        ghrbm:[],
      },
      // 载体详细信息
      ztwcxdWcscScjlList: [{
        'ztmc': '',
        'xmbh': '',
        'ztbh': '',
        'lx': '',
        'smmj': '',
        'bmqx': '',
        'ys': '',
        'fs': '',
        'czbtn1': '增加行',
        'czbtn2': '',
      }],
      ztlxList: [
        {
          lxid: '1',
          lxmc: '纸介质'
        },
        {
          lxid: '2',
          lxmc: '光盘'
        },
        {
          lxid: '3',
          lxmc: '电磁介质'
        },
      ],
      smdjList: [
        {
          smdjid: '1',
          smdjmc: '绝密'
        },
        {
          smdjid: '2',
          smdjmc: '机密'
        },
        {
          smdjid: '3',
          smdjmc: '秘密'
        },
        {
          smdjid: '4',
          smdjmc: '内部'
        },
      ],
      xdfsList: [
        {
          xdfsid: '1',
          xdfsmc: '包装密封，封口处加盖密封章'
        },
        {
          xdfsid: '2',
          xdfsmc: '指派专人传递'
        },
        {
          xdfsid: '3',
          xdfsmc: '密码箱防护'
        },
      ],
      jtgjList: [
        {
          jtgjid: '1',
          jtgjmc: '飞机'
        },
        {
          jtgjid: '2',
          jtgjmc: '火车'
        },
        {
          jtgjid: '3',
          jtgjmc: '专车'
        },
      ],
      ryInfo: {},
      // 政治面貌下拉选项
      sltshow: '', // 文档的缩略图显示
      routeType: '',
      pdfBase64: '',
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      approvalDialogVisible: false, // 选择申请人弹框
      fileRow: '',
      // 选择审核人table
      applyColumns: [{
        name: '姓名',
        prop: 'xm',
        scopeType: 'text',
        formatter: false
      },
      {
        name: '部门',
        prop: 'bmmc',
        scopeType: 'text',
        formatter: false
      },
      {
        name: '岗位',
        prop: 'gwmc',
        scopeType: 'text',
        formatter: false
      }
      ],
      handleColumnApply: [],
      scqk: [
        {
          sfty: '同意',
          id: 1
        },
        {
          sfty: '不同意',
          id: 0
        },
      ],
      disabled2: false,
      //知悉范围选择
      rydialogVisible: false,
      formInlinery: {
        bm: ''
      },
      table1Data: [],
      table2Data: [],
    }
  },
  computed: {
    // selectedLabel() {
    //   const option = this.ynoptions.find(o => o.value === this.selectedValue);
    //   return option ? option.label : '';
    // }
  },
  mounted() {
    this.onfwid()
    this.smdj()
    this.smsblx()
    this.smsbdj()
    this.gwxx()
    this.rydata()
    this.getOrganization()
    this.tjlist = this.$route.query.datas
    this.ztwcxdWcscScjlList = this.$route.query.ztzz
    this.tjlist.wcqsrq = []
    this.tjlist.wcqsrq.push(this.tjlist.jyqsrq)
    this.tjlist.wcqsrq.push(this.tjlist.jyjzrq)
    if (this.tjlist.jsjcrbm!=undefined) {
      this.tjlist.jsjcrbm = this.tjlist.jsjcrbm.split('/')
    }
    if (this.tjlist.ghbm!=undefined) {
      this.tjlist.ghbm = this.tjlist.ghbm.split('/')
    }
    if (this.tjlist.ztqk != undefined) {
      this.tjlist.ztqk = this.tjlist.ztqk.toString()
    }
    console.log('this.radioIdSelect', this.ztwcxdWcscScjlList);
    // this.ryInfo = this.$route.query.datas.gwbgscb
    this.routeType = this.$route.query.type
    this.routezt = this.$route.query.zt
    console.log(this.routezt);
    let result = {}
    if (this.$route.query.type == 'add') {
      // 首次发起申请
      result = {
        ...this.tjlist,
        ...this.$route.query.datas
      }
    } else {
      // 保存 继续编辑
      result = {
        ...this.tjlist,
        ...this.$route.query.datas
      }
    }
    this.tjlist = result
    this.tjlist.jsjcrbm = this.tjlist.szbm.split('/')
    this.tjlist.ghrbm = this.tjlist.szbm.split('/')
    this.handleChange(1)
    var date = new Date();
    this.year = date.getFullYear();
    this.yue = date.getMonth() + 1;
    this.yue = this.yue < 10 ? '0' + this.yue : this.yue;
    this.ri = date.getDate();
    this.ri = this.ri < 10 ? '0' + this.ri : this.ri;
      this.tjlist.ghsj = this.year + '-' + this.yue + '-' + this.ri
  },
  methods: {
    //人员获取
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async smry() {
      this.restaurants = await getAllYhxx()
    },
    async handleChange(index) {
      // let nodesObj = this.$refs['cascaderArr'].getCheckedNodes()[0].data
      // this.glbmid = nodesObj.bmm
      // console.log(nodesObj);
      let resList
      let params
      if (index == 1) {
        params = {
          bmmc: this.tjlist.ghrbm.join('/')
        }
        resList = await getAllYhxx(params)
      } else if (index == 2) {
        params = {
          bmmc: this.tjlist.jsjcrbm.join('/')
        }
        resList = await getAllYhxx(params)
      }
      this.restaurants = resList;

    },
    //培训清单
    zxfw() {
      this.rydialogVisible = true
      // this.indexzx = 1
    },
    addpxry() {
      // this.tianjiaryList = this.table2Data
      // this.xglist.ry = this.table2Data
      // this.rydialogVisible = false
      let ry = []
      this.table2Data.forEach(item => {
        ry.push(item.xm)
        // console.log(item);
      })
      console.log(ry);
      this.tjlist.zxfw = ry.join(',')
      this.rydialogVisible = false
      this.$refs.table1.clearSelection()
      this.table2Data = []
    },
    pxrygb() {
      this.rydialogVisible = false
      this.$refs.table1.clearSelection()
      this.table2Data = []
    },
    bmrycx() {
      let nodesObj = this.$refs['cascaderArr'].getCheckedNodes()[0]
      if (nodesObj != undefined) {
        // console.log(nodesObj);
        this.bmm = nodesObj.data.bmm
      } else {
        this.bmm = undefined
      }
    },
    onSubmitry() {
      this.rydata()
    },
    async rydata() {
      let param = {
        bmid: this.bmm
      }
      let list = await getAllYhxx(param)
      this.table1Data = list
    },
    onTable1Select(rows) {
      console.log(rows);
      this.table2Data = rows
      this.selectlistRow = rows
    },
    onTable2Select(rows) {
      this.$refs.table1.selection.forEach((item, label) => {
        if (item == rows) {
          this.$refs.table1.selection.splice(label, 1)
        }
      })
      this.table2Data.forEach((item, label) => {
        if (item == rows) {
          console.log(label);
          this.table2Data.splice(label, 1)
        }
      })
    },
    handleRowClick(row, column, event) {
      this.$refs.table1.toggleRowSelection(row);
    },
    chRadio() { },
    async gwxx() {
      let param = {
        bmmc: this.tjlist.bmmc
      }
      let data = await getAllGwxx(param)
      this.gwmclist = data
      console.log(data);
    },
    //获取涉密等级信息
    async smdj() {
      let data = await getAllSmdj()
      this.smdjxz = data
    },
    handleSelectBghgwmc(item, i) {
      console.log(i);
      this.gwmclist.forEach(item1 => {
        if (i == item1.gwmc) {
          console.log(item1);
          this.tjlist.bgsmdj = item1.smdj
        }

      })
    },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    handleSelectionChange(index, row) {
      this.radioIdSelect = row
    },
    // 载体详细信息增加行
    addRow(data) {
      data.push({
        'ztmc': '',
        'xmbh': '',
        'ztbh': '',
        'lx': '',
        'smmj': '',
        'bmqx': '',
        'ys': '',
        'fs': '',
        'czbtn1': '增加行',
        'czbtn2': '删除',
      })
    },
    // 载体详细信息删除行
    delRow(index, rows) {
      rows.splice(index, 1)
    },

    async onfwid() {
      let params = {
        fwlx: 24
      }
      let data = await getFwdyidByFwlx(params)
      console.log(data);
      this.fwdyid = data.data.fwdyid
    },
    // 保存
    async save() {
      let param = {
        'fwdyid': this.fwdyid,
        'lcslclzt': 3
      }
      param.smryid = ''
      if (this.routeType == 'update') {
        param.slid = this.tjlist.slid
        let res = await getLcSLid(param)
        if (res.code == 10000) {
          this.tjlist.jyqsrq = this.tjlist.wcqsrq[0]
          this.tjlist.jyjzrq = this.tjlist.wcqsrq[1]
          let params = this.tjlist
          let resDatas = await updateZtglJy(params)
          if (resDatas.code == 10000) {
            deleteZtqdByYjlid({
              'yjlid': this.tjlist.jlid
            })
            this.ztwcxdWcscScjlList.forEach(item => {
              item.splx = 6
              item.yjlid = this.tjlist.jlid
            })
            let ztqd = await savaZtqdBatch(this.ztwcxdWcscScjlList)
            if (ztqd.code == 10000) {
              this.$router.push('/ztjysc')
              this.$message({
                message: '保存并提交成功',
                type: 'success'
              })
            }
          }
        }
      } else {
        let res = await getLcSLid(param)
        if (res.code == 10000) {
          this.tjlist.slid = res.data.slid
          this.tjlist.jyqsrq = this.tjlist.wcqsrq[0]
          this.tjlist.jyjzrq = this.tjlist.wcqsrq[1]
          let params = this.tjlist
          let resDatas = await addZtglJy(params)
          if (resDatas.code == 10000) {
            this.ztwcxdWcscScjlList.forEach(item => {
              item.splx = 6
              item.yjlid = resDatas.data
            })
            let ztqd = await savaZtqdBatch(this.ztwcxdWcscScjlList)
            if (ztqd.code == 10000) {
              this.$router.push('/ztjysc')
              this.$message({
                message: '保存成功',
                type: 'success'
              })
            }

          }
        }
      }
    },
    //全部组织机构List
    async getOrganization() {
      let zzjgList = await getZzjgList()
      this.zzjgmc = zzjgList
      let shu = []
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            childrenRegionVo.push(item1)
            item.childrenRegionVo = childrenRegionVo
          }
        });
        shu.push(item)
      })
      let shuList = []
      let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    handleSelectionChange1(index, row) {
      this.radioIdSelect = row
    },
    handleCurrentChangeRy(val) {
      this.page = val
      this.chooseApproval()
    },
    //列表分页--更改每页显示个数
    handleSizeChangeRy(val) {
      this.page = 1
      this.pageSize = val
      this.chooseApproval()
    },
    // 人员搜索
    searchRy() {
      this.tableKey++
      this.chooseApproval()
    },
    // 发起申请选择人员 人员下拉
    bmSelectChange(item) {
      if (item != undefined) {
        this.ryChoose.bm = item.join('/')
      }
    },
    
    // 选择审批人
    async chooseApproval() {
        this.ztwcxdWcscScjlList.forEach(item => {
          let param = {
            jlid: item.jlid,
            ghr: this.tjlist.ghr,
            ghsj: this.tjlist.ghsj,
            jsjcr: this.tjlist.jsjcr,
            ztqk: this.tjlist.ztqk,
            ghzt: 1
          }
          if (this.tjlist.ghrbm!=undefined) {
            param.ghrbm=this.tjlist.ghrbm.join('/')
          }
          if (this.tjlist.jsjcrbm!=undefined) {
            param.jsjcrbm=this.tjlist.jsjcrbm.join('/')
          }
          updateZtglJydj(param)
          let params = {
            ztid:item.ztid,
            zt:1
          }
          updateZtgl(params)
        })
        this.$router.push('/smztjy')
    },
    // 保存并提交
    async saveAndSubmit() {
      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {
        let param = {
          'fwdyid': this.fwdyid
        }
        if (this.routeType == 'update' && this.routezt == undefined) {
          param.lcslclzt = 2
          param.smryid = ''
          param.slid = this.tjlist.slid
          param.clrid = this.radioIdSelect.yhid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.jyqsrq = this.tjlist.wcqsrq[0]
            this.tjlist.jyjzrq = this.tjlist.wcqsrq[1]
            let params = this.tjlist
            let resDatas = await updateZtglJy(params)
            if (resDatas.code == 10000) {
              let paramStatus = {
                'fwdyid': this.fwdyid,
                'slid': this.tjlist.slid
              }
              let resStatus
              resStatus = await updateSlzt(paramStatus)
              if (resStatus.code == 10000) {
                this.$router.push('/ztjysc')
                this.$message({
                  message: '保存并提交成功',
                  type: 'success'
                })
              }
            }
          }
        } else {
          param.lcslclzt = 0
          param.clrid = this.radioIdSelect.yhid
          param.smryid = ''
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.jyqsrq = this.tjlist.wcqsrq[0]
            this.tjlist.jyjzrq = this.tjlist.wcqsrq[1]
            this.tjlist.slid = res.data.slid
            let params = this.tjlist
            let resDatas = await addZtglJy(params)
            if (resDatas.code == 10000) {
              this.ztwcxdWcscScjlList.forEach(item => {
                item.splx = 6
                item.yjlid = resDatas.data
              })
              let ztqd = await savaZtqdBatch(this.ztwcxdWcscScjlList)
              if (ztqd.code == 10000) {
                this.$router.push('/ztjysc')
                this.$message({
                  message: '保存并提交成功',
                  type: 'success'
                })
              }
            }
          }
        }
      } else {
        this.$message({
          message: '请选择审批人',
          type: 'warning'
        })
      }
    },
    // 返回
    returnIndex() {
      this.$router.push('/smztjy')
    },
    //获取涉密等级信息
    async smsbdj() {
      let data = await getAllSmsbmj()
      this.smsbdjxz = data
    },
    //获取涉密等级信息
    async smsblx() {
      let data = await getSmztlx()
      this.smxblxxz = data
    },
    formj(row) {
      let hxsj
      this.smsbdjxz.forEach(item => {
        if (row.smmj == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    forlx(row) {
      let hxsj
      this.smxblxxz.forEach(item => {
        if (row.lx == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
  },
  watch: {

  }
}

</script>

<style scoped>
.sec-container {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: overlay;
}

.sec-title {
  border-left: 5px solid #1b72d8;
  color: #1b72d8;
  font-size: 20px;
  font-weight: 700;
  text-indent: 10px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.sec-form-container {
  width: 100%;
  height: 100%;
}

.sec-form-left {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
}

.sec-form-left:not(:first-child) {
  border-top: 0;
}

.sec-form-left .el-form-item {
  float: left;
  width: 100%;
}

.sec-header-section {
  width: 100%;
  position: relative;
}

.sec-header-pic {
  width: 258px;
  position: absolute;
  right: 0px;
  top: 0;
  height: 163px;
  border: 1px solid #CDD2D9;
  border-left: 0;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sec-form-second {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
  border-top: 0;
}

.sec-form-third {
  border: 1px solid #CDD2D9;
  /* height: 40px;  */
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

/deep/.el-checkbox-group {
  display: flex;
  justify-content: center;
  background-color: #F5F7FA;
  border-right: 1px solid #CDD2D9;
}

.checkbox {
  display: inline-block !important;
  background-color: rgba(255, 255, 255, 0) !important;
  border-right: none !important;
}

.sec-form-four {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-five {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  overflow: hidden;
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.yulan {
  text-align: center;
  cursor: pointer;
  color: #3874D5;
  font-weight: 600;
  float: left;
  margin-left: 10px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 2px solid #EBEBEB;
}

.sec-form-six {
  border: 1px solid #CDD2D9;
  overflow: hidden;
  background: #ffffff;
  padding: 10px;
}

.ml10 {
  margin-left: 10px;
}

.sec-footer {
  margin-top: 10px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

.sec-form-left-textarea {
  height: 54px !important;
}

.sec-form-left-textarea>>>.el-form-item__label {
  line-height: 54px !important;
}

>>>.sec-form-four .el-textarea__inner {
  border: none;
}

.sec-left-text {
  float: left;
  margin-right: 130px;
}

.haveBorderTop {
  border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
  width: 500px !important;
}

>>>.longLabel .el-form-item__content {
  margin-left: 500px !important;
  padding-left: 20px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

/* .sec-form-second:not(:first-child){
  border-top: 0;
} */
.sec-form-second .el-form-item {
  float: left;
  width: 100%;
}

.sec-el-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
  padding-left: 15px;
  background-color: #F5F7FA;
  width: calc(100% - 16px);
  border-right: 1px solid #CDD2D9;
  color: #C0C4CC;
}

>>>.sec-el-table .el-input__inner {
  border: none !important;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
  width: 200px;
  text-align: center;
  font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
  border: none;
  border-right: 1px solid #CDD2D9;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item {
  margin-bottom: 0px;
}

/* >>>.el-form > div {
  border: 1px solid #CDD2D9;;
} */
>>>.el-form-item__label {
  border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
} */
.riq {
  width: 100% !important;
}

.widthw {
  width: 6vw;
}

.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}
.rip {
  width: 100% !important;
}
</style>
