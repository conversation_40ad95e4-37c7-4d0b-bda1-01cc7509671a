<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">涉密人员岗位变更汇总情况</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="台账时间" style="font-weight: 700;">
                  <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widthw">
                  </el-input> -->
                  <el-select v-model="formInline.tzsj" placeholder="台账时间">
                    <el-option v-for="item in yearSelect" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="变更时间" style="font-weight: 700;">
                  <el-date-picker v-model="formInline.bgsj" type="daterange" range-separator="至"
                    start-placeholder="查询起始时间" end-placeholder="查询结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>

              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" @click="fh()">返回
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="gwbgList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="xm" label="姓名"></el-table-column>
                  <el-table-column prop="sfzhm" label="身份证号码"></el-table-column>
                  <el-table-column prop="bmmc" label="所在部门"></el-table-column>
                  <el-table-column prop="gwmc" label="变更前涉密岗位">
                    <template slot-scope="scoped">
                      <div>
                        {{ scoped.row.gwmc.join(',') }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="smdj" label="变更前涉密等级" :formatter="forbgqsmdj"></el-table-column>
                  <!-- <el-table-column prop="bghbmmc" label="变更后部门"></el-table-column> -->
                  <el-table-column prop="bgsmgw" label="变更后涉密岗位">
                    <template slot-scope="scoped">
                      <div>
                        {{ scoped.row.bgsmgw.join(',') }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="bgsmdj" label="变更后涉密等级" :formatter="forbghsmdj"></el-table-column>
                  <el-table-column prop="bgsj" label="变更时间"></el-table-column>
                  <el-table-column prop="tznf" label="台账时间"></el-table-column>
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>

        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密人员岗位变更汇总情况" class="scbg-dialog"
          :visible.sync="dialogVisible_dr" show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="姓名" label="姓名"></el-table-column>
              <el-table-column prop="身份证号码" label="身份证号码"></el-table-column>
              <el-table-column prop="变更后涉密岗位" label="变更后涉密岗位"></el-table-column>
              <el-table-column prop="变更后涉密等级" label="变更后涉密等级"></el-table-column>
              <el-table-column prop="变更时间" label="变更时间"></el-table-column>
              <el-table-column prop="备注" label="备注">
              </el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- -----------------涉密岗位-弹窗--------------------------- -->
        <el-dialog title="新增涉密人员岗位变更信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="150px" size="mini">
            <el-form-item label="姓名" prop="xm" class="one-line">
              <!-- <el-input placeholder="姓名" v-model="tjlist.xm" clearable></el-input> -->
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.xm" @blur="dwxxByDwmc1(tjlist.xm)"
                :fetch-suggestions="querySearch" style="width: 100%;" placeholder="请输入姓名" @select="handleSelect">
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="身份证号码" prop="sfzhm" class="one-line">
              <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="sfzhm = $event.target.value"
                placeholder="身份证号码" v-model="tjlist.sfzhm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="所在部门" prop="bmmc" class="one-line">
              <el-input v-model="tjlist.bmmc" clearable placeholder="部门" disabled></el-input>
            </el-form-item>
            <el-form-item label="变更前岗位名称" prop="gwmc" class="one-line">
              <!-- <el-input v-model="tjlist.gwmc" clearable placeholder="变更前岗位名称" disabled></el-input> -->
              <el-select v-model="tjlist.gwmc" placeholder="变更前岗位名称" disabled style="width:100%;">
                <el-option v-for="item in gwmc" :label="item.gwmc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更前涉密等级" prop="smdj" class="one-line">
              <el-select v-model="tjlist.smdj" placeholder="请选择变更前涉密等级" disabled style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更时间" prop="bgsj" class="one-line">
              <!-- <el-input v-model="tjlist.bgsj" clearable></el-input> -->
              <el-date-picker v-model="tjlist.bgsj" clearable type="date" placeholder="选择日期" style="width:100%;"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <!-- <el-form-item label="变更后部门" prop="bghbmmc" class="one-line">
              <el-cascader v-model="tjlist.bghbmmc" :options="regionOption" :props="regionParams" filterable
                style="width:100%;" ref="cascaderArr" @change="handleChange(1)"></el-cascader>
            </el-form-item> -->
            <el-form-item label="变更后岗位名称" prop="bgsmgw" class="one-line">
              <el-select v-model="tjlist.bgsmgw" placeholder="请选择变更后岗位名称" style="width:100%;" multiple
                @change="handleSelectBghgwmc(tjlist, $event)">
                <el-option v-for="(item, index) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="index">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更后涉密等级" prop="bgsmdj" class="one-line">
              <el-select v-model="tjlist.bgsmdj" placeholder="请选择变更后涉密等级" style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="涉密人员岗位变更信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%"
          class="xg">
          <el-form ref="form" :model="xglist" label-width="150px" size="mini" disabled>
            <el-form-item label="姓名" prop="xm" class="one-line">
              <el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input>
            </el-form-item>
            <el-form-item label="身份证号码" prop="sfzhm" class="one-line">
              <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="sfzhm = $event.target.value"
                placeholder="身份证号码" v-model="xglist.sfzhm" clearable></el-input>
            </el-form-item>
            <el-form-item label="所在部门" prop="bmmc" class="one-line">
              <el-input v-model="xglist.bmmc" clearable placeholder="部门"></el-input>
            </el-form-item>
            <el-form-item label="变更前岗位名称" prop="gwmc" class="one-line">
              <el-select v-model="xglist.gwmc" placeholder="变更前岗位名称" disabled style="width:100%;" multiple>
                <el-option v-for="item in gwmc" :label="item.gwmc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更前涉密等级" prop="smdj" class="one-line">
              <el-select v-model="xglist.smdj" placeholder="请选择变更前涉密等级" style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更时间" prop="bgsj" class="one-line">
              <!-- <el-input v-model="tjlist.bgsj" clearable></el-input> -->
              <el-date-picker v-model="xglist.bgsj" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <!-- <el-form-item label="变更后部门" prop="bghbmmc" class="one-line">
              <el-input placeholder="变更后部门" v-model="xglist.bghbmmc" clearable></el-input>
            </el-form-item> -->
            <el-form-item label="变更后岗位名称" prop="bgsmgw" class="one-line">
              <el-select v-model="xglist.bgsmgw" placeholder="请选择变更后岗位名称" style="width:100%;" multiple
                @change="handleSelectBghgwmc(tjlist, $event)">
                <el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
                </el-option>
              </el-select>

            </el-form-item>
            <el-form-item label="变更后涉密等级" prop="bgsmdj" class="one-line">
              <el-select v-model="xglist.bgsmdj" placeholder="请选择变更后涉密等级" style="width:100%;">
                <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getMjbgList,
  saveMjbg,
  getAllYhxx,
  removeMjbg,
  getZzjgList
} from '../../../api/index'
import {
  getMjbgLsPage,
} from '../../../api/djgwbg'
import {
  getAllSmdj,
  getAllGwqdyj,
  getAllXl
} from '../../../api/xlxz'
import { dateFormatNYR } from '@/utils/moment'

// import {
//   getMjbgLsPage
// } from '../../../api/lstz'

import {
  exportLsGwbgData
} from '../../../api/dcwj'
import { getAllGwxx } from '../../../api/qblist'
export default {
  components: {},
  props: {},
  data() {
    return {
      yearSelect: [],
      bgsmgw: '',
      gwbgjy: 0,
      smdjxz: [],
      gwbgList: [],
      gwmc: [
      ],
      formInline: {
        bgsj: [],
        tzsj: new Date().getFullYear().toString()
      },
      tjlist: {
        xm: '',
        sfzhm: '',
        xb: '',
        nl: '',
        bmmc: '',
        // bghbmmc: '',
        gwmc: '',
        smdj: '',
        gwmch: '',
        smdjh: '',
        bgsj: '',
        bgsmgw: '',
        bgsmdj: '',
        gwqdyj: '',
        zgxl: '',
        zw: '',
        zj: '',
        jbzc: '',
        gwdyjb: '',
        sflx: '',
        yrxs: '',
        sfsc: '',
        sfcrj: '',
        sfbgzj: '',
        bgsj: '',
        bz: '',
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        xm: [{
          required: true,
          message: '请输入姓名',
          trigger: ['blur', 'change'],
        },],
        sfzhm: [{
          required: true,
          message: '请输入身份证号码',
          trigger: 'blur'
        },
        {
          min: 18,
          max: 18,
          message: '长度为16个字符',
          trigger: 'blur'
        }
        ],
        xb: [{
          required: true,
          message: '请选择性别',
          trigger: 'blur'
        },],
        nl: [{
          required: true,
          message: '请输入年龄',
          trigger: 'blur'
        },],
        bmmc: [{
          required: true,
          message: '请输入部门',
          trigger: 'blur'
        },],
        // bghbmmc: [{
        //   required: true,
        //   message: '请选择变更后部门',
        //   trigger: ['blur', 'change'],
        // },],
        gwmc: [{
          required: true,
          message: '请输入岗位名称',
          trigger: 'blur'
        },],
        smdj: [{
          required: true,
          message: '请选择涉密等级',
          trigger: 'blur'
        },],
        bgsj: [{
          required: true,
          message: '请选择变更时间',
          trigger: 'blur'
        },],
        bgsmgw: [{
          required: true,
          message: '请输入变更后岗位名称',
          trigger: 'blur'
        },],
        bgsmdj: [{
          required: true,
          message: '请选择变更后涉密等级',
          trigger: 'blur'
        },],
        gwqdyj: [{
          required: true,
          message: '请选择岗位确定依据',
          trigger: 'blur'
        },],
        zgxl: [{
          required: true,
          message: '请选择最高学历',
          trigger: 'blur'
        },],
        zw: [{
          required: true,
          message: '请输入职务',
          trigger: 'blur'
        },],
        zj: [{
          required: true,
          message: '请输入职级',
          trigger: 'blur'
        },],
        jbzc: [{
          required: true,
          message: '请选择级别职称',
          trigger: 'blur'
        },],
        gwdyjb: [{
          required: true,
          message: '请选择岗位对应级别',
          trigger: 'blur'
        },],
        sflx: [{
          required: true,
          message: '请选择身份类型',
          trigger: 'blur'
        },],
        yrxs: [{
          required: true,
          message: '请选择用人形式',
          trigger: 'blur'
        },],
        sfsc: [{
          required: true,
          message: '请选择是否审查',
          trigger: 'blur'
        },],
        sfcrj: [{
          required: true,
          message: '请选择是否出入境登记备案',
          trigger: 'blur'
        },],
        sfbgzj: [{
          required: true,
          message: '请选择是否统一保管出入境证件',
          trigger: 'blur'
        },],
        bgsj: [{
          required: true,
          message: '请选择上岗时间（现涉密岗位）',
          trigger: 'blur'
        },],
        // bz: [{
        // 	required: true,
        // 	message: '请输入文件名',
        // 	trigger: 'blur'
        // },],
      },
      xglist: {},
      restaurants: [],
      restaurantsBghgwmc: [],
      updateItemOld: {},
      xqdialogVisible: false,
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: '',
      zzjgmc: [],
      bghbmid: '',
    }
  },
  computed: {},
  mounted() {
    //获取最近十年的年份
    let yearArr = []
    for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
      yearArr.push(
        {
          label: i.toString(),
          value: i.toString()
        })
    }
    yearArr.unshift({
      label: "全部",
      value: ""
    })
    this.yearSelect = yearArr
    this.smdj()
    // this.gwqdyj()
    // this.zgxl()
    this.gwbg()
    this.smry()
    this.zzjg()
  },
  methods: {
    //全部组织机构List
    async zzjg() {
      let zzjgList = await getZzjgList()
      console.log(zzjgList);
      this.zzjgmc = zzjgList
      let shu = []
      console.log(this.zzjgmc);
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            // console.log(item, item1);
            childrenRegionVo.push(item1)
            // console.log(childrenRegionVo);
            item.childrenRegionVo = childrenRegionVo
          }
        });
        // console.log(item);
        shu.push(item)
      })

      console.log(shu);
      console.log(shu[0].childrenRegionVo);
      let shuList = []
      shu.forEach(item => {
        if (item.fbmm == '') {
          shuList.push(item)
        }
      })
      console.log(shuList);
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    //获取涉密等级信息
    async smdj() {
      let data = await getAllSmdj()
      this.smdjxz = data
    },
    // //获取岗位确定依据
    // async gwqdyj() {
    //   let data = await getAllGwqdyj()
    //   console.log(data);
    //   this.gwqdyjxz = data
    // },
    // //获取最高学历
    // async zgxl() {
    //   let data = await getAllXl()
    //   console.log(data);
    //   this.zgxlxz = data
    // },
    Radio(val) {

    },
    mbxzgb() {

    },
    mbdc() {

    },
    //导入
    chooseFile() {

    },
    //----成员组选择
    handleSelectionChange(val) {

    },
    //---确定导入成员组
    drcy() {

    },
    //----表格导入方法
    readExcel(e) {

    },
    //查询
    onSubmit() {
      this.page = 1
      this.gwbg()
    },
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))
      this.xglist = JSON.parse(JSON.stringify(row))
      this.xglist.bgsj = dateFormatNYR(this.xglist.bgsj)
      this.xqdialogVisible = true
    },
    returnSy() {
      this.$router.push("/tzglsy");
    },
    async gwbg() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
      }
      if (this.formInline.bgsj != null) {
        params.kssj = this.formInline.bgsj[0]
        params.jssj = this.formInline.bgsj[1]
      }
      // params.tznf = this.formInline.tzsj
      if(this.formInline.tzsj){
        params.tznf = this.formInline.tzsj
      }
      let resList = await getMjbgLsPage(params)
      this.gwbgList = resList.records
      this.gwbgList.forEach((item) =>{
        item.bgsj = dateFormatNYR(item.bgsj)
      })
      // this.dclist = resList.list_total
      // this.dclist.forEach((item, label) => {
      //   this.xh.push(label + 1)
      // })
      this.total = resList.total
    },
    //删除
    shanchu(id) {
      let that = this
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            let params = {
              jlid: item.jlid,
              dwid: item.dwid,
            }
            removeMjbg(params).then(() => {
              that.gwbg()
            })
            console.log("删除：", item);
            console.log("删除：", item);
          })
          // let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });

        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {
      this.dialogVisible = true
    },

    //导出
    async exportList() {
      let param = {}
      if (this.formInline.bgsj != null) {
        param.kssj = this.formInline.bgsj[0]
        param.jssj = this.formInline.bgsj[1]
      }
      param.nf = this.formInline.tzsj
      let returnData = await exportLsGwbgData(param);
      let date = new Date()
      let sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "涉密岗位变更记录表-" + sj + ".xls");
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) // 创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) // 指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            dwid: '901',
            smryid: this.tjlist.smryid,
            sfzhm: this.tjlist.sfzhm,
            xm: this.tjlist.xm,
            ybmid: this.tjlist.ybmid,
            bmmc: this.tjlist.bmmc,
            bghbmid: this.bghbmid,
            // bghbmmc: this.tjlist.bghbmmc.join('/'),
            smdj: this.tjlist.smdj,
            bgsmdj: this.tjlist.bgsmdj,
            gwmc: this.tjlist.gwmc,
            bgsmgw: this.tjlist.bgsmgw,
            bgsj: this.tjlist.bgsj,
            zw: this.tjlist.zw,
            zj: this.tjlist.zj,
            jbzc: this.tjlist.jbzc,
            sflx: this.tjlist.sflx,
            yrxs: this.tjlist.yrxs,
            sfsc: this.tjlist.sfsc,
            sfcrj: this.tjlist.sfcrj,
            sfbgzj: this.tjlist.sfbgzj,
            rzsj: this.tjlist.rzsj,
            sgsj: this.tjlist.sgsj,
            scsj: this.tjlist.scsj,
            sbnf: "2023年",
            bz: this.tjlist.bz,
            cjrid: "111",
          }
          let that = this
          saveMjbg(params).then(() => {
            that.resetForm()
            that.gwbg()
            that.smry()
          })
          this.dialogVisible = false
          this.$message({
            message: '添加成功',
            type: 'success'
          });



        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    deleteTkglBtn() {

    },
    selectRow(val) {
      this.selectlistRow = val
    },
    //列表分页--跳转页数
    handleCurrentChange(val) { },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {

    },
    //添加重置
    resetForm() {
      this.tjlist.xm = '',
        this.tjlist.sfzhm = '',
        this.tjlist.xb = '',
        this.tjlist.nl = '',
        this.tjlist.bmmc = '',
        this.tjlist.gwmc = '',
        this.tjlist.smdj = '',
        this.tjlist.bgsj = this.Date,
        this.tjlist.bgsmgw = '',
        this.tjlist.bgsmdj = '',
        this.tjlist.gwqdyj = '',
        this.tjlist.zgxl = '',
        this.tjlist.zw = '',
        this.tjlist.zj = '',
        this.tjlist.jbzc = '',
        this.tjlist.sflx = '',
        this.tjlist.yrxs = '',
        this.tjlist.sfsc = '',
        this.tjlist.sfcrj = '',
        this.tjlist.sfbgzj = '',
        this.tjlist.bgsj = '',
        this.tjlist.bz = ''
    },
    handleClose(done) {

      this.dialogVisible = false
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据

      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async smry() {
      let resList = await getAllYhxx()
      this.restaurants = resList;
      console.log("this.restaurants", this.restaurants);
      console.log(resList)
    },
    handleSelect(item) {
      console.log(item);
      this.tjlist.ybmid = item.bmid
      this.tjlist.xm = item.xm
      this.tjlist.sfzhm = item.sfzhm
      this.tjlist.xb = item.xb
      this.tjlist.nl = item.nl
      this.tjlist.bmmc = item.bmmc
      this.tjlist.gwmc = item.gwmc
      this.tjlist.smdj = item.smdj
      this.tjlist.gwqdyj = item.gwqdyj
      this.tjlist.zgxl = item.zgxl
      this.tjlist.zw = item.zw
      this.tjlist.zj = item.zj
      this.tjlist.jbzc = item.jbzc
      this.tjlist.sflx = item.sflx
      this.tjlist.yrxs = item.yrxs
      this.tjlist.sfsc = item.sfsc
      this.tjlist.rzsj = item.rzsj
      this.tjlist.sfcrj = item.sfcrj
      this.tjlist.sfbgzj = item.sfbgzj
      this.tjlist.zgzt = item.zgzt
      this.tjlist.sbnf = item.sbnf
      this.tjlist.bgsj = item.bgsj
      this.tjlist.smryid = item.smryid
      this.tjlist.sgsj = item.sgsj
      this.tjlist.scsj = item.scsj
    },
    dwxxByDwmc1(xm) {


      /**
       * 有旧的数据
       * 直接给到页面显示用的数据dwxxShow
       */
      // this.dwxxShow = smryDataBase;
      //把用户实际操作的表单信息中的dwid设置为数据库里的数据
      // this.dwidPage = smryDataBase.dwid;
    },
    querySearchBghgwmc(queryString, cb) {
      var restaurants = this.restaurantsBghgwmc;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterBghgwmc(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据

      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilterBghgwmc(queryString) {
      return (restaurant) => {
        return (restaurant.gwmc.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    smbm() {

    },

    handleSelectBghgwmc(item, i) {
      console.log(i);

      this.gwmc.forEach(item1 => {
        if (i == item1.gwmc) {
          console.log(item1);
          this.tjlist.bgsmdj = item1.smdj
        }

      })
    },
    dwxxByDwmc2(xm) {



    },
    fh() {
      this.$router.go(-1)
    },
    querySearch1(queryString, cb) {

    },
    bmxzsj(item) {

    },
    async handleChange(index) {
      let nodesObj = this.$refs['cascaderArr'].getCheckedNodes()[0].data
      this.bghbmid = nodesObj.bmm
      let resList
      if (index == 1) {
        let params = {
          bmmc: this.tjlist.bghbmmc.join('/')
        }
        resList = await getAllGwxx(params)
      } else if (index == 2) {
        let params1 = {
          bmmc: this.xglist.bghbmmc.join('/')
        }
        resList = await getAllGwxx(params1)
      }
      console.log(resList);
      this.restaurantsBghgwmc = resList;
      this.gwmc = resList
      if (this.gwmc.length == 0) {
        this.$message.error('该部门没有添加岗位');
      }
      console.log(this.gwmc);
      this.tjlist.bgsmgw = ''
      this.tjlist.bgsmdj = ''
    },
    bghbm() {

    },
    forbgqsmdj(row) {
      let hxsj
      this.smdjxz.forEach(item => {
        if (row.smdj == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    forbghsmdj(row) {
      let hxsj
      this.smdjxz.forEach(item => {
        if (row.bgsmdj == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },

  },
  watch: {

  }
}

</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

/deep/.inline-input .el-input--medium .el-input__inner {
  width: 556px;
  height: 28px;
  font-size: 12px;
}

/deep/.el-radio-group {
  width: 184px;
}

/deep/.el-dialog {
  margin-top: 3vh !important;
}

/* /deep/.el-select .el-input__inner{
	width: 587.96px;
} */

.cd {
  width: 184px;
}

/* /deep/.el-form-item__label {
	text-align: left;
} */

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

/deep/.el-select .el-select__tags>span {
  display: flex !important;
  flex-wrap: wrap;
}

/deep/.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.bz {
  height: 72px !important;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

/deep/.el-dialog__body .el-form>div>div {
  width: auto;
  max-width: 100%;
}

/deep/.el-dialog__body .el-form>div .el-form-item__label {
  width: 155px !important;
}
</style>
