{"version": 3, "sources": ["webpack:///src/renderer/view/zczp/childPage/ccdnsjgDj.vue", "webpack:///./src/renderer/view/zczp/childPage/ccdnsjgDj.vue?adca", "webpack:///./src/renderer/view/zczp/childPage/ccdnsjgDj.vue"], "names": ["ccdnsjgDj", "data", "dialogObj", "dxdfArr", "syxmDfArr", "showDxList", "spanArr", "reverse", "activities", "showMdmenu", "dwmc", "nsjg", "computed", "syxmzf", "zf", "this", "for<PERSON>ach", "nr", "sfsynr", "fz", "syxmdf", "resDf", "df", "syxmbfb", "num", "Math", "round", "components", "methods", "pfss", "_this2", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "params", "data1", "wrap", "_context", "prev", "next", "dwid", "rwid", "jgid", "Object", "zczp", "sent", "djzt", "item", "item1", "zcxid", "dxmc", "zcxmc", "console", "log", "stop", "save", "_this3", "_callee2", "_context2", "ccbmRK", "submit", "dxXxList", "zt", "_this4", "_callee3", "list", "xx", "_params", "_context3", "code", "jgmc", "push", "$router", "path", "query", "rwmc", "jcjd", "$message", "message", "type", "updateTable", "_this5", "_callee4", "bmxxzcjlList", "resBmxxzcjlList", "_context4", "bmxxzcjl", "gdkffz", "jsqkf", "ykf", "getSpanArr", "returnSy", "go", "getRwxxDwxxCcnsjgxxByRwidCcdnsjgid", "rwxxDwxxCcnsjgxx", "selectRwxxDwxxCcnsjgxxByRwidCcdnsjgid", "assign_default", "JSON", "parse", "stringify_default", "i", "length", "pos", "objectSpanMethod", "_ref", "row", "column", "rowIndex", "columnIndex", "rowspan", "scid", "colspan", "handleKfjsq", "checked", "handleSynrTextarea", "undefined", "synr", "handleDfsmTextarea", "dfsm", "getZD", "zdList", "getBmxxzcjlZD", "mouseoverMdMenu", "mouseoutMenu", "watch", "handler", "newVal", "oldVal", "_this6", "_this", "dxMdIdArr", "dxIdArr", "nrIndex", "mdIndex", "indexOf", "kffs", "nrid", "children", "href", "deep", "mounted", "$route", "bmmc", "childPage_ccdnsjgDj", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "width", "height", "staticClass", "float", "attrs", "inline", "size", "_v", "_s", "icon", "on", "click", "clear", "overflow-y", "margin-bottom", "span-method", "header-cell-style", "text-align", "border", "label", "scopedSlots", "_u", "key", "fn", "scope", "id", "$index", "scnr", "align", "model", "value", "callback", "$$v", "$set", "expression", "margin-right", "name", "min", "zdkffz", "max", "zgkffz", "step", "kfzf", "change", "$event", "_e", "prop", "rows", "input", "trim", "color", "display", "font-weight", "margin-left", "list-style", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "wPAoLAA,GACAC,KADA,WAEA,OACAC,aAKAC,WAEAC,aAEAC,cAEAC,WAEAC,SAAA,EAEAC,cAEAC,YAAA,EACAC,KAAA,GACAC,KAAA,KAGAC,UACAC,OADA,WAEA,IAAAC,EAAA,EAMA,OALAC,KAAAV,WAAAW,QAAA,SAAAC,GACAA,EAAAC,SACAJ,GAAA,EAAAG,EAAAE,MAGAL,GAEAM,OAVA,WAWA,IAAAC,EAAA,EAIA,OAHAN,KAAAX,UAAAY,QAAA,SAAAM,GACAD,GAAA,EAAAC,IAEAD,GAEAE,QAjBA,WAkBA,MAAAR,KAAAF,OACA,SAGA,IAAAW,EAAAT,KAAAK,OAAAL,KAAAF,OACA,OAAAY,KAAAC,MAAA,IAAAF,GAAA,KAGAG,cAEAC,SACAC,KADA,WACA,IAAAC,EAAAf,KAAA,OAAAgB,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAApC,EAAA,OAAA+B,EAAAC,EAAAK,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACAL,GACAM,KAAAZ,EAAAY,KACAC,KAAAb,EAAAa,KACAC,KAAAd,EAAAc,MAEAP,OANA,EAAAE,EAAAE,KAAA,EAOAI,OAAAC,EAAA,EAAAD,GAPA,UAOA5C,EAPAsC,EAAAQ,KAQA,KAAAjB,EAAAkB,KARA,CAAAT,EAAAE,KAAA,gBAAAF,EAAAE,KAAA,EASAI,OAAAC,EAAA,EAAAD,GATA,OASAR,EATAE,EAAAQ,KAAAR,EAAAE,KAAA,wBAAAF,EAAAE,KAAA,GAWAI,OAAAC,EAAA,EAAAD,CAAAT,GAXA,QAWAC,EAXAE,EAAAQ,KAAA,QAcAV,EAAApC,KAAAe,QAAA,SAAAiC,GACAhD,OAAAe,QAAA,SAAAkC,GACAA,EAAAC,OAAAF,EAAAE,QACAF,EAAAG,KAAAF,EAAAG,WAIAvB,EAAAzB,WAAAgC,EAAApC,KACAqD,QAAAC,IAAAzB,EAAAzB,WAAA,mBAtBA,yBAAAkC,EAAAiB,SAAArB,EAAAL,KAAAC,IA2BA0B,KA5BA,WA4BA,IAAAC,EAAA3C,KAAA,OAAAgB,IAAAC,EAAAC,EAAAC,KAAA,SAAAyB,IAAA,OAAA3B,EAAAC,EAAAK,KAAA,SAAAsB,GAAA,cAAAA,EAAApB,KAAAoB,EAAAnB,MAAA,OACAa,QAAAC,IAAA,kBAAAG,EAAArD,YAEAqD,EAAAG,OAAAH,EAAArD,WAAA,GAHA,wBAAAuD,EAAAJ,SAAAG,EAAAD,KAAA3B,IAMA+B,OAlCA,WAmCA/C,KAAA8C,OAAA9C,KAAAV,WAAA,IAGAwD,OAtCA,SAsCAE,EAAAC,GAAA,IAAAC,EAAAlD,KAAA,OAAAgB,IAAAC,EAAAC,EAAAC,KAAA,SAAAgC,IAAA,IAAAC,EAAAC,EAAAhC,EAAAiC,EAAA,OAAArC,EAAAC,EAAAK,KAAA,SAAAgC,GAAA,cAAAA,EAAA9B,KAAA8B,EAAA7B,MAAA,cACAa,QAAAC,IAAAQ,GACAI,KACAC,GACAzB,KAAAsB,EAAAtB,KACAC,KAAAqB,EAAArB,MALA0B,EAAA7B,KAAA,EAOAI,OAAAC,EAAA,EAAAD,CAAAuB,GAPA,UAQA,KARAE,EAAAvB,KAQAwB,KARA,CAAAD,EAAA7B,KAAA,gBASAsB,EAAA/C,QAAA,SAAAiC,GACAA,EAAAP,KAAAuB,EAAAvB,KACAO,EAAAN,KAAAsB,EAAAtB,KACAM,EAAAL,KAAAqB,EAAArB,KACAK,EAAAuB,KAAAP,EAAAtD,KACAwD,EAAAM,KAAAxB,KAdAqB,EAAA7B,KAAA,GAgBAI,OAAAC,EAAA,EAAAD,CAAAsB,GAhBA,QAAAG,EAAAvB,KAiBA,GAAAiB,GACA5B,GACAM,KAAAuB,EAAAvB,KACAE,KAAAqB,EAAArB,KACAD,KAAAsB,EAAAtB,KACAK,KAAAgB,GAEAnB,OAAAC,EAAA,EAAAD,CAAAT,GACA,GAAA6B,EAAAD,GACAC,EAAAS,QAAAD,MACAE,KAAA,WACAC,OACAjC,KAAAsB,EAAAtB,KACAkC,KAAAZ,EAAAY,KACAC,KAAAb,EAAAa,QAIAb,EAAAS,QAAAD,MACAE,KAAA,QACAC,OACAjC,KAAAsB,EAAAtB,KACAkC,KAAAZ,EAAAY,KACAC,KAAAb,EAAAa,QAKAb,EAAAc,UACAC,QAAA,SACAC,KAAA,aAEA,GAAAjB,IACAK,GACA3B,KAAAuB,EAAAvB,KACAE,KAAAqB,EAAArB,KACAD,KAAAsB,EAAAtB,KACAK,KAAAgB,GAEAnB,OAAAC,EAAA,EAAAD,CAAAwB,GACA,GAAAJ,EAAAD,GACAC,EAAAS,QAAAD,MACAE,KAAA,WACAC,OACAjC,KAAAsB,EAAAtB,KACAkC,KAAAZ,EAAAY,KACAC,KAAAb,EAAAa,QAIAb,EAAAS,QAAAD,MACAE,KAAA,QACAC,OACAjC,KAAAsB,EAAAtB,KACAkC,KAAAZ,EAAAY,KACAC,KAAAb,EAAAa,QAIAb,EAAAc,UACAC,QAAA,OACAC,KAAA,aA9EA,yBAAAX,EAAAd,SAAAU,EAAAD,KAAAlC,IAqFAmD,YA3HA,WA2HA,IAAAC,EAAApE,KAAA,OAAAgB,IAAAC,EAAAC,EAAAC,KAAA,SAAAkD,IAAA,IAAAC,EAAAjD,EAAAkD,EAAA,OAAAtD,EAAAC,EAAAK,KAAA,SAAAiD,GAAA,cAAAA,EAAA/C,KAAA+C,EAAA9C,MAAA,UAEA4C,OAFA,EAIAjD,GACAM,KAAAyC,EAAAzC,KACAC,KAAAwC,EAAAxC,KACAC,KAAAuC,EAAAvC,MAEA,KAAAuC,EAAAnC,KATA,CAAAuC,EAAA9C,KAAA,eAAA8C,EAAA9C,KAAA,EAUAI,OAAAC,EAAA,EAAAD,GAVA,OAUAwC,EAVAE,EAAAxC,KAAAwC,EAAA9C,KAAA,uBAAA8C,EAAA9C,KAAA,GAYAI,OAAAC,EAAA,EAAAD,CAAAT,GAZA,QAYAiD,EAZAE,EAAAxC,KAAA,QAcAuC,KACAhC,QAAAC,IAAA,eAAA8B,GACAA,EAAApF,KAAAe,QAAA,SAAAwE,GAEAA,EAAAC,OAAAD,EAAArE,GACAqE,EAAAE,MAAAF,EAAAG,IAEAH,EAAAC,OAAAD,EAAArE,KAWAmE,EAAAD,EAAApF,KACAqD,QAAAC,IAAA+B,GAEAH,EAAA7E,QAAA6E,EAAAS,WAAAN,GACAH,EAAA9E,WAAAiF,EApCA,yBAAAC,EAAA/B,SAAA4B,EAAAD,KAAApD,IAsCA8D,SAjKA,WAkKA9E,KAAA2D,QAAAoB,IAAA,IAGAC,mCArKA,WAsKA,IAAAC,EAAAC,sCAAAlF,KAAAb,WACAoD,QAAAC,IAAA,mBAAAyC,GACME,IAANnF,KAAAb,UAAA8F,GACAjF,KAAAb,UAAAiG,KAAAC,MAAAC,IAAAtF,KAAAb,aAGA0F,WA5KA,SA4KAzB,GACAb,QAAAC,IAAAY,GAEA,IADA,IAAA7D,KACAgG,EAAA,EAAAA,EAAAnC,EAAAoC,OAAAD,IACA,IAAAA,GACAhG,EAAAmE,KAAA,GACA1D,KAAAyF,IAAA,GAGArC,EAAAmC,GAAAnD,OAAAgB,EAAAmC,EAAA,GAAAnD,OACA7C,EAAAS,KAAAyF,MAAA,EACAlG,EAAAmE,KAAA,KAEAnE,EAAAmE,KAAA,GACA1D,KAAAyF,IAAAF,GAIA,OAAAhG,GAEAmG,iBAhMA,SAAAC,GAgMA,IAAAC,EAAAD,EAAAC,IAAAD,EAAAE,OAAAF,EAAAG,SACA,OADAH,EAAAI,YAIA,OACAC,QAFAhG,KAAAT,QAAAqG,EAAAK,KAAA,GAGAC,QAAA,IAKAC,YA3MA,SA2MAjG,GACAA,EAAAkG,SAAA,GAKAC,mBAjNA,SAiNAnG,QACAoG,IAAApG,EAAAqG,MAAA,IAAArG,EAAAqG,KACArG,EAAAC,QAAA,EAGAD,EAAAC,QAAA,GAOAqG,mBA7NA,SA6NAtG,QACAoG,IAAApG,EAAAuG,MAAA,IAAAvG,EAAAuG,KACAvG,EAAAkG,SAAA,EAEAlG,EAAAkG,SAAA,GAMAM,MAvOA,WAyOA,IAAAC,EAAAC,gBAYA,OAXArE,QAAAC,IAAAmE,GACAA,EAAA1G,QAAA,SAAAC,GACAA,EAAA0E,IAAA,EACA1E,EAAAkG,SAAA,EACAlG,EAAAyE,MAAA,EACAzE,EAAAwE,OAAAxE,EAAAE,GAEAF,EAAAC,QAAA,IAEAH,KAAAT,QAAAS,KAAA6E,WAAA8B,GAEAA,GAGAE,gBAxPA,WAyPA7G,KAAAN,YAAA,GAGAoH,aA5PA,WA6PA9G,KAAAN,YAAA,IAGAqH,OACAzH,YACA0H,QADA,SACAC,EAAAC,GAAA,IAAAC,EAAAnH,KACAuC,QAAAC,IAAA,6BAAAyE,GACA,IAAAG,EAAApH,KAEAA,KAAAP,cAEA,IAAA4H,KAEAD,EAAA/H,aAEA,IAAAiI,KAEAL,EAAAhH,QAAA,SAAAC,EAAAqH,GAEArH,EAAAsH,QAAA,MAAAD,GACA,GAAAD,EAAAG,QAAAvH,EAAAkC,QACAkF,EAAA5D,KAAAxD,EAAAkC,OAGAlC,EAAAkG,SACA,GAAAlG,EAAAwH,OACAxH,EAAA0E,IAAA1E,EAAAyE,OAEA,GAAAzE,EAAAwH,OACAxH,EAAA0E,IAAA1E,EAAAwE,QAEAnC,QAAAC,IAAAtC,EAAAyH,KAAAzH,EAAAE,GAAAF,EAAA0E,IAAA1E,EAAAE,GAAAF,EAAA0E,KACA1E,EAAAK,GAAAL,EAAAE,GAAAF,EAAA0E,KAEA,GAAAyC,EAAAI,QAAAvH,EAAAkC,QACAiF,EAAA3D,KAAAxD,EAAAkC,QAEA,GAAAiF,EAAAI,QAAAvH,EAAAkC,SACA+E,EAAA1H,WAAA4H,EAAAI,QAAAvH,EAAAkC,SACA+E,EAAA1H,WAAAiE,MACArB,KAAAnC,EAAAmC,KACAuF,cAGAT,EAAA1H,WAAA4H,EAAAI,QAAAvH,EAAAkC,QAAAwF,SAAAlE,MACAmE,KAAA,IAAA3H,EAAAsH,QACAtH,QACA0E,IAAA1E,EAAA0E,SAIA1E,EAAA0E,IAAA,EACA1E,EAAAyE,MAAA,EACAzE,EAAAK,GAAAL,EAAAE,IAGAF,EAAAC,QACAiH,EAAA/H,UAAAqE,KAAAxD,EAAAK,OAIAuH,MAAA,IAGAC,QAlXA,WAmXAxF,QAAAC,IAAAxC,KAAAgI,OAAAnE,OACA7D,KAAAL,KAAAK,KAAAgI,OAAAnE,MAAAlE,KACAK,KAAAJ,KAAAI,KAAAgI,OAAAnE,MAAAoE,KACAjI,KAAA2B,KAAA3B,KAAAgI,OAAAnE,MAAAlC,KACA3B,KAAA4B,KAAA5B,KAAAgI,OAAAnE,MAAAjC,KACA5B,KAAA6B,KAAA7B,KAAAgI,OAAAnE,MAAAhC,KACA7B,KAAA8D,KAAA9D,KAAAgI,OAAAnE,MAAAC,KACA9D,KAAAiC,KAAAjC,KAAAgI,OAAAnE,MAAA5B,KACAjC,KAAAiD,GAAAjD,KAAAgI,OAAAnE,MAAAZ,GACAjD,KAAA+D,KAAA/D,KAAAgI,OAAAnE,MAAAE,KACA/D,KAAAc,OAiBAd,KAAAmE,gBC/jBe+D,GADEC,OAFjB,WAA0B,IAAAC,EAAApI,KAAaqI,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,MAAA,OAAAC,OAAA,UAAgCJ,EAAA,OAAYK,YAAA,SAAmBL,EAAA,WAAgBK,YAAA,mBAAAH,aAA4CI,MAAA,QAAeC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,UAAgBN,EAAA,OAAAH,EAAAa,GAAA,MAAAb,EAAAc,GAAAd,EAAAzI,WAAAyI,EAAAa,GAAA,KAAAV,EAAA,gBAA8EE,aAAaI,MAAA,UAAgBN,EAAA,OAAAH,EAAAa,GAAA,UAAAb,EAAAc,GAAAd,EAAAxI,YAAA,GAAAwI,EAAAa,GAAA,KAAAV,EAAA,WAAiFK,YAAA,mBAAAH,aAA4CI,MAAA,SAAgBC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAO5E,KAAA,UAAA8E,KAAA,SAAAG,KAAA,wBAA+DC,IAAKC,MAAAjB,EAAArF,UAAoBqF,EAAAa,GAAA,kCAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAA0EE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAO5E,KAAA,UAAA8E,KAAA,SAAAG,KAAA,oBAA2DC,IAAKC,MAAAjB,EAAA1F,QAAkB0F,EAAAa,GAAA,wCAAAb,EAAAa,GAAA,KAAAV,EAAA,OAAuEE,aAAaa,MAAA,WAAgB,GAAAlB,EAAAa,GAAA,KAAAV,EAAA,OAA4BE,aAAac,aAAA,SAAAZ,OAAA,iDAAAa,gBAAA,SAAuGjB,EAAA,WAAgBE,aAAae,gBAAA,SAAuBjB,EAAA,YAAiBO,OAAO5J,KAAAkJ,EAAA9I,WAAAmK,cAAArB,EAAA1C,iBAAAgE,qBAA8EC,aAAA,UAAyBC,OAAA,MAAcrB,EAAA,mBAAwBO,OAAOe,MAAA,MAAAnB,MAAA,MAA2BoB,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAA3B,EAAA,OAAAA,EAAA,QAA6BO,OAAOqB,GAAA/B,EAAA9I,WAAA4K,EAAAE,QAAA5C,WAA2CY,EAAAa,GAAAb,EAAAc,GAAAd,EAAA9I,WAAA4K,EAAAE,QAAA/H,MAAA,2BAA4E+F,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOe,MAAA,QAAeC,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAA3B,EAAA,OAAAA,EAAA,QAA6BO,OAAOqB,GAAA/B,EAAA9I,WAAA4K,EAAAE,QAAA5C,WAA2CY,EAAAa,GAAAb,EAAAc,GAAAd,EAAA9I,WAAA4K,EAAAE,QAAAC,MAAA,2BAA4EjC,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOe,MAAA,KAAAnB,MAAA,MAAA4B,MAAA,UAA4CR,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAA3B,EAAA,UAAAH,EAAA9I,WAAA4K,EAAAE,QAAA1C,KAAAa,EAAA,OAAAA,EAAA,qBAA6FgC,OAAOC,MAAApC,EAAA9I,WAAA4K,EAAAE,QAAA,QAAAK,SAAA,SAAAC,GAAsEtC,EAAAuC,KAAAvC,EAAA9I,WAAA4K,EAAAE,QAAA,UAAAM,IAAuDE,WAAA,sCAAgDrC,EAAA,eAAoBE,aAAaoC,eAAA,SAAuB/B,OAAQe,MAAA,GAAAiB,KAAA,cAA8B1C,EAAAa,GAAA,KAAAV,EAAA,mBAAoCE,aAAaC,MAAA,SAAgBI,OAAQiC,IAAA3C,EAAA9I,WAAA4K,EAAAE,QAAAY,OAAAC,IAAA7C,EAAA9I,WAAA4K,EAAAE,QAAAc,OAAAC,KAAA/C,EAAA9I,WAAA4K,EAAAE,QAAAgB,KAAApC,KAAA,QAA2II,IAAKiC,OAAA,SAAAC,GAA0B,OAAAlD,EAAAjC,YAAAiC,EAAA9I,WAAA4K,EAAAE,WAAsDG,OAAQC,MAAApC,EAAA9I,WAAA4K,EAAAE,QAAA,MAAAK,SAAA,SAAAC,GAAoEtC,EAAAuC,KAAAvC,EAAA9I,WAAA4K,EAAAE,QAAA,QAAAM,IAAqDE,WAAA,qCAA8C,OAAAxC,EAAAmD,KAAAnD,EAAAa,GAAA,QAAAb,EAAA9I,WAAA4K,EAAAE,QAAA1C,KAAAa,EAAA,OAAAA,EAAA,qBAA0GgC,OAAOC,MAAApC,EAAA9I,WAAA4K,EAAAE,QAAA,QAAAK,SAAA,SAAAC,GAAsEtC,EAAAuC,KAAAvC,EAAA9I,WAAA4K,EAAAE,QAAA,UAAAM,IAAuDE,WAAA,sCAAgDrC,EAAA,eAAoBO,OAAOe,MAAA,GAAAiB,KAAA,cAA8B1C,EAAAa,GAAAb,EAAAc,GAAAd,EAAA9I,WAAA4K,EAAAE,QAAA1F,YAAA,OAAA0D,EAAAmD,cAAgFnD,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAO0C,KAAA,KAAA3B,MAAA,KAAAnB,MAAA,KAAA4B,MAAA,YAAwDlC,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOe,MAAA,OAAAnB,MAAA,KAAA4B,MAAA,UAA6CR,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAA3B,EAAA,qBAAgCgC,OAAOC,MAAApC,EAAA9I,WAAA4K,EAAAE,QAAA,OAAAK,SAAA,SAAAC,GAAqEtC,EAAAuC,KAAAvC,EAAA9I,WAAA4K,EAAAE,QAAA,SAAAM,IAAsDE,WAAA,qCAA+CrC,EAAA,eAAoBO,OAAOe,MAAA,IAAAiB,KAAA,mBAAmC,UAAW1C,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAO0C,KAAA,GAAA3B,MAAA,QAAyBC,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAA3B,EAAA,YAAuBO,OAAO5E,KAAA,WAAAuH,KAAA,GAA2BrC,IAAKsC,MAAA,SAAAJ,GAAyB,OAAAlD,EAAA/B,mBAAA+B,EAAA9I,WAAA4K,EAAAE,WAA6DG,OAAQC,MAAApC,EAAA9I,WAAA4K,EAAAE,QAAA,KAAAK,SAAA,SAAAC,GAAmEtC,EAAAuC,KAAAvC,EAAA9I,WAAA4K,EAAAE,QAAA,wBAAAM,IAAAiB,OAAAjB,IAA2FE,WAAA,0CAAoDxC,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOe,MAAA,KAAAnB,MAAA,KAAA4B,MAAA,UAA2CR,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAA3B,EAAA,OAAAH,EAAAa,GAAAb,EAAAc,GAAAd,EAAA9I,WAAA4K,EAAAE,QAAA7J,cAAyE6H,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAO0C,KAAA,OAAA3B,MAAA,UAA8BzB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOe,MAAA,QAAeC,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAA3B,EAAA,YAAuBO,OAAO5E,KAAA,WAAAuH,KAAA,GAA2BrC,IAAKsC,MAAA,SAAAJ,GAAyB,OAAAlD,EAAA5B,mBAAA4B,EAAA9I,WAAA4K,EAAAE,WAA6DG,OAAQC,MAAApC,EAAA9I,WAAA4K,EAAAE,QAAA,KAAAK,SAAA,SAAAC,GAAmEtC,EAAAuC,KAAAvC,EAAA9I,WAAA4K,EAAAE,QAAA,wBAAAM,IAAAiB,OAAAjB,IAA2FE,WAAA,2CAAoD,WAAAxC,EAAAa,GAAA,KAAAV,EAAA,WAAwCE,aAAae,gBAAA,OAAsBV,OAAQqB,GAAA,YAAe5B,EAAA,QAAaK,YAAA,iBAA2BR,EAAAa,GAAA,WAAAV,EAAA,QAA+BE,aAAamD,MAAA,SAAexD,EAAAa,GAAAb,EAAAc,GAAAd,EAAAtI,aAAAsI,EAAAa,GAAA,KAAAV,EAAA,QAAwDK,YAAA,iBAA2BR,EAAAa,GAAA,WAAAV,EAAA,QAA+BE,aAAamD,MAAA,SAAexD,EAAAa,GAAAb,EAAAc,GAAAd,EAAA/H,aAAA+H,EAAAa,GAAA,KAAAV,EAAA,QAAwDK,YAAA,iBAA2BR,EAAAa,GAAA,qBAAAV,EAAA,QAAyCE,aAAamD,MAAA,SAAexD,EAAAa,GAAAb,EAAAc,GAAAd,EAAA5H,SAAA,WAAA4H,EAAAa,GAAA,KAAAV,EAAA,WAAkEO,OAAOqB,GAAA,YAAe5B,EAAA,QAAaE,aAAamD,MAAA,MAAAC,QAAA,QAAAnD,MAAA,OAAAoD,cAAA,UAAqE1D,EAAAa,GAAA,SAAAb,EAAAa,GAAA,KAAAV,EAAA,MAAuCE,aAAasD,cAAA,MAAAC,aAAA,UAAyCzD,EAAA,MAAWE,aAAauD,aAAA,UAAqB5D,EAAAa,GAAA,iEAAAb,EAAAa,GAAA,KAAAV,EAAA,MAA+FE,aAAauD,aAAA,UAAqB5D,EAAAa,GAAA,uHAEx8LgD,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEnN,EACAiJ,GATF,EAVA,SAAAmE,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/118.943a3ac65e09b7b7ea09.js", "sourcesContent": ["<template>\r\n  <div style=\"width: 100%;height: 100%;\">\r\n    <!---->\r\n    <div class=\"mhcx\">\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:left\">\r\n        <!-- <el-form-item style=\"float: left;\">\r\n          <div>当前审查任务：{{dialogObj.rwmc}}</div>\r\n        </el-form-item> -->\r\n        <el-form-item style=\"float: left;\">\r\n          <div>单位：{{ dwmc }}</div>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: left;\">\r\n          <div>抽查内设机构：{{ nsjg }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!---->\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:right\">\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"primary\" size=\"medium\" icon=\"el-icon-document-add\" @click=\"submit\">\r\n            提交\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"warning\" size=\"medium\" icon=\"el-icon-document\" @click=\"save\">\r\n            临时保存\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div style=\"clear: both;\"></div>\r\n    </div>\r\n    <!---->\r\n    <div style=\"overflow-y: scroll;height: calc(100% - 58px - 64px - 108px - 16px - 20px);margin-bottom: 5px;\">\r\n      <el-card style=\"margin-bottom: 1em\">\r\n        <el-table :data=\"showDxList\" :span-method=\"objectSpanMethod\" :header-cell-style=\"{ 'text-align': 'center' }\"\r\n          border>\r\n          <el-table-column label=\"自查类\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <span :id=\"showDxList[scope.$index].mdIndex\"></span>{{ showDxList[scope.$index].dxmc }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"检查内容\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <span :id=\"showDxList[scope.$index].mdIndex\"></span>{{ showDxList[scope.$index].scnr }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"扣分\" width=\"150\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <!--计数器扣分-->\r\n                <div v-if=\"showDxList[scope.$index].kffs == 3\">\r\n                  <el-checkbox-group v-model=\"showDxList[scope.$index].checked\">\r\n                    <el-checkbox label=\"\" name=\"checkbox\" style=\"margin-right: 0.5em\">\r\n                    </el-checkbox>\r\n                    <el-input-number v-model=\"showDxList[scope.$index].jsqkf\" :min=\"showDxList[scope.$index].zdkffz\"\r\n                      :max=\"showDxList[scope.$index].zgkffz\" :step=\"showDxList[scope.$index].kfzf\" size=\"mini\"\r\n                      style=\"width: 100px\" @change=\"handleKfjsq(showDxList[scope.$index])\"></el-input-number>\r\n                  </el-checkbox-group>\r\n                </div>\r\n                <!--固定扣分-->\r\n                <div v-if=\"showDxList[scope.$index].kffs == 2\">\r\n                  <el-checkbox-group v-model=\"showDxList[scope.$index].checked\">\r\n                    <el-checkbox label=\"\" name=\"checkbox\">{{\r\n                      showDxList[scope.$index].gdkffz\r\n                    }}</el-checkbox>\r\n                  </el-checkbox-group>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"fz\" label=\"分值\" width=\"50\" align=\"center\"></el-table-column>\r\n          <el-table-column label=\"实有项目\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox-group v-model=\"showDxList[scope.$index].sfsynr\">\r\n                <el-checkbox label=\"是\" name=\"checkboxSynr\"></el-checkbox>\r\n              </el-checkbox-group>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"\" label=\"实有内容\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input type=\"textarea\" v-model.trim=\"showDxList[scope.$index].synr\" :rows=\"3\"\r\n                @input=\"handleSynrTextarea(showDxList[scope.$index])\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"得分\" width=\"50\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <div>{{(showDxList[scope.$index].fz-showDxList[scope.$index].ykf)>0?(showDxList[scope.$index].fz-showDxList[scope.$index].ykf):0}}</div> -->\r\n              <div>{{ showDxList[scope.$index].df }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"kfbz\" label=\"扣分标准\"></el-table-column>\r\n          <el-table-column label=\"评分说明\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input type=\"textarea\" v-model.trim=\"showDxList[scope.$index].dfsm\" :rows=\"3\"\r\n                @input=\"handleDfsmTextarea(showDxList[scope.$index])\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-card>\r\n    </div>\r\n    <!---->\r\n    <el-card id=\"sslist\" style=\"margin-bottom: 5px;\">\r\n      <span class=\"sslist_class\">实有项目总分：<span style=\"color: red\">{{ syxmzf }}</span></span>\r\n      <span class=\"sslist_class\">实有项目得分：<span style=\"color: red\">{{ syxmdf }}</span></span>\r\n      <span class=\"sslist_class\">实有项目得分占实有项目总分百分比：<span style=\"color: red\">{{ syxmbfb }}%</span></span>\r\n    </el-card>\r\n    <el-card id=\"sslist\">\r\n      <span style=\"color: red; display: block; width: 100%; font-weight: bold\">备注：</span>\r\n      <ol style=\"margin-left: 2em; list-style: disc\">\r\n        <li style=\"list-style: disc\">\r\n          1.单项检查内容存在多起不符合要求行为的，每个单项的总扣分最高不超过该项的总分值。\r\n        </li>\r\n        <li style=\"list-style: disc\">\r\n          2.实行100分评分制,得分为实有项目得分与实有项目总分比值的百分制得分。实有项目总分为实有检查内容各项分值之和，实有项目得分为实有项目总分扣除自查发现问题分值之后的得分。\r\n        </li>\r\n      </ol>\r\n    </el-card>\r\n    <!---->\r\n    <!--锚点索引-->\r\n    <!-- <div class=\"md-menu\" @mouseover=\"mouseoverMdMenu\" @mouseout=\"mouseoutMenu\">\r\n      <div class=\"md-left\"></div>\r\n      <transition name=\"el-fade-in-linear\">\r\n        <div v-show=\"showMdmenu\" class=\"md-right\">\r\n          <div class=\"md-article\">\r\n            <el-timeline :reverse=\"reverse\">\r\n              <el-timeline-item v-for=\"(item, index) in activities\" :key=\"index\">\r\n                <div>\r\n                  <h4>{{ item.dxmc }}</h4>\r\n                  <div v-for=\"(xxItem, xxIndex) in item.children\" :key=\"xxIndex\" class=\"md-article-article\">\r\n                    <span v-if=\"xxItem.xxmc\" style=\"color:#409EFF;\">【{{ xxItem.xxmc }}】</span>\r\n                    <a :href=\"xxItem.href\">\r\n                      <span>{{ xxItem.nr }}</span>\r\n                    </a>\r\n                    <span v-if=\"xxItem.ykf\" style=\"color:#F56C6C;\">扣{{ xxItem.ykf }}分<span\r\n                        class=\"el-icon-caret-right\"></span></span>\r\n                  </div>\r\n                </div>\r\n              </el-timeline-item>\r\n            </el-timeline>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n      <div class=\"md-right-margin-div\"></div>\r\n    </div> -->\r\n    <!---->\r\n    <!---->\r\n    <!---->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\n// import {\r\n//   // 通过任务ID获取任务信息、单位信息和抽查机构信息\r\n//   selectRwxxDwxxCcnsjgxxByRwidCcdnsjgid,\r\n//   // 获取部门详细自查记录字典\r\n//   getBmxxzcjlZD,\r\n//   // 插入或更新内设机构评分记录表\r\n//   insertUpdateNsjgpfjlByCcdnsjgid,\r\n//   // 更新 抽查的内设机构表(ccdnsjg_list)\r\n//   updateCcdnsjgById,\r\n//   // 通过[抽查内设机构流水ID]获取内设机构评分记录历史信息\r\n//   selectNsjgpfjlListByCcdnsjgid\r\n// } from '../../../../db/zczpdb'\r\n\r\nimport { getZczpIdsObj, removeZczpIdsObjField } from '../../../../utils/windowLocation'\r\n\r\nimport { writeOptionsLog } from '../../../../utils/logUtils'\r\nimport {\r\n  selectAllZczpBmjczcx,\r\n  selectAllZczpBmjcnr,\r\n  addZczpBmjcpfjlList,\r\n  updateZczpZzjgxxDjzt,\r\n  selectZczpBmjcpfjl,\r\n  deleteZczpBmjcpfjlByRwidAndJgid\r\n} from '../../../../api/zczp'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogObj: {\r\n        // rwid: '93BC8D4D-2FDE-4BB9-85E5-F6214393B82E',\r\n        // ccdnsjgid: '3AE1A8CD-83E7-4228-951C-1D71604C4631'\r\n      },\r\n      //各大项得分总和array（实时变化）\r\n      dxdfArr: [],\r\n      //实有项目分值array\r\n      syxmDfArr: [],\r\n      //table实际操作的检查评分数据\r\n      showDxList: [],\r\n      //单元格合并规则\r\n      spanArr: [],\r\n      // 时间线排序方向\r\n      reverse: true,\r\n      // 锚点菜单集合\r\n      activities: [],\r\n      // 锚点菜单显隐\r\n      showMdmenu: false,\r\n      dwmc: '',\r\n      nsjg: '',\r\n    }\r\n  },\r\n  computed: {\r\n    syxmzf() {\r\n      let zf = 0;\r\n      this.showDxList.forEach((nr) => {\r\n        if (nr.sfsynr) {\r\n          zf += nr.fz * 1\r\n        }\r\n      })\r\n      return zf\r\n    },\r\n    syxmdf() {\r\n      let resDf = 0\r\n      this.syxmDfArr.forEach((df) => {\r\n        resDf += df * 1\r\n      })\r\n      return resDf\r\n    },\r\n    syxmbfb() {\r\n      if (this.syxmzf == 0) {\r\n        return 0\r\n      }\r\n      // return (this.syxmdf / this.syxmzf).toFixed(3) * 100;\r\n      let num = this.syxmdf / this.syxmzf\r\n      return Math.round(num * 1000) / 10\r\n    }\r\n  },\r\n  components: {\r\n  },\r\n  methods: {\r\n    async pfss() {\r\n      let params = {\r\n        dwid:this.dwid,\r\n        rwid:this.rwid,\r\n        jgid:this.jgid,\r\n      }\r\n      let data1\r\n      let data = await selectAllZczpBmjczcx()\r\n      if (this.djzt == '0') {\r\n        data1 = await selectAllZczpBmjcnr()\r\n      }else{\r\n        data1 = await selectZczpBmjcpfjl(params)\r\n      } \r\n      // let data1 = await selectAllZczpBmjcnr()\r\n      data1.data.forEach((item) => {\r\n        data.data.forEach((item1) => {\r\n          if (item1.zcxid == item.zcxid) {\r\n            item.dxmc = item1.zcxmc;\r\n          }\r\n        })\r\n      })\r\n      this.showDxList = data1.data\r\n      console.log(this.showDxList,'dasdadasdsadasd');\r\n      // console.log(data.data);\r\n      // console.log(data1.data);\r\n    },\r\n    // 临时保存\r\n    async save() {\r\n      console.log('this.showDxList', this.showDxList)\r\n\r\n      this.ccbmRK(this.showDxList, 1)\r\n    },\r\n    // 提交\r\n    submit() {\r\n      this.ccbmRK(this.showDxList, 2)\r\n    },\r\n    //部门抽查入库，状态为该记录对应的 抽查的内设机构表(ccdnsjg_list) 的状态（0待登记 1继续登记 2完成登记）\r\n    async ccbmRK(dxXxList, zt) {\r\n      console.log(dxXxList);\r\n      let list = []\r\n      let xx = {\r\n        rwid:this.rwid,\r\n        jgid:this.jgid,\r\n      }\r\n      let data1 = await deleteZczpBmjcpfjlByRwidAndJgid(xx)\r\n      if (data1.code == 10000) {\r\n        dxXxList.forEach((item) => {\r\n        item.dwid = this.dwid\r\n        item.rwid = this.rwid\r\n        item.jgid = this.jgid\r\n        item.jgmc = this.nsjg\r\n        list.push(item)\r\n      })\r\n      let data = await addZczpBmjcpfjlList(list)\r\n      if (zt == 1) {\r\n        let params = {\r\n          dwid: this.dwid,\r\n          jgid: this.jgid,\r\n          rwid: this.rwid,\r\n          djzt: zt\r\n        }\r\n        updateZczpZzjgxxDjzt(params)\r\n        if (this.zt == 0) {\r\n          this.$router.push({\r\n          path: '/ccdnsjg',\r\n          query: {\r\n            rwid: this.rwid,\r\n            rwmc: this.rwmc,\r\n            jcjd:this.jcjd,\r\n          }\r\n        })\r\n        }else{\r\n          this.$router.push({\r\n            path: '/jczj',\r\n            query: {\r\n            rwid: this.rwid,\r\n            rwmc: this.rwmc,\r\n            jcjd:this.jcjd,\r\n          }\r\n          })\r\n        }\r\n        \r\n        this.$message({\r\n          message: '临时保存成功',\r\n          type: 'success'\r\n        });\r\n      } else if (zt == 2) {\r\n        let params = {\r\n          dwid: this.dwid,\r\n          jgid: this.jgid,\r\n          rwid: this.rwid,\r\n          djzt: zt\r\n        }\r\n        updateZczpZzjgxxDjzt(params)\r\n        if (this.zt == 0) {\r\n          this.$router.push({\r\n          path: '/ccdnsjg',\r\n          query: {\r\n            rwid: this.rwid,\r\n            rwmc: this.rwmc,\r\n            jcjd:this.jcjd,\r\n          }\r\n        })\r\n        }else{\r\n          this.$router.push({\r\n            path: '/jczj',\r\n            query: {\r\n            rwid: this.rwid,\r\n            rwmc: this.rwmc,\r\n            jcjd:this.jcjd,\r\n          }\r\n          })\r\n        }\r\n        this.$message({\r\n          message: '提交成功',\r\n          type: 'success'\r\n        });\r\n      }\r\n      }\r\n      \r\n    },\r\n    // 通过抽查的内设机构流水ID更新数据\r\n    async updateTable() {\r\n      // let bmxxzcjlList = selectNsjgpfjlListByCcdnsjgid(this.dialogObj.ccdnsjgid)\r\n      let bmxxzcjlList\r\n      \r\n      let params = {\r\n        dwid:this.dwid,\r\n        rwid:this.rwid,\r\n        jgid:this.jgid,\r\n      }\r\n      if (this.djzt == '0') {\r\n        bmxxzcjlList = await selectAllZczpBmjcnr()\r\n      }else{\r\n        bmxxzcjlList = await selectZczpBmjcpfjl(params)\r\n      } \r\n      let resBmxxzcjlList = []\r\n      console.log('bmxxzcjlList',bmxxzcjlList);\r\n      bmxxzcjlList.data.forEach((bmxxzcjl) => {\r\n        // console.log('bmxxzcjl', bmxxzcjl)\r\n        bmxxzcjl.gdkffz = bmxxzcjl.fz\r\n        bmxxzcjl.jsqkf = bmxxzcjl.ykf\r\n        // console.log(bmxxzcjl.ykf);\r\n        bmxxzcjl.gdkffz = bmxxzcjl.fz\r\n        // if (bmxxzcjl.checked == 1) {\r\n        //   bmxxzcjl.checked = true\r\n        // } else {\r\n        //   bmxxzcjl.checked = false\r\n        // }\r\n        //   console.log(bmxxzcjl.sfsynr);\r\n        // if (bmxxzcjl.sfsynr == 1) {\r\n        //   bmxxzcjl.sfsynr = true\r\n        // }\r\n      })\r\n      resBmxxzcjlList = bmxxzcjlList.data\r\n      console.log(resBmxxzcjlList);\r\n      //计算单元格合并规则\r\n      this.spanArr = this.getSpanArr(resBmxxzcjlList)\r\n      this.showDxList = resBmxxzcjlList\r\n    },\r\n    returnSy() {\r\n      this.$router.go(-1)\r\n    },\r\n    // 获取抽查单位信息\r\n    getRwxxDwxxCcnsjgxxByRwidCcdnsjgid() {\r\n      let rwxxDwxxCcnsjgxx = selectRwxxDwxxCcnsjgxxByRwidCcdnsjgid(this.dialogObj)\r\n      console.log('rwxxDwxxCcnsjgxx', rwxxDwxxCcnsjgxx)\r\n      Object.assign(this.dialogObj, rwxxDwxxCcnsjgxx)\r\n      this.dialogObj = JSON.parse(JSON.stringify(this.dialogObj))\r\n    },\r\n    //----------------------------用来返回this.spanArr数组的，定义每一行的 rowspan-----------\r\n    getSpanArr(list) {\r\n      console.log(list)\r\n      let spanArr = []\r\n      for (var i = 0; i < list.length; i++) {\r\n        if (i === 0) {\r\n          spanArr.push(1)\r\n          this.pos = 0\r\n        } else {\r\n          // 判断当前元素与上一个元素是否相同\r\n          if (list[i].zcxid == list[i - 1].zcxid) {\r\n            spanArr[this.pos] += 1\r\n            spanArr.push(0)\r\n          } else {\r\n            spanArr.push(1)\r\n            this.pos = i\r\n          }\r\n        }\r\n      }\r\n      return spanArr\r\n    },\r\n    objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      if (columnIndex === 0) {\r\n        //\r\n        const _row = this.spanArr[row.scid - 1]\r\n        return {\r\n          rowspan: _row,\r\n          colspan: 1\r\n        }\r\n      }\r\n    },\r\n    // 扣分计数器改变\r\n    handleKfjsq(nr) {\r\n      nr.checked = true\r\n    },\r\n    /**\r\n     * 实有内容textarea值改变\r\n     */\r\n    handleSynrTextarea(nr) {\r\n      if (nr.synr !== undefined && nr.synr != \"\") {\r\n        nr.sfsynr = true\r\n        // xx.check=true\r\n      } else {\r\n        nr.sfsynr = false\r\n        // xx.check=true\r\n      }\r\n    },\r\n    /**\r\n     * 评分说明改变\r\n     */\r\n    handleDfsmTextarea(nr) {\r\n      if (nr.dfsm !== undefined && nr.dfsm != \"\") {\r\n        nr.checked = true\r\n      } else {\r\n        nr.checked = false\r\n      }\r\n    },\r\n    /**\r\n     * 获取部门详细自查记录字典\r\n     */\r\n    getZD() {\r\n      //\r\n      const zdList = getBmxxzcjlZD()\r\n      console.log(zdList)\r\n      zdList.forEach((nr) => {\r\n        nr.ykf = 0\r\n        nr.checked = false\r\n        nr.jsqkf = 0\r\n        nr.gdkffz = nr.fz\r\n        //是否实有内容在字典里默认选中\r\n        nr.sfsynr = true\r\n      })\r\n      this.spanArr = this.getSpanArr(zdList)\r\n      //\r\n      return zdList\r\n    },\r\n    // 锚点菜单鼠标移入事件\r\n    mouseoverMdMenu() {\r\n      this.showMdmenu = true\r\n    },\r\n    // 锚点菜单鼠标移出事件\r\n    mouseoutMenu() {\r\n      this.showMdmenu = false\r\n    }\r\n  },\r\n  watch: {\r\n    showDxList: {\r\n      handler(newVal, oldVal) {\r\n        console.log(\"bmcc showDxList changed...\", newVal)\r\n        const _this = this\r\n        // 清空锚点，防重复\r\n        this.activities = []\r\n        // 大项ID数组，用以对数据进行分组\r\n        let dxMdIdArr = []\r\n        // 实有项目得分数组\r\n        _this.syxmDfArr = []\r\n        //\r\n        let dxIdArr = []\r\n        //\r\n        newVal.forEach((nr, nrIndex) => {\r\n          // 锚点\r\n          nr.mdIndex = 'md-' + nrIndex\r\n          if (dxIdArr.indexOf(nr.zcxid) == -1) {\r\n            dxIdArr.push(nr.zcxid)\r\n          }\r\n          //\r\n          if (nr.checked) {\r\n            if (nr.kffs == 3) {\r\n              nr.ykf = nr.jsqkf\r\n            }\r\n            if (nr.kffs == 2) {\r\n              nr.ykf = nr.gdkffz\r\n            }\r\n            console.log(nr.nrid, nr.fz, nr.ykf, nr.fz - nr.ykf)\r\n            nr.df = nr.fz - nr.ykf\r\n            // 锚点\r\n            if (dxMdIdArr.indexOf(nr.zcxid) == -1) {\r\n              dxMdIdArr.push(nr.zcxid)\r\n            }\r\n            if (dxMdIdArr.indexOf(nr.zcxid) != -1) {\r\n              if (!this.activities[dxMdIdArr.indexOf(nr.zcxid)]) {\r\n                this.activities.push({\r\n                  dxmc: nr.dxmc,\r\n                  children: []\r\n                })\r\n              }\r\n              this.activities[dxMdIdArr.indexOf(nr.zcxid)].children.push({\r\n                href: '#' + nr.mdIndex,\r\n                nr: nr.nr,\r\n                ykf: nr.ykf\r\n              })\r\n            }\r\n          } else {\r\n            nr.ykf = 0\r\n            nr.jsqkf = 0\r\n            nr.df = nr.fz\r\n          }\r\n          //\r\n          if (nr.sfsynr) {\r\n            _this.syxmDfArr.push(nr.df)\r\n          }\r\n        })\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    console.log(this.$route.query);\r\n    this.dwmc = this.$route.query.dwmc\r\n    this.nsjg = this.$route.query.bmmc\r\n    this.dwid = this.$route.query.dwid\r\n    this.rwid = this.$route.query.rwid\r\n    this.jgid = this.$route.query.jgid\r\n    this.rwmc = this.$route.query.rwmc\r\n    this.djzt = this.$route.query.djzt\r\n    this.zt = this.$route.query.zt\r\n    this.jcjd = this.$route.query.jcjd\r\n    this.pfss()\r\n    // let params = this.$route.query\r\n    // let params = getZczpIdsObj()\r\n    // if (params && Object.keys(params).length > 0) {\r\n    //   console.log('抽查的内设机构登记', params)\r\n    //   this.dialogObj.rwid = params.rwid\r\n    //   this.dialogObj.ccdnsjgid = params.ccdnsjgid\r\n    // } else {\r\n    //   this.$message.warning('未检测到抽查的内设机构ID，请关闭页面重新进入')\r\n    //   return\r\n    // }\r\n    // 获取抽查单位信息\r\n    // 获取任务信息、单位信息和抽查机构信息\r\n    // this.getRwxxDwxxCcnsjgxxByRwidCcdnsjgid()\r\n    // // 获取字典信息\r\n    // this.showDxList = this.getZD()\r\n    // 调用更新数据方法，如果没有历史信息，则返回的是字典信息\r\n    this.updateTable()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/zczp/childPage/ccdnsjgDj.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"}},[_c('div',{staticClass:\"mhcx\"},[_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"left\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"left\"}},[_c('div',[_vm._v(\"单位：\"+_vm._s(_vm.dwmc))])]),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"left\"}},[_c('div',[_vm._v(\"抽查内设机构：\"+_vm._s(_vm.nsjg))])])],1),_vm._v(\" \"),_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"right\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\",\"icon\":\"el-icon-document-add\"},on:{\"click\":_vm.submit}},[_vm._v(\"\\n          提交\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"medium\",\"icon\":\"el-icon-document\"},on:{\"click\":_vm.save}},[_vm._v(\"\\n          临时保存\\n        \")])],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1),_vm._v(\" \"),_c('div',{staticStyle:{\"overflow-y\":\"scroll\",\"height\":\"calc(100% - 58px - 64px - 108px - 16px - 20px)\",\"margin-bottom\":\"5px\"}},[_c('el-card',{staticStyle:{\"margin-bottom\":\"1em\"}},[_c('el-table',{attrs:{\"data\":_vm.showDxList,\"span-method\":_vm.objectSpanMethod,\"header-cell-style\":{ 'text-align': 'center' },\"border\":\"\"}},[_c('el-table-column',{attrs:{\"label\":\"自查类\",\"width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('span',{attrs:{\"id\":_vm.showDxList[scope.$index].mdIndex}}),_vm._v(_vm._s(_vm.showDxList[scope.$index].dxmc)+\"\\n            \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"检查内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('span',{attrs:{\"id\":_vm.showDxList[scope.$index].mdIndex}}),_vm._v(_vm._s(_vm.showDxList[scope.$index].scnr)+\"\\n            \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"扣分\",\"width\":\"150\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[(_vm.showDxList[scope.$index].kffs == 3)?_c('div',[_c('el-checkbox-group',{model:{value:(_vm.showDxList[scope.$index].checked),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"checked\", $$v)},expression:\"showDxList[scope.$index].checked\"}},[_c('el-checkbox',{staticStyle:{\"margin-right\":\"0.5em\"},attrs:{\"label\":\"\",\"name\":\"checkbox\"}}),_vm._v(\" \"),_c('el-input-number',{staticStyle:{\"width\":\"100px\"},attrs:{\"min\":_vm.showDxList[scope.$index].zdkffz,\"max\":_vm.showDxList[scope.$index].zgkffz,\"step\":_vm.showDxList[scope.$index].kfzf,\"size\":\"mini\"},on:{\"change\":function($event){return _vm.handleKfjsq(_vm.showDxList[scope.$index])}},model:{value:(_vm.showDxList[scope.$index].jsqkf),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"jsqkf\", $$v)},expression:\"showDxList[scope.$index].jsqkf\"}})],1)],1):_vm._e(),_vm._v(\" \"),(_vm.showDxList[scope.$index].kffs == 2)?_c('div',[_c('el-checkbox-group',{model:{value:(_vm.showDxList[scope.$index].checked),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"checked\", $$v)},expression:\"showDxList[scope.$index].checked\"}},[_c('el-checkbox',{attrs:{\"label\":\"\",\"name\":\"checkbox\"}},[_vm._v(_vm._s(_vm.showDxList[scope.$index].gdkffz))])],1)],1):_vm._e()])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"fz\",\"label\":\"分值\",\"width\":\"50\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"实有项目\",\"width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-checkbox-group',{model:{value:(_vm.showDxList[scope.$index].sfsynr),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"sfsynr\", $$v)},expression:\"showDxList[scope.$index].sfsynr\"}},[_c('el-checkbox',{attrs:{\"label\":\"是\",\"name\":\"checkboxSynr\"}})],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"\",\"label\":\"实有内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3},on:{\"input\":function($event){return _vm.handleSynrTextarea(_vm.showDxList[scope.$index])}},model:{value:(_vm.showDxList[scope.$index].synr),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"synr\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"showDxList[scope.$index].synr\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"得分\",\"width\":\"50\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_vm._v(_vm._s(_vm.showDxList[scope.$index].df))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"kfbz\",\"label\":\"扣分标准\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"评分说明\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3},on:{\"input\":function($event){return _vm.handleDfsmTextarea(_vm.showDxList[scope.$index])}},model:{value:(_vm.showDxList[scope.$index].dfsm),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"dfsm\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"showDxList[scope.$index].dfsm\"}})]}}])})],1)],1)],1),_vm._v(\" \"),_c('el-card',{staticStyle:{\"margin-bottom\":\"5px\"},attrs:{\"id\":\"sslist\"}},[_c('span',{staticClass:\"sslist_class\"},[_vm._v(\"实有项目总分：\"),_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.syxmzf))])]),_vm._v(\" \"),_c('span',{staticClass:\"sslist_class\"},[_vm._v(\"实有项目得分：\"),_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.syxmdf))])]),_vm._v(\" \"),_c('span',{staticClass:\"sslist_class\"},[_vm._v(\"实有项目得分占实有项目总分百分比：\"),_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.syxmbfb)+\"%\")])])]),_vm._v(\" \"),_c('el-card',{attrs:{\"id\":\"sslist\"}},[_c('span',{staticStyle:{\"color\":\"red\",\"display\":\"block\",\"width\":\"100%\",\"font-weight\":\"bold\"}},[_vm._v(\"备注：\")]),_vm._v(\" \"),_c('ol',{staticStyle:{\"margin-left\":\"2em\",\"list-style\":\"disc\"}},[_c('li',{staticStyle:{\"list-style\":\"disc\"}},[_vm._v(\"\\n        1.单项检查内容存在多起不符合要求行为的，每个单项的总扣分最高不超过该项的总分值。\\n      \")]),_vm._v(\" \"),_c('li',{staticStyle:{\"list-style\":\"disc\"}},[_vm._v(\"\\n        2.实行100分评分制,得分为实有项目得分与实有项目总分比值的百分制得分。实有项目总分为实有检查内容各项分值之和，实有项目得分为实有项目总分扣除自查发现问题分值之后的得分。\\n      \")])])])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-7198e963\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/zczp/childPage/ccdnsjgDj.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-7198e963\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ccdnsjgDj.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdnsjgDj.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdnsjgDj.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-7198e963\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ccdnsjgDj.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-7198e963\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/zczp/childPage/ccdnsjgDj.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}