// import Vue from 'vue'
// import Router from 'vue-router'
// import Login from '../pages/login/Login.vue'

// Vue.use(Router)

// export default new Router({
//   routes: [
//     {
//       path: '/',
//       name: 'Login',
//       component: Login
//     }
//   ]
// })
import Vue from 'vue'
import Router from 'vue-router'
import store from '../store/index';

// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch((err) => err)
}

Vue.use(Router)

// export default new Router({
//   routes: [
//     // {
//     //   path: '/',
//     //   name: 'landing-page',
//     //   component: require('@/components/LandingPage').default
//     // },
//     {
//       path: '/',
//       name: 'xcsc',
//       component: require('../xcsc/Xcsc').default
//     },
//     {
//       path: '*',
//       redirect: '/'
//     }
//   ]
// })

const routers = [{
        path: '/',
        name: 'login',
        component: require('../login/Login.vue').default,
    },
    {
        path: '*',
        redirect: '/',
    },
    // {
    //   path: '/dwzc',
    //   name: 'dwzc',
    //   component: require('../login/components/dwzc.vue').default,
    // },
]

const routerFiles = require.context(
        '../view',
        true,
        /(.*?)\/router\/(.*?)\.js$/
    )
    // console.log('routerFiles', routerFiles)
routerFiles.keys().forEach((item) => {
    const value = routerFiles(item)
        // console.log(item, value)
    const valueDefaultType = Object.prototype.toString.call(value.default)
    if (valueDefaultType == '[object Array]') {
        value.default.forEach((vdItem) => {
            routers.push(vdItem)
        })
    } else if (valueDefaultType == '[object Object]') {
        routers.push(value.default)
    } else {
        console.log('路由文件中存在未知配置类型', item)
    }
})

// console.log('router中的路由')
// routers.forEach((item) => {
//   console.log(item)
// })
// console.log('router中的路由end')

const router = new Router({
    routes: [...routers],
})

let currentPath, currentFullPath

router.beforeEach((to, from, next) => {
    // console.log('from', from)
    console.log('to', to)
        // console.log('菜单逻辑 路由变更', to.path)
        //
    if (to.meta && to.meta.menuList) {
        console.log('菜单逻辑 to meta中检测到菜单配置', to.meta)
        console.log(router.app.$options);
        // 向vue提交菜单配置集合
        router.app.$options.store.default.commit('changeElAsideMenuList', to.meta.menuList)
            // console.log('router.app.$options.store', router.app.$options.store.state.Counter.elAsideMenuList)
            // router.app.$options.store.dispatch('changeElAsideMenuList', to.meta.menuList)
    }
    next()
})

router.beforeEach((to, from, next) => {
    // const token = localStorage.getItem('token')
    console.log(to.meta.requireAuth)
    console.log(store.state.Counter.token)
    if (to.meta.requireAuth && store.state.Counter.token == '') {
        next('/login')
    } else {
        next()
    }
})

export default router