<template>
	<div style="height:calc(100% - 32px - 10px);">
		<div class="zdwb">

			<div class="zzjg-nr">
				<div class="nr-sxt">
					<div class="nr-dwxx" @click="todwmc">
						<img src="../../assets/icons/icon-04.png" alt="" />
						&nbsp;&nbsp;{{ dwmc }}
					</div>
					<div class="organization_configuration">
						<el-tree :data="data" ref="tree" :default-expand-all="zksq" node-key="id"
							:default-checked-keys="[1]" highlight-current @check-change="handleCheckChange"
							@node-click="clickNode"></el-tree>
					</div>
				</div>
				<div class="nr-tabs">

					<el-tabs v-model="activeName" @tab-click="handleClick" style="height:100%;">
						<el-tab-pane label="下属组织机构" name="third" style="height:100%;">
							<div>
								<el-button type="success" size="medium" @click="xgdialogVisible = true">新增下属机构
								</el-button>
								&nbsp;
								<el-button type="danger" size="medium" @click="yuchu">移 除</el-button>
							</div>
							<div style="height: 100%; margin-top: 10px">
								<el-table :data="xsjgList" border @selection-change="selectRow" :header-cell-style="{
									background: '#EEF7FF',
									color: '#4D91F8',
								}" style="width: 100%; border: 1px solid #ebeef5" height="calc(100% - 55px - 36px - 10px - 34px - 9px)" stripe>
									<el-table-column type="selection" width="55" align="center">
									</el-table-column>
									<el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
									<el-table-column prop="label" label="名称"></el-table-column>
									<el-table-column prop="" label="操作">
										<template slot-scope="scope">
											<el-button size="medium" type="text" :disabled="scope.$index == 0"
												@click="moveUpward(scope.row, scope.$index)">↑
											</el-button>
											<el-button size="medium" type="text"
												:disabled="(scope.$index + 1) == xsjgList.length"
												@click="moveDown(scope.row, scope.$index)">↓
											</el-button>
										</template>
									</el-table-column>
								</el-table>

								<!-- -------------------------分页区域---------------------------- -->
								<div style="border: 1px solid #ebeef5">
									<el-pagination background @current-change="xshandleCurrentChange"
										@size-change="xshandleSizeChange" :pager-count="5" :current-page="xspage"
										:page-sizes="[5, 10, 20, 30]" :page-size="xspageSize"
										layout="total, prev, pager, sizes,next, jumper" :total="xstotal">
									</el-pagination>
								</div>
							</div>
						</el-tab-pane>
						<el-tab-pane label="组织机构详情" name="first" style="height:100%;">
							<div>
								<el-button v-show="!disabledEdit" type="primary" size="medium" @click="zzjgbj">编 辑
								</el-button>
								<el-button v-show="disabledEdit" type="primary" size="medium" @click="zzjgbc">保 存
								</el-button>
							</div>
							<div style="margin-top: 20px">
								<el-form ref="form1" :model="zzjgxqform" size="mini" label-width="200px"
									:disabled="!disabledEdit">
									<el-form-item label="名称">
										<el-input v-model="zzjgxqform.label"></el-input>
									</el-form-item>
									<el-form-item label="是否为涉密部门" prop="sfwbmxzgldw">
										<!-- <el-radio-group v-model="zzjgxqform.Sfwbmxzgldw">
											<el-radio label="是" value="1"></el-radio>
											<el-radio label="否" value="0"></el-radio>
										</el-radio-group> -->
										<el-radio-group v-model="zzjgxqform.sfwbmxzgldw">
											<el-radio v-for="item in sfwsmbm" :v-model="zzjgxqform.sfwbmxzgldw"
												:label="item.id" :value="item.id" :key="item.id">{{ item.mc }}</el-radio>
										</el-radio-group>
									</el-form-item>
								</el-form>
							</div>
						</el-tab-pane>
						<el-tab-pane label="组织机构用户" name="second" style="height:100%;">
							<div>
								<!-- <el-button type="success" size="medium" @click="dialogVisible = true">新增机构用户</el-button>
								&nbsp;
								<el-button type="danger" size="medium" @click="shanchu">移 除</el-button> -->
							</div>
							<div style="height: 100%; margin-top: 10px">
								<el-table :data="yhList" border @selection-change="selectRow" :header-cell-style="{
									background: '#EEF7FF',
									color: '#4D91F8',
								}" style="width: 100%; border: 1px solid #ebeef5" height="calc(100% - 55px  - 34px - 10px - 9px)" stripe>
									<el-table-column type="selection" width="55" align="center">
									</el-table-column>
									<el-table-column prop="xm" label="姓名"></el-table-column>
									<el-table-column prop="xb" label="性别" :formatter="xbhx"></el-table-column>
									<el-table-column prop="zw" label="职务"></el-table-column>
									<el-table-column prop="yrxs" label="任职方式" :formatter="rzfs"></el-table-column>
								</el-table>

								<!-- -------------------------分页区域---------------------------- -->
								<div style="border: 1px solid #ebeef5">
									<el-pagination background @current-change="handleCurrentChange"
										@size-change="handleSizeChange" :pager-count="5" :current-page="page"
										:page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
										layout="total, prev, pager, sizes,next, jumper" :total="total">
									</el-pagination>
								</div>
							</div>
						</el-tab-pane>
					</el-tabs>
				</div>
			</div>
		</div>

		<el-dialog title="新增下属组织机构" :visible.sync="xgdialogVisible" width="25%" @close="close('form')">
			<el-form ref="form" :model="xglist" :rules="rules" label-width="130px" size="mini">
				<el-form-item label="部门名称" prop="label" class="one-line">
					<el-input placeholder="部门名称" v-model="xglist.label" clearable @blur="onIndexBlur"
						style="width:100%;"></el-input>
				</el-form-item>
				<el-form-item label="是否涉密部门" label-width="130px" prop="Sfwbmxzgldw">
					<!-- <el-radio-group v-model="xglist.Sfwbmxzgldw">
						<el-radio label="是" value= 1 ></el-radio>
						<el-radio label="否" value= 0 ></el-radio>
					</el-radio-group> -->
					<el-radio-group v-model="xglist.Sfwbmxzgldw">
						<el-radio v-for="item in sfwsmbm" :v-model="xglist.Sfwbmxzgldw" :label="item.id" :value="item.id"
							:key="item.id">{{ item.mc }}</el-radio>
					</el-radio-group>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="xszzjgxz('form')">保 存</el-button>
				<el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import {
	getLoginInfo,
	getZzjgList,
	saveZzjg,
	getZzjgById,
	getChildrenZzjgBybmm,//右侧子组织
	getZzjgPage,//不用
	removeZzjg,
	getYhxxPageByBmm,
	updateZzjg,
	changePxh,
	getSfsmbm,
} from '../../../api/index'
import {
	getAllYsxs,
} from '../../../api/xlxz'
export default {
	data() {
		return {
			// 控制编辑按钮的转换
			pdm: 0,
			zksq: true,
			disabledEdit: false,
			//
			tjlist: {
				xm: '',
				xb: '',
				zw: '',
				rzfs: '',
			},
			xglist: {
				// zzjgh: '',
				label: '',
				// pxh: '',
				Sfwbmxzgldw: '',
			},
			xb: [{
				xb: '男',
				id: 1
			},
			{
				xb: '女',
				id: 2
			}],
			value: '',
			dialogVisible: false,
			xgdialogVisible: false,
			yhList: [],
			xsjgList: [],
			page: 1,
			pageSize: 10,
			total: 0,
			xspage: 1,
			xspageSize: 10,
			xstotal: 0,
			zzjgxqform: {},
			zzjgxq: {},
			data: [],
			oldArr: [],
			newArr: [],
			count: 1,
			defaultProps: {
				children: "children",
				label: "label",
			},
			activeName: "third",
			selectlistRow: [], //列表的值
			rules: {
				xm: [{
					required: true,
					message: '请输入姓名',
					trigger: 'blur'
				},],
				xb: [{
					required: true,
					message: '请选择性别',
					trigger: 'blur'
				},],
				zw: [{
					required: true,
					message: '请输入职务',
					trigger: 'blur'
				},],
				rzfs: [{
					required: true,
					message: '请选择组织机构号',
					trigger: 'blur'
				},],
				zzjgh: [{
					required: true,
					message: '请输入培训主题',
					trigger: 'blur'
				},],
				label: [{
					required: true,
					message: '请输入组织机构名称',
					trigger: 'blur'
				},],
				bmjb: [{
					required: true,
					message: '请输入部门级别',
					trigger: 'blur'
				},],
				Sfwbmxzgldw: [{
					required: true,
					message: '请选择保密行政管理单位',
					trigger: 'blur'
				},],
			},
			dwmc: '',
			olddata: {},
			bmm: '',
			tsxx: '',
			yrxsxz: [],
			sfwsmbm: [],
		};
	},

	mounted() {
		// this.olddata.bmm = getZzjgList().records[0].bmm
		// console.log("this.olddata.bmm", this.olddata.bmm);\
		this.dwxx()//获取单位信息
		this.yrxs()
		this.sfsmbm()
		//this.hqbmm()//获取部门码

		// // this.$nextTick(() => {
		// // 	this.$refs.tree.setCurrentKey()
		// // })
	},

	methods: {
		//获取是否涉密部门
		async sfsmbm() {
			let data = await getSfsmbm()
			console.log("是否涉密部门", data);
			this.sfwsmbm = data
		},
		//获取用人形式
		async yrxs() {
			let data = await getAllYsxs()
			console.log("用人形式：", data);
			this.yrxsxz = data
		},
		//编辑
		zzjgbj() {
			let bjcxbmm = this.zzjgxq.bmm
			console.log("bjcxbmm", bjcxbmm);
			let sxtbmm = this.olddata.bmm
			console.log("sxtbmm", sxtbmm);

			// if(){

			// }
			if (bjcxbmm == sxtbmm) {
				this.disabledEdit = false
				this.$message.warning("单位名称不能修改！")
			} else {
				this.disabledEdit = true
			}
		},
		zzjgbc() {
			updateZzjg(this.zzjgxqform)
			this.disabledEdit = false
		},
		//废弃代码
		//获取部门码
		// async hqbmm() {
		// 	let hqbmm = []
		// 	hqbmm = await getZzjgList()
		// 	// this.olddata = hqbmm
		// 	// console.log("this.olddata", this.olddata);
		// 	hqbmm.forEach((item) => {
		// 		// item.children = []
		// 		if (item.fbmm == '') {
		// 			console.log("有被冒犯到", item);
		// 			this.olddata = item
		// 			this.olddata.bmm = item.bmm
		// 			// this.zzjgxqform = item
		// 		}
		// 	});
		// 	console.log("this.olddata.bmm", this.olddata.bmm);
		// 	console.log("this.olddata", this.olddata);
		// 	console.log("this.olddata.bmm", this.olddata.bmm);
		// },
		//废弃代码
		// 下属组织机构初始化成员列表
		// async xszzjg() {
		// 	let params = {
		// 		page: this.xspage,
		// 		pageSize: this.xspageSize,
		// 		bmm: this.olddata.bmm
		// 	}
		// 	let data = await getChildrenZzjgBybmm(params)
		// 	console.log(data);
		// 	// let data = await getChildrenZzjgBybmm(params)
		// 	console.log("组织机构", data);
		// 	// let xszzjgList = []
		// 	// data.forEach((item) => {
		// 	// 	if (this.bmm == item.fbmm) {
		// 	// 		xszzjgList.push(item)
		// 	// 	}
		// 	// })
		// 	this.xsjgList = data.records
		// 	this.xstotal = data.total
		// },



		//查询子部门
		async cxzbmzzjg() {
			let params = {
				page: this.xspage,
				pageSize: this.xspageSize,
				bmm: this.olddata.bmm
			}
			console.log(this.olddata.bmm)
			let data = await getChildrenZzjgBybmm(params)
			console.log("zzzzzzzzzzzzzzzzzzzzzzz组织机构", data);
			this.xsjgList = data.records
			this.xstotal = data.total
		},

		filters(arr) {
			this.newArr = arr.filter((item, itemIndex) => {
				this.oldArr.forEach((oldItem, oldIndex) => {
					// console.log("oldItem",oldItem);
					// return
					if (oldItem.fbmm == item.bmm) { //有子节点，oldItem是item的子项
						item.children.push(oldItem)
					}
					if (oldIndex == this.oldArr.length - 1) { //内层循环最后一项处理完毕
						if (item.children && item.children.length) {//当前层级有子项，子项不为空
							this.filters(item.children); //调用递归过滤函数
						}
					}
				});
				return true //返回过滤后的新数组赋值给this.newArr
			})
			// console.log(this.newArr,"this.newArrthis.newArr");
			return this.newArr
		},
		async fun() {
			this.oldArr = await getZzjgList()
			console.log(this.oldArr)
			let arr = []
			this.oldArr.forEach((item1) => {
				item1.children = []
				if (item1.fbmm == "") {
					arr.push(item1)
				}
			});
			this.data = this.filters(arr)[0].children; //调用递归过滤函数
			console.log(this.data, "组装后数据");
		},
		todwmc() {
			this.zzjgxqform = this.zzjgxq
			this.olddata = this.zzjgxqform
			console.log("this.zzjgxqform.label", this.zzjgxqform);
			this.disabledEdit = false
			// this.fun()
			// this.cxzbmzzjg()
			this.cxzbmzzjg()
			this.zzjgyh()
		},
		async dwxx() {
			let list = await getLoginInfo()
			if (list.bmm != null) {
				console.log("单位信息dwxx：", list);
				this.dwmc = list.label
				this.bmm = list.bmm
				this.olddata.bmm = list.bmm
				this.zzjgxqform = list
				this.zzjgxq = list
				console.log("单位信息dwxx：", this.dwmc);
				console.log("单位信息dwxx：", this.bmm);
				console.log(this.olddata)
				this.cxzbmzzjg()//查询子部门
				// this.xszzjg()//查询下属组织机构列表
				this.zzjgyh()//查询组织机构用户表
				this.fun()//组织机构树
			}

		},


		//删除
		shanchu(id) {

		},
		//用户分页
		//列表分页--跳转页数
		handleCurrentChange(val) {
			this.page = val
			this.zzjgyh()
		},
		//列表分页--更改每页显示个数
		handleSizeChange(val) {
			this.page = val
			this.pageSize = val
			this.zzjgyh()
		},
		clickNode(data) {
			console.log(data)
			//点击节点触发,不同层级的level事件不同
			//可对应界面变化，比如通过v-if控制模块显隐
			console.log("data", data);
			this.olddata = data
			this.zzjgxqform = data
			console.log("this.olddata.bmm", this.olddata.bmm);
			console.log("this.olddata.label", this.olddata.label)
			this.zzjgyh()
			// this.fun()
			this.cxzbmzzjg()

			// this.xszzjg()

		},
		//组织机构用户初始化成员列表
		async zzjgyh() {
			// this.olddata.bmm = this.bmm
			let params = {
				page: this.page,
				pageSize: this.pageSize,
				bmm: this.olddata.bmm,
			}
			let resList = await getYhxxPageByBmm(params)
			console.log(".............................", params);
			console.log("涉密人员", resList);
			// resList.records.forEach((item) => {
			// 	let xdbm = item.bmmc.split("/");
			// 	console.log("xdbm", xdbm);
			// 	item.bmmc = xdbm[xdbm.length - 1];
			// 	console.log("item.bm", item.bmmc);
			// 	if (this.olddata.label == undefined) {
			// 		return item;
			// 	}
			// 	if (this.olddata.label == item.bmmc) {
			// 		console.log("全都没有", item);
			// 		return item;
			// 	}
			// })
			this.yhList = resList.records
			this.total = resList.total
		},

		async moveUpward(row, index) {
			console.log('row', row)
			// 上一条
			let upData1 = this.xsjgList[index - 1]
			console.log("上一条数据：", upData1);
			console.log("当前条数据：", row);

			const data = await changePxh([row.bmm, upData1.bmm])
			if (data.code = 10000) {
				this.cxzbmzzjg()
				this.fun()
			}

		},
		async moveDown(row, index) {
			if ((index + 1) == this.xsjgList.length) {
				this.$message({
					message: '已经是最后一条，下移失败',
					type: 'warning'
				});
			} else {
				// 上一条
				let upData1 = this.xsjgList[index + 1]
				console.log('下一个--------------', upData1.label)
				console.log(upData1)
				console.log('当前--------------', row.label)
				const data = await changePxh([row.bmm, upData1.bmm])
				if (data.code = 10000) {
					this.cxzbmzzjg()
					this.fun()
				}
			}
		},

		//下属组织机构新增
		xszzjgxz(form) {
			this.$refs[form].validate((valid) => {
				if (valid) {
					let params = {
						label: this.xglist.label,
						bmm: '1',
						fbmm: this.olddata.bmm,
						bmjb: this.xglist.bmjb,
						Sfwbmxzgldw: this.xglist.Sfwbmxzgldw,
						// haschild: this.xglist.haschild,
						Sfejbm: '2',
						Pxh: '1',
						cjr: '谭祯杰',
					}
					// if (this.pdm == 0) {

					const then = this
					saveZzjg(params).then(function () {
						then.cxzbmzzjg()
						then.fun()

					})
					this.xgdialogVisible = false
					this.$message({
						message: '添加成功',
						type: 'success'
					});
					this.resetForm1()

					// this.xszzjg()
					// this.cxzbmzzjg()
					// }
					//  else {
					// 	this.$message.error('下属组织机构已存在')
					// }
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		},

		handleCheckChange() {
			let res = this.$refs.tree.getCheckedNodes()
			console.log("res", res);
		},

		//删除
		yuchu(id) {
			if (this.selectlistRow != '') {
				this.$confirm('是否继续移除?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					let valArr = this.selectlistRow
					// console.log("....", val);
					valArr.forEach(function (item) {
						console.log(item);
						let params = {
							bmm: item.bmm
						}
						removeZzjg(params)
						console.log("移除：", item);
						console.log("移除：", item);
					})
					let params = valArr
					this.$message({
						message: '移除成功',
						type: 'success'
					});
					this.cxzbmzzjg()
					// this.xszzjg()
					this.fun()
				}).catch(() => {
					this.$message('已取消移除')
				})
			} else {
				this.$message({
					message: '请选择下列列表',
					type: 'warning'
				});
			}
		},
		//下属组织机构
		//列表分页--跳转页数
		xshandleCurrentChange(val) {
			console.log(val)
			// debugger
			this.xspage = val
			// this.xszzjg()
			this.cxzbmzzjg()

			console.log(this.olddata.bmm);
		},
		//列表分页--更改每页显示个数
		xshandleSizeChange(val) {
			this.xspage = 1
			this.xspageSize = val
			// this.xszzjg()
			this.cxzbmzzjg()

		},
		selectRow(val) {
			console.log(val);
			this.selectlistRow = val;
		},
		handleClose(done) {
			this.resetForm()
			this.dialogVisible = false
		},
		//添加重置
		resetForm() {
			this.tjlist.xm = ''
			this.tjlist.xb = ''
			this.tjlist.zw = ''
			this.tjlist.rzfs = ''
		},
		resetForm1() {
			this.xglist.zzjgh = ''
			this.xglist.label = ''
			this.xglist.pxh = ''
			this.xglist.xfbmgzjg = ''
		},
		handleNodeClick(data) {
			console.log(data);
		},
		handleClick(tab, event) {
			console.log(tab, event);
		},
		// 弹框关闭触发
		close(formName) {
			// 清空表单校验，避免再次进来会出现上次校验的记录
			this.$refs[formName].resetFields();
		},
		close1(form) {
			// 清空表单校验，避免再次进来会出现上次校验的记录
			this.$refs[form].resetFields();
		},
		onIndexBlur() {

		},
		//列表数据回显
		xbhx(row) {
			let listqx
			this.xb.forEach(item => {
				if (row.xb == item.id) {
					listqx = item.xb
				}
			})
			return listqx
		},
		rzfs(row) {
			let listqx
			this.yrxsxz.forEach(item => {
				if (row.yrxs == item.csz) {
					listqx = item.csm
				}
			})
			return listqx
		},
	},
};
</script>

<style scoped>
.zdwb {
	width: 100%;
	height: 100%;
	/* box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.1); */
	/* background: url(../../assets/background/table_bg.png) no-repeat center; */
	/* background-size: 100% 100%; */
}

/* 
.mk_bt {
	width: 100%;
	height: 7%;
	border-bottom: 2px solid rgba(216, 216, 216, 1);
} */

.mk_btl {
	display: flex;
	align-items: center;
	margin-left: 20px;
	font-size: 0.9vw;
	height: 100%;
	font-weight: 400;
}

.zzjg-nr {
	width: 100%;
	height: 100%;
	display: flex;
}

.nr-sxt {
	width: 25%;
	height: 100%;
	/* background-color: #523fff; */
	padding: 1% 1.5% 0% 1.5%;
	border-right: 2px solid rgba(216, 216, 216, 1);
}

.organization_configuration {
	cursor: pointer;
	height: 95%;
	overflow-y: scroll;
}

.nr-dwxx {
	width: 100%;
	height: 5%;
	display: flex;
	align-items: center;
	border-bottom: 2px solid rgba(216, 216, 216, 1);
	font-size: 14px;
	color: #657089;
	letter-spacing: 0;
	line-height: 19.6px;
	font-weight: 400;
	cursor: pointer;
}

.nr-tabs {
	width: 75%;
	height: 100%;
	padding: 0.75% 1.5% 0% 1.5%;
}

/deep/.el-tree {
	background: none;
}

/deep/.el-tabs__nav-wrap::after {
	background-color: rgba(216, 216, 216, 1);
}

/deep/.el-form-item__label {
	text-align: left;
	/* font-size: 12px !important; */
}

/deep/.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
	margin-bottom: 15px !important;
}

/deep/ .el-tree-node:focus>.el-tree-node__content {
	background: rgb(217, 236, 255);
}

:deep(.el-tree-node:focus>.el-tree-node__content) {
	background: rgb(217, 236, 255);
}

:deep(.el-tree-node__content:hover, .el-upload-list__item:hover) {
	background: rgb(217, 236, 255);
}

/* .organization_configuration {} */
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
	/* // 设置颜色 */
	background: rgb(217, 236, 255);
	/* // 透明度为0.2的skyblue，作者比较喜欢的颜色  */
	color: #409eff;
	/* // 节点的字体颜色 */
	font-weight: bold;
	/* // 字体加粗 */
}

/deep/ .el-tabs__content {
	height: 100%;
}

/deep/.el-dialog__body .el-form>div .el-form-item__label {
	width: 130px !important;
}
</style>
<style>
.el-tree-node__content:hover,
.el-upload-list__item:hover {
	background-color: #e0effe87 !important;
}
</style>
