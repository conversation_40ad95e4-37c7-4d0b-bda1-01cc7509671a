<template>
	<div class="bg_con">
		<el-button class="fhsmry" type="primary" size="small" @click="fhsmry">返回</el-button>
		<el-tabs v-model="activeName" @tab-click="handleClick" style="height: 100%;z-index: 1;
    position: relative;">
			<el-tab-pane label="场所信息详情" name="jbxx" style="height: 92%;">
				<div class="jbxx">
					<el-form ref="form" :model="jbxx" label-width="152px" size="mini" :label-position="labelPosition"
						disabled>
						<div style="display:flex;justify-content: center;">
							<el-form-item label="场所名称" class="xm" style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="场所名称" v-model="jbxx.csmc" clearable>
								</el-input>
							</el-form-item>
							<el-form-item label="涉密程度" prop="smcd" class="xm"
								style="height:50px;border: 1px solid #ebebeb;">
								<el-radio-group v-model="jbxx.smcd">
									<el-radio v-for="item in sbmjxz" :v-model="jbxx.smcd" :label="item.id" :value="item.id"
										:key="item.id">{{ item.mc }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="申请时间" prop="qyrq" class="xm one-input"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-date-picker v-model="jbxx.qyrq" style="width:100%;" clearable type="date"
									placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
								</el-date-picker>
							</el-form-item>
						</div>
						<div style="display:flex;justify-content: center;">
							<el-form-item label="责任人" class="xm" style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="责任人" v-model="jbxx.zrr">
								</el-input>
							</el-form-item>
							<el-form-item label="责任人电话" class="xm" style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="责任人电话" v-model="jbxx.zrrdh">
								</el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="所在位置" prop="szdd" class="xm one-input"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="所在位置" v-model="jbxx.szdd" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;justify-content: center;">
							<el-form-item label="用途" prop="yt" class="one-line-textarea xm one-line-bz"
								style="height:50px;border: 1px solid #ebebeb;">
								<el-input type="textarea" v-model="jbxx.yt"></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;justify-content: center;">
							<el-form-item label="备注" prop="bz" class="one-line-textarea xm one-line-bz"
								style="height:50px;border: 1px solid #ebebeb;">
								<el-input type="textarea" v-model="jbxx.bz"></el-input>
							</el-form-item>
						</div>
					</el-form>
				</div>
			</el-tab-pane>
			<el-tab-pane label="审定审批详情" name="cssd" style="height: 100%;">
				<Cssd :msg="jbxx"></Cssd>
			</el-tab-pane>
			<el-tab-pane label="变更审批详情" name="bgsp" style="height: 100%;">
				<Bgsp :msg="jbxx"></Bgsp>
			</el-tab-pane>
			<el-tab-pane label="门禁授权详情" name="mjsq" style="height: 100%;">
				<Mjsq :msg="jbxx"></Mjsq>
			</el-tab-pane>
			<el-tab-pane label="无人授权人员进入涉密场所审批" name="wrsq" style="height: 100%;">
				<Wrsq :msg="jbxx"></Wrsq>
			</el-tab-pane>
			<el-tab-pane label="携带设备进入涉密场所审批" name="xdsb" style="height: 100%;">
				<Xdsb :msg="jbxx"></Xdsb>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
import Cssd from './csspdxqy/cssdxqy.vue'
import Bgsp from './csspdxqy/bgspxqy.vue'
import Wrsq from './csspdxqy/wrsqxqy.vue'
import Xdsb from './csspdxqy/xdsbxqy.vue'
import Mjsq from './csspdxqy/mjsqxqy.vue'
import {
	getAllSmsbmj,
} from '../../../api/index'
export default {
	data() {
		return {
			activeName: 'jbxx',
			jbxx: {},
			gwmc: [],
			jbxxsj: {},
			updateItemOld: {},
			labelPosition: 'right',
			sbmjxz: [],
			regionOption: [], //地域信息
			regionParams: {
				label: 'label', //这里可以配置你们后端返回的属性
				value: 'label',
				children: 'childrenRegionVo',
				expandTrigger: 'click',
				checkStrictly: true,
			},
			smdjxz: [],
			gwqdyjxz: [],
			jbzcxz: [],
			zgxlxz: [],
			sflxxz: [],
			yrxsxz: [],
			imageUrl: '',
			smryid: '',
		};
	},
	computed: {
	},
	components: {
		Cssd,
		Bgsp,
		Wrsq,
		Xdsb,
		Mjsq
	},
	mounted() {
		this.smdj()
		console.log(this.$route.query.row);
		this.jbxxsj = JSON.parse(JSON.stringify(this.$route.query.row))
		this.jbxx = this.jbxxsj
		// this.smryid = JSON.parse(JSON.stringify(this.$route.query.row.smryid))
		// console.log('this.smryid', this.smryid);
		console.log('this.jbxx', this.jbxx);
	},
	methods: {
		//获取涉密等级信息
		async smdj() {
			let data = await getAllSmsbmj()
			this.sbmjxz = data
		},
		handleClick(tab, event) {
			console.log(tab, event);
		},
		//返回涉密人员
		fhsmry() {
			this.$router.push({
				path: '/csgl'
			})
		},
	},
	watch: {},

};
</script>
<style scoped>
.bg_con {
	width: 100%;
	height: calc(100% - 38px);
}



>>>.el-tabs__content {
	height: 100%;
}

.jbxx {
	height: 92%;
	display: flex;
	justify-content: center;
	height: 100%;
	overflow-y: scroll;
	background: #fff;
}

.xm {
	background-color: #fff;
}

.container {
	height: 92%;
}

.dabg {
	box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
	border-radius: 8px;
	width: 100%;
}

.item_button {
	height: 100%;
	float: left;
	padding-left: 10px;
	line-height: 50px;
}

.select_wrap {

	.select_wrap_content {
		float: left;
		width: 100%;
		line-height: 50px;
		height: 100%;
		background: rgba(255, 255, 255, 0.7);

		.item_label {
			padding-left: 10px;
			height: 100%;
			float: left;
			line-height: 50px;
			font-size: 1em;
		}
	}
}

.mhcx1 {
	margin-top: 0px;
}

.widthw {
	width: 6vw;
}


/deep/.el-date-editor.el-input,
.el-date-editor.el-input__inner {
	width: 184px;
}

/deep/.el-radio-group {
	width: 300px;
	margin-left: 15px;
}

/deep/.mhcx .el-form-item {
	margin-top: 5px;
	margin-bottom: 5px;
}

/deep/.el-dialog {
	margin-top: 6vh !important;
}

/deep/.inline-inputgw {
	width: 105%;
}

.drfs {
	width: 126px
}

.daochu {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

/deep/.el-select .el-select__tags>span {
	display: flex !important;
	flex-wrap: wrap;
}

/deep/.el-dialog__body .el-form>div .el-form-item__label {
	width: 155px !important;
}

.bz {
	height: 72px !important;
}

/deep/.el-dialog__body .el-form>div>div {
	/* width: auto; */
	max-width: 100%;
}

.el-select__tags {
	white-space: nowrap;
	overflow: hidden;
}

.dialog-footer {
	display: block;
	margin-top: 10px;
}

.xmr /deep/.el-dialog__body .el-form .el-form-item--mini.el-form-item {
	height: 52px;
}

.avatar-uploader .el-upload {
	border: 1px dashed #d9d9d9;
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;

}

.avatar-uploader .el-upload:hover {
	border-color: #409EFF;
}

.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 482px;
	height: 254px;
	line-height: 254px;
	text-align: center;
}

.fhsmry {
	float: right;
	z-index: 99;
	margin-top: 5px;
	position: relative;
}

.avatar {
	width: 400px;
	height: 254px;
}

>>>.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
	margin-bottom: 0px;
}

.xm>>>.el-form-item__label {
	line-height: 50px;
	background-color: #f3f8ff;
}

/deep/.el-form-item--mini .el-form-item__content,
.el-form-item--mini .el-form-item__label {
	line-height: 50px;
	width: 330px !important;
}

/deep/.el-select>.el-input,
.el-color-picker__icon,
.el-input {
	margin-left: 15px;
	width: 300px !important;
}

/deep/.el-textarea {
	margin-left: 15px;
	width: 784px !important;
}

.one-line-bz>>>.el-form-item__content {
	line-height: 50px;
	width: 814px !important;
}

.one-input>>>.el-input{
	width: 784px !important;
}
/deep/.el-cascader--mini {
	margin-left: 15px;
	width: 300px !important;
}

/deep/.el-select .el-tag {
	margin-left: 28px;
}
</style>
