<template>
  <div id="layout" style="position: relative;height: 32px;overflow: hidden;">
  <!-- <div id="layout" style="position: relative;height: 132px;overflow: hidden;"> -->
    <!-- <div>{{tags}}</div> -->
    <div id="father" class="out">
      <span @click="spanClick(tag)" @contextmenu.prevent="rightClick($event,tag)" :id="tag.id" v-for="(tag,index) in tags" :key="index" :style="{'--background': tag.spanBackground, '--color': tag.fontColor}">
        {{tag.name}}
        <i v-if="tag.closable" class="el-icon-close" :style="{'--color': tag.fontColor, '--hoverColor': fontColorArr[0]}" @click="closeTag(tag)"></i>
      </span>
    </div>
    <!--右键菜单-->
    <div class="right-menu" @mouseleave="rightMenuLeave" :style="{'--left': rightMenuLeft, '--top': rightMenuTop, '--display': rightMenuDisplay}">
      <div class="div-out" @click="closeTagLeft()">
        <i class="el-icon-caret-right"></i>关闭左侧标签
      </div>
      <div class="div-out" @click="closeTagRight()">
        <i class="el-icon-caret-right"></i>关闭右侧标签
      </div>
      <div class="div-out" @click="closeTagOther()">
        <i class="el-icon-caret-right"></i>关闭其他标签
      </div>
      <div class="div-out" @click="closeTagAll()">
        <i class="el-icon-caret-right"></i>关闭全部标签
      </div>
    </div>
    <!---->
  </div>
</template>

<script>

export default {
  data () {
    return {
      tags: [],
      // span背景色字典
      spanBackgroundArr: ['#ECF5FF', '#409EFF'],
      // 字体颜色字典，span标签和i标签共用，为了颜色统一
      fontColorArr: ['#409EFF', 'white'],
      // 右键菜单
      rightMenuLeft: '0px',
      rightMenuTop: '0px',
      rightMenuDisplayArr: ['none', 'block'],
      rightMenuDisplay: 'none',
      // 当前右键操作的tag信息
      rightMenuCurrectTag: undefined,
      // 定时器
      // 左移定时器
      leftTimer: 0,
      // 右移定时器
      rightTimer: 0
    }
  },
  props: {
    // 组件重置标记
    resetFlagTags: {
      type: Number,
      default: 0
    },
    // app发过来的左侧菜单配置list，用以关闭tags中非左侧菜单的tag
    newMenuList: {
      type: Array,
      default: []
    }
  },
  methods: {
    //
    // 关闭不属于左侧菜单的tag
    removeNotInLeftMenuTags (leftMenuList) {
      let delObj
      let len = this.tags.length
      while (len > 0) {
        delObj = this.tags[len - 1]
        if (delObj && leftMenuList.indexOf(delObj.path) == -1) {
          this.tags.splice(this.tags.indexOf(delObj), 1)
        }
        len--
      }
      // 第一个设置为不可删除
      this.$nextTick(() => {
        console.log('this.tags[0]', this.tags[0])
        if (this.tags[0]) {
          this.tags[0].closable = false
        }
      })
      // 偏移量重置为0（left）
      let father = document.getElementById('father')
      father.style.left = '0px'
    },
    // 关闭左侧标签（第一个不允许关闭）
    closeTagLeft () {
      let delObj
      let len = this.tags.length
      let rightMenuCurrectTagIndex = this.tags.indexOf(this.rightMenuCurrectTag)
      while (len > 1) {
        delObj = this.tags[len - 1]
        if (delObj && this.tags.indexOf(delObj) < rightMenuCurrectTagIndex && delObj.id != this.rightMenuCurrectTag.id) {
          this.tags.splice(this.tags.indexOf(delObj), 1)
        }
        len--
      }
      this.rightMenuDisplay = this.rightMenuDisplayArr[0]
      this.$router.push(this.rightMenuCurrectTag.path)
    },
    // 关闭右侧标签
    closeTagRight () {
      this.tags.length = this.tags.indexOf(this.rightMenuCurrectTag) + 1
      this.rightMenuDisplay = this.rightMenuDisplayArr[0]
      this.$router.push(this.rightMenuCurrectTag.path)
    },
    // 关闭其他标签（第一个不允许关闭）
    closeTagOther () {
      let delObj
      let len = this.tags.length
      while (len > 1) {
        delObj = this.tags[len - 1]
        if (delObj && delObj.id != this.rightMenuCurrectTag.id) {
          this.tags.splice(this.tags.indexOf(delObj), 1)
        }
        len--
      }
      this.rightMenuDisplay = this.rightMenuDisplayArr[0]
      this.$router.push(this.rightMenuCurrectTag.path)
      // 调用一次第一个tag的右移
      this.spanClick(this.tags[0])
    },
    // 关闭全部标签（第一个不允许关闭）
    closeTagAll () {
      console.log('关闭全部', this.tags, this.tags.length <= 0)
      if (this.tags.length <= 0) {
        return
      }
      this.tags.length = 1
      this.rightMenuDisplay = this.rightMenuDisplayArr[0]
      // 偏移量重置为0（left）
      let father = document.getElementById('father')
      father.style.left = '0px'
      //
      if (this.$store.default.state.Counter.elAsideMenuList.indexOf(this.tags[0].path) != -1) {
        this.$router.push(this.tags[0].path)
      }
    },
    // span鼠标移出事件
    rightMenuLeave () {
      this.rightMenuDisplay = this.rightMenuDisplayArr[0]
    },
    // span鼠标右击事件
    rightClick (e, tag) {
      console.log('右键', tag, e)
      // 计算右边界
      let body = document.getElementsByTagName('body')[0]
      console.log('body widht', body.offsetWidth, e.x - 10 + 144, e.x - 10 + 144 - body.offsetWidth)
      let rightMenuLeftNum = e.x + 144
      if (rightMenuLeftNum > body.offsetWidth) {
        rightMenuLeftNum -= rightMenuLeftNum - body.offsetWidth
        rightMenuLeftNum -= 144
        // 右边距阈值
        rightMenuLeftNum -= 20
      } else {
        rightMenuLeftNum = e.x - 20
      }
      //
      this.rightMenuLeft = rightMenuLeftNum + 'px'
      this.rightMenuTop = e.y - 10 + 'px'
      this.rightMenuDisplay = this.rightMenuDisplayArr[1]
      this.rightMenuCurrectTag = tag
    },
    // 滚动事件
    getItemWidth () { },
    //
    spanClick (tag) {
      console.log('spanClick tag', tag)
      // 跳转到该tag的路由
      // this.$router.push({
      //   path:tag.path,
      //   query:tag.query
      //   })
      // 获取 relative 的 div
      let layout = document.getElementById('layout')
      // 获取 father 的 div
      let father = document.getElementById('father')
      // 获取 被点击的 span 元素
      let child = document.getElementById(tag.id)
      // 基准点（这里的基准点是 layout 的中轴）
      const basePointX = layout.offsetWidth / 2
      // 偏移误差值（防止刚刚好顶在右侧，不好看）
      const errorRightVal = 10
      /**
       * 计算是否需要偏移
       * 1、如果father已经发生了偏移（即offsetLeft小于0），
       * 则判断child是否是最后一个，是则不发生偏移，
       * 否则：
       * 1-1、child在基准点右侧则father的offsetLeft减少child的offsetLeft - father的offsetLeft - basePointX的距离（即child离中轴的距离）（当father的right>=0时中断）
       * 1-2、child在基准点左侧则father的offsetLeft增加basePintX - child的offsetLeft的距离（当father的offsetLeft<=0时中断）
       * 2、如果father未发生偏移（即offsetLeft大于等于0），
       * 则将father偏移child的offsetLeft-father的offsetLeft+child的offsetWidth-layout的offsetWidth/2的距离，
       * 使该元素居中显示（当father的style的right>=0时中断移动）
      */
      const fatherOffsetLeft = father.offsetLeft
      // return
      if (fatherOffsetLeft < 0) {
        // 判断child的位置在中轴的左侧还是右侧
        let checkPosNum = child.offsetLeft + father.offsetLeft
        console.log('child.offsetLeft', child.offsetLeft, father.offsetLeft, child.offsetLeft + father.offsetLeft, basePointX)
        if (this.tags[this.tags.length - 1].path != tag.path) {
          if (checkPosNum == basePointX) {
            // 刚好居中，什么也不用做
          } else if (checkPosNum > basePointX) {
            // 右侧
            console.log('右侧', (checkPosNum - basePointX) + 'px')
            // 左移
            this.moveToLeft(checkPosNum + basePointX, tag)
          } else {
            // 左侧
            // 右移
            this.moveToRight(basePointX + Math.abs(checkPosNum), tag)
          }
        } else {
          // 左移最后一个元素的宽度距离
          console.log('左移最后一个元素的宽度距离')
          this.moveToLeft(child.offsetWidth, tag)
        }
      } else {
        // 未发生偏移
        let checkPosNum = child.offsetLeft + child.offsetWidth + errorRightVal
        if (checkPosNum <= basePointX * 2) {
          // 刚好在最后或在可是范围内，没有超出father的可视范围，什么也不用做
        } else {
          // 超出可视范围，需要偏移
          this.moveToLeft(checkPosNum - basePointX * 2, tag)
        }
      }
    },
    // 计算每像素移动时间间隔（要求在600毫秒内完成）
    computeMoveInterval (step) {
      let interval = step / 300
      return interval <= 0 ? 1 : interval
    },
    // 左移
    moveToLeft (step, tag) {
      let start = new Date().getTime()
      console.log('准备左移')
      step = Math.abs(step)
      if (step > 0) {
        // 判断是否清除右移定时器
        if (this.rightTimer) {
          // 清除右移定时器
          console.log('左移，清除右移定时器')
          clearInterval(this.rightTimer)
        }
        // 判断重复开启定时器任务，清除旧的左移定时器
        if (this.leftTimer) {
          // 清除左移定时器
          console.log('左移，清除旧得左移定时器')
          clearInterval(this.leftTimer)
        }
        // 开启左移定时器
        this.leftTimer = setInterval(() => {
          this.$nextTick(() => {
            let layout = document.getElementById('layout')
            let father = document.getElementById('father')
            let child = document.getElementById(tag.id)
            /**
             * father 相对于 layout 的右侧基准点
             * 最右侧刚好在可视区域： father.offsetLeft + father.offsetWidth = layout.offsetWidth
             * 最右侧超出可视区域：father.offsetLeft + father.offsetWidth > layout.offsetWidth
             * 最右侧在可视区域内：father.offsetLeft + father.offsetWidth < layout.offsetWidth
            */
            const fatherRightPoint = father.offsetLeft + father.offsetWidth
            const childCenterPoint = father.offsetLeft + child.offsetLeft + child.offsetWidth / 2
            if (fatherRightPoint <= layout.offsetWidth || childCenterPoint <= layout.offsetWidth / 2) {
              // 左移完成，中断
              console.log('左移完成，中断')
              clearInterval(this.leftTimer)
              let end = new Date().getTime()
              console.log('左移总耗时', end - start)
            }
            // 计算步幅
            let stride = step / 150
            stride = stride < 1 ? 1 : stride
            // console.log('左移stride', stride)
            // 移动
            father.style.left = father.offsetLeft - stride + 'px'
            step -= stride
          })
        }, 1)
      }
    },
    // 右移
    moveToRight (step, tag) {
      let start = new Date().getTime()
      console.log('准备右移')
      step = Math.abs(step)
      if (step > 0) {
        // 判断是否清除左移定时器
        if (this.leftTimer) {
          // 清除左移定时器
          console.log('右移，清除左移定时器')
          clearInterval(this.leftTimer)
        }
        // 判断重复开启定时器任务，清除旧的右移定时器
        if (this.rightTimer) {
          // 清除左移定时器
          console.log('右移，清除旧的右移定时器')
          clearInterval(this.rightTimer)
        }
        // 开启右移定时器
        this.rightTimer = setInterval(() => {
          console.log('右移')
          let layout = document.getElementById('layout')
          let father = document.getElementById('father')
          let child = document.getElementById(tag.id)
          const childCenterPoint = father.offsetLeft + child.offsetLeft + child.offsetWidth / 2
          if (father.offsetLeft >= 0 || childCenterPoint >= layout.offsetWidth / 2) {
            // 右移完成，中断
            console.log('右移完成，中断')
            clearInterval(this.rightTimer)
            let end = new Date().getTime()
            console.log('右移总耗时', end - start)
          }
          // 计算步幅
          let stride = step / 150
          stride = stride < 1 ? 1 : stride
          // console.log('右移stride', stride)
          // 移动
          father.style.left = father.offsetLeft + stride + 'px'
          step -= stride
        }, 1)
      }
    },
    // tag关闭事件
    closeTag (tag) {
      /**
       * 移除当前tag
       * 判断tag是否有后一个，有则打开后一个
       * 没有则打开前一个
      */
      console.log(tag, this.tags)
      let delTagI = undefined
      for (let i = 0; i < this.tags.length; i++) {
        if (this.tags[i].path == tag.path) {
          delTagI = i
          // 成功找到当前tag，开始判断是否有后一个
          if (i < (this.tags.length - 1)) {
            this.$router.push(this.tags[i + 1].path)
            break
          } else {
            this.$router.push(this.tags[i - 1].path)
          }
        }
      }
      this.tags.splice(delTagI, 1)
      // 设置第一个tag不能被删除
      if (this.tags[0]) {
        this.tags[0].closable = false
      }
    }
  },
  watch: {
    '$route' (to, from) {
      // console.log(to)
      // 检测路由是否在左侧菜单中（为了做出只显示左侧菜单的效果）
      console.log(this.$store);
      if (this.$store.default.state.Counter.elAsideMenuList.indexOf(to.path) == -1) {
        console.log('检测路由不在左侧菜单中', to.path, '从tags中移除该不属于左侧菜单的项')
        return
      }
      // 标记需要进入可视化区域的tag
      let moveTag
      // 检测to路由是否已在tags里，没在则加入
      let isHaving = false
      for (let i = 0; i < this.tags.length; i++) {
        if (this.tags[i] && this.tags[i].path == to.path) {
          isHaving = true
          //
          moveTag = this.tags[i]
          break
        }
      }
      if (!isHaving) {
        console.log('!isHaving', to)
        let tagItem = {
          id: 'tagSpan' + this.tags.length,
          name: to.meta.name,
          path: to.path,
          closable: true,
          spanBackground: this.spanBackgroundArr[0],
          fontColor: this.fontColorArr[0],
          query:to.query
        }
        this.tags.push(tagItem)
        // // 设置第一个为不可删除
        // this.tags[0].closable = false
        //
        moveTag = tagItem
      }
      // 判断哪个tag应该高亮（即判断to所属的tag）
      for (let i = 0; i < this.tags.length; i++) {
        if (this.tags[i]) {
          if (this.tags[i].path == to.path) {
            // 高亮
            this.tags[i].spanBackground = this.spanBackgroundArr[1]
            this.tags[i].fontColor = this.fontColorArr[1]
          } else {
            // 取消高亮
            this.tags[i].spanBackground = this.spanBackgroundArr[0]
            this.tags[i].fontColor = this.fontColorArr[0]
          }
        }
      }
      // 设置第一个为不可删除
      if (this.tags[0]) {
        this.tags[0].closable = false
      }
      // 判断移动
      this.$nextTick(() => {
        // this.spanClick(document.getElementById(moveTag.id))
        this.spanClick(moveTag)
      })
    },
    // 监听左侧菜单配置集合变更
    newMenuList (newVal, oldVal) {
      // console.log('菜单逻辑 组件检测到左侧菜单配置变更', newVal)
      this.removeNotInLeftMenuTags(newVal)
    },
    // // 监听组件重置标记
    // resetFlagTags(newVal, oldVal) {
    //   console.log('组件hsoft-enum接收到重置标记', newVal)
    //   this.tags.length = 0
    // }
    // 监听vuex状态机
    "$store.default.state.Counter": {
      handler (newVal, oldVal) {
        console.log('监测到vuex状态机关闭tag变化', newVal, newVal.closeTag)
        if (newVal && newVal.closeTag && Object.keys(newVal.closeTag).length > 0) {
          // if (newVal.closeTag.path == 'allTagWithOutFirst') {
          //   console.log('关闭全部tag（除第一个）')
          //   this.closeTagAll()
          //   return
          // }
          if (newVal.closeTag.path != 'allTagWithOutFirst') {
            this.closeTag(newVal.closeTag)
          }
        }
      },
      deep: true,
    },
  }
}
</script>

<style scoped>
.out {
  /**不能增加 width 样式哈，否则无法滚动
  width: 100%;width: 100px;**/
  height: 32px;
  white-space: nowrap;
  overflow: auto;
  position: absolute;
  left: 0;
}
span {
  background-color: var(--background);
  color: var(--color);
  border-color: var(--background);
  height: 32px;
  padding: 0 10px;
  line-height: 30px;
  font-size: 16px;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  box-sizing: border-box;
  white-space: nowrap;
  display: inline-block;
  margin-right: 10px;
  cursor: pointer;
}
i {
  color: var(--color);
}
.el-icon-close {
  /* color: #409EFF; */
  font-size: 12px;
}
.el-icon-close:hover {
  cursor: pointer;
  /* border-radius: 50%; */
  /* padding: 2px; */
  box-sizing: border-box;
  /* background: white; */
  color: var(--hoverColor);
  color: var(--color);
  /* color: white; */
}
/**右键菜单**/
.right-menu {
  z-index: 999;
  position: fixed;
  left: var(--left);
  top: var(--top);
  /* top: 130px; */
  padding: 10px 0px;
  border-radius: 6px;
  box-shadow: 0px 0px 10px #ababab;
  background: white;
  font-size: 14px;
  /* color: #000; */
  color: rgba(0, 0, 0, 1);
  display: var(--display);
  /* display: block; */
}
.right-menu .div-out {
  height: 26px;
  line-height: 26px;
  /* background: red; */
  font-size: 12px;
  padding: 0 30px;
}
.right-menu .div-out:hover {
  background: #dee1e6;
  cursor: pointer;
}
</style>
