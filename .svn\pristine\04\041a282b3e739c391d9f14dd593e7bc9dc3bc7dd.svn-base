<template>
    <div class="sec-container" v-loading="loading">
        <!-- 标题 -->
        <el-tabs v-model="activeName">
            <el-tab-pane label="审批指南" name="first">
                <div class="sec-form-six haveBorderTop sec-footer">
                    <el-button @click="ljbl" class="fr" type="success">立即办理</el-button>
                </div>
                <el-table border class="sec-el-table" :data="spznList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                    <el-table-column prop="hjmc" label="办理流程"></el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="审批信息" name="second">
                <p class="sec-title">基本信息</p>
                <div class="sec-form-container">
                    <el-form ref="formName" :model="tjlist" label-width="225px">
                        <!-- 第一部分包括姓名到常住地公安start -->
                        <div class="sec-header-section">
                            <div class="sec-form-left">
                                <el-form-item label="申请部门">
                                    <el-input placeholder="" v-model="tjlist.szbm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="申请人">
                                    <el-input placeholder="" v-model="tjlist.xqr" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="原使用期限">
                                    <el-date-picker v-model="tjlist.ysyqx" class="riq" disabled type="daterange"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                                    </el-date-picker>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="续借期限">
                                    <el-date-picker v-model="tjlist.xjqx" class="riq" disabled type="daterange"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                                    </el-date-picker>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="项目编号">
                                    <el-input placeholder="" v-model="tjlist.xmbh" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left sec-form-left-textarea">
                                <el-form-item label="续借情况说明">
                                    <el-input placeholder="" v-model="tjlist.xjqksm" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="携带部门">
                                    <el-input placeholder="" v-model="tjlist.xdrbm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="携带人">
                                    <el-input placeholder="" v-model="tjlist.xdr" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="项目经理部门">
                                    <el-input placeholder="" v-model="tjlist.xmjlbm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="项目经理">
                                    <el-input placeholder="" v-model="tjlist.xmjl" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <!-- 载体详细信息start -->
                            <p class="sec-title">借用/携带外出设备详细信息</p>
                            <el-table border class="sec-el-table" :data="sbGlSpList"
                                :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                                <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                                <el-table-column prop="bmbh" label="设备保密编号"></el-table-column>
                                <el-table-column prop="mj" label="密级"></el-table-column>
                                <el-table-column prop="lx" label="设备类型"></el-table-column>
                                <el-table-column prop="ppxh" label="品牌型号"></el-table-column>
                                <el-table-column prop="cfwz" label="存放位置"></el-table-column>
                                <el-table-column prop="zjxlh" label="设备序列号"></el-table-column>
                                <el-table-column prop="ypxlh" label="硬盘序列号"></el-table-column>
                                <el-table-column prop="yzrr" label="责任人"></el-table-column>
                            </el-table>
                        </div>
                        <!-- 载体详细信息end -->
                        <p class="sec-title">部门保密员审核</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmysc">
                                <el-radio v-model="tjlist.bmysc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="设备超期借用" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门保密员审批人" prop="bmyscxm">
                                <el-input placeholder="" disabled v-model="tjlist.bmyscxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmyscsj">
                                <el-date-picker disabled v-model="tjlist.bmyscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">部门领导审批</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmldsc">
                                <el-radio v-model="tjlist.bmldsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="设备超期借用" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门领导审批人" prop="bmldscxm">
                                <el-input placeholder="" disabled v-model="tjlist.bmldscxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmldscsj">
                                <el-date-picker disabled v-model="tjlist.bmldscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">保密办意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmbsc">
                                <el-radio v-model="tjlist.bmbsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="设备超期借用" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="保密办领导审批人" prop="bmbscxm">
                                <el-input placeholder="" disabled v-model="tjlist.bmbscxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmbscsj">
                                <el-date-picker disabled v-model="tjlist.bmbscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">轨迹处理</p>
                        <el-table border class="sec-el-table" :data="gjclList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                            <el-table-column prop="clrid" label="办理人"></el-table-column>
                            <el-table-column prop="bllx" label="办理类型"></el-table-column>
                            <el-table-column prop="clyj" label="办理意见"></el-table-column>
                            <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                            <el-table-column prop="clsj" label="办理时间"></el-table-column>
                        </el-table>

                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="流程跟踪" name="third">
                <el-table border class="sec-el-table" :data="lcgzList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                    <el-table-column prop="clrid" label="办理人"></el-table-column>
                    <el-table-column prop="bllx" label="办理类型"></el-table-column>
                    <el-table-column prop="clyj" label="办理意见"></el-table-column>
                    <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                    <el-table-column prop="clsj" label="办理时间"></el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import {
    getSpUserList,
} from '../../../../api/index'
import {
    updateSbCqjy,
    getSbCqjyJlidBySlid,
    getSbCqjyBySlid,
    getSbqdListByYjlid,
} from '../../../../api/cqjy'
import {
    //审批指南
    getBlzn,
    //审批信息
    //判断实例所处环节
    getSchj,
    //事项审核
    getSxsh,
    //非第一环节选择审批人
    tjclr,
    //流程跟踪
    getSpGjxx,
} from '../../../../api/wdgz'
import { getUserInfo } from '../../../../api/dwzc'
import AddLineTable from "../../../components/common/addLineTable.vue"; //人工纠错组件
export default {
    components: {
        AddLineTable,
    },
    props: {},
    data() {
        return {
            activeName: 'second',
            // table 行样式
            headerCellStyle: {
                background: '#EEF7FF',
                color: '#4D91F8'
            },
            //审批指南
            spznList: [],
            formInline: {
                'bmmc': '',
                'xm': ''
            }, // 搜索条件
            loading: false,
            page: 1, // 审批人弹框当前页
            pageSize: 10, // 审批人弹框每页条数
            radioIdSelect: '', // 审批人弹框人员单选
            total: 0, // 弹框人员总数
            regionParams: {
                label: 'label', //这里可以配置你们后端返回的属性
                value: 'label',
                children: 'childrenRegionVo',
                expandTrigger: 'click',
                checkStrictly: true
            }, //地域信息配置参数
            selectlistRow: [], //列表的值
            mbhjid: '',

            // form表单提交数据
            tjlist: {
                xqr: '',
                szbm: '',
                jscdqx: [],
                sbGlSpList: [],
                zxfw: '',
                yt: '',
                yjr: '',
                zfdw: '',
                yztbh: '',
                qsdd: '',
                mddd: '',
                fhcs: [],
                jtgj: [],
                jtxl: '',
                gdr: '',
                bgbm: '',
                bcwz: ''
            },
            sbGlSpList: [],

            scqk: [
                {
                    sfty: '同意',
                    id: 1
                },
                {
                    sfty: '不同意',
                    id: 0
                },
            ],
            ztlxList: [
                {
                    lxid: '1',
                    lxmc: '纸介质'
                },
                {
                    lxid: '2',
                    lxmc: '光盘'
                },
                {
                    lxid: '3',
                    lxmc: '电磁介质'
                },
            ],
            smdjList: [
                {
                    smdjid: '1',
                    smdjmc: '绝密'
                },
                {
                    smdjid: '2',
                    smdjmc: '机密'
                },
                {
                    smdjid: '3',
                    smdjmc: '秘密'
                },
                {
                    smdjid: '4',
                    smdjmc: '内部'
                },
            ],
            xdfsList: [
                {
                    xdfsid: '1',
                    xdfsmc: '包装密封，封口处加盖密封章'
                },
                {
                    xdfsid: '2',
                    xdfsmc: '指派专人传递'
                },
                {
                    xdfsid: '3',
                    xdfsmc: '密码箱防护'
                },
            ],
            jtgjList: [
                {
                    jtgjid: '1',
                    jtgjmc: '飞机'
                },
                {
                    jtgjid: '2',
                    jtgjmc: '火车'
                },
                {
                    jtgjid: '3',
                    jtgjmc: '专车'
                },
            ],
            //轨迹处理
            gjclList: [],
            //人员任用
            smryList: [],
            disabled2: false,
            disabled3: false,
            disabled4: false,
            //通过
            tgdis: false,
            dialogVisible: false,
            fileRow: '',
            fwdyid: '',
            slid: '',
            jlid: '',
            xsyc: true,
            zhsp: true,
            jgyf: '',
            xm: '',
            //审批状态码 1 2 3 4
            zplcztm: null,
            //流程跟踪
            lcgzList: [],
        }
    },
    computed: {

    },
    mounted() {
        this.getNowTime()
        console.log(this.$route.query.list);
        this.fwdyid = this.$route.query.fwdyid
        console.log("this.fwdyid", this.fwdyid);
        this.slid = this.$route.query.slid
        console.log("this.slid", this.slid);
        this.getjlid()
        this.dqlogin()
        //审批指南初始化列表
        this.spzn()
        // //审批信息初始化列表
        // this.spxxxgcc()
        setTimeout(() => {
            this.spxx()
        }, 500)
        // // //事项审核
        this.sxsh()
        // //初始化el-dialog列表数据
        this.splist()
        //流程跟踪初始化列表
        this.lcgz()
    },
    methods: {
        async getjlid() {
            let params = {
                slid: this.slid
            }
            let data = await getSbCqjyJlidBySlid(params)
            console.log(data);
            this.jlid = data
        },
        getNowTime() {
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            console.log(defaultDate)
            return defaultDate;
            this.$set(this.info, "stockDate", defaultDate);
        },

        //当前登录用户
        async dqlogin() {
            let data = await getUserInfo()
            this.xm = data.xm
            console.log('this.dqlogin', this.xm);
        },
        //立即办理
        ljbl() {
            this.activeName = 'second'
        },
        //审批指南
        //审批指南初始化列表
        async spzn() {
            let params = {
                fwdyid: this.fwdyid,
            }
            let data = await getBlzn(params)
            if (data.code == 10000) {
                this.spznList = data.data.content
            }
        },
        //审批信息
        async spxx() {
            let params = {
                slid: this.slid
            }
            let data = await getSbCqjyBySlid(params)
            console.log(data);
            this.tjlist = data
            let zt = {
                yjlid: this.jlid
            }
            console.log(zt);
            let ztqd = await getSbqdListByYjlid(zt)
            this.sbGlSpList = ztqd
            this.sbGlSpList.forEach((item) => {
                console.log(item);
                if (item.mj == 1) {
                    item.mj = '绝密'
                } else if (item.mj == 2) {
                    item.mj = '机密'
                } else if (item.mj == 3) {
                    item.mj = '秘密'
                } else if (item.mj == 4) {
                    item.mj = '内部'
                }
            })
            let Array = []
            Array.push(this.tjlist.yjyqsrq, this.tjlist.yjyjzrq)
            console.log(Array);
            this.tjlist.ysyqx = Array
            let xjArray = []
            xjArray.push(this.tjlist.xjqsrq, this.tjlist.xjjzrq)
            console.log(xjArray);
            this.tjlist.xjqx = xjArray
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            console.log('this.spxx', this.xm);
            if (this.zplcztm == 1) {
                this.tjlist.bmyscxm = this.xm
                this.$set(this.tjlist, 'bmyscsj', defaultDate)
                console.log(this.tjlist.bmyscxm);

            } else if (this.zplcztm == 2) {
                this.tjlist.bmyscxm = this.tjlist.bmyscxm
                this.tjlist.bmldscxm = this.xm
                console.log(this.tjlist.bmldscxm);

                this.$set(this.tjlist, 'bmldscsj', defaultDate)
            } else if (this.zplcztm == 3) {
                this.tjlist.bmyscxm = this.tjlist.bmyscxm
                this.tjlist.bmldscxm = this.tjlist.bmldscxm
                this.tjlist.bmbscxm = this.xm
                console.log(this.tjlist.bmbscxm);

                this.$set(this.tjlist, 'bmbscsj', defaultDate)
            }
        },
        //判断实例所处环节
        async pdschj() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSchj(params)
            this.zplcztm = data.data.content
            console.log('this.zplcztm', this.zplcztm);
            if (data.code == 10000) {
                if (data.data.content == 1) {
                    this.disabled3 = true
                    this.disabled4 = true
                }
                if (data.data.content == 2) {
                    this.disabled2 = true
                    this.disabled4 = true
                }
                if (data.data.content == 3) {
                    this.disabled2 = true
                    this.disabled3 = true
                }
            }
        },
        chRadio() { },
        //初始化el-dialog列表数据
        async splist() {
            let params = {
                fwdyid: this.fwdyid,
                'xm': this.formInline.xm,
                'bmmc': this.formInline.bmmc,
                page: this.page,
                pageSize: this.pageSize,
                qshjid: this.mbhjid,
            }
            let data = await getSpUserList(params)
            this.smryList = data.records
            this.total = data.total


        },
        onSubmit() {
            this.splist()
        },
        async submit() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                shry: this.selectlistRow[0].yhid,
                mbhjid: this.mbhjid,
            }
            let data = await tjclr(params)
            if (data.code == 10000) {
                this.$message({
                    message: data.message,
                    type: 'success'
                });
                this.dialogVisible = false
                setTimeout(() => {
                    this.$router.push('/dbsx')
                }, 500)
            }
        },
        handleSelectionChange(index, row) {
            this.radioIdSelect = row
        },
        // 保存
        async save(index) {
            let jgbz = index
            if (jgbz == 1) {
                console.log(this.tjlist.bmysc);
                console.log(this.tjlist.bmldsc);
                console.log(this.tjlist.bmbsc);
                if (this.zplcztm == 1) {
                    if (this.tjlist.bmysc != undefined) {
                        if (this.tjlist.bmyscsj != undefined) {
                            this.tgdis = true
                            let obj = {
                                bmysc: this.tjlist.bmysc,
                                bmyscsj: this.tjlist.bmyscsj,
                                bmyscxm: this.tjlist.bmyscxm,
                            }
                            let params = Object.assign(this.tjlist, obj)
                            let data = await updateSbCqjy(params)
                            if (data.code == 10000) {
                                this.jgyf = 1
                                this.sxsh()
                                this.spxx()
                            } else {
                                this.spxx()
                            }
                        } else { this.$message.warning('请选择日期') }
                    } else { this.$message.warning('是否同意') }
                }
                else if (this.zplcztm == 2) {
                    if (this.tjlist.bmldsc != undefined) {
                        if (this.tjlist.bmldscsj != undefined) {
                            this.tgdis = true
                            let obj = {
                                bmldsc: this.tjlist.bmldsc,
                                bmldscsj: this.tjlist.bmldscsj,
                                bmldscxm: this.tjlist.bmldscxm,
                            }
                            let params = Object.assign(this.tjlist, obj)
                            let data = await updateSbCqjy(params)
                            if (data.code == 10000) {
                                this.jgyf = 1
                                this.sxsh()
                                this.spxx()
                            } else {
                                this.spxx()
                            }
                        } else { this.$message.warning('请选择日期') }
                    } else { this.$message.warning('是否同意') }
                }
                else if (this.zplcztm == 3) {
                    if (this.tjlist.bmbsc != undefined) {
                        if (this.tjlist.bmbscsj != undefined) {
                            this.tgdis = true
                            let obj = {
                                bmbsc: this.tjlist.bmbsc,
                                bmbscsj: this.tjlist.bmbscsj,
                                bmbscxm: this.tjlist.bmbscxm,
                            }
                            let params = Object.assign(this.tjlist, obj)
                            let data = await updateSbCqjy(params)
                            if (data.code == 10000) {
                                this.jgyf = 1
                                this.sxsh()
                                this.spxx()
                            } else {
                                this.spxx()
                            }
                        } else { this.$message.warning('请选择日期') }
                    } else { this.$message.warning('是否同意') }
                }
            } else if (jgbz == 2) {
                this.jgyf = 2
                this.sxsh()
                this.spxx()
            } else if (jgbz == 3) {
                this.jgyf = 3
                this.sxsh()
                this.spxx()
            }
        },
        //事项审核
        async sxsh() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                jg: this.jgyf,
                smryid: ''
            }
            let data = await getSxsh(params)
            if (data.code == 10000) {
                this.tgdis = false
                if (data.data.zt == 0) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // this.smryList = data.data.blrarr
                    this.mbhjid = data.data.mbhjid
                    this.splist()
                    this.dialogVisible = true
                } else if (data.data.zt == 1) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 2) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 3) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
                else if (data.data.zt == 4) {
                    this.$message({
                        message: data.data.msg
                    });
                    console.log(1111111111111);
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
            }
        },
        //列表分页--跳转页数
        handleCurrentChange(val) {
            this.page = val
            this.splist()
        },
        //列表分页--更改每页显示个数
        handleSizeChange(val) {
            this.page = 1
            this.pageSize = val
            this.splist()
        },
        // 点击行触发，选中或不选中复选框
        handleRowClick(row, column, event) {
            this.$refs.multipleTable.toggleRowSelection(row)
            this.selectChange(this.selectlistRow)
        },
        handleSelect(selection, val) {
            // //只能选择一行，选择其他，清除上一行
            if (selection.length > 1) {
                let del_row = selection.shift()
                this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中
            }
        },
        selectRow(selection) {
            if (selection.length <= 1) {
                console.log('点击选中数据：', selection);
                this.selectlistRow = selection
                this.xsyc = true
            } else if (selection.length > 1) {
                this.$message.warning('只能选中一条数据')
                this.xsyc = false
            }
        },
        // 返回
        returnIndex() {
            this.$router.push('/gwbgscb')
        },
        //流程跟踪
        //流程跟踪初始化列表
        async lcgz() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSpGjxx(params)
            if (data.code == 10000) {
                this.lcgzList = data.data.content
                this.gjclList = data.data.content
                console.log(this.gjclList);
            }
        },
    },
    watch: {

    }
}

</script>
  
<style scoped>
.sec-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: overlay;
}

.sec-title {
    border-left: 5px solid #1b72d8;
    color: #1b72d8;
    font-size: 20px;
    font-weight: 700;
    text-indent: 10px;
    margin-bottom: 20px;
    margin-top: 10px;
}

.sec-form-container {
    width: 100%;
    height: 100%;
}

.sec-form-left {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
}

.sec-form-left:not(:first-child) {
    border-top: 0;
}

.sec-form-left .el-form-item {
    float: left;
    width: 100%;
}

.sec-header-section {
    width: 100%;
    position: relative;
}

.sec-header-pic {
    width: 258px;
    position: absolute;
    right: 0px;
    top: 0;
    height: 163px;
    border: 1px solid #CDD2D9;
    border-left: 0;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sec-form-second {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
    border-top: 0;
}

.sec-form-third {
    border: 1px solid #CDD2D9;
    /* height: 40px;  */
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-four {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-five {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    overflow: hidden;
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.yulan {
    text-align: center;
    cursor: pointer;
    color: #3874D5;
    font-weight: 600;
    float: left;
    margin-left: 10px;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 2px solid #EBEBEB;
}

.sec-form-six {
    border: 1px solid #CDD2D9;
    overflow: hidden;
    background: #ffffff;
    padding: 10px;
}

.ml10 {
    margin-left: 10px;
}

.sec-footer {
    margin-top: 10px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
    border: none;
}

.sec-left-text {
    float: left;
    margin-right: 130px;
}

.haveBorderTop {
    border-top: 1px solid #CDD2D9;
}

.gtzzsmgwgz>>>.el-form-item__label {
    text-align: left !important;
}

>>>.longLabel .el-form-item__content {
    padding-left: 20px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

/* .sec-form-second:not(:first-child){
    border-top: 0;
  } */
.sec-form-second .el-form-item {
    float: left;
    width: 100%;
}

.sec-el-table {
    width: 100%;
    border: 1px solid #EBEEF5;
    height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
    padding-left: 15px;
    background-color: #F5F7FA;
    width: calc(100% - 16px);
    border-right: 1px solid #CDD2D9;
    color: #C0C4CC;
}

>>>.sec-el-table .el-input__inner {
    border: none !important;
    border-radius: 0;
}

.riq {
    width: 100% !important;
}

>>>.sec-form-container .el-form-item__label {
    width: 200px;
    text-align: center;
    font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
    border: none;
    border-right: 1px solid #CDD2D9;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item {
    margin-bottom: 0px;
}

/* >>>.el-form > div {
    border: 1px solid #CDD2D9;;
  } */
>>>.el-form-item__label {
    border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
    margin-top: 5px;
    margin-bottom: 5px;
  } */

.widthw {
    width: 6vw;
}

.dlFqsqContainer {
    width: 100%;
    height: 100%;
}

.dlFqsqContainer label {
    font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
    width: 150px;
    margin-left: 10px;
}

.dlFqsqContainer .searchButton {
    margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
    height: 40px;
}

.dlFqsqContainer .input1 {
    margin-right: 20px;
}

.dlFqsqContainer .tb-container {
    margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
    margin-top: 20px;
}
</style>
  