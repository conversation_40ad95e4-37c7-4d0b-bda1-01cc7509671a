{"version": 3, "sources": ["webpack:///src/renderer/view/bggl/bgglgl.vue", "webpack:///./src/renderer/view/bggl/bgglgl.vue?a9e9", "webpack:///./src/renderer/view/bggl/bgglgl.vue"], "names": ["__webpack_exports__", "components", "props", "data", "pageLoading", "wdList", "tableDataCopy", "headerCellStyle", "background", "color", "tableHeight", "wdlx", "lxmc", "lxid", "sltshow", "tjlist", "wdmc", "wdys", "wdslt", "wdzy", "wd<PERSON><PERSON><PERSON>", "page", "pageSize", "formInline", "uploadDisable", "rules", "required", "message", "trigger", "selectlistRow", "dr_dialog", "computed", "mounted", "_this", "this", "getWdList", "$refs", "upload", "addEventListener", "e", "readExcel", "watch", "tjlist.wdmc", "deep", "handler", "newV", "oldV", "methods", "beforeAvatarUpload", "file", "isJPG", "type", "isPNG", "$message", "error", "httpRequest", "URL", "createObjectURL", "chooseFile", "click", "submitTj", "formName", "_ref", "_this2", "validate", "__WEBPACK_IMPORTED_MODULE_2_babel_runtime_helpers_asyncToGenerator___default", "__WEBPACK_IMPORTED_MODULE_0_babel_runtime_regenerator___default", "a", "mark", "_callee2", "valid", "wrap", "_context2", "prev", "next", "Object", "__WEBPACK_IMPORTED_MODULE_3__api_bggl__", "then", "_ref2", "_callee", "res", "keyStr", "buf", "encryptStr", "wdlxNumber", "formData", "_context", "__WEBPACK_IMPORTED_MODULE_4__utils_aesUtils__", "__WEBPACK_IMPORTED_MODULE_1_babel_runtime_core_js_json_stringify___default", "sent", "FormData", "append", "code", "resetForm", "catch", "err", "stop", "_x2", "apply", "arguments", "abrupt", "_x", "_this3", "target", "files", "fr", "FileReader", "name", "readAsA<PERSON>y<PERSON><PERSON>er", "onload", "buffer", "<PERSON><PERSON><PERSON>", "from", "result", "_this4", "params", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "view", "Uint8Array", "i", "downloadFileItem", "row", "_this5", "_callee3", "jmdatas", "bufferFile", "fileName", "_context3", "wdFile", "uuid", "JSON", "parse", "Blob", "__WEBPACK_IMPORTED_MODULE_5_file_saver___default", "saveAs", "onSubmit", "returnSy", "$router", "push", "deleteFiles", "id", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "bgidList", "for<PERSON>ach", "sel", "bgid", "selectRow", "val", "closeDialog", "resetFields", "bggl_bgglgl", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "value", "expression", "staticClass", "attrs", "inline", "model", "size", "clearable", "placeholder", "callback", "$$v", "$set", "_l", "item", "key", "label", "_v", "icon", "on", "ref", "accept", "$event", "border", "header-cell-style", "height", "stripe", "selection-change", "width", "align", "prop", "scopedSlots", "_u", "fn", "scoped", "title", "top", "visible", "show-close", "close", "update:visible", "label-width", "disabled", "action", "show-file-list", "before-upload", "http-request", "src", "_e", "autosize", "minRows", "maxRows", "maxlength", "show-word-limit", "slot", "staticRenderFns", "__vue_styles__", "ssrContext", "__webpack_require__", "Component", "normalizeComponent", "bgglgl"], "mappings": "kLA+GAA,EAAA,GACAC,cACAC,SACAC,KAHA,WAIA,OACAC,aAAA,EACAC,UACAC,iBAEAC,iBACAC,WAAA,UAAAC,MAAA,WAGAC,YAAA,iCAEAC,OAEAC,KAAA,KACAC,KAAA,MAGAD,KAAA,aACAC,KAAA,MAGAD,KAAA,eACAC,KAAA,MAGAD,KAAA,aACAC,KAAA,MAGAC,QAAA,GAEAC,QACAJ,KAAA,GACAK,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,SAAA,MAEAC,KAAA,EACAC,SAAA,GACAC,YACAP,KAAA,GACAL,KAAA,IAEAa,eAAA,EAEAC,OACAd,OACAe,UAAA,EACAC,QAAA,UACAC,QAAA,SAEAZ,OACAU,UAAA,EACAC,QAAA,QACAC,QAAA,SAEAX,OACAS,UAAA,EACAC,QAAA,WACAC,QAAA,SAEAV,QACAQ,UAAA,EACAC,QAAA,WACAC,QAAA,UAGAC,iBACAC,WAAA,IAGAC,YACAC,QA9EA,WA8EA,IAAAC,EAAAC,KAEAA,KAAAC,UAAAD,KAAAX,WAAAP,KAAAkB,KAAAX,WAAAZ,MAEAuB,KAAAE,MAAAC,OAAAC,iBAAA,kBAAAC,GACAN,EAAAO,UAAAD,MAIAE,OACAC,eACAC,MAAA,EACAC,QAAA,SAAAC,EAAAC,GACA,IAAAD,IACAX,KAAAV,eAAA,MAKAuB,SAEAC,mBAFA,SAEAC,GACA,IAAAC,EAAA,eAAAD,EAAAE,KACAC,EAAA,cAAAH,EAAAE,KAIA,OAHAD,GAAAE,GACAlB,KAAAmB,SAAAC,MAAA,wBAEAJ,GAAAE,GAGAG,YAXA,SAWApD,GACA+B,KAAApB,QAAA0C,IAAAC,gBAAAtD,EAAA8C,MACAf,KAAAnB,OAAAG,MAAAf,EAAA8C,MAGAS,WAhBA,WAiBAxB,KAAAE,MAAAC,OAAAsB,SAGAC,SApBA,SAoBAC,GAAA,IAEAC,EAFAC,EAAA7B,KACAA,KAAA9B,aAAA,EACA8B,KAAAE,MAAAyB,GAAAG,UAAAF,EAAAG,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,EAAAC,GAAA,OAAAJ,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,WACAJ,EADA,CAAAE,EAAAE,KAAA,QAEAC,OAAAC,EAAA,EAAAD,GAAAE,KAAA,eAAAC,EAAAb,IAAAC,EAAAC,EAAAC,KAAA,SAAAW,EAAAC,GAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAnB,EAAAC,EAAAI,KAAA,SAAAe,GAAA,cAAAA,EAAAb,KAAAa,EAAAZ,MAAA,cACAO,EAAA,cAAAD,EACAE,EAAAnB,EAAAhD,OAAAK,SACA+D,OAHA,EAAAG,EAAAZ,KAAA,EAIAC,OAAAY,EAAA,EAAAZ,CAAAa,IAAAN,GAAAD,GAJA,OAIAE,EAJAG,EAAAG,KAKAL,EAAA,MAAArB,EAAAhD,OAAAJ,KAAA,gBAAAoD,EAAAhD,OAAAJ,KAAA,kBAAAoD,EAAAhD,OAAAJ,KAAA,gBAAAoD,EAAAhD,OAAAJ,KAAA,KACA0E,EAAA,IAAAK,UACAC,OAAA,OAAAX,GACAK,EAAAM,OAAA,OAAAP,GACAC,EAAAM,OAAA,OAAA5B,EAAAhD,OAAAC,MACAqE,EAAAM,OAAA,KAAA5B,EAAAhD,OAAAE,MACAoE,EAAAM,OAAA,YAAA5B,EAAAhD,OAAAG,OACAmE,EAAAM,OAAA,OAAA5B,EAAAhD,OAAAI,MACAkE,EAAAM,OAAA,SAAAR,GACApB,EAAAjC,WAAA,EAEA6C,OAAAC,EAAA,EAAAD,CAAAU,GAAAR,KAAA,SAAAG,GACA,KAAAA,EAAAY,OACA7B,EAAA3D,aAAA,EACA2D,EAAAV,UACA1B,QAAA,OACAwB,KAAA,YAEAY,EAAA8B,YACA9B,EAAA5B,UAAA4B,EAAAxC,WAAAP,KAAA+C,EAAAxC,WAAAZ,SAEAmF,MAAA,SAAAC,GACAhC,EAAAV,SAAAC,MAAA,aA3BA,yBAAAgC,EAAAU,SAAAjB,EAAAhB,MAAA,gBAAAkC,GAAA,OAAAnB,EAAAoB,MAAAhE,KAAAiE,YAAA,IA6BAL,MAAA,SAAAC,GACAhC,EAAAV,SAAAC,MAAA,eAhCAkB,EAAAE,KAAA,mBAkCAJ,EAlCA,CAAAE,EAAAE,KAAA,gBAmCAX,EAAAV,SAAAC,MAAA,cACAS,EAAA8B,YACA9B,EAAAjC,WAAA,EACAiC,EAAA3D,aAAA,EAtCAoE,EAAA4B,OAAA,UAuCA,GAvCA,yBAAA5B,EAAAwB,SAAA3B,EAAAN,MAAA,SAAAsC,GAAA,OAAAvC,EAAAoC,MAAAhE,KAAAiE,eA4CA3D,UAlEA,SAkEAD,GAAA,IAAA+D,EAAApE,KACAe,EAAAV,EAAAgE,OAAAC,MACAC,EAAA,IAAAC,WACAxE,KAAAnB,OAAAC,KAAAiC,EAAA,GAAA0D,KACAF,EAAAG,kBAAA3D,EAAA,IACAwD,EAAAI,OAAA,SAAAtE,GAEA,IAAAuE,EAAAC,EAAAC,KAAAzE,EAAAgE,OAAAU,QAEAX,EAAAvF,OAAAK,SAAA0F,EACAR,EAAA9E,eAAA,IAIAW,UAhFA,SAgFAnB,EAAAL,GAAA,IAAAuG,EAAAhF,KACAA,KAAA9B,aAAA,EAEA,IAAA+G,GACAnG,OACAL,QAEMgE,OAAAC,EAAA,EAAAD,CAANwC,GAAAtC,KAAA,SAAAG,GACA,KAAAA,EAAAY,OACAsB,EAAA7G,OAAA2E,EAAA7E,KACA+G,EAAA5G,cAAA0E,EACAkC,EAAA9G,aAAA,KAEA0F,MAAA,SAAAC,GACAmB,EAAA7D,SAAAC,MAAA,gBAIA8D,cAlGA,SAkGAlC,GAGA,IAFA,IAAAmC,EAAA,IAAAC,YAAApC,EAAAqC,QACAC,EAAA,IAAAC,WAAAJ,GACAK,EAAA,EAAAA,EAAAxC,EAAAqC,SAAAG,EACAF,EAAAE,GAAAxC,EAAAwC,GAEA,OAAAL,GAGAM,iBA3GA,SA2GAC,GAAA,IAAAC,EAAA3F,KAAA,OAAA+B,IAAAC,EAAAC,EAAAC,KAAA,SAAA0D,IAAA,IAAAC,EAAAC,EAAAC,EAAAhF,EAAA,OAAAiB,EAAAC,EAAAI,KAAA,SAAA2D,GAAA,cAAAA,EAAAzD,KAAAyD,EAAAxD,MAAA,cAAAwD,EAAAxD,KAAA,EACAC,OAAAY,EAAA,EAAAZ,CAAAiD,EAAAO,OAAA,cAAAP,EAAAQ,MADA,OACAL,EADAG,EAAAzC,KAEAuC,EAAAH,EAAAT,cAAA,IAAAL,EAAAsB,KAAAC,MAAAP,KACAE,EAAAL,EAAA5G,KACAiC,EAAA,IAAAsF,MAAA,IAAAA,MAAAP,MACAQ,EAAArE,EAAAsE,OAAAxF,EAAAgF,GALA,wBAAAC,EAAAlC,SAAA8B,EAAAD,KAAA5D,IAQAyE,SAnHA,WAoHAxG,KAAAC,UAAAD,KAAAX,WAAAP,KAAAkB,KAAAX,WAAAZ,OAGAgI,SAvHA,WAwHAzG,KAAA0G,QAAAC,KAAA,YAGAC,YA3HA,SA2HAC,GAAA,IAAAC,EAAA9G,KACAA,KAAAL,cAAA0F,OAAA,EACArF,KAAA+G,SAAA,gBACAC,kBAAA,KACAC,iBAAA,KACAhG,KAAA,YACA0B,KAAA,WACA,IAAAuE,KACAJ,EAAAnH,cAAAwH,QAAA,SAAAC,GACAF,EAAAP,KAAAS,EAAAC,QAEA,IAAAlE,EAAA,IAAAK,SACAL,EAAAM,OAAA,WAAAyD,GACUzE,OAAAC,EAAA,EAAAD,CAAVU,GAAAR,KAAA,SAAAG,GACA,KAAAA,EAAAY,MACAoD,EAAA7G,cAEA2D,MAAA,SAAAC,GACAiD,EAAA3F,SAAAC,MAAA,eAEAwC,MAAA,WACAkD,EAAA3F,SAAA,WAGAnB,KAAAmB,UACA1B,QAAA,kBACAwB,KAAA,aAKAqG,UA1JA,SA0JAC,GACAvH,KAAAL,cAAA4H,GAEAC,YA7JA,SA6JA7F,GACA3B,KAAAJ,WAAA,EACAI,KAAAE,MAAAyB,GAAA8F,cACAzH,KAAA2D,aAcAA,UA9KA,WA+KA3D,KAAAnB,OAAAJ,KAAA,GACAuB,KAAAnB,OAAAC,KAAA,GACAkB,KAAAnB,OAAAE,KAAA,GACAiB,KAAAnB,OAAAI,KAAA,GACAe,KAAApB,QAAA,GACAoB,KAAAnB,OAAAK,SAAA,yICjYewI,GADEC,OAFjB,WAA0B,IAAAC,EAAA5H,KAAa6H,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaxD,KAAA,UAAAyD,QAAA,YAAAC,MAAAP,EAAA,YAAAQ,WAAA,gBAAoFC,YAAA,oBAAgCN,EAAA,OAAYM,YAAA,gDAA0DN,EAAA,OAAYM,YAAA,mBAA6BN,EAAA,OAAYM,YAAA,sBAAgCN,EAAA,OAAYM,YAAA,oBAA8BN,EAAA,OAAYM,YAAA,SAAmBN,EAAA,WAAgBM,YAAA,mBAAAC,OAAsCC,QAAA,EAAAC,MAAAZ,EAAAvI,WAAAoJ,KAAA,YAAsDV,EAAA,gBAAAA,EAAA,aAAqCM,YAAA,YAAAC,OAA+BI,UAAA,GAAAC,YAAA,SAAqCH,OAAQL,MAAAP,EAAAvI,WAAA,KAAAuJ,SAAA,SAAAC,GAAqDjB,EAAAkB,KAAAlB,EAAAvI,WAAA,OAAAwJ,IAAsCT,WAAA,oBAA+BR,EAAAmB,GAAAnB,EAAA,cAAAoB,GAAkC,OAAAjB,EAAA,aAAuBkB,IAAAD,EAAArK,KAAA2J,OAAqBY,MAAAF,EAAAtK,KAAAyJ,MAAAa,EAAArK,UAAuC,OAAAiJ,EAAAuB,GAAA,KAAApB,EAAA,gBAAAA,EAAA,aAAwDO,OAAOrH,KAAA,UAAAmI,KAAA,kBAAyCC,IAAK5H,MAAAmG,EAAApB,YAAsBoB,EAAAuB,GAAA,gBAAAvB,EAAAuB,GAAA,KAAApB,EAAA,WAAmDM,YAAA,sBAAAC,OAAyCC,QAAA,EAAAE,KAAA,YAA+BV,EAAA,gBAAqBM,YAAA,OAAiBN,EAAA,aAAkBO,OAAOG,KAAA,SAAAW,KAAA,gBAAsCC,IAAK5H,MAAAmG,EAAAnB,YAAsBmB,EAAAuB,GAAA,kDAAAvB,EAAAuB,GAAA,KAAApB,EAAA,gBAA0FM,YAAA,OAAiBN,EAAA,aAAkBO,OAAOrH,KAAA,SAAAwH,KAAA,SAAAW,KAAA,wBAA8DC,IAAK5H,MAAAmG,EAAAhB,eAAyBgB,EAAAuB,GAAA,kDAAAvB,EAAAuB,GAAA,KAAApB,EAAA,gBAA0FM,YAAA,OAAiBN,EAAA,SAAcuB,IAAA,SAAAjB,YAAA,qDAAAC,OAAqFrH,KAAA,OAAAsI,OAAA,gBAAqC3B,EAAAuB,GAAA,KAAApB,EAAA,aAA8BO,OAAOrH,KAAA,UAAAmI,KAAA,kBAAAX,KAAA,UAA0DY,IAAK5H,MAAA,SAAA+H,GAAyB5B,EAAAhI,WAAA,MAAuBgI,EAAAuB,GAAA,0DAAAvB,EAAAuB,GAAA,KAAApB,EAAA,OAAyFM,YAAA,oCAA8CN,EAAA,OAAYM,YAAA,4BAAsCN,EAAA,YAAiBM,YAAA,wBAAAC,OAA2CrK,KAAA2J,EAAAzJ,OAAAsL,OAAA,GAAAC,oBAAA9B,EAAAvJ,gBAAAsL,OAAA/B,EAAApJ,YAAAoL,OAAA,IAA2GP,IAAKQ,mBAAAjC,EAAAN,aAAkCS,EAAA,mBAAwBO,OAAOrH,KAAA,YAAA6I,MAAA,KAAAC,MAAA,YAAkDnC,EAAAuB,GAAA,KAAApB,EAAA,mBAAoCO,OAAOrH,KAAA,QAAA6I,MAAA,KAAAZ,MAAA,KAAAa,MAAA,YAA2DnC,EAAAuB,GAAA,KAAApB,EAAA,mBAAoCO,OAAO0B,KAAA,OAAAd,MAAA,UAA8BtB,EAAAuB,GAAA,KAAApB,EAAA,mBAAoCO,OAAO0B,KAAA,OAAAd,MAAA,UAA8BtB,EAAAuB,GAAA,KAAApB,EAAA,mBAAoCO,OAAO0B,KAAA,KAAAd,MAAA,QAA0BtB,EAAAuB,GAAA,KAAApB,EAAA,mBAAoCO,OAAO0B,KAAA,SAAAd,MAAA,UAAgCtB,EAAAuB,GAAA,KAAApB,EAAA,mBAAoCO,OAAOY,MAAA,KAAAY,MAAA,OAA2BG,YAAArC,EAAAsC,KAAsBjB,IAAA,UAAAkB,GAAA,SAAAC,GAAkC,OAAArC,EAAA,aAAwBO,OAAOG,KAAA,SAAAxH,KAAA,QAA8BoI,IAAK5H,MAAA,SAAA+H,GAAyB,OAAA5B,EAAAnC,iBAAA2E,EAAA1E,SAA0CkC,EAAAuB,GAAA,sCAA4C,aAAAvB,EAAAuB,GAAA,KAAApB,EAAA,aAA4CM,YAAA,cAAAC,OAAiC+B,MAAA,OAAAC,IAAA,MAAAR,MAAA,QAAAS,QAAA3C,EAAAhI,UAAA4K,aAAA,IAAmFnB,IAAKoB,MAAA,SAAAjB,GAAyB,OAAA5B,EAAAJ,YAAA,aAAmCkD,iBAAA,SAAAlB,GAAmC5B,EAAAhI,UAAA4J,MAAuBzB,EAAA,WAAgBuB,IAAA,WAAAhB,OAAsBE,MAAAZ,EAAA/I,OAAAU,MAAAqI,EAAArI,MAAAoL,cAAA,QAAAlC,KAAA,UAA0EV,EAAA,gBAAqBO,OAAOY,MAAA,OAAAc,KAAA,UAA8BjC,EAAA,aAAkBM,YAAA,eAAAC,OAAkCK,YAAA,WAAwBH,OAAQL,MAAAP,EAAA/I,OAAA,KAAA+J,SAAA,SAAAC,GAAiDjB,EAAAkB,KAAAlB,EAAA/I,OAAA,OAAAgK,IAAkCT,WAAA,gBAA2BR,EAAAmB,GAAAnB,EAAA,cAAAoB,GAAkC,OAAAjB,EAAA,aAAuBkB,IAAAD,EAAArK,KAAA2J,OAAqBY,MAAAF,EAAAtK,KAAAyJ,MAAAa,EAAAtK,UAAuC,OAAAkJ,EAAAuB,GAAA,KAAApB,EAAA,gBAAwCM,YAAA,WAAAC,OAA8BY,MAAA,OAAAc,KAAA,UAA8BjC,EAAA,YAAiBM,YAAA,gBAAAC,OAAmCsC,SAAA,GAAAjC,YAAA,OAAAD,UAAA,IAAkDF,OAAQL,MAAAP,EAAA/I,OAAA,KAAA+J,SAAA,SAAAC,GAAiDjB,EAAAkB,KAAAlB,EAAA/I,OAAA,OAAAgK,IAAkCT,WAAA,iBAA2BR,EAAAuB,GAAA,KAAApB,EAAA,OAAwBM,YAAA,8BAAAgB,IAA8C5H,MAAAmG,EAAApG,cAAwBoG,EAAAuB,GAAA,YAAAvB,EAAAuB,GAAA,KAAApB,EAAA,gBAAoDM,YAAA,WAAAC,OAA8BY,MAAA,OAAAc,KAAA,UAA8BjC,EAAA,YAAiBO,OAAOK,YAAA,OAAAD,UAAA,IAAoCF,OAAQL,MAAAP,EAAA/I,OAAA,KAAA+J,SAAA,SAAAC,GAAiDjB,EAAAkB,KAAAlB,EAAA/I,OAAA,OAAAgK,IAAkCT,WAAA,kBAA2B,GAAAR,EAAAuB,GAAA,KAAApB,EAAA,gBAAqCM,YAAA,+BAAAC,OAAkDY,MAAA,SAAAc,KAAA,WAAiCjC,EAAA,aAAkBM,YAAA,kBAAAC,OAAqCuC,OAAA,IAAAD,SAAAhD,EAAAtI,cAAAwL,kBAAA,EAAAC,gBAAAnD,EAAA9G,mBAAAkK,eAAApD,EAAAvG,eAAwIuG,EAAA,QAAAG,EAAA,OAA0BM,YAAA,SAAAC,OAA4B2C,IAAArD,EAAAhJ,WAAmBmJ,EAAA,KAAUM,YAAA,sCAAgDT,EAAAuB,GAAA,KAAAvB,EAAA,cAAAG,EAAA,KAA0CM,YAAA,kBAA4BT,EAAAuB,GAAA,cAAAvB,EAAAsD,QAAA,GAAAtD,EAAAuB,GAAA,KAAApB,EAAA,gBAAqEM,YAAA,0BAAAC,OAA6CY,MAAA,OAAAc,KAAA,UAA8BjC,EAAA,YAAiBO,OAAOrH,KAAA,WAAAkK,UAA8BC,QAAA,EAAAC,QAAA,GAAwBC,UAAA,MAAAC,kBAAA,GAAA5C,YAAA,OAAAD,UAAA,IAA4EF,OAAQL,MAAAP,EAAA/I,OAAA,KAAA+J,SAAA,SAAAC,GAAiDjB,EAAAkB,KAAAlB,EAAA/I,OAAA,OAAAgK,IAAkCT,WAAA,kBAA2B,OAAAR,EAAAuB,GAAA,KAAApB,EAAA,QAAiCM,YAAA,gBAAAC,OAAmCkD,KAAA,UAAgBA,KAAA,WAAezD,EAAA,aAAkBO,OAAOrH,KAAA,WAAiBoI,IAAK5H,MAAA,SAAA+H,GAAyB,OAAA5B,EAAAlG,SAAA,gBAAkCkG,EAAAuB,GAAA,SAAAvB,EAAAuB,GAAA,KAAApB,EAAA,aAA8CO,OAAOrH,KAAA,WAAiBoI,IAAK5H,MAAA,SAAA+H,GAAyB,OAAA5B,EAAAJ,YAAA,gBAAqCI,EAAAuB,GAAA,0BAEhrLsC,oBCCjB,IASAC,EAZA,SAAAC,GACEC,EAAQ,SAgBVC,EAdyBD,EAAQ,OAcjCE,CACEC,EAAA,EACArE,GATF,EAWAgE,EAPA,kBAEA,MAUe5N,EAAA,QAAA+N,EAAiB", "file": "js/27.2f49ba34cb500d4bd02e.js", "sourcesContent": ["<template>\n  <div class=\"bg_con bgHeight\" v-loading=\"pageLoading\">\n    <div class=\"maxWidth maxHeight positionR overflowHidden\">\n      <div class=\"dabg maxHeight\">\n        <div class=\"content maxHeight\">\n          <div class=\"table maxHeight\">\n            <!-- 查询以及操作start -->\n            <div class=\"mhcx\">\n              <el-form :inline=\"true\" :model=\"formInline\" size=\"medium\" class=\"demo-form-inline\">\n                <el-form-item>\n                  <el-select v-model=\"formInline.wdlx\" clearable placeholder=\"请选择类型\" class=\"widthx fl\">\n                    <el-option v-for=\"item in wdlx\" :label=\"item.lxmc\" :value=\"item.lxid\" :key=\"item.lxid\"></el-option>\n                  </el-select>\n                </el-form-item>\n                <el-form-item>\n                  <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"onSubmit\">查询</el-button>\n                </el-form-item>\n              </el-form>\n              <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline fr\">\n                <el-form-item class=\"fr\">\n                  <el-button size=\"medium\" @click=\"returnSy\" icon=\"el-icon-back\">\n                    返回\n                  </el-button>\n                </el-form-item>\n                <el-form-item class=\"fr\">\n                  <el-button type=\"danger\" size=\"medium\" @click=\"deleteFiles\" icon=\"el-icon-delete-solid\">\n                    删除\n                  </el-button>\n                </el-form-item>\n                <el-form-item class=\"fr\">\n                  <input type=\"file\" ref=\"upload\" class=\"uploadClass hidden positionA opacity-0 cursorClick\"\n                    accept=\".docx,.pdf\">\n                  <el-button type=\"primary\" icon=\"el-icon-upload2\" size=\"medium\" @click=\"dr_dialog = true\">\n                    上传\n                  </el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n            <!-- 查询以及操作end -->\n            <!-- 文档列表start -->\n            <div class=\"table_content_padding maxHeight\">\n              <div class=\"table_content maxHeight\">\n                <el-table class=\"eltableClass maxWidth\" :data=\"wdList\" border @selection-change=\"selectRow\"\n                  :header-cell-style=\"headerCellStyle\" :height=\"tableHeight\" stripe>\n                  <el-table-column type=\"selection\" width=\"55\" align=\"center\"> </el-table-column>\n                  <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\n                  <el-table-column prop=\"wdmc\" label=\"文档名称\"></el-table-column>\n                  <el-table-column prop=\"wdlx\" label=\"文档类型\"></el-table-column>\n                  <el-table-column prop=\"ys\" label=\"页数\"></el-table-column>\n                  <el-table-column prop=\"wdscsj\" label=\"上传时间\"></el-table-column>\n                  <el-table-column label=\"操作\" width=\"120\">\n                    <template slot-scope=\"scoped\">\n                      <el-button size=\"medium\" type=\"text\" @click=\"downloadFileItem(scoped.row)\">下载\n                      </el-button>\n                    </template>\n                  </el-table-column>\n                </el-table>\n                <!-- <div style=\"border: 1px solid #ebeef5;\">\n                  <el-pagination background @current-change=\"handleCurrentChange\" @size-change=\"handleSizeChange\"\n                    :pager-count=\"5\" :current-page=\"page\" :page-sizes=\"[5, 10, 20, 30]\" :page-size=\"pageSize\"\n                    layout=\"total, prev, pager, sizes,next, jumper\" :total=\"total\">\n                  </el-pagination>\n                </div> -->\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 文档列表end -->\n        <!-- 上传start -->\n        <el-dialog title=\"文档上传\" class=\"scbg-dialog\" top=\"5vh\" width=\"600px\" @close=\"closeDialog('formName')\" :visible.sync=\"dr_dialog\"\n          show-close>\n          <el-form ref=\"formName\" :model=\"tjlist\" :rules=\"rules\" label-width=\"120px\" size=\"mini\">\n            <el-form-item label=\"文档类型\" prop=\"wdlx\">\n              <el-select v-model=\"tjlist.wdlx\" placeholder=\"请选择文档类型\" class=\"maxWidthCalc\">\n                <el-option v-for=\"item in wdlx\" :label=\"item.lxmc\" :value=\"item.lxmc\" :key=\"item.lxid\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"文档名称\" prop=\"wdmc\" class=\"one-line\">\n              <el-input class=\"fl inputWidth\" disabled placeholder=\"文档名称\" v-model=\"tjlist.wdmc\" clearable></el-input>\n              <div class=\"uploadButton fr cursorClick\" @click=\"chooseFile\">上传</div>\n            </el-form-item>\n            <el-form-item label=\"文档页数\" prop=\"wdys\" class=\"one-line\">\n              <el-input placeholder=\"文档页数\" v-model=\"tjlist.wdys\" clearable></el-input>\n            </el-form-item>\n            <el-form-item label=\"文档页缩略图\" prop=\"wdslt\" class=\"one-line picHeight positionR\">\n              <el-upload class=\"avatar-uploader\" action=\"#\" :disabled=\"uploadDisable\" :show-file-list=\"false\" :before-upload=\"beforeAvatarUpload\"\n                :http-request=\"httpRequest\">\n                <img v-if=\"sltshow\" :src=\"sltshow\" class=\"avatar\">\n                <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\n                <p v-if=\"uploadDisable\" class=\"fr zyposition\">注：需先上传文件</p>\n              </el-upload>\n            </el-form-item>\n            <el-form-item label=\"文档摘要\" prop=\"wdzy\" class=\"one-line textareaHeight\">\n              <el-input type=\"textarea\" :autosize=\"{ minRows: 6, maxRows: 6}\" maxlength=\"140\" show-word-limit placeholder=\"文档摘要\" v-model=\"tjlist.wdzy\" clearable></el-input>\n            </el-form-item>\n          </el-form>\n          <span slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitTj('formName')\">保 存</el-button>\n            <el-button type=\"warning\" @click=\"closeDialog('formName')\">关 闭</el-button>\n          </span>\n        </el-dialog>\n        <!-- 上传end -->\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport { getUuid,getSaveFile,getAllWdList,getDeleteFile } from '../../../api/bggl'\nimport { encryptAes,decryptAes } from '../../../utils/aesUtils' // 文件加密\nimport FileSaver from 'file-saver';\nexport default {\n  components: {},\n  props: {},\n  data() {\n    return {\n      pageLoading: false, // loading\n      wdList: [], // 文档列表数据\n      tableDataCopy: [], // 备份数据\n      // 改变了首行样式\n      headerCellStyle: {\n        background: '#EEF7FF', color: '#4D91F8'\n      },\n      // eltable高度\n      tableHeight: 'calc(100% - 34px - 41px - 3px)',\n      // 文档类型\n      wdlx: [\n        {\n          lxmc: '通用',\n          lxid: '1'\n        },\n        {\n          lxmc: '涉密信息系统集成资质',\n          lxid: '2'\n        },\n        {\n          lxmc: '武器装备科研生产单位资格',\n          lxid: '3'\n        },\n        {\n          lxmc: '国家秘密载体印刷资质',\n          lxid: '4'\n        }\n      ],\n      sltshow: '', // 文档的缩略图显示\n      // 上传文档信息\n      tjlist: {\n        wdlx: '',\n        wdmc: '',\n        wdys: '',\n        wdslt: '',\n        wdzy: '',\n        wdBuffer: null\n      },\n      page: 1, // 当前页\n      pageSize: 10, // 每页条数\n      formInline: {\n        wdmc:'',\n        wdlx:''\n      }, // 查询条件\n      uploadDisable: true,\n      //表单验证\n      rules: {\n        wdlx: [{\n          required: true,\n          message: '请输入文档类型',\n          trigger: 'blur'\n        }],\n        wdmc: [{\n          required: true,\n          message: '请上传文件',\n          trigger: 'blur'\n        }],\n        wdys: [{\n          required: true,\n          message: '请输入文档总页数',\n          trigger: 'blur'\n        }],\n        wdslt: [{\n          required: true,\n          message: '请上传文档缩略图',\n          trigger: 'blur'\n        }]\n      },\n      selectlistRow: [], //列表的值\n      dr_dialog: false, // 上传弹框\n    }\n  },\n  computed: {},\n  mounted() {\n    //列表初始化\n    this.getWdList(this.formInline.wdmc,this.formInline.wdlx)\n    //绑定监听表格导入事件\n    this.$refs.upload.addEventListener('change', e => {\n      this.readExcel(e);\n    })\n  },\n  // 监听\n  watch:{\n    'tjlist.wdmc':{ //监听的对象\n        deep:true, //深度监听设置为 true\n        handler:function(newV,oldV){\n          if(newV == ''){\n            this.uploadDisable = true\n          }\n        }\n    }\n  },\n  methods: {\n    // 上传的缩略图格式限定jpg/png\n    beforeAvatarUpload(file) {\n      const isJPG = file.type === 'image/jpeg';\n      const isPNG = file.type === 'image/png';\n      if (!isJPG && !isPNG) {\n        this.$message.error('上传缩略图只能是 JPG/PNG 格式!');\n      }\n      return isJPG || isPNG;\n    },\n    // 不用action\n    httpRequest(data) {\n      this.sltshow = URL.createObjectURL(data.file);\n      this.tjlist.wdslt = data.file\n    },\n    //导入\n    chooseFile() {\n      this.$refs.upload.click()\n    },\n    // 上传文件确定\n    submitTj(formName) {\n      this.pageLoading = true\n      this.$refs[formName].validate(async (valid) => {\n        if (valid) {\n          getUuid().then(async res => {\n            let keyStr = 'hsoftBanner' + res\n            let buf = this.tjlist.wdBuffer\n            let encryptStr\n            encryptStr = await encryptAes(JSON.stringify(buf), keyStr)\n            let wdlxNumber =  this.tjlist.wdlx == '通用' ? 1 : this.tjlist.wdlx == '涉密信息系统集成资质' ? 2 : this.tjlist.wdlx == '武器装备科研生产单位资格' ? 3 : this.tjlist.wdlx == '国家秘密载体印刷资质' ? 4 : 0\n            let formData = new FormData()\n            formData.append('uuid', res)\n            formData.append('wdlx', wdlxNumber)\n            formData.append('wdmc', this.tjlist.wdmc)\n            formData.append('ys', this.tjlist.wdys)\n            formData.append('wdsltFile', this.tjlist.wdslt)\n            formData.append('wdzy', this.tjlist.wdzy)\n            formData.append('wdFile', encryptStr)\n            this.dr_dialog = false\n            // 添加文档信息\n            getSaveFile(formData).then(res => {\n              if(res.code == 10000){\n                this.pageLoading = false\n                this.$message({\n                  message: '添加成功',\n                  type: 'success'\n                });\n                this.resetForm()\n                this.getWdList(this.formInline.wdmc,this.formInline.wdlx)\n              }\n            }).catch(err => {\n              this.$message.error('文档添加失败！')\n            })\n          }).catch(err => {\n            this.$message.error('获取Uuid失败！')\n          })\n        } else if (!valid) {\n          this.$message.error('请将文件信息填写完整');\n          this.resetForm()\n          this.dr_dialog = false\n          this.pageLoading = false\n          return false;\n        }\n      })\n    },\n    //----表格导入方法\n    readExcel(e) {\n      const file = e.target.files;\n      const fr = new FileReader();\n      this.tjlist.wdmc = file[0].name\n      fr.readAsArrayBuffer(file[0])\n      fr.onload = (e) => {\n        // 文件的ArrayBuffer结果\n        const buffer = Buffer.from(e.target.result)\n        // let jsonBuffer = JSON.stringify(buffer, null, 2)\n        this.tjlist.wdBuffer = buffer\n        this.uploadDisable = false\n      }\n    },\n    // 获取文档列表数据\n    getWdList(wdmc,wdlx) {\n      this.pageLoading = true\n      // 获取文档信息\n      let params = {\n        wdmc,\n        wdlx\n      }\n      getAllWdList(params).then(res => {\n        if(res.code == 10000){\n          this.wdList = res.data\n          this.tableDataCopy = res\n          this.pageLoading = false\n        }\n      }).catch(err => {\n        this.$message.error('文档列表获取失败！')\n      })\n    },\n    // 将buffer转换成ArrayBuffer\n    toArrayBuffer(buf) {\n      var ab = new ArrayBuffer(buf.length);\n      var view = new Uint8Array(ab);\n      for (var i = 0; i < buf.length; ++i) {\n        view[i] = buf[i];\n      }\n      return ab;\n    },\n    // 下载文件\n    async downloadFileItem(row) {\n      let jmdatas = await decryptAes(row.wdFile, 'hsoftBanner' + row.uuid)\n      let bufferFile = this.toArrayBuffer(new Buffer(JSON.parse(jmdatas)))\n      let fileName = row.wdmc\n      var file = new Blob([new Blob([bufferFile])])\n      FileSaver.saveAs(file,fileName);\n    },\n    //查询\n    onSubmit() {\n      this.getWdList(this.formInline.wdmc,this.formInline.wdlx)\n    },\n    // 返回报告管理列表页\n    returnSy() {\n      this.$router.push(\"/bgglsy\");\n    },\n    //删除\n    deleteFiles(id) {\n      if (this.selectlistRow.length > 0) {\n        this.$confirm('是否继续删除?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let bgidList = []\n          this.selectlistRow.forEach((sel)=>{\n            bgidList.push(sel.bgid)\n          })\n          let formData = new FormData()\n          formData.append('bgidList', bgidList)\n          getDeleteFile(formData).then(res => {\n              if(res.code == 10000){\n                this.getWdList()\n              }\n          }).catch(err => {\n              this.$message.error('文档删除失败！')\n          })\n        }).catch(() => {\n          this.$message('已取消删除')\n        })\n      } else {\n        this.$message({\n          message: '未选择删除记录，请选择下列列表',\n          type: 'warning'\n        });\n      }\n    },\n    // 复选框\n    selectRow(val) {\n      this.selectlistRow = val;\n    },\n    closeDialog(formName){\n      this.dr_dialog = false\n      this.$refs[formName].resetFields();\n      this.resetForm()\n    },\n    //列表分页--跳转页数\n    // handleCurrentChange(val) {\n    //   this.page = val\n    //   this.getWdList()\n    // },\n    //列表分页--更改每页显示个数\n    // handleSizeChange(val) {\n    //   this.page = 1\n    //   this.pageSize = val\n    //   this.getWdList()\n    // },\n    //重置\n    resetForm() {\n      this.tjlist.wdlx = ''\n      this.tjlist.wdmc = ''\n      this.tjlist.wdys = ''\n      this.tjlist.wdzy = ''\n      this.sltshow = ''\n      this.tjlist.wdBuffer = null\n    }\n  }\n}\n</script>\n\n<style scoped>\n.bgHeight {\n  height: calc(100% - 38px);\n}\n.uploadClass {\n  top: 10px;\n  right: 0;\n  height: 32px;\n  width: 56px;\n  z-index: 1;\n}\n\n.eltableClass {\n  border: 1px solid #EBEEF5;\n}\n\n.uploadButton {\n  background: #409EFF;\n  border-radius: 5px;\n  padding: 0px 10px;\n  color: #ffffff;\n}\n\n.inputWidth {\n  width: 85%;\n}\n\n.maxWidthCalc {\n  width: calc(100% - 120px);\n}\n\n.bg_con /deep/ .el-dialog__body .el-form>div .el-form-item__label {\n  border: 0px;\n  border-right: 1px solid rgba(235, 235, 235, 1);\n}\n\n.bg_con /deep/ .el-form-item__content {\n  width: 100% !important;\n}\n.picHeight {\n  height: 210px !important;\n}\n.textareaHeight {\n  height: 150px !important;\n}\n.bg_con /deep/.avatar-uploader-icon {\n  border: 2px solid #EBEBEB!important;\n}\n/deep/ .el-dialog__body .el-form .el-form-item__error {\n  bottom: -10px;\n}\n.avatar-uploader .el-upload {\n  border: 1px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n}\n.avatar-uploader .el-upload:hover {\n  border-color: #409EFF;\n}\n.avatar-uploader-icon {\n  font-size: 28px;\n  color: #8c939d;\n  width: 178px;\n  height: 178px;\n  line-height: 178px;\n  text-align: center;\n  border: 2px solid #EBEBEB!important;\n}\n.avatar {\n  width: 178px;\n  height: 178px;\n  display: block;\n}\n.bg_con {\n  width: 100%;\n}\n.dabg {\n  /* margin-top: 10px; */\n  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);\n  border-radius: 8px;\n  width: 100%;\n}\n.item_button {\n  height: 100%;\n  float: left;\n  padding-left: 10px;\n  line-height: 50px;\n}\n.widthx {\n  width: 8vw;\n}\n/deep/.mhcx .el-form-item {\n  /* margin-top: 5px; */\n  margin-bottom: 5px;\n}\n.dialog-footer {\n  display: block;\n  margin-top: 10px;\n}\n.zyposition {\n  color: gray!important;\n  color: gray!important;\n  position: absolute;\n  bottom: 10px;\n  left: 200px;\n}\n</style>\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/bggl/bgglgl.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.pageLoading),expression:\"pageLoading\"}],staticClass:\"bg_con bgHeight\"},[_c('div',{staticClass:\"maxWidth maxHeight positionR overflowHidden\"},[_c('div',{staticClass:\"dabg maxHeight\"},[_c('div',{staticClass:\"content maxHeight\"},[_c('div',{staticClass:\"table maxHeight\"},[_c('div',{staticClass:\"mhcx\"},[_c('el-form',{staticClass:\"demo-form-inline\",attrs:{\"inline\":true,\"model\":_vm.formInline,\"size\":\"medium\"}},[_c('el-form-item',[_c('el-select',{staticClass:\"widthx fl\",attrs:{\"clearable\":\"\",\"placeholder\":\"请选择类型\"},model:{value:(_vm.formInline.wdlx),callback:function ($$v) {_vm.$set(_vm.formInline, \"wdlx\", $$v)},expression:\"formInline.wdlx\"}},_vm._l((_vm.wdlx),function(item){return _c('el-option',{key:item.lxid,attrs:{\"label\":item.lxmc,\"value\":item.lxid}})}),1)],1),_vm._v(\" \"),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"查询\")])],1)],1),_vm._v(\" \"),_c('el-form',{staticClass:\"demo-form-inline fr\",attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticClass:\"fr\"},[_c('el-button',{attrs:{\"size\":\"medium\",\"icon\":\"el-icon-back\"},on:{\"click\":_vm.returnSy}},[_vm._v(\"\\n                  返回\\n                \")])],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"fr\"},[_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"medium\",\"icon\":\"el-icon-delete-solid\"},on:{\"click\":_vm.deleteFiles}},[_vm._v(\"\\n                  删除\\n                \")])],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"fr\"},[_c('input',{ref:\"upload\",staticClass:\"uploadClass hidden positionA opacity-0 cursorClick\",attrs:{\"type\":\"file\",\"accept\":\".docx,.pdf\"}}),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-upload2\",\"size\":\"medium\"},on:{\"click\":function($event){_vm.dr_dialog = true}}},[_vm._v(\"\\n                  上传\\n                \")])],1)],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"table_content_padding maxHeight\"},[_c('div',{staticClass:\"table_content maxHeight\"},[_c('el-table',{staticClass:\"eltableClass maxWidth\",attrs:{\"data\":_vm.wdList,\"border\":\"\",\"header-cell-style\":_vm.headerCellStyle,\"height\":_vm.tableHeight,\"stripe\":\"\"},on:{\"selection-change\":_vm.selectRow}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"wdmc\",\"label\":\"文档名称\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"wdlx\",\"label\":\"文档类型\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"ys\",\"label\":\"页数\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"wdscsj\",\"label\":\"上传时间\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scoped){return [_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.downloadFileItem(scoped.row)}}},[_vm._v(\"下载\\n                    \")])]}}])})],1)],1)])])]),_vm._v(\" \"),_c('el-dialog',{staticClass:\"scbg-dialog\",attrs:{\"title\":\"文档上传\",\"top\":\"5vh\",\"width\":\"600px\",\"visible\":_vm.dr_dialog,\"show-close\":\"\"},on:{\"close\":function($event){return _vm.closeDialog('formName')},\"update:visible\":function($event){_vm.dr_dialog=$event}}},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"rules\":_vm.rules,\"label-width\":\"120px\",\"size\":\"mini\"}},[_c('el-form-item',{attrs:{\"label\":\"文档类型\",\"prop\":\"wdlx\"}},[_c('el-select',{staticClass:\"maxWidthCalc\",attrs:{\"placeholder\":\"请选择文档类型\"},model:{value:(_vm.tjlist.wdlx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"wdlx\", $$v)},expression:\"tjlist.wdlx\"}},_vm._l((_vm.wdlx),function(item){return _c('el-option',{key:item.lxid,attrs:{\"label\":item.lxmc,\"value\":item.lxmc}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"文档名称\",\"prop\":\"wdmc\"}},[_c('el-input',{staticClass:\"fl inputWidth\",attrs:{\"disabled\":\"\",\"placeholder\":\"文档名称\",\"clearable\":\"\"},model:{value:(_vm.tjlist.wdmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"wdmc\", $$v)},expression:\"tjlist.wdmc\"}}),_vm._v(\" \"),_c('div',{staticClass:\"uploadButton fr cursorClick\",on:{\"click\":_vm.chooseFile}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"文档页数\",\"prop\":\"wdys\"}},[_c('el-input',{attrs:{\"placeholder\":\"文档页数\",\"clearable\":\"\"},model:{value:(_vm.tjlist.wdys),callback:function ($$v) {_vm.$set(_vm.tjlist, \"wdys\", $$v)},expression:\"tjlist.wdys\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line picHeight positionR\",attrs:{\"label\":\"文档页缩略图\",\"prop\":\"wdslt\"}},[_c('el-upload',{staticClass:\"avatar-uploader\",attrs:{\"action\":\"#\",\"disabled\":_vm.uploadDisable,\"show-file-list\":false,\"before-upload\":_vm.beforeAvatarUpload,\"http-request\":_vm.httpRequest}},[(_vm.sltshow)?_c('img',{staticClass:\"avatar\",attrs:{\"src\":_vm.sltshow}}):_c('i',{staticClass:\"el-icon-plus avatar-uploader-icon\"}),_vm._v(\" \"),(_vm.uploadDisable)?_c('p',{staticClass:\"fr zyposition\"},[_vm._v(\"注：需先上传文件\")]):_vm._e()])],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line textareaHeight\",attrs:{\"label\":\"文档摘要\",\"prop\":\"wdzy\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"autosize\":{ minRows: 6, maxRows: 6},\"maxlength\":\"140\",\"show-word-limit\":\"\",\"placeholder\":\"文档摘要\",\"clearable\":\"\"},model:{value:(_vm.tjlist.wdzy),callback:function ($$v) {_vm.$set(_vm.tjlist, \"wdzy\", $$v)},expression:\"tjlist.wdzy\"}})],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.submitTj('formName')}}},[_vm._v(\"保 存\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\"},on:{\"click\":function($event){return _vm.closeDialog('formName')}}},[_vm._v(\"关 闭\")])],1)],1)],1)])])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-8478b1fa\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/bggl/bgglgl.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-8478b1fa\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./bgglgl.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./bgglgl.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./bgglgl.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-8478b1fa\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./bgglgl.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-8478b1fa\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/bggl/bgglgl.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}