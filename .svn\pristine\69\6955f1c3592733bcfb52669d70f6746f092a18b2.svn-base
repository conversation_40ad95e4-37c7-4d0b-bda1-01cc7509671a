{"version": 3, "sources": ["webpack:///src/renderer/view/zczp/xjzczp.vue", "webpack:///./src/renderer/view/zczp/xjzczp.vue?6c9b", "webpack:///./src/renderer/view/zczp/xjzczp.vue"], "names": ["xjzczp", "data", "dialogVisibleXjzczpRw", "dialogObj", "jcjdList", "dxdfArr", "showDxList", "spanArr", "reverse", "activities", "showMdmenu", "newArray", "dwxx", "rwid", "zt", "computed", "syxmzf", "zf", "this", "for<PERSON>ach", "dx", "xx", "sfsynr", "fz", "syxmdf", "df", "kffs", "jsqkf", "checked", "gdkffz", "syxmbfb", "num", "Math", "round", "dxdf", "resDf", "components", "methods", "getJcjdList", "date", "Date", "year", "getFullYear", "resObj", "currentQuarter", "resList", "jcjdid", "jcjdmc", "getMonth", "console", "log", "getDwxxList", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "Object", "dwzc", "sent", "stop", "getDxxxList", "_this2", "_callee3", "newArr", "_context3", "zczp", "code", "promise_default", "all", "map", "_ref", "_callee2", "item", "params", "xxnrlist", "xxlist", "_context2", "dxid", "JSON", "parse", "stringify_default", "item1", "xxid", "xxmc", "abrupt", "_x", "apply", "arguments", "getSpanArr", "getDxxxlssj", "_this3", "_callee5", "_context5", "_ref2", "_callee4", "_context4", "_x2", "closeXjzczpDialog", "createZczpRw", "_this4", "_callee6", "_context6", "rwmc", "dwmc", "dwid", "jcjd", "jczt", "$message", "message", "type", "saveToNext", "dwjcRK", "save", "checkXjzczpRwParams", "jcid", "dwpfjlList", "_this5", "_callee7", "del", "list", "_zt", "_zt2", "_context7", "warning", "nr", "push", "$router", "path", "query", "getDxXxIndex", "nrid", "spanArr2", "index", "length", "i", "pos", "sa", "objectSpanMethod", "_ref3", "row", "column", "rowIndex", "columnIndex", "rowspan", "scid", "colspan", "handleKfjsq", "handCheckdx", "handCheckbox", "handleSynrTextarea", "handleKfsmTextarea", "getZD", "_this6", "_callee8", "_context8", "getDwpfjlLsxxByRwid", "newScrw", "undefined", "mouseoverMdMenu", "mouseoutMenu", "handleJcjdChanged", "val", "watch", "mounted", "$route", "zczp_xjzczp", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "width", "height", "staticClass", "float", "attrs", "inline", "size", "_v", "_s", "icon", "on", "click", "clear", "_l", "key", "margin-bottom", "id", "slot", "dxmc", "span-method", "header-cell-style", "text-align", "border", "prop", "label", "scopedSlots", "_u", "fn", "scope", "$index", "mdIndex", "scnr", "align", "model", "value", "callback", "$$v", "$set", "expression", "name", "change", "$event", "_e", "margin-right", "min", "zdkffz", "max", "zgkffz", "step", "kfzf", "rows", "input", "trim", "kfbz", "color", "display", "font-weight", "margin-left", "list-style", "title", "visible", "update:visible", "ref", "label-width", "clearable", "placeholder", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "4OAiNAA,GACAC,KADA,WAEA,OAEAC,uBAAA,EACAC,aAMAC,YAIAC,WAEAC,cAEAC,WAEAC,SAAA,EAEAC,cAEAC,YAAA,EACAC,YACAC,QACAC,KAAA,GACAC,GAAA,KAGAC,UAEAC,OAFA,WAGA,IAAAC,EAAA,EAQA,OAPAC,KAAAP,SAAAQ,QAAA,SAAAC,GACAA,EAAAC,GAAAF,QAAA,SAAAE,GACAA,EAAAC,SACAL,GAAAI,EAAAE,QAIAN,GAGAO,OAdA,WAeA,IAAAC,EAAA,EAiBA,OAhBAP,KAAAP,SAAAQ,QAAA,SAAAC,GACAA,EAAAC,GAAAF,QAAA,SAAAE,GACAA,EAAAC,SAEA,GAAAD,EAAAK,KACAD,GAAAJ,EAAAE,GAAAF,EAAAM,MACA,GAAAN,EAAAK,OACA,GAAAL,EAAAO,QACAH,GAAAJ,EAAAE,GAAA,EAEAE,GAAAJ,EAAAE,GAAAF,EAAAQ,aAMAJ,GAGAK,QAnCA,WAoCA,MAAAZ,KAAAF,OACA,SAEA,IAAAe,EAAAb,KAAAM,OAAAN,KAAAF,OACA,OAAAgB,KAAAC,MAAA,IAAAF,GAAA,IAGAG,KA3CA,WA4CA,gBAAAd,GACA,IAAAe,EAAA,EAYA,OAXAf,EAAAC,GAAAF,QAAA,SAAAE,GACA,GAAAA,EAAAK,KACAS,GAAAd,EAAAE,GAAAF,EAAAM,MACA,GAAAN,EAAAK,OACA,GAAAL,EAAAO,QACAO,GAAAd,EAAAE,GAAA,EAEAY,GAAAd,EAAAE,GAAAF,EAAAQ,UAIAM,KAIAC,cAEAC,SASAC,YATA,WAUA,IAAAC,EAAA,IAAAC,KACAC,EAAAF,EAAAG,cAGAC,GACAC,eAAA,EACAC,UAEAC,OAAA,EACAC,OAAAN,EAAA,UAGAK,OAAA,EACAC,OAAAN,EAAA,UAGAK,OAAA,EACAC,OAAAN,EAAA,UAGAK,OAAA,EACAC,OAAAN,EAAA,WAIA,OAvBAF,EAAAS,WAAA,GAwBA,OAGA,OAGA,OACAL,EAAAC,eAAA,EACA,MACA,OAGA,OAGA,OACAD,EAAAC,eAAA,EACA,MACA,OAGA,OAGA,OACAD,EAAAC,eAAA,EACA,MACA,QAGA,QAGA,QACAD,EAAAC,eAAA,EAGAK,QAAAC,IAAA,2BAAAP,GACAzB,KAAAf,UAAA2C,OAAAH,EAAAC,eACA1B,KAAAd,SAAAuC,EAAAE,SAGAM,YA9EA,WA8EA,IAAAC,EAAAlC,KAAA,OAAAmC,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAxD,EAAA,OAAAqD,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACA7D,EADA0D,EAAAK,KAEAf,QAAAC,IAAAjD,GACAmD,EAAAxC,KAAAX,EAHA,wBAAA0D,EAAAM,SAAAR,EAAAL,KAAAC,IAMAa,YApFA,WAoFA,IAAAC,EAAAjD,KAAA,OAAAmC,IAAAC,EAAAC,EAAAC,KAAA,SAAAY,IAAA,IAAAnE,EAAAoE,EAAA,OAAAf,EAAAC,EAAAG,KAAA,SAAAY,GAAA,cAAAA,EAAAV,KAAAU,EAAAT,MAAA,cAAAS,EAAAT,KAAA,EACAC,OAAAS,EAAA,EAAAT,GADA,UAEA,MADA7D,EADAqE,EAAAN,MAEAQ,KAFA,CAAAF,EAAAT,KAAA,gBAGAM,EAAA7D,WAAAL,OAHAqE,EAAAT,KAAA,EAIAY,EAAAlB,EAAAmB,IACAP,EAAA7D,WAAAqE,IAAA,eAAAC,EAAAvB,IAAAC,EAAAC,EAAAC,KAAA,SAAAqB,EAAAC,GAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAA3B,EAAAC,EAAAG,KAAA,SAAAwB,GAAA,cAAAA,EAAAtB,KAAAsB,EAAArB,MAAA,cACAkB,GACAI,KAAAL,EAAAK,MAFAD,EAAArB,KAAA,EAIAC,OAAAS,EAAA,EAAAT,CAAAiB,GAJA,cAIAC,EAJAE,EAAAlB,KAKAc,EAAAzD,GAAA+D,KAAAC,MAAAC,IAAAN,IALAE,EAAArB,KAAA,EAMAC,OAAAS,EAAA,EAAAT,GANA,cAMAmB,EANAC,EAAAlB,KAOAc,EAAAzD,GAAAsD,IAAA,SAAAG,GACAG,EAAAN,IAAA,SAAAY,GACAA,EAAAC,MAAAV,EAAAU,OACAV,EAAAW,KAAAF,EAAAE,UAVAP,EAAAQ,OAAA,SAcAZ,GAdA,yBAAAI,EAAAjB,SAAAY,EAAAV,MAAA,gBAAAwB,GAAA,OAAAf,EAAAgB,MAAA1E,KAAA2E,YAAA,KALA,OAIAxB,EAJAC,EAAAN,KAsBAG,EAAAxD,SAAAyE,KAAAC,MAAAC,IAAAjB,IACAF,EAAA5D,QAAA4D,EAAA2B,WAAA3B,EAAAxD,UACAsC,QAAAC,IAAAiB,EAAAxD,UAxBA,yBAAA2D,EAAAL,SAAAG,EAAAD,KAAAd,IA4BA0C,YAhHA,WAgHA,IAAAC,EAAA9E,KAAA,OAAAmC,IAAAC,EAAAC,EAAAC,KAAA,SAAAyC,IAAA,IAAAhG,EAAAoE,EAAA,OAAAf,EAAAC,EAAAG,KAAA,SAAAwC,GAAA,cAAAA,EAAAtC,KAAAsC,EAAArC,MAAA,cAAAqC,EAAArC,KAAA,EACAC,OAAAS,EAAA,EAAAT,GADA,UAEA,MADA7D,EADAiG,EAAAlC,MAEAQ,KAFA,CAAA0B,EAAArC,KAAA,gBAGAmC,EAAA1F,WAAAL,OAHAiG,EAAArC,KAAA,EAIAY,EAAAlB,EAAAmB,IACAsB,EAAA1F,WAAAqE,IAAA,eAAAwB,EAAA9C,IAAAC,EAAAC,EAAAC,KAAA,SAAA4C,EAAAtB,GAAA,IAAAC,EAAAC,EAAA,OAAA1B,EAAAC,EAAAG,KAAA,SAAA2C,GAAA,cAAAA,EAAAzC,KAAAyC,EAAAxC,MAAA,cACAkB,GACAI,KAAAL,EAAAK,KACAtE,KAAAmF,EAAAnF,MAHAwF,EAAAxC,KAAA,EAMAC,OAAAS,EAAA,EAAAT,CAAAiB,GANA,cAMAC,EANAqB,EAAArC,KAOAc,EAAAzD,GAAA+D,KAAAC,MAAAC,IAAAN,EAAA/E,OAPAoG,EAAAX,OAAA,SAgBAZ,GAhBA,wBAAAuB,EAAApC,SAAAmC,EAAAJ,MAAA,gBAAAM,GAAA,OAAAH,EAAAP,MAAA1E,KAAA2E,YAAA,KALA,OAIAxB,EAJA6B,EAAAlC,KAwBAgC,EAAArF,SAAAyE,KAAAC,MAAAC,IAAAjB,IACApB,QAAAC,IAAA8C,EAAArF,UACAqF,EAAAzF,QAAAyF,EAAAF,WAAAE,EAAArF,UA1BA,yBAAAuF,EAAAjC,SAAAgC,EAAAD,KAAA3C,IA8BAkD,kBA9IA,WAgJArF,KAAAhB,uBAAA,GAGAsG,aAnJA,WAmJA,IAAAC,EAAAvF,KAAA,OAAAmC,IAAAC,EAAAC,EAAAC,KAAA,SAAAkD,IAAA,IAAA3B,EAAA9E,EAAA,OAAAqD,EAAAC,EAAAG,KAAA,SAAAiD,GAAA,cAAAA,EAAA/C,KAAA+C,EAAA9C,MAAA,cACAkB,GACA6B,KAAAH,EAAAtG,UAAAyG,KACAC,KAAAJ,EAAA7F,KAAAiG,KACAC,KAAAL,EAAA7F,KAAAkG,KACAC,KAAAN,EAAAtG,UAAA4C,OACAiE,KAAA,UANAL,EAAA9C,KAAA,EAQAC,OAAAS,EAAA,EAAAT,CAAAiB,GARA,OASA,MADA9E,EARA0G,EAAA3C,MASAQ,OACAiC,EAAAQ,UACAC,QAAA,cACAC,KAAA,YAEAV,EAAAvG,uBAAA,EACAuG,EAAA5F,KAAAZ,QAfA,wBAAA0G,EAAA1C,SAAAyC,EAAAD,KAAApD,IAmBA+D,WAtKA,WAuKAlG,KAAAmG,OAAAnG,KAAAP,SAAA,IAGA2G,KA1KA,WA2KApG,KAAAmG,OAAAnG,KAAAP,SAAA,IAGA4G,oBA9KA,WA+KA,IAAAV,EAAA3F,KAAAf,UAAAyG,KACAY,EAAAtG,KAAAf,UAAA4C,OACA,GAAA8D,GAAAW,EACA,UAOAH,OAzLA,SAyLAI,EAAA3G,GAAA,IAAA4G,EAAAxG,KAAA,OAAAmC,IAAAC,EAAAC,EAAAC,KAAA,SAAAmE,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAzE,EAAAC,EAAAG,KAAA,SAAAsE,GAAA,cAAAA,EAAApE,KAAAoE,EAAAnE,MAAA,UACA6D,EAAAH,sBADA,CAAAS,EAAAnE,KAAA,eAEA6D,EAAAT,SAAAgB,QAAA,iBACAP,EAAAxH,uBAAA,EAHA8H,EAAAtC,OAAA,wBAMAkC,GACA/G,KAAA6G,EAAA7G,MAPAmH,EAAAnE,KAAA,EASAC,OAAAS,EAAA,EAAAT,CAAA8D,GATA,UAUA,KAVAI,EAAAhE,KAUAQ,KAVA,CAAAwD,EAAAnE,KAAA,gBAWAZ,QAAAC,IAAAuE,GACAxE,QAAAC,IAAApC,GAEA+G,KACAJ,EAAAtG,QAAA,SAAA2D,GACAA,EAAAzD,GAAAF,QAAA,SAAA+G,GACAA,EAAApB,KAAAY,EAAA9G,KAAAkG,KACAoB,EAAArH,KAAA6G,EAAA7G,KACAgH,EAAAM,KAAAD,GACAjF,QAAAC,IAAAgF,OApBAF,EAAAnE,KAAA,GAwBAC,OAAAS,EAAA,EAAAT,CAAA+D,GAxBA,WAyBA,KAzBAG,EAAAhE,KAyBAQ,KAzBA,CAAAwD,EAAAnE,KAAA,YA0BA,GAAA/C,EA1BA,CAAAkH,EAAAnE,KAAA,gBA2BAiE,GACAjH,KAAA6G,EAAA7G,KACAmG,KAAA,eACAvF,GAAAiG,EAAAlG,QA9BAwG,EAAAnE,KAAA,GAgCAC,OAAAS,EAAA,EAAAT,CAAAgE,GAhCA,QAAAE,EAAAhE,KAiCAQ,MACAkD,EAAAT,UACAC,QAAA,UACAC,KAAA,YApCAa,EAAAnE,KAAA,oBAuCA,GAAA/C,EAvCA,CAAAkH,EAAAnE,KAAA,gBAwCAkE,GACAlH,KAAA6G,EAAA7G,KACAmG,KAAA,eACAvF,GAAAiG,EAAAlG,QA3CAwG,EAAAnE,KAAA,GA6CAC,OAAAS,EAAA,EAAAT,CAAAiE,GA7CA,QAAAC,EAAAhE,KA8CAQ,OACAkD,EAAAT,UACAC,QAAA,QACAC,KAAA,YAEAO,EAAAU,QAAAD,MACAE,KAAA,WACAC,OACAzH,KAAA6G,EAAA7G,KACA+F,KAAAc,EAAAvH,UAAAyG,KACAG,KAAAW,EAAAvH,UAAA4C,WAxDA,yBAAAiF,EAAA/D,SAAA0D,EAAAD,KAAArE,IAiEAkF,aA1PA,SA0PAC,KAIA1C,WA9PA,SA8PA+B,GAEA,IADA,IAAAY,KACAC,EAAA,EAAAA,EAAAb,EAAAc,OAAAD,IAAA,CAGA,IAFA,IAAAzI,EAAA4H,EAAAa,GAAArH,GACAd,KACAqI,EAAA,EAAAA,EAAA3I,EAAA0I,OAAAC,IACA,IAAAA,GACArI,EAAA4H,KAAA,GACAjH,KAAA2H,IAAA,GAGA5I,EAAA2I,GAAApD,MAAAvF,EAAA2I,EAAA,GAAApD,MACAjF,EAAAW,KAAA2H,MAAA,EACAtI,EAAA4H,KAAA,KAEA5H,EAAA4H,KAAA,GACAjH,KAAA2H,IAAAD,GAIArI,EAAAY,QAAA,SAAA2H,GACAL,EAAAN,KAAAW,KAGA,OAAAL,GAEAM,iBAxRA,SAAAC,GAwRA,IAAAC,EAAAD,EAAAC,IAAAD,EAAAE,OAAAF,EAAAG,SAEA,OAFAH,EAAAI,YAKA,OACAC,QAFAnI,KAAAX,QAAA0I,EAAAK,KAAA,GAGAC,QAAA,IAKAC,YApSA,SAoSAnI,GACA4B,QAAAC,IAAA7B,GACAA,EAAAO,SAAA,EACAP,EAAAI,GAAAJ,EAAAE,GAAAF,EAAAM,OAEA8H,YAzSA,SAySApI,GACA4B,QAAAC,IAAA7B,GACA,GAAAA,EAAAO,QACAP,EAAAI,GAAAJ,EAAAE,GAAAF,EAAAM,MACA,GAAAN,EAAAO,UACAP,EAAAI,GAAAJ,EAAAE,GACAF,EAAAM,MAAA,IAIA+H,aAnTA,SAmTArI,GACA4B,QAAAC,IAAA7B,GACA,GAAAA,EAAAO,QACAP,EAAAI,GAAAJ,EAAAE,GAAAF,EAAAQ,OACA,GAAAR,EAAAO,UACAP,EAAAI,GAAAJ,EAAAE,KAMAoI,mBA9TA,SA8TAtI,KAMAuI,mBApUA,SAoUAvI,KAMAwI,MA1UA,WA0UA,IAAAC,EAAA5I,KAAA,OAAAmC,IAAAC,EAAAC,EAAAC,KAAA,SAAAuG,IAAA,OAAAzG,EAAAC,EAAAG,KAAA,SAAAsG,GAAA,cAAAA,EAAApG,KAAAoG,EAAAnG,MAAA,wBAAAmG,EAAA/F,SAAA8F,EAAAD,KAAAzG,IAIA4G,oBA9UA,aAkVAC,QAlVA,WAoVAhJ,KAAAf,UAAAU,UAAAsJ,EAEAjJ,KAAAhB,uBAAA,GAGAkK,gBAzVA,WA0VAlJ,KAAAR,YAAA,GAGA2J,aA7VA,WA8VAnJ,KAAAR,YAAA,GAGA4J,kBAjWA,SAiWAC,GAEAtH,QAAAC,IAAAqH,KAIAC,SA+DAC,QArgBA,WAugBAvJ,KAAAiC,cAEAjC,KAAAoB,mBACA6H,GAAAjJ,KAAAwJ,OAAApC,MAAAzH,MACAK,KAAAL,KAAAK,KAAAwJ,OAAApC,MAAAzH,KAEAK,KAAA6E,cACA7E,KAAAJ,GAAAI,KAAAwJ,OAAApC,MAAAxH,GACAI,KAAAf,UAAAyG,KAAA1F,KAAAwJ,OAAApC,MAAA1B,KACA1F,KAAAf,UAAA4C,OAAA7B,KAAAwJ,OAAApC,MAAAvB,MAGA7F,KAAAgD,gBCjuBeyG,GADEC,OAFjB,WAA0B,IAAAC,EAAA3J,KAAa4J,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,MAAA,OAAAC,OAAA,uBAA6CJ,EAAA,OAAYK,YAAA,SAAmBL,EAAA,WAAgBK,YAAA,mBAAAH,aAA4CI,MAAA,QAAeC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,OAAAH,EAAAa,GAAA,UAAAb,EAAAc,GAAAd,EAAA1K,UAAAyG,YAAA,GAAAiE,EAAAa,GAAA,KAAAV,EAAA,WAA2FK,YAAA,mBAAAH,aAA4CI,MAAA,SAAgBC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOpE,KAAA,UAAAsE,KAAA,SAAAG,KAAA,wBAA+DC,IAAKC,MAAAjB,EAAAzD,cAAwByD,EAAAa,GAAA,sCAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAA8EE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOpE,KAAA,UAAAsE,KAAA,SAAAG,KAAA,oBAA2DC,IAAKC,MAAAjB,EAAAvD,QAAkBuD,EAAAa,GAAA,wBAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAAgEE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOpE,KAAA,OAAAsE,KAAA,SAAAG,KAAA,oBAAwDC,IAAKC,MAAAjB,EAAAX,WAAqBW,EAAAa,GAAA,8BAAAb,EAAAa,GAAA,KAAAV,EAAA,OAA6DE,aAAaa,MAAA,WAAgB,GAAAlB,EAAAa,GAAA,KAAAV,EAAA,OAA4BK,YAAA,aAAwBR,EAAAmB,GAAAnB,EAAA,kBAAAzJ,EAAAsH,GAA0C,OAAAsC,EAAA,WAAqBiB,IAAAvD,EAAAwC,aAAuBgB,gBAAA,OAAsBX,OAAQY,GAAA,QAAAzD,KAAsBsC,EAAA,OAAYK,YAAA,WAAAE,OAA8Ba,KAAA,UAAgBA,KAAA,WAAevB,EAAAa,GAAA,aAAAb,EAAAc,GAAAvK,EAAAiL,MAAA,eAAArB,EAAA,QAAAH,EAAAa,GAAAb,EAAAc,GAAAd,EAAA3I,KAAAd,OAAAyJ,EAAAa,GAAA,gBAAAb,EAAAa,GAAA,KAAAV,EAAA,YAAiJO,OAAOtL,KAAAmB,EAAAC,GAAAiL,cAAAzB,EAAA9B,iBAAAwD,qBAAqEC,aAAA,UAAyBC,OAAA,MAAczB,EAAA,mBAAwBO,OAAOmB,KAAA,OAAAC,MAAA,MAAAxB,MAAA,QAA0CN,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOoB,MAAA,QAAeC,YAAA/B,EAAAgC,KAAsBZ,IAAA,UAAAa,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,OAAAA,EAAA,QAA6BO,OAAOY,GAAA/K,EAAAC,GAAA0L,EAAAC,QAAAC,WAAkCpC,EAAAa,GAAAb,EAAAc,GAAAvK,EAAAC,GAAA0L,EAAAC,QAAAE,MAAA,4BAAoE,WAAarC,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOoB,MAAA,KAAAxB,MAAA,MAAAgC,MAAA,UAA4CP,YAAA/B,EAAAgC,KAAsBZ,IAAA,UAAAa,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,UAAA5J,EAAAC,GAAA0L,EAAAC,QAAAtL,KAAAsJ,EAAA,OAAAA,EAAA,qBAAoFoC,OAAOC,MAAAjM,EAAAC,GAAA0L,EAAAC,QAAA,QAAAM,SAAA,SAAAC,GAA6D1C,EAAA2C,KAAApM,EAAAC,GAAA0L,EAAAC,QAAA,UAAAO,IAA8CE,WAAA,iCAA2CzC,EAAA,eAAoBO,OAAOoB,MAAA,GAAAe,KAAA,YAA6B7B,IAAK8B,OAAA,SAAAC,GAA0B,OAAA/C,EAAAnB,aAAAtI,EAAAC,GAAA0L,EAAAC,aAA+CnC,EAAAa,GAAAb,EAAAc,GAAAvK,EAAAC,GAAA0L,EAAAC,QAAAnL,YAAA,OAAAgJ,EAAAgD,KAAAhD,EAAAa,GAAA,QAAAtK,EAAAC,GAAA0L,EAAAC,QAAAtL,KAAAsJ,EAAA,OAAAA,EAAA,qBAA8IoC,OAAOC,MAAAjM,EAAAC,GAAA0L,EAAAC,QAAA,QAAAM,SAAA,SAAAC,GAA6D1C,EAAA2C,KAAApM,EAAAC,GAAA0L,EAAAC,QAAA,UAAAO,IAA8CE,WAAA,iCAA2CzC,EAAA,eAAoBE,aAAa4C,eAAA,SAAuBvC,OAAQoB,MAAA,GAAAe,KAAA,YAA6B7B,IAAK8B,OAAA,SAAAC,GAA0B,OAAA/C,EAAApB,YAAArI,EAAAC,GAAA0L,EAAAC,aAA8CnC,EAAAa,GAAA,KAAAV,EAAA,mBAAoCE,aAAaC,MAAA,SAAgBI,OAAQwC,IAAA3M,EAAAC,GAAA0L,EAAAC,QAAAgB,OAAAC,IAAA7M,EAAAC,GAAA0L,EAAAC,QAAAkB,OAAAC,KAAA/M,EAAAC,GAAA0L,EAAAC,QAAAoB,KAAA3C,KAAA,QAAgHI,IAAK8B,OAAA,SAAAC,GAA0B,OAAA/C,EAAArB,YAAApI,EAAAC,GAAA0L,EAAAC,WAA6CI,OAAQC,MAAAjM,EAAAC,GAAA0L,EAAAC,QAAA,MAAAM,SAAA,SAAAC,GAA2D1C,EAAA2C,KAAApM,EAAAC,GAAA0L,EAAAC,QAAA,QAAAO,IAA4CE,WAAA,gCAAyC,OAAA5C,EAAAgD,WAAuB,WAAahD,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOoB,MAAA,KAAAxB,MAAA,KAAAgC,MAAA,UAA2CP,YAAA/B,EAAAgC,KAAsBZ,IAAA,UAAAa,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,OAAAH,EAAAa,GAAA,mBAAAb,EAAAc,GAAAvK,EAAAC,GAAA0L,EAAAC,QAAAzL,IAAA,wBAAiG,WAAasJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOoB,MAAA,OAAAxB,MAAA,KAAAgC,MAAA,UAA6CP,YAAA/B,EAAAgC,KAAsBZ,IAAA,UAAAa,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,qBAAgCoC,OAAOC,MAAAjM,EAAAC,GAAA0L,EAAAC,QAAA,OAAAM,SAAA,SAAAC,GAA4D1C,EAAA2C,KAAApM,EAAAC,GAAA0L,EAAAC,QAAA,SAAAO,IAA6CE,WAAA,gCAA0CzC,EAAA,eAAoBO,OAAOoB,MAAA,IAAAe,KAAA,mBAAmC,OAAQ,WAAa7C,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOmB,KAAA,GAAAC,MAAA,QAAyBC,YAAA/B,EAAAgC,KAAsBZ,IAAA,UAAAa,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,YAAuBO,OAAOpE,KAAA,WAAAkH,KAAA,GAA2BxC,IAAKyC,MAAA,SAAAV,GAAyB,OAAA/C,EAAAlB,mBAAAvI,EAAAC,GAAA0L,EAAAC,WAAoDI,OAAQC,MAAAjM,EAAAC,GAAA0L,EAAAC,QAAA,KAAAM,SAAA,SAAAC,GAA0D1C,EAAA2C,KAAApM,EAAAC,GAAA0L,EAAAC,QAAA,wBAAAO,IAAAgB,OAAAhB,IAAkFE,WAAA,kCAA4C,WAAa5C,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOoB,MAAA,KAAAxB,MAAA,KAAAgC,MAAA,UAA2CP,YAAA/B,EAAAgC,KAAsBZ,IAAA,UAAAa,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,OAAAH,EAAAa,GAAAb,EAAAc,GAAAvK,EAAAC,GAAA0L,EAAAC,QAAAvL,WAA6D,WAAaoJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOmB,KAAA,OAAAC,MAAA,QAA6BC,YAAA/B,EAAAgC,KAAsBZ,IAAA,UAAAa,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,OAAAH,EAAAa,GAAA,mBAAAb,EAAAc,GAAAvK,EAAAC,GAAA0L,EAAAC,QAAAwB,MAAA,wBAAmG,WAAa3D,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOoB,MAAA,QAAeC,YAAA/B,EAAAgC,KAAsBZ,IAAA,UAAAa,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,YAAuBO,OAAOpE,KAAA,WAAAkH,KAAA,GAA2BxC,IAAKyC,MAAA,SAAAV,GAAyB,OAAA/C,EAAAjB,mBAAAxI,EAAAC,GAAA0L,EAAAC,WAAoDI,OAAQC,MAAAjM,EAAAC,GAAA0L,EAAAC,QAAA,KAAAM,SAAA,SAAAC,GAA0D1C,EAAA2C,KAAApM,EAAAC,GAAA0L,EAAAC,QAAA,wBAAAO,IAAAgB,OAAAhB,IAAkFE,WAAA,kCAA4C,YAAa,SAAU,GAAA5C,EAAAa,GAAA,KAAAV,EAAA,WAA+BE,aAAagB,gBAAA,OAAsBX,OAAQY,GAAA,YAAenB,EAAA,QAAaK,YAAA,iBAA2BR,EAAAa,GAAA,WAAAV,EAAA,QAA+BE,aAAauD,MAAA,SAAe5D,EAAAa,GAAAb,EAAAc,GAAAd,EAAA7J,aAAA6J,EAAAa,GAAA,KAAAV,EAAA,QAAwDK,YAAA,iBAA2BR,EAAAa,GAAA,WAAAV,EAAA,QAA+BE,aAAauD,MAAA,SAAe5D,EAAAa,GAAAb,EAAAc,GAAAd,EAAArJ,aAAAqJ,EAAAa,GAAA,KAAAV,EAAA,QAAwDK,YAAA,iBAA2BR,EAAAa,GAAA,qBAAAV,EAAA,QAAyCE,aAAauD,MAAA,SAAe5D,EAAAa,GAAAb,EAAAc,GAAAd,EAAA/I,SAAA,WAAA+I,EAAAa,GAAA,KAAAV,EAAA,WAAkEO,OAAOY,GAAA,YAAenB,EAAA,QAAaE,aAAauD,MAAA,MAAAC,QAAA,QAAAvD,MAAA,OAAAwD,cAAA,UAAqE9D,EAAAa,GAAA,SAAAb,EAAAa,GAAA,KAAAV,EAAA,MAAuCE,aAAa0D,cAAA,MAAAC,aAAA,UAAyC7D,EAAA,MAAWE,aAAa2D,aAAA,UAAqBhE,EAAAa,GAAA,iEAAAb,EAAAa,GAAA,KAAAV,EAAA,MAA+FE,aAAa2D,aAAA,UAAqBhE,EAAAa,GAAA,kHAAAb,EAAAa,GAAA,KAAAV,EAAA,aAAuJO,OAAOuD,MAAA,WAAAC,QAAAlE,EAAA3K,sBAAAiL,MAAA,OAAqEU,IAAKmD,iBAAA,SAAApB,GAAkC/C,EAAA3K,sBAAA0N,MAAmC5C,EAAA,WAAgBiE,IAAA,WAAA1D,OAAsB6B,MAAAvC,EAAA1K,UAAA+O,cAAA,QAAAzD,KAAA,UAA2DT,EAAA,OAAYE,aAAawD,QAAA,UAAkB1D,EAAA,gBAAqBK,YAAA,WAAAE,OAA8BoB,MAAA,UAAgB3B,EAAA,YAAiBO,OAAO4D,UAAA,GAAAC,YAAA,QAAoChC,OAAQC,MAAAxC,EAAA1K,UAAA,KAAAmN,SAAA,SAAAC,GAAoD1C,EAAA2C,KAAA3C,EAAA1K,UAAA,OAAAoN,IAAqCE,WAAA,qBAA8B,OAAA5C,EAAAa,GAAA,KAAAV,EAAA,OAAgCE,aAAawD,QAAA,UAAkB1D,EAAA,gBAAqBK,YAAA,WAAAE,OAA8BoB,MAAA,aAAmB3B,EAAA,aAAkBE,aAAaC,MAAA,QAAeI,OAAQ6D,YAAA,WAAwBvD,IAAK8B,OAAA9C,EAAAP,mBAA+B8C,OAAQC,MAAAxC,EAAA1K,UAAA,OAAAmN,SAAA,SAAAC,GAAsD1C,EAAA2C,KAAA3C,EAAA1K,UAAA,SAAAoN,IAAuCE,WAAA,qBAAgC5C,EAAAmB,GAAAnB,EAAA,kBAAA/F,EAAA4D,GAA4C,OAAAsC,EAAA,aAAuBiB,IAAAvD,EAAA6C,OAAiBoB,MAAA7H,EAAA/B,OAAAsK,MAAAvI,EAAA/B,YAA2C,aAAA8H,EAAAa,GAAA,KAAAV,EAAA,QAAsCK,YAAA,gBAAAE,OAAmCa,KAAA,UAAgBA,KAAA,WAAepB,EAAA,aAAkBO,OAAOpE,KAAA,UAAAsE,KAAA,UAAiCI,IAAKC,MAAA,SAAA8B,GAAyB,OAAA/C,EAAArE,mBAA4BqE,EAAAa,GAAA,SAAAb,EAAAa,GAAA,KAAAV,EAAA,aAA8CO,OAAOE,KAAA,UAAgBI,IAAKC,MAAA,SAAA8B,GAAyB,OAAA/C,EAAAtE,wBAAiCsE,EAAAa,GAAA,sBAEv2P2D,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACExP,EACA2K,GATF,EAVA,SAAA8E,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/282.027041475489db89b690.js", "sourcesContent": ["<template>\r\n  <div style=\"width: 100%;height: calc(100% - 32px);\">\r\n    <!---->\r\n    <div class=\"mhcx\">\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:left\">\r\n        <el-form-item style=\"float: right;\">\r\n          <div>当前审查任务：{{ dialogObj.rwmc }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!---->\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:right\">\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"primary\" size=\"medium\" icon=\"el-icon-document-add\" @click=\"saveToNext\">\r\n            保存至下一步\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"warning\" size=\"medium\" @click=\"save\" icon=\"el-icon-document\">临时保存\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"info\" size=\"medium\" @click=\"newScrw\" icon=\"el-icon-document\">新建审查任务\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div style=\"clear: both;\"></div>\r\n    </div>\r\n    <!---->\r\n    <div class=\"div-table\">\r\n      <el-card v-for=\"(dx, index) in newArray\" :key=\"index\" :id=\"'table' + index\" style=\"margin-bottom: 1em\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          {{ dx.dxmc }}\r\n          （<span>{{ dxdf(dx) }}</span>分）\r\n        </div>\r\n        <el-table :data=\"dx.xx\" :span-method=\"objectSpanMethod\" :header-cell-style=\"{ 'text-align': 'center' }\" border>\r\n          <el-table-column prop=\"xxmc\" label=\"检查项\" width=\"80\"></el-table-column>\r\n          <el-table-column label=\"检查内容\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- v-for=\"(item,index) in dx.xx[scope.$index].xx\" :key=\"item.scid\" -->\r\n              <div>\r\n                <!-- {{ dx.xx[scope.$index].xx[scope.$index].scnr }} -->\r\n                <!-- {{ item.scnr }} -->\r\n                <span :id=\"dx.xx[scope.$index].mdIndex\"></span>{{ dx.xx[scope.$index].scnr }}\r\n\r\n                <!-- <span :id=\"dx.xx[scope.$index].xx[scope.$index].scid\"></span>{{ dx.xx[scope.$index].xx[scope.$index].scnr }} -->\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"扣分\" width=\"150\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <div v-if=\"dx.xx[scope.$index].kffs == 0\">\r\n                  <el-checkbox-group v-model=\"dx.xx[scope.$index].checked\">\r\n                    <el-checkbox label=\"\" name=\"checkbox\" @change=\"handCheckbox(dx.xx[scope.$index])\">{{\r\n        dx.xx[scope.$index].gdkffz\r\n      }}</el-checkbox>\r\n                  </el-checkbox-group>\r\n                </div>\r\n                <div v-if=\"dx.xx[scope.$index].kffs == 1\">\r\n                  <el-checkbox-group v-model=\"dx.xx[scope.$index].checked\">\r\n                    <el-checkbox label=\"\" name=\"checkbox\" style=\"margin-right: 0.5em\"\r\n                      @change=\"handCheckdx(dx.xx[scope.$index])\">\r\n                    </el-checkbox>\r\n                    <el-input-number v-model=\"dx.xx[scope.$index].jsqkf\" :min=\"dx.xx[scope.$index].zdkffz\"\r\n                      :max=\"dx.xx[scope.$index].zgkffz\" :step=\"dx.xx[scope.$index].kfzf\" size=\"mini\"\r\n                      style=\"width: 100px\" @change=\"handleKfjsq(dx.xx[scope.$index])\"></el-input-number>\r\n                  </el-checkbox-group>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"分值\" width=\"50\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{ dx.xx[scope.$index].fz }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"实有项目\" width=\"80\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox-group v-model=\"dx.xx[scope.$index].sfsynr\">\r\n                <el-checkbox label=\"是\" name=\"checkboxSynr\"></el-checkbox>\r\n              </el-checkbox-group>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"\" label=\"实有内容\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input type=\"textarea\" v-model.trim=\"dx.xx[scope.$index].synr\" :rows=\"3\"\r\n                @input=\"handleSynrTextarea(dx.xx[scope.$index])\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"得分\" width=\"50\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <div>{{ dx.xx[scope.$index].df }}</div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"kfbz\" label=\"扣分标准\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{ dx.xx[scope.$index].kfbz }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"评分说明\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input type=\"textarea\" v-model.trim=\"dx.xx[scope.$index].dfsm\" :rows=\"3\"\r\n                @input=\"handleKfsmTextarea(dx.xx[scope.$index])\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-card>\r\n    </div>\r\n    <!---->\r\n    <el-card id=\"sslist\" style=\"margin-bottom: 5px;\">\r\n      <span class=\"sslist_class\">实有项目总分：<span style=\"color: red\">{{ syxmzf }}</span></span>\r\n      <span class=\"sslist_class\">实有项目得分：<span style=\"color: red\">{{ syxmdf }}</span></span>\r\n      <span class=\"sslist_class\">实有项目得分占实有项目总分百分比：<span style=\"color: red\">{{ syxmbfb }}%</span></span>\r\n    </el-card>\r\n    <el-card id=\"sslist\">\r\n      <span style=\"color: red; display: block; width: 100%; font-weight: bold\">备注：</span>\r\n      <ol style=\"margin-left: 2em; list-style: disc\">\r\n        <li style=\"list-style: disc\">\r\n          1.单项检查内容存在多起不符合要求行为的，每个单项的总扣分最高不超过该项的总分值。\r\n        </li>\r\n        <li style=\"list-style: disc\">\r\n          2.实行100分评分制,得分为实有项目得分与实有项目总分比值的百分制得分。实有项目总分为实有检查内容各项分值之和，实有项目得分为实有项目总分扣除自查发现问题分值之后的得分。\r\n        </li>\r\n      </ol>\r\n    </el-card>\r\n    <!-- 创建自查自评任务dialog -->\r\n    <el-dialog title=\"新建自查自评任务\" :visible.sync=\"dialogVisibleXjzczpRw\" width=\"35%\">\r\n      <el-form ref=\"formName\" :model=\"dialogObj\" label-width=\"120px\" size=\"mini\">\r\n        <div style=\"display:flex\">\r\n          <el-form-item label=\"任务名称\" class=\"one-line\">\r\n            <el-input v-model=\"dialogObj.rwmc\" clearable placeholder=\"任务名称\"></el-input>\r\n          </el-form-item>\r\n        </div>\r\n        <!-- <div style=\"display:flex\">\r\n          <el-form-item label=\"请选择检查单位名称\" class=\"one-line\">\r\n            <el-select v-model=\"dialogObj.dwid\" placeholder=\"请选择检查单位名称\" style=\"width: 100%;\">\r\n              <el-option v-for=\"(item, index) in dialogObj.dwxxList\" :key=\"index\" :label=\"item.dwmc\" :value=\"item.dwid\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </div> -->\r\n        <div style=\"display:flex\">\r\n          <el-form-item label=\"请选择检查季度\" class=\"one-line\">\r\n            <el-select v-model=\"dialogObj.jcjdmc\" placeholder=\"请选择检查季度\" @change=\"handleJcjdChanged\"\r\n              style=\"width: 100%;\">\r\n              <el-option v-for=\"(item, index) in jcjdList\" :key=\"index\" :label=\"item.jcjdmc\" :value=\"item.jcjdmc\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </div>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" size=\"medium\" @click=\"createZczpRw()\">创 建</el-button>\r\n        <el-button size=\"medium\" @click=\"closeXjzczpDialog()\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!--锚点索引-->\r\n    <!-- <div class=\"md-menu\" @mouseover=\"mouseoverMdMenu\" @mouseout=\"mouseoutMenu\">\r\n      <div class=\"md-left\"></div>\r\n      <transition name=\"el-fade-in-linear\">\r\n        <div v-show=\"showMdmenu\" class=\"md-right\">\r\n          <div class=\"md-article\">\r\n            <el-timeline :reverse=\"reverse\">\r\n              <el-timeline-item v-for=\"(item, index) in activities\" :key=\"index\">\r\n                <div>\r\n                  <h4>{{ item.dxmc }}</h4>\r\n                  <div v-for=\"(xxItem, xxIndex) in item.children\" :key=\"xxIndex\" class=\"md-article-article\">\r\n                    <span v-if=\"xxItem.xxmc\" style=\"color:#409EFF;\">【{{ xxItem.xxmc }}】</span>\r\n                    <a :href=\"xxItem.href\">\r\n                      <span>{{ xxItem.nr }}</span>\r\n                    </a>\r\n                    <span v-if=\"xxItem.ykf\" style=\"color:#F56C6C;\">扣{{ xxItem.ykf }}分<span\r\n                        class=\"el-icon-caret-right\"></span></span>\r\n                  </div>\r\n                </div>\r\n              </el-timeline-item>\r\n            </el-timeline>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n      <div class=\"md-right-margin-div\"></div>\r\n    </div> -->\r\n    <!---->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getAllDwzcdx, getxxListByDxid, getDwjcnrByXxid,\r\n  addJcrw, savaJcpfjlBatch, updateJcrw,\r\n  deletePfjlByRwid, getDwzcpfjlByRwid,\r\n  getAllDwzcxx, getAllDwzcnr, getDwzcnrByDxid\r\n} from '../../../api/zczp'\r\nimport {\r\n  // 获取注册信息\r\n  getDwxx,\r\n} from '../../../api/dwzc'\r\n\r\n\r\n// import { checkObjIsBlank, machineSendParams, generatorCurrentYearQuarter } from '../../../utils/utils'\r\n\r\n// import { getWindowLocation, setZczpIdsObj, getZczpIdsObj, initZczpIdsObj } from '../../../utils/windowLocation'\r\n\r\n// import { writeOptionsLog } from '../../../utils/logUtils'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 新建自查自评任务 dialog\r\n      dialogVisibleXjzczpRw: false,\r\n      dialogObj: {},\r\n      // dialogObj: {\r\n      //   rwmc: '自查任务20221105.1915',\r\n      //   dwid: 'D66215EC-1B9A-408A-BEE8-7A9396165EA7'\r\n      // },\r\n      // 当前年份的检查季度集合\r\n      jcjdList: [],\r\n      ////////////////////\r\n      //页面实际操作的评分数据[dx:{scnr:[]}]\r\n      //各大项得分总和array（实时变化）\r\n      dxdfArr: [],\r\n      //table实际操作的检查评分数据\r\n      showDxList: [],\r\n      //单元格合并规则\r\n      spanArr: [],\r\n      // 时间线排序方向\r\n      reverse: true,\r\n      // 锚点菜单集合\r\n      activities: [],\r\n      // 锚点菜单显隐\r\n      showMdmenu: false,\r\n      newArray: [],\r\n      dwxx: {},\r\n      rwid: '',\r\n      zt: '',\r\n    }\r\n  },\r\n  computed: {\r\n    // //实有项目总分\r\n    syxmzf() {\r\n      let zf = 0;\r\n      this.newArray.forEach((dx) => {\r\n        dx.xx.forEach((xx) => {\r\n          if (xx.sfsynr) {\r\n            zf += xx.fz;\r\n          }\r\n        });\r\n      });\r\n      return zf;\r\n    },\r\n    // //实有项目得分\r\n    syxmdf() {\r\n      let df = 0;\r\n      this.newArray.forEach((dx) => {\r\n        dx.xx.forEach((xx) => {\r\n          if (xx.sfsynr) {\r\n            // df += xx.fz - xx.ykf;\r\n            if (xx.kffs == 1) {\r\n              df += xx.fz - xx.jsqkf\r\n            } else if (xx.kffs == 0) {\r\n              if (xx.checked == false) {\r\n                df += xx.fz - 0\r\n              } else {\r\n                df += xx.fz - xx.gdkffz\r\n              }\r\n            }\r\n          }\r\n        });\r\n      });\r\n      return df;\r\n    },\r\n    //计算实有项目得分百分比\r\n    syxmbfb() {\r\n      if (this.syxmzf == 0) {\r\n        return 0;\r\n      }\r\n      let num = this.syxmdf / this.syxmzf;\r\n      return Math.round(num * 1000) / 10;\r\n    },\r\n    //计算大项得分\r\n    dxdf() {\r\n      return function (dx) {\r\n        let resDf = 0\r\n        dx.xx.forEach((xx) => {\r\n          if (xx.kffs == 1) {\r\n            resDf += xx.fz - xx.jsqkf\r\n          } else if (xx.kffs == 0) {\r\n            if (xx.checked == false) {\r\n              resDf += xx.fz - 0\r\n            } else {\r\n              resDf += xx.fz - xx.gdkffz\r\n            }\r\n          }\r\n        })\r\n        return resDf\r\n      }\r\n    }\r\n  },\r\n  components: {\r\n  },\r\n  methods: {\r\n    // async getscrw() {\r\n    //   let params = {\r\n    //     rwid: this.rwid\r\n    //   }\r\n    //   let data = await getDwzcpfjlByRwid(params)\r\n    //   console.log(data);\r\n    // },\r\n    // 获取当前年份的检查季度表集合\r\n    getJcjdList() {\r\n      let date = new Date()\r\n      let year = date.getFullYear()\r\n      let month = date.getMonth() + 1\r\n      //\r\n      let resObj = {\r\n        currentQuarter: 0,\r\n        resList: [\r\n          {\r\n            jcjdid: 1,\r\n            jcjdmc: year + '年第一季度',\r\n          },\r\n          {\r\n            jcjdid: 2,\r\n            jcjdmc: year + '年第二季度',\r\n          },\r\n          {\r\n            jcjdid: 3,\r\n            jcjdmc: year + '年第三季度',\r\n          },\r\n          {\r\n            jcjdid: 4,\r\n            jcjdmc: year + '年第四季度',\r\n          },\r\n        ],\r\n      }\r\n      switch (month) {\r\n        case 1:\r\n          resObj.currentQuarter = 1\r\n          break\r\n        case 2:\r\n          resObj.currentQuarter = 1\r\n          break\r\n        case 3:\r\n          resObj.currentQuarter = 1\r\n          break\r\n        case 4:\r\n          resObj.currentQuarter = 2\r\n          break\r\n        case 5:\r\n          resObj.currentQuarter = 2\r\n          break\r\n        case 6:\r\n          resObj.currentQuarter = 2\r\n          break\r\n        case 7:\r\n          resObj.currentQuarter = 3\r\n          break\r\n        case 8:\r\n          resObj.currentQuarter = 3\r\n          break\r\n        case 9:\r\n          resObj.currentQuarter = 3\r\n          break\r\n        case 10:\r\n          resObj.currentQuarter = 4\r\n          break\r\n        case 11:\r\n          resObj.currentQuarter = 4\r\n          break\r\n        case 12:\r\n          resObj.currentQuarter = 4\r\n          break\r\n      }\r\n      console.log('111111111111111111111111', resObj);\r\n      this.dialogObj.jcjdid = resObj.currentQuarter\r\n      this.jcjdList = resObj.resList\r\n    },\r\n    // 获取单位信息集合（默认选中最后一个，也就是最新的单位信息）\r\n    async getDwxxList() {\r\n      let data = await getDwxx()\r\n      console.log(data);\r\n      this.dwxx = data\r\n    },\r\n    // 获取大项小项内容信息\r\n    async getDxxxList() {\r\n      let data = await getAllDwzcdx()\r\n      if (data.code == 10000) {\r\n        this.showDxList = data.data\r\n        let newArr = await Promise.all(\r\n          this.showDxList.map(async (item) => {\r\n            let params = {\r\n              dxid: item.dxid\r\n            }\r\n            let xxnrlist = await getDwzcnrByDxid(params)\r\n            item.xx = JSON.parse(JSON.stringify(xxnrlist))\r\n            let xxlist = await getAllDwzcxx()\r\n            item.xx.map((item) => {\r\n              xxlist.map((item1) => {\r\n                if (item1.xxid == item.xxid) {\r\n                  item.xxmc = item1.xxmc;\r\n                }\r\n              })\r\n            })\r\n            return item\r\n          })\r\n        );\r\n        this.newArray = JSON.parse(JSON.stringify(newArr))\r\n        this.spanArr = this.getSpanArr(this.newArray)\r\n        console.log(this.newArray);\r\n      }\r\n    },\r\n    //获取历史数据信息\r\n    async getDxxxlssj() {\r\n      let data = await getAllDwzcdx()\r\n      if (data.code == 10000) {\r\n        this.showDxList = data.data\r\n        let newArr = await Promise.all(\r\n          this.showDxList.map(async (item) => {\r\n            let params = {\r\n              dxid: item.dxid,\r\n              rwid: this.rwid\r\n            }\r\n            //更换历史数据接口\r\n            let xxnrlist = await getDwzcpfjlByRwid(params)\r\n            item.xx = JSON.parse(JSON.stringify(xxnrlist.data))\r\n            // let xxlist = await getAllDwzcxx()\r\n            // item.xx.map((item) => {\r\n            //   xxlist.map((item1) => {\r\n            //     if (item1.xxid == item.xxid) {\r\n            //       item.xxmc = item1.xxmc;\r\n            //     }\r\n            //   })\r\n            // })\r\n            return item\r\n          })\r\n        );\r\n        this.newArray = JSON.parse(JSON.stringify(newArr))\r\n        console.log(this.newArray);\r\n        this.spanArr = this.getSpanArr(this.newArray)\r\n      }\r\n    },\r\n    // 关闭新建自查自评dialog\r\n    closeXjzczpDialog() {\r\n      // 通知tags组件关闭新建zczp tag\r\n      this.dialogVisibleXjzczpRw = false\r\n    },\r\n    // 新建自查自评任务\r\n    async createZczpRw() {\r\n      let params = {\r\n        rwmc: this.dialogObj.rwmc,\r\n        dwmc: this.dwxx.dwmc,\r\n        dwid: this.dwxx.dwid,\r\n        jcjd: this.dialogObj.jcjdmc,\r\n        jczt: '新建审查任务'\r\n      }\r\n      let data = await addJcrw(params)\r\n      if (data.code == 10000) {\r\n        this.$message({\r\n          message: '新建自查自评任务成功！',\r\n          type: 'success'\r\n        });\r\n        this.dialogVisibleXjzczpRw = false\r\n        this.rwid = data.data\r\n      }\r\n    },\r\n    // 保存至下一步\r\n    saveToNext() {\r\n      this.dwjcRK(this.newArray, 1)\r\n    },\r\n    // 临时保存\r\n    save() {\r\n      this.dwjcRK(this.newArray, 0)\r\n    },\r\n    // 新建自查自评任务参数校验（为了复用）\r\n    checkXjzczpRwParams() {\r\n      const dwmc = this.dialogObj.rwmc\r\n      const jcid = this.dialogObj.jcjdmc\r\n      if (dwmc && jcid) {\r\n        return true\r\n      }\r\n    },\r\n    /**\r\n     * 单位检查数据入库\r\n     * dwpfjlList：页面上的评分数据\r\n    */\r\n    async dwjcRK(dwpfjlList, zt) {\r\n      if (!this.checkXjzczpRwParams()) {\r\n        this.$message.warning('请先创建检查任务再保存数据')\r\n        this.dialogVisibleXjzczpRw = true\r\n        return\r\n      }\r\n      let del = {\r\n        rwid: this.rwid\r\n      }\r\n      let rwdel = await deletePfjlByRwid(del)\r\n      if (rwdel.code == 10000) {\r\n        console.log(dwpfjlList);\r\n        console.log(zt);\r\n        // return\r\n        let list = []\r\n        dwpfjlList.forEach((item) => {\r\n          item.xx.forEach((nr) => {\r\n            nr.dwid = this.dwxx.dwid\r\n            nr.rwid = this.rwid\r\n            list.push(nr)\r\n            console.log(nr);\r\n\r\n          })\r\n        })\r\n        let data = await savaJcpfjlBatch(list)\r\n        if (data.code == 10000) {\r\n          if (zt == 0) {\r\n            let zt = {\r\n              rwid: this.rwid,\r\n              jczt: '机关单位基本信息临时保存',\r\n              df: this.syxmdf\r\n            }\r\n            let data = await updateJcrw(zt)\r\n            if (data.code) {\r\n              this.$message({\r\n                message: '临时保存成功!',\r\n                type: 'success'\r\n              });\r\n            }\r\n          } else if (zt == 1) {\r\n            let zt = {\r\n              rwid: this.rwid,\r\n              jczt: '机关单位基本信息保存完成',\r\n              df: this.syxmdf\r\n            }\r\n            let data = await updateJcrw(zt)\r\n            if (data.code) {\r\n              this.$message({\r\n                message: '保存成功!',\r\n                type: 'success'\r\n              });\r\n              this.$router.push({\r\n                path: '/ccdnsjg',\r\n                query: {\r\n                  rwid: this.rwid,\r\n                  rwmc: this.dialogObj.rwmc,\r\n                  jcjd: this.dialogObj.jcjdmc,\r\n                }\r\n              })\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //根据内容id计算内容所在大项、小项索引\r\n    getDxXxIndex(nrid) {\r\n\r\n    },\r\n    //----------------------------用来返回this.spanArr数组的，定义每一行的 rowspan-----------\r\n    getSpanArr(list) {\r\n      let spanArr2 = []\r\n      for (var index = 0; index < list.length; index++) {\r\n        let data = list[index].xx\r\n        let spanArr = []\r\n        for (var i = 0; i < data.length; i++) {\r\n          if (i === 0) {\r\n            spanArr.push(1)\r\n            this.pos = 0\r\n          } else {\r\n            // 判断当前元素与上一个元素是否相同\r\n            if (data[i].xxid == data[i - 1].xxid) {\r\n              spanArr[this.pos] += 1\r\n              spanArr.push(0)\r\n            } else {\r\n              spanArr.push(1)\r\n              this.pos = i\r\n            }\r\n          }\r\n        }\r\n        spanArr.forEach((sa) => {\r\n          spanArr2.push(sa)\r\n        })\r\n      }\r\n      return spanArr2\r\n    },\r\n    objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n\r\n      if (columnIndex === 0) {\r\n        //\r\n        const _row = this.spanArr[row.scid - 1]\r\n        return {\r\n          rowspan: _row,\r\n          colspan: 1,\r\n        }\r\n      }\r\n    },\r\n    // 扣分计数器改变\r\n    handleKfjsq(xx) {\r\n      console.log(xx);\r\n      xx.checked = true\r\n      xx.df = xx.fz - xx.jsqkf\r\n    },\r\n    handCheckdx(xx) {\r\n      console.log(xx);\r\n      if (xx.checked == true) {\r\n        xx.df = xx.fz - xx.jsqkf\r\n      } else if (xx.checked == false) {\r\n        xx.df = xx.fz\r\n        xx.jsqkf = 0\r\n      }\r\n    },\r\n    //固定扣分改变\r\n    handCheckbox(xx) {\r\n      console.log(xx);\r\n      if (xx.checked == true) {\r\n        xx.df = xx.fz - xx.gdkffz\r\n      } else if (xx.checked == false) {\r\n        xx.df = xx.fz\r\n      }\r\n    },\r\n    /**\r\n     * 实有内容textarea值改变\r\n     */\r\n    handleSynrTextarea(xx) {\r\n\r\n    },\r\n    /**\r\n     * 评分说明改变\r\n     */\r\n    handleKfsmTextarea(xx) {\r\n\r\n    },\r\n    /**\r\n     * 获取单位详细自查记录字典\r\n     */\r\n    async getZD() {\r\n\r\n    },\r\n    // 通过任务ID获取单位评分记录历史信息\r\n    getDwpfjlLsxxByRwid() {\r\n\r\n    },\r\n    // 新建审查任务\r\n    newScrw() {\r\n      // 清除任务信息\r\n      this.dialogObj.rwid = undefined\r\n      // 弹出新建任务dialog\r\n      this.dialogVisibleXjzczpRw = true\r\n    },\r\n    // 锚点菜单鼠标移入事件\r\n    mouseoverMdMenu() {\r\n      this.showMdmenu = true\r\n    },\r\n    // 锚点菜单鼠标移出事件\r\n    mouseoutMenu() {\r\n      this.showMdmenu = false\r\n    },\r\n    // 检测季度下拉框改变事件\r\n    handleJcjdChanged(val) {\r\n      // this.dialogObj = JSON.parse(JSON.stringify(this.dialogObj))\r\n      console.log(val);\r\n\r\n    }\r\n  },\r\n  watch: {\r\n    // showDxList: {\r\n    //   handler(newVal, oldVal) {\r\n    //     console.log(\"showDxList changed...\")\r\n    //     const _this = this\r\n    //     // 清空锚点，防重复\r\n    //     this.activities = []\r\n    //     let dxMdObj = {}\r\n    //     let xxMdObj = {}\r\n    //     //\r\n    //     _this.dxdfArr = []\r\n    //     newVal.forEach((dx, dxIndex) => {\r\n    //       // 初始化大项锚点\r\n    //       dxMdObj = {\r\n    //         dxmc: dx.dxmc,\r\n    //         children: []\r\n    //       }\r\n    //       //\r\n    //       dx.dxdf = 0\r\n    //       dx.xx.forEach((xx, xxIndex) => {\r\n    //         // 锚点\r\n    //         xx.mdIndex = 'md-' + dxIndex + '-' + xxIndex\r\n    //         /**\r\n    //          * 判断是否扣分\r\n    //          * 1、如果是扣分，则计算两种扣分方式应该扣除的分值\r\n    //          * 2、不是，则：\r\n    //          * 2.1 重置已扣分ykf为0，\r\n    //          * 2.2 得分为该项分值\r\n    //          */\r\n    //         if (xx.check) {\r\n    //           if (xx.kffs == 6) {\r\n    //             xx.ykf = xx.jsqkf\r\n    //           }\r\n    //           if (xx.kffs == 7) {\r\n    //             xx.ykf = xx.gdkffz\r\n    //           }\r\n    //           xx.df = xx.fz - xx.ykf\r\n    //           // 锚点\r\n    //           xxMdObj = {\r\n    //             href: '#' + xx.mdIndex,\r\n    //             xxmc: xx.xxmc,\r\n    //             nr: xx.nr,\r\n    //             ykf: xx.ykf\r\n    //           }\r\n    //           dxMdObj.children.push(xxMdObj)\r\n    //         } else {\r\n    //           xx.ykf = 0\r\n    //           xx.jsqkf = 0\r\n    //           xx.df = xx.fz\r\n    //         }\r\n    //         dx.dxdf += xx.df\r\n    //       })\r\n    //       // 判断是否需要将大项的锚点放入锚点集合中\r\n    //       if (dxMdObj.children.length > 0) {\r\n    //         this.activities.push(dxMdObj)\r\n    //       }\r\n    //       //把大项得分总和实时数据push到array数组中\r\n    //       _this.dxdfArr.push(dx.dxdf)\r\n    //     })\r\n    //   },\r\n    //   deep: true\r\n    // }\r\n  },\r\n  mounted() {\r\n    // // 获取单位信息集合（默认选中最后一个，也就是最新的单位信息）\r\n    this.getDwxxList()\r\n    // // 获取当前年份的检查季度表集合\r\n    this.getJcjdList()\r\n    if (this.$route.query.rwid != undefined) {\r\n      this.rwid = this.$route.query.rwid\r\n      // // 获取大项小项历史数据\r\n      this.getDxxxlssj()\r\n      this.zt = this.$route.query.zt\r\n      this.dialogObj.rwmc = this.$route.query.rwmc\r\n      this.dialogObj.jcjdmc = this.$route.query.jcjd\r\n    } else {\r\n      // // 获取大项小项内容数据\r\n      this.getDxxxList()\r\n    }\r\n\r\n    /**\r\n     * 判断路由是否携带任务ID\r\n     * 携带则获取历史\r\n     * 否则查询字典\r\n    */\r\n    // let routeRwid = this.$route.query.rwid\r\n    // let routeRwid = getZczpIdsObj().rwid\r\n    // console.log('routeRwid', routeRwid)\r\n    // ////////////////\r\n    // if (routeRwid) {\r\n    //   // 获取审查任务表历史信息\r\n    //   let scrw = selectScrwByRwid(routeRwid)\r\n    //   this.dialogObj = scrw\r\n    //   // 获取单位评分记录表历史信息\r\n    //   this.getDwpfjlLsxxByRwid()\r\n    //   return\r\n    // }\r\n    // this.dialogVisibleXjzczpRw = true\r\n    /**\r\n     * 新建自查自评\r\n     * 1、不需要查询历史信息\r\n     * 2、需要新生成一个自查自评任务\r\n     * 3、任务ID很重要，而在上方操作按钮左边的区域实际是查询参数的区域，故这里为了不混淆，提供dialog来填写任务名称和选择该任务关联的单位信息\r\n    */\r\n    // console.log('新建自查自评，查询字典信息')\r\n    // // 查询字典信息\r\n    // const zdList = this.getZD()\r\n    // this.showDxList = zdList\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/deep/.mhcx .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.widthw {\r\n  width: 100%;\r\n}\r\n\r\n/***/\r\n/deep/ .el-dialog .el-dialog__body .form-out {\r\n  display: flex;\r\n  margin-bottom: 22px;\r\n}\r\n\r\n/deep/ .el-dialog .el-dialog__body .form-label {\r\n  width: 80px;\r\n  text-align: right;\r\n  vertical-align: middle;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  padding: 0 12px 0 0;\r\n  box-sizing: border-box;\r\n  line-height: 40px;\r\n  /* margin-right: 15px; */\r\n}\r\n\r\n/****/\r\n.div-table {\r\n  overflow-y: scroll;\r\n  height: calc(100% - 46px - 64px - 108px - 20px - 10px - 10px);\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/zczp/xjzczp.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"calc(100% - 32px)\"}},[_c('div',{staticClass:\"mhcx\"},[_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"left\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('div',[_vm._v(\"当前审查任务：\"+_vm._s(_vm.dialogObj.rwmc))])])],1),_vm._v(\" \"),_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"right\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\",\"icon\":\"el-icon-document-add\"},on:{\"click\":_vm.saveToNext}},[_vm._v(\"\\n          保存至下一步\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"medium\",\"icon\":\"el-icon-document\"},on:{\"click\":_vm.save}},[_vm._v(\"临时保存\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"info\",\"size\":\"medium\",\"icon\":\"el-icon-document\"},on:{\"click\":_vm.newScrw}},[_vm._v(\"新建审查任务\\n        \")])],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"div-table\"},_vm._l((_vm.newArray),function(dx,index){return _c('el-card',{key:index,staticStyle:{\"margin-bottom\":\"1em\"},attrs:{\"id\":'table' + index}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_vm._v(\"\\n        \"+_vm._s(dx.dxmc)+\"\\n        （\"),_c('span',[_vm._v(_vm._s(_vm.dxdf(dx)))]),_vm._v(\"分）\\n      \")]),_vm._v(\" \"),_c('el-table',{attrs:{\"data\":dx.xx,\"span-method\":_vm.objectSpanMethod,\"header-cell-style\":{ 'text-align': 'center' },\"border\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"xxmc\",\"label\":\"检查项\",\"width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"检查内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('span',{attrs:{\"id\":dx.xx[scope.$index].mdIndex}}),_vm._v(_vm._s(dx.xx[scope.$index].scnr)+\"\\n\\n              \")])]}}],null,true)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"扣分\",\"width\":\"150\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[(dx.xx[scope.$index].kffs == 0)?_c('div',[_c('el-checkbox-group',{model:{value:(dx.xx[scope.$index].checked),callback:function ($$v) {_vm.$set(dx.xx[scope.$index], \"checked\", $$v)},expression:\"dx.xx[scope.$index].checked\"}},[_c('el-checkbox',{attrs:{\"label\":\"\",\"name\":\"checkbox\"},on:{\"change\":function($event){return _vm.handCheckbox(dx.xx[scope.$index])}}},[_vm._v(_vm._s(dx.xx[scope.$index].gdkffz))])],1)],1):_vm._e(),_vm._v(\" \"),(dx.xx[scope.$index].kffs == 1)?_c('div',[_c('el-checkbox-group',{model:{value:(dx.xx[scope.$index].checked),callback:function ($$v) {_vm.$set(dx.xx[scope.$index], \"checked\", $$v)},expression:\"dx.xx[scope.$index].checked\"}},[_c('el-checkbox',{staticStyle:{\"margin-right\":\"0.5em\"},attrs:{\"label\":\"\",\"name\":\"checkbox\"},on:{\"change\":function($event){return _vm.handCheckdx(dx.xx[scope.$index])}}}),_vm._v(\" \"),_c('el-input-number',{staticStyle:{\"width\":\"100px\"},attrs:{\"min\":dx.xx[scope.$index].zdkffz,\"max\":dx.xx[scope.$index].zgkffz,\"step\":dx.xx[scope.$index].kfzf,\"size\":\"mini\"},on:{\"change\":function($event){return _vm.handleKfjsq(dx.xx[scope.$index])}},model:{value:(dx.xx[scope.$index].jsqkf),callback:function ($$v) {_vm.$set(dx.xx[scope.$index], \"jsqkf\", $$v)},expression:\"dx.xx[scope.$index].jsqkf\"}})],1)],1):_vm._e()])]}}],null,true)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"分值\",\"width\":\"50\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_vm._v(\"\\n              \"+_vm._s(dx.xx[scope.$index].fz)+\"\\n            \")])]}}],null,true)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"实有项目\",\"width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-checkbox-group',{model:{value:(dx.xx[scope.$index].sfsynr),callback:function ($$v) {_vm.$set(dx.xx[scope.$index], \"sfsynr\", $$v)},expression:\"dx.xx[scope.$index].sfsynr\"}},[_c('el-checkbox',{attrs:{\"label\":\"是\",\"name\":\"checkboxSynr\"}})],1)]}}],null,true)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"\",\"label\":\"实有内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3},on:{\"input\":function($event){return _vm.handleSynrTextarea(dx.xx[scope.$index])}},model:{value:(dx.xx[scope.$index].synr),callback:function ($$v) {_vm.$set(dx.xx[scope.$index], \"synr\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"dx.xx[scope.$index].synr\"}})]}}],null,true)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"得分\",\"width\":\"50\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_vm._v(_vm._s(dx.xx[scope.$index].df))])]}}],null,true)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"kfbz\",\"label\":\"扣分标准\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_vm._v(\"\\n              \"+_vm._s(dx.xx[scope.$index].kfbz)+\"\\n            \")])]}}],null,true)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"评分说明\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3},on:{\"input\":function($event){return _vm.handleKfsmTextarea(dx.xx[scope.$index])}},model:{value:(dx.xx[scope.$index].dfsm),callback:function ($$v) {_vm.$set(dx.xx[scope.$index], \"dfsm\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"dx.xx[scope.$index].dfsm\"}})]}}],null,true)})],1)],1)}),1),_vm._v(\" \"),_c('el-card',{staticStyle:{\"margin-bottom\":\"5px\"},attrs:{\"id\":\"sslist\"}},[_c('span',{staticClass:\"sslist_class\"},[_vm._v(\"实有项目总分：\"),_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.syxmzf))])]),_vm._v(\" \"),_c('span',{staticClass:\"sslist_class\"},[_vm._v(\"实有项目得分：\"),_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.syxmdf))])]),_vm._v(\" \"),_c('span',{staticClass:\"sslist_class\"},[_vm._v(\"实有项目得分占实有项目总分百分比：\"),_c('span',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.syxmbfb)+\"%\")])])]),_vm._v(\" \"),_c('el-card',{attrs:{\"id\":\"sslist\"}},[_c('span',{staticStyle:{\"color\":\"red\",\"display\":\"block\",\"width\":\"100%\",\"font-weight\":\"bold\"}},[_vm._v(\"备注：\")]),_vm._v(\" \"),_c('ol',{staticStyle:{\"margin-left\":\"2em\",\"list-style\":\"disc\"}},[_c('li',{staticStyle:{\"list-style\":\"disc\"}},[_vm._v(\"\\n        1.单项检查内容存在多起不符合要求行为的，每个单项的总扣分最高不超过该项的总分值。\\n      \")]),_vm._v(\" \"),_c('li',{staticStyle:{\"list-style\":\"disc\"}},[_vm._v(\"\\n        2.实行100分评分制,得分为实有项目得分与实有项目总分比值的百分制得分。实有项目总分为实有检查内容各项分值之和，实有项目得分为实有项目总分扣除自查发现问题分值之后的得分。\\n      \")])])]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"新建自查自评任务\",\"visible\":_vm.dialogVisibleXjzczpRw,\"width\":\"35%\"},on:{\"update:visible\":function($event){_vm.dialogVisibleXjzczpRw=$event}}},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.dialogObj,\"label-width\":\"120px\",\"size\":\"mini\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"任务名称\"}},[_c('el-input',{attrs:{\"clearable\":\"\",\"placeholder\":\"任务名称\"},model:{value:(_vm.dialogObj.rwmc),callback:function ($$v) {_vm.$set(_vm.dialogObj, \"rwmc\", $$v)},expression:\"dialogObj.rwmc\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"请选择检查季度\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择检查季度\"},on:{\"change\":_vm.handleJcjdChanged},model:{value:(_vm.dialogObj.jcjdmc),callback:function ($$v) {_vm.$set(_vm.dialogObj, \"jcjdmc\", $$v)},expression:\"dialogObj.jcjdmc\"}},_vm._l((_vm.jcjdList),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item.jcjdmc,\"value\":item.jcjdmc}})}),1)],1)],1)]),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\"},on:{\"click\":function($event){return _vm.createZczpRw()}}},[_vm._v(\"创 建\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"medium\"},on:{\"click\":function($event){return _vm.closeXjzczpDialog()}}},[_vm._v(\"取 消\")])],1)],1)],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-0037878a\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/zczp/xjzczp.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-0037878a\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./xjzczp.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./xjzczp.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./xjzczp.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-0037878a\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./xjzczp.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-0037878a\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/zczp/xjzczp.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}