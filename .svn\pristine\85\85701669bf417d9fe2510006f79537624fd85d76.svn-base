{"version": 3, "sources": ["webpack:///src/renderer/view/wdgz/blsp/fqblxxscb.vue", "webpack:///./src/renderer/view/wdgz/blsp/fqblxxscb.vue?bd86", "webpack:///./src/renderer/view/wdgz/blsp/fqblxxscb.vue"], "names": ["fqblxxscb", "components", "AddLineTable", "props", "data", "headerCellStyle", "background", "color", "fwdyid", "slid", "activeName", "spznList", "tjlist", "xxjlList", "cyjshgxList", "ygrjzjqkList", "ysrjzjqkList", "yscgqkList", "jsjwzzqkList", "clhwffzqkList", "gjclList", "upccLsit", "disabled1", "disabled2", "disabled3", "btnsftg", "btnsfth", "jgyf", "xb", "id", "yjgwqk", "yw", "bmjysfwc", "sfwc", "scqk", "sfty", "zzmmoptions", "sltshow", "fileList", "dialogVisible", "fileRow", "smryList", "page", "pageSize", "total", "formInline", "bmmc", "xm", "selectlistRow", "xsyc", "mbh<PERSON>", "imageUrl", "imageUrlbrcn", "ylxy", "file", "bmcnssmj", "bmxyssmj", "dialogVisible_bmcns", "bmcnsImageUrl", "dialogVisible_bmxys", "bmxysImageUrl", "lcgzList", "computed", "mounted", "this", "$route", "query", "console", "log", "spzn", "spxx", "lcgz", "methods", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "params", "wrap", "_context", "prev", "next", "Object", "wdgz", "sent", "code", "content", "stop", "_this2", "_callee2", "lbxx", "ryglScjlList", "ryglJtcyList", "ryglSwzjList", "ryglYccgList", "ryglJwzzqkList", "ryglCfjlList", "bmC", "zwC", "jtarr", "arr", "list", "iamgeBase64", "_validDataUrl", "previwImg", "that", "_context2", "lx", "zgfs", "ryglZgfsScjlList", "ryglZgfsJtcyList", "ryglZgfsSwzjList", "ryglZgfsYccgList", "ryglZgfsJwzzqkList", "ryglZgfsCfjlList", "rysc", "ryglRyscScjlList", "ryglRyscJtcyList", "ryglRyscSwzjList", "ryglRyscYccgList", "ryglRyscJwzzqkList", "ryglRyscCfjlList", "jbzcComputed", "jbzc", "zw", "bmzwzcComputed", "for<PERSON>ach", "item", "jwjlqk", "push", "cyqk", "fjlb", "zp", "s", "regex", "test", "abrupt", "brcn", "yldis", "hyzk", "zzmm", "smdj", "cnsqk", "show", "xysqk", "show1", "yulan", "_validDataUrl2", "ljbl", "beforeAvatarUpload", "isJPG", "type", "isPNG", "$message", "error", "blobToBase64", "blob", "callback", "fileReader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "bmcnsyl", "cnssmj", "_validDataUrl3", "bmxysyl", "xyssmj", "_validDataUrl4", "_this3", "_callee3", "_context3", "watch", "blsp_fqblxxscb", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "model", "value", "$$v", "expression", "attrs", "label", "name", "on", "click", "_v", "border", "header-cell-style", "stripe", "width", "align", "prop", "ref", "label-width", "placeholder", "clearable", "disabled", "$set", "src", "_e", "scopedSlots", "_u", "key", "fn", "scope", "_s", "undefined", "_l", "autosize", "staticStyle", "display", "align-items", "size", "visible", "update:visible", "$event", "alt", "slot", "format", "value-format", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "qLAwcAA,GACAC,YACAC,uBAAA,GAEAC,SACAC,KALA,WAMA,OAEAC,iBACAC,WAAA,UACAC,MAAA,WAEAC,OAAA,GACAC,KAAA,GACAC,WAAA,SAEAC,YAGAC,QAEAC,YAEAC,eAEAC,gBAEAC,gBAEAC,cAEAC,gBAEAC,kBAGAC,YACAC,YAEAC,WAAA,EACAC,WAAA,EACAC,WAAA,EACAC,SAAA,EACAC,SAAA,EACAC,KAAA,GAEAC,KACAA,GAAA,IACAC,GAAA,IAGAD,GAAA,IACAC,GAAA,IAIAC,SACAC,GAAA,IACAF,GAAA,IAGAE,GAAA,IACAF,GAAA,IAIAG,WAEAC,KAAA,MACAJ,GAAA,IAGAI,KAAA,MACAJ,GAAA,IAGAK,OAEAC,KAAA,KACAN,GAAA,IAGAM,KAAA,MACAN,GAAA,IAIAO,eACAC,QAAA,GACAC,YACAC,eAAA,EACAC,QAAA,GAEAC,YACAC,KAAA,EACAC,SAAA,GACAC,MAAA,EACAC,YACAC,KAAA,GACAC,GAAA,IAEAC,iBACAC,MAAA,EACAC,OAAA,GACAC,SAAA,GACAC,aAAA,GACAC,MAAA,EACAC,QACAC,SAAA,GACAC,SAAA,GAEAC,qBAAA,EACAC,cAAA,GAEAC,qBAAA,EACAC,cAAA,GAEAC,cAIAC,YACAC,QA1HA,WA2HAC,KAAAxD,OAAAwD,KAAAC,OAAAC,MAAA1D,OACA2D,QAAAC,IAAA,cAAAJ,KAAAxD,QACAwD,KAAAvD,KAAAuD,KAAAC,OAAAC,MAAAzD,KACA0D,QAAAC,IAAA,YAAAJ,KAAAvD,MAEAuD,KAAAK,OAGAL,KAAAM,OAQAN,KAAAO,QAGAC,SAGAH,KAHA,WAGA,IAAAI,EAAAT,KAAA,OAAAU,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAA3E,EAAA,OAAAuE,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACAJ,GACAvE,OAAAiE,EAAAjE,QAFAyE,EAAAE,KAAA,EAIAC,OAAAC,EAAA,EAAAD,CAAAL,GAJA,OAKA,MADA3E,EAJA6E,EAAAK,MAKAC,OACAd,EAAA9D,SAAAP,OAAAoF,SANA,wBAAAP,EAAAQ,SAAAX,EAAAL,KAAAC,IAuIAJ,KA1IA,WA0IA,IAAAoB,EAAA1B,KAAA,OAAAU,IAAAC,EAAAC,EAAAC,KAAA,SAAAc,IAAA,IAAAZ,EAAA3E,EAAAwF,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAhC,EAAAC,EAAAI,KAAA,SAAA4B,GAAA,cAAAA,EAAA1B,KAAA0B,EAAAzB,MAAA,cACAJ,GACAtE,KAAAiF,EAAAjF,MAEAL,OAJA,EAKAwF,KACAC,KACAC,KACAC,KACAC,KACAC,KACAC,KACA/B,QAAAC,IAAAsB,EAAAmB,IAZAD,EAAAzB,KAAA,GAcAC,OAAAC,EAAA,EAAAD,CAAAL,GAdA,WAcA3E,EAdAwG,EAAAtB,KAeAnB,QAAAC,IAAAhE,GACA,MAAAA,EAAAmF,MAAA,IAAAnF,EAhBA,CAAAwG,EAAAzB,KAAA,gBAAAyB,EAAAzB,KAAA,GAiBAC,OAAAC,EAAA,EAAAD,CAAAL,GAjBA,QAiBA3E,EAjBAwG,EAAAtB,KAkBAM,EAAAxF,EAAA0G,KACAjB,EAAAzF,EAAA2G,iBACAjB,EAAA1F,EAAA4G,iBACAjB,EAAA3F,EAAA6G,iBACAjB,EAAA5F,EAAA8G,iBACAjB,EAAA7F,EAAA+G,mBACAjB,EAAA9F,EAAAgH,iBAxBAR,EAAAzB,KAAA,iBA0BAS,EAAAxF,EAAAiH,KACAxB,EAAAzF,EAAAkH,iBACAxB,EAAA1F,EAAAmH,iBACAxB,EAAA3F,EAAAoH,iBACAxB,EAAA5F,EAAAqH,iBACAxB,EAAA7F,EAAAsH,mBACAxB,EAAA9F,EAAAuH,iBAhCA,WAkCAxD,QAAAC,IAAAwB,GACAA,EAAAgC,aAAA,GAAAhC,EAAAiC,KAAA,SAAAjC,EAAAiC,KAAA,SAAAjC,EAAAiC,KACA,SAAAjC,EAAAiC,KAAA,YAAAjC,EAAAiC,KAAA,gBAAAjC,EAAAiC,KAAA,KACA,GAAAjC,EAAAiC,KAAA,WAAAjC,EAAAiC,KAAA,WAAAjC,EAAAiC,KAAA,OACA,IADAjC,EACAiC,KAAA,WAAAjC,EAAAiC,KAAA,QACA1B,EAAA,IAAAP,EAAA9C,KAAA,MAAA8C,EAAA9C,KAAA,OACAsD,EAAA,IAAAR,EAAAkC,GAAA,MAAAlC,EAAAkC,GAAA,OACAlC,EAAAmC,eAAA5B,EAAAC,EAAA,MAAAR,EAAAgC,aACAlC,EAAA9E,OAAAgF,EAEAF,EAAA9E,OAAAC,SAAAgF,EAEAQ,KACAP,EAAAkC,QAAA,SAAAC,GACA,GAAAA,EAAAC,OACAD,EAAAC,OAAA,IACA,GAAAD,EAAAC,SACAD,EAAAC,OAAA,KAEA7B,EAAA8B,KAAAF,GACAvC,EAAA9E,OAAAE,YAAAuF,IAGAC,KACAC,KACAR,EAAAiC,QAAA,SAAAC,GACA,GAAAA,EAAAG,KACAH,EAAAG,KAAA,IACA,GAAAH,EAAAG,OACAH,EAAAG,KAAA,KAEA,GAAAH,EAAAI,MAAA,GAAAJ,EAAAI,MAAA,GAAAJ,EAAAI,MACA,GAAAJ,EAAAI,KACAJ,EAAAI,KAAA,KACA,GAAAJ,EAAAI,KACAJ,EAAAI,KAAA,QACA,GAAAJ,EAAAI,OACAJ,EAAAI,KAAA,SAEA/B,EAAA6B,KAAAF,GACAvC,EAAA9E,OAAAG,aAAAuF,GAGA,GAAA2B,EAAAI,MAAA,GAAAJ,EAAAI,MAAA,GAAAJ,EAAAI,MAAA,GAAAJ,EAAAI,OACA,GAAAJ,EAAAI,KACAJ,EAAAI,KAAA,KACA,GAAAJ,EAAAI,KACAJ,EAAAI,KAAA,QACA,GAAAJ,EAAAI,KACAJ,EAAAI,KAAA,QACA,GAAAJ,EAAAI,OACAJ,EAAAI,KAAA,mBAEA9B,EAAA4B,KAAAF,GACAvC,EAAA9E,OAAAI,aAAAuF,KAIAb,EAAA9E,OAAAK,WAAA+E,EAEAN,EAAA9E,OAAAM,aAAA+E,EAEAP,EAAA9E,OAAAO,cAAA+E,EAEA,iBADAM,EAAA,0BAAAd,EAAA9E,OAAA0H,IAjGA,CAAA1B,EAAAzB,KAAA,YAqGAsB,EAAA,SAAAA,EAAA8B,GACA,OAAA9B,EAAA+B,MAAAC,KAAAF,IAFA/B,EApGA,CAAAI,EAAAzB,KAAA,gBAAAyB,EAAA8B,OAAA,kBAwGAjC,EAAA+B,MACA,6GACA/B,EAAAD,KAIAE,EAAA,SAAAuB,GACAtB,EAAAxD,SAAA8E,GAHAtB,EAAAjB,EAKAgB,EAAAF,IAjHA,QAoHA,IAAAd,EAAA9E,OAAA+H,KACAjD,EAAAkD,OAAA,EAEAlD,EAAAkD,OAAA,EAEA,GAAAlD,EAAA9E,OAAAgB,GACA8D,EAAA9E,OAAAgB,GAAA,IACA,GAAA8D,EAAA9E,OAAAgB,KACA8D,EAAA9E,OAAAgB,GAAA,KAEA,GAAA8D,EAAA9E,OAAAiI,KACAnD,EAAA9E,OAAAiI,KAAA,KACA,GAAAnD,EAAA9E,OAAAiI,OACAnD,EAAA9E,OAAAiI,KAAA,MAEA,GAAAnD,EAAA9E,OAAAkI,KACApD,EAAA9E,OAAAkI,KAAA,OACA,GAAApD,EAAA9E,OAAAkI,KACApD,EAAA9E,OAAAkI,KAAA,KACA,GAAApD,EAAA9E,OAAAkI,KACApD,EAAA9E,OAAAkI,KAAA,OACA,GAAApD,EAAA9E,OAAAkI,OACApD,EAAA9E,OAAAkI,KAAA,MAEA,GAAApD,EAAA9E,OAAAmI,KACArD,EAAA9E,OAAAmI,KAAA,KACA,GAAArD,EAAA9E,OAAAmI,KACArD,EAAA9E,OAAAmI,KAAA,KACA,GAAArD,EAAA9E,OAAAmI,OACArD,EAAA9E,OAAAmI,KAAA,MAEA,GAAArD,EAAA9E,OAAAoI,MACAtD,EAAAuD,MAAA,EAEAvD,EAAAuD,MAAA,EAEA,GAAAvD,EAAA9E,OAAAsI,MACAxD,EAAAyD,OAAA,EAEAzD,EAAAyD,OAAA,EA3JA,yBAAAvC,EAAAnB,SAAAE,EAAAD,KAAAhB,IA+JA0E,MAzSA,WA0SApF,KAAAX,MAAA,EACA,IAaA4E,EAbAzB,EAAA,0BAAAxC,KAAApD,OAAA+H,KACA,oBAAAnC,EAAA,KAGA6C,EAAA,SAAAA,EAAAd,GACA,OAAAc,EAAAb,MAAAC,KAAAF,IAFA,IAAA/B,EAAA,OAMA,GAFA6C,EAAAb,MACA,6GACAa,EAAA7C,GAAA,CAIAyB,EAGAzB,EALAxC,KAGAZ,aAAA6E,KA0FAqB,KAnZA,WAoZAtF,KAAAtD,WAAA,UAoHA6I,mBAxgBA,SAwgBAjG,GACA,IAAAkG,EAAA,eAAAlG,EAAAmG,KACAC,EAAA,cAAApG,EAAAmG,KAIA,OAHAD,GAAAE,GACA1F,KAAA2F,SAAAC,MAAA,wBAEAJ,GAAAE,GAGAG,aAjhBA,SAihBAC,EAAAC,GACA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAA,SAAAC,GACAJ,EAAAI,EAAAC,OAAAC,SAEAL,EAAAM,cAAAR,IAGAS,QAzhBA,WA0hBAvG,KAAAP,qBAAA,EACA,IAaAwE,EAbAzB,EAAA,0BAAAxC,KAAApD,OAAA4J,OACA,oBAAAhE,EAAA,KAGAiE,EAAA,SAAAA,EAAAlC,GACA,OAAAkC,EAAAjC,MAAAC,KAAAF,IAFA,IAAA/B,EAAA,OAMA,GAFAiE,EAAAjC,MACA,6GACAiC,EAAAjE,GAAA,CAIAyB,EAGAzB,EALAxC,KAGAN,cAAAuE,KAOAyC,QAhjBA,WAijBA1G,KAAAL,qBAAA,EACA,IAaAsE,EAbAzB,EAAA,0BAAAxC,KAAApD,OAAA+J,OACA,oBAAAnE,EAAA,KAGAoE,EAAA,SAAAA,EAAArC,GACA,OAAAqC,EAAApC,MAAAC,KAAAF,IAFA,IAAA/B,EAAA,OAMA,GAFAoE,EAAApC,MACA,6GACAoC,EAAApE,GAAA,CAIAyB,EAGAzB,EALAxC,KAGAJ,cAAAqE,KAiEA1D,KAjoBA,WAioBA,IAAAsG,EAAA7G,KAAA,OAAAU,IAAAC,EAAAC,EAAAC,KAAA,SAAAiG,IAAA,IAAA/F,EAAA3E,EAAA,OAAAuE,EAAAC,EAAAI,KAAA,SAAA+F,GAAA,cAAAA,EAAA7F,KAAA6F,EAAA5F,MAAA,cACAJ,GACAvE,OAAAqK,EAAArK,OACAC,KAAAoK,EAAApK,MAHAsK,EAAA5F,KAAA,EAKAC,OAAAC,EAAA,EAAAD,CAAAL,GALA,OAMA,MADA3E,EALA2K,EAAAzF,MAMAC,OACAsF,EAAAhH,SAAAzD,OAAAoF,QACAqF,EAAAzJ,SAAAhB,OAAAoF,SARA,wBAAAuF,EAAAtF,SAAAqF,EAAAD,KAAAnG,KAYAsG,UChuCeC,GADEC,OAFjB,WAA0B,IAAAC,EAAAnH,KAAaoH,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,kBAA4BF,EAAA,WAAgBG,OAAOC,MAAAP,EAAA,WAAApB,SAAA,SAAA4B,GAAgDR,EAAAzK,WAAAiL,GAAmBC,WAAA,gBAA0BN,EAAA,eAAoBO,OAAOC,MAAA,OAAAC,KAAA,WAA+BT,EAAA,OAAYE,YAAA,0CAAoDF,EAAA,aAAkBE,YAAA,KAAAK,OAAwBpC,KAAA,WAAiBuC,IAAKC,MAAAd,EAAA7B,QAAkB6B,EAAAe,GAAA,cAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAkDE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAxK,SAAAyL,qBAAqD9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOpC,KAAA,QAAA6C,MAAA,KAAAR,MAAA,KAAAS,MAAA,YAA2DpB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,WAA8B,OAAAX,EAAAe,GAAA,KAAAZ,EAAA,eAAwCO,OAAOC,MAAA,OAAAC,KAAA,YAAgCT,EAAA,KAAUE,YAAA,kBAA4BL,EAAAe,GAAA,eAAAf,EAAAe,GAAA,KAAAZ,EAAA,KAA4CE,YAAA,cAAwBL,EAAAe,GAAA,UAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAyCE,YAAA,uBAAiCF,EAAA,WAAgBmB,IAAA,WAAAZ,OAAsBJ,MAAAN,EAAAvK,OAAA8L,cAAA,WAA0CpB,EAAA,OAAYE,YAAA,uBAAiCF,EAAA,OAAYE,YAAA,kBAA4BF,EAAA,gBAAqBO,OAAOC,MAAA,KAAAU,KAAA,QAA0BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,GAAAmJ,SAAA,SAAA4B,GAA+CR,EAAA2B,KAAA3B,EAAAvK,OAAA,KAAA+K,IAAgCC,WAAA,gBAAyB,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,KAAAU,KAAA,QAA0BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,GAAAmJ,SAAA,SAAA4B,GAA+CR,EAAA2B,KAAA3B,EAAAvK,OAAA,KAAA+K,IAAgCC,WAAA,gBAAyB,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,KAAAU,KAAA,QAA0BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,GAAAmJ,SAAA,SAAA4B,GAA+CR,EAAA2B,KAAA3B,EAAAvK,OAAA,KAAA+K,IAAgCC,WAAA,gBAAyB,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,kBAA4BF,EAAA,gBAAqBO,OAAOC,MAAA,MAAAU,KAAA,SAA4BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,IAAAmJ,SAAA,SAAA4B,GAAgDR,EAAA2B,KAAA3B,EAAAvK,OAAA,MAAA+K,IAAiCC,WAAA,iBAA0B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,KAAAU,KAAA,QAA0BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,GAAAmJ,SAAA,SAAA4B,GAA+CR,EAAA2B,KAAA3B,EAAAvK,OAAA,KAAA+K,IAAgCC,WAAA,gBAAyB,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,OAAAU,KAAA,UAA8BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,kBAA2B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,kBAA4BF,EAAA,gBAAqBO,OAAOC,MAAA,OAAAU,KAAA,UAA8BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,kBAA2B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,OAAAU,KAAA,UAA8BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,kBAA2B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,kBAA4BF,EAAA,gBAAqBO,OAAOC,MAAA,OAAAU,KAAA,WAA+BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,MAAAmJ,SAAA,SAAA4B,GAAkDR,EAAA2B,KAAA3B,EAAAvK,OAAA,QAAA+K,IAAmCC,WAAA,mBAA4B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,kBAA4BF,EAAA,gBAAqBO,OAAOC,MAAA,OAAAU,KAAA,UAA8BlB,EAAA,YAAiBO,OAAOc,YAAA,OAAAC,UAAA,GAAAC,SAAA,IAAkDpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,kBAA2B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,UAAAU,KAAA,YAAmClB,EAAA,YAAiBO,OAAOc,YAAA,QAAAC,UAAA,GAAAC,SAAA,IAAmDpB,OAAQC,MAAAP,EAAAvK,OAAA,OAAAmJ,SAAA,SAAA4B,GAAmDR,EAAA2B,KAAA3B,EAAAvK,OAAA,SAAA+K,IAAoCC,WAAA,oBAA6B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,kBAA4BF,EAAA,gBAAqBO,OAAOC,MAAA,OAAAU,KAAA,UAA8BlB,EAAA,YAAiBO,OAAOc,YAAA,OAAAC,UAAA,GAAAC,SAAA,IAAkDpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,kBAA2B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,UAAAU,KAAA,YAAmClB,EAAA,YAAiBO,OAAOc,YAAA,QAAAC,UAAA,GAAAC,SAAA,IAAmDpB,OAAQC,MAAAP,EAAAvK,OAAA,OAAAmJ,SAAA,SAAA4B,GAAmDR,EAAA2B,KAAA3B,EAAAvK,OAAA,SAAA+K,IAAoCC,WAAA,oBAA6B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,mCAA6CL,EAAA,SAAAG,EAAA,OAA2BE,YAAA,SAAAK,OAA4BkB,IAAA5B,EAAAhI,YAAoBgI,EAAA6B,SAAA7B,EAAAe,GAAA,KAAAZ,EAAA,OAAqCE,YAAA,oBAA8BF,EAAA,gBAAqBO,OAAOC,MAAA,WAAAU,KAAA,QAAiCS,YAAA9B,EAAA+B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAA/B,EAAA,KAAgBE,YAAA,SAAmBL,EAAAvK,OAAA,KAAA0K,EAAA,QAAAH,EAAAe,GAAA,SAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,KAAAf,EAAAvK,OAAA,KAAA0K,EAAA,QAAAH,EAAAe,GAAAf,EAAAmC,GAAAnC,EAAAvK,OAAAkC,MAAA,OAAAqI,EAAA6B,KAAA7B,EAAAe,GAAA,KAAAf,EAAAvK,OAAA,GAAA0K,EAAA,QAAAH,EAAAe,GAAA,SAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,KAAAf,EAAAvK,OAAA,GAAA0K,EAAA,QAAAH,EAAAe,GAAAf,EAAAmC,GAAAnC,EAAAvK,OAAAkH,IAAA,OAAAqD,EAAA6B,KAAA7B,EAAAe,GAAA,UAAAqB,GAAApC,EAAAvK,OAAAiH,MAAA,IAAAsD,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,SAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,SAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,SAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,SAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,YAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,gBAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,QAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,WAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,WAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,QAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,UAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,SAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,UAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,SAAAf,EAAAvK,OAAAiH,KAAAyD,EAAA,QAAAH,EAAAe,GAAA,QAAAf,EAAA6B,eAAgrC,GAAA7B,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,oBAA8BF,EAAA,gBAAqBO,OAAOC,MAAA,YAAAU,KAAA,UAAmClB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,kBAA2B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,OAAAU,KAAA,UAA8BlB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,kBAA2B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,KAA8BE,YAAA,cAAwBL,EAAAe,GAAA,eAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAmDE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAvK,OAAAC,SAAAuL,qBAA4D9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOpC,KAAA,QAAA6C,MAAA,KAAAR,MAAA,KAAAS,MAAA,YAA2DpB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,iBAAqCX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,KAAAV,MAAA,UAA4BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,MAAAV,MAAA,UAA4B,GAAAX,EAAAe,GAAA,KAAAZ,EAAA,KAA0BE,YAAA,cAAwBL,EAAAe,GAAA,mBAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAuDE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAvK,OAAAE,YAAAsL,qBAA+D9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOpC,KAAA,QAAA6C,MAAA,KAAAR,MAAA,KAAAS,MAAA,YAA2DpB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,WAA+BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,KAAAV,MAAA,QAA0BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,SAAAV,MAAA,wBAA8CX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,QAAAV,MAAA,QAA6BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,KAAAV,MAAA,QAA0BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,WAA8B,GAAAX,EAAAe,GAAA,KAAAZ,EAAA,KAA0BE,YAAA,cAAwBL,EAAAe,GAAA,eAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA8CE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,yBAAAU,KAAA,aAAoDrB,EAAAqC,GAAArC,EAAA,gBAAAlD,GAAoC,OAAAqD,EAAA,YAAsB6B,IAAAlF,EAAApG,GAAAgK,OAAmBC,MAAA7D,EAAApG,GAAAgL,SAAA,IAA8BpB,OAAQC,MAAAP,EAAAvK,OAAA,MAAAmJ,SAAA,SAAA4B,GAAkDR,EAAA2B,KAAA3B,EAAAvK,OAAA,QAAA+K,IAAmCC,WAAA,kBAA4BT,EAAAe,GAAAf,EAAAmC,GAAArF,EAAAlG,SAA4B,OAAAoJ,EAAAe,GAAA,KAAAZ,EAAA,KAA6BE,YAAA,cAAwBL,EAAAe,GAAA,iBAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAqDE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAvK,OAAAG,aAAAqL,qBAAgE9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOpC,KAAA,QAAA6C,MAAA,KAAAR,MAAA,KAAAS,MAAA,YAA2DpB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,MAAAV,MAAA,UAA4B,GAAAX,EAAAe,GAAA,KAAAZ,EAAA,KAA0BE,YAAA,cAAwBL,EAAAe,GAAA,iBAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAqDE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAvK,OAAAI,aAAAoL,qBAAgE9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOpC,KAAA,QAAA6C,MAAA,KAAAR,MAAA,KAAAS,MAAA,YAA2DpB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,MAAAV,MAAA,UAA4B,GAAAX,EAAAe,GAAA,KAAAZ,EAAA,KAA0BE,YAAA,cAAwBL,EAAAe,GAAA,eAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAmDE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAvK,OAAAK,WAAAmL,qBAA8D9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOpC,KAAA,QAAA6C,MAAA,KAAAR,MAAA,KAAAS,MAAA,YAA2DpB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,gBAAoCX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,KAAAV,MAAA,SAA0B,GAAAX,EAAAe,GAAA,KAAAZ,EAAA,KAA0BE,YAAA,cAAwBL,EAAAe,GAAA,cAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAkDE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAvK,OAAAM,aAAAkL,qBAAgE9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOpC,KAAA,QAAA6C,MAAA,KAAAR,MAAA,KAAAS,MAAA,YAA2DpB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,KAAAV,MAAA,UAA4BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,WAA8B,GAAAX,EAAAe,GAAA,KAAAZ,EAAA,KAA0BE,YAAA,cAAwBL,EAAAe,GAAA,gBAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAoDE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAvK,OAAAO,cAAAiL,qBAAiE9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOpC,KAAA,QAAA6C,MAAA,KAAAR,MAAA,KAAAS,MAAA,YAA2DpB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,WAA8B,GAAAX,EAAAe,GAAA,KAAAZ,EAAA,KAA0BE,YAAA,cAAwBL,EAAAe,GAAA,cAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA6CE,YAAA,iCAA2CF,EAAA,OAAYE,YAAA,kBAA4BF,EAAA,KAAAH,EAAAe,GAAA,kCAAAf,EAAAe,GAAA,KAAAZ,EAAA,KAAAH,EAAAe,GAAA,uBAAAf,EAAAe,GAAA,KAAAZ,EAAA,KAAAH,EAAAe,GAAA,4BAAAf,EAAAe,GAAA,KAAAZ,EAAA,gBAA2LO,OAAOC,MAAA,GAAAU,KAAA,WAA4BrB,EAAAqC,GAAArC,EAAA,gBAAAlD,GAAoC,OAAAqD,EAAA,YAAsB6B,IAAAlF,EAAApG,GAAAgK,OAAmBC,MAAA7D,EAAApG,GAAAgL,SAAA,IAA8BpB,OAAQC,MAAAP,EAAAvK,OAAA,OAAAmJ,SAAA,SAAA4B,GAAmDR,EAAA2B,KAAA3B,EAAAvK,OAAA,SAAA+K,IAAoCC,WAAA,mBAA6BT,EAAAe,GAAAf,EAAAmC,GAAArF,EAAAlG,SAA4B,OAAAoJ,EAAAe,GAAA,KAAAZ,EAAA,KAA6BE,YAAA,cAAwBL,EAAAe,GAAA,eAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA8CE,YAAA,gCAA0CF,EAAA,YAAiBO,OAAOpC,KAAA,WAAAgE,SAAA,GAAAd,YAAA,QAAAE,SAAA,IAAoEpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,kBAA2B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,KAA0BE,YAAA,cAAwBL,EAAAe,GAAA,UAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAyCE,YAAA,8BAAAkC,aAAuDC,QAAA,OAAAC,cAAA,YAAyCzC,EAAA9H,KAA4E8H,EAAA6B,KAA5E1B,EAAA,OAAwBE,YAAA,SAAAK,OAA4BkB,IAAA5B,EAAA/H,gBAAwB+H,EAAAe,GAAA,KAAAf,EAAA,KAAAG,EAAA,aAAkDO,OAAOgC,KAAA,QAAApE,KAAA,WAAgCuC,IAAKC,MAAAd,EAAA/B,SAAmB+B,EAAAe,GAAA,SAAAf,EAAA6B,MAAA,GAAA7B,EAAAe,GAAA,KAAAZ,EAAA,KAAmDE,YAAA,cAAwBL,EAAAe,GAAA,oBAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAmDE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,SAAAU,KAAA,SAAgCrB,EAAAqC,GAAArC,EAAA,kBAAAlD,GAAsC,OAAAqD,EAAA,YAAsB6B,IAAAlF,EAAApG,GAAAgK,OAAmBC,MAAA7D,EAAApG,GAAAgL,SAAA,IAA8BpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,iBAA2BT,EAAAe,GAAAf,EAAAmC,GAAArF,EAAAhG,WAA8B,OAAAkJ,EAAAe,GAAA,KAAAZ,EAAA,OAA+BE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,UAAAU,KAAA,UAAkCrB,EAAAqC,GAAArC,EAAA,kBAAAlD,GAAsC,OAAAqD,EAAA,YAAsB6B,IAAAlF,EAAApG,GAAAgK,OAAmBC,MAAA7D,EAAApG,GAAAgL,SAAA,IAA8BpB,OAAQC,MAAAP,EAAAvK,OAAA,MAAAmJ,SAAA,SAAA4B,GAAkDR,EAAA2B,KAAA3B,EAAAvK,OAAA,QAAA+K,IAAmCC,WAAA,kBAA4BT,EAAAe,GAAAf,EAAAmC,GAAArF,EAAAhG,WAA8B,GAAAkJ,EAAAe,GAAA,KAAAZ,EAAA,OAA2BE,YAAA,kCAA4CL,EAAA,KAAAG,EAAA,aAA6BO,OAAOpC,KAAA,UAAAoE,KAAA,QAA+B7B,IAAKC,MAAAd,EAAAZ,WAAqBY,EAAAe,GAAA,QAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,KAAAZ,EAAA,aAAsDO,OAAOiC,QAAA3C,EAAA1H,qBAAkCuI,IAAK+B,iBAAA,SAAAC,GAAkC7C,EAAA1H,oBAAAuK,MAAiC1C,EAAA,OAAYoC,aAAapB,MAAA,QAAeT,OAAQkB,IAAA5B,EAAAzH,cAAAuK,IAAA,MAAkC9C,EAAAe,GAAA,KAAAZ,EAAA,OAAwBE,YAAA,gBAAAK,OAAmCqC,KAAA,UAAgBA,KAAA,WAAe5C,EAAA,aAAkBO,OAAOgC,KAAA,SAAe7B,IAAKC,MAAA,SAAA+B,GAAyB7C,EAAA1H,qBAAA,MAAkC0H,EAAAe,GAAA,uBAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAsDE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,UAAAU,KAAA,UAAkCrB,EAAAqC,GAAArC,EAAA,kBAAAlD,GAAsC,OAAAqD,EAAA,YAAsB6B,IAAAlF,EAAApG,GAAAgK,OAAmBC,MAAA7D,EAAApG,GAAAgL,SAAA,IAA8BpB,OAAQC,MAAAP,EAAAvK,OAAA,MAAAmJ,SAAA,SAAA4B,GAAkDR,EAAA2B,KAAA3B,EAAAvK,OAAA,QAAA+K,IAAmCC,WAAA,kBAA4BT,EAAAe,GAAAf,EAAAmC,GAAArF,EAAAhG,WAA8B,GAAAkJ,EAAAe,GAAA,KAAAZ,EAAA,OAA2BE,YAAA,kCAA4CL,EAAA,MAAAG,EAAA,aAA8BO,OAAOpC,KAAA,UAAAoE,KAAA,QAA+B7B,IAAKC,MAAAd,EAAAT,WAAqBS,EAAAe,GAAA,QAAAf,EAAA6B,KAAA7B,EAAAe,GAAA,KAAAZ,EAAA,aAAsDO,OAAOiC,QAAA3C,EAAAxH,qBAAkCqI,IAAK+B,iBAAA,SAAAC,GAAkC7C,EAAAxH,oBAAAqK,MAAiC1C,EAAA,OAAYoC,aAAapB,MAAA,QAAeT,OAAQkB,IAAA5B,EAAAvH,cAAAqK,IAAA,MAAkC9C,EAAAe,GAAA,KAAAZ,EAAA,OAAwBE,YAAA,gBAAAK,OAAmCqC,KAAA,UAAgBA,KAAA,WAAe5C,EAAA,aAAkBO,OAAOgC,KAAA,SAAe7B,IAAKC,MAAA,SAAA+B,GAAyB7C,EAAAxH,qBAAA,MAAkCwH,EAAAe,GAAA,uBAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAsDE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,WAAAU,KAAA,WAAmClB,EAAA,YAAiBO,OAAOc,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CpB,OAAQC,MAAAP,EAAAvK,OAAA,MAAAmJ,SAAA,SAAA4B,GAAkDR,EAAA2B,KAAA3B,EAAAvK,OAAA,QAAA+K,IAAmCC,WAAA,mBAA4B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,KAAAU,KAAA,WAA6BlB,EAAA,kBAAuBO,OAAOsC,OAAA,aAAAC,eAAA,aAAAvB,SAAA,GAAApD,KAAA,OAAAkD,YAAA,QAAmGlB,OAAQC,MAAAP,EAAAvK,OAAA,MAAAmJ,SAAA,SAAA4B,GAAkDR,EAAA2B,KAAA3B,EAAAvK,OAAA,QAAA+K,IAAmCC,WAAA,mBAA4B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,KAA8BE,YAAA,cAAwBL,EAAAe,GAAA,cAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA6CE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,SAAAU,KAAA,SAAgCrB,EAAAqC,GAAArC,EAAA,cAAAlD,GAAkC,OAAAqD,EAAA,YAAsB6B,IAAAlF,EAAApG,GAAAgK,OAAmBC,MAAA7D,EAAApG,GAAAgL,SAAA,IAA8BpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,iBAA2BT,EAAAe,GAAAf,EAAAmC,GAAArF,EAAA9F,WAA8B,GAAAgJ,EAAAe,GAAA,KAAAZ,EAAA,gBAAoCE,YAAA,aAAAK,OAAgCC,MAAA,aAAAU,KAAA,iBAA0C,GAAArB,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,UAAAU,KAAA,WAAkClB,EAAA,YAAiBO,OAAOc,YAAA,GAAAE,SAAA,GAAAD,UAAA,IAA8CnB,OAAQC,MAAAP,EAAAvK,OAAA,MAAAmJ,SAAA,SAAA4B,GAAkDR,EAAA2B,KAAA3B,EAAAvK,OAAA,QAAA+K,IAAmCC,WAAA,mBAA4B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,KAAAU,KAAA,YAA8BlB,EAAA,kBAAuBO,OAAOgB,SAAA,GAAAsB,OAAA,aAAAC,eAAA,aAAA3E,KAAA,OAAAkD,YAAA,QAAmGlB,OAAQC,MAAAP,EAAAvK,OAAA,OAAAmJ,SAAA,SAAA4B,GAAmDR,EAAA2B,KAAA3B,EAAAvK,OAAA,SAAA+K,IAAoCC,WAAA,oBAA6B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,KAA8BE,YAAA,cAAwBL,EAAAe,GAAA,eAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA8CE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,SAAAU,KAAA,SAAgCrB,EAAAqC,GAAArC,EAAA,cAAAlD,GAAkC,OAAAqD,EAAA,YAAsB6B,IAAAlF,EAAApG,GAAAgK,OAAmBC,MAAA7D,EAAApG,GAAAgL,SAAA,IAA8BpB,OAAQC,MAAAP,EAAAvK,OAAA,KAAAmJ,SAAA,SAAA4B,GAAiDR,EAAA2B,KAAA3B,EAAAvK,OAAA,OAAA+K,IAAkCC,WAAA,iBAA2BT,EAAAe,GAAAf,EAAAmC,GAAArF,EAAA9F,WAA8B,GAAAgJ,EAAAe,GAAA,KAAAZ,EAAA,gBAAoCE,YAAA,aAAAK,OAAgCC,MAAA,aAAAU,KAAA,iBAA0C,GAAArB,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,aAAAU,KAAA,aAAuClB,EAAA,YAAiBO,OAAOc,YAAA,GAAAE,SAAA,GAAAD,UAAA,IAA8CnB,OAAQC,MAAAP,EAAAvK,OAAA,QAAAmJ,SAAA,SAAA4B,GAAoDR,EAAA2B,KAAA3B,EAAAvK,OAAA,UAAA+K,IAAqCC,WAAA,qBAA8B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,KAAAU,KAAA,YAA8BlB,EAAA,kBAAuBO,OAAOgB,SAAA,GAAAsB,OAAA,aAAAC,eAAA,aAAA3E,KAAA,OAAAkD,YAAA,QAAmGlB,OAAQC,MAAAP,EAAAvK,OAAA,OAAAmJ,SAAA,SAAA4B,GAAmDR,EAAA2B,KAAA3B,EAAAvK,OAAA,SAAA+K,IAAoCC,WAAA,oBAA6B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,KAA8BE,YAAA,cAAwBL,EAAAe,GAAA,WAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA0CE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,SAAAU,KAAA,UAAiCrB,EAAAqC,GAAArC,EAAA,cAAAlD,GAAkC,OAAAqD,EAAA,YAAsB6B,IAAAlF,EAAApG,GAAAgK,OAAmBC,MAAA7D,EAAApG,GAAAgL,SAAA,IAA8BpB,OAAQC,MAAAP,EAAAvK,OAAA,MAAAmJ,SAAA,SAAA4B,GAAkDR,EAAA2B,KAAA3B,EAAAvK,OAAA,QAAA+K,IAAmCC,WAAA,kBAA4BT,EAAAe,GAAAf,EAAAmC,GAAArF,EAAA9F,WAA8B,GAAAgJ,EAAAe,GAAA,KAAAZ,EAAA,gBAAoCE,YAAA,aAAAK,OAAgCC,MAAA,aAAAU,KAAA,iBAA0C,GAAArB,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,4CAAsDF,EAAA,gBAAqBO,OAAOC,MAAA,WAAAU,KAAA,cAAsClB,EAAA,YAAiBO,OAAOc,YAAA,GAAAE,SAAA,GAAAD,UAAA,IAA8CnB,OAAQC,MAAAP,EAAAvK,OAAA,SAAAmJ,SAAA,SAAA4B,GAAqDR,EAAA2B,KAAA3B,EAAAvK,OAAA,WAAA+K,IAAsCC,WAAA,sBAA+B,GAAAT,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCO,OAAOC,MAAA,KAAAU,KAAA,aAA+BlB,EAAA,kBAAuBO,OAAOgB,SAAA,GAAAsB,OAAA,aAAAC,eAAA,aAAA3E,KAAA,OAAAkD,YAAA,QAAmGlB,OAAQC,MAAAP,EAAAvK,OAAA,QAAAmJ,SAAA,SAAA4B,GAAoDR,EAAA2B,KAAA3B,EAAAvK,OAAA,UAAA+K,IAAqCC,WAAA,qBAA8B,OAAAT,EAAAe,GAAA,KAAAZ,EAAA,KAA8BE,YAAA,cAAwBL,EAAAe,GAAA,0BAAAf,EAAAe,GAAA,KAAAZ,EAAA,KAAuDE,YAAA,cAAwBL,EAAAe,GAAA,UAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAA8CE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAA/J,SAAAgL,qBAAqD9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,QAAAV,MAAA,SAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,SAAAV,MAAA,YAAkCX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,WAA8B,aAAAX,EAAAe,GAAA,KAAAZ,EAAA,eAA8CO,OAAOC,MAAA,OAAAC,KAAA,WAA+BT,EAAA,YAAiBE,YAAA,eAAAK,OAAkCM,OAAA,GAAA/L,KAAA+K,EAAAtH,SAAAuI,qBAAqD9L,WAAA,UAAAC,MAAA,WAA0C8L,OAAA,MAAcf,EAAA,mBAAwBO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,QAAAV,MAAA,SAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,UAA8BX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,SAAAV,MAAA,YAAkCX,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCO,OAAOW,KAAA,OAAAV,MAAA,WAA8B,gBAEj7qBuC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACExO,EACAiL,GATF,EAVA,SAAAwD,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/52.1cea8df7e417596b38ff.js", "sourcesContent": ["<template>\r\n    <div class=\"sec-container\">\r\n        <el-tabs v-model=\"activeName\">\r\n            <el-tab-pane label=\"审批指南\" name=\"first\">\r\n                <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n                    <el-button @click=\"ljbl\" class=\"fr\" type=\"success\">立即办理</el-button>\r\n                </div>\r\n                <el-table border class=\"sec-el-table\" :data=\"spznList\"\r\n                    :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                    <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                    <el-table-column prop=\"hjmc\" label=\"办理流程\"></el-table-column>\r\n                </el-table>\r\n            </el-tab-pane>\r\n            <el-tab-pane label=\"审批信息\" name=\"second\">\r\n                <!-- 标题 -->\r\n                <p class=\"sec-title-big\">涉密人员保密审查表</p>\r\n                <p class=\"sec-title\">基本信息</p>\r\n                <div class=\"sec-form-container\">\r\n                    <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n                        <!-- 第一部分包括姓名到常住地公安start -->\r\n                        <div class=\"sec-header-section\">\r\n                            <div class=\"sec-form-left\">\r\n                                <el-form-item label=\"姓名\" prop=\"xm\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.xm\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                                <el-form-item label=\"性别\" prop=\"xb\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.xb\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                                <el-form-item label=\"国籍\" prop=\"gj\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.gj\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                            </div>\r\n                            <div class=\"sec-form-left\">\r\n                                <el-form-item label=\"曾用名\" prop=\"cym\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.cym\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                                <el-form-item label=\"民族\" prop=\"mz\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.mz\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                                <el-form-item label=\"婚姻状况\" prop=\"hyzk\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.hyzk\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                            </div>\r\n                            <div class=\"sec-form-left\">\r\n                                <el-form-item label=\"政治面貌\" prop=\"zzmm\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.zzmm\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                                <el-form-item label=\"联系电话\" prop=\"lxdh\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.lxdh\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                            </div>\r\n                            <div class=\"sec-form-left\">\r\n                                <el-form-item label=\"身份证号\" prop=\"sfzhm\">\r\n                                    <el-input placeholder=\"\" v-model=\"tjlist.sfzhm\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                            </div>\r\n                            <div class=\"sec-form-left\">\r\n                                <el-form-item label=\"户籍地址\" prop=\"hjdz\">\r\n                                    <el-input placeholder=\"（详细）\" v-model=\"tjlist.hjdz\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                                <el-form-item label=\"户籍地公安机关\" prop=\"hjgajg\">\r\n                                    <el-input placeholder=\"街道派出所\" v-model=\"tjlist.hjgajg\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                            </div>\r\n                            <div class=\"sec-form-left\">\r\n                                <el-form-item label=\"常住地址\" prop=\"czdz\">\r\n                                    <el-input placeholder=\"（详细）\" v-model=\"tjlist.czdz\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                                <el-form-item label=\"常住地公安机关\" prop=\"czgajg\">\r\n                                    <el-input placeholder=\"街道派出所\" v-model=\"tjlist.czgajg\" clearable disabled></el-input>\r\n                                </el-form-item>\r\n                            </div>\r\n                            <!-- 电子照片 -->\r\n                            <div class=\"sec-header-pic sec-header-flex\">\r\n                                <img v-if=\"imageUrl\" :src=\"imageUrl\" class=\"avatar\" style=\"\">\r\n                            </div>\r\n                        </div>\r\n                        <!-- 第一部分包括姓名到常住地公安end -->\r\n                        <!-- 部门及职务、职称到涉密等级start -->\r\n                        <div class=\"sec-form-second\">\r\n                            <el-form-item label=\"部门及职务、职称\" prop=\"jbzc\">\r\n                                <!-- <el-input placeholder=\"\" v-model=\"tjlist.jbzc\" clearable disabled></el-input> -->\r\n                                <template slot-scope=\"scope\">\r\n                                    <p class=\"hyzk\">\r\n                                        <span v-if=\"tjlist.bmmc\">部门：</span>\r\n                                        <span v-if=\"tjlist.bmmc\">{{ tjlist.bmmc }}、</span>\r\n                                        <span v-if=\"tjlist.zw\">职务：</span>\r\n                                        <span v-if=\"tjlist.zw\">{{ tjlist.zw }}、</span>\r\n                                        <span v-if=\"tjlist.jbzc != undefined && tjlist.jbzc != ''\">职称：</span>\r\n                                        <span v-if=\"tjlist.jbzc == 1\">省部级</span> <span v-if=\"tjlist.jbzc == 2\">厅局级</span>\r\n                                        <span v-if=\"tjlist.jbzc == 3\">县处级</span> <span v-if=\"tjlist.jbzc == 4\">乡科级及以下</span>\r\n                                        <span v-if=\"tjlist.jbzc == 5\">高级(含正高、副高)</span> <span\r\n                                            v-if=\"tjlist.jbzc == 6\">中级</span> <span v-if=\"tjlist.jbzc == 7\">初级及以下</span>\r\n                                        <span v-if=\"tjlist.jbzc == 8\">试用期人员</span>\r\n                                        <span v-if=\"tjlist.jbzc == 9\">工勤人员</span> <span v-if=\"tjlist.jbzc == 10\">企业职员</span>\r\n                                        <span v-if=\"tjlist.jbzc == 11\">其他</span>\r\n                                    </p>\r\n                                </template>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <div class=\"sec-form-second\">\r\n                            <el-form-item label=\"已（拟）任涉密岗位\" prop=\"gwmc\">\r\n                                <el-input placeholder=\"\" v-model=\"tjlist.gwmc\" clearable disabled></el-input>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"涉密等级\" prop=\"smdj\">\r\n                                <el-input placeholder=\"\" v-model=\"tjlist.smdj\" clearable disabled></el-input>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <!-- 部门及职务、职称到涉密等级end -->\r\n                        <!-- 主要学习及工作经历start -->\r\n                        <p class=\"sec-title\">主要学习及工作经历</p>\r\n                        <el-table border class=\"sec-el-table\" :data=\"tjlist.xxjlList\"\r\n                            :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"qssj\" label=\"起始日期\"></el-table-column>\r\n                            <el-table-column prop=\"zzsj\" label=\"终止日期\"></el-table-column>\r\n                            <el-table-column prop=\"szdw\" label=\"主要学习经历、工作单位\"></el-table-column>\r\n                            <el-table-column prop=\"zw\" label=\"任职情况\"></el-table-column>\r\n                            <el-table-column prop=\"zmr\" label=\"证明人\"></el-table-column>\r\n                        </el-table>\r\n                        <!-- 主要学习及工作经历end -->\r\n                        <!-- 家庭成员及主要社会关系情况start -->\r\n                        <p class=\"sec-title\">家庭成员及主要社会关系情况</p>\r\n                        <el-table border class=\"sec-el-table\" :data=\"tjlist.cyjshgxList\"\r\n                            :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"gxms\" label=\"与本人关系\"></el-table-column>\r\n                            <el-table-column prop=\"xm\" label=\"姓名\"> </el-table-column>\r\n                            <el-table-column prop=\"jwjlqk\" label=\"是否有外籍、境外居留权、长期居留许可\"></el-table-column>\r\n                            <el-table-column prop=\"cgszd\" label=\"单位\"> </el-table-column>\r\n                            <el-table-column prop=\"zw\" label=\"职务\"> </el-table-column>\r\n                            <el-table-column prop=\"zzmm\" label=\"政治面貌\"></el-table-column>\r\n                        </el-table>\r\n                        <!-- 家庭成员及主要社会关系情况end -->\r\n                        <!-- 移居国(境)外情况start -->\r\n                        <p class=\"sec-title\">移居国(境)外情况</p>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"拥有外籍、境外永久居留权或者长期居留许可情况\" prop=\"cqjlxkqk\">\r\n                                <el-radio v-model=\"tjlist.sfcrj\" v-for=\"item in yjgwqk\" :label=\"item.id\" :key=\"item.id\"\r\n                                    disabled>{{ item.yw }}</el-radio>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <!-- 移居国(境)外情况end -->\r\n                        <!-- 持有因公出入境证件情况start -->\r\n                        <p class=\"sec-title\">持有因公出入境证件情况</p>\r\n                        <el-table border class=\"sec-el-table\" :data=\"tjlist.ygrjzjqkList\"\r\n                            :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"fjlb\" label=\"证件名称\"></el-table-column>\r\n                            <el-table-column prop=\"cyqk\" label=\"持有情况\"></el-table-column>\r\n                            <el-table-column prop=\"zjhm\" label=\"证件号码\"></el-table-column>\r\n                            <el-table-column prop=\"yxq\" label=\"有效期\"></el-table-column>\r\n                        </el-table>\r\n                        <!-- 持有因公出入境证件情况end -->\r\n                        <!-- 持有因私出入境证件情况start -->\r\n                        <p class=\"sec-title\">持有因私出入境证件情况</p>\r\n                        <el-table border class=\"sec-el-table\" :data=\"tjlist.ysrjzjqkList\"\r\n                            :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"fjlb\" label=\"证件名称\"></el-table-column>\r\n                            <el-table-column prop=\"cyqk\" label=\"持有情况\"></el-table-column>\r\n                            <el-table-column prop=\"zjhm\" label=\"证件号码\"></el-table-column>\r\n                            <el-table-column prop=\"yxq\" label=\"有效期\"></el-table-column>\r\n                        </el-table>\r\n                        <!-- 持有因私出入境证件情况end -->\r\n                        <!-- 因私出国(境)情况start -->\r\n                        <p class=\"sec-title\">因私出国(境)情况</p>\r\n                        <el-table border class=\"sec-el-table\" :data=\"tjlist.yscgqkList\"\r\n                            :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"qssj\" label=\"起始日期\"></el-table-column>\r\n                            <el-table-column prop=\"zzsj\" label=\"终止日期\"></el-table-column>\r\n                            <el-table-column prop=\"cggj\" label=\"近3年所到国家或地区\"> </el-table-column>\r\n                            <el-table-column prop=\"sy\" label=\"事由\"></el-table-column>\r\n                        </el-table>\r\n                        <!-- 因私出国(境)情况end -->\r\n                        <!-- 接受境外资助情况start -->\r\n                        <p class=\"sec-title\">接受境外资助情况</p>\r\n                        <el-table border class=\"sec-el-table\" :data=\"tjlist.jsjwzzqkList\"\r\n                            :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"zzsj\" label=\"起始日期\"></el-table-column>\r\n                            <el-table-column prop=\"gj\" label=\"国家地区\"></el-table-column>\r\n                            <el-table-column prop=\"jgmc\" label=\"机构名称\"></el-table-column>\r\n                            <el-table-column prop=\"zznr\" label=\"资助内容\"></el-table-column>\r\n                        </el-table>\r\n                        <!-- 接受境外资助情况end -->\r\n                        <!-- 处分或者违法犯罪情况start -->\r\n                        <p class=\"sec-title\">处分或者违法犯罪情况</p>\r\n                        <el-table border class=\"sec-el-table\" :data=\"tjlist.clhwffzqkList\"\r\n                            :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"cfsj\" label=\"起始日期\"></el-table-column>\r\n                            <el-table-column prop=\"cfjg\" label=\"处理结果\"> </el-table-column>\r\n                            <el-table-column prop=\"cfyy\" label=\"处理原因\"></el-table-column>\r\n                            <el-table-column prop=\"cfdw\" label=\"处理机构\"> </el-table-column>\r\n                        </el-table>\r\n                        <!-- 处分或者违法犯罪情况end -->\r\n                        <!-- 配偶子女有关情况start -->\r\n                        <p class=\"sec-title\">配偶子女有关情况</p>\r\n                        <div class=\"sec-form-third haveBorderTop\">\r\n                            <div class=\"sec-left-text\">\r\n                                <p>1.在国境内外从事反对、攻击党和国家或者颠覆国家政权活动</p>\r\n                                <p>2.被列为影响国家安全重点管控人员</p>\r\n                                <p>3.因危害国家安全的行为收到处分或者处罚</p>\r\n                            </div>\r\n                            <el-form-item label=\"\" prop=\"qscfqk\">\r\n                                <el-radio v-model=\"tjlist.qscfqk\" v-for=\"item in yjgwqk\" :label=\"item.id\" :key=\"item.id\"\r\n                                    disabled>{{ item.yw }}</el-radio>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <!-- 配偶子女有关情况end -->\r\n                        <!-- 其他需要说明的情况start -->\r\n                        <p class=\"sec-title\">其他需要说明的情况</p>\r\n                        <div class=\"sec-form-four haveBorderTop\">\r\n                            <el-input type=\"textarea\" autosize placeholder=\"请输入内容\" v-model=\"tjlist.qtqk\" disabled>\r\n                            </el-input>\r\n                        </div>\r\n                        <!-- 其他需要说明的情况end -->\r\n                        <!-- 本人承诺start -->\r\n                        <p class=\"sec-title\">本人承诺</p>\r\n                        <div class=\"sec-form-five haveBorderTop\" style=\"display:flex;align-items: center;\">\r\n                            <!-- <img v-if=\"imageUrlbrcn\" :src=\"imageUrlbrcn\" class=\"avatar\" style=\"\"> -->\r\n                            <img v-if=\"!ylxy\" :src=\"imageUrlbrcn\" class=\"avatar\" style=\"\">\r\n                            <el-button size=\"small\" type=\"primary\" v-if=\"ylxy\" @click=\"yulan\">预 览</el-button>\r\n                        </div>\r\n                        <!-- 本人承诺end -->\r\n                        <p class=\"sec-title\">上岗保密教育、签订保密承诺书</p>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"保密教育培训\" prop=\"pxqk\">\r\n                                <el-radio v-model=\"tjlist.pxqk\" v-for=\"item in bmjysfwc\" :label=\"item.id\" :key=\"item.id\"\r\n                                    disabled>{{\r\n                                        item.sfwc }}</el-radio>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"签订保密承诺书\" prop=\"cnsqk\">\r\n                                <el-radio v-model=\"tjlist.cnsqk\" v-for=\"item in bmjysfwc\" :label=\"item.id\" :key=\"item.id\"\r\n                                    disabled>{{\r\n                                        item.sfwc }}</el-radio>\r\n                            </el-form-item>\r\n                            <div class=\"sec-form-fddw sec-header-flex\">\r\n                                <el-button type=\"primary\" @click=\"bmcnsyl\" size=\"mini\"  v-if=\"show\">预览</el-button>\r\n                                <el-dialog :visible.sync=\"dialogVisible_bmcns\">\r\n                                    <img :src=\"bmcnsImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                                        <el-button size=\"small\" @click=\"dialogVisible_bmcns = false\">取 消</el-button>\r\n                                    </div>\r\n                                </el-dialog>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"签订保密协议书\" prop=\"xysqk\">\r\n                                <el-radio v-model=\"tjlist.xysqk\" v-for=\"item in bmjysfwc\" :label=\"item.id\" :key=\"item.id\"\r\n                                    disabled>{{\r\n                                        item.sfwc }}</el-radio>\r\n                            </el-form-item>\r\n                            <div class=\"sec-form-fddw sec-header-flex\">\r\n                                <el-button type=\"primary\" @click=\"bmxysyl\" size=\"mini\" v-if=\"show1\">预览</el-button>\r\n                                <el-dialog :visible.sync=\"dialogVisible_bmxys\">\r\n                                    <img :src=\"bmxysImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                                        <el-button size=\"small\" @click=\"dialogVisible_bmxys = false\">取 消</el-button>\r\n                                    </div>\r\n                                </el-dialog>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"人力资源部审批人\" prop=\"rlspr\">\r\n                                <el-input placeholder=\"\" v-model=\"tjlist.rlspr\" clearable disabled></el-input>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"日期\" prop=\"cnsrq\">\r\n                                <el-date-picker v-model=\"tjlist.cnsrq\" format=\"yyyy-MM-dd\" value-format=\"yyyy-MM-dd\"\r\n                                    disabled type=\"date\" placeholder=\"选择日期\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <p class=\"sec-title\">所在部门审查情况</p>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"信息属实，拟\" prop=\"bmsc\">\r\n                                <!-- <el-radio v-model=\"tjlist.bmsc\" v-for=\"item in scqk\" :label=\"item.id\" @change=\"chRadio\"\r\n                                    :disabled=\"disabled1\" :key=\"item.id\">{{\r\n                                        item.sfty }}</el-radio> -->\r\n                                <el-radio v-model=\"tjlist.bmsc\" v-for=\"item in scqk\" :label=\"item.id\" disabled\r\n                                    :key=\"item.id\">{{\r\n                                        item.sfty }}</el-radio>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"该同志在涉密岗位工作\" prop=\"gtzzsmgwgz\" class=\"gtzzsmgwgz\"></el-form-item>\r\n                        </div>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"部门领导审批人\" prop=\"bmspr\">\r\n                                <!-- <el-input placeholder=\"\" :disabled=\"disabled1\" v-model=\"tjlist.bmspr\"\r\n                                    clearable></el-input> -->\r\n                                <el-input placeholder=\"\" disabled v-model=\"tjlist.bmspr\" clearable></el-input>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"日期\" prop=\"bmscrq\">\r\n                                <!-- <el-date-picker :disabled=\"disabled1\" v-model=\"tjlist.bmscrq\" format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                                </el-date-picker> -->\r\n                                <el-date-picker disabled v-model=\"tjlist.bmscrq\" format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <p class=\"sec-title\">人力资源部审查情况</p>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"信息属实，拟\" prop=\"rlsc\">\r\n                                <!-- <el-radio v-model=\"tjlist.rlsc\" v-for=\"item in scqk\" :label=\"item.id\" @change=\"chRadio\"\r\n                                    :disabled=\"disabled2\" :key=\"item.id\">{{\r\n                                        item.sfty }}</el-radio> -->\r\n                                <el-radio v-model=\"tjlist.rlsc\" v-for=\"item in scqk\" :label=\"item.id\" disabled\r\n                                    :key=\"item.id\">{{\r\n                                        item.sfty }}</el-radio>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"该同志在涉密岗位工作\" prop=\"gtzzsmgwgz\" class=\"gtzzsmgwgz\"></el-form-item>\r\n                        </div>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"人力资源部领导审批人\" prop=\"rlldspr\">\r\n                                <!-- <el-input placeholder=\"\" :disabled=\"disabled2\" v-model=\"tjlist.rlldspr\"\r\n                                    clearable></el-input> -->\r\n                                <el-input placeholder=\"\" disabled v-model=\"tjlist.rlldspr\" clearable></el-input>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"日期\" prop=\"rlscrq\">\r\n                                <!-- <el-date-picker :disabled=\"disabled2\" v-model=\"tjlist.rlscrq\" format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                                </el-date-picker> -->\r\n                                <el-date-picker disabled v-model=\"tjlist.rlscrq\" format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <p class=\"sec-title\">保密办意见</p>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"信息属实，拟\" prop=\"bmbsc\">\r\n                                <!-- <el-radio v-model=\"tjlist.bmbsc\" v-for=\"item in scqk\" :label=\"item.id\" @change=\"chRadio\"\r\n                                    :disabled=\"disabled3\" :key=\"item.id\">{{\r\n                                        item.sfty }}</el-radio> -->\r\n                                <el-radio v-model=\"tjlist.bmbsc\" v-for=\"item in scqk\" :label=\"item.id\" disabled\r\n                                    :key=\"item.id\">{{\r\n                                        item.sfty }}</el-radio>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"该同志在涉密岗位工作\" prop=\"gtzzsmgwgz\" class=\"gtzzsmgwgz\"></el-form-item>\r\n                        </div>\r\n                        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n                            <el-form-item label=\"保密办领导审批人\" prop=\"bmbldspr\">\r\n                                <!-- <el-input placeholder=\"\" :disabled=\"disabled3\" v-model=\"tjlist.bmbldspr\"\r\n                                    clearable></el-input> -->\r\n                                <el-input placeholder=\"\" disabled v-model=\"tjlist.bmbldspr\" clearable></el-input>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"日期\" prop=\"bmbscrq\">\r\n                                <!-- <el-date-picker :disabled=\"disabled3\" v-model=\"tjlist.bmbscrq\" format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                                </el-date-picker> -->\r\n                                <el-date-picker disabled v-model=\"tjlist.bmbscrq\" format=\"yyyy-MM-dd\"\r\n                                    value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                                </el-date-picker>\r\n                            </el-form-item>\r\n                        </div>\r\n                        <p class=\"sec-title\">备注：涉密人员上岗审查、在岗复审均填本表</p>\r\n\r\n                        <p class=\"sec-title\">轨迹处理</p>\r\n                        <el-table border class=\"sec-el-table\" :data=\"gjclList\"\r\n                            :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                            <el-table-column prop=\"hjmc\" label=\"办理环节\"></el-table-column>\r\n                            <el-table-column prop=\"clrid\" label=\"办理人\"></el-table-column>\r\n                            <el-table-column prop=\"bllx\" label=\"办理类型\"></el-table-column>\r\n                            <el-table-column prop=\"clyj\" label=\"办理意见\"></el-table-column>\r\n                            <el-table-column prop=\"xybclr\" label=\"下一步办理人\"></el-table-column>\r\n                            <el-table-column prop=\"clsj\" label=\"办理时间\"></el-table-column>\r\n                        </el-table>\r\n\r\n                        <!-- 底部操作按钮start -->\r\n                        <!-- <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n                            <el-dropdown class=\"fr ml10\">\r\n                                <el-button type=\"primary\" :disabled=\"btnsfth\">\r\n                                    退回\r\n                                </el-button>\r\n                                <el-dropdown-menu slot=\"dropdown\">\r\n                                    <el-dropdown-item @click.native=\"save(2)\">至上步办理人</el-dropdown-item>\r\n                                    <el-dropdown-item @click.native=\"save(3)\">至发起人</el-dropdown-item>\r\n                                </el-dropdown-menu>\r\n                            </el-dropdown>\r\n                            <el-button @click=\"save(1)\" :disabled=\"btnsftg\" class=\"fr\" type=\"success\">通过</el-button>\r\n                        </div> -->\r\n                        <!-- 底部操作按钮end -->\r\n                    </el-form>\r\n                </div>\r\n                <!-- 涉密人员任用审查列表end -->\r\n                <!-- 发起申请弹框start -->\r\n                <!-- <el-dialog title=\"人员选择\" :close-on-click-modal=\"false\" :visible.sync=\"dialogVisible\" width=\"40%\">\r\n                    <div class=\"dlFqsqContainer\">\r\n                        <label for=\"\">部门:</label>\r\n                        <el-input class=\"input1\" v-model=\"formInline.bmmc\" clearable placeholder=\"部门\"></el-input>\r\n                        <label for=\"\">姓名:</label>\r\n                        <el-input class=\"input2\" v-model=\"formInline.xm\" clearable placeholder=\"姓名\"></el-input>\r\n                        <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\"\r\n                            @click=\"onSubmit\">查询</el-button>\r\n                        <el-table class=\"tb-container\" ref=\"multipleTable\" :data=\"smryList\" border\r\n                            :header-cell-style=\"headerCellStyle\" stripe @selection-change=\"selectRow\" @select=\"handleSelect\"\r\n                            @row-click=\"handleRowClick\">\r\n                            <el-table-column type=\"selection\" width=\"55\" align=\"center\"> </el-table-column>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"xm\" label=\"姓名\"></el-table-column>\r\n                            <el-table-column prop=\"bmmc\" label=\"部门\"></el-table-column>\r\n                            <el-table-column prop=\"gwmc\" label=\"岗位\"></el-table-column>\r\n                        </el-table>\r\n                        <el-pagination class=\"paginationContainer\" background @current-change=\"handleCurrentChange\"\r\n                            @size-change=\"handleSizeChange\" :pager-count=\"5\" :current-page=\"page\"\r\n                            :page-sizes=\"[5, 10, 20, 30]\" :page-size=\"pageSize\"\r\n                            layout=\"total, prev, pager, sizes,next, jumper\" :total=\"total\">\r\n                        </el-pagination>\r\n                    </div>\r\n                    <span slot=\"footer\" class=\"dialog-footer\">\r\n                        <el-button type=\"primary\" v-if=\"xsyc\" @click=\"submit('formName')\">确 定</el-button>\r\n                        <el-button type=\"warning\" @click=\"dialogVisible = false\">关 闭</el-button>\r\n                    </span>\r\n                </el-dialog> -->\r\n                <!-- 发起申请弹框end -->\r\n            </el-tab-pane>\r\n            <el-tab-pane label=\"流程跟踪\" name=\"third\">\r\n                <el-table border class=\"sec-el-table\" :data=\"lcgzList\"\r\n                    :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                    <el-table-column prop=\"hjmc\" label=\"办理环节\"></el-table-column>\r\n                    <el-table-column prop=\"clrid\" label=\"办理人\"></el-table-column>\r\n                    <el-table-column prop=\"bllx\" label=\"办理类型\"></el-table-column>\r\n                    <el-table-column prop=\"clyj\" label=\"办理意见\"></el-table-column>\r\n                    <el-table-column prop=\"xybclr\" label=\"下一步办理人\"></el-table-column>\r\n                    <el-table-column prop=\"clsj\" label=\"办理时间\"></el-table-column>\r\n                </el-table>\r\n            </el-tab-pane>\r\n        </el-tabs>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\nimport {\r\n    //审批指南\r\n    getBlzn,\r\n    //审批信息\r\n    getRyscInfoBySlid,\r\n    //审批信息\r\n    getZgfsInfoBySlid,\r\n    //判断实例所处环节\r\n    getSchj,\r\n    //事项审核\r\n    getSxsh,\r\n    //查询审批用户列表\r\n    getSpUserList,\r\n    //非第一环节选择审批人\r\n    tjclr,\r\n    //修改任用审查详情记录\r\n    updateRysc,\r\n    //流程跟踪\r\n    getSpGjxx,\r\n} from '../../../../api/wdgz'\r\nimport AddLineTable from \"../../../components/common/addLineTable.vue\";   //人工纠错组件\r\nexport default {\r\n    components: {\r\n        AddLineTable\r\n    },\r\n    props: {},\r\n    data() {\r\n        return {\r\n            // table 行样式\r\n            headerCellStyle: {\r\n                background: '#EEF7FF',\r\n                color: '#4D91F8'\r\n            },\r\n            fwdyid: '',\r\n            slid: '',\r\n            activeName: 'second',\r\n            //审批指南\r\n            spznList: [],\r\n            // form表单提交数据\r\n            //审批信息\r\n            tjlist: {\r\n                // 主要学习及工作经历\r\n                xxjlList: [],\r\n                // 家庭成员及社会关系\r\n                cyjshgxList: [],\r\n                // 持有因公出入境证件情况\r\n                ygrjzjqkList: [],\r\n                // 持有因私出入境证件情况\r\n                ysrjzjqkList: [],\r\n                // 因私出国(境)情况\r\n                yscgqkList: [],\r\n                // 接受境外资助情况\r\n                jsjwzzqkList: [],\r\n                // 处分或者违法犯罪情况\r\n                clhwffzqkList: [],\r\n            },\r\n            //轨迹处理\r\n            gjclList: [],\r\n            upccLsit: {},\r\n            //判断实例所处环节\r\n            disabled1: false,\r\n            disabled2: false,\r\n            disabled3: false,\r\n            btnsftg: true,\r\n            btnsfth: true,\r\n            jgyf: '',\r\n            //性别\r\n            xb: [{\r\n                xb: '男',\r\n                id: 1\r\n            },\r\n            {\r\n                xb: '女',\r\n                id: 2\r\n            },\r\n            ],\r\n            //移居国(境)外情况\r\n            yjgwqk: [{\r\n                yw: '有',\r\n                id: 1\r\n            },\r\n            {\r\n                yw: '无',\r\n                id: 0\r\n            },\r\n            ],\r\n            //上岗保密教育、签订保密承诺书\r\n            bmjysfwc: [\r\n                {\r\n                    sfwc: '已完成',\r\n                    id: 1\r\n                },\r\n                {\r\n                    sfwc: '未完成',\r\n                    id: 0\r\n                },\r\n            ],\r\n            scqk: [\r\n                {\r\n                    sfty: '同意',\r\n                    id: 1\r\n                },\r\n                {\r\n                    sfty: '不同意',\r\n                    id: 0\r\n                },\r\n            ],\r\n            // 政治面貌下拉选项\r\n            zzmmoptions: [],\r\n            sltshow: '', // 文档的缩略图显示\r\n            fileList: [],\r\n            dialogVisible: false,\r\n            fileRow: '',\r\n            //人员任用\r\n            smryList: [],\r\n            page: 1,\r\n            pageSize: 10,\r\n            total: 0,\r\n            formInline: {\r\n                'bmmc': '',\r\n                'xm': ''\r\n            }, // 搜索条件\r\n            selectlistRow: [], //列表的值\r\n            xsyc: true,\r\n            mbhjid: '',\r\n            imageUrl: '',\r\n            imageUrlbrcn: '',\r\n            ylxy: true,\r\n            file: {},\r\n            bmcnssmj: '',\r\n            bmxyssmj: '',\r\n            //保密承诺书预览\r\n            dialogVisible_bmcns: false,\r\n            bmcnsImageUrl: '',\r\n            //保密承诺书预览\r\n            dialogVisible_bmxys: false,\r\n            bmxysImageUrl: '',\r\n            //流程跟踪\r\n            lcgzList: [],\r\n\r\n        }\r\n    },\r\n    computed: {},\r\n    mounted() {\r\n        this.fwdyid = this.$route.query.fwdyid\r\n        console.log(\"this.fwdyid\", this.fwdyid);\r\n        this.slid = this.$route.query.slid\r\n        console.log(\"this.slid\", this.slid);\r\n        //审批指南初始化列表\r\n        this.spzn()\r\n        //审批信息初始化列表\r\n        // this.spxxxgcc()\r\n        this.spxx()\r\n        //判断实例所处环节\r\n        // this.pdschj()\r\n        // //事项审核\r\n        // this.sxsh()\r\n        //初始化el-dialog列表数据\r\n        // this.splist()\r\n        //流程跟踪初始化列表\r\n        this.lcgz()\r\n\r\n    },\r\n    methods: {\r\n        //审批指南\r\n        //审批指南初始化列表\r\n        async spzn() {\r\n            let params = {\r\n                fwdyid: this.fwdyid,\r\n            }\r\n            let data = await getBlzn(params)\r\n            if (data.code == 10000) {\r\n                this.spznList = data.data.content\r\n            }\r\n        },\r\n        //审批信息\r\n        //审批信息初始化数据\r\n        // async spxxxgcc() {\r\n        //     // let params = {\r\n        //     //     slid: this.slid\r\n        //     // }\r\n        //     // let data = await getRyscInfoBySlid(params)\r\n        //     // this.upccLsit = data\r\n        //     // console.log('this.upccLsit', this.upccLsit);\r\n        //     // this.chRadio()\r\n        // },\r\n        // async spxx() {\r\n        //     let params = {\r\n        //         slid: this.slid\r\n        //     }\r\n        //     let data ;\r\n        //     data= await getRyscInfoBySlid(params);\r\n        //     this.tjlist = data.rysc\r\n        //     if (data == '') {\r\n        //         data= await getZgfsInfoBySlid(params);\r\n        //         this.tjlist = data.zgfs\r\n        //     }\r\n        //     console.log(data);\r\n\r\n\r\n        //     const iamgeBase64 = \"data:image/jpeg;base64,\" + this.tjlist.zp;\r\n        //     if (typeof iamgeBase64 === \"string\") {\r\n        //         // 复制某条消息\r\n        //         if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        //         function validDataUrl(s) {\r\n        //             return validDataUrl.regex.test(s);\r\n        //         }\r\n        //         validDataUrl.regex =\r\n        //             /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        //         if (validDataUrl(iamgeBase64)) {\r\n        //             // debugger;\r\n        //             let that = this;\r\n\r\n        //             function previwImg(item) {\r\n        //                 that.imageUrl = item;\r\n        //             }\r\n        //             previwImg(iamgeBase64);\r\n        //         }\r\n        //     }\r\n        //     if (this.tjlist.xb == 1) {\r\n        //         this.tjlist.xb = '男'\r\n        //     } else if (this.tjlist.xb == 2) {\r\n        //         this.tjlist.xb = '女'\r\n        //     }\r\n        //     if (this.tjlist.hyzk == 1) {\r\n        //         this.tjlist.hyzk = '已婚'\r\n        //     } else if (this.tjlist.hyzk == 0) {\r\n        //         this.tjlist.hyzk = '未婚'\r\n        //     }\r\n        //     if (this.tjlist.zzmm == 1) {\r\n        //         this.tjlist.zzmm = '中共党员'\r\n        //     } else if (this.tjlist.zzmm == 2) {\r\n        //         this.tjlist.zzmm = '团员'\r\n        //     } else if (this.tjlist.zzmm == 3) {\r\n        //         this.tjlist.zzmm = '民主党派'\r\n        //     } else if (this.tjlist.zzmm == 4) {\r\n        //         this.tjlist.zzmm = '群众'\r\n        //     }\r\n        //     if (this.tjlist.smdj == 1) {\r\n        //         this.tjlist.smdj = '核心'\r\n        //     } else if (this.tjlist.smdj == 2) {\r\n        //         this.tjlist.smdj = '重要'\r\n        //     } else if (this.tjlist.smdj == 3) {\r\n        //         this.tjlist.smdj = '一般'\r\n        //     }\r\n        //     //主要学习及工作经历\r\n        //     this.tjlist.xxjlList = data.ryglRyscScjlList\r\n        //     //家庭成员及主要社会关系情况\r\n        //     let jtarr = []\r\n        //     data.ryglRyscJtcyList.forEach((item) => {\r\n        //         if (item.jwjlqk == 0) {\r\n        //             item.jwjlqk = '否'\r\n        //         } else if (item.jwjlqk == 1) {\r\n        //             item.jwjlqk = '是'\r\n        //         }\r\n        //         jtarr.push(item)\r\n        //         this.tjlist.cyjshgxList = jtarr\r\n        //     })\r\n        //     //持有因公出入境证件情况\r\n        //     let arr = []\r\n        //     let list = []\r\n        //     data.ryglRyscSwzjList.forEach((item) => {\r\n        //         if (item.cyqk == 0) {\r\n        //             item.cyqk = '无'\r\n        //         } else if (item.cyqk == 1) {\r\n        //             item.cyqk = '有'\r\n        //         }\r\n        //         if (item.fjlb == 1 || item.fjlb == 2 || item.fjlb == 3) {\r\n        //             if (item.fjlb == 1) {\r\n        //                 item.fjlb = '护照'\r\n        //             } else if (item.fjlb == 2) {\r\n        //                 item.fjlb = '港澳通行证'\r\n        //             } else if (item.fjlb == 3) {\r\n        //                 item.fjlb = '台湾通行证'\r\n        //             }\r\n        //             arr.push(item)\r\n        //             this.tjlist.ygrjzjqkList = arr\r\n        //         }\r\n        //         //持有因公出入境证件情况\r\n        //         else if (item.fjlb == 4 || item.fjlb == 5 || item.fjlb == 6 || item.fjlb == 7) {\r\n        //             if (item.fjlb == 4) {\r\n        //                 item.fjlb = '护照'\r\n        //             } else if (item.fjlb == 5) {\r\n        //                 item.fjlb = '港澳通行证'\r\n        //             } else if (item.fjlb == 6) {\r\n        //                 item.fjlb = '台湾通行证'\r\n        //             } else if (item.fjlb == 7) {\r\n        //                 item.fjlb = '境外永久居留权长期居留许可证件'\r\n        //             }\r\n        //             list.push(item)\r\n        //             this.tjlist.ysrjzjqkList = list\r\n        //         }\r\n        //     })\r\n        //     //因私出国(境)情况\r\n        //     this.tjlist.yscgqkList = data.ryglRyscYccgList\r\n        //     //接受境外资助情况\r\n        //     this.tjlist.jsjwzzqkList = data.ryglRyscJwzzqkList\r\n        //     //处分或者违法犯罪情况\r\n        //     this.tjlist.clhwffzqkList = data.ryglRyscCfjlList\r\n\r\n        // },\r\n        async spxx() {\r\n            let params = {\r\n                slid: this.slid\r\n            }\r\n            let data;\r\n            let lbxx = {};\r\n            let ryglScjlList = [];\r\n            let ryglJtcyList = [];\r\n            let ryglSwzjList = [];\r\n            let ryglYccgList = [];\r\n            let ryglJwzzqkList = [];\r\n            let ryglCfjlList = [];\r\n            console.log(this.lx);\r\n\r\n            data = await getRyscInfoBySlid(params);\r\n            console.log(data);\r\n            if (data.code == 9999 || data == '') {\r\n                data = await getZgfsInfoBySlid(params)\r\n                lbxx = data.zgfs;\r\n                ryglScjlList = data.ryglZgfsScjlList;\r\n                ryglJtcyList = data.ryglZgfsJtcyList;\r\n                ryglSwzjList = data.ryglZgfsSwzjList;\r\n                ryglYccgList = data.ryglZgfsYccgList;\r\n                ryglJwzzqkList = data.ryglZgfsJwzzqkList;\r\n                ryglCfjlList = data.ryglZgfsCfjlList;\r\n            } else {\r\n                lbxx = data.rysc;\r\n                ryglScjlList = data.ryglRyscScjlList;\r\n                ryglJtcyList = data.ryglRyscJtcyList;\r\n                ryglSwzjList = data.ryglRyscSwzjList;\r\n                ryglYccgList = data.ryglRyscYccgList;\r\n                ryglJwzzqkList = data.ryglRyscJwzzqkList;\r\n                ryglCfjlList = data.ryglRyscCfjlList;\r\n            }\r\n            console.log(lbxx);\r\n            lbxx.jbzcComputed = lbxx.jbzc == 1 ? '省部级' : lbxx.jbzc == 2 ? '厅局级' : lbxx.jbzc == 3 ?\r\n                '县处级' : lbxx.jbzc == 4 ? '乡科级及以下' : lbxx.jbzc == 5 ? '高级(含正高、副高)' : lbxx.jbzc == 6 ? '中级' :\r\n                    lbxx.jbzc == 7 ? '初级及以下' : lbxx.jbzc == 8 ? '试用期人员' : lbxx.jbzc == 9 ? '工勤人员' : lbxx\r\n                        .jbzc == 10 ? '企业职员' : lbxx.jbzc == 11 ? '其他' : ''\r\n            let bmC = lbxx.bmmc != '' ? '部门：' + lbxx.bmmc + '、' : ''\r\n            let zwC = lbxx.zw != '' ? '职务：' + lbxx.zw + '、' : ''\r\n            lbxx.bmzwzcComputed = bmC + zwC + '职称：' + lbxx.jbzcComputed\r\n            this.tjlist = lbxx\r\n            //主要学习及工作经历\r\n            this.tjlist.xxjlList = ryglScjlList\r\n            //家庭成员及主要社会关系情况\r\n            let jtarr = []\r\n            ryglJtcyList.forEach((item) => {\r\n                if (item.jwjlqk == 0) {\r\n                    item.jwjlqk = '否'\r\n                } else if (item.jwjlqk == 1) {\r\n                    item.jwjlqk = '是'\r\n                }\r\n                jtarr.push(item)\r\n                this.tjlist.cyjshgxList = jtarr\r\n            })\r\n            //持有因公出入境证件情况\r\n            let arr = []\r\n            let list = []\r\n            ryglSwzjList.forEach((item) => {\r\n                if (item.cyqk == 0) {\r\n                    item.cyqk = '无'\r\n                } else if (item.cyqk == 1) {\r\n                    item.cyqk = '有'\r\n                }\r\n                if (item.fjlb == 1 || item.fjlb == 2 || item.fjlb == 3) {\r\n                    if (item.fjlb == 1) {\r\n                        item.fjlb = '护照'\r\n                    } else if (item.fjlb == 2) {\r\n                        item.fjlb = '港澳通行证'\r\n                    } else if (item.fjlb == 3) {\r\n                        item.fjlb = '台湾通行证'\r\n                    }\r\n                    arr.push(item)\r\n                    this.tjlist.ygrjzjqkList = arr\r\n                }\r\n                //持有因公出入境证件情况\r\n                else if (item.fjlb == 4 || item.fjlb == 5 || item.fjlb == 6 || item.fjlb == 7) {\r\n                    if (item.fjlb == 4) {\r\n                        item.fjlb = '护照'\r\n                    } else if (item.fjlb == 5) {\r\n                        item.fjlb = '港澳通行证'\r\n                    } else if (item.fjlb == 6) {\r\n                        item.fjlb = '台湾通行证'\r\n                    } else if (item.fjlb == 7) {\r\n                        item.fjlb = '境外永久居留权长期居留许可证件'\r\n                    }\r\n                    list.push(item)\r\n                    this.tjlist.ysrjzjqkList = list\r\n                }\r\n            })\r\n            //因私出国(境)情况\r\n            this.tjlist.yscgqkList = ryglYccgList\r\n            //接受境外资助情况\r\n            this.tjlist.jsjwzzqkList = ryglJwzzqkList\r\n            //处分或者违法犯罪情况\r\n            this.tjlist.clhwffzqkList = ryglCfjlList\r\n            const iamgeBase64 = \"data:image/jpeg;base64,\" + this.tjlist.zp;\r\n            if (typeof iamgeBase64 === \"string\") {\r\n                // 复制某条消息\r\n                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n                function validDataUrl(s) {\r\n                    return validDataUrl.regex.test(s);\r\n                }\r\n                validDataUrl.regex =\r\n                    /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n                if (validDataUrl(iamgeBase64)) {\r\n                    // debugger;\r\n                    let that = this;\r\n\r\n                    function previwImg(item) {\r\n                        that.imageUrl = item;\r\n                    }\r\n                    previwImg(iamgeBase64);\r\n                }\r\n            }\r\n            if (this.tjlist.brcn != '') {\r\n                this.yldis = false\r\n            } else {\r\n                this.yldis = true\r\n            }\r\n            if (this.tjlist.xb == 1) {\r\n                this.tjlist.xb = '男'\r\n            } else if (this.tjlist.xb == 2) {\r\n                this.tjlist.xb = '女'\r\n            }\r\n            if (this.tjlist.hyzk == 1) {\r\n                this.tjlist.hyzk = '已婚'\r\n            } else if (this.tjlist.hyzk == 0) {\r\n                this.tjlist.hyzk = '未婚'\r\n            }\r\n            if (this.tjlist.zzmm == 1) {\r\n                this.tjlist.zzmm = '中共党员'\r\n            } else if (this.tjlist.zzmm == 2) {\r\n                this.tjlist.zzmm = '团员'\r\n            } else if (this.tjlist.zzmm == 3) {\r\n                this.tjlist.zzmm = '民主党派'\r\n            } else if (this.tjlist.zzmm == 4) {\r\n                this.tjlist.zzmm = '群众'\r\n            }\r\n            if (this.tjlist.smdj == 1) {\r\n                this.tjlist.smdj = '核心'\r\n            } else if (this.tjlist.smdj == 2) {\r\n                this.tjlist.smdj = '重要'\r\n            } else if (this.tjlist.smdj == 3) {\r\n                this.tjlist.smdj = '一般'\r\n            }\r\n            if (this.tjlist.cnsqk == 1) {\r\n                this.show = true\r\n            } else {\r\n                this.show = false\r\n            }\r\n            if (this.tjlist.xysqk == 1) {\r\n                this.show1 = true\r\n            } else {\r\n                this.show1 = false\r\n            }\r\n        },\r\n        // 预览\r\n        yulan() {\r\n            this.ylxy = false\r\n            const iamgeBase64 = \"data:image/jpeg;base64,\" + this.tjlist.brcn;\r\n            if (typeof iamgeBase64 === \"string\") {\r\n                // 复制某条消息\r\n                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n                function validDataUrl(s) {\r\n                    return validDataUrl.regex.test(s);\r\n                }\r\n                validDataUrl.regex =\r\n                    /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n                if (validDataUrl(iamgeBase64)) {\r\n                    // debugger;\r\n                    let that = this;\r\n\r\n                    function previwImg(item) {\r\n                        that.imageUrlbrcn = item;\r\n                    }\r\n                    previwImg(iamgeBase64);\r\n                }\r\n            }\r\n        },\r\n        // chRadio(val) {\r\n        //     console.log(val);\r\n        //     if (val == 1) {\r\n        //         this.btnsftg = false\r\n        //         this.btnsfth = true\r\n        //     } else if (val == 0) {\r\n        //         this.btnsftg = true\r\n        //         this.btnsfth = false\r\n        //     } else if (this.upccLsit.rysc.bmsc == 1) {\r\n        //         this.btnsftg = false\r\n        //         this.btnsfth = true\r\n        //     } else if (this.upccLsit.rysc.bmsc == 0) {\r\n        //         this.btnsftg = true\r\n        //         this.btnsfth = false\r\n        //     } else if (this.upccLsit.rysc.rlsc == 1) {\r\n        //         this.btnsftg = false\r\n        //         this.btnsfth = true\r\n        //     } else if (this.upccLsit.rysc.rlsc == 0) {\r\n        //         this.btnsftg = true\r\n        //         this.btnsfth = false\r\n        //     } else if (this.upccLsit.rysc.bmbsc == 1) {\r\n        //         this.btnsftg = false\r\n        //         this.btnsfth = true\r\n        //     } else if (this.upccLsit.rysc.bmbsc == 0) {\r\n        //         this.btnsftg = true\r\n        //         this.btnsfth = false\r\n        //     }\r\n        // },\r\n\r\n        // 通过\r\n        // async save(index) {\r\n        //     // let jgbz = index\r\n        //     // if (this.tjlist.pxqk != undefined) {\r\n        //     //     if (this.tjlist.cnsqk != undefined) {\r\n        //     //         if (this.tjlist.xysqk != undefined) {\r\n        //     //             if (this.tjlist.cnsrq != undefined) {\r\n        //     //                 let obj = {\r\n        //     //                     pxqk: this.tjlist.pxqk,\r\n        //     //                     cnsqk: this.tjlist.cnsqk,\r\n        //     //                     xysqk: this.tjlist.xysqk,\r\n        //     //                     cnsrq: this.tjlist.cnsrq,\r\n        //     //                     bmsc: this.tjlist.bmsc,\r\n        //     //                     bmscrq: this.tjlist.bmscrq,\r\n        //     //                     rlsc: this.tjlist.rlsc,\r\n        //     //                     rlscrq: this.tjlist.rlscrq,\r\n        //     //                     scqk: this.tjlist.scqk,\r\n        //     //                     bmbscrq: this.tjlist.bmbscrq,\r\n        //     //                 }\r\n        //     //                 let params = {\r\n        //     //                     ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,\r\n        //     //                     ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,\r\n        //     //                     ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,\r\n        //     //                     ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,\r\n        //     //                     ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,\r\n        //     //                     ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,\r\n        //     //                     rysc: Object.assign(this.upccLsit.rysc, obj)\r\n        //     //                 }\r\n        //     //                 let data = await updateRysc(params)\r\n        //     //                 if (data.code == 10000) {\r\n        //     //                     if (this.tjlist.bmscrq != undefined || this.tjlist.rlscrq != undefined || this.tjlist.bmbscrq != undefined) {\r\n        //     //                         if (this.tjlist.bmsc == 1 || this.tjlist.rlsc == 1 || this.tjlist.bmbsc == 1) {\r\n        //     //                             if (jgbz == 1) {\r\n        //     //                                 this.jgyf = 1\r\n        //     //                             }\r\n        //     //                             this.sxsh()\r\n        //     //                             this.spxx()\r\n        //     //                         }\r\n        //     //                         if (this.tjlist.bmsc == 2 || this.tjlist.rlsc == 2 || this.tjlist.bmbsc == 2) {\r\n        //     //                             if (jgbz == 2) {\r\n        //     //                                 this.jgyf = 2\r\n        //     //                             } else if (jgbz == 3) {\r\n        //     //                                 this.jgyf = 3\r\n        //     //                             }\r\n        //     //                             this.sxsh()\r\n        //     //                             this.spxx()\r\n        //     //                         }\r\n        //     //                     } else { this.$message.warning('请选择日期') }\r\n        //     //                 }\r\n        //     //             } else { this.$message.warning('请选择日期') }\r\n        //     //         } else { this.$message.warning('是否签订保密协议书') }\r\n        //     //     } else { this.$message.warning('是否签订保密承诺书') }\r\n        //     // } else { this.$message.warning('是否进行保密教育培训') }\r\n        // },\r\n        //立即办理\r\n        ljbl() {\r\n            this.activeName = 'second'\r\n        },\r\n        // //判断实例所处环节\r\n        // async pdschj() {\r\n        //     // let params = {\r\n        //     //     fwdyid: this.fwdyid,\r\n        //     //     slid: this.slid\r\n        //     // }\r\n        //     // let data = await getSchj(params)\r\n        //     // if (data.code == 10000) {\r\n        //     //     if (data.data.content == 1) {\r\n        //     //         this.disabled2 = true\r\n        //     //         this.disabled3 = true\r\n        //     //     }\r\n        //     //     if (data.data.content == 2) {\r\n        //     //         this.disabled1 = true\r\n        //     //         this.disabled3 = true\r\n        //     //     }\r\n        //     //     if (data.data.content == 3) {\r\n        //     //         this.disabled1 = true\r\n        //     //         this.disabled2 = true\r\n        //     //     }\r\n        //     // }\r\n        // },\r\n        // //事项审核\r\n        // async sxsh() {\r\n        //     // let params = {\r\n        //     //     fwdyid: this.fwdyid,\r\n        //     //     slid: this.slid,\r\n        //     //     jg: this.jgyf,\r\n        //     //     smryid: this.tjlist.smryid\r\n        //     // }\r\n        //     // let data = await getSxsh(params)\r\n        //     // if (data.code == 10000) {\r\n        //     //     if (data.data.zt == 0) {\r\n        //     //         this.$message({\r\n        //     //             message: data.data.msg,\r\n        //     //             type: 'success'\r\n        //     //         });\r\n        //     //         // this.smryList = data.data.blrarr\r\n        //     //         this.mbhjid = data.data.mbhjid\r\n        //     //         this.splist()\r\n        //     //         this.dialogVisible = true\r\n        //     //     } else if (data.data.zt == 1) {\r\n        //     //         this.$message({\r\n        //     //             message: data.data.msg,\r\n        //     //             type: 'success'\r\n        //     //         });\r\n        //     //     } else if (data.data.zt == 2) {\r\n        //     //         this.$message({\r\n        //     //             message: data.data.msg\r\n        //     //         });\r\n        //     //     } else if (data.data.zt == 3) {\r\n        //     //         this.$message({\r\n        //     //             message: data.data.msg\r\n        //     //         });\r\n        //     //     }\r\n        //     // }\r\n        // },\r\n        //初始化el-dialog列表数据\r\n        // async splist() {\r\n        //     // let params = {\r\n        //     //     fwdyid: this.fwdyid,\r\n        //     //     'xm': this.formInline.xm,\r\n        //     //     'bmmc': this.formInline.bmmc,\r\n        //     //     page: this.page,\r\n        //     //     pageSize: this.pageSize,\r\n        //     //     qshjid: this.mbhjid,\r\n        //     // }\r\n        //     // let data = await getSpUserList(params)\r\n        //     // this.smryList = data.records\r\n        //     // this.total = data.total\r\n        // },\r\n        // onSubmit() {\r\n        //     this.splist()\r\n        // },\r\n        // selectRow(selection) {\r\n        //     if (selection.length <= 1) {\r\n        //         console.log('点击选中数据：', selection);\r\n        //         this.selectlistRow = selection\r\n        //         this.xsyc = true\r\n        //     } else if (selection.length > 1) {\r\n        //         this.$message.warning('只能选中一条数据')\r\n        //         this.xsyc = false\r\n        //     }\r\n\r\n        // },\r\n        // handleSelect(selection, val) {\r\n        //     //只能选择一行，选择其他，清除上一行\r\n        //     if (selection.length > 1) {\r\n        //         let del_row = selection.shift()\r\n        //         this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中\r\n        //     }\r\n        // },\r\n        // // 点击行触发，选中或不选中复选框\r\n        // handleRowClick(row, column, event) {\r\n        //     this.$refs.multipleTable.toggleRowSelection(row)\r\n        //     this.selectChange(this.selectlistRow)\r\n        // },\r\n        // async submit() {\r\n        //     let params = {\r\n        //         fwdyid: this.fwdyid,\r\n        //         slid: this.slid,\r\n        //         shry: this.selectlistRow[0].yhid,\r\n        //         mbhjid: this.mbhjid,\r\n        //     }\r\n        //     let data = await tjclr(params)\r\n        //     if (data.code == 10000) {\r\n        //         this.$message({\r\n        //             message: data.message,\r\n        //             type: 'success'\r\n        //         });\r\n        //         this.dialogVisible = false\r\n        //     }\r\n        // },\r\n        //上传文件\r\n        beforeAvatarUpload(file) {\r\n            const isJPG = file.type === 'image/jpeg';\r\n            const isPNG = file.type === 'image/png';\r\n            if (!isJPG && !isPNG) {\r\n                this.$message.error('上传缩略图只能是 JPG/PNG 格式!');\r\n            }\r\n            return isJPG || isPNG;\r\n        },\r\n        // 64码\r\n        blobToBase64(blob, callback) {\r\n            const fileReader = new FileReader();\r\n            fileReader.onload = (e) => {\r\n                callback(e.target.result);\r\n            };\r\n            fileReader.readAsDataURL(blob);\r\n        },\r\n        //保密承诺书预览\r\n        bmcnsyl() {\r\n            this.dialogVisible_bmcns = true\r\n            const iamgeBase64 = \"data:image/jpeg;base64,\" + this.tjlist.cnssmj;\r\n            if (typeof iamgeBase64 === \"string\") {\r\n                // 复制某条消息\r\n                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n                function validDataUrl(s) {\r\n                    return validDataUrl.regex.test(s);\r\n                }\r\n                validDataUrl.regex =\r\n                    /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n                if (validDataUrl(iamgeBase64)) {\r\n                    // debugger;\r\n                    let that = this;\r\n\r\n                    function previwImg(item) {\r\n                        that.bmcnsImageUrl = item;\r\n                    }\r\n                    previwImg(iamgeBase64);\r\n                }\r\n            }\r\n        },\r\n        //保密协议书预览\r\n        bmxysyl() {\r\n            this.dialogVisible_bmxys = true\r\n            const iamgeBase64 = \"data:image/jpeg;base64,\" + this.tjlist.xyssmj;\r\n            if (typeof iamgeBase64 === \"string\") {\r\n                // 复制某条消息\r\n                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n                function validDataUrl(s) {\r\n                    return validDataUrl.regex.test(s);\r\n                }\r\n                validDataUrl.regex =\r\n                    /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n                if (validDataUrl(iamgeBase64)) {\r\n                    // debugger;\r\n                    let that = this;\r\n\r\n                    function previwImg(item) {\r\n                        that.bmxysImageUrl = item;\r\n                    }\r\n                    previwImg(iamgeBase64);\r\n                }\r\n            }\r\n        },\r\n        // //保密承诺书扫描件\r\n        // async bmcns(item) {\r\n        //     // this.file = item.file\r\n        //     this.file = URL.createObjectURL(item.file);\r\n        //     this.bmcnssmj = item.file\r\n        //     this.blobToBase64(item.file, async (dataurl) => {\r\n        //         this.upccLsit.rysc.cnssmj = dataurl.split(',')[1]\r\n        //         let params = {\r\n        //             ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,\r\n        //             ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,\r\n        //             ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,\r\n        //             ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,\r\n        //             ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,\r\n        //             ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,\r\n        //             rysc: this.upccLsit.rysc\r\n        //         }\r\n        //         let data = await updateRysc(params)\r\n        //         if (data.code == 10000) {\r\n        //             this.$message.success('上传保密承诺书成功！')\r\n        //             this.spxx()\r\n        //         }\r\n        //     });\r\n        // },\r\n        // //签订保密协议书\r\n        // async bmxys(item) {\r\n        //     // this.file = item.file\r\n        //     this.file = URL.createObjectURL(item.file);\r\n        //     this.bmxyssmj = item.file\r\n        //     this.blobToBase64(item.file, async (dataurl) => {\r\n        //         this.upccLsit.rysc.xyssmj = dataurl.split(',')[1]\r\n        //         let params = {\r\n        //             ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,\r\n        //             ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,\r\n        //             ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,\r\n        //             ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,\r\n        //             ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,\r\n        //             ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,\r\n        //             rysc: this.upccLsit.rysc\r\n        //         }\r\n        //         let data = await updateRysc(params)\r\n        //         if (data.code == 10000) {\r\n        //             this.$message.success('上传保密承诺书成功！')\r\n        //             this.spxx()\r\n        //         }\r\n        //     });\r\n        // },\r\n        // //列表分页--跳转页数\r\n        // handleCurrentChange(val) {\r\n        //     this.page = val\r\n        //     this.splist()\r\n        // },\r\n        // //列表分页--更改每页显示个数\r\n        // handleSizeChange(val) {\r\n        //     this.page = 1\r\n        //     this.pageSize = val\r\n        //     this.splist()\r\n        // },\r\n        //流程跟踪\r\n        //流程跟踪初始化列表\r\n        async lcgz() {\r\n            let params = {\r\n                fwdyid: this.fwdyid,\r\n                slid: this.slid\r\n            }\r\n            let data = await getSpGjxx(params)\r\n            if (data.code == 10000) {\r\n                this.lcgzList = data.data.content\r\n                this.gjclList = data.data.content\r\n            }\r\n        },\r\n    },\r\n    watch: {\r\n\r\n    }\r\n}\r\n\r\n</script>\r\n  \r\n<style scoped>\r\n.sec-container {\r\n    width: 100%;\r\n    height: calc(100% - 50px);\r\n    overflow-y: overlay;\r\n}\r\n\r\n.sec-title-big {\r\n    width: 100%;\r\n    color: #1b72d8;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-weight: bold;\r\n    font-size: 26px;\r\n}\r\n\r\n.sec-title {\r\n    border-left: 5px solid #1b72d8;\r\n    color: #1b72d8;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    text-indent: 10px;\r\n    margin-bottom: 20px;\r\n    margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n    width: calc(100% - 260px);\r\n    border: 1px solid #CDD2D9;\r\n    height: 40px;\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    overflow: hidden;\r\n    border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n    border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n    float: left;\r\n    width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n    width: 100%;\r\n    position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n    width: 258px;\r\n    position: absolute;\r\n    right: 0px;\r\n    top: 0;\r\n    height: 245px;\r\n    border: 1px solid #CDD2D9;\r\n    border-left: 0;\r\n    background: #ffffff;\r\n}\r\n\r\n.sec-header-flex {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.sec-header-mar {\r\n    margin-right: 10px;\r\n}\r\n\r\n.sec-form-second {\r\n    /* width: 100%; */\r\n    border: 1px solid #CDD2D9;\r\n    height: 40px;\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    overflow: hidden;\r\n    border-right: 0px;\r\n    border-top: 0;\r\n    position: relative;\r\n}\r\n\r\n.sec-form-fddw {\r\n    height: 100%;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 46.5%;\r\n}\r\n\r\n.sec-form-third {\r\n    border: 1px solid #CDD2D9;\r\n    /* height: 40px;  */\r\n    /* display: flex;\r\n    justify-content: space-evenly; */\r\n    overflow: hidden;\r\n    /* border-right: 0px; */\r\n    border-top: 0;\r\n    background: #ffffff;\r\n    padding: 10px;\r\n}\r\n\r\n.hyzk {\r\n    padding-left: 15px;\r\n    background-color: #F5F7FA;\r\n    width: calc(100% - 16px);\r\n    border-right: 1px solid #CDD2D9;\r\n    color: #000;\r\n}\r\n\r\n.sec-form-four {\r\n    border: 1px solid #CDD2D9;\r\n    height: auto;\r\n    min-height: 100px;\r\n    /* display: flex;\r\n    justify-content: space-evenly; */\r\n    overflow: hidden;\r\n    /* border-right: 0px; */\r\n    border-top: 0;\r\n    background: #ffffff;\r\n    padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n    border: 1px solid #CDD2D9;\r\n    height: auto;\r\n    min-height: 100px;\r\n    overflow: hidden;\r\n    border-top: 0;\r\n    background: #ffffff;\r\n    padding: 10px;\r\n}\r\n\r\n.yulan {\r\n    text-align: center;\r\n    cursor: pointer;\r\n    color: #3874D5;\r\n    font-weight: 600;\r\n    float: left;\r\n    margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n    width: 100px;\r\n    height: 100px;\r\n    display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n    font-size: 28px;\r\n    color: #8c939d;\r\n    width: 100px;\r\n    height: 100px;\r\n    line-height: 100px;\r\n    text-align: center;\r\n    border: 2px solid #EBEBEB;\r\n}\r\n\r\n.sec-form-six {\r\n    border: 1px solid #CDD2D9;\r\n    overflow: hidden;\r\n    background: #ffffff;\r\n    padding: 10px;\r\n}\r\n\r\n.ml10 {\r\n    margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n    margin-top: 10px;\r\n    border-right: 1px solid #CDD2D9;\r\n    background: #ffffff;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n    border: none;\r\n}\r\n\r\n.sec-left-text {\r\n    float: left;\r\n    margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n    border-top: 1px solid #CDD2D9;\r\n}\r\n\r\n>>>.longLabel .el-form-item__label {\r\n    width: 500px !important;\r\n}\r\n\r\n>>>.longLabel .el-form-item__content {\r\n    margin-left: 500px !important;\r\n    padding-left: 20px;\r\n    border-right: 1px solid #CDD2D9;\r\n    background: #ffffff;\r\n}\r\n\r\n.gtzzsmgwgz {\r\n    border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n.gtzzsmgwgz>>>.el-form-item__content {\r\n    display: none !important;\r\n}\r\n\r\n.gtzzsmgwgz>>>.el-form-item__label {\r\n    border: none;\r\n    text-align: left !important;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n    border-top: 0;\r\n  } */\r\n.sec-form-second .el-form-item {\r\n    float: left;\r\n    width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n    width: 100%;\r\n    border: 1px solid #EBEEF5;\r\n    height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n    border: none !important;\r\n    border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n    width: 200px;\r\n    text-align: center;\r\n    font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n    border: none;\r\n    border-right: 1px solid #CDD2D9;\r\n    border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n    margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n    border: 1px solid #CDD2D9;;\r\n  } */\r\n>>>.el-form-item__label {\r\n    border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n    width: 150px;\r\n    margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n    margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n    height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n    margin-right: 20px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n/deep/ .el-input.is-disabled .el-input__inner {\r\n    color: #000 !important;\r\n}\r\n</style>\r\n  \n\n\n// WEBPACK FOOTER //\n// src/renderer/view/wdgz/blsp/fqblxxscb.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"sec-container\"},[_c('el-tabs',{model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:\"activeName\"}},[_c('el-tab-pane',{attrs:{\"label\":\"审批指南\",\"name\":\"first\"}},[_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.ljbl}},[_vm._v(\"立即办理\")])],1),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.spznList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"hjmc\",\"label\":\"办理流程\"}})],1)],1),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"label\":\"审批信息\",\"name\":\"second\"}},[_c('p',{staticClass:\"sec-title-big\"},[_vm._v(\"涉密人员保密审查表\")]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"基本信息\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"姓名\",\"prop\":\"xm\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xm\", $$v)},expression:\"tjlist.xm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"性别\",\"prop\":\"xb\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xb),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xb\", $$v)},expression:\"tjlist.xb\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"国籍\",\"prop\":\"gj\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.gj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gj\", $$v)},expression:\"tjlist.gj\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"曾用名\",\"prop\":\"cym\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.cym),callback:function ($$v) {_vm.$set(_vm.tjlist, \"cym\", $$v)},expression:\"tjlist.cym\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"民族\",\"prop\":\"mz\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.mz),callback:function ($$v) {_vm.$set(_vm.tjlist, \"mz\", $$v)},expression:\"tjlist.mz\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"婚姻状况\",\"prop\":\"hyzk\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.hyzk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"hyzk\", $$v)},expression:\"tjlist.hyzk\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"政治面貌\",\"prop\":\"zzmm\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.zzmm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zzmm\", $$v)},expression:\"tjlist.zzmm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"联系电话\",\"prop\":\"lxdh\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.lxdh),callback:function ($$v) {_vm.$set(_vm.tjlist, \"lxdh\", $$v)},expression:\"tjlist.lxdh\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"身份证号\",\"prop\":\"sfzhm\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.sfzhm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sfzhm\", $$v)},expression:\"tjlist.sfzhm\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"户籍地址\",\"prop\":\"hjdz\"}},[_c('el-input',{attrs:{\"placeholder\":\"（详细）\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.hjdz),callback:function ($$v) {_vm.$set(_vm.tjlist, \"hjdz\", $$v)},expression:\"tjlist.hjdz\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"户籍地公安机关\",\"prop\":\"hjgajg\"}},[_c('el-input',{attrs:{\"placeholder\":\"街道派出所\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.hjgajg),callback:function ($$v) {_vm.$set(_vm.tjlist, \"hjgajg\", $$v)},expression:\"tjlist.hjgajg\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"常住地址\",\"prop\":\"czdz\"}},[_c('el-input',{attrs:{\"placeholder\":\"（详细）\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.czdz),callback:function ($$v) {_vm.$set(_vm.tjlist, \"czdz\", $$v)},expression:\"tjlist.czdz\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"常住地公安机关\",\"prop\":\"czgajg\"}},[_c('el-input',{attrs:{\"placeholder\":\"街道派出所\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.czgajg),callback:function ($$v) {_vm.$set(_vm.tjlist, \"czgajg\", $$v)},expression:\"tjlist.czgajg\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-header-pic sec-header-flex\"},[(_vm.imageUrl)?_c('img',{staticClass:\"avatar\",attrs:{\"src\":_vm.imageUrl}}):_vm._e()])]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second\"},[_c('el-form-item',{attrs:{\"label\":\"部门及职务、职称\",\"prop\":\"jbzc\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('p',{staticClass:\"hyzk\"},[(_vm.tjlist.bmmc)?_c('span',[_vm._v(\"部门：\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.bmmc)?_c('span',[_vm._v(_vm._s(_vm.tjlist.bmmc)+\"、\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.zw)?_c('span',[_vm._v(\"职务：\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.zw)?_c('span',[_vm._v(_vm._s(_vm.tjlist.zw)+\"、\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc != undefined && _vm.tjlist.jbzc != '')?_c('span',[_vm._v(\"职称：\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 1)?_c('span',[_vm._v(\"省部级\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 2)?_c('span',[_vm._v(\"厅局级\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 3)?_c('span',[_vm._v(\"县处级\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 4)?_c('span',[_vm._v(\"乡科级及以下\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 5)?_c('span',[_vm._v(\"高级(含正高、副高)\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 6)?_c('span',[_vm._v(\"中级\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 7)?_c('span',[_vm._v(\"初级及以下\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 8)?_c('span',[_vm._v(\"试用期人员\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 9)?_c('span',[_vm._v(\"工勤人员\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 10)?_c('span',[_vm._v(\"企业职员\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 11)?_c('span',[_vm._v(\"其他\")]):_vm._e()])]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second\"},[_c('el-form-item',{attrs:{\"label\":\"已（拟）任涉密岗位\",\"prop\":\"gwmc\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.gwmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gwmc\", $$v)},expression:\"tjlist.gwmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"涉密等级\",\"prop\":\"smdj\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.smdj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"smdj\", $$v)},expression:\"tjlist.smdj\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"主要学习及工作经历\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.tjlist.xxjlList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"qssj\",\"label\":\"起始日期\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzsj\",\"label\":\"终止日期\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"szdw\",\"label\":\"主要学习经历、工作单位\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zw\",\"label\":\"任职情况\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zmr\",\"label\":\"证明人\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"家庭成员及主要社会关系情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.tjlist.cyjshgxList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gxms\",\"label\":\"与本人关系\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xm\",\"label\":\"姓名\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"jwjlqk\",\"label\":\"是否有外籍、境外居留权、长期居留许可\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cgszd\",\"label\":\"单位\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zw\",\"label\":\"职务\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzmm\",\"label\":\"政治面貌\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"移居国(境)外情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"拥有外籍、境外永久居留权或者长期居留许可情况\",\"prop\":\"cqjlxkqk\"}},_vm._l((_vm.yjgwqk),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"},model:{value:(_vm.tjlist.sfcrj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sfcrj\", $$v)},expression:\"tjlist.sfcrj\"}},[_vm._v(_vm._s(item.yw))])}),1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"持有因公出入境证件情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.tjlist.ygrjzjqkList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"fjlb\",\"label\":\"证件名称\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cyqk\",\"label\":\"持有情况\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zjhm\",\"label\":\"证件号码\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"yxq\",\"label\":\"有效期\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"持有因私出入境证件情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.tjlist.ysrjzjqkList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"fjlb\",\"label\":\"证件名称\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cyqk\",\"label\":\"持有情况\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zjhm\",\"label\":\"证件号码\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"yxq\",\"label\":\"有效期\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"因私出国(境)情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.tjlist.yscgqkList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"qssj\",\"label\":\"起始日期\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzsj\",\"label\":\"终止日期\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cggj\",\"label\":\"近3年所到国家或地区\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"sy\",\"label\":\"事由\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"接受境外资助情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.tjlist.jsjwzzqkList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzsj\",\"label\":\"起始日期\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gj\",\"label\":\"国家地区\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"jgmc\",\"label\":\"机构名称\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zznr\",\"label\":\"资助内容\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"处分或者违法犯罪情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.tjlist.clhwffzqkList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfsj\",\"label\":\"起始日期\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfjg\",\"label\":\"处理结果\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfyy\",\"label\":\"处理原因\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfdw\",\"label\":\"处理机构\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"配偶子女有关情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-third haveBorderTop\"},[_c('div',{staticClass:\"sec-left-text\"},[_c('p',[_vm._v(\"1.在国境内外从事反对、攻击党和国家或者颠覆国家政权活动\")]),_vm._v(\" \"),_c('p',[_vm._v(\"2.被列为影响国家安全重点管控人员\")]),_vm._v(\" \"),_c('p',[_vm._v(\"3.因危害国家安全的行为收到处分或者处罚\")])]),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"\",\"prop\":\"qscfqk\"}},_vm._l((_vm.yjgwqk),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"},model:{value:(_vm.tjlist.qscfqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qscfqk\", $$v)},expression:\"tjlist.qscfqk\"}},[_vm._v(_vm._s(item.yw))])}),1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"其他需要说明的情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-four haveBorderTop\"},[_c('el-input',{attrs:{\"type\":\"textarea\",\"autosize\":\"\",\"placeholder\":\"请输入内容\",\"disabled\":\"\"},model:{value:(_vm.tjlist.qtqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qtqk\", $$v)},expression:\"tjlist.qtqk\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"本人承诺\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-five haveBorderTop\",staticStyle:{\"display\":\"flex\",\"align-items\":\"center\"}},[(!_vm.ylxy)?_c('img',{staticClass:\"avatar\",attrs:{\"src\":_vm.imageUrlbrcn}}):_vm._e(),_vm._v(\" \"),(_vm.ylxy)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":_vm.yulan}},[_vm._v(\"预 览\")]):_vm._e()],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"上岗保密教育、签订保密承诺书\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"保密教育培训\",\"prop\":\"pxqk\"}},_vm._l((_vm.bmjysfwc),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"},model:{value:(_vm.tjlist.pxqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"pxqk\", $$v)},expression:\"tjlist.pxqk\"}},[_vm._v(_vm._s(item.sfwc))])}),1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"签订保密承诺书\",\"prop\":\"cnsqk\"}},_vm._l((_vm.bmjysfwc),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"},model:{value:(_vm.tjlist.cnsqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"cnsqk\", $$v)},expression:\"tjlist.cnsqk\"}},[_vm._v(_vm._s(item.sfwc))])}),1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-fddw sec-header-flex\"},[(_vm.show)?_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\"},on:{\"click\":_vm.bmcnsyl}},[_vm._v(\"预览\")]):_vm._e(),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogVisible_bmcns},on:{\"update:visible\":function($event){_vm.dialogVisible_bmcns=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.bmcnsImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogVisible_bmcns = false}}},[_vm._v(\"取 消\")])],1)])],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"签订保密协议书\",\"prop\":\"xysqk\"}},_vm._l((_vm.bmjysfwc),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"},model:{value:(_vm.tjlist.xysqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xysqk\", $$v)},expression:\"tjlist.xysqk\"}},[_vm._v(_vm._s(item.sfwc))])}),1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-fddw sec-header-flex\"},[(_vm.show1)?_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\"},on:{\"click\":_vm.bmxysyl}},[_vm._v(\"预览\")]):_vm._e(),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogVisible_bmxys},on:{\"update:visible\":function($event){_vm.dialogVisible_bmxys=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.bmxysImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogVisible_bmxys = false}}},[_vm._v(\"取 消\")])],1)])],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"人力资源部审批人\",\"prop\":\"rlspr\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.rlspr),callback:function ($$v) {_vm.$set(_vm.tjlist, \"rlspr\", $$v)},expression:\"tjlist.rlspr\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"日期\",\"prop\":\"cnsrq\"}},[_c('el-date-picker',{attrs:{\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"disabled\":\"\",\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.tjlist.cnsrq),callback:function ($$v) {_vm.$set(_vm.tjlist, \"cnsrq\", $$v)},expression:\"tjlist.cnsrq\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"所在部门审查情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"信息属实，拟\",\"prop\":\"bmsc\"}},_vm._l((_vm.scqk),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"},model:{value:(_vm.tjlist.bmsc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmsc\", $$v)},expression:\"tjlist.bmsc\"}},[_vm._v(_vm._s(item.sfty))])}),1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"gtzzsmgwgz\",attrs:{\"label\":\"该同志在涉密岗位工作\",\"prop\":\"gtzzsmgwgz\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"部门领导审批人\",\"prop\":\"bmspr\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":\"\",\"clearable\":\"\"},model:{value:(_vm.tjlist.bmspr),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmspr\", $$v)},expression:\"tjlist.bmspr\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"日期\",\"prop\":\"bmscrq\"}},[_c('el-date-picker',{attrs:{\"disabled\":\"\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.tjlist.bmscrq),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmscrq\", $$v)},expression:\"tjlist.bmscrq\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"人力资源部审查情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"信息属实，拟\",\"prop\":\"rlsc\"}},_vm._l((_vm.scqk),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"},model:{value:(_vm.tjlist.rlsc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"rlsc\", $$v)},expression:\"tjlist.rlsc\"}},[_vm._v(_vm._s(item.sfty))])}),1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"gtzzsmgwgz\",attrs:{\"label\":\"该同志在涉密岗位工作\",\"prop\":\"gtzzsmgwgz\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"人力资源部领导审批人\",\"prop\":\"rlldspr\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":\"\",\"clearable\":\"\"},model:{value:(_vm.tjlist.rlldspr),callback:function ($$v) {_vm.$set(_vm.tjlist, \"rlldspr\", $$v)},expression:\"tjlist.rlldspr\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"日期\",\"prop\":\"rlscrq\"}},[_c('el-date-picker',{attrs:{\"disabled\":\"\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.tjlist.rlscrq),callback:function ($$v) {_vm.$set(_vm.tjlist, \"rlscrq\", $$v)},expression:\"tjlist.rlscrq\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"保密办意见\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"信息属实，拟\",\"prop\":\"bmbsc\"}},_vm._l((_vm.scqk),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":\"\"},model:{value:(_vm.tjlist.bmbsc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmbsc\", $$v)},expression:\"tjlist.bmbsc\"}},[_vm._v(_vm._s(item.sfty))])}),1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"gtzzsmgwgz\",attrs:{\"label\":\"该同志在涉密岗位工作\",\"prop\":\"gtzzsmgwgz\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"保密办领导审批人\",\"prop\":\"bmbldspr\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":\"\",\"clearable\":\"\"},model:{value:(_vm.tjlist.bmbldspr),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmbldspr\", $$v)},expression:\"tjlist.bmbldspr\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"日期\",\"prop\":\"bmbscrq\"}},[_c('el-date-picker',{attrs:{\"disabled\":\"\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.tjlist.bmbscrq),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmbscrq\", $$v)},expression:\"tjlist.bmbscrq\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"备注：涉密人员上岗审查、在岗复审均填本表\")]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"轨迹处理\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.gjclList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"hjmc\",\"label\":\"办理环节\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clrid\",\"label\":\"办理人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bllx\",\"label\":\"办理类型\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clyj\",\"label\":\"办理意见\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xybclr\",\"label\":\"下一步办理人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clsj\",\"label\":\"办理时间\"}})],1)],1)],1)]),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"label\":\"流程跟踪\",\"name\":\"third\"}},[_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.lcgzList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"hjmc\",\"label\":\"办理环节\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clrid\",\"label\":\"办理人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bllx\",\"label\":\"办理类型\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clyj\",\"label\":\"办理意见\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xybclr\",\"label\":\"下一步办理人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clsj\",\"label\":\"办理时间\"}})],1)],1)],1)],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-d2600710\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/wdgz/blsp/fqblxxscb.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-d2600710\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./fqblxxscb.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./fqblxxscb.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./fqblxxscb.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-d2600710\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./fqblxxscb.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-d2600710\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/wdgz/blsp/fqblxxscb.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}