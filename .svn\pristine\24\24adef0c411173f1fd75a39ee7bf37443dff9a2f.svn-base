webpackJsonp([200],{"/p/O":function(e,t){},JYHt:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=s("mvHQ"),n=s.n(a),l=(s("oAV5"),s("urfq")),i={data:function(){return{formInline:{},pageInfo:{page:1,pageSize:10,total:0},scList:[],ssmkList:[],logsAllList:[]}},methods:{formatDate:function(e){return Object(l.b)(new Date(e))},getSjrz:function(){this.pageInfo.page=1,this.getLogs()},getAllOptionsLogs:function(){var e=this;parseOperationLogsSj(function(t){console.log("操作日志",t),e.logsAllList=t,e.initSsmkList(t),e.getLogs()})},initSsmkList:function(e){var t=this,s=[],a=void 0;e.forEach(function(e){a={},-1==s.indexOf(e.xyybs)&&(s.push(e.xyybs),a.xyybs=e.xyybs,a.name=t.translationSsmk(e.xyybs),t.ssmkList.push(a))})},translationSsmk:function(e){switch(e){case"yybs-xjzczp":return"新建自查自评";case"yybs-jxzczp":return"继续自查自评";case"yybs-zczpls":return"自查自评历史";case"yybs-ccdnsjg":return"抽查的内设机构";case"yybs-ccdnsjgDj":return"内设机构自查自评结果登记";case"yybs-ccdry":return"抽查的人员";case"yybs-ccdryDj":return"人员自查自评";case"yybs-jczj":return"检查总结";default:return e}},handleCurrentChange:function(e){this.pageInfo.page=e,this.getLogs()},handleSizeChange:function(e){this.pageInfo.pageSize=e,this.pageInfo.page=1,this.getLogs()},getLogs:function(){var e=this.formInline.cxsj,t=this.formInline.xyybs,s=JSON.parse(n()(this.logsAllList));t&&(s=this.logsAllList.filter(function(e){if(e.xyybs==t)return e})),e&&(s=this.logsAllList.filter(function(t){if(t.time>=e[0]&&t.time<=e[1])return t}));var a=this.pageInfo.page,l=this.pageInfo.pageSize;this.scList=s.slice(l*(a-1),l*(a-1)+l),this.pageInfo.total=s.length}},mounted:function(){this.getAllOptionsLogs()}},r={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticStyle:{height:"calc(100% - 32px)",width:"100%"}},[s("div",{staticClass:"mhcx"},[s("el-form",{staticClass:"demo-form-inline",staticStyle:{float:"left"},attrs:{inline:!0,model:e.formInline,size:"medium"}},[s("el-form-item",{staticStyle:{"font-weight":"700"},attrs:{label:"所属模块"}},[s("el-select",{attrs:{clearable:""},model:{value:e.formInline.xyybs,callback:function(t){e.$set(e.formInline,"xyybs",t)},expression:"formInline.xyybs"}},e._l(e.ssmkList,function(e,t){return s("el-option",{key:t,attrs:{label:e.name,value:e.xyybs}})}),1)],1),e._v(" "),s("el-form-item",{staticStyle:{"font-weight":"700"},attrs:{label:"时间"}},[s("el-date-picker",{attrs:{size:"",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"timestamp",editable:!1,"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.formInline.cxsj,callback:function(t){e.$set(e.formInline,"cxsj",t)},expression:"formInline.cxsj"}})],1),e._v(" "),s("el-form-item",[s("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.getSjrz}},[e._v("查询")])],1)],1)],1),e._v(" "),s("div",{staticStyle:{height:"calc(100% - 34px - 20px)"}},[s("el-table",{staticClass:"table",staticStyle:{width:"100%",border:"1px solid #EBEEF5"},attrs:{data:e.scList,border:"",stripe:"","header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},height:"calc(100% - 34px - 20px)"}},[s("el-table-column",{attrs:{type:"index",width:"60",label:"序号",align:"center"}}),e._v(" "),s("el-table-column",{attrs:{prop:"yhm",label:"账号"}}),e._v(" "),s("el-table-column",{attrs:{prop:"xm",label:"姓名"}}),e._v(" "),s("el-table-column",{attrs:{prop:"rwmc",label:"任务名称"}}),e._v(" "),s("el-table-column",{attrs:{prop:"jcjdmc",label:"检查季度"}}),e._v(" "),s("el-table-column",{attrs:{label:"操作模块"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",[e._v(e._s(e.translationSsmk(t.row.xyybs)))])]}}])}),e._v(" "),s("el-table-column",{attrs:{prop:"ymngnmc",label:"操作功能"}}),e._v(" "),s("el-table-column",{attrs:{label:"时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",[e._v(e._s(e.formatDate(t.row.time)))])]}}])})],1)],1),e._v(" "),s("div",{staticStyle:{border:"1px solid #ebeef5"}},[s("el-pagination",{attrs:{background:"","pager-count":5,"current-page":e.pageInfo.page,"page-sizes":[5,10,20,30],"page-size":e.pageInfo.pageSize,layout:"total, prev, pager, sizes,next, jumper",total:e.pageInfo.total},on:{"current-change":e.handleCurrentChange,"size-change":e.handleSizeChange}})],1)])},staticRenderFns:[]};var o=s("VU/8")(i,r,!1,function(e){s("/p/O")},"data-v-374fbbe8",null);t.default=o.exports}});
//# sourceMappingURL=200.5dae8e8bddd02d7814b0.js.map