webpackJsonp([169],{UQSU:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=i("Xxa5"),l=i.n(s),a=i("exGp"),n=i.n(a),o=i("gyMJ"),r=i("cwdu"),c=i("G3m0"),d=i("TSC9"),m=i("rouf"),u=i("W3A/"),g=i("kCU4"),p={components:{},props:{},data:function(){return{pdgwbm:0,gwmc:"",smdj:[],gwqdyj:[{id:1,mc:"定性标准确定"},{id:2,mc:"定量标准确定"},{id:3,mc:"特别情况确定"}],jbzc:[],regionOption:[],regionParams:{label:"label",value:"label",children:"childrenRegionVo",expandTrigger:"click",checkStrictly:!0},formInline:{gwmc:void 0,bmmc:void 0},smgwglList:[],tjlist:{bmmc:"",gwmc:"",smdj:"",gwqdyj:"",zw:"",zj:"",zc:"",gwdyjb:"",bz:""},gwmcList:[{id:1,mc:"物业管理员"},{id:2,mc:"保洁组长"},{id:3,mc:"保安队长"},{id:4,mc:"保洁员"},{id:5,mc:"保安员"},{id:6,mc:"维修人员"},{id:7,mc:"其他人员"}],xglist:{},bmid:"",updateItemOld:{},xgdialogVisible:!1,dialogVisible_dr:!1,dr_cyz_list:[],multipleTable:[],xqdialogVisible:!1,page:1,pageSize:10,total:0,selectlistRow:[],dialogVisible:!1,tsxx:"",rules:{bmmc:[{required:!0,message:"请输入部门",trigger:"change"}],gwmc:[{required:!0,message:"请输入岗位名称",trigger:"blur"}],smdj:[{required:!0,message:"请选择涉密等级",trigger:"blur"}],gwqdyj:[{required:!0,message:"请选择岗位确定依据",trigger:"blur"}],zw:[{required:!0,message:"请输入职务",trigger:"blur"}],zj:[{required:!0,message:"请输入职级",trigger:"blur"}],zc:[{required:!0,message:"请选择级别职称",trigger:"blur"}]},dwmc:"",year:"",yue:"",ri:"",Date:"",xh:[],dclist:[],dr_dialog:!1,sjdrfs:"",zzjgmc:[],ssbmmc:"",dwxxList:{},filename:"",form:{file:{}},accept:"",dwjy:!0,uploadShow:!1}},computed:{},mounted:function(){this.getLogin(),this.smdjxz(),this.gwqdyjxz(),this.smgwgl(),this.zzjg();var t=localStorage.getItem("dwjy");console.log(t),this.dwjy=1!=t},methods:{addsp:function(t){var e=this;return n()(l.a.mark(function i(){return l.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,Object(r.h)({ryid:t.ryid});case 2:10001==i.sent.code?e.$message({message:"人员在审批中",type:"warning"}):e.$router.push({path:"/fmzdryscTable",query:{type:"add",rwid:t.rwid}});case 4:case"end":return i.stop()}},i,e)}))()},ckls:function(){this.$router.push({path:"/lsSmgwgl"})},forgwqdyj:function(t){var e=void 0;return this.gwqdyj.forEach(function(i){t.gwqdyj==i.id&&(e=i.mc)}),e},getLogin:function(){var t=this;return n()(l.a.mark(function e(){return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.g)();case 2:t.dwxxList=e.sent;case 3:case"end":return e.stop()}},e,t)}))()},smdjxz:function(){var t=this;return n()(l.a.mark(function e(){return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(c.e)();case 2:t.smdj=e.sent;case 3:case"end":return e.stop()}},e,t)}))()},gwqdyjxz:function(){var t=this;return n()(l.a.mark(function e(){return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(c.b)();case 2:t.gwqdyj=e.sent;case 3:case"end":return e.stop()}},e,t)}))()},zzjg:function(){var t=this;return n()(l.a.mark(function e(){var i,s,a,n;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(o._14)();case 2:return i=e.sent,console.log(i),t.zzjgmc=i,s=[],console.log(t.zzjgmc),t.zzjgmc.forEach(function(e){var i=[];t.zzjgmc.forEach(function(t){e.bmm==t.fbmm&&(i.push(t),e.childrenRegionVo=i)}),s.push(e)}),console.log(s),console.log(s[0].childrenRegionVo),a=[],e.next=13,Object(o.U)();case 13:""==(n=e.sent).fbmm&&s.forEach(function(t){""==t.fbmm&&a.push(t)}),""!=n.fbmm&&s.forEach(function(t){console.log(t),t.fbmm==n.fbmm&&a.push(t)}),console.log(a),a[0].childrenRegionVo.forEach(function(e){t.regionOption.push(e)});case 18:case"end":return e.stop()}},e,t)}))()},Radio:function(t){this.sjdrfs=t,console.log("当前选中的数据导入方式",t),""!=this.sjdrfs&&(this.uploadShow=!0)},mbxzgb:function(){this.sjdrfs=""},uploadFile:function(t){this.form.file=t.file,console.log(this.form.file,"this.form.file"),this.filename=t.file.name,console.log(this.filename,"this.filename"),this.uploadZip()},uploadZip:function(){var t=this;return n()(l.a.mark(function e(){var i,s;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return(i=new FormData).append("file",t.form.file),e.next=4,Object(u._52)(i);case 4:s=e.sent,console.log(s),1e4==s.code?(t.dr_cyz_list=s.data,t.dialogVisible_dr=!0,t.hide(),t.$message({title:"提示",message:"上传成功",type:"success"})):10001==s.code?(t.$message({title:"提示",message:s.message,type:"error"}),t.$confirm("["+t.filename+"]中存在问题，是否下载错误批注文件？","提示",{confirmButtonText:"下载",cancelButtonText:"取消",type:"warning"}).then(n()(l.a.mark(function e(){var i;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(u._24)();case 2:i=e.sent,t.dom_download(i,"非密重点人员错误批注.xls");case 4:case"end":return e.stop()}},e,t)})))):10002==s.code&&t.$message({title:"提示",message:s.message,type:"error"});case 7:case"end":return e.stop()}},e,t)}))()},handleSelectionChange:function(t){this.multipleTable=t,console.log("选中：",this.multipleTable)},drcy:function(){var t=this;return n()(l.a.mark(function e(){return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:1==t.sjdrfs?(t.multipleTable.forEach(function(e){var i=t;1==e.gwmc?e.gwmc="物业管理员":2==e.gwmc?e.gwmc="保洁组长":3==e.gwmc?e.gwmc="保安队长":4==e.gwmc?e.gwmc="保洁员":5==e.gwmc?e.gwmc="保安员":6==e.gwmc?e.gwmc="维修人员":7==e.gwmc&&(e.gwmc="其他人员"),Object(r.a)(e).then(function(){i.smgwgl()})}),t.dialogVisible_dr=!1):2==t.sjdrfs&&(Object(r.e)(),setTimeout(function(){t.multipleTable.forEach(function(e){var i=t;1==e.gwmc?e.gwmc="物业管理员":2==e.gwmc?e.gwmc="保洁组长":3==e.gwmc?e.gwmc="保安队长":4==e.gwmc?e.gwmc="保洁员":5==e.gwmc?e.gwmc="保安员":6==e.gwmc?e.gwmc="维修人员":7==e.gwmc&&(e.gwmc="其他人员"),Object(r.a)(e).then(function(){i.smgwgl()})})},500),t.dialogVisible_dr=!1),t.uploadShow=!1,t.dr_dialog=!1;case 3:case"end":return e.stop()}},e,t)}))()},hide:function(){this.filename=null,this.form.file={}},mbdc:function(){var t=this;return n()(l.a.mark(function e(){var i,s,a;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(u._5)();case 2:i=e.sent,s=new Date,a=s.getFullYear()+""+(s.getMonth()+1)+s.getDate(),t.dom_download(i,"非密重点人员模板表-"+a+".xls");case 6:case"end":return e.stop()}},e,t)}))()},onSubmit:function(){this.page=1,this.smgwgl()},returnSy:function(){this.$router.push("/tzglsy")},smgwgl:function(){var t=this;return n()(l.a.mark(function e(){var i,s;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i={page:t.page,pageSize:t.pageSize,xm:t.formInline.xm},e.next=3,Object(r.k)(i);case 3:s=e.sent,console.log(s.records),t.smgwglList=s.records,t.total=s.total;case 7:case"end":return e.stop()}},e,t)}))()},shanchu:function(t){var e=this;""!=this.selectlistRow?this.$confirm("是否继续删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=e;e.selectlistRow.forEach(function(e){var i={rwid:e.rwid};Object(r.f)(i).then(function(){t.smgwgl()})}),e.$message({message:"删除成功",type:"success"})}).catch(function(){e.$message("已取消删除")}):this.$message({message:"未选择删除记录，请选择下列列表",type:"warning"})},showDialog:function(){this.$router.push({path:"/fmzdryscdjTable",query:{type:"add"}})},resetForm:function(){this.tjlist.gwmc="",this.tjlist.smdj="",this.tjlist.gwqdyj="",this.tjlist.bz=""},submitTj:function(t){var e=this;this.$refs[t].validate(function(t){if(!t)return console.log("error submit!!"),!1;var i={bmmc:e.tjlist.bmmc.join("/"),gwmc:e.tjlist.gwmc,smdj:e.tjlist.smdj,gwqdyj:e.tjlist.gwqdyj,bz:e.tjlist.bz,dwid:e.dwxxList.dwid,bmid:e.bmid,sbnf:"2023",cjrid:e.dwxxList.cjrid,cjrxm:e.dwxxList.cjrxm},s=new Date(i.sbnf);if("Invalid Date"!=s&&(i.sbnf=s.getFullYear()),e.onInputBlurXg(1),1e4==e.pdgwbm.code){var l=e;Object(o._63)(i).then(function(){l.resetForm(),l.smgwgl()}),e.dialogVisible=!1,e.$message({message:"添加成功",type:"success"})}})},close:function(t){this.$refs[t].clearValidate()},close1:function(t){this.$refs[t].resetFields()},updataDialog:function(t){var e=this;this.$refs[t].validate(function(t){if(!t)return console.log("error submit!!"),!1;var i=e;e.onInputBlurXg(2),1e4==e.pdgwbm.code&&(e.xglist.bmmc=e.xglist.bmmc.join("/"),Object(o._96)(e.xglist).then(function(){i.smgwgl()}),e.$message.success("修改成功"),e.xgdialogVisible=!1)})},xqyl:function(t){this.$router.push({path:"/fmzdryscdjTable",query:{type:"update",rwid:t.rwid,xqpd:1}})},updateItem:function(t){this.$router.push({path:"/fmzdryscdjTable",query:{type:"update",rwid:t.rwid,xqpd:0}})},exportList:function(){var t=this;return n()(l.a.mark(function e(){var i,s,a,n;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0==t.formInline.bmmc){e.next=7;break}return i={bmmc:t.formInline.bmmc.join("/"),gwmc:t.formInline.gwmc},e.next=4,Object(m.n)(i);case 4:s=e.sent,e.next=10;break;case 7:return e.next=9,Object(m.n)();case 9:s=e.sent;case 10:a=new Date,n=a.getFullYear()+""+(a.getMonth()+1)+a.getDate(),t.dom_download(s,"非密重点人员表-"+n+".xls");case 13:case"end":return e.stop()}},e,t)}))()},dom_download:function(t,e){var i=new Blob([t]),s=window.URL.createObjectURL(i),l=document.createElement("a");console.log("dom",l),l.style.display="none",l.href=s,l.setAttribute("download",e),document.body.appendChild(l),l.click()},selectRow:function(t){this.selectlistRow=t,console.log(t)},handleCurrentChange:function(t){this.page=t,this.smgwgl()},handleSizeChange:function(t){this.page=1,this.pageSize=t,this.smgwgl()},handleClose:function(t){this.resetForm(),this.dialogVisible=!1},handleChange:function(){},onInputBlur:function(t){var e=this.$refs.cascader.getCheckedNodes()[0].data;this.bmid=e.bmm,console.log(e),console.log(t)},onInputBlurXg:function(t){var e=this;return n()(l.a.mark(function i(){var s,a;return l.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(1!=t){i.next=9;break}return s={bmid:e.bmid,gwmc:e.tjlist.gwmc,smdj:e.tjlist.smdj},i.next=4,Object(d.d)(s);case 4:e.pdgwbm=i.sent,console.log(e.pdgwbm),40001==e.pdgwbm.code&&e.$message.error("该涉密等级下岗位已经存在"),i.next=16;break;case 9:if(2!=t){i.next=16;break}return a={bmid:e.bmid,gwmc:e.xglist.gwmc,smdj:e.tjlist.smdj},i.next=13,Object(d.d)(a);case 13:e.pdgwbm=i.sent,console.log(e.pdgwbm),40001==e.pdgwbm.code&&e.$message.error("该涉密等级下岗位已经存在");case 16:case"end":return i.stop()}},i,e)}))()},cz:function(){this.ssbmmc="",this.formInline={}},bmmccl:function(t){void 0!=t&&(this.ssbmmc=t.join("/"))},forsmdj:function(t){var e=void 0;return this.smdj.forEach(function(i){t.smdj==i.id&&(e=i.mc)}),e},forgwmc:function(t){var e=void 0;return this.gwmcList.forEach(function(i){t.gwmc==i.id&&(e=i.mc)}),e}},watch:{}},f={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"bg_con",staticStyle:{height:"calc(100% - 32px)"}},[i("div",{staticStyle:{width:"100%",position:"relative",overflow:"hidden",height:"100%"}},[i("div",{staticClass:"dabg",staticStyle:{height:"100%"}},[i("div",{staticClass:"content",staticStyle:{height:"100%"}},[i("div",{staticClass:"table",staticStyle:{height:"100%"}},[i("div",{staticClass:"select_wrap"},[i("div",{staticClass:"select_wrap_content"},[i("el-form",{staticClass:"demo-form-inline",staticStyle:{float:"left"},attrs:{inline:!0,model:t.formInline,size:"medium"}},[i("el-form-item",{staticStyle:{"font-weight":"700"},attrs:{label:"姓名"}},[i("el-input",{staticClass:"widthw",attrs:{clearable:"",placeholder:"姓名"},model:{value:t.formInline.xm,callback:function(e){t.$set(t.formInline,"xm",e)},expression:"formInline.xm"}})],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSubmit}},[t._v("查询")])],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"warning",icon:"el-icon-circle-close"},on:{click:t.cz}},[t._v("重置")])],1)],1),t._v(" "),i("div",{staticClass:"item_button",staticStyle:{float:"right"}},[this.dwjy?i("el-button",{attrs:{type:"danger",icon:"el-icon-delete-solid",size:"medium"},on:{click:function(e){return t.shanchu()}}},[t._v("删除\n                ")]):t._e()],1),t._v(" "),i("div",{staticClass:"item_button",staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-download",size:"medium"},on:{click:t.exportList}},[t._v("\n                  导出\n                ")])],1),t._v(" "),i("div",{staticClass:"item_button",staticStyle:{float:"right"}},[this.dwjy?i("el-button",{attrs:{type:"primary",icon:"el-icon-upload2",size:"medium"},on:{click:function(e){t.dr_dialog=!0}}},[t._v("\n                  导入\n                ")]):t._e()],1),t._v(" "),i("div",{staticClass:"item_button",staticStyle:{float:"right"}},[this.dwjy?i("el-button",{attrs:{type:"success",icon:"el-icon-plus",size:"medium"},on:{click:t.showDialog}},[t._v("添加\n                ")]):t._e()],1)],1)]),t._v(" "),i("div",{staticClass:"table_content_padding",staticStyle:{height:"100%"}},[i("div",{staticClass:"table_content",staticStyle:{height:"100%"}},[i("el-table",{staticClass:"table",staticStyle:{width:"100%",border:"1px solid #EBEEF5"},attrs:{data:t.smgwglList,border:"","header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},height:"calc(100% - 34px - 57.6px - 10px)",stripe:""},on:{"selection-change":t.selectRow}},[i("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),t._v(" "),i("el-table-column",{attrs:{type:"index",width:"60",label:"序号",align:"center"}}),t._v(" "),i("el-table-column",{attrs:{prop:"xm",label:"姓名"}}),t._v(" "),i("el-table-column",{attrs:{prop:"xb",label:"性别"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("div",[t._v(t._s(1==e.row.xb?"男":"女"))])]}}])}),t._v(" "),i("el-table-column",{attrs:{prop:"nl",label:"年龄"}}),t._v(" "),i("el-table-column",{attrs:{prop:"gzdd",label:"工作地点"}}),t._v(" "),i("el-table-column",{attrs:{prop:"gwmc",label:"岗位名称"}}),t._v(" "),i("el-table-column",{attrs:{prop:"",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.dwjy?i("el-button",{attrs:{size:"medium",type:"text"},on:{click:function(i){return t.updateItem(e.row)}}},[t._v("修改\n                    ")]):t._e(),t._v(" "),0==e.row.sfsc||t.dwjy?i("el-button",{attrs:{size:"medium",type:"text"},on:{click:function(i){return t.addsp(e.row)}}},[t._v("资格审查\n                    ")]):t._e(),t._v(" "),i("el-button",{attrs:{size:"medium",type:"text"},on:{click:function(i){return t.xqyl(e.row)}}},[t._v("详情\n                    ")])]}}])})],1),t._v(" "),i("div",{staticStyle:{border:"1px solid #ebeef5"}},[i("el-pagination",{attrs:{background:"","pager-count":5,"current-page":t.page,"page-sizes":[5,10,20,30],"page-size":t.pageSize,layout:"total, prev, pager, sizes,next, jumper",total:t.total},on:{"current-change":t.handleCurrentChange,"size-change":t.handleSizeChange}})],1)],1)])])]),t._v(" "),i("el-dialog",{staticClass:"scbg-dialog",attrs:{title:"开始导入",width:"600px",visible:t.dr_dialog,"show-close":""},on:{close:t.mbxzgb,"update:visible":function(e){t.dr_dialog=e}}},[i("div",{staticStyle:{padding:"20px"}},[i("div",{staticClass:"daochu"},[i("div",[t._v("一、请点击“导出模板”，并参照模板填写信息。")]),t._v(" "),i("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.mbdc}},[t._v("\n              模板导出\n            ")])],1),t._v(" "),i("div",{staticClass:"daochu"},[i("div",{staticClass:"drfs"},[t._v("二、数据导入方式：")]),t._v(" "),i("el-radio-group",{on:{change:function(e){return t.Radio(e)}},model:{value:t.sjdrfs,callback:function(e){t.sjdrfs=e},expression:"sjdrfs"}},[i("el-radio",{attrs:{label:"1"}},[t._v("追加（导入时已有的记录信息不变，只添加新的记录）")]),t._v(" "),i("el-radio",{attrs:{label:"2"}},[t._v("覆盖（导入时更新已有的记录信息，并添加新的记录）")])],1)],1),t._v(" "),t.uploadShow?i("div",{staticClass:"daochu"},[i("div",[t._v("三、将按模板填写的文件，导入到系统中。")]),t._v(" "),i("el-upload",{staticClass:"upload-button",staticStyle:{display:"inline-block","margin-left":"20px"},attrs:{disabled:!1,"http-request":t.uploadFile,action:"/",data:{},"show-file-list":!1,accept:t.accept}},[i("el-button",{attrs:{size:"small",type:"primary"}},[t._v("上传导入")])],1)],1):t._e()])]),t._v(" "),i("el-dialog",{staticClass:"scbg-dialog",attrs:{width:"1000px",height:"800px",title:"导入非密重点人员",visible:t.dialogVisible_dr,"show-close":""},on:{"update:visible":function(e){t.dialogVisible_dr=e}}},[i("div",{staticStyle:{height:"600px"}},[i("el-table",{ref:"multipleTable",staticStyle:{width:"100%",border:"1px solid #EBEEF5"},attrs:{data:t.dr_cyz_list,height:"100%",stripe:""},on:{"selection-change":t.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),i("el-table-column",{attrs:{prop:"xm",label:"姓名"}}),t._v(" "),i("el-table-column",{attrs:{prop:"xb",label:"性别"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                "+t._s(1==e.row.xb?"男":"女")+"\n              ")]}}])}),t._v(" "),i("el-table-column",{attrs:{prop:"nl",label:"年龄"}}),t._v(" "),i("el-table-column",{attrs:{prop:"gzdd",label:"工作地点"}}),t._v(" "),i("el-table-column",{attrs:{prop:"gwmc",label:"岗位名称",formatter:t.forgwmc}})],1)],1),t._v(" "),i("div",{staticStyle:{height:"30px",display:"flex","align-items":"center","justify-content":"center",margin:"10px 0"}},[i("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.drcy}},[t._v("导 入")]),t._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:function(e){t.dialogVisible_dr=!1}}},[t._v("关 闭")])],1)]),t._v(" "),i("el-dialog",{staticClass:"xg",attrs:{title:"添加涉密岗位","close-on-click-modal":!1,visible:t.dialogVisible,width:"47%","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e},close:function(e){return t.close("formName")}}},[i("el-form",{ref:"formName",attrs:{model:t.tjlist,rules:t.rules,size:"mini","label-width":"120px"}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"部门",prop:"bmmc"}},[i("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.regionOption,props:t.regionParams,filterable:""},on:{change:t.onInputBlur},model:{value:t.tjlist.bmmc,callback:function(e){t.$set(t.tjlist,"bmmc",e)},expression:"tjlist.bmmc"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"岗位名称",prop:"gwmc"}},[i("el-input",{staticStyle:{width:"calc(100% - 20px)"},attrs:{clearable:"",placeholder:"岗位名称"},on:{blur:function(e){return t.onInputBlurXg(1)}},model:{value:t.tjlist.gwmc,callback:function(e){t.$set(t.tjlist,"gwmc",e)},expression:"tjlist.gwmc"}}),t._v(" "),i("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[i("div",[i("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[i("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),i("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),i("div",{staticClass:"smzt"},[t._v("\n                    一般按照机关、单位设置的岗位名称填写。有明确行政职务的涉密岗位，其涉密岗位名称按照行政职务名称填写，没有明确行政职务的涉密岗位，以具体岗位职责命名，如案件查办岗，市场监管岗，执法检查岗等，请勿填写如岗位1、岗位A等不清晰的岗位名称。\n                  ")])]),t._v(" "),i("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1)],1),t._v(" "),i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"涉密等级",prop:"smdj"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择涉密等级"},on:{change:function(e){return t.onInputBlurXg(1)}},model:{value:t.tjlist.smdj,callback:function(e){t.$set(t.tjlist,"smdj",e)},expression:"tjlist.smdj"}},t._l(t.smdj,function(t){return i("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1),t._v(" "),i("el-form-item",{attrs:{label:"岗位确定依据",prop:"gwqdyj"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择岗位确定依据"},model:{value:t.tjlist.gwqdyj,callback:function(e){t.$set(t.tjlist,"gwqdyj",e)},expression:"tjlist.gwqdyj"}},t._l(t.gwqdyj,function(t){return i("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),i("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"备注",prop:"bz"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea"},model:{value:t.tjlist.bz,callback:function(e){t.$set(t.tjlist,"bz",e)},expression:"tjlist.bz"}})],1)],1),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitTj("formName")}}},[t._v("保 存")]),t._v(" "),i("el-button",{attrs:{type:"warning"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("关 闭")])],1)],1),t._v(" "),i("el-dialog",{staticClass:"xg",attrs:{title:"修改涉密岗位","close-on-click-modal":!1,visible:t.xgdialogVisible,width:"47%"},on:{"update:visible":function(e){t.xgdialogVisible=e},close:function(e){return t.close1("form")}}},[i("el-form",{ref:"form",attrs:{model:t.xglist,rules:t.rules,"label-width":"120px",size:"mini"}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"部门",prop:"bmmc"}},[i("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.regionOption,props:t.regionParams,filterable:""},on:{change:function(e){return t.onInputBlur(1)}},model:{value:t.xglist.bmmc,callback:function(e){t.$set(t.xglist,"bmmc",e)},expression:"xglist.bmmc"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"岗位名称",prop:"gwmc"}},[i("el-input",{staticStyle:{width:"calc(100% - 20px)"},attrs:{clearable:"",placeholder:"岗位名称"},on:{blur:function(e){return t.onInputBlurXg(2)}},model:{value:t.xglist.gwmc,callback:function(e){t.$set(t.xglist,"gwmc",e)},expression:"xglist.gwmc"}}),t._v(" "),i("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[i("div",[i("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[i("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),i("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),i("div",{staticClass:"smzt"},[t._v("\n                    一般按照机关、单位设置的岗位名称填写。有明确行政职务的涉密岗位，其涉密岗位名称按照行政职务名称填写，没有明确行政职务的涉密岗位，以具体岗位职责命名，如案件查办岗，市场监管岗，执法检查岗等，请勿填写如岗位1、岗位A等不清晰的岗位名称。\n                  ")])]),t._v(" "),i("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1)],1),t._v(" "),i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"涉密等级",prop:"smdj"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择涉密等级"},on:{change:function(e){return t.onInputBlurXg(1)}},model:{value:t.xglist.smdj,callback:function(e){t.$set(t.xglist,"smdj",e)},expression:"xglist.smdj"}},t._l(t.smdj,function(t){return i("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1),t._v(" "),i("el-form-item",{attrs:{label:"岗位确定依据",prop:"gwqdyj"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择岗位确定依据"},model:{value:t.xglist.gwqdyj,callback:function(e){t.$set(t.xglist,"gwqdyj",e)},expression:"xglist.gwqdyj"}},t._l(t.gwqdyj,function(t){return i("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),i("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"备注",prop:"bz"}},[i("el-input",{attrs:{type:"textarea"},model:{value:t.xglist.bz,callback:function(e){t.$set(t.xglist,"bz",e)},expression:"xglist.bz"}})],1)],1),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.updataDialog("form")}}},[t._v("保 存")]),t._v(" "),i("el-button",{attrs:{type:"warning"},on:{click:function(e){t.xgdialogVisible=!1}}},[t._v("关 闭")])],1)],1),t._v(" "),i("el-dialog",{staticClass:"xg",attrs:{title:"涉密岗位详情","close-on-click-modal":!1,visible:t.xqdialogVisible,width:"47%"},on:{"update:visible":function(e){t.xqdialogVisible=e}}},[i("el-form",{ref:"form",attrs:{model:t.xglist,size:"mini",disabled:"","label-width":"120px"}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"部门",prop:"bmmc"}},[i("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.regionOption,props:t.regionParams,filterable:""},on:{change:function(e){return t.onInputBlur(1)}},model:{value:t.xglist.bmmc,callback:function(e){t.$set(t.xglist,"bmmc",e)},expression:"xglist.bmmc"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"岗位名称",prop:"gwmc"}},[i("el-input",{staticStyle:{width:"calc(100% - 20px)"},attrs:{clearable:"",placeholder:"岗位名称"},on:{blur:function(e){return t.onInputBlur(1)}},model:{value:t.xglist.gwmc,callback:function(e){t.$set(t.xglist,"gwmc",e)},expression:"xglist.gwmc"}}),t._v(" "),i("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[i("div",[i("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[i("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),i("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),i("div",{staticClass:"smzt"},[t._v("\n                    一般按照机关、单位设置的岗位名称填写。有明确行政职务的涉密岗位，其涉密岗位名称按照行政职务名称填写，没有明确行政职务的涉密岗位，以具体岗位职责命名，如案件查办岗，市场监管岗，执法检查岗等，请勿填写如岗位1、岗位A等不清晰的岗位名称。\n                  ")])]),t._v(" "),i("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1)],1),t._v(" "),i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"涉密等级",prop:"smdj"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择涉密等级"},model:{value:t.xglist.smdj,callback:function(e){t.$set(t.xglist,"smdj",e)},expression:"xglist.smdj"}},t._l(t.smdj,function(t){return i("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1),t._v(" "),i("el-form-item",{attrs:{label:"岗位确定依据",prop:"gwqdyj"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择岗位确定依据"},model:{value:t.xglist.gwqdyj,callback:function(e){t.$set(t.xglist,"gwqdyj",e)},expression:"xglist.gwqdyj"}},t._l(t.gwqdyj,function(t){return i("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),i("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"备注",prop:"bz"}},[i("el-input",{attrs:{type:"textarea"},model:{value:t.xglist.bz,callback:function(e){t.$set(t.xglist,"bz",e)},expression:"xglist.bz"}})],1)],1),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"warning"},on:{click:function(e){t.xqdialogVisible=!1}}},[t._v("关 闭")])],1)],1)],1)])])},staticRenderFns:[]};var b=i("VU/8")(p,f,!1,function(t){i("pHmg")},"data-v-4ba4fd95",null);e.default=b.exports},pHmg:function(t,e){}});
//# sourceMappingURL=169.ecda3c49a0f77cf1d199.js.map