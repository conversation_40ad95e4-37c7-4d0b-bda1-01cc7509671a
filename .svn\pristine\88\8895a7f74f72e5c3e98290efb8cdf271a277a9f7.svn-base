{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/gwbgTable.vue", "webpack:///./src/renderer/view/rcgz/gwbgTable.vue?9df5", "webpack:///./src/renderer/view/rcgz/gwbgTable.vue"], "names": ["gwbgTable", "components", "AddLineTable", "BaseTable", "baseTable", "props", "data", "table<PERSON><PERSON>", "value1", "loading", "ry<PERSON><PERSON>ose", "bm", "xm", "gwmclist", "smdjxz", "regionOption", "page", "pageSize", "radioIdSelect", "ryDatas", "total", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "headerCellStyle", "background", "color", "tjlist", "sm<PERSON><PERSON>", "xb", "gj", "dwzwzc", "yrsmgw", "cym", "mz", "hyzk", "zzmm", "lxdh", "sfzhm", "hjdz", "hjdgajg", "czdz", "czgajg", "imageUrl", "yjqk", "qscfqk", "qtqk", "brcn", "smdj", "bgsmdj", "ryglRyscScjlList", "qssj", "zzsj", "szdw", "zw", "zmr", "czbtn1", "czbtn2", "ryglRyscJtcyList", "gxms", "jwjlqk", "cgszd", "ryglRyscYccgList", "cggj", "sy", "ryglRyscJwzzqkList", "jgmc", "zznr", "ryglRyscCfjlList", "cfdw", "cfsj", "cfjg", "cfyy", "ryglRyscSwzjList", "zjmc", "fjlb", "cyqk", "zjhm", "yxq", "qzmc", "ryInfo", "zzmmoptions", "ynoptions", "sltshow", "routeType", "pdfBase64", "fileList", "dialogImageUrl", "dialogVisible", "approvalDialogVisible", "fileRow", "applyColumns", "name", "prop", "scopeType", "formatter", "handleColumnApply", "scqk", "sfty", "id", "disabled2", "ysmdj", "computed", "mounted", "this", "onfwid", "gwxx", "getOrganization", "yhDatas", "$route", "query", "datas", "type", "routezt", "zt", "console", "log", "result", "extends_default", "toString", "methods", "chRadio", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "param", "wrap", "_context", "prev", "next", "bmmc", "Object", "qblist", "sent", "stop", "_this2", "_callee2", "_context2", "xlxz", "xzsmdj", "$message", "warning", "handleSelectBghgwmc", "item", "i", "_this3", "for<PERSON>ach", "item1", "gwmc", "length", "blobToBase64", "blob", "callback", "fileReader", "FileReader", "onload", "e", "target", "readAsDataURL", "handleSelectionChange", "index", "row", "addRow", "push", "delRow", "rows", "splice", "cyjshgxAddRow", "ybrgx", "sfywjjwjlqcqjlxk", "dw", "cyjshgxDelRow", "yscgqkAddRow", "qsrq", "zzrq", "jsnsdgjhdq", "yscgqkDelRow", "jsjwzzqkAddRow", "gjdq", "jsjwzzqkDelRow", "clhwffzqkAddRow", "_data$push", "cljg", "clyy", "defineProperty_default", "clhwffzqkDelRow", "httpRequest", "_this4", "URL", "createObjectURL", "file", "dataurl", "split", "yulan", "shanchu", "_this5", "_callee3", "params", "_context3", "fwlx", "api", "fwdyid", "save", "_this6", "_callee4", "res", "_context4", "bgsmgw", "lcslclzt", "dwid", "code", "lcslid", "slid", "djgwbg", "$router", "message", "_this7", "_callee5", "zzjgList", "shu", "shuList", "list", "_context5", "zzjgmc", "childrenRegionVo", "bmm", "fbmm", "handleSelectionChange1", "handleCurrentChangeRy", "val", "chooseApproval", "handleSizeChangeRy", "searchRy", "bmSelectChange", "undefined", "join", "_this8", "_callee6", "resData", "_context6", "records", "error", "saveAndSubmit", "_this9", "_callee7", "paramStatus", "_res", "_params", "_context7", "keys_default", "clrid", "yhid", "returnIndex", "watch", "rcgz_gwbgTable", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "_v", "ref", "attrs", "model", "label-width", "placeholder", "clearable", "disabled", "$$v", "$set", "scopedSlots", "_u", "key", "fn", "scope", "staticStyle", "width", "multiple", "on", "change", "$event", "_l", "mc", "plain", "click", "title", "close-on-click-modal", "visible", "destroy-on-close", "update:visible", "for", "options", "filterable", "icon", "tableHeight", "showIndex", "tableData", "columns", "showSingleSelection", "handleColumn", "showPagination", "currentPage", "totalCount", "handleCurrentChange", "handleSizeChange", "slot", "clear", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "gRA+HAA,GACAC,YACAC,uBAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAOA,OACAC,SAAA,EACAC,OAAA,GACAC,SAAA,EAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,YACAC,UACAC,gBACAC,KAAA,EACAC,SAAA,GACAC,cAAA,GACAC,WACAC,MAAA,EACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,iBACAC,WAAA,UACAC,MAAA,WAGAC,QACAC,OAAA,GACAnB,GAAA,GACAoB,GAAA,GACAC,GAAA,KACAC,OAAA,GACAC,OAAA,GACAC,IAAA,GACAC,GAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,QAAA,GACAC,KAAA,GACAC,OAAA,GACAC,SAAA,GACAC,KAAA,IACAC,OAAA,IACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,OAAA,IAGAC,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,KAGAC,mBACAC,KAAA,GACAvB,KAAA,GACAwB,OAAA,GACAnD,GAAA,GACAoD,MAAA,GACAP,GAAA,GACAE,OAAA,MACAC,OAAA,KAGAK,mBACAC,KAAA,GACAC,GAAA,GACAZ,KAAA,GACAD,KAAA,GAEAK,OAAA,MACAC,OAAA,KAGAQ,qBACAb,KAAA,GACAc,KAAA,GAEAC,KAAA,GACArC,GAAA,GACA0B,OAAA,MACAC,OAAA,KAGAW,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAhB,OAAA,MACAC,OAAA,KAGAgB,mBACAC,KAAA,gBACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,GACAC,KAAA,eAEAL,KAAA,mBACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,GACAC,KAAA,eAEAL,KAAA,iBACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,GACAC,KAAA,mBAEAL,KAAA,eACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,GACAC,KAAA,mBAEAC,UAEAC,cACA7D,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAEAC,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAEA+D,YACA9D,MAAA,IACAD,MAAA,MAEAC,MAAA,IACAD,MAAA,MAEAgE,QAAA,GACAC,UAAA,GACAC,UAAA,GACAC,YACAC,eAAA,GACAC,eAAA,EACAC,uBAAA,EACAC,QAAA,GAEAC,eACAC,KAAA,KACAC,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAC,qBACAC,OAEAC,KAAA,KACAC,GAAA,IAGAD,KAAA,MACAC,GAAA,IAGAC,WAAA,EACAC,MAAA,KAGAC,YAMAC,QArNA,WAsNAC,KAAAC,SACAD,KAAAxD,OACAwD,KAAAE,OACAF,KAAAG,kBACAH,KAAAI,QAAAJ,KAAAK,OAAAC,MAAAC,MAGAP,KAAApB,UAAAoB,KAAAK,OAAAC,MAAAE,KACAR,KAAAS,QAAAT,KAAAK,OAAAC,MAAAI,GACAC,QAAAC,IAAAZ,KAAAS,SACA,IAAAI,KAKAA,GAFAb,KAAAK,OAAAC,MAAAE,KAEeM,OACfd,KAAA7E,OACA6E,KAAAK,OAAAC,MAAAC,QASAP,KAAA7E,OAAA0F,EACA,GAAAb,KAAA7E,OAAAqB,KACAwD,KAAA7E,OAAAqB,KAAA,KACA,GAAAwD,KAAA7E,OAAAqB,KACAwD,KAAA7E,OAAAqB,KAAA,KACA,GAAAwD,KAAA7E,OAAAqB,OACAwD,KAAA7E,OAAAqB,KAAA,MAEA,MAAAwD,KAAA7E,OAAAqB,KACAwD,KAAAH,MAAA,EACA,MAAAG,KAAA7E,OAAAqB,KACAwD,KAAAH,MAAA,EACA,MAAAG,KAAA7E,OAAAqB,OACAwD,KAAAH,MAAA,GAEA,GAAAG,KAAA7E,OAAAE,GACA2E,KAAA7E,OAAAE,GAAA,IACA,GAAA2E,KAAA7E,OAAAE,KACA2E,KAAA7E,OAAAE,GAAA,KAEAsF,QAAAC,IAAA,cAAAZ,KAAA7E,QACA6E,KAAA7E,OAAAiB,KAAAyE,EAAAzE,KAAA2E,WACAf,KAAA7E,OAAAkB,OAAAwE,EAAAxE,OAAA0E,WACAJ,QAAAC,IAAAZ,KAAA7E,SAoBA6F,SACAC,QADA,aAEAf,KAFA,WAEA,IAAAgB,EAAAlB,KAAA,OAAAmB,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAA7H,EAAA,OAAAyH,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACAJ,GACAK,KAAAX,EAAA/F,OAAA0G,MAFAH,EAAAE,KAAA,EAIAE,OAAAC,EAAA,EAAAD,CAAAN,GAJA,OAIA7H,EAJA+H,EAAAM,KAKAd,EAAAhH,SAAAP,EACAgH,QAAAC,IAAAjH,GANA,wBAAA+H,EAAAO,SAAAV,EAAAL,KAAAC,IASA3E,KAXA,WAWA,IAAA0F,EAAAlC,KAAA,OAAAmB,IAAAC,EAAAC,EAAAC,KAAA,SAAAa,IAAA,IAAAxI,EAAA,OAAAyH,EAAAC,EAAAI,KAAA,SAAAW,GAAA,cAAAA,EAAAT,KAAAS,EAAAR,MAAA,cAAAQ,EAAAR,KAAA,EACAE,OAAAO,EAAA,EAAAP,GADA,OACAnI,EADAyI,EAAAJ,KAEAE,EAAA/H,OAAAR,EAFA,wBAAAyI,EAAAH,SAAAE,EAAAD,KAAAf,IAIAmB,OAfA,WAgBA3B,QAAAC,IAAAZ,KAAAH,OACAc,QAAAC,IAAAZ,KAAA7E,OAAAsB,QACAuD,KAAAH,OAAAG,KAAA7E,OAAAsB,QACAuD,KAAAuC,SAAAC,QAAA,8BAGAC,oBAtBA,SAsBAC,EAAAC,GAAA,IAAAC,EAAA5C,KACAW,QAAAC,IAAA+B,GACA3C,KAAA9F,SAAA2I,QAAA,SAAAC,GACAH,GAAAG,EAAAC,MACApC,QAAAC,IAAAkC,GACAF,EAAAzH,OAAAsB,OAAAqG,EAAAtG,KACAoG,EAAA/C,OAAA+C,EAAAzH,OAAAsB,QACAmG,EAAAL,SAAAC,QAAA,8BAEA,GAAAG,EAAAK,SACAJ,EAAAzH,OAAAsB,OAAA,OAIAwG,aApCA,SAoCAC,EAAAC,GACA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAA,SAAAC,GACAJ,EAAAI,EAAAC,OAAA3C,SAEAuC,EAAAK,cAAAP,IAEAQ,sBA3CA,SA2CAC,EAAAC,GACA5D,KAAAzF,cAAAqJ,GAGAC,OA/CA,SA+CAlK,GACAA,EAAAmK,MACAnH,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,QAIA8G,OA3DA,SA2DAJ,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGAO,cA/DA,SA+DAvK,GACAA,EAAAmK,MACAK,MAAA,GACAlK,GAAA,GACAmK,iBAAA,GACAC,GAAA,GACAvH,GAAA,GACAlB,KAAA,GACAoB,OAAA,MACAC,OAAA,QAIAqH,cA5EA,SA4EAX,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGAY,aAhFA,SAgFA5K,GACAA,EAAAmK,MACAU,KAAA,GACAC,KAAA,GACAC,WAAA,GACAlH,GAAA,GACAR,OAAA,MACAC,OAAA,QAIA0H,aA3FA,SA2FAhB,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGAiB,eA/FA,SA+FAjL,GACAA,EAAAmK,MACAU,KAAA,GACAK,KAAA,GACAnH,KAAA,GACAC,KAAA,GACAX,OAAA,MACAC,OAAA,QAIA6H,eA1GA,SA0GAnB,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGAoB,gBA9GA,SA8GApL,GAAA,IAAAqL,EACArL,EAAAmK,MAAAkB,GACAR,KAAA,GACAS,KAAA,GACAC,KAAA,IAHAC,IAAAH,EAAA,OAIA,IAJAG,IAAAH,EAKA,gBALAG,IAAAH,EAMA,eANAA,KAUAI,gBAzHA,SAyHAzB,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGA0B,YA7HA,SA6HA1L,GAAA,IAAA2L,EAAAtF,KACAA,KAAArB,QAAA4G,IAAAC,gBAAA7L,EAAA8L,MACAzF,KAAAd,QAAAvF,EAAA8L,KAEAzF,KAAAiD,aAAAtJ,EAAA8L,KAAA,SAAAC,GACAJ,EAAAnK,OAAAoB,KAAAmJ,EAAAC,MAAA,WAIAC,MAtIA,WAuIAjF,QAAAC,IAAAZ,KAAApB,WACA,OAAAoB,KAAApB,UACAoB,KAAAjB,eAAAwG,IAAAC,gBAAAxF,KAAAd,SAEAc,KAAAjB,eAAAiB,KAAArB,QAEAqB,KAAAhB,eAAA,GAGA6G,QAhJA,WAiJA7F,KAAA7E,OAAAoB,KAAA,GACAyD,KAAArB,QAAA,IAEAsB,OApJA,WAoJA,IAAA6F,EAAA9F,KAAA,OAAAmB,IAAAC,EAAAC,EAAAC,KAAA,SAAAyE,IAAA,IAAAC,EAAArM,EAAA,OAAAyH,EAAAC,EAAAI,KAAA,SAAAwE,GAAA,cAAAA,EAAAtE,KAAAsE,EAAArE,MAAA,cACAoE,GACAE,KAAA,GAFAD,EAAArE,KAAA,EAIAE,OAAAqE,EAAA,EAAArE,CAAAkE,GAJA,OAIArM,EAJAsM,EAAAjE,KAKArB,QAAAC,IAAAjH,GACAmM,EAAAM,OAAAzM,OAAAyM,OANA,wBAAAH,EAAAhE,SAAA8D,EAAAD,KAAA3E,IASAkF,KA7JA,WA6JA,IAAAC,EAAAtG,KAAA,OAAAmB,IAAAC,EAAAC,EAAAC,KAAA,SAAAiF,IAAA,IAAA/E,EAAAgF,EAAAR,EAAA,OAAA5E,EAAAC,EAAAI,KAAA,SAAAgF,GAAA,cAAAA,EAAA9E,KAAA8E,EAAA7E,MAAA,YACA0E,EAAAzG,OAAAyG,EAAAnL,OAAAsB,QADA,CAAAgK,EAAA7E,KAAA,QAEA0E,EAAA/D,SAAAC,QAAA,6BAFAiE,EAAA7E,KAAA,mBAIA,GAAA0E,EAAAnL,OAAAuL,OAAA1D,OAJA,CAAAyD,EAAA7E,KAAA,gBAKAJ,GACA4E,OAAAE,EAAAF,OACAO,SAAA,GAEAL,EAAApJ,iBAAA2F,QAAA,SAAAU,GACA,KAAAA,EAAAnG,OACAmG,EAAAnG,OAAA,EACA,KAAAmG,EAAAnG,SACAmG,EAAAnG,OAAA,KAIAoE,EAAApG,OAAAkL,EAAAlG,QAAAhF,OACAkL,EAAAnL,OAAAyL,KAAAN,EAAAlG,QAAAwG,KAlBAH,EAAA7E,KAAA,GAmBAE,OAAAqE,EAAA,EAAArE,CAAAN,GAnBA,WAoBA,MADAgF,EAnBAC,EAAAzE,MAoBA6E,KApBA,CAAAJ,EAAA7E,KAAA,gBAqBA0E,EAAAnL,OAAA2L,OAAAN,EAAA7M,KAAAoN,KACA,KAAAT,EAAAnL,OAAAE,GACAiL,EAAAnL,OAAAE,GAAA,EACA,KAAAiL,EAAAnL,OAAAE,KACAiL,EAAAnL,OAAAE,GAAA,GAEA,MAAAiL,EAAAnL,OAAAqB,KACA8J,EAAAnL,OAAA0E,MAAA,EACA,MAAAyG,EAAAnL,OAAAqB,KACA8J,EAAAnL,OAAA0E,MAAA,EACA,MAAAyG,EAAAnL,OAAAqB,OACA8J,EAAAnL,OAAA0E,MAAA,GAEAc,QAAAC,IAAA0F,EAAAnL,OAAA2L,OAAA,sBACAd,EAAAM,EAAAnL,OAnCAsL,EAAA7E,KAAA,GAoCAE,OAAAkF,EAAA,EAAAlF,CAAAkE,GApCA,QAqCA,KArCAS,EAAAzE,KAqCA6E,MACAP,EAAAW,QAAAnD,KAAA,YACAwC,EAAA/D,UACA2E,QAAA,OACA1G,KAAA,aAGAsB,OAAAqE,EAAA,EAAArE,EAAAiF,KAAAP,EAAA7M,KAAAoN,OA5CA,QAAAN,EAAA7E,KAAA,iBAgDA0E,EAAA/D,SAAAC,QAAA,eAhDA,yBAAAiE,EAAAxE,SAAAsE,EAAAD,KAAAnF,IAqDAhB,gBAlNA,WAkNA,IAAAgH,EAAAnH,KAAA,OAAAmB,IAAAC,EAAAC,EAAAC,KAAA,SAAA8F,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAApG,EAAAC,EAAAI,KAAA,SAAAgG,GAAA,cAAAA,EAAA9F,KAAA8F,EAAA7F,MAAA,cAAA6F,EAAA7F,KAAA,EACAE,OAAAqE,EAAA,IAAArE,GADA,cACAuF,EADAI,EAAAzF,KAEAmF,EAAAO,OAAAL,EACAC,KACAH,EAAAO,OAAA7E,QAAA,SAAAH,GACA,IAAAiF,KACAR,EAAAO,OAAA7E,QAAA,SAAAC,GACAJ,EAAAkF,KAAA9E,EAAA+E,OACAF,EAAA7D,KAAAhB,GACAJ,EAAAiF,sBAGAL,EAAAxD,KAAApB,KAEA6E,KAdAE,EAAA7F,KAAA,EAeAE,OAAAqE,EAAA,EAAArE,GAfA,OAgBA,KADA0F,EAfAC,EAAAzF,MAgBA6F,MACAP,EAAAzE,QAAA,SAAAH,GACA,IAAAA,EAAAmF,MACAN,EAAAzD,KAAApB,KAIA,IAAA8E,EAAAK,MACAP,EAAAzE,QAAA,SAAAH,GACA/B,QAAAC,IAAA8B,GACAA,EAAAmF,MAAAL,EAAAK,MACAN,EAAAzD,KAAApB,KAIA6E,EAAA,GAAAI,iBAAA9E,QAAA,SAAAH,GACAyE,EAAA/M,aAAA0J,KAAApB,KAhCA,yBAAA+E,EAAAxF,SAAAmF,EAAAD,KAAAhG,IAmCA2G,uBArPA,SAqPAnE,EAAAC,GACA5D,KAAAzF,cAAAqJ,GAEAmE,sBAxPA,SAwPAC,GACAhI,KAAA3F,KAAA2N,EACAhI,KAAAiI,kBAGAC,mBA7PA,SA6PAF,GACAhI,KAAA3F,KAAA,EACA2F,KAAA1F,SAAA0N,EACAhI,KAAAiI,kBAGAE,SAnQA,WAoQAnI,KAAApG,WACAoG,KAAAiI,kBAGAG,eAxQA,SAwQA1F,QACA2F,GAAA3F,IACA1C,KAAAjG,SAAAC,GAAA0I,EAAA4F,KAAA,OAIAL,eA9QA,WA8QA,IAAAM,EAAAvI,KAAA,OAAAmB,IAAAC,EAAAC,EAAAC,KAAA,SAAAkH,IAAA,IAAAhH,EAAAiH,EAAA,OAAArH,EAAAC,EAAAI,KAAA,SAAAiH,GAAA,cAAAA,EAAA/G,KAAA+G,EAAA9G,MAAA,YACA2G,EAAA1I,OAAA0I,EAAApN,OAAAsB,QADA,CAAAiM,EAAA9G,KAAA,QAEA2G,EAAAhG,SAAAC,QAAA,6BAFAkG,EAAA9G,KAAA,mBAIA,GAAA2G,EAAApN,OAAAuL,OAAA1D,OAJA,CAAA0F,EAAA9G,KAAA,gBAMA2G,EAAAtJ,uBAAA,EACAuC,GACAnH,KAAAkO,EAAAlO,KACAC,SAAAiO,EAAAjO,SACA8L,OAAAmC,EAAAnC,OACAvE,KAAA0G,EAAAxO,SAAAC,GACAC,GAAAsO,EAAAxO,SAAAE,IAZAyO,EAAA9G,KAAA,EAcAE,OAAAqE,EAAA,GAAArE,CAAAN,GAdA,QAcAiH,EAdAC,EAAA1G,MAeA2G,SAEAJ,EAAA/N,QAAAiO,EAAAE,QACAJ,EAAA9N,MAAAgO,EAAAhO,OAEA8N,EAAAhG,SAAAqG,MAAA,WApBAF,EAAA9G,KAAA,iBAuBA2G,EAAAhG,SAAAC,QAAA,eAvBA,yBAAAkG,EAAAzG,SAAAuG,EAAAD,KAAApH,IA4BA0H,cA1SA,WA0SA,IAAAC,EAAA9I,KAAA,OAAAmB,IAAAC,EAAAC,EAAAC,KAAA,SAAAyH,IAAA,IAAAvH,EAAAwE,EAAAgD,EAAAC,EAAAC,EAAA,OAAA9H,EAAAC,EAAAI,KAAA,SAAA0H,GAAA,cAAAA,EAAAxH,KAAAwH,EAAAvH,MAAA,YACA,IAAAkH,EAAAvO,eAAA6O,IAAAN,EAAAvO,eAAAyI,OAAA,GADA,CAAAmG,EAAAvH,KAAA,YAEAJ,GACA4E,OAAA0C,EAAA1C,QAEA0C,EAAA5L,iBAAA2F,QAAA,SAAAU,GACA,KAAAA,EAAAnG,OACAmG,EAAAnG,OAAA,EACA,KAAAmG,EAAAnG,SACAmG,EAAAnG,OAAA,KAGA,KAAA0L,EAAA3N,OAAAE,GACAyN,EAAA3N,OAAAE,GAAA,EACA,KAAAyN,EAAA3N,OAAAE,KACAyN,EAAA3N,OAAAE,GAAA,GAEA,MAAAyN,EAAA3N,OAAAqB,KACAsM,EAAA3N,OAAAqB,KAAA,EACA,MAAAsM,EAAA3N,OAAAqB,KACAsM,EAAA3N,OAAAqB,KAAA,EACA,MAAAsM,EAAA3N,OAAAqB,OACAsM,EAAA3N,OAAAqB,KAAA,GAIA,UAAAsM,EAAAlK,gBAAAyJ,GAAAS,EAAArI,QA1BA,CAAA0I,EAAAvH,KAAA,gBA2BAJ,EAAAmF,SAAA,EACAnF,EAAApG,OAAA0N,EAAA3N,OAAAC,OACAoG,EAAAuF,KAAA+B,EAAA3N,OAAA2L,OACAtF,EAAA6H,MAAAP,EAAAvO,cAAA+O,KA9BAH,EAAAvH,KAAA,GA+BAE,OAAAqE,EAAA,EAAArE,CAAAN,GA/BA,WAgCA,KAhCA2H,EAAAnH,KAgCA6E,KAhCA,CAAAsC,EAAAvH,KAAA,gBAiCAoE,EAAA8C,EAAA3N,OAjCAgO,EAAAvH,KAAA,GAkCAE,OAAAkF,EAAA,EAAAlF,CAAAkE,GAlCA,WAmCA,KAnCAmD,EAAAnH,KAmCA6E,KAnCA,CAAAsC,EAAAvH,KAAA,gBAoCAoH,GACA5C,OAAA0C,EAAA1C,OACAW,KAAA+B,EAAA3N,OAAA2L,aAtCA,EAAAqC,EAAAvH,KAAA,GAyCAE,OAAAqE,EAAA,IAAArE,CAAAkH,GAzCA,QA0CA,KA1CAG,EAAAnH,KA0CA6E,OACAiC,EAAA7B,QAAAnD,KAAA,YACAgF,EAAAvG,UACA2E,QAAA,UACA1G,KAAA,aA9CA,QAAA2I,EAAAvH,KAAA,wBAoDAJ,EAAAmF,SAAA,EACAnF,EAAA6H,MAAAP,EAAAvO,cAAA+O,KACA9H,EAAApG,OAAA0N,EAAA1I,QAAAhF,OAtDA+N,EAAAvH,KAAA,GAuDAE,OAAAqE,EAAA,EAAArE,CAAAN,GAvDA,WAwDA,MADAyH,EAvDAE,EAAAnH,MAwDA6E,KAxDA,CAAAsC,EAAAvH,KAAA,gBAyDAkH,EAAA3N,OAAAyL,KAAAkC,EAAA1I,QAAAwG,KACAkC,EAAA3N,OAAA2L,OAAAmC,EAAAtP,KAAAoN,KAGAmC,EAAAJ,EAAA3N,OA7DAgO,EAAAvH,KAAA,GA8DAE,OAAAkF,EAAA,EAAAlF,CAAAoH,GA9DA,QA+DA,KA/DAC,EAAAnH,KA+DA6E,MACAiC,EAAA7B,QAAAnD,KAAA,YACAgF,EAAAvG,UACA2E,QAAA,UACA1G,KAAA,aAGAsB,OAAAqE,EAAA,EAAArE,EAAAiF,KAAAkC,EAAAtP,KAAAoN,OAtEA,QAAAoC,EAAAvH,KAAA,iBA2EAkH,EAAAvG,UACA2E,QAAA,SACA1G,KAAA,YA7EA,yBAAA2I,EAAAlH,SAAA8G,EAAAD,KAAA3H,IAkFAoI,YA5XA,WA6XAvJ,KAAAiH,QAAAnD,KAAA,cAGA0F,UCvxBeC,GADEC,OAFjB,WAA0B,IAAAC,EAAA3J,KAAa4J,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAa5K,KAAA,UAAA6K,QAAA,YAAArP,MAAA+O,EAAA,QAAAO,WAAA,YAA4EC,YAAA,kBAA8BL,EAAA,KAAUK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,uBAAiCL,EAAA,WAAgBO,IAAA,WAAAC,OAAsBC,MAAAZ,EAAAxO,OAAAqP,cAAA,WAA0CV,EAAA,OAAYK,YAAA,uBAAiCL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAO3P,MAAA,QAAcmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,GAAAgI,SAAA,SAAAyH,GAA+CjB,EAAAkB,KAAAlB,EAAAxO,OAAA,KAAAyP,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO3P,MAAA,MAAamQ,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,GAAAgI,SAAA,SAAAyH,GAA+CjB,EAAAkB,KAAAlB,EAAAxO,OAAA,KAAAyP,IAAgCV,WAAA,uBAAgC,GAAAP,EAAAS,GAAA,KAAAN,EAAA,OAA4BK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAO3P,MAAA,QAAcmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,KAAAgI,SAAA,SAAAyH,GAAiDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,OAAAyP,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO3P,MAAA,YAAkBmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,GAAAgI,SAAA,SAAAyH,GAA+CjB,EAAAkB,KAAAlB,EAAAxO,OAAA,KAAAyP,IAAgCV,WAAA,gBAAyB,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAO3P,MAAA,YAAkBmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,KAAAgI,SAAA,SAAAyH,GAAiDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,OAAAyP,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO3P,MAAA,aAAmBmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,KAAAgI,SAAA,SAAAyH,GAAiDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,OAAAyP,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAO3P,MAAA,UAAgBmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,MAAAgI,SAAA,SAAAyH,GAAkDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,QAAAyP,IAAmCV,WAAA,mBAA4B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO3P,MAAA,UAAgBmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,KAAAgI,SAAA,SAAAyH,GAAiDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,OAAAyP,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAO3P,MAAA,WAAiBmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,KAAAgI,SAAA,SAAAyH,GAAiDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,OAAAyP,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO3P,MAAA,WAAiBmP,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ3P,MAAA+O,EAAAxO,OAAA,KAAAgI,SAAA,SAAAyH,GAAiDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,OAAAyP,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBK,YAAA,WAAAG,OAA8B3P,MAAA,UAAA0E,KAAA,YAAmCyK,EAAA,aAAkBqB,aAAaC,MAAA,QAAed,OAAQG,YAAA,aAAAY,SAAA,IAAyCC,IAAKC,OAAA,SAAAC,GAA0B,OAAA7B,EAAAlH,oBAAAkH,EAAAxO,OAAAqQ,KAAoDjB,OAAQ3P,MAAA+O,EAAAxO,OAAA,OAAAgI,SAAA,SAAAyH,GAAmDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,SAAAyP,IAAoCV,WAAA,kBAA6BP,EAAA8B,GAAA9B,EAAA,kBAAAjH,EAAA/H,GAA4C,OAAAmP,EAAA,aAAuBkB,IAAArQ,EAAA2P,OAAiB3P,MAAA+H,EAAAK,KAAAnI,MAAA8H,EAAAK,UAAuC,OAAA4G,EAAAS,GAAA,KAAAN,EAAA,gBAAwCK,YAAA,WAAAG,OAA8B3P,MAAA,UAAA0E,KAAA,YAAmCyK,EAAA,aAAkBqB,aAAaC,MAAA,QAAed,OAAQG,YAAA,cAA2Ba,IAAKC,OAAA5B,EAAArH,QAAoBiI,OAAQ3P,MAAA+O,EAAAxO,OAAA,OAAAgI,SAAA,SAAAyH,GAAmDjB,EAAAkB,KAAAlB,EAAAxO,OAAA,SAAAyP,IAAoCV,WAAA,kBAA6BP,EAAA8B,GAAA9B,EAAA,gBAAAjH,GAAoC,OAAAoH,EAAA,aAAuBkB,IAAAtI,EAAA/C,GAAA2K,OAAmB3P,MAAA+H,EAAAgJ,GAAA9Q,MAAA8H,EAAA/C,QAAmC,WAAAgK,EAAAS,GAAA,KAAAN,EAAA,KAAiCK,YAAA,cAAwBR,EAAAS,GAAA,mCAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkEK,YAAA,0CAAoDL,EAAA,aAAkBK,YAAA,UAAAG,OAA6BqB,MAAA,IAAWL,IAAKM,MAAAjC,EAAAJ,eAAyBI,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CK,YAAA,KAAAG,OAAwB9J,KAAA,WAAiB8K,IAAKM,MAAAjC,EAAA1B,kBAA4B0B,EAAAS,GAAA,WAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAgDK,YAAA,KAAAG,OAAwB9J,KAAA,WAAiB8K,IAAKM,MAAAjC,EAAAtD,QAAkBsD,EAAAS,GAAA,oBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAyDQ,OAAOuB,MAAA,QAAAC,wBAAA,EAAAC,QAAApC,EAAA1K,sBAAAmM,MAAA,MAAAY,oBAAA,GAAuHV,IAAKW,iBAAA,SAAAT,GAAkC7B,EAAA1K,sBAAAuM,MAAmC1B,EAAA,OAAYK,YAAA,oBAA8BL,EAAA,SAAcQ,OAAO4B,IAAA,MAAUvC,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,eAAgDO,IAAA,cAAAC,OAAyB6B,QAAAxC,EAAAvP,aAAAV,MAAAiQ,EAAAjP,aAAA0R,WAAA,GAAA1B,UAAA,IAAmFY,IAAKC,OAAA5B,EAAAvB,gBAA4BmC,OAAQ3P,MAAA+O,EAAA5P,SAAA,GAAAoJ,SAAA,SAAAyH,GAAiDjB,EAAAkB,KAAAlB,EAAA5P,SAAA,KAAA6Q,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,SAA0BQ,OAAO4B,IAAA,MAAUvC,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA6CK,YAAA,SAAAG,OAA4BI,UAAA,GAAAD,YAAA,MAAkCF,OAAQ3P,MAAA+O,EAAA5P,SAAA,GAAAoJ,SAAA,SAAAyH,GAAiDjB,EAAAkB,KAAAlB,EAAA5P,SAAA,KAAA6Q,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,aAA8BK,YAAA,eAAAG,OAAkC9J,KAAA,UAAA6L,KAAA,kBAAyCf,IAAKM,MAAAjC,EAAAxB,YAAsBwB,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CkB,IAAArB,EAAA/P,SAAAuQ,YAAA,YAAAG,OAAgDgC,YAAA,MAAAC,WAAA,EAAAC,UAAA7C,EAAAnP,QAAAiS,QAAA9C,EAAAxK,aAAAuN,qBAAA,EAAAC,aAAAhD,EAAAnK,kBAAAoN,gBAAA,EAAAC,YAAAlD,EAAAtP,KAAAC,SAAAqP,EAAArP,SAAAwS,WAAAnD,EAAAlP,OAAoP6Q,IAAKyB,oBAAApD,EAAA5B,sBAAAiF,iBAAArD,EAAAzB,mBAAAxE,sBAAAiG,EAAAjG,0BAA6I,GAAAiG,EAAAS,GAAA,KAAAN,EAAA,QAA6BK,YAAA,gBAAAG,OAAmC2C,KAAA,UAAgBA,KAAA,WAAenD,EAAA,aAAkBK,YAAA,UAAAG,OAA6B9J,KAAA,WAAiB8K,IAAKM,MAAA,SAAAJ,GAAyB7B,EAAA1K,uBAAA,MAAoC0K,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA8CK,YAAA,KAAAG,OAAwB9J,KAAA,WAAiB8K,IAAKM,MAAAjC,EAAAd,iBAA2Bc,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCqB,aAAa+B,MAAA,WAAgB,UAEz9MC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEjU,EACAoQ,GATF,EAVA,SAAA8D,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/198.e3ea5e98dabcefbe61fa.js", "sourcesContent": ["<template>\r\n  <div class=\"sec-container\" v-loading=\"loading\">\r\n    <!-- 标题 -->\r\n    <p class=\"sec-title\">基本信息</p>\r\n    <div class=\"sec-form-container\">\r\n      <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n        <!-- 第一部分包括姓名到常住地公安start -->\r\n        <div class=\"sec-header-section\">\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"姓名\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.xm\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"性别\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input placeholder=\"\" v-model=\"tjlist.xb\" clearable disabled></el-input>\r\n              </template>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"部门\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.bmmc\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"职务（职称）\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.zw\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"进入公司日期\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.rzsj\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"入涉密岗位日期\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.sgsj\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"身份证号\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.sfzhm\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.lxdh\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"原涉密岗位\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.gwmc\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"原涉密等级\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.smdj\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"变更后岗位名称\" prop=\"bgsmgw\" class=\"one-line\">\r\n              <el-select v-model=\"tjlist.bgsmgw\" placeholder=\"请选择变更后岗位名称\" style=\"width:100%;\" multiple\r\n                @change=\"handleSelectBghgwmc(tjlist, $event)\">\r\n                <el-option v-for=\"(item, label) in gwmclist\" :label=\"item.gwmc\" :value=\"item.gwmc\" :key=\"label\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"变更后涉密等级\" prop=\"bgsmdj\" class=\"one-line\">\r\n              <el-select v-model=\"tjlist.bgsmdj\" placeholder=\"请选择变更后涉密等级\" style=\"width:100%;\" @change=\"xzsmdj\">\r\n                <el-option v-for=\"item in smdjxz\" :label=\"item.mc\" :value=\"item.id\" :key=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n          <p class=\"sec-title\">备注：本表只适用于涉密人员由高涉密等级调整到低涉密等级</p>\r\n        </div>\r\n\r\n        <!-- 底部操作按钮start -->\r\n        <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n          <el-button @click=\"returnIndex\" class=\"fr ml10\" plain>返回</el-button>\r\n          <el-button @click=\"chooseApproval\" class=\"fr\" type=\"success\">保存并提交</el-button>\r\n          <!-- <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">保存并提交</el-button>\r\n          <el-button @click=\"save\" class=\"fr\" type=\"primary\">保存</el-button> -->\r\n          <el-button @click=\"save\" class=\"fr\" type=\"primary\">临时保存</el-button>\r\n        </div>\r\n        <!-- 底部操作按钮end -->\r\n\r\n      </el-form>\r\n    </div>\r\n    <!-- 发起申请弹框start -->\r\n    <el-dialog title=\"选择审批人\" :close-on-click-modal=\"false\" :visible.sync=\"approvalDialogVisible\" width=\"40%\" :destroy-on-close=\"true\">\r\n      <div class=\"dlFqsqContainer\">\r\n        <label for=\"\">部门:</label>\r\n        <el-cascader v-model=\"ryChoose.bm\" :options=\"regionOption\" :props=\"regionParams\" filterable clearable\r\n          ref=\"cascaderArr\" @change=\"bmSelectChange\"></el-cascader>\r\n        <label for=\"\">姓名:</label>\r\n        <el-input class=\"input2\" v-model=\"ryChoose.xm\" clearable placeholder=\"姓名\"></el-input>\r\n        <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\" @click=\"searchRy\">查询</el-button>\r\n        <BaseTable class=\"baseTable\" :tableHeight=\"'300'\" :key=\"tableKey\" :showIndex=true :tableData=\"ryDatas\" :columns=\"applyColumns\"\r\n          :showSingleSelection=\"true\" :handleColumn=\"handleColumnApply\" :showPagination=true :currentPage=\"page\"\r\n          :pageSize=\"pageSize\" :totalCount=\"total\" @handleCurrentChange=\"handleCurrentChangeRy\"\r\n          @handleSizeChange=\"handleSizeChangeRy\" @handleSelectionChange=\"handleSelectionChange\">\r\n        </BaseTable>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"warning\" class=\"fr ml10\" @click=\"approvalDialogVisible = false\">关 闭</el-button>\r\n        <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">提交</el-button>\r\n        <!-- <el-button @click=\"save\" class=\"fr\" type=\"primary\">保存</el-button> -->\r\n        <div style=\"clear:both\"></div>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 发起申请弹框end -->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  getLcSLid,\r\n  updateZgfs,\r\n  updateSlzt,\r\n  getZzjgList,\r\n  getSpUserList,\r\n  getCurZgfsjl,\r\n  getFwdyidByFwlx,\r\n  getLoginInfo,\r\n  deleteSlxxBySlid\r\n} from '../../../api/index'\r\nimport {\r\n  getDjgwbgInfo,\r\n  submitDjgwbg,\r\n  getDjgwbgInfoByLcsllid,\r\n  updateDjgwbg\r\n} from '../../../api/djgwbg'\r\nimport { getAllGwxx } from '../../../api/qblist'\r\nimport { getAllSmdj } from '../../../api/xlxz'\r\nimport BaseTable from '../../components/common/baseTable.vue'\r\nimport AddLineTable from \"../../components/common/addLineTable.vue\"; //人工纠错组件\r\nexport default {\r\n  components: {\r\n    AddLineTable,\r\n    BaseTable\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      tableKey:1,\r\n      value1: '',\r\n      loading: false,\r\n      // 弹框人员选择条件\r\n      ryChoose: {\r\n        'bm': '',\r\n        'xm': ''\r\n      },\r\n      gwmclist: [],\r\n      smdjxz: [],\r\n      regionOption: [], // 部门下拉\r\n      page: 1, // 审批人弹框当前页\r\n      pageSize: 10, // 审批人弹框每页条数\r\n      radioIdSelect: '', // 审批人弹框人员单选\r\n      ryDatas: [], // 弹框人员选择\r\n      total: 0, // 弹框人员总数\r\n      regionParams: {\r\n        label: 'label', //这里可以配置你们后端返回的属性\r\n        value: 'label',\r\n        children: 'childrenRegionVo',\r\n        expandTrigger: 'click',\r\n        checkStrictly: true\r\n      }, //地域信息配置参数\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: '#EEF7FF',\r\n        color: '#4D91F8'\r\n      },\r\n      // form表单提交数据\r\n      tjlist: {\r\n        smryid: '',\r\n        xm: '',\r\n        xb: '',\r\n        gj: '中国',\r\n        dwzwzc: '',\r\n        yrsmgw: '',\r\n        cym: '',\r\n        mz: '',\r\n        hyzk: '',\r\n        zzmm: '',\r\n        lxdh: '',\r\n        sfzhm: '',\r\n        hjdz: '',\r\n        hjdgajg: '',\r\n        czdz: '',\r\n        czgajg: '',\r\n        imageUrl: '',\r\n        yjqk: '0', // 拥有外籍、境外永久居留权或者长期居留许可情况\r\n        qscfqk: '0', // 配偶子女有关情况\r\n        qtqk: '', // 其他需要说明的情况\r\n        brcn: '',\r\n        smdj: '',\r\n        bgsmdj: '',\r\n      },\r\n      // 主要学习及工作经历\r\n      ryglRyscScjlList: [{\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 家庭成员及社会关系\r\n      ryglRyscJtcyList: [{\r\n        \"gxms\": \"\", //关系描述\r\n        \"zzmm\": \"\", //政治面貌\r\n        \"jwjlqk\": '', //是否有外籍、境外居留权、长期居留许可\r\n        \"xm\": \"\", //姓名\r\n        \"cgszd\": \"\", //工作(学习)单位\r\n        \"zw\": \"\", //职务\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 因私出国(境)情况\r\n      ryglRyscYccgList: [{\r\n        \"cggj\": \"\", //出国国家\r\n        \"sy\": \"\", //事由\r\n        \"zzsj\": \"\", //终止时间\r\n        \"qssj\": \"\", //起始时间\r\n        // \"bz\": \"\",//备注\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 接受境外资助情况\r\n      ryglRyscJwzzqkList: [{\r\n        \"zzsj\": \"\", //时间\r\n        \"jgmc\": \"\", //机构名称\r\n        // \"bz\": \"\",//备注\r\n        \"zznr\": \"\", //资助内容\r\n        \"gj\": \"\", //国家\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 处分或者违法犯罪情况\r\n      ryglRyscCfjlList: [{\r\n        \"cfdw\": \"\", //处罚单位\r\n        \"cfsj\": \"\", //处罚时间\r\n        \"cfjg\": \"\", //处罚结果\r\n        \"cfyy\": \"\", //处罚原因\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 持有因公出入境证件情况\r\n      ryglRyscSwzjList: [{\r\n        'zjmc': '涉密载体（含纸质、光盘等）',\r\n        'fjlb': 1,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': '',\r\n        'qzmc': '部门保密员核定签字：'\r\n      }, {\r\n        'zjmc': '信息设备（含计算机、存储介质等）',\r\n        'fjlb': 2,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': '',\r\n        'qzmc': '部门保密员核定签字：'\r\n      }, {\r\n        'zjmc': '涉密信息系统访问权限回收情况',\r\n        'fjlb': 3,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': '',\r\n        'qzmc': '系统管理员(三员)核定签字：'\r\n      }, {\r\n        'zjmc': '涉密场所出入权限回收情况',\r\n        'fjlb': 4,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': '',\r\n        'qzmc': '涉密场所管理员核定签字：  '\r\n      }],\r\n      ryInfo: {},\r\n      // 政治面貌下拉选项\r\n      zzmmoptions: [{\r\n        value: '中央党员',\r\n        label: '中央党员'\r\n      }, {\r\n        value: '团员',\r\n        label: '团员'\r\n      }, {\r\n        value: '民主党派',\r\n        label: '民主党派'\r\n      }, {\r\n        value: '群众',\r\n        label: '群众'\r\n      }],\r\n      ynoptions: [{\r\n        value: '1',\r\n        label: '是'\r\n      }, {\r\n        value: '0',\r\n        label: '否'\r\n      }],\r\n      sltshow: '', // 文档的缩略图显示\r\n      routeType: '',\r\n      pdfBase64: '',\r\n      fileList: [],\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n      approvalDialogVisible: false, // 选择申请人弹框\r\n      fileRow: '',\r\n      // 选择审核人table\r\n      applyColumns: [{\r\n        name: '姓名',\r\n        prop: 'xm',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      },\r\n      {\r\n        name: '部门',\r\n        prop: 'bmmc',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      },\r\n      {\r\n        name: '岗位',\r\n        prop: 'gwmc',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      }\r\n      ],\r\n      handleColumnApply: [],\r\n      scqk: [\r\n        {\r\n          sfty: '同意',\r\n          id: 1\r\n        },\r\n        {\r\n          sfty: '不同意',\r\n          id: 0\r\n        },\r\n      ],\r\n      disabled2: false,\r\n      ysmdj: '',\r\n    }\r\n  },\r\n  computed: {\r\n    // selectedLabel() {\r\n    //   const option = this.ynoptions.find(o => o.value === this.selectedValue);\r\n    //   return option ? option.label : '';\r\n    // }\r\n  },\r\n  mounted() {\r\n    this.onfwid()\r\n    this.smdj()\r\n    this.gwxx()\r\n    this.getOrganization()\r\n    this.yhDatas = this.$route.query.datas\r\n    // console.log('this.radioIdSelect', this.yhDatas);\r\n    // this.ryInfo = this.$route.query.datas.gwbgscb\r\n    this.routeType = this.$route.query.type\r\n    this.routezt = this.$route.query.zt\r\n    console.log(this.routezt);\r\n    let result = {}\r\n    let iamgeBase64 = ''\r\n    let iamgeBase64Brcn = ''\r\n    if (this.$route.query.type == 'add') {\r\n      // 首次发起申请\r\n      result = {\r\n        ...this.tjlist,\r\n        ...this.$route.query.datas\r\n      }\r\n    } else {\r\n      // 保存 继续编辑\r\n      result = {\r\n        ...this.tjlist,\r\n        ...this.$route.query.datas\r\n      }\r\n    }\r\n    this.tjlist = result\r\n    if (this.tjlist.smdj == 1) {\r\n      this.tjlist.smdj = '核心'\r\n    } else if (this.tjlist.smdj == 2) {\r\n      this.tjlist.smdj = '重要'\r\n    } else if (this.tjlist.smdj == 3) {\r\n      this.tjlist.smdj = '一般'\r\n    }\r\n    if (this.tjlist.smdj == '核心') {\r\n      this.ysmdj = 1\r\n    } else if (this.tjlist.smdj == '重要') {\r\n      this.ysmdj = 2\r\n    } else if (this.tjlist.smdj == '一般') {\r\n      this.ysmdj = 3\r\n    }\r\n    if (this.tjlist.xb == 1) {\r\n      this.tjlist.xb = '男'\r\n    } else if (this.tjlist.xb == 2) {\r\n      this.tjlist.xb = '女'\r\n    }\r\n    console.log('this.tjlist', this.tjlist);\r\n    this.tjlist.yjqk = result.yjqk.toString()\r\n    this.tjlist.qscfqk = result.qscfqk.toString()\r\n    console.log(this.tjlist);\r\n    if (typeof iamgeBase64 === \"string\") {\r\n      // 复制某条消息\r\n      if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n      function validDataUrl(s) {\r\n        return validDataUrl.regex.test(s);\r\n      }\r\n      validDataUrl.regex =\r\n        /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n      if (validDataUrl(iamgeBase64)) {\r\n        let that = this;\r\n\r\n        function previwImg(item) {\r\n          that.tjlist.imageUrl = item;\r\n        }\r\n        previwImg(iamgeBase64);\r\n      }\r\n    }\r\n\r\n  },\r\n  methods: {\r\n    chRadio() { },\r\n    async gwxx() {\r\n      let param = {\r\n        bmmc: this.tjlist.bmmc\r\n      }\r\n      let data = await getAllGwxx(param)\r\n      this.gwmclist = data\r\n      console.log(data);\r\n    },\r\n    //获取涉密等级信息\r\n    async smdj() {\r\n      let data = await getAllSmdj()\r\n      this.smdjxz = data\r\n    },\r\n    xzsmdj() {\r\n      console.log(this.ysmdj);\r\n      console.log(this.tjlist.bgsmdj);\r\n      if (this.ysmdj >= this.tjlist.bgsmdj) {\r\n        this.$message.warning('本表只适用于涉密人员由高涉密等级调整到低涉密等级!')\r\n      }\r\n    },\r\n    handleSelectBghgwmc(item, i) {\r\n      console.log(i);\r\n      this.gwmclist.forEach(item1 => {\r\n        if (i == item1.gwmc) {\r\n          console.log(item1);\r\n          this.tjlist.bgsmdj = item1.smdj\r\n          if (this.ysmdj >= this.tjlist.bgsmdj) {\r\n            this.$message.warning('本表只适用于涉密人员由高涉密等级调整到低涉密等级!')\r\n          }\r\n        } else if (i.length == 0) {\r\n          this.tjlist.bgsmdj = ''\r\n        }\r\n      })\r\n    },\r\n    blobToBase64(blob, callback) {\r\n      const fileReader = new FileReader();\r\n      fileReader.onload = (e) => {\r\n        callback(e.target.result);\r\n      };\r\n      fileReader.readAsDataURL(blob);\r\n    },\r\n    handleSelectionChange(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    // 主要学习及工作经历增加行\r\n    addRow(data) {\r\n      data.push({\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除',\r\n      })\r\n    },\r\n    // 主要学习及工作经历删除行\r\n    delRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 家庭成员及主要社会关系情况 添加行\r\n    cyjshgxAddRow(data) {\r\n      data.push({\r\n        'ybrgx': '',\r\n        'xm': '',\r\n        'sfywjjwjlqcqjlxk': '',\r\n        'dw': '',\r\n        'zw': '',\r\n        'zzmm': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 家庭成员及主要社会关系情况 删除行\r\n    cyjshgxDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 因私出国(境)情况 添加行\r\n    yscgqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'zzrq': '',\r\n        'jsnsdgjhdq': '',\r\n        'sy': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 因私出国(境)情况 删除行\r\n    yscgqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 接受境外资助情况 添加行\r\n    jsjwzzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'gjdq': '',\r\n        'jgmc': '',\r\n        'zznr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 接受境外资助情况 删除行\r\n    jsjwzzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 处分或者违法犯罪情况 添加行\r\n    clhwffzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'cljg': '',\r\n        'clyy': '',\r\n        'cljg': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 处分或者违法犯罪情况 删除行\r\n    clhwffzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 上传本人承诺凭证\r\n    httpRequest(data) {\r\n      this.sltshow = URL.createObjectURL(data.file);\r\n      this.fileRow = data.file\r\n      // this.tjlist.brcn = URL.createObjectURL(this.fileRow)\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.brcn = dataurl.split(',')[1]\r\n      });\r\n    },\r\n    // 预览\r\n    yulan() {\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogImageUrl = URL.createObjectURL(this.fileRow)\r\n      } else {\r\n        this.dialogImageUrl = this.sltshow\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除\r\n    shanchu() {\r\n      this.tjlist.brcn = ''\r\n      this.sltshow = ''\r\n    },\r\n    async onfwid() {\r\n      let params = {\r\n        fwlx: 3\r\n      }\r\n      let data = await getFwdyidByFwlx(params)\r\n      console.log(data);\r\n      this.fwdyid = data.data.fwdyid\r\n    },\r\n    // 保存\r\n    async save() {\r\n      if (this.ysmdj >= this.tjlist.bgsmdj) {\r\n        this.$message.warning('本表只适用于涉密人员由高涉密等级调整到低涉密等级!')\r\n      } else {\r\n        if (this.tjlist.bgsmgw.length != 0) {\r\n          let param = {\r\n            'fwdyid': this.fwdyid,\r\n            'lcslclzt': 3\r\n          }\r\n          this.ryglRyscJtcyList.forEach((e) => {\r\n            if (e.jwjlqk == '否') {\r\n              e.jwjlqk = 0\r\n            } else if (e.jwjlqk == '是') {\r\n              e.jwjlqk = 1\r\n            }\r\n          })\r\n\r\n          param.smryid = this.yhDatas.smryid\r\n          this.tjlist.dwid = this.yhDatas.dwid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.lcslid = res.data.slid\r\n            if (this.tjlist.xb == '男') {\r\n              this.tjlist.xb = 1\r\n            } else if (this.tjlist.xb == '女') {\r\n              this.tjlist.xb = 2\r\n            }\r\n            if (this.tjlist.smdj == '核心') {\r\n              this.tjlist.ysmdj = 1\r\n            } else if (this.tjlist.smdj == '重要') {\r\n              this.tjlist.ysmdj = 2\r\n            } else if (this.tjlist.smdj == '一般') {\r\n              this.tjlist.ysmdj = 3\r\n            }\r\n            console.log(this.tjlist.lcslid, 'this.tjlist.lcslid');\r\n            let params = this.tjlist\r\n            let resDatas = await submitDjgwbg(params)\r\n            if (resDatas.code == 10000) {\r\n              this.$router.push('/gwbgscb')\r\n              this.$message({\r\n                message: '保存成功',\r\n                type: 'success'\r\n              })\r\n            }else {\r\n          deleteSlxxBySlid({ slid: res.data.slid })\r\n        }\r\n          }\r\n        } else {\r\n          this.$message.warning('请选择变更后岗位名称！')\r\n        }\r\n      }\r\n    },\r\n    //全部组织机构List\r\n    async getOrganization() {\r\n      let zzjgList = await getZzjgList()\r\n      this.zzjgmc = zzjgList\r\n      let shu = []\r\n      this.zzjgmc.forEach(item => {\r\n        let childrenRegionVo = []\r\n        this.zzjgmc.forEach(item1 => {\r\n          if (item.bmm == item1.fbmm) {\r\n            childrenRegionVo.push(item1)\r\n            item.childrenRegionVo = childrenRegionVo\r\n          }\r\n        });\r\n        shu.push(item)\r\n      })\r\n      let shuList = []\r\n      let list = await getLoginInfo()\r\n      if (list.fbmm == '') {\r\n        shu.forEach(item => {\r\n          if (item.fbmm == '') {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      if (list.fbmm != '') {\r\n        shu.forEach(item => {\r\n          console.log(item);\r\n          if (item.fbmm == list.fbmm) {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      shuList[0].childrenRegionVo.forEach(item => {\r\n        this.regionOption.push(item)\r\n      })\r\n    },\r\n    handleSelectionChange1(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    handleCurrentChangeRy(val) {\r\n      this.page = val\r\n      this.chooseApproval()\r\n    },\r\n    //列表分页--更改每页显示个数\r\n    handleSizeChangeRy(val) {\r\n      this.page = 1\r\n      this.pageSize = val\r\n      this.chooseApproval()\r\n    },\r\n    // 人员搜索\r\n    searchRy() {\r\n      this.tableKey++\r\n      this.chooseApproval()\r\n    },\r\n    // 发起申请选择人员 人员下拉\r\n    bmSelectChange(item) {\r\n      if (item != undefined) {\r\n        this.ryChoose.bm = item.join('/')\r\n      }\r\n    },\r\n    // 选择审批人\r\n    async chooseApproval() {\r\n      if (this.ysmdj >= this.tjlist.bgsmdj) {\r\n        this.$message.warning('本表只适用于涉密人员由高涉密等级调整到低涉密等级!')\r\n      } else {\r\n        if (this.tjlist.bgsmgw.length != 0) {\r\n          \r\n          this.approvalDialogVisible = true\r\n          let param = {\r\n            'page': this.page,\r\n            'pageSize': this.pageSize,\r\n            'fwdyid': this.fwdyid,\r\n            'bmmc': this.ryChoose.bm,\r\n            'xm': this.ryChoose.xm\r\n          }\r\n          let resData = await getSpUserList(param)\r\n          if (resData.records) {\r\n            // this.loading = false\r\n            this.ryDatas = resData.records\r\n            this.total = resData.total\r\n          } else {\r\n            this.$message.error('数据获取失败！')\r\n          }\r\n        } else {\r\n          this.$message.warning('请选择变更后岗位名称！')\r\n        }\r\n      }\r\n    },\r\n    // 保存并提交\r\n    async saveAndSubmit() {\r\n      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {\r\n        let param = {\r\n          'fwdyid': this.fwdyid\r\n        }\r\n        this.ryglRyscJtcyList.forEach((e) => {\r\n          if (e.jwjlqk == '否') {\r\n            e.jwjlqk = 0\r\n          } else if (e.jwjlqk == '是') {\r\n            e.jwjlqk = 1\r\n          }\r\n        })\r\n        if (this.tjlist.xb == '男') {\r\n          this.tjlist.xb = 1\r\n        } else if (this.tjlist.xb == '女') {\r\n          this.tjlist.xb = 2\r\n        }\r\n        if (this.tjlist.smdj == '核心') {\r\n          this.tjlist.smdj = 1\r\n        } else if (this.tjlist.smdj == '重要') {\r\n          this.tjlist.smdj = 2\r\n        } else if (this.tjlist.smdj == '一般') {\r\n          this.tjlist.smdj = 3\r\n        }\r\n        // this.tjlist.dwid = this.ryInfo.dwid\r\n        // this.tjlist.lcslid = this.ryInfo.lcslid\r\n        if (this.routeType == 'update' && this.routezt == undefined) {\r\n          param.lcslclzt = 2\r\n          param.smryid = this.tjlist.smryid\r\n          param.slid = this.tjlist.lcslid\r\n          param.clrid = this.radioIdSelect.yhid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            let params = this.tjlist\r\n            let resDatas = await updateDjgwbg(params)\r\n            if (resDatas.code == 10000) {\r\n              let paramStatus = {\r\n                'fwdyid': this.fwdyid,\r\n                'slid': this.tjlist.lcslid\r\n              }\r\n              let resStatus\r\n              resStatus = await updateSlzt(paramStatus)\r\n              if (resStatus.code == 10000) {\r\n                this.$router.push('/gwbgscb')\r\n                this.$message({\r\n                  message: '保存并提交成功',\r\n                  type: 'success'\r\n                })\r\n              }\r\n            }\r\n          }\r\n        } else {\r\n          param.lcslclzt = 0\r\n          param.clrid = this.radioIdSelect.yhid\r\n          param.smryid = this.yhDatas.smryid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = this.yhDatas.dwid\r\n            this.tjlist.lcslid = res.data.slid\r\n\r\n\r\n            let params = this.tjlist\r\n            let resDatas = await submitDjgwbg(params)\r\n            if (resDatas.code == 10000) {\r\n              this.$router.push('/gwbgscb')\r\n              this.$message({\r\n                message: '保存并提交成功',\r\n                type: 'success'\r\n              })\r\n            }else {\r\n          deleteSlxxBySlid({ slid: res.data.slid })\r\n        }\r\n          }\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: '请选择审批人',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 返回\r\n    returnIndex() {\r\n      this.$router.push('/gwbgscb')\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.sec-container {\r\n  width: 100%;\r\n  height: calc(100% - 50px);\r\n  overflow-y: overlay;\r\n}\r\n\r\n.sec-title {\r\n  border-left: 5px solid #1b72d8;\r\n  color: #1b72d8;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  text-indent: 10px;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n  width: 258px;\r\n  position: absolute;\r\n  right: 0px;\r\n  top: 0;\r\n  height: 163px;\r\n  border: 1px solid #CDD2D9;\r\n  border-left: 0;\r\n  background: #ffffff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.sec-form-second {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-third {\r\n  border: 1px solid #CDD2D9;\r\n  /* height: 40px;  */\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-four {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  overflow: hidden;\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.yulan {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  color: #3874D5;\r\n  font-weight: 600;\r\n  float: left;\r\n  margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n  width: 178px;\r\n  height: 178px;\r\n  display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 178px;\r\n  height: 178px;\r\n  line-height: 178px;\r\n  text-align: center;\r\n  border: 2px solid #EBEBEB;\r\n}\r\n\r\n.sec-form-six {\r\n  border: 1px solid #CDD2D9;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.ml10 {\r\n  margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n  margin-top: 10px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n  border: none;\r\n}\r\n\r\n.sec-left-text {\r\n  float: left;\r\n  margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n  border-top: 1px solid #CDD2D9;\r\n}\r\n\r\n>>>.longLabel .el-form-item__label {\r\n  width: 500px !important;\r\n}\r\n\r\n>>>.longLabel .el-form-item__content {\r\n  margin-left: 500px !important;\r\n  padding-left: 20px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n  border-top: 0;\r\n} */\r\n.sec-form-second .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n  width: 100%;\r\n  border: 1px solid #EBEEF5;\r\n  height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n.hyzk {\r\n  padding-left: 15px;\r\n  background-color: #F5F7FA;\r\n  width: calc(100% - 16px);\r\n  border-right: 1px solid #CDD2D9;\r\n  color: #C0C4CC;\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n  border: none !important;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n  width: 200px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n  border: none;\r\n  border-right: 1px solid #CDD2D9;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n  border: 1px solid #CDD2D9;;\r\n} */\r\n>>>.el-form-item__label {\r\n  border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n/* /deep/.sec-form-container .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n} */\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/gwbgTable.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"sec-container\"},[_c('p',{staticClass:\"sec-title\"},[_vm._v(\"基本信息\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"姓名\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xm\", $$v)},expression:\"tjlist.xm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"性别\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xb),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xb\", $$v)},expression:\"tjlist.xb\"}})]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"部门\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.bmmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmmc\", $$v)},expression:\"tjlist.bmmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"职务（职称）\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.zw),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zw\", $$v)},expression:\"tjlist.zw\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"进入公司日期\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.rzsj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"rzsj\", $$v)},expression:\"tjlist.rzsj\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"入涉密岗位日期\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.sgsj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sgsj\", $$v)},expression:\"tjlist.sgsj\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"身份证号\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.sfzhm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sfzhm\", $$v)},expression:\"tjlist.sfzhm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"联系电话\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.lxdh),callback:function ($$v) {_vm.$set(_vm.tjlist, \"lxdh\", $$v)},expression:\"tjlist.lxdh\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"原涉密岗位\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.gwmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gwmc\", $$v)},expression:\"tjlist.gwmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"原涉密等级\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.smdj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"smdj\", $$v)},expression:\"tjlist.smdj\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"变更后岗位名称\",\"prop\":\"bgsmgw\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择变更后岗位名称\",\"multiple\":\"\"},on:{\"change\":function($event){return _vm.handleSelectBghgwmc(_vm.tjlist, $event)}},model:{value:(_vm.tjlist.bgsmgw),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bgsmgw\", $$v)},expression:\"tjlist.bgsmgw\"}},_vm._l((_vm.gwmclist),function(item,label){return _c('el-option',{key:label,attrs:{\"label\":item.gwmc,\"value\":item.gwmc}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"变更后涉密等级\",\"prop\":\"bgsmdj\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择变更后涉密等级\"},on:{\"change\":_vm.xzsmdj},model:{value:(_vm.tjlist.bgsmdj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bgsmdj\", $$v)},expression:\"tjlist.bgsmdj\"}},_vm._l((_vm.smdjxz),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.mc,\"value\":item.id}})}),1)],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"备注：本表只适用于涉密人员由高涉密等级调整到低涉密等级\")])]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"plain\":\"\"},on:{\"click\":_vm.returnIndex}},[_vm._v(\"返回\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.chooseApproval}},[_vm._v(\"保存并提交\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.save}},[_vm._v(\"临时保存\")])],1)])],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"选择审批人\",\"close-on-click-modal\":false,\"visible\":_vm.approvalDialogVisible,\"width\":\"40%\",\"destroy-on-close\":true},on:{\"update:visible\":function($event){_vm.approvalDialogVisible=$event}}},[_c('div',{staticClass:\"dlFqsqContainer\"},[_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"部门:\")]),_vm._v(\" \"),_c('el-cascader',{ref:\"cascaderArr\",attrs:{\"options\":_vm.regionOption,\"props\":_vm.regionParams,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.bmSelectChange},model:{value:(_vm.ryChoose.bm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"bm\", $$v)},expression:\"ryChoose.bm\"}}),_vm._v(\" \"),_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"姓名:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"姓名\"},model:{value:(_vm.ryChoose.xm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"xm\", $$v)},expression:\"ryChoose.xm\"}}),_vm._v(\" \"),_c('el-button',{staticClass:\"searchButton\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchRy}},[_vm._v(\"查询\")]),_vm._v(\" \"),_c('BaseTable',{key:_vm.tableKey,staticClass:\"baseTable\",attrs:{\"tableHeight\":'300',\"showIndex\":true,\"tableData\":_vm.ryDatas,\"columns\":_vm.applyColumns,\"showSingleSelection\":true,\"handleColumn\":_vm.handleColumnApply,\"showPagination\":true,\"currentPage\":_vm.page,\"pageSize\":_vm.pageSize,\"totalCount\":_vm.total},on:{\"handleCurrentChange\":_vm.handleCurrentChangeRy,\"handleSizeChange\":_vm.handleSizeChangeRy,\"handleSelectionChange\":_vm.handleSelectionChange}})],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.approvalDialogVisible = false}}},[_vm._v(\"关 闭\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.saveAndSubmit}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-38049881\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/gwbgTable.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-38049881\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./gwbgTable.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./gwbgTable.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./gwbgTable.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-38049881\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./gwbgTable.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-38049881\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/gwbgTable.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}