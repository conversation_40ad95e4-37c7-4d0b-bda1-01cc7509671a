webpackJsonp([145],{SRKn:function(t,e,l){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=l("Xxa5"),a=l.n(s),i=l("exGp"),c=l.n(i),r=l("Bkxl"),o=l("wjd/"),n=(l("gyMJ"),{components:{AddLineTable:l("dCuz").a},props:{},data:function(){return{headerCellStyle:{background:"#EEF7FF",color:"#4D91F8"},fwdyid:"",slid:"",activeName:"second",spznList:[],checkList:[],tjlist:{smryid:"",xm:"",xb:"",gj:"中国",dwzwzc:"",yrsmgw:"",cym:"",mz:"",hyzk:"",zzmm:"",lxdh:"",sfzhm:"",hjdz:"",hjdgajg:"",czdz:"",czgajg:"",imageUrl:"",yjqk:"0",qscfqk:"0",qtqk:"",brcn:"",splx:"",value1:[]},zzhmList:[{zzid:1,fjlb:"因公护照",zjhm:"",yxq:"",checked:0},{zzid:2,fjlb:"因公港澳通行证",zjhm:"",yxq:"",checked:0},{zzid:3,fjlb:"因公台湾通行证",zjhm:"",yxq:"",checked:0},{zzid:4,fjlb:"因私护照",zjhm:"",yxq:"",checked:0},{zzid:5,fjlb:"因私港澳通行证",zjhm:"",yxq:"",checked:0},{zzid:6,fjlb:"因私台湾通行证",zjhm:"",yxq:"",checked:0}],deb:!0,ryglRyscJtcyList:[{gx:"",nl:"",zzmm:"",jwjlqk:"",xm:"",cgszd:"",zw:"",czbtn1:"增加行",czbtn2:""}],gjclList:[],upccLsit:{},disabled1:!0,disabled2:!0,disabled3:!0,btnsftg:!0,btnsfth:!0,jgyf:"",xb:[{xb:"男",id:1},{xb:"女",id:2}],yjgwqk:[{yw:"有",id:1},{yw:"无",id:0}],bmjysfwc:[{sfwc:"已完成",id:1},{sfwc:"未完成",id:0}],scqk:[{sfty:"同意",id:1},{sfty:"不同意",id:0}],zzmmoptions:[],fileList:[],dialogVisible_brcn:!1,dialogVisible:!1,sltshow:"",sltbmcnsshow:"",sltwtsshow:"",dialogImageUrl:"",dialogThtxVisible:!1,dialogBmcnsImageUrl:"",dialogBmcnsVisible:!1,dialogWtsImageUrl:"",dialogWtsVisible:!1,fileRow:"",filebmcnsRow:"",filewtsRow:"",fileryxxRow:"",filexzglRow:"",ylth:!1,ylcn:!1,ylwt:!1,smryList:[],page:1,pageSize:10,total:0,formInline:{bmmc:"",xm:""},selectlistRow:[],xsyc:!0,mbhjid:"",imageUrl:"",imageUrlbrcn:"",ylxy:!0,yldis:!1,file:{},bmcnssmj:"",bmxyssmj:"",dialogVisible_bmcns:!1,bmcnsImageUrl:"",dialogVisible_bmxys:!1,bmxysImageUrl:"",show:!0,show1:!0,typezt:"",lcgzList:[]}},computed:{},mounted:function(){this.typezt=this.$route.query.typezt,"fhxq"!=this.typezt&&(this.deb=!1),this.fwdyid=this.$route.query.fwdyid,console.log("this.fwdyid",this.fwdyid),this.slid=this.$route.query.slid,console.log("this.slid",this.slid),this.lx=this.$route.query.lx,console.log(this.lx),this.spzn(),this.spxx(),this.lcgz()},methods:{zzxx:function(){console.log(this.checkList)},chRadio:function(){},returnIndex:function(){this.$router.push("/cgjsc")},spzn:function(){var t=this;return c()(a.a.mark(function e(){var l,s;return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return l={fwdyid:t.fwdyid},e.next=3,Object(r.a)(l);case 3:1e4==(s=e.sent).code&&(t.spznList=s.data.content);case 5:case"end":return e.stop()}},e,t)}))()},fhry:function(){this.$router.push({path:"/ryspxqy",query:{row:this.$route.query.row}})},spxx:function(){var t=this;return c()(a.a.mark(function e(){var l,s,i,c,r;return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return l={slid:t.slid},s=void 0,e.next=4,Object(o.d)(l);case 4:s=e.sent,console.log(s),t.tjlist=s.data.ryglCgcj,(i=s.data.ryglCgcj).xingbie=2==i.xb?"女":1==i.xb?"男":"",i.hyzk=0==i.hyzk?"未婚":1==i.hyzk?"已婚":"",i.zzmmxx=1==i.zzmm?"中共党员":2==i.zzmm?"团员":3==i.zzmm?"民主党派":4==i.zzmm?"群众":"",i.jbzc=1==i.jbzc?"省部级":2==i.jbzc?"厅局级":3==i.jbzc?"县处级":4==i.jbzc?"乡科级及以下":5==i.jbzc?"高级(含正高、副高)":6==i.jbzc?"中级":7==i.jbzc?"初级及以下":8==i.jbzc?"试用期人员":9==i.jbzc?"工勤人员":10==i.jbzc?"企业职员":11==i.jbzc?"其他":"",c=""!=i.bmmc?"部门："+i.bmmc+"、":"",r=""!=i.zw?"职务："+i.zw+"、":"",i.bmzwzc=c+r+"职称："+i.jbzc,i.smdjxx=1==i.smdj?"核心":2==i.smdj?"重要":3==i.smdj?"一般":"",t.tjlist=i,1==i.splx||0==i.splx?t.tjlist.splx=i.splx.toString():(console.log(123),t.tjlist.splx=""),void 0!=s.data.ryglCgcjSwzjList&&(t.zzhmList=s.data.ryglCgcjSwzjList),t.zzhmList.forEach(function(e){1==e.checked&&t.checkList.push(e.zzid)}),""!=t.tjlist.bmcns&&(t.ylth=!0),""!=t.tjlist.xqbmjyqkb&&(t.ylcn=!0),""!=t.tjlist.hfjlb&&(t.ylwt=!0),""==i.qssj&&void 0==i.qssj||(t.tjlist.value1=[],t.tjlist.value1.push(i.qssj),t.tjlist.value1.push(i.jssj)),0==s.data.ryglCgcjTxqkList.length?t.ryglRyscJtcyList=[{gx:"",nl:"",zzmm:"",jwjlqk:"",xm:"",cgszd:"",zw:"",czbtn1:"增加行",czbtn2:""}]:t.ryglRyscJtcyList=s.data.ryglCgcjTxqkList.map(function(t){return t.czbtn1="增加行",t.czbtn2="删除",t}),t.tjlist.csny=t.tjlist.sfzhm.substring(6,10)+"-"+t.tjlist.sfzhm.substring(10,12)+"-"+t.tjlist.sfzhm.substring(12,14);case 26:case"end":return e.stop()}},e,t)}))()},yulan:function(){this.dialogVisible_brcn=!0;var t,e="data:image/jpeg;base64,"+this.tjlist.brcn;if("string"==typeof e){var l=function t(e){return t.regex.test(e)};if(!e)return;if(l.regex=/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i,l(e)){t=e,this.imageUrlbrcn=t}}},ljbl:function(){this.activeName="second"},beforeAvatarUpload:function(t){var e="image/jpeg"===t.type,l="image/png"===t.type;return e||l||this.$message.error("上传缩略图只能是 JPG/PNG 格式!"),e||l},zpzm:function(t){var e="data:image/jpeg;base64,"+t,l=void 0;if("string"==typeof e){var s=function t(e){return t.regex.test(e)};if(!e)return;if(s.regex=/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i,s(e)){l=e}}return l},ylbmtxth:function(){var t;console.log(this.routeType),t=this.zpzm(this.tjlist.bmcns),this.dialogImageUrl=t,this.dialogThtxVisible=!0},ylbmcns:function(){var t;t=this.zpzm(this.tjlist.xqbmjyqkb),this.dialogBmcnsImageUrl=t,this.dialogBmcnsVisible=!0},ylwts:function(){var t;console.log(this.routeType),t=this.zpzm(this.tjlist.hfjlb),this.dialogWtsImageUrl=t,this.dialogWtsVisible=!0},blobToBase64:function(t,e){var l=new FileReader;l.onload=function(t){e(t.target.result)},l.readAsDataURL(t)},bmcnsyl:function(){this.dialogVisible_bmcns=!0;var t,e="data:image/jpeg;base64,"+this.tjlist.cnssmj;if("string"==typeof e){var l=function t(e){return t.regex.test(e)};if(!e)return;if(l.regex=/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i,l(e)){t=e,this.bmcnsImageUrl=t}}},bmxysyl:function(){this.dialogVisible_bmxys=!0;var t,e="data:image/jpeg;base64,"+this.tjlist.xyssmj;if("string"==typeof e){var l=function t(e){return t.regex.test(e)};if(!e)return;if(l.regex=/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i,l(e)){t=e,this.bmxysImageUrl=t}}},lcgz:function(){var t=this;return c()(a.a.mark(function e(){var l,s;return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return l={fwdyid:t.fwdyid,slid:t.slid},e.next=3,Object(r.g)(l);case 3:1e4==(s=e.sent).code&&(t.lcgzList=s.data.content,t.gjclList=s.data.content);case 5:case"end":return e.stop()}},e,t)}))()}},watch:{}}),d={render:function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"sec-container"},[l("el-button",{directives:[{name:"show",rawName:"v-show",value:t.deb,expression:"deb"}],staticClass:"fhry",attrs:{type:"primary",size:"small"},on:{click:t.fhry}},[t._v("返回")]),t._v(" "),l("el-tabs",{model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[l("el-tab-pane",{attrs:{label:"审批指南",name:"first"}},[l("div",{staticClass:"sec-form-six haveBorderTop sec-footer"},[l("el-button",{staticClass:"fr",attrs:{type:"success"},on:{click:t.ljbl}},[t._v("立即办理")])],1),t._v(" "),l("el-table",{staticClass:"sec-el-table",attrs:{border:"",data:t.spznList,"header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},stripe:""}},[l("el-table-column",{attrs:{type:"index",width:"60",label:"序号",align:"center"}}),t._v(" "),l("el-table-column",{attrs:{prop:"hjmc",label:"办理流程"}})],1)],1),t._v(" "),l("el-tab-pane",{attrs:{label:"审批信息",name:"second"}},[l("p",{staticClass:"sec-title-big"},[t._v("涉密人员保密审查表")]),t._v(" "),l("p",{staticClass:"sec-title"},[t._v("基本信息")]),t._v(" "),l("div",{staticClass:"sec-form-container"},[l("el-form",{ref:"formName",attrs:{model:t.tjlist,"label-width":"225px"}},[l("div",{staticClass:"sec-header-section"},[l("div",{staticClass:"sec-form-left"},[l("el-form-item",{attrs:{label:"姓名"}},[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.xm,callback:function(e){t.$set(t.tjlist,"xm",e)},expression:"tjlist.xm"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"性别"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.xingbie,callback:function(e){t.$set(t.tjlist,"xingbie",e)},expression:"tjlist.xingbie"}})]}}])}),t._v(" "),l("el-form-item",{attrs:{label:"出生年月日"}},[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.csny,callback:function(e){t.$set(t.tjlist,"csny",e)},expression:"tjlist.csny"}})],1)],1),t._v(" "),l("div",{staticClass:"sec-form-left"},[l("el-form-item",{attrs:{label:"政治面貌"}},[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.zzmmxx,callback:function(e){t.$set(t.tjlist,"zzmmxx",e)},expression:"tjlist.zzmmxx"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"涉密岗位"}},[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.gwmc,callback:function(e){t.$set(t.tjlist,"gwmc",e)},expression:"tjlist.gwmc"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"涉密等级"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.smdjxx,callback:function(e){t.$set(t.tjlist,"smdjxx",e)},expression:"tjlist.smdjxx"}})]}}])})],1),t._v(" "),l("div",{staticClass:"sec-form-left"},[l("el-form-item",{attrs:{label:"工作单位及职务"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.bmzwzc,callback:function(e){t.$set(t.tjlist,"bmzwzc",e)},expression:"tjlist.bmzwzc"}})]}}])}),t._v(" "),l("el-form-item",{attrs:{label:"身份证号"}},[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.sfzhm,callback:function(e){t.$set(t.tjlist,"sfzhm",e)},expression:"tjlist.sfzhm"}})],1)],1)]),t._v(" "),l("p",{staticClass:"sec-title"},[t._v("审批事项")]),t._v(" "),l("div",{staticClass:"sec-form-container"},[l("el-form",{ref:"formName",attrs:{model:t.tjlist,"label-width":"225px"}},[l("div",{staticClass:"sec-header-section"},[l("div",{staticClass:"sec-form-left longLabel"},[l("el-form-item",{attrs:{label:"审批事件类型"}},[l("el-radio",{attrs:{label:"1",disabled:""},model:{value:t.tjlist.splx,callback:function(e){t.$set(t.tjlist,"splx",e)},expression:"tjlist.splx"}},[t._v("新申办从出入境证件")]),t._v(" "),l("el-radio",{attrs:{label:"0",disabled:""},model:{value:t.tjlist.splx,callback:function(e){t.$set(t.tjlist,"splx",e)},expression:"tjlist.splx"}},[t._v("申请出国（境）")])],1)],1),t._v(" "),l("div",{staticClass:"sec-form-left longLabel widthzz"},[l("el-form-item",{attrs:{label:"证件类型"}},[l("div",{staticStyle:{display:"flex","flex-direction":"column"}},t._l(t.zzhmList,function(e,s){return l("div",{key:e.zzid},[l("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[l("el-checkbox-group",{attrs:{disabled:""},model:{value:t.checkList,callback:function(e){t.checkList=e},expression:"checkList"}},[l("el-checkbox",{staticStyle:{width:"200px"},attrs:{label:e.zzid}},[t._v(t._s(e.fjlb))])],1),t._v(" "),l("div",[t._v("证件号码:"),l("el-input",{staticStyle:{width:"200px"},attrs:{disabled:""},on:{blur:t.zzxx},model:{value:e.zjhm,callback:function(l){t.$set(e,"zjhm",l)},expression:"item.zjhm"}})],1),t._v(" "),l("div",[t._v("有效期:"),l("el-date-picker",{staticStyle:{width:"200px"},attrs:{type:"date",placeholder:"选择日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",disabled:""},model:{value:e.yxq,callback:function(l){t.$set(e,"yxq",l)},expression:"item.yxq"}})],1)],1)])}),0)])],1),t._v(" "),l("div",{staticClass:"sec-form-left longLabel"},[l("el-form-item",{attrs:{label:"本年度因私出国(境)次数"}},[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.cs,callback:function(e){t.$set(t.tjlist,"cs",e)},expression:"tjlist.cs"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"前往国家(地区)"}},[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.qwgj,callback:function(e){t.$set(t.tjlist,"qwgj",e)},expression:"tjlist.qwgj"}})],1)],1),t._v(" "),l("div",{staticClass:"sec-form-left longLabel"},[l("el-form-item",{attrs:{label:"起止日期"}},[l("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",disabled:""},model:{value:t.tjlist.value1,callback:function(e){t.$set(t.tjlist,"value1",e)},expression:"tjlist.value1"}})],1)],1),t._v(" "),l("div",{staticClass:"sec-form-left longLabel"},[l("el-form-item",{attrs:{label:"出国（境）事由"}},[l("el-input",{attrs:{placeholder:"",clearable:"",disabled:""},model:{value:t.tjlist.sy,callback:function(e){t.$set(t.tjlist,"sy",e)},expression:"tjlist.sy"}})],1)],1)])])],1),t._v(" "),l("p",{staticClass:"sec-title"},[t._v("同行人员情况")]),t._v(" "),l("el-table",{staticClass:"sec-el-table",attrs:{border:"",data:t.ryglRyscJtcyList,"header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},stripe:""}},[l("el-table-column",{attrs:{type:"index",width:"60",label:"序号",align:"center"}}),t._v(" "),l("el-table-column",{attrs:{prop:"gx",label:"与本人关系"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-input",{attrs:{placeholder:"",disabled:""},model:{value:e.row.gx,callback:function(l){t.$set(e.row,"gx",l)},expression:"scope.row.gx"}})]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"xm",label:"姓名"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-input",{attrs:{placeholder:"",disabled:""},model:{value:e.row.xm,callback:function(l){t.$set(e.row,"xm",l)},expression:"scope.row.xm"}})]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"nl",label:"年龄"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-input",{attrs:{placeholder:"",disabled:""},model:{value:e.row.nl,callback:function(l){t.$set(e.row,"nl",l)},expression:"scope.row.nl"}})]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"zzmm",label:"政治面貌"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-select",{attrs:{placeholder:"请选择"},model:{value:e.row.zzmm,callback:function(l){t.$set(e.row,"zzmm",l)},expression:"scope.row.zzmm"}},t._l(t.zzmmoptions,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value,disabled:""}})}),1)]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"zw",label:"工作单位,职务及居住地"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-input",{attrs:{placeholder:"",disabled:""},model:{value:e.row.zw,callback:function(l){t.$set(e.row,"zw",l)},expression:"scope.row.zw"}})]}}])})],1),t._v(" "),l("div",{staticClass:"sec-form-five haveBorderTop",staticStyle:{position:"relative"}},[l("div",{staticClass:"sec-left-text"},[l("div",{staticClass:"flex"},[t._v("\n                1.上传保密提醒谈话确认扫描件\n                "),l("el-button",{directives:[{name:"show",rawName:"v-show",value:t.ylth,expression:"ylth"}],staticClass:"upload-demo",attrs:{size:"mini",type:"primary"},on:{click:t.ylbmtxth}},[t._v("预览")]),t._v(" "),l("el-dialog",{attrs:{visible:t.dialogThtxVisible},on:{"update:visible":function(e){t.dialogThtxVisible=e}}},[l("img",{staticStyle:{width:"100%"},attrs:{src:t.dialogImageUrl,alt:""}}),t._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogThtxVisible=!1}}},[t._v("取 消")])],1)])],1),t._v(" "),l("div",{staticClass:"flex"},[t._v("\n                2.上传离岗离职涉密人员保密承诺书扫描件\n                "),l("el-button",{directives:[{name:"show",rawName:"v-show",value:t.ylcn,expression:"ylcn"}],staticClass:"upload-demo2",attrs:{size:"mini",type:"primary"},on:{click:t.ylbmcns}},[t._v("预览")]),t._v(" "),l("el-dialog",{attrs:{visible:t.dialogBmcnsVisible},on:{"update:visible":function(e){t.dialogBmcnsVisible=e}}},[l("img",{staticStyle:{width:"100%"},attrs:{src:t.dialogBmcnsImageUrl,alt:""}}),t._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogBmcnsVisible=!1}}},[t._v("取 消")])],1)])],1),t._v(" "),l("div",{staticClass:"flex"},[t._v("\n                3.上传脱密期委托管理书扫描件\n                "),l("el-button",{directives:[{name:"show",rawName:"v-show",value:t.ylwt,expression:"ylwt"}],staticClass:"upload-demo3",attrs:{size:"mini",type:"primary"},on:{click:t.ylwts}},[t._v("预览")]),t._v(" "),l("el-dialog",{attrs:{visible:t.dialogWtsVisible},on:{"update:visible":function(e){t.dialogWtsVisible=e}}},[l("img",{staticStyle:{width:"100%"},attrs:{src:t.dialogWtsImageUrl,alt:""}}),t._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogWtsVisible=!1}}},[t._v("取 消")])],1)])],1)])]),t._v(" "),l("p",{staticClass:"sec-title"},[t._v("所在部门意见")]),t._v(" "),l("div",{staticClass:"sec-form-second haveBorderTop longLabel"},[l("el-form-item",{attrs:{label:"信息属实，拟",prop:"bmsc"}},t._l(t.scqk,function(e){return l("el-radio",{key:e.id,attrs:{label:e.id,disabled:t.disabled1},on:{change:t.chRadio},model:{value:t.tjlist.bmsc,callback:function(e){t.$set(t.tjlist,"bmsc",e)},expression:"tjlist.bmsc"}},[t._v(t._s(e.sfty))])}),1),t._v(" "),l("el-form-item",{staticClass:"gtzzsmgwgz",attrs:{label:"出国出境",prop:"gtzzsmgwgz"}})],1),t._v(" "),l("div",{staticClass:"sec-form-second haveBorderTop longLabel"},[l("el-form-item",{attrs:{label:"部门领导审批人",prop:"bmspr"}},[l("el-input",{attrs:{placeholder:"",disabled:t.disabled1,clearable:""},model:{value:t.tjlist.bmscxm,callback:function(e){t.$set(t.tjlist,"bmscxm",e)},expression:"tjlist.bmscxm"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"日期",prop:"bmscrq"}},[l("el-date-picker",{attrs:{disabled:t.disabled1,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:t.tjlist.bmscsj,callback:function(e){t.$set(t.tjlist,"bmscsj",e)},expression:"tjlist.bmscsj"}})],1)],1),t._v(" "),l("p",{staticClass:"sec-title"},[t._v("保密办意见")]),t._v(" "),l("div",{staticClass:"sec-form-second haveBorderTop longLabel"},[l("el-form-item",{attrs:{label:"信息属实，拟",prop:"bmsc"}},t._l(t.scqk,function(e){return l("el-radio",{key:e.id,attrs:{label:e.id,disabled:t.disabled2},on:{change:t.chRadio},model:{value:t.tjlist.bmbsc,callback:function(e){t.$set(t.tjlist,"bmbsc",e)},expression:"tjlist.bmbsc"}},[t._v(t._s(e.sfty))])}),1),t._v(" "),l("el-form-item",{staticClass:"gtzzsmgwgz",attrs:{label:"出国出境",prop:"gtzzsmgwgz"}})],1),t._v(" "),l("div",{staticClass:"sec-form-second haveBorderTop longLabel"},[l("el-form-item",{attrs:{label:"部门领导审批人",prop:"bmspr"}},[l("el-input",{attrs:{placeholder:"",disabled:t.disabled2,clearable:""},model:{value:t.tjlist.bmbscxm,callback:function(e){t.$set(t.tjlist,"bmbscxm",e)},expression:"tjlist.bmbscxm"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"日期",prop:"bmscrq"}},[l("el-date-picker",{attrs:{disabled:t.disabled2,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:t.tjlist.bmbscsj,callback:function(e){t.$set(t.tjlist,"bmbscsj",e)},expression:"tjlist.bmbscsj"}})],1)],1),t._v(" "),l("p",{staticClass:"sec-title"},[t._v("人力资源部意见")]),t._v(" "),l("div",{staticClass:"sec-form-second haveBorderTop longLabel"},[l("el-form-item",{attrs:{label:"信息属实，拟",prop:"bmsc"}},t._l(t.scqk,function(e){return l("el-radio",{key:e.id,attrs:{label:e.id,disabled:t.disabled3},on:{change:t.chRadio},model:{value:t.tjlist.rlsc,callback:function(e){t.$set(t.tjlist,"rlsc",e)},expression:"tjlist.rlsc"}},[t._v(t._s(e.sfty))])}),1),t._v(" "),l("el-form-item",{staticClass:"gtzzsmgwgz",attrs:{label:"出国出境",prop:"gtzzsmgwgz"}})],1),t._v(" "),l("div",{staticClass:"sec-form-second haveBorderTop longLabel"},[l("el-form-item",{attrs:{label:"部门领导审批人",prop:"bmspr"}},[l("el-input",{attrs:{placeholder:"",disabled:t.disabled3,clearable:""},model:{value:t.tjlist.rlscxm,callback:function(e){t.$set(t.tjlist,"rlscxm",e)},expression:"tjlist.rlscxm"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"日期",prop:"bmscrq"}},[l("el-date-picker",{attrs:{disabled:t.disabled3,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:t.tjlist.rlscsj,callback:function(e){t.$set(t.tjlist,"rlscsj",e)},expression:"tjlist.rlscsj"}})],1)],1),t._v(" "),l("p",{staticClass:"sec-title"},[t._v("轨迹处理")]),t._v(" "),l("el-table",{staticClass:"sec-el-table",attrs:{border:"",data:t.gjclList,"header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},stripe:""}},[l("el-table-column",{attrs:{prop:"hjmc",label:"办理环节"}}),t._v(" "),l("el-table-column",{attrs:{prop:"clrid",label:"办理人"}}),t._v(" "),l("el-table-column",{attrs:{prop:"bllx",label:"办理类型"}}),t._v(" "),l("el-table-column",{attrs:{prop:"clyj",label:"办理意见"}}),t._v(" "),l("el-table-column",{attrs:{prop:"xybclr",label:"下一步办理人"}}),t._v(" "),l("el-table-column",{attrs:{prop:"clsj",label:"办理时间"}})],1)],1)],1)]),t._v(" "),l("el-tab-pane",{attrs:{label:"流程跟踪",name:"third"}},[l("el-table",{staticClass:"sec-el-table",attrs:{border:"",data:t.lcgzList,"header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},stripe:""}},[l("el-table-column",{attrs:{prop:"hjmc",label:"办理环节"}}),t._v(" "),l("el-table-column",{attrs:{prop:"clrid",label:"办理人"}}),t._v(" "),l("el-table-column",{attrs:{prop:"bllx",label:"办理类型"}}),t._v(" "),l("el-table-column",{attrs:{prop:"clyj",label:"办理意见"}}),t._v(" "),l("el-table-column",{attrs:{prop:"xybclr",label:"下一步办理人"}}),t._v(" "),l("el-table-column",{attrs:{prop:"clsj",label:"办理时间"}})],1)],1)],1)],1)},staticRenderFns:[]};var b=l("VU/8")(n,d,!1,function(t){l("tsWp")},"data-v-5df8a4f4",null);e.default=b.exports},tsWp:function(t,e){}});
//# sourceMappingURL=145.e8c6379fdc876c33d51f.js.map