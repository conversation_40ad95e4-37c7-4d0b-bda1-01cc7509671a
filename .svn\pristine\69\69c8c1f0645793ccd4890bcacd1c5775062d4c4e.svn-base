<template>
  <div class="bg_con" style="height: 100%;">
    <div style="width: 100%; position: relative; overflow: hidden;height: calc(100% - 38px); ">
      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="台账时间" style="font-weight: 700;">
                  <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widthw">
                  </el-input> -->
                  <el-select v-model="formInline.tzsj" placeholder="台账时间">
                    <el-option v-for="item in yearSelect" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.sxmc" clearable placeholder="名称" class="widthx">
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-select v-model="formInline.mj" clearable placeholder="密级" class="widthx">
                    <el-option v-for="item in mjxz" :label="item.mc" :value="item.id" :key="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="启用日期" style="font-weight: 700;">
                  <el-date-picker v-model="formInline.qrsj" type="daterange" range-separator="至" style="width:300px;" start-placeholder="查询起始时间" end-placeholder="查询结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <!-- <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item> -->
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" @click="fh()">返回
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList">
                    导出
                  </el-button>
                </el-form-item>
                <!-- <el-form-item style="float: right;">
                  <input type="file" ref="upload" style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;" accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="dialogVisible = true" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item> -->
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="bmqsxqdqlList" border @selection-change="selectRow" :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 41px - 3px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <!-- <el-table-column prop="mc" label="名称"></el-table-column> -->
                  <el-table-column prop="sxmc" label="事项名称"></el-table-column>
                  <el-table-column prop="mj" label="密级" :formatter="formj"></el-table-column>
                  <el-table-column prop="bmqx" label="保密期限"></el-table-column>
                  <el-table-column prop="qrsj" label="确认时间"></el-table-column>
                  <el-table-column prop="qrly" label="确认理由"></el-table-column>
                  <el-table-column prop="djsj" label="登记时间"></el-table-column>
                  <el-table-column prop="tznf" label="台账时间"></el-table-column>
                  <!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button> -->
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize" layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入不明确事项信息" class="scbg-dialog" :visible.sync="dialogVisible_dr" show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange" style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <!-- <el-table-column prop="不明确事项产生单位" label="不明确事项产生单位"></el-table-column> -->
              <el-table-column prop="事项名称" label="事项名称"></el-table-column>
              <el-table-column prop="密级" label="密级"></el-table-column>
              <el-table-column prop="保密期限" label="保密期限"></el-table-column>
              <el-table-column prop="确认理由" label="确认理由"></el-table-column>
              <el-table-column prop="备注" label="备注"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->
        <el-dialog title="不明确事项信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg" :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
            <!-- <el-form-item label="不明确事项产生单位" prop="bmcsxcsdw">
              <el-input placeholder="不明确事项产生单位" v-model="tjlist.bmcsxcsdw" clearable ></el-input>
            </el-form-item> -->
            <el-form-item label="事项名称" prop="sxmc" class="one-line">
              <el-input placeholder="事项名称" v-model="tjlist.sxmc" clearable></el-input>
            </el-form-item>
            <el-form-item label="登记时间" prop="djsj" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <el-date-picker v-model="tjlist.djsj" style="width: 100%;" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="说明" prop="sxsm" class="one-line-textarea bmqsx">
              <el-input type="textarea" placeholder="说明" v-model="tjlist.sxsm" clearable></el-input>
            </el-form-item>
            <el-form-item label="密级" prop="mj" class="one-line">
              <el-select v-model="tjlist.mj" placeholder="请选择密级" style="width: 100%;">
                <el-option v-for="item in mjxz" :label="item.mc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="保密期限（年）" prop="bmqx" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <!-- <el-date-picker v-model="tjlist.bmqx" class="cd" clearable type="date" placeholder="选择日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker> -->
              <el-input v-model="tjlist.bmqx" clearable placeholder="保密期限" @blur="bmqx = $event.target.value" oninput="value=value.replace(/[^\d.]/g,'')">
              </el-input>
            </el-form-item>
            <el-form-item label="确认时间" prop="qrsj" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <el-date-picker v-model="tjlist.qrsj" style="width: 100%;" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="确认理由" prop="qrly" class="one-line-textarea bmqsx">
              <el-input type="textarea" placeholder="确认理由" v-model="tjlist.qrly" clearable></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" placeholder="备注" v-model="tjlist.bz" clearable></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改不明确事项信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%" class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
            <!-- <el-form-item label="不明确事项产生单位" prop="bmcsxcsdw">
              <el-input placeholder="不明确事项产生单位" v-model="xglist.bmcsxcsdw" clearable ></el-input>
            </el-form-item> -->
            <el-form-item label="事项名称" prop="sxmc" class="one-line">
              <el-input placeholder="事项名称" v-model="xglist.sxmc" clearable></el-input>
            </el-form-item>
            <el-form-item label="登记时间" prop="djsj" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <el-date-picker v-model="xglist.djsj" style="width: 100%;" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="说明" prop="sxsm" class="one-line-textarea bmqsx">
              <el-input type="textarea" placeholder="说明" v-model="xglist.sxsm" clearable></el-input>
            </el-form-item>
            <el-form-item label="密级" prop="mj" class="one-line">
              <el-select v-model="xglist.mj" placeholder="请选择密级" style="width: 100%;">
                <el-option v-for="item in mjxz" :label="item.mc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="保密期限（年）" prop="bmqx" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <!-- <el-date-picker v-model="xglist.bmqx" class="cd" clearable type="date" placeholder="选择日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker> -->
              <el-input v-model="xglist.bmqx" clearable placeholder="保密期限" @blur="bmqx = $event.target.value" oninput="value=value.replace(/[^\d.]/g,'')">
              </el-input>
            </el-form-item>
            <el-form-item label="确认时间" prop="qrsj" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <el-date-picker v-model="xglist.qrsj" style="width: 100%;" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="确认理由" prop="qrly" class="one-line-textarea bmqsx">
              <el-input type="textarea" placeholder="确认理由" v-model="xglist.qrly" clearable></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" placeholder="备注" v-model="xglist.bz" clearable></el-input>
            </el-form-item>

          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="不明确事项信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%" class="xg">
          <el-form ref="form" :model="xglist" label-width="180px" size="mini" disabled>
            <!-- <el-form-item label="不明确事项产生单位" prop="bmcsxcsdw">
              <el-input placeholder="不明确事项产生单位" v-model="xglist.bmcsxcsdw" clearable ></el-input>
            </el-form-item> -->
            <el-form-item label="事项名称" prop="sxmc" class="one-line">
              <el-input placeholder="事项名称" v-model="xglist.sxmc" clearable></el-input>
            </el-form-item>
            <el-form-item label="登记时间" prop="djsj" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <el-date-picker v-model="xglist.djsj" style="width: 100%;" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="说明" prop="sxsm" class="one-line-textarea">
              <el-input type="textarea" placeholder="说明" v-model="xglist.sxsm" clearable></el-input>
            </el-form-item>
            <el-form-item label="密级" prop="mj" class="one-line">
              <el-select v-model="xglist.mj" placeholder="请选择密级" style="width: 100%;">
                <el-option v-for="item in mjxz" :label="item.mc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="保密期限（年）" prop="bmqx" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <!-- <el-date-picker v-model="xglist.bmqx" class="cd" clearable type="date" placeholder="选择日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker> -->
              <el-input v-model="xglist.bmqx" clearable placeholder="保密期限" @blur="bmqx = $event.target.value" oninput="value=value.replace(/[^\d.]/g,'')">
              </el-input>
            </el-form-item>
            <el-form-item label="确认时间" prop="qrsj" class="one-line">
              <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
              <el-date-picker v-model="xglist.qrsj" style="width: 100%;" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="确认理由" prop="qrly" class="one-line-textarea">
              <el-input type="textarea" placeholder="确认理由" v-model="xglist.qrly" clearable></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" placeholder="备注" v-model="xglist.bz" clearable></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>

</template>
<script>
import {
   saveBmqsxqdqk,
   removeBmqsxqdqk,
   updateBmqsxqdqk,
   getBmqsxqdqkList
} from "../../../api/index"
  import {
    getBmqsxqdqkHistoryPage
  } from '../../../api/lstz'
  import {
    exportLsBmqsxqdqkData
  } from '../../../api/dcwj'
import {getmj} from '../../../api/xlxz'
export default {
  components: {},
  props: {},
  data () {
    return {
        yearSelect: [],
      excelList: [],
      pdaqcp: 0, //提示信息判断
      mjxz: [], //下拉框数据
      bmqsxqdqlList: [], //列表数据
      tableDataCopy: [], //查询备份数据
      xglist: {}, //修改与详情数据
      updateItemOld: {},
      xgdialogVisible: false, //修改弹框
      xqdialogVisible: false, //详情弹框
      formInline: {
          tzsj: new Date().getFullYear().toString()
      }, //查询区域数据
      tjlist: {
        bmcsxcsdw: '',
        sxmc: '',
        djsj: '',
        sxsm: '',
        mj: '',
        bmqx: '',
        qrsj: '',
        qrly: '',
        bz: '',
      }, //添加数据
      rules: {
        bmcsxcsdw: [{
          required: true,
          message: '请输入不明确事项产生单位',
          trigger: 'blur'
        },],
        sxmc: [{
          required: true,
          message: '请输入事项名称',
          trigger: 'blur'
        },],
        djsj: [{
          required: true,
          message: '请选择登记时间',
          trigger: 'blur'
        },],
        sxsm: [{
          required: true,
          message: '请输入说明',
          trigger: 'blur'
        },],
        mj: [{
          required: true,
          message: '请选择密级',
          trigger: 'blur'
        },],
        bmqx: [{
          required: true,
          message: '请选择保密期限',
          trigger: 'blur'
        },],
        qrsj: [{
          required: true,
          message: '请选择确认时间',
          trigger: 'blur'
        },],
        qrly: [{
          required: true,
          message: '请输入确认理由',
          trigger: 'blur'
        },],
      }, //校验
      page: 1, //当前页
      pageSize: 10, //每页条数
      total: 0, //总共数据数
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      dwmc: '',
      dwdm: '',
      dwlxr: '',
      dwlxdh: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    };
  },
  computed: {},
  mounted () {
      //获取最近十年的年份
      let yearArr = []
      for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
        yearArr.push(
          {
            label: i.toString(),
            value: i.toString()
          })
      }
      yearArr.unshift({
        label: "全部",
        value: ""
      })
      this.yearSelect = yearArr
  this.bmqsxqdqk()
  this.smmj()
  },
  methods: {
    async smmj(){
      this.mjxz = await getmj()
    },
    Radio (val) {
    
    },
    mbxzgb () {
     
    },
    mbdc () {
    
    },
    //----成员组选择
    handleSelectionChange (val) {
     
    },
    //---确定导入成员组
    drcy () {
     
    },
    //----表格导入方法
    readExcel (e) {
    
    },
    chooseFile () {
      
    },
    //导出
    async exportList() {
      var param = {
        sxmc: this.formInline.sxmc,
        mj: this.formInline.mj,
          nf: this.formInline.tzsj
      }
      if (this.formInline.qrsj != null) {
        param.kssj = this.formInline.qrsj[0]
        param.jssj = this.formInline.qrsj[1]
      }
      var returnData = await exportLsBmqsxqdqkData(param);
      var date = new Date()
      var sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "不明确事项确定情况数据信息表-" + sj + ".xls");
    },

    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    cz () {
      this.formInline = {}
    },
    //修改
    updataDialog (form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          let that = this
          updateBmqsxqdqk(this.xglist).then(()=>{
that.bmqsxqdqk();
          })
          
          // 关闭dialog
          this.$message.success("修改成功");
          this.xgdialogVisible = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    //详情弹框
    xqyl (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row));
      this.xglist = JSON.parse(JSON.stringify(row));
      // this.form1.ywlx = row.ywlx
      console.log("old", row);
      console.log("this.xglist.ywlx", this.xglist);
      this.xqdialogVisible = true;
    },
    //修改弹框
    updateItem (row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row));
      this.xglist = JSON.parse(JSON.stringify(row));
      // this.form1.ywlx = row.ywlx
      console.log("old", row);
      console.log("this.xglist.ywlx", this.xglist);
      this.xgdialogVisible = true;
    },
    fh() {
      this.$router.go(-1)
    },
    //查询
    onSubmit () {
      this.page = 1
      this.bmqsxqdqk()
      // //  form是查询条件
      // console.log(this.formInline);
      // // 备份了一下数据
      // let arr = this.tableDataCopy
      // // 通过遍历key值来循环处理
      // Object.keys(this.formInline).forEach(e => {
      //   // 调用自己定义好的筛选方法
      //   console.log(this.formInline[e]);
      //   arr = this.filterFunc(this.formInline[e], e, arr)
      // })
      // // 为表格赋值
      // this.bmqsxqdqlList = arr
     
    },
    //查询方法
    filterFunc (val, target, filterArr) {
     
    },

    returnSy () {
      this.$router.push("/tzglsy");
    },
    //获取列表的值
    async bmqsxqdqk () {
     let params = {
        page: this.page,
        pageSize: this.pageSize,
        sxmc:this.formInline.sxmc,
        mj:this.formInline.mj,
          // tznf: this.formInline.tzsj
      };
      if(this.formInline.tzsj){
        params.tznf = this.formInline.tzsj
      }
      if (this.formInline.qrsj != null) {
           params.kssj = this.formInline.qrsj[0]
            params.jssj = this.formInline.qrsj[1]
        }
      // Object.assign(params, this.formInline);
      let resList = await getBmqsxqdqkHistoryPage(params);
      this.bmqsxqdqlList = resList.records;
      this.total = resList.total;
    },
    //删除
    shanchu (id) {
      let that = this
      if (this.selectlistRow != '') {
        this.$confirm("是否继续删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let valArr = this.selectlistRow;
            // console.log("....", val);
            valArr.forEach(function (item) {
              let params = {
                sxid:item.sxid,
                dwid:item.dwid
              }
              removeBmqsxqdqk(params).then(()=>{
                that.bmqsxqdqk();
              });
              console.log("删除：", item);
              console.log("删除：", item);
            });
            let params = valArr;
            this.$message({
              message: "删除成功",
              type: "success",
            });
            
          })
          .catch(() => {
            this.$message("已取消删除");
          });
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog () {
      
      this.dialogVisible = true;
    },
    //确定添加成员组
    submitTj (formName) {
     this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            // bmcsxcsdw: this.tjlist.bmcsxcsdw,
            dwmc:'111',
            dwid:'111',
            sxmc: this.tjlist.sxmc,
            djsj: this.tjlist.djsj,
            // lx: this.tjlist.lx,
            sxsm: this.tjlist.sxsm,
            mj: this.tjlist.mj,
            bmqx: this.tjlist.bmqx,
            qrsj: this.tjlist.qrsj,
            qrly: this.tjlist.qrly,
            bz: this.tjlist.bz,
            cjrid:'111',
            // bmqsxqdqk: getUuid()
          };
          let that = this
          saveBmqsxqdqk(params).then(()=>{
that.resetForm();
          that.bmqsxqdqk();
          });
          this.dialogVisible = false;

          this.$message({
            message: '添加成功',
            type: 'success'
          });
          


        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },

    deleteTkglBtn () { },
    //选中列表的数据
    selectRow (val) {
      this.selectlistRow = val
    },
    //列表分页--跳转页数
    handleCurrentChange (val) {
      this.page = val;
      this.bmqsxqdqk();
    },
    //列表分页--更改每页显示个数
    handleSizeChange (val) {
      this.page = 1;
      this.pageSize = val;
      this.bmqsxqdqk();
    },
    //添加重置
    resetForm () {
      this.tjlist.bmcsxcsdw = "";
      this.tjlist.sxmc = "";
      this.tjlist.djsj = "";
      this.tjlist.sxsm = "";
      this.tjlist.mj = "";
      this.tjlist.bmqx = "";
      this.tjlist.qrsj = "";
      this.tjlist.qrly = "";
      this.tjlist.bz = "";
    },
    handleClose (done) {
      this.resetForm();
      this.dialogVisible = false;
    },
    // 弹框关闭触发
    close (formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    //取消校验
    close1 (form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    formj(row) {
        let hxsj
        this.mjxz.forEach(item => {
          if (row.mj == item.id) {
            hxsj = item.mc
          }
        })
        return hxsj
      },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
} */

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 5vw;
}

.widthx {
  width: 6.5vw;
}

.cd {
  width: 184px;
}

/deep/.el-form--inline .el-form-item {
  margin-right: 9px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
</style>