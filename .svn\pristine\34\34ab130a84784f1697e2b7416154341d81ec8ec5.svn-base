<template>
  <div class="bgglContainer" v-loading="pageLoading">
    <div class="bgglDiv" onselectstart="return false" ref="contentData">
      <!-- 存放文章的容器 -->
      <!-- word -->
      <div
        v-if="wordShow"
        class="wordContainer"
        :class="gdt ? 'gdt' : ''"
        id="bodyContainer"
        ref="file"
      ></div>
      <!-- pdf -->
      <div class="pdfContainer" :class="gdt ? 'gtpdf' : ''" v-if="pdfShow">
        <!-- <pdf :src="pdfSrc" v-for="i in pageCount" :key="i" :page="i"></pdf> -->
        <!-- {{ pdfSrc }} -->
        
      <iframe ref="myiframe" id="myiframe" width="1000"
        height="100%" :src="pdfSrc" frameborder="0"> </iframe>
      </div>
      <!-- HTML -->
      <div class="pdfContainer" :class="gdt ? 'gdt' : ''" v-if="xmlShow">
        <iframe
          :src="htmlSrc"
        ></iframe>
        <div v-html="htmlContent"></div>
        <!-- <div v-html="htmlSrc" v-for="i in pageCount" :key="i" :page="i"></div> -->
        <!-- <img :src="htmlSrc" v-for="i in pageCount" :key="i" :page="i"> -->

        <!-- <iframe
          ref="iframe"
          v-for="i in pageCount"
          :key="i"
          :page="i"
        ></iframe> -->
        <!-- <div v-html="htmlContent"></div> -->
        <!-- <div ref="contentArea"></div> -->
      </div>
    </div>
    <!-- 界面功能按钮 -->
    <div class="gnbutton">
      <div class="backBtn cursorClick text-center fl" @click="fhsy">
        <p>返回</p>
        <img src="./img/backbtn.png" alt="" />
      </div>
      <!-- <div class="downloadBtn cursorClick text-center" @click="downloadFile(JSON.parse($route.query.item))" v-if="gdt">
          <p>下载</p>
          <img src="./img/download.png" alt="">
        </div> -->
    </div>
  </div>
</template>
  
  <script>
import pdf from "vue-pdf";
import { decryptAes } from "../../../../utils/aesUtils";
import { decryptAesCBCHy, encryptAesCBCHy } from "../../../../utils/aesUtils";
import { reviseHyKl } from "../../../../api/bggl";
import FileSaver from "file-saver";
import CMapReaderFactory from 'vue-pdf/src/CMapReaderFactory'
import { getDwxx } from "../../../../api/dwzc";
// // 引入docx-preview插件将word文档显示到界面
// let docxPreview = require("docx-preview");
// import docxPreview from 'docx-preview'
import MD5 from "md5";
import { error } from "shelljs";
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {
    pdf,
  },
  props: {},
  data() {
    //这里存放数据
    return {
      pageLoading: false, // 界面整体loading
      wordShow: false, // word文件显隐
      pdfShow: false, // pdf文件显隐
      xmlShow: false, // html文件显隐
      gdt: false, // 下载按钮
      hylb: true, // 非会员提示性文字
      // allFileDatas: [], // 获取所有带id的文件信息
      pdfSrc: "", // pdf文件src
      htmlSrc: "", // html文件src
      currentPage: 1, // pdf文件页码
      pageCount: 0, // pdf文件总页数
      fileType: "pdf", // 文件类型
      nowfileType: "",
      tjlist: {
        kl: "",
      },
      htmlContent: "",
      xmlContent: "",
    };
  },
  //计算属性 类似于data概念
  computed: {},
  //监控data中数据变化
  watch: {},
  //方法集合
  methods: {
    handleFileChange(event) {
      const file = event.target.files[0];
      const reader = new FileReader();

      reader.onload = (e) => {
        const fileContent = e.target.result;

        // 解析 XML 数据
        xml2js.parseString(fileContent, (err, result) => {
          if (err) {
            console.error(err);
            return;
          }

          // 将解析后的数据存储在组件的 data 中
          this.xmlData = result;

          // 格式化数据以便在模板中显示
          this.formattedXmlData = JSON.stringify(result, null, 2);
        });
      };

      // 读取文件内容
      reader.readAsText(file);
    },
    // 查看文件的所有内容
    allFileShow() {
      if (this.nowfileType == ".docx") {
        // word
        this.kldialogVisible = false;
        this.gdt = true;
        this.hylb = false;
        console.log(**************************);
        // this.$refs.file.style.padding = '20px 10px 10px 10px'
      } else if (this.nowfileType == ".pdf") {
        this.kldialogVisible = false;
        this.gdt = true;
        this.hylb = false;
        this.pdfShow = true;
        this.xmlShow = false;
        console.log(222222222222222222222222222222);
      } else if (this.nowfileType == ".HTML") {
        this.kldialogVisible = false;
        this.gdt = true;
        this.hylb = false;
        this.pdfShow = false;
        this.xmlShow = true;
        console.log(333333333333333333333333333333333);
      }
      this.pageLoading = false;
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    // 将buffer转换成ArrayBuffer
    toArrayBuffer(buf) {
      var ab = new ArrayBuffer(buf.length);
      var view = new Uint8Array(ab);
      for (var i = 0; i < buf.length; ++i) {
        view[i] = buf[i];
      }
      return ab;
    },
    // 显示word文档到界面
    async handlePreview(item) {
      console.log(item);
      if (item.fileType == ".pdf") {
        // pdf文件回显
        this.pdfShow = true;
        this.xmlShow = false;
        this.wordShow = false;
        let arrayBufferDatas;
        let jmdatas = await decryptAes(item.wdFile, "hsoftBanner" + item.uuid);
        arrayBufferDatas = this.toArrayBuffer(new Buffer(JSON.parse(jmdatas)));
        const blob = new Blob([arrayBufferDatas], { type: 'application/pdf' });
        // let loadingTask = pdf.createLoadingTask({
        //   url: URL.createObjectURL(blob),
        //   cMapPacked: true,
        //   CMapReaderFactory
        // })
        // this.pdfSrc = loadingTask;
        this.pdfSrc = URL.createObjectURL(blob)  + '#scrollbars=0&toolbar=0&statusbar=0&view=FitH&messages=0&navpanes=0';
        console.log(this.pdfSrc);
        // this.pageCount = item.ys;
        this.pageLoading = false;
      } else if (item.fileType == ".HTML") {
        // pdf文件回显
        this.pdfShow = false;
        this.xmlShow = true;
        this.wordShow = false;
        let arrayBufferDatas;
        // console.log(item.wdFile);

        // let jmdatas = await decryptAes(item.wdFile, "hsoftBanner" + item.uuid);
        // console.log(jmdatas);
        // arrayBufferDatas = this.toArrayBuffer(new Buffer(JSON.parse(jmdatas)));
        // console.log(jmdatas);
        const blob = new Blob([item.htmlfile],{type:'text/html'});
        // console.log('==========================');
        // console.log(blob);
        // console.log('==========================');
        // const blobUrl = URL.createObjectURL(blob);
        // this.htmlSrc = blobUrl;
        try {
          const reader = new FileReader();
          console.log("reader", reader);

          reader.onload = () => {
            this.htmlContent = reader.result;
            console.log("this.htmlContent", this.htmlContent);
          };
          reader.readAsText(blob);
        } catch (error) {
          console.error('Error readingfile:',error)
        }
        // let jmdatas = await decryptAes(item.htmlfile, "hsoftBanner" + item.uuid);
        // arrayBufferDatas = this.toArrayBuffer(new Buffer(JSON.parse(jmdatas)));
        // const blob = new Blob([item.htmlfile]);
        // this.htmlSrc = URL.createObjectURL(blob);
        // const reader = new FileReader();

        // reader.onload = () => {
          // this.htmlContent = item.htmlfile
        // };
        // reader.readAsText(item.wdFile);

        // console.log("reader.readAsText(item.wdFile)", reader.readAsText(item.wdFile));

        // console.log("this.htmlSrc,this.htmlSrc", this.htmlSrc);
        // this.pageCount = item.ys;
        this.pageLoading = false;
      }
    },
    // 渲染docx
    // docxRender(buffer) {
    //   docxPreview.renderAsync(
    //     buffer, // Blob | ArrayBuffer | Uint8Array, 可以是 JSZip.loadAsync 支持的任何类型
    //     this.$refs.file, // HTMLElement 渲染文档内容的元素
    //   ).then(res => {
    //     this.pageLoading = false
    //   })
    // },
    // 返回
    fhsy() {
      this.$router.push({
        path: "/InspectionReportUpload",
      });
    },
    // 下载功能
    async downloadFile(row) {
      console.log(row);
      let jmdatas = await decryptAes(row.wdFile, "hsoftBanner" + row.uuid);
      let bufferFile = this.toArrayBuffer(new Buffer(JSON.parse(jmdatas)));
      let fileName = row.wdmc;
      var file = new Blob([new Blob([bufferFile])]);
      FileSaver.saveAs(file, fileName);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.nowfileType = this.$route.query.type;
    this.handlePreview(JSON.parse(this.$route.query.item));
    this.allFileShow();
    // this.testVrifyFun(this.$route.query.hyklDatas)
    if (
      this.$route.query.klIsRight == "true" &&
      this.$route.query.type == ".docx"
    ) {
      this.gdt = true;
      this.hylb = false;
      this.wordShow = true;
      this.pdfShow = false;
      this.xmlShow = false;
    } else if (
      this.$route.query.klIsRight == "true" &&
      this.$route.query.type == ".pdf"
    ) {
      this.gdt = true;
      this.hylb = false;
      this.pdfShow = true;
      this.wordShow = false;
      this.xmlShow = false;
    } else if (
      this.$route.query.klIsRight == "false" &&
      this.$route.query.type == ".pdf"
    ) {
      this.gdt = false;
      this.hylb = true;
      this.pdfShow = true;
      this.wordShow = false;
      this.xmlShow = false;
    } else if (
      this.$route.query.klIsRight == "false" &&
      this.$route.query.type == ".docx"
    ) {
      this.gdt = false;
      this.hylb = true;
      this.pdfShow = false;
      this.xmlShow = false;
      this.wordShow = true;
    } else if (
      this.$route.query.klIsRight == "true" &&
      this.$route.query.type == ".HTML"
    ) {
      this.gdt = true;
      this.hylb = false;
      this.pdfShow = false;
      this.wordShow = false;
      this.xmlShow = true;
    } else if (
      this.$route.query.klIsRight == "false" &&
      this.$route.query.type == ".HTML"
    ) {
      this.gdt = false;
      this.hylb = true;
      this.pdfShow = false;
      this.wordShow = false;
      this.xmlShow = true;
    }
  },
  created() {},
  //生命周期-创建之前
  beforeCreated() {},
  //生命周期-挂载之前
  beforeMount() {},
  //生命周期-更新之前
  beforUpdate() {},
  //生命周期-更新之后
  updated() {},
  //生命周期-销毁之前
  beforeDestory() {},
  //生命周期-销毁完成
  destoryed() {},
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() {},
};
</script>
  <style scoped>
.bgglContainer {
  height: 100%;
  position: relative;
}

.wordContainer {
  padding: 20px;
  /* background: #525659; */
}

.pdfContainer {
  /* height:100%;
    overflow-y: scroll; */
}

.bgglDiv {
  width: 1000px;
  margin: 0 auto;
  background-color: #fff;
  height: 100%;
}

.bgglDiv img {
  width: 20px;
  height: 20px;
  margin-right: 20px;
}

.bgglDiv .erweima {
  color: #e4745a;
  font-weight: 700;
  margin-right: 20px;
}

.xg .xgImg {
  display: block;
  width: 200px;
  margin: auto;
}

.dwheader {
  height: 15px;
  background: #525659;
}

.bgglhyDiv {
  width: 1000px;
  margin: 0px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 31px;
  height: 52px;
  background: linear-gradient(to bottom, transparent, #fff);
}

.backBtn {
  width: 70px;
  height: 35px;
  background: #409eff;
  color: #ffffff;
  line-height: 35px;
  border-radius: 3px;
  overflow: hidden;
}

.backBtn p {
  float: left;
  margin-left: 6px;
}

.backBtn img {
  float: left;
  width: 22px;
  margin-top: 6px;
  /* position: absolute; */
  margin-left: 5px;
}

.downloadBtn {
  width: 70px;
  height: 35px;
  background: #ffffff;
  color: #409eff;
  line-height: 35px;
  border-radius: 3px;
  float: left;
  margin-left: 10px;
}

.downloadBtn p {
  float: left;
  margin-left: 6px;
}

.downloadBtn img {
  float: left;
  width: 22px;
  margin-top: 6px;
  /* position: absolute; */
  margin-left: 5px;
}

/* @import url(); 引入css类 */
.klBtn {
  width: 165px;
  height: 40px;
  background-image: linear-gradient(
    90deg,
    rgb(40, 220, 134) 0%,
    rgb(33, 165, 102) 56%
  );
  font-size: 20px;
  color: #ffffff;
  letter-spacing: 0;
  line-height: 18px;
  font-weight: 400;
  border: none;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

.gdt {
  height: calc(100% - 38px);
  overflow-y: scroll;
}
.gtpdf {
  height: 100%;
}

::-webkit-scrollbar {
  display: block !important;
}

::-webkit-scrollbar {
  width: 15px;
  height: 15px;
}

::-webkit-scrollbar-track-piece {
  background-color: #f1f1f1;
  -webkit-border-radius: 0px;
}

::-webkit-scrollbar-thumb {
  height: 5px;
  background-color: #c1c1c1;
  -webkit-border-radius: 0px;
}

.gnbutton {
  position: absolute;
  right: 0px;
  top: 0px;
  overflow: hidden;
}

/* 修改word文档显示的样式 */
>>> .docx {
  padding: 0px !important;
}

>>> .docx-wrapper {
  background: none !important;
  padding: 0 !important;
  /* display:block!important; */
}

>>> .docx-wrapper > section.docx {
  background: white;
  width: 100% !important;
  box-shadow: none;
}

/deep/.el-dialog__body {
  padding: 30px 20px;
}
</style>