<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="台账时间" style="font-weight: 700;">
                  <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widthw">
                  </el-input> -->
                  <el-select v-model="formInline.tzsj" placeholder="台账时间">
                    <el-option v-for="item in yearSelect" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="涉密人员" style="font-weight: 700;">
                  <el-input v-model="formInline.xm" clearable placeholder="涉密人员" class="widthw">
                  </el-input>
                </el-form-item>
                <el-form-item label="脱密期限" style="font-weight: 700;">
                  <el-date-picker v-model="formInline.tmjssj" type="daterange" range-separator="至" style="width:294px;"
                    start-placeholder="查询起始时间" end-placeholder="查询结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" @click="fh()">返回
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="smryList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="xm" label="姓名"></el-table-column>
                  <el-table-column prop="bmmc" label="原部门"></el-table-column>
                  <el-table-column prop="smdj" label="原涉密等级" :formatter="forysmdj"></el-table-column>
                  <el-table-column prop="tmqssj" label="脱密期开始时间"></el-table-column>
                  <el-table-column prop="tmjssj" label="脱密期结束时间"></el-table-column>
                  <el-table-column prop="tznf" label="台账时间"></el-table-column>
                  <el-table-column prop="" label="操作" width="140">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>
                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->
        <el-dialog title="涉密人员离岗离职信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%"
          class="xg">
          <el-form ref="form" :model="xglist" label-width="194px" size="mini" disabled>
            <div style="display:flex">
              <el-form-item label="姓名" prop="xm">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.xm" disabled
                  style="width: 100%;" size="medium"
                  placeholder="请输入姓名">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="原部门" prop="bmmc">
                <el-input v-model="xglist.bmmc" clearable placeholder="原部门" disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="原涉密等级" prop="smdj">
                <el-select v-model="xglist.smdj" placeholder="请选择涉密等级" disabled style="width: 100%;">
                  <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="级别职称">
                <el-select v-model="xglist.jbzc" placeholder="请选择级别职称" disabled style="width: 100%;">
                  <el-option v-for="item in jbzcxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="脱密期开始时间" prop="tmqssj">
                <el-date-picker v-model="xglist.tmqssj" class="cd" clearable type="date" style="width:100%;"
                  placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="脱密期结束时间" prop="tmjssj">
                <el-date-picker v-model="xglist.tmjssj" class="cd" clearable type="date" style="width:100%;"
                  placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="手机号码" prop="lxdh">
                <el-input v-model="xglist.lxdh" clearable placeholder="手机号码" maxlength="11"></el-input>
              </el-form-item>
              <el-form-item label="去向单位名称" prop="qxdw">
                <el-input v-model="xglist.qxdw" clearable placeholder="去向单位名称"></el-input>
              </el-form-item>
            </div>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getAllYhxx,
  saveLzlg,
  updateLzlg,
  removeLzlg
} from '../../../api/index'
import {
  getLzlgLsPage,
} from '../../../api/djgwbg'
import {
  getAllSmdj,
  getAllJbzc,
  getAllSflx,
  getLzlglx
} from '../../../api/xlxz'

import {
  exportLsLglzData
} from '../../../api/dcwj'
import {
  dateFormatNYRChinese
} from "../../../utils/moment.js"
export default {
  components: {},
  props: {},
  data() {
    var isMobileNumber = (rule, value, callback) => {
      if (!value) {
        return new Error('请输入电话号码')
      } else {
        const reg =
          /^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/
        const isPhone = reg.test(value)
        value = Number(value) //转换为数字
        if (typeof value === 'number' && !isNaN(value)) {
          //判断是否为数字
          value = value.toString() //转换成字符串
          if (value.length < 0 || value.length > 12 || !isPhone) {
            //判断是否为11位手机号
            callback(new Error('手机号格式:138xxxx8754'))
          } else {
            callback()
          }
        } else {
          callback(new Error('请输入电话号码'))
        }
      }
    }
    return {
      yearSelect: [],
      // 历史轨迹dialog显隐
      lsgjDialogVisible: false,
      // 历史轨迹dialog数据
      lsgjDialogData: {
        bmbh: '',
        zcbh: '',
        // 历史轨迹时间线数据
        timelineList: [],
      },
      xb: [{
        xb: '男',
        id: 1
      },
      {
        xb: '女',
        id: 2
      },
      ],
      // gwmc: [],
      gwmc: [{
        gwmc: '前端岗',
        id: 1
      },
      {
        gwmc: 'java',
        id: 2
      },
      {
        gwmc: '测试',
        id: 3
      },
      ],
      smdjxz: [],
      jbzcxz: [],
      sflxxz: [],
      sfba: [{
        mc: '是',
        id: 1
      },
      {
        mc: '否',
        id: 0
      },
      ],
      sfwt: [{
        mc: '是',
        id: 1
      },
      {
        mc: '否',
        id: 0
      },
      ],
      lzlglxxz: [
      ],
      smryList: [],
      xglist: {},
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      formInline: {
        tzsj: new Date().getFullYear().toString()
      },
      smryid: '',
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
    
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    }
  },
  computed: {},
  mounted() {
    //获取最近十年的年份
    let yearArr = []
    for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
      yearArr.push(
        {
          label: i.toString(),
          value: i.toString()
        })
    }
    yearArr.unshift({
      label: "全部",
      value: ""
    })
    this.yearSelect = yearArr
    this.smdj()
    this.jbzc()
    this.sflx()
    this.lglz()
    this.smry()
    this.lzlglx()
  },
  methods: {
    //离职离岗类型
    async lzlglx() {
      let data = await getLzlglx()
      this.lzlglxxz = data
    },
    //获取涉密等级信息
    async smdj() {
      let data = await getAllSmdj()
      this.smdjxz = data
    },
    //获取级别职称
    async jbzc() {
      let data = await getAllJbzc()
      console.log(data);
      this.jbzcxz = data
    },
    //获取身份类型
    async sflx() {
      let data = await getAllSflx()
      console.log(data);
      this.sflxxz = data
    },
    //导出
    async exportList() {
      let param = {
        xm: this.formInline.xm,
        nf: this.formInline.tzsj
      }
      if (this.formInline.bgrq != null) {
        param.kssj = this.formInline.bgrq[0]
        param.jssj = this.formInline.bgrq[1]
      }
      let returnData = await exportLsLglzData(param);
      let date = new Date()
      let sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "离岗离职信息表-" + sj + ".xls");
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) // 创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) // 指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
   
    xqyl(row) {
      console.log(row);
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      this.xqdialogVisible = true
      // this.form1.ywlx = row.ywlx
    },

    //查询
    onSubmit() {
      this.page = 1
      this.smry()
    },
    cz() {
      this.formInline = {}
    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    async smry() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        xm: this.formInline.xm,
        // tznf: this.formInline.tzsj
      }
      if(this.formInline.tzsj){
        params.tznf = this.formInline.tzsj
      }
      if (this.formInline.tmjssj != null) {
        params.kssj = this.formInline.tmjssj[0]
        params.jssj = this.formInline.tmjssj[1]
      }
      let resList = await getLzlgLsPage(params)
      this.smryList = resList.records
      this.total = resList.total
    },
   
    //添加
    showDialog() {
      this.dialogVisible = true
    },
    selectRow(val) {
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.smry()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.smry()
    },
    fh() {
      this.$router.go(-1)
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      // console.log("cb(results.dwmc)", results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async lglz() {
      let resList = await getAllYhxx()
      console.log(resList);
      this.restaurants = resList;
      console.log("this.restaurants", this.restaurants);
      console.log(resList)
    },
    forysmdj(row) {
      let hxsj
      this.smdjxz.forEach(item => {
        if (row.smdj == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
  },
  watch: {

  }
}

</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  /* padding: 20px 20px; */
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widthw {
  width: 6vw;
}

.cd {
  width: 164px;
}

/deep/.el-dialog {
  margin-top: 6vh !important;
}

/deep/.inline-input .el-input--medium .el-input__inner {
  /* width: 164px; */
  height: 28px;
  font-size: 12px;
}

/deep/.el-select .el-select__tags>span {
  display: flex !important;
  flex-wrap: wrap;
}

.bz {
  height: 72px !important;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

/deep/.el-dialog__body .el-form>div>div {
  /* width: auto; */
  max-width: 100%;
}


/deep/.el-dialog__body .el-form>div .el-form-item__label {
  width: 194px !important;
}
</style>
