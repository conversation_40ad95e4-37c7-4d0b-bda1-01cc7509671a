webpackJsonp([155],{BN1X:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=i("mvHQ"),r=i.n(s),n=i("woOf"),o=i.n(n),c=i("Xxa5"),d=i.n(c),a=i("exGp"),l=i.n(a),u=i("9WY5"),h=i("kCU4"),f=(i("1clA"),i("ye4p"),{data:function(){return{dialogObj:{zw:"",xm:"",bm:""},showDxList:[],spanArr:[],reverse:!0,activities:[],showMdmenu:!1,dwxx:{},ryid:"",rwid:"",djzt:"",rwmc:"",dlzt:"",jcjd:""}},computed:{},components:{},mounted:function(){this.rwid=this.$route.query.rwid,this.djzt=this.$route.query.djzt,this.ryid=this.$route.query.ryid,this.dlzt=this.$route.query.dlzt,console.log("this.rwid",this.rwid),this.jcjd=this.$route.query.jcjd,this.dialogObj.zw=this.$route.query.zw,this.dialogObj.xm=this.$route.query.xm,this.dialogObj.bm=this.$route.query.bm,this.rwmc=this.$route.query.rwmc,this.getDwxxList(),this.getRyzcxxjl()},methods:{submit:function(){1==this.dlzt?this.$router.push({path:"/ccdry",query:{rwid:this.rwid,rwmc:this.rwmc,jcjd:this.jcjd}}):2==this.dlzt&&this.$router.push({path:"/jczj",query:{rwid:this.rwid,rwmc:this.rwmc,jcjd:this.jcjd}})},getDwxxList:function(){var t=this;return l()(d.a.mark(function e(){var i;return d.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(h.g)();case 2:i=e.sent,console.log(i),t.dwxx=i;case 5:case"end":return e.stop()}},e,t)}))()},getRyzcxxjl:function(){var t=this;return l()(d.a.mark(function e(){var i,s,r;return d.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(u.p)();case 2:return i=e.sent,s=[],r={rwid:t.rwid,ryid:t.ryid},e.next=7,Object(u.t)(r);case 7:(s=e.sent).forEach(function(t){i.forEach(function(e){e.zcxid==t.zcxid&&(t.dxmc=e.zcxmc)})}),t.showDxList=s,t.spanArr=t.getSpanArr(s),console.log(t.showDxList),console.log(i),console.log(s);case 14:case"end":return e.stop()}},e,t)}))()},returnSy:function(){this.$router.go(-1)},getCcdryxxByCcdryid:function(){var t=selectCcdryxxByCcdryid(this.dialogObj);console.log("ccdryxx",t),o()(this.dialogObj,t),this.dialogObj=JSON.parse(r()(this.dialogObj))},getSpanArr:function(t){for(var e=[],i=0;i<t.length;i++)0===i?(e.push(1),this.pos=0):t[i].zcxid==t[i-1].zcxid?(e[this.pos]+=1,e.push(0)):(e.push(1),this.pos=i);return e},objectSpanMethod:function(t){var e=t.row;t.column,t.rowIndex;if(0===t.columnIndex)return{rowspan:this.spanArr[e.scid-1],colspan:1}},getZD:function(){console.log("getZD");var t=getRyzcxxjlZD();return console.log(t),t.forEach(function(t){void 0===t.sffh&&(t.sffh=!0)}),this.spanArr=this.getSpanArr(t),t},mouseoverMdMenu:function(){this.showMdmenu=!0},mouseoutMenu:function(){this.showMdmenu=!1}},watch:{showDxList:{handler:function(t,e){var i=this;this.activities=[];var s=[];t.forEach(function(t,e){t.mdIndex="md"+e,t.sffh||(t.mdIndex="md"+e,-1==s.indexOf(t.zcxid)&&s.push(t.zcxid),-1!=s.indexOf(t.zcxid)&&(i.activities[s.indexOf(t.zcxid)]||i.activities.push({dxmc:t.dxmc,children:[]}),i.activities[s.indexOf(t.zcxid)].children.push({href:"#"+t.mdIndex,nr:t.scnr,ykf:t.ykf})))})},deep:!0}}}),x={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{width:"100%",height:"100%"}},[i("div",{staticClass:"mhcx"},[i("el-form",{staticClass:"demo-form-inline",staticStyle:{float:"left"},attrs:{inline:!0,size:"medium"}},[i("el-form-item",{staticStyle:{float:"left"}},[i("div",[t._v("姓名："+t._s(t.dialogObj.xm))])]),t._v(" "),i("el-form-item",{staticStyle:{float:"left"}},[i("div",[t._v("部门："+t._s(t.dialogObj.bm))])]),t._v(" "),i("el-form-item",{staticStyle:{float:"left"}},[i("div",[t._v("职务："+t._s(t.dialogObj.zw))])])],1),t._v(" "),i("el-form",{staticClass:"demo-form-inline",staticStyle:{float:"right"},attrs:{inline:!0,size:"medium"}},[i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary",size:"medium"},on:{click:t.submit}},[t._v("\n          返回\n        ")])],1)],1),t._v(" "),i("div",{staticStyle:{clear:"both"}})],1),t._v(" "),i("el-table",{attrs:{data:t.showDxList,"span-method":t.objectSpanMethod,border:"",height:"calc(100% - 58px - 5px)"}},[i("el-table-column",{attrs:{label:"自查类",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("div",[i("span",{attrs:{id:t.showDxList[e.$index].mdIndex}}),t._v(t._s(t.showDxList[e.$index].dxmc)+"\n          ")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"自查内容"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("div",[i("span",{attrs:{id:t.showDxList[e.$index].mdIndex}}),t._v(t._s(t.showDxList[e.$index].scnr)+"\n        ")])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"是否符合要求",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio-group",{attrs:{disabled:""},model:{value:t.showDxList[e.$index].sffh,callback:function(i){t.$set(t.showDxList[e.$index],"sffh",i)},expression:"showDxList[scope.$index].sffh"}},[i("el-radio",{attrs:{label:!0}},[t._v("是")]),t._v(" "),i("el-radio",{attrs:{label:!1}},[t._v("否")])],1)]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"备注说明"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-input",{attrs:{type:"textarea",rows:3,disabled:""},model:{value:t.showDxList[e.$index].bzsm,callback:function(i){t.$set(t.showDxList[e.$index],"bzsm","string"==typeof i?i.trim():i)},expression:"showDxList[scope.$index].bzsm"}})]}}])})],1)],1)},staticRenderFns:[]};var m=i("VU/8")(f,x,!1,function(t){i("qvWi")},"data-v-556c0e8d",null);e.default=m.exports},qvWi:function(t,e){}});
//# sourceMappingURL=155.67cf22e28f9c28f6d7f7.js.map