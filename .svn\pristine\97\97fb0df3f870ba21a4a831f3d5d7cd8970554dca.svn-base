{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/ztzzsc.vue", "webpack:///./src/renderer/view/rcgz/ztzzsc.vue?154f", "webpack:///./src/renderer/view/rcgz/ztzzsc.vue"], "names": ["rcgz_ztzzsc", "components", "BaseHeader", "baseHeader", "BaseTable", "baseTable", "props", "data", "_this", "this", "rules", "fyys", "required", "message", "trigger", "xhr", "xhfs", "jxr", "xhrq", "tjlist", "xgdialogVisible", "loading", "headerCellStyle", "background", "color", "formInline", "dialogVisible", "ryDatas", "page", "pageSize", "page1", "pageSize1", "ry<PERSON><PERSON>ose", "bm", "xm", "total", "total1", "radioIdSelect", "smryList", "scjtlist", "mc", "id", "dqztlist", "rowdata", "regionOption", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "params", "name", "tmjssj", "columns", "type", "placeholder", "startPlaceholder", "rangeSeparator", "endPlaceholder", "format", "disabled", "icon", "mold", "tableColumns", "prop", "scopeType", "formatter", "showOverflowTooltip", "row", "column", "cellValue", "index", "opt", "find", "d", "handleColumn", "show", "Lcfwslzt", "cjrid", "loginName", "handleColumnProp", "width", "align", "applyColumns", "join", "handleColumnApply", "smryColumns", "computed", "mounted", "onfwid", "getLogin<PERSON>hm", "rysclist", "zzjg", "methods", "close", "form", "$refs", "resetFields", "xszzjgxz", "_this2", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "validate", "valid", "console", "log", "param", "j<PERSON>", "Object", "api", "stop", "_this3", "_callee2", "userInfo", "_context2", "dwzc", "sent", "yhm", "handleSizeChange", "val", "handleCurrentChange", "selectBtn", "shanchu", "_this4", "length", "$message", "$confirm", "confirmButtonText", "cancelButtonText", "then", "_ref", "for<PERSON>ach", "_callee3", "item", "_context3", "slid", "ztzzsc", "code", "_x", "apply", "arguments", "catch", "handleBtnAll", "parameter", "JSON", "parse", "stringify_default", "_this5", "_callee4", "_context4", "xqr", "kssj", "jssj", "records", "error", "submit", "$router", "push", "searchRy", "table<PERSON><PERSON>", "sendApplay", "_this6", "_callee5", "_context5", "path", "query", "handleCurrentChangeRy", "handleSizeChangeRy", "handleSelectionChange", "submitRy", "_this7", "_callee6", "zp", "_context6", "keys_default", "sm<PERSON><PERSON>", "datas", "scjgsj", "dqztsj", "_this8", "_callee7", "_context7", "fwlx", "fwdyid", "operateBtn", "_this9", "_callee8", "res", "zt", "_context8", "yj<PERSON>", "ztzz", "undefined", "lx", "_this10", "_callee9", "zzjgList", "shu", "shuList", "list", "_context9", "zzjgmc", "childrenRegionVo", "item1", "bmm", "fbmm", "bmSelectChange", "watch", "view_rcgz_ztzzsc", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "attrs", "on", "handleBtn", "_v", "inline", "model", "size", "click", "showSelection", "<PERSON><PERSON><PERSON><PERSON>", "showIndex", "tableData", "showPagination", "currentPage", "totalCount", "title", "visible", "update:visible", "$event", "ref", "label-width", "staticStyle", "clearable", "callback", "$$v", "$set", "value-format", "slot", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "4PAiGAA,GACAC,YACAC,WAAAC,EAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAMA,IAAAC,EAAAC,KACA,OACAC,OACAC,OACAC,UAAA,EACAC,QAAA,UACAC,QAAA,SAEAC,MACAH,UAAA,EACAC,QAAA,SACAC,QAAA,SAEAE,OACAJ,UAAA,EACAC,QAAA,UACAC,QAAA,SAEAG,MACAL,UAAA,EACAC,QAAA,SACAC,QAAA,SAEAI,OACAN,UAAA,EACAC,QAAA,UACAC,SAAA,oBAGAK,UAGAC,iBAAA,EACAC,SAAA,EAEAC,iBACAC,WAAA,UACAC,MAAA,WAEAC,cACAC,eAAA,EACAC,WACAC,KAAA,EACAC,SAAA,EACAC,MAAA,EACAC,UAAA,GAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,MAAA,EACAC,OAAA,EACAC,cAAA,GACAC,YACAC,WAEAC,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAC,WAEAF,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAE,WACAC,gBACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,QACAC,KAAA,GACAC,OAAA,IAGAC,UACAC,KAAA,cACAH,KAAA,MACAL,MAAA,MACAS,YAAA,QAGAD,KAAA,YACAH,KAAA,OACAL,MAAA,SACAU,iBAAA,SACAC,eAAA,IACAC,eAAA,SACAC,OAAA,eAGAL,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,iBACAC,KAAA,YAGAR,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,uBACAC,KAAA,YAIAC,eAEAZ,KAAA,MACAa,KAAA,MACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,QACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,EACAC,qBAAA,IAGAhB,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,EACAC,qBAAA,IAGAhB,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,OACAa,KAAA,WACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,IAkBAC,IAhBAjC,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAiC,KAAA,SAAAC,GAAA,OAAAA,EAAAlC,KAAA8B,IACA,OAAAE,IAAAjC,GAAA,MAIAY,KAAA,OACAa,KAAA,WACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,IAkBAC,IAhBAjC,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAiC,KAAA,SAAAC,GAAA,OAAAA,EAAAlC,KAAA8B,IACA,OAAAE,IAAAjC,GAAA,MAKAoC,eAEAxB,KAAA,KACAS,UAAA,EACAgB,MAAA,EACAV,UAAA,SAAAE,EAAAC,GACA,UAAAD,EAAAS,UAAAT,EAAAU,OAAAvE,EAAAwE,UACA,KACA,GAAAX,EAAAS,UAAA,GAAAT,EAAAS,UAAA,GAAAT,EAAAS,SACA,UADA,KA2BAG,kBACAnC,MAAA,KACAoC,MAAA,MACAC,MAAA,QAGAC,eAEAhC,KAAA,KACAa,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,KACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,KACAa,KAAA,OACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,OAAAD,EAAAc,KAAA,QAIAC,qBAEAC,cACAhC,KAAA,WACAH,KAAA,KACAL,MAAA,OACAS,YAAA,UAEAD,KAAA,cACAH,KAAA,KACAL,MAAA,OACAS,YAAA,OAGAD,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,iBACAC,KAAA,YAGAR,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,uBACAC,KAAA,YAIAiB,UAAA,KAGAQ,YACAC,QAxUA,WAyUAhF,KAAAiF,SACAjF,KAAAkF,cACAlF,KAAAmF,WACAnF,KAAAoF,QAEAC,SAEAC,MAFA,SAEAC,GAEAvF,KAAAwF,MAAAD,GAAAE,eAEAC,SANA,SAMAH,GAAA,IAAAI,EAAA3F,KAAA,OAAA4F,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,OAAAH,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OACAT,EAAAH,MAAAD,GAAAc,SAAA,SAAAC,GACA,IAAAA,EAcA,OADAC,QAAAC,IAAA,mBACA,EAbA,IAAAC,GACAC,KAAAf,EAAAe,KACAxG,KAAAyF,EAAAjF,OAAAR,KACAI,IAAAqF,EAAAjF,OAAAJ,IACAE,IAAAmF,EAAAjF,OAAAF,IACAC,KAAAkF,EAAAjF,OAAAD,KACAF,KAAAoF,EAAAjF,OAAAH,MAEAoG,OAAAC,EAAA,KAAAD,CAAAF,GACAd,EAAAhF,iBAAA,EACAgF,EAAAR,aAbA,wBAAAe,EAAAW,SAAAb,EAAAL,KAAAC,IAsBAV,YA5BA,WA4BA,IAAA4B,EAAA9G,KAAA,OAAA4F,IAAAC,EAAAC,EAAAC,KAAA,SAAAgB,IAAA,IAAAC,EAAA,OAAAnB,EAAAC,EAAAG,KAAA,SAAAgB,GAAA,cAAAA,EAAAd,KAAAc,EAAAb,MAAA,cAAAa,EAAAb,KAAA,EACAO,OAAAO,EAAA,EAAAP,GADA,OACAK,EADAC,EAAAE,KAEAL,EAAAvC,UAAAyC,EAAAI,IAFA,wBAAAH,EAAAJ,SAAAE,EAAAD,KAAAlB,IAKAyB,iBAjCA,SAiCAC,GACAtH,KAAAqB,MAAA,EACArB,KAAAsB,UAAAgG,EACAtH,KAAAmF,YAEAoC,oBAtCA,SAsCAD,GACAtH,KAAAqB,MAAAiG,EACAtH,KAAAmF,YAGAqC,UA3CA,SA2CA5D,GACA5D,KAAAkC,QAAA0B,EACA2C,QAAAC,IAAA5C,IAGA6D,QAhDA,WAgDA,IAAAC,EAAA1H,KACA,GAAAA,KAAAkC,QAAAyF,OACA3H,KAAA4H,UACAxH,QAAA,aACA0C,KAAA,YAGA9C,KAAA6H,SAAA,2BACAC,kBAAA,KACAC,iBAAA,KACAjF,KAAA,YACAkF,KAAA,WACA,IAAAC,EAAAP,EAAAxF,QAAAgG,SAAAD,EAAArC,IAAAC,EAAAC,EAAAC,KAAA,SAAAoC,EAAAC,GAAA,IAAA1F,EAAA,OAAAmD,EAAAC,EAAAG,KAAA,SAAAoC,GAAA,cAAAA,EAAAlC,KAAAkC,EAAAjC,MAAA,cACA1D,GACA4F,KAAAF,EAAAE,MAFAD,EAAAjC,KAAA,EAIAO,OAAA4B,EAAA,EAAA5B,CAAAjE,GAJA,OAKA,KALA2F,EAAAlB,KAKAqB,OACAd,EAAAE,UACAxH,QAAA,OACA0C,KAAA,YAEA4E,EAAAvC,YAVA,wBAAAkD,EAAAxB,SAAAsB,EAAAT,MAAA,SAAAe,GAAA,OAAAR,EAAAS,MAAA1I,KAAA2I,gBAaAC,MAAA,WACAlB,EAAAE,UACA9E,KAAA,OACA1C,QAAA,aAMAyI,aAlFA,SAkFAC,EAAAV,GACA,MAAAA,EAAAzF,MACA3C,KAAA0C,OAAAqG,KAAAC,MAAAC,IAAAH,IACA9I,KAAAqB,MAAA,EACArB,KAAAmF,YACA,MAAAiD,EAAAzF,OACA3C,KAAA0C,QACAC,KAAA,GACAC,OAAA,MAKAuC,SA/FA,SA+FA2D,GAAA,IAAAI,EAAAlJ,KAAA,OAAA4F,IAAAC,EAAAC,EAAAC,KAAA,SAAAoD,IAAA,IAAAzG,EAAA5C,EAAA,OAAA+F,EAAAC,EAAAG,KAAA,SAAAmD,GAAA,cAAAA,EAAAjD,KAAAiD,EAAAhD,MAAA,cACA1D,GACA2G,IAAAH,EAAAxG,OAAA2G,IACAlI,KAAA+H,EAAA7H,MACAD,SAAA8H,EAAA5H,WAEA,MAAA4H,EAAAxG,OAAAE,SACAF,EAAA4G,KAAAJ,EAAAxG,OAAAE,OAAA,GACAF,EAAA6G,KAAAL,EAAAxG,OAAAE,OAAA,IARAwG,EAAAhD,KAAA,EAUAO,OAAA4B,EAAA,EAAA5B,CAAAjE,GAVA,QAUA5C,EAVAsJ,EAAAjC,MAWArH,KAAA0J,SACAN,EAAArH,SAAA/B,OAAA0J,QACAN,EAAAvH,OAAA7B,OAAA4B,OAEAwH,EAAAtB,SAAA6B,MAAA,WAfA,wBAAAL,EAAAvC,SAAAsC,EAAAD,KAAAtD,IAmBA8D,OAlHA,WAmHA1J,KAAA2J,QAAAC,KAAA,eAGAC,SAtHA,WAuHA7J,KAAA8J,WACA9J,KAAAmB,KAAA,EACAnB,KAAA+J,cAGAA,WA5HA,WA4HA,IAAAC,EAAAhK,KAAA,OAAA4F,IAAAC,EAAAC,EAAAC,KAAA,SAAAkE,IAAA,OAAApE,EAAAC,EAAAG,KAAA,SAAAiE,GAAA,cAAAA,EAAA/D,KAAA+D,EAAA9D,MAAA,OACA4D,EAAAL,QAAAC,MACAO,KAAA,eACAC,OACAtH,KAAA,SAJA,wBAAAoH,EAAArD,SAAAoD,EAAAD,KAAApE,IAQAyE,sBApIA,SAoIA/C,GACAtH,KAAAmB,KAAAmG,EACAtH,KAAA+J,cAGAO,mBAzIA,SAyIAhD,GACAtH,KAAAmB,KAAA,EACAnB,KAAAoB,SAAAkG,EACAtH,KAAA+J,cAEAQ,sBA9IA,SA8IAxG,EAAAH,GACA5D,KAAA4B,cAAAgC,GAGA4G,SAlJA,WAkJA,IAAAC,EAAAzK,KAAA,OAAA4F,IAAAC,EAAAC,EAAAC,KAAA,SAAA2E,IAAA,IAAAC,EAAA,OAAA9E,EAAAC,EAAAG,KAAA,SAAA2E,GAAA,cAAAA,EAAAzE,KAAAyE,EAAAxE,MAAA,UACAqE,EAAA7J,SAAA,IACA,IAAA6J,EAAA7I,eAAAiJ,IAAAJ,EAAA7I,eAAA+F,OAAA,GAFA,CAAAiD,EAAAxE,KAAA,gBAGAqE,EAAA7J,SAAA,EAHAgK,EAAAxE,KAAA,EAIAO,OAAAC,EAAA,IAAAD,EAAAmE,OAAAL,EAAA7I,cAAAkJ,SAJA,OAIAH,EAJAC,EAAAzD,KAKAsD,EAAA7I,cAAA+I,KACAF,EAAAd,QAAAC,MACAO,KAAA,aACAC,OACAtH,KAAA,MACAiI,MAAAN,EAAA7I,iBAVAgJ,EAAAxE,KAAA,iBAcAqE,EAAA7C,SAAA6B,MAAA,WACAgB,EAAA7J,SAAA,EAfA,yBAAAgK,EAAA/D,SAAA6D,EAAAD,KAAA7E,IAmBAoF,OArKA,SAqKApH,GACA,IAAA9D,OAAA,EAMA,OALAE,KAAA8B,SAAAoG,QAAA,SAAAE,GACAA,EAAApG,IAAA4B,EAAAS,WACAvE,EAAAsI,EAAArG,MAGAjC,GAGAmL,OA/KA,SA+KArH,GACA,IAAA9D,OAAA,EAMA,OALAE,KAAAiC,SAAAiG,QAAA,SAAAE,GACAA,EAAApG,IAAA4B,EAAAS,WACAvE,EAAAsI,EAAArG,MAGAjC,GAEAmF,OAxLA,WAwLA,IAAAiG,EAAAlL,KAAA,OAAA4F,IAAAC,EAAAC,EAAAC,KAAA,SAAAoF,IAAA,IAAAzI,EAAA5C,EAAA,OAAA+F,EAAAC,EAAAG,KAAA,SAAAmF,GAAA,cAAAA,EAAAjF,KAAAiF,EAAAhF,MAAA,cACA1D,GACA2I,KAAA,IAFAD,EAAAhF,KAAA,EAIAO,OAAAC,EAAA,EAAAD,CAAAjE,GAJA,OAIA5C,EAJAsL,EAAAjE,KAKAZ,QAAAC,IAAA1G,GACAoL,EAAAI,OAAAxL,OAAAwL,OANA,wBAAAF,EAAAvE,SAAAsE,EAAAD,KAAAtF,IASA2F,WAjMA,SAiMA3H,EAAAwE,GAAA,IAAAoD,EAAAxL,KAAA,OAAA4F,IAAAC,EAAAC,EAAAC,KAAA,SAAA0F,IAAA,IAAAC,EAAAC,EAAAL,EAAA,OAAAzF,EAAAC,EAAAG,KAAA,SAAA2F,GAAA,cAAAA,EAAAzF,KAAAyF,EAAAxF,MAAA,UAEA,MAAAgC,EAFA,CAAAwD,EAAAxF,KAAA,gBAGAoF,EAAA5K,SAAA,EAHAgL,EAAAxF,KAAA,EAIAO,OAAA4B,EAAA,EAAA5B,EACAD,KAAA9C,EAAA8C,OALA,cAIAgF,EAJAE,EAAAzE,KAAAyE,EAAAxF,KAAA,EAOAO,OAAAC,EAAA,IAAAD,EACAkF,MAAAjI,EAAA8C,OARA,OAOAiF,EAPAC,EAAAzE,KAWAuE,EAAApD,MACAkD,EAAA5K,SAAA,EACA4K,EAAA7B,QAAAC,MACAO,KAAA,eACAC,OACAtH,KAAA,SACAiI,MAAAW,EACAI,KAAAH,MAKAH,EAAA5D,SAAA6B,MAAA,UAvBAmC,EAAAxF,KAAA,iBAyBA,MAAAgC,GACAkD,EAAAE,EAAAF,OACA,IAAAE,EAAAF,aAAAS,GAAAP,EAAAF,OACAE,EAAA5D,SAAA6B,MAAA,cAEA+B,EAAA7B,QAAAC,MACAO,KAAA,iBACAC,OACA4B,GAAA,OACAV,SACAhD,KAAA1E,EAAA0E,SAKA,QAAAF,GACAoD,EAAA9E,KAAA9C,EAAA8C,KACA8E,EAAA7K,iBAAA,GACA,QAAAyH,IACAoD,EAAA9E,KAAA9C,EAAA8C,KACA8E,EAAA9K,OAAAJ,IAAAsD,EAAAtD,IACAkL,EAAA9K,OAAAR,KAAA0D,EAAA1D,KACAsL,EAAA9K,OAAAF,IAAAoD,EAAApD,IACAgL,EAAA9K,OAAAH,KAAAqD,EAAArD,KACAiL,EAAA9K,OAAAD,KAAAmD,EAAAnD,KACA+K,EAAA7K,iBAAA,GAlDA,yBAAAiL,EAAA/E,SAAA4E,EAAAD,KAAA5F,IAsDAR,KAvPA,WAuPA,IAAA6G,EAAAjM,KAAA,OAAA4F,IAAAC,EAAAC,EAAAC,KAAA,SAAAmG,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAzG,EAAAC,EAAAG,KAAA,SAAAsG,GAAA,cAAAA,EAAApG,KAAAoG,EAAAnG,MAAA,cAAAmG,EAAAnG,KAAA,EACAO,OAAAC,EAAA,IAAAD,GADA,cACAwF,EADAI,EAAApF,KAEA8E,EAAAO,OAAAL,EACAC,KACAH,EAAAO,OAAAtE,QAAA,SAAAE,GACA,IAAAqE,KACAR,EAAAO,OAAAtE,QAAA,SAAAwE,GACAtE,EAAAuE,KAAAD,EAAAE,OACAH,EAAA7C,KAAA8C,GACAtE,EAAAqE,sBAGAL,EAAAxC,KAAAxB,KAEAiE,KAdAE,EAAAnG,KAAA,EAeAO,OAAAC,EAAA,EAAAD,GAfA,OAgBA,KADA2F,EAfAC,EAAApF,MAgBAyF,MACAR,EAAAlE,QAAA,SAAAE,GACA,IAAAA,EAAAwE,MACAP,EAAAzC,KAAAxB,KAIA,IAAAkE,EAAAM,MACAR,EAAAlE,QAAA,SAAAE,GACA7B,QAAAC,IAAA4B,GACAA,EAAAwE,MAAAN,EAAAM,MACAP,EAAAzC,KAAAxB,KAIAiE,EAAA,GAAAI,iBAAAvE,QAAA,SAAAE,GACA6D,EAAA9J,aAAAyH,KAAAxB,KAhCA,yBAAAmE,EAAA1F,SAAAqF,EAAAD,KAAArG,IAoCAiH,eA3RA,SA2RAzE,GACA7B,QAAAC,IAAA4B,QACA2D,GAAA3D,IACApI,KAAAuB,SAAAC,GAAA4G,EAAAxD,KAAA,QAIAkI,UC9sBeC,GADEC,OAFjB,WAA0B,IAAAC,EAAAjN,KAAakN,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAa3K,KAAA,UAAA4K,QAAA,YAAAjL,MAAA2K,EAAA,QAAAO,WAAA,YAA4EC,YAAA,WAAuBL,EAAA,OAAYK,YAAA,cAAwBL,EAAA,cAAmBM,OAAO7K,QAAAoK,EAAApK,QAAAH,OAAAuK,EAAAvK,QAA0CiL,IAAKC,UAAAX,EAAApE,gBAA8BoE,EAAAY,GAAA,KAAAT,EAAA,WAA4BK,YAAA,KAAAC,OAAwBI,QAAA,EAAAC,MAAAd,EAAAjM,WAAAgN,KAAA,YAAsDZ,EAAA,gBAAqBK,YAAA,OAAiBL,EAAA,aAAkBM,OAAO5K,KAAA,SAAAkL,KAAA,SAAA3K,KAAA,wBAA8DsK,IAAKM,MAAAhB,EAAAxF,WAAqBwF,EAAAY,GAAA,kCAAAZ,EAAAY,GAAA,KAAAT,EAAA,gBAA0EK,YAAA,OAAiBL,EAAA,aAAkBM,OAAO5K,KAAA,UAAAkL,KAAA,SAAA3K,KAAA,gBAAuDsK,IAAKM,MAAAhB,EAAAlD,cAAwBkD,EAAAY,GAAA,wCAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6EM,OAAOQ,eAAA,EAAAC,eAAA,KAAAC,WAAA,EAAAC,UAAApB,EAAApL,SAAAgB,QAAAoK,EAAA1J,aAAAY,aAAA8I,EAAA9I,aAAAK,iBAAAyI,EAAAzI,iBAAA8J,gBAAA,EAAAC,YAAAtB,EAAA5L,MAAAD,SAAA6L,EAAA3L,UAAAkN,WAAAvB,EAAAtL,QAAuRgM,IAAKpC,WAAA0B,EAAA1B,WAAA/D,UAAAyF,EAAAzF,UAAAD,oBAAA0F,EAAA1F,oBAAAF,iBAAA4F,EAAA5F,oBAA6I4F,EAAAY,GAAA,KAAAT,EAAA,aAA8BM,OAAOe,MAAA,OAAAC,QAAAzB,EAAAtM,gBAAA8D,MAAA,OAA2DkJ,IAAKgB,iBAAA,SAAAC,GAAkC3B,EAAAtM,gBAAAiO,GAA2BtJ,MAAA,SAAAsJ,GAA0B,OAAA3B,EAAA3H,MAAA,YAA2B8H,EAAA,WAAgByB,IAAA,OAAAnB,OAAkBK,MAAAd,EAAAvM,OAAAoO,cAAA,QAAAd,KAAA,OAAA/N,MAAAgN,EAAAhN,SAA0EmN,EAAA,gBAAqBK,YAAA,WAAAC,OAA8BrL,MAAA,OAAAmB,KAAA,UAA8B4J,EAAA,YAAiB2B,aAAatK,MAAA,QAAeiJ,OAAQ3K,YAAA,OAAAiM,UAAA,IAAoCjB,OAAQzL,MAAA2K,EAAAvM,OAAA,KAAAuO,SAAA,SAAAC,GAAiDjC,EAAAkC,KAAAlC,EAAAvM,OAAA,OAAAwO,IAAkC1B,WAAA,kBAA2B,GAAAP,EAAAY,GAAA,KAAAT,EAAA,gBAAqCK,YAAA,WAAAC,OAA8BrL,MAAA,MAAAmB,KAAA,SAA4B4J,EAAA,YAAiB2B,aAAatK,MAAA,QAAeiJ,OAAQ3K,YAAA,MAAAiM,UAAA,IAAmCjB,OAAQzL,MAAA2K,EAAAvM,OAAA,IAAAuO,SAAA,SAAAC,GAAgDjC,EAAAkC,KAAAlC,EAAAvM,OAAA,MAAAwO,IAAiC1B,WAAA,iBAA0B,GAAAP,EAAAY,GAAA,KAAAT,EAAA,gBAAqCK,YAAA,WAAAC,OAA8BrL,MAAA,OAAAmB,KAAA,UAA8B4J,EAAA,YAAiB2B,aAAatK,MAAA,QAAeiJ,OAAQ3K,YAAA,OAAAiM,UAAA,IAAoCjB,OAAQzL,MAAA2K,EAAAvM,OAAA,KAAAuO,SAAA,SAAAC,GAAiDjC,EAAAkC,KAAAlC,EAAAvM,OAAA,OAAAwO,IAAkC1B,WAAA,kBAA2B,GAAAP,EAAAY,GAAA,KAAAT,EAAA,gBAAqCK,YAAA,WAAAC,OAA8BrL,MAAA,MAAAmB,KAAA,SAA4B4J,EAAA,YAAiB2B,aAAatK,MAAA,QAAeiJ,OAAQ3K,YAAA,MAAAiM,UAAA,IAAmCjB,OAAQzL,MAAA2K,EAAAvM,OAAA,IAAAuO,SAAA,SAAAC,GAAgDjC,EAAAkC,KAAAlC,EAAAvM,OAAA,MAAAwO,IAAiC1B,WAAA,iBAA0B,GAAAP,EAAAY,GAAA,KAAAT,EAAA,gBAAqCK,YAAA,WAAAC,OAA8BrL,MAAA,OAAAmB,KAAA,UAA8B4J,EAAA,kBAAuBM,OAAO5K,KAAA,OAAAC,YAAA,OAAAI,OAAA,aAAAiM,eAAA,cAAqFrB,OAAQzL,MAAA2K,EAAAvM,OAAA,KAAAuO,SAAA,SAAAC,GAAiDjC,EAAAkC,KAAAlC,EAAAvM,OAAA,OAAAwO,IAAkC1B,WAAA,kBAA2B,OAAAP,EAAAY,GAAA,KAAAT,EAAA,QAAiCK,YAAA,gBAAAC,OAAmC2B,KAAA,UAAgBA,KAAA,WAAejC,EAAA,aAAkBM,OAAO5K,KAAA,WAAiB6K,IAAKM,MAAA,SAAAW,GAAyB,OAAA3B,EAAAvH,SAAA,YAA8BuH,EAAAY,GAAA,SAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA8CM,OAAO5K,KAAA,WAAiB6K,IAAKM,MAAA,SAAAW,GAAyB3B,EAAAtM,iBAAA,MAA8BsM,EAAAY,GAAA,wBAEt5GyB,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACElQ,EACAwN,GATF,EAVA,SAAA2C,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/30.4bfd3e3ca071af326c42.js", "sourcesContent": ["<template>\r\n  <div class=\"bg_con\" v-loading=\"loading\">\r\n    <div class=\"container\">\r\n      <BaseHeader :columns=\"columns\" :params=\"params\" @handleBtn=\"handleBtnAll\"></BaseHeader>\r\n      <el-form :inline=\"true\" :model=\"formInline\" size=\"medium\" class=\"fr\">\r\n        <el-form-item class=\"fr\">\r\n          <el-button type=\"danger\" size=\"medium\" @click=\"shanchu\" icon=\"el-icon-delete-solid\">\r\n            删除\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item class=\"fr\">\r\n          <el-button type=\"success\" size=\"medium\" @click=\"sendApplay\" icon=\"el-icon-plus\">\r\n            制作申请\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!-- 查询条件以及操作按钮end -->\r\n      <!-- 涉密人员任用审查列表start -->\r\n      <BaseTable :showSelection=true :selectionWidth=\"'55'\" :showIndex=true :tableData=\"smryList\" :columns=\"tableColumns\"\r\n        :handleColumn=\"handleColumn\" :handleColumnProp=\"handleColumnProp\" :showPagination=true :currentPage=\"page1\"\r\n        :pageSize=\"pageSize1\" :totalCount=\"total1\" @operateBtn=\"operateBtn\" @selectBtn=\"selectBtn\"\r\n        @handleCurrentChange=\"handleCurrentChange\" @handleSizeChange=\"handleSizeChange\">\r\n      </BaseTable>\r\n      <el-dialog title=\"添加废页\" :visible.sync=\"xgdialogVisible\" width=\"25%\" @close=\"close('form')\">\r\n        <el-form ref=\"form\" :model=\"tjlist\" label-width=\"130px\" size=\"mini\" :rules=\"rules\">\r\n          <el-form-item label=\"废页页数\" prop=\"fyys\" class=\"one-line\">\r\n            <el-input placeholder=\"废页页数\" v-model=\"tjlist.fyys\" clearable style=\"width:100%;\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"销毁人\" prop=\"xhr\" class=\"one-line\">\r\n            <el-input placeholder=\"销毁人\" v-model=\"tjlist.xhr\" clearable style=\"width:100%;\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"销毁方式\" prop=\"xhfs\" class=\"one-line\">\r\n            <el-input placeholder=\"销毁方式\" v-model=\"tjlist.xhfs\" clearable style=\"width:100%;\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"监销人\" prop=\"jxr\" class=\"one-line\">\r\n            <el-input placeholder=\"监销人\" v-model=\"tjlist.jxr\" clearable style=\"width:100%;\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"销毁日期\" prop=\"xhrq\" class=\"one-line\">\r\n            <el-date-picker v-model=\"tjlist.xhrq\" type=\"date\" placeholder=\"选择日期\" format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"xszzjgxz('form')\">保 存</el-button>\r\n          <el-button type=\"warning\" @click=\"xgdialogVisible = false\">关 闭</el-button>\r\n        </span>\r\n      </el-dialog>\r\n      <!-- 涉密人员任用审查列表end -->\r\n      <!-- 发起申请弹框start -->\r\n      <!-- <el-dialog title=\"选择涉密人员\" :close-on-click-modal=\"false\" :visible.sync=\"dialogVisible\" width=\"40%\">\r\n        <div class=\"dlFqsqContainer\">\r\n          <label for=\"\">部门:</label>\r\n          <el-cascader v-model=\"ryChoose.bm\" :options=\"regionOption\" :props=\"regionParams\" filterable clearable\r\n            ref=\"cascaderArr\" @change=\"bmSelectChange\"></el-cascader>\r\n          <label for=\"\">姓名:</label>\r\n          <el-input class=\"input2\" v-model=\"ryChoose.xm\" clearable placeholder=\"姓名\"></el-input>\r\n          <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\" @click=\"searchRy\">查询</el-button>\r\n          <BaseTable class=\"baseTable\" :tableHeight=\"'300'\" :key=\"tableKey\" :showIndex=true :tableData=\"ryDatas\" :columns=\"applyColumns\"\r\n            :showSingleSelection=\"true\" :handleColumn=\"handleColumnApply\" :showPagination=true :currentPage=\"page\"\r\n            :pageSize=\"pageSize\" :totalCount=\"total\" @handleCurrentChange=\"handleCurrentChangeRy\"\r\n            @handleSizeChange=\"handleSizeChangeRy\" @handleSelectionChange=\"handleSelectionChange\">\r\n          </BaseTable>\r\n        </div>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitRy()\">保 存</el-button>\r\n          <el-button type=\"warning\" @click=\"dialogVisible = false\">关 闭</el-button>\r\n        </span>\r\n      </el-dialog> -->\r\n      <!-- 发起申请弹框end -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  selectRyscPage,\r\n  removeRysc,\r\n  getRyscInfo,\r\n  getZzjgList,\r\n  getSpYhxxPage,\r\n  getLoginInfo,\r\n  getFwdyidByFwlx,\r\n  getZpBySmryid,\r\n  getZtqdListByYjlid,\r\n  updateZtqdByJlid\r\n} from '../../../api/index'\r\nimport {\r\n  selectPageZtglZtzz,\r\n  selectByIdZtglZtzz,\r\n  deleteZtglZtzz\r\n} from '../../../api/ztzzsc'\r\nimport {\r\n  getUserInfo,\r\n} from '../../../api/dwzc'\r\nimport BaseHeader from '../../components/common/baseHeader.vue'\r\nimport BaseTable from '../../components/common/baseTable.vue'\r\nexport default {\r\n  components: {\r\n    BaseHeader,\r\n    BaseTable\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      rules: {\r\n        fyys: [{\r\n          required: true,\r\n          message: '请输入废页页数',\r\n          trigger: 'blur'\r\n        },],\r\n        xhr: [{\r\n          required: true,\r\n          message: '请输入销毁人',\r\n          trigger: 'blur'\r\n        },],\r\n        xhfs: [{\r\n          required: true,\r\n          message: '请输入销毁方式',\r\n          trigger: 'blur'\r\n        },],\r\n        jxr: [{\r\n          required: true,\r\n          message: '请输入监销人',\r\n          trigger: 'blur'\r\n        },],\r\n        xhrq: [{\r\n          required: true,\r\n          message: '请输入监销日期',\r\n          trigger: ['blur', 'change']\r\n        },],\r\n      },\r\n      tjlist: {\r\n\r\n      },\r\n      xgdialogVisible: false,\r\n      loading: false,\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: '#EEF7FF',\r\n        color: '#4D91F8'\r\n      },\r\n      formInline: {}, // 搜索条件\r\n      dialogVisible: false, // 发起申请弹框\r\n      ryDatas: [], // 弹框人员选择\r\n      page: 1, // 弹框人员当前页\r\n      pageSize: 5, // 弹框人员每页条数\r\n      page1: 1, // 弹框人员当前页\r\n      pageSize1: 10, // 弹框人员每页条数\r\n      // 弹框人员选择条件\r\n      ryChoose: {\r\n        'bm': '',\r\n        'xm': ''\r\n      },\r\n      total: 0, // 弹框人员总数\r\n      total1: 0, // 弹框人员总数\r\n      radioIdSelect: '', // 弹框人员单选\r\n      smryList: [], //页面数据\r\n      scjtlist: [ //审查状态数据\r\n        {\r\n          mc: \"审批中\",\r\n          id: 0\r\n        },\r\n        {\r\n          mc: \"通过\",\r\n          id: 1\r\n        },\r\n        {\r\n          mc: \"已驳回\",\r\n          id: 2\r\n        },\r\n        {\r\n          mc: \"草稿\",\r\n          id: 3\r\n        }\r\n      ],\r\n      dqztlist: [ //当前状态数据\r\n        {\r\n          mc: \"审批中\",\r\n          id: 0\r\n        },\r\n        {\r\n          mc: \"已结束\",\r\n          id: 1\r\n        },\r\n        {\r\n          mc: \"已驳回\",\r\n          id: 2\r\n        },\r\n        {\r\n          mc: \"草稿\",\r\n          id: 3\r\n        }\r\n      ],\r\n      rowdata: [], //列表选中的值\r\n      regionOption: [], // 部门下拉\r\n      regionParams: {\r\n        label: 'label', //这里可以配置你们后端返回的属性\r\n        value: 'label',\r\n        children: 'childrenRegionVo',\r\n        expandTrigger: 'click',\r\n        checkStrictly: true\r\n      }, //地域信息配置参数\r\n      // 查询条件\r\n      params: {\r\n        name: '',\r\n        tmjssj: ''\r\n      },\r\n      // 查询条件以及功能按钮\r\n      columns: [{\r\n        type: 'searchInput',\r\n        name: '申请人',\r\n        value: 'xqr',\r\n        placeholder: '申请人',\r\n      },\r\n      {\r\n        type: 'dataRange',\r\n        name: '申请时间',\r\n        value: 'tmjssj',\r\n        startPlaceholder: '申请起始时间',\r\n        rangeSeparator: '至',\r\n        endPlaceholder: '申请结束时间',\r\n        format: 'yyyy-MM-dd'\r\n      },\r\n      {\r\n        type: 'button',\r\n        name: '查询',\r\n        disabled: false,\r\n        icon: 'el-icon-search',\r\n        mold: 'primary'\r\n      },\r\n      {\r\n        type: 'button',\r\n        name: '重置',\r\n        disabled: false,\r\n        icon: 'el-icon-circle-close',\r\n        mold: 'warning'\r\n      }\r\n      ],\r\n      // table项\r\n      tableColumns: [\r\n        {\r\n          name: '申请人',\r\n          prop: 'xqr',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '申请人部门',\r\n          prop: 'szbm',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '载体名称',\r\n          prop: 'ztmc',\r\n          scopeType: 'text',\r\n          formatter: false,\r\n          showOverflowTooltip: true,\r\n        },\r\n        {\r\n          name: '载体编号',\r\n          prop: 'ztbh',\r\n          scopeType: 'text',\r\n          formatter: false,\r\n          showOverflowTooltip: true,\r\n        },\r\n        {\r\n          name: '审查时间',\r\n          prop: 'cjsj',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '审查结果',\r\n          prop: 'Lcfwslzt',\r\n          scopeType: 'text',\r\n          formatter: (row, column, cellValue, index) => {\r\n            const options = [\r\n              {\r\n                mc: \"审批中\",\r\n                id: 0\r\n              },\r\n              {\r\n                mc: \"通过\",\r\n                id: 1\r\n              },\r\n              {\r\n                mc: \"已驳回\",\r\n                id: 2\r\n              },\r\n              {\r\n                mc: \"草稿\",\r\n                id: 3\r\n              }\r\n            ]\r\n            const opt = options.find(d => d.id === cellValue)\r\n            return opt ? opt.mc : ''\r\n          }\r\n        },\r\n        {\r\n          name: '当前状态',\r\n          prop: 'Lcfwslzt',\r\n          scopeType: 'text',\r\n          formatter: (row, column, cellValue, index) => {\r\n            const options = [\r\n              {\r\n                mc: \"审批中\",\r\n                id: 0\r\n              },\r\n              {\r\n                mc: \"已结束\",\r\n                id: 1\r\n              },\r\n              {\r\n                mc: \"已驳回\",\r\n                id: 2\r\n              },\r\n              {\r\n                mc: \"草稿\",\r\n                id: 3\r\n              }\r\n            ]\r\n            const opt = options.find(d => d.id === cellValue)\r\n            return opt ? opt.mc : ''\r\n          }\r\n        }\r\n      ],\r\n      // table操作按钮\r\n      handleColumn: [\r\n        {\r\n          name: '编辑',\r\n          disabled: false,\r\n          show: true,\r\n          formatter: (row, column) => {\r\n            if (row.Lcfwslzt == 3 && row.cjrid == this.loginName) {\r\n              return '编辑'\r\n            } else if (row.Lcfwslzt == 0 || row.Lcfwslzt == 1 || row.Lcfwslzt == 2) {\r\n              return '查看'\r\n            }\r\n          }\r\n        },\r\n        // {\r\n        //   name: '添加废页',\r\n        //   disabled: false,\r\n        //   show: true,\r\n        //   formatter: (row, column) => {\r\n        //     if (row.Lcfwslzt == 1&&row.xhr == undefined) {\r\n        //       return '添加废页'\r\n        //     }\r\n        //   }\r\n        // },\r\n        // {\r\n        //   name: '修改废页',\r\n        //   disabled: false,\r\n        //   show: true,\r\n        //   formatter: (row, column) => {\r\n        //     if (row.Lcfwslzt == 1&&row.xhr != undefined) {\r\n        //       return '修改废页'\r\n        //     }\r\n        //   }\r\n        // },\r\n      ],\r\n      // 表格的操作\r\n      handleColumnProp: {\r\n        label: '操作',\r\n        width: '230',\r\n        align: 'left'\r\n      },\r\n      // 发起申请table\r\n      applyColumns: [\r\n        {\r\n          name: '姓名',\r\n          prop: 'xm',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '部门',\r\n          prop: 'bmmc',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '岗位',\r\n          prop: 'gwmc',\r\n          scopeType: 'text',\r\n          formatter: (row, column, cellValue, index) => {\r\n            return cellValue.join('/')\r\n          }\r\n        }\r\n      ],\r\n      handleColumnApply: [],\r\n      // 查询条件以及功能按钮\r\n      smryColumns: [{\r\n        type: 'cascader',\r\n        name: '部门',\r\n        value: 'bmmc',\r\n        placeholder: '请选择部门',\r\n      }, {\r\n        type: 'searchInput',\r\n        name: '姓名',\r\n        value: 'name',\r\n        placeholder: '姓名',\r\n      },\r\n      {\r\n        type: 'button',\r\n        name: '查询',\r\n        disabled: false,\r\n        icon: 'el-icon-search',\r\n        mold: 'primary'\r\n      },\r\n      {\r\n        type: 'button',\r\n        name: '重置',\r\n        disabled: false,\r\n        icon: 'el-icon-circle-close',\r\n        mold: 'warning'\r\n      }\r\n      ],\r\n      // 当前登录人的用户名\r\n      loginName: ''\r\n    }\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.onfwid()\r\n    this.getLoginYhm() // 获取当前登录人姓名\r\n    this.rysclist() // 任用审查数据获取\r\n    this.zzjg() // 获取组织机构所有部门下拉\r\n  },\r\n  methods: {\r\n    // 弹框关闭触发\r\n    close(form) {\r\n      // 清空表单校验，避免再次进来会出现上次校验的记录\r\n      this.$refs[form].resetFields();\r\n    },\r\n    async xszzjgxz(form) {\r\n      this.$refs[form].validate((valid) => {\r\n        if (valid) {\r\n          let param = {\r\n            jlid: this.jlid,\r\n            fyys: this.tjlist.fyys,\r\n            xhr: this.tjlist.xhr,\r\n            jxr: this.tjlist.jxr,\r\n            xhrq: this.tjlist.xhrq,\r\n            xhfs: this.tjlist.xhfs,\r\n          }\r\n          updateZtqdByJlid(param)\r\n          this.xgdialogVisible = false\r\n          this.rysclist()\r\n        }else {\r\n          console.log('error submit!!');\r\n          return false;\r\n        }\r\n      })\r\n\r\n    },\r\n    // 获取当前登录人姓名\r\n    async getLoginYhm() {\r\n      let userInfo = await getUserInfo()\r\n      this.loginName = userInfo.yhm\r\n    },\r\n    //分页\r\n    handleSizeChange(val) {\r\n      this.page1 = 1\r\n      this.pageSize1 = val\r\n      this.rysclist()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page1 = val\r\n      this.rysclist()\r\n    },\r\n    // table复选集合\r\n    selectBtn(row) {\r\n      this.rowdata = row\r\n      console.log(row);\r\n    },\r\n    //删除\r\n    shanchu() {\r\n      if (this.rowdata.length == 0) {\r\n        this.$message({\r\n          message: '未选择想要删除的数据',\r\n          type: 'warning'\r\n        });\r\n      } else {\r\n        this.$confirm('此操作将永久删除该申请, 是否继续?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.rowdata.forEach(async (item) => {\r\n            let params = {\r\n              slid: item.slid\r\n            }\r\n            let res = await deleteZtglZtzz(params)\r\n            if (res.code == 10000) {\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.rysclist()\r\n            }\r\n          })\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          });\r\n        });\r\n      }\r\n    },\r\n    // 点击公共头部按钮事件\r\n    handleBtnAll(parameter, item) {\r\n      if (item.name == '查询') {\r\n        this.params = JSON.parse(JSON.stringify(parameter))\r\n        this.page1 = 1\r\n        this.rysclist()\r\n      } else if (item.name == '重置') {\r\n        this.params = {\r\n          name: '',\r\n          tmjssj: ''\r\n        }\r\n      }\r\n    },\r\n    //任用审查数据获取\r\n    async rysclist(parameter) {\r\n      let params = {\r\n        xqr: this.params.xqr,\r\n        page: this.page1,\r\n        pageSize: this.pageSize1\r\n      }\r\n      if (this.params.tmjssj != null) {\r\n        params.kssj = this.params.tmjssj[0]\r\n        params.jssj = this.params.tmjssj[1]\r\n      }\r\n      let data = await selectPageZtglZtzz(params)\r\n      if (data.data.records) {\r\n        this.smryList = data.data.records\r\n        this.total1 = data.data.total\r\n      } else {\r\n        this.$message.error('数据获取失败！')\r\n      }\r\n    },\r\n    // 人员选择弹框保存按钮\r\n    submit() {\r\n      this.$router.push('/ryscTable')\r\n    },\r\n    // 人员搜索\r\n    searchRy() {\r\n      this.tableKey++\r\n      this.page = 1\r\n      this.sendApplay()\r\n    },\r\n    // 发起申请\r\n    async sendApplay() {\r\n      this.$router.push({\r\n        path: '/ztzzscTable',\r\n        query: {\r\n          type: 'add',\r\n        }\r\n      })\r\n    },\r\n    handleCurrentChangeRy(val) {\r\n      this.page = val\r\n      this.sendApplay()\r\n    },\r\n    //列表分页--更改每页显示个数\r\n    handleSizeChangeRy(val) {\r\n      this.page = 1\r\n      this.pageSize = val\r\n      this.sendApplay()\r\n    },\r\n    handleSelectionChange(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    // 选择人员提交\r\n    async submitRy() {\r\n      this.loading = true\r\n      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {\r\n        this.loading = false\r\n        let zp = await getZpBySmryid({ smryid: this.radioIdSelect.smryid })\r\n        this.radioIdSelect.zp = zp\r\n        this.$router.push({\r\n          path: '/ryscTable',\r\n          query: {\r\n            type: 'add',\r\n            datas: this.radioIdSelect\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.error('请选择涉密人员')\r\n        this.loading = false\r\n      }\r\n    },\r\n    //审查状态数据回想\r\n    scjgsj(row) {\r\n      let data;\r\n      this.scjtlist.forEach(item => {\r\n        if (item.id == row.Lcfwslzt) {\r\n          data = item.mc\r\n        }\r\n      })\r\n      return data\r\n    },\r\n    //当前状态数据回想\r\n    dqztsj(row) {\r\n      let data;\r\n      this.dqztlist.forEach(item => {\r\n        if (item.id == row.Lcfwslzt) {\r\n          data = item.mc\r\n        }\r\n      })\r\n      return data\r\n    },\r\n    async onfwid() {\r\n      let params = {\r\n        fwlx: 18\r\n      }\r\n      let data = await getFwdyidByFwlx(params)\r\n      console.log(data);\r\n      this.fwdyid = data.data.fwdyid\r\n    },\r\n    // 功能操作按钮\r\n    async operateBtn(row, item) {\r\n      // 编辑方法\r\n      if (item == '编辑') {\r\n        this.loading = true\r\n        let res = await selectByIdZtglZtzz({\r\n          'jlid': row.jlid\r\n        })\r\n        let zt = await getZtqdListByYjlid({\r\n          'yjlid': row.jlid\r\n        })\r\n\r\n        if (res.slid) {\r\n          this.loading = false\r\n          this.$router.push({\r\n            path: '/ztzzscTable',\r\n            query: {\r\n              type: 'update',\r\n              datas: res,\r\n              ztzz: zt\r\n              // cjrid: \r\n            }\r\n          })\r\n        } else {\r\n          this.$message.error('任务不匹配！')\r\n        }\r\n      } else if (item == '查看') {  // 查看方法\r\n        let fwdyid = this.fwdyid\r\n        if (this.fwdyid == '' || this.fwdyid == undefined) {\r\n          this.$message.error('请到流程管理进行配置');\r\n        } else {\r\n          this.$router.push({\r\n            path: '/ztzzscblxxscb',\r\n            query: {\r\n              lx: '载体制作',\r\n              fwdyid: fwdyid,\r\n              slid: row.slid\r\n            }\r\n          })\r\n        }\r\n\r\n      } else if (item == '添加废页') {\r\n        this.jlid = row.jlid\r\n        this.xgdialogVisible = true\r\n      }else if (item == '修改废页') {\r\n        this.jlid = row.jlid\r\n        this.tjlist.xhr = row.xhr\r\n        this.tjlist.fyys = row.fyys\r\n        this.tjlist.jxr = row.jxr\r\n        this.tjlist.xhfs = row.xhfs\r\n        this.tjlist.xhrq = row.xhrq\r\n        this.xgdialogVisible = true\r\n      }\r\n    },\r\n    //全部组织机构List\r\n    async zzjg() {\r\n      let zzjgList = await getZzjgList()\r\n      this.zzjgmc = zzjgList\r\n      let shu = []\r\n      this.zzjgmc.forEach(item => {\r\n        let childrenRegionVo = []\r\n        this.zzjgmc.forEach(item1 => {\r\n          if (item.bmm == item1.fbmm) {\r\n            childrenRegionVo.push(item1)\r\n            item.childrenRegionVo = childrenRegionVo\r\n          }\r\n        });\r\n        shu.push(item)\r\n      })\r\n      let shuList = []\r\n      let list = await getLoginInfo()\r\n      if (list.fbmm == '') {\r\n        shu.forEach(item => {\r\n          if (item.fbmm == '') {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      if (list.fbmm != '') {\r\n        shu.forEach(item => {\r\n          console.log(item);\r\n          if (item.fbmm == list.fbmm) {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      shuList[0].childrenRegionVo.forEach(item => {\r\n        this.regionOption.push(item)\r\n      })\r\n    },\r\n    // 发起申请选择人员 人员下拉\r\n    bmSelectChange(item) {\r\n      console.log(item)\r\n      if (item != undefined) {\r\n        this.ryChoose.bm = item.join('/')\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.fl {\r\n  float: left;\r\n}\r\n\r\n.fr {\r\n  float: right;\r\n}\r\n\r\n.container {\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n  height: 100%;\r\n  /* box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13); */\r\n  border-radius: 8px;\r\n}\r\n\r\n.bg_con {\r\n  width: 100%;\r\n  height: calc(100% - 38px);\r\n}\r\n\r\n/deep/.mhcx .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n/* 发起申请弹框 */\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n\r\n.spImg {\r\n  width: 15px;\r\n}\r\n\r\n.baseTable {\r\n  margin-top: 20px;\r\n  /* height: 400px!important; */\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/ztzzsc.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"bg_con\"},[_c('div',{staticClass:\"container\"},[_c('BaseHeader',{attrs:{\"columns\":_vm.columns,\"params\":_vm.params},on:{\"handleBtn\":_vm.handleBtnAll}}),_vm._v(\" \"),_c('el-form',{staticClass:\"fr\",attrs:{\"inline\":true,\"model\":_vm.formInline,\"size\":\"medium\"}},[_c('el-form-item',{staticClass:\"fr\"},[_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"medium\",\"icon\":\"el-icon-delete-solid\"},on:{\"click\":_vm.shanchu}},[_vm._v(\"\\n          删除\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"fr\"},[_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"medium\",\"icon\":\"el-icon-plus\"},on:{\"click\":_vm.sendApplay}},[_vm._v(\"\\n          制作申请\\n        \")])],1)],1),_vm._v(\" \"),_c('BaseTable',{attrs:{\"showSelection\":true,\"selectionWidth\":'55',\"showIndex\":true,\"tableData\":_vm.smryList,\"columns\":_vm.tableColumns,\"handleColumn\":_vm.handleColumn,\"handleColumnProp\":_vm.handleColumnProp,\"showPagination\":true,\"currentPage\":_vm.page1,\"pageSize\":_vm.pageSize1,\"totalCount\":_vm.total1},on:{\"operateBtn\":_vm.operateBtn,\"selectBtn\":_vm.selectBtn,\"handleCurrentChange\":_vm.handleCurrentChange,\"handleSizeChange\":_vm.handleSizeChange}}),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"添加废页\",\"visible\":_vm.xgdialogVisible,\"width\":\"25%\"},on:{\"update:visible\":function($event){_vm.xgdialogVisible=$event},\"close\":function($event){return _vm.close('form')}}},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"130px\",\"size\":\"mini\",\"rules\":_vm.rules}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"废页页数\",\"prop\":\"fyys\"}},[_c('el-input',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"废页页数\",\"clearable\":\"\"},model:{value:(_vm.tjlist.fyys),callback:function ($$v) {_vm.$set(_vm.tjlist, \"fyys\", $$v)},expression:\"tjlist.fyys\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"销毁人\",\"prop\":\"xhr\"}},[_c('el-input',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"销毁人\",\"clearable\":\"\"},model:{value:(_vm.tjlist.xhr),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xhr\", $$v)},expression:\"tjlist.xhr\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"销毁方式\",\"prop\":\"xhfs\"}},[_c('el-input',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"销毁方式\",\"clearable\":\"\"},model:{value:(_vm.tjlist.xhfs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xhfs\", $$v)},expression:\"tjlist.xhfs\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"监销人\",\"prop\":\"jxr\"}},[_c('el-input',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"监销人\",\"clearable\":\"\"},model:{value:(_vm.tjlist.jxr),callback:function ($$v) {_vm.$set(_vm.tjlist, \"jxr\", $$v)},expression:\"tjlist.jxr\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"销毁日期\",\"prop\":\"xhrq\"}},[_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(_vm.tjlist.xhrq),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xhrq\", $$v)},expression:\"tjlist.xhrq\"}})],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.xszzjgxz('form')}}},[_vm._v(\"保 存\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.xgdialogVisible = false}}},[_vm._v(\"关 闭\")])],1)],1)],1)])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-fb1378da\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/ztzzsc.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-fb1378da\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ztzzsc.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ztzzsc.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ztzzsc.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-fb1378da\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ztzzsc.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-fb1378da\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/ztzzsc.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}