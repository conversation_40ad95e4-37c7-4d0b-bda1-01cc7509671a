<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div class="bg_con_top">
      <img src="./img/title.png" alt="" />
      <span
        class="title"
        :class="[sbxxQhVal == 1 ? 'title1' : '']"
        @click="sbxxClick(1)"
        >机房信息</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 2 ? 'title1' : '']"
        @click="sbxxClick(2)"
        v-if="tjType == '3'"
        >机房巡检信息</span
      >
    </div>
    <div v-if="sbxxQhVal == 1">
      <el-form ref="form" :model="form" size="mini" label-width="120px">
        <div class="flex">
          <el-form-item label="机房编号">
            <el-input v-model="form.computerRoomCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="机房名称">
            <el-input
              v-model="form.computerRoomName"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="机柜数量">
            <el-input
              v-model="form.cabinetNumber"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <!--          <el-form-item label="服务器数量">
            <el-input
              v-model="form.serverNumber"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="交换机数量">
            <el-input
              v-model="form.switchNumber"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="路由器数量">
            <el-input
              v-model="form.routerNumber"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex">-->
          <el-form-item label="地址">
            <el-input v-model="form.address" :disabled="disabled"></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="上次巡检时间" v-show="show">
            <el-date-picker
              :disabled="true"
              v-model="form.lastInspectionTime"
              v-show="show"
              style="width: 100%"
              clearable
              type="date"
              placeholder="选择时间"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item label="上次巡检结果" v-show="show">
            <el-input
              v-model="form.lastInspectionResult"
              :disabled="true"
              v-show="show"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="使用状态" v-show="show">
            <el-select
              :disabled="disabled"
              v-show="show"
              v-model="form.useStatus"
              clearable
              placeholder="请选择类型"
              class="widthx"
            >
              <el-option
                v-for="item in syztList"
                :label="item.mc"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <!-- <div class="flex">
          <el-form-item label="审批状态">
            <el-select
              :disabled="disabled"
              v-model="form.approveStatus"
              clearable
              placeholder="请选择类型"
              class="widthx"
            >
              <el-option
                v-for="item in spztList"
                :label="item.mc"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div> -->
        <div class="flex">
          <el-form-item label="备注">
            <el-input
              type="textarea"
              :disabled="disabled"
              v-model="form.remark"
            ></el-input>
          </el-form-item>
        </div>
        <div style="width: 950px">
          <el-form-item style="float: right">
            <el-button type="success" @click="add" v-if="tjType == '1'"
              >提交</el-button
            >
            <el-button type="primary" @click="update" v-if="tjType == '2'"
              >修改</el-button
            >
            <!-- <el-button class="ml-10" v-if="tjType != '1'" @click="print"
              >巡检工单打印</el-button
            > -->
            <el-button type="primary" @click="fh">返回</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <el-dialog :visible.sync="dialogVisible" ref="dialog" width="28.7%">
      <template #title>
        <img
          src="./img/title.png"
          style="margin-right: 15px"
          alt="机房巡检工单图标"
        />
        机房巡检工单
      </template>
      <div class="formDialog">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">机房编号：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.computerRoomCode }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">机房名称：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.computerRoomName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">机房地址：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.address }}</div>
        </div>
      </div>
      <div class="fjx"></div>
      <div class="formDialog formDialog1">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">提交人：</div>
          <div class="formDialogCon1">{{ this.xjgddyQbj.createByName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <!-- <div class="formDialogItem">负责人：</div>
          <div class="formDialogCon1"></div> -->
        </div>
        <div class="flexAlign">
          <div class="formDialogItem">打印时间：</div>
          <div class="formDialogCon1">
            {{ this.xjgddyQbj.printTime }}
          </div>
        </div>
        <div class="ewm">
          <img :src="img" width="130" height="130" />
        </div>
      </div>
      <div class="btnRight" v-if="!isPrinting">
        <el-button
          @click="printToPDF"
          type="primary"
          class="btnRightItem"
          size="mini"
          >打 印</el-button
        >
      </div>
    </el-dialog>
    <div style="height: 100%" v-if="sbxxQhVal == 2">
      <el-table
        :data="xjList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="computerRoomCode"
          label="机房编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="computerRoomName"
          label="机房名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cleanliness"
          label="清洁度"
          sortable
        ></el-table-column>
        <el-table-column
          prop="airConditioning"
          label="空调"
          sortable
        ></el-table-column>
        <el-table-column
          prop="powerEnvironmentSystem"
          label="机房动力环境系统运行"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionTime"
          label="巡检时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionCode"
          label="巡检人员编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionName"
          label="巡检人员名称"
          sortable
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

import {
  insertComputerRoom,
  updateComputerRoom,
  jfQueryByCondition,
  getComputerRoomCode,
  downloadInspectionForm,
  downloadInspectionComputerRoom,
  selectEquipmentInspectionProcessPage,
  selectComputerRoomInspectionProcessPage,
} from "../../../../api/shma";
import AraleQRCode from "arale-qrcode";

export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        id: "",
        computerRoomCode: "",
        computerRoomName: "",
        cabinetNumber: "",
        serverNumber: "",
        switchNumber: "",
        routerNumber: "",
        address: "",
        lastInspectionTime: "",
        lastInspectionResult: "",
        remark: "",
        useStatus: 1,
        approveStatus: "",
      },
      tjType: "",
      id: "",
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" },
      ],
      spztList: [
        { id: 1, mc: "正在审批" },
        { id: 2, mc: "审批通过" },
        { id: 3, mc: "审批拒绝" },
      ],
      sbxxQhVal: 1,

      xjList: [],
      ksList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      disabled: false,
      show: true, //是否显示
      isPrinting: false, //是否正在打印
      dialogVisible: false, //弹框
      img: "",
      xjgddyQbj: {},
    };
  },
  computed: {},
  mounted() {
    this.tjType = this.$route.query.routeType;
    if (this.tjType == "1") {
      this.getComputerRoomCode();
    } else if (this.tjType == "2") {
      this.id = this.$route.query.id;
      this.jfQueryByCondition();
    } else if (this.tjType == "3") {
      this.disabled = true;
      this.id = this.$route.query.id;
      this.jfQueryByCondition();
    }
  },
  methods: {
    //生成二维码方法
    makeCode(item) {
      const result = new AraleQRCode({
        render: "svg", // 定义生成的类型 'svg' or 'table dom’
        text: item, // 二维码的链接
        size: 150, //二维码大小
      });

      // 将svg xml文档转换成字符串
      const svgXml = new XMLSerializer().serializeToString(result);

      // 将svg字符串转成base64格式，通过 window.btoa方法创建一个 base-64 编码的字符串，进行二次编码解码(encodeURIComponent 字符串进行编码和解码，unescape 进行解码)。
      const src =
        "data:image/svg+xml;base64," +
        window.btoa(unescape(encodeURIComponent(svgXml)));

      // 本地存储图片
      localStorage.setItem("image", src);
      this.getImg();
    },

    sbxxClick(index) {
      console.log(index);
      this.sbxxQhVal = index;
      if (this.sbxxQhVal == 2) {
        this.selectComputerRoomInspectionProcessPage();
      }
    },
    async selectComputerRoomInspectionProcessPage() {
      let data = await selectComputerRoomInspectionProcessPage({
        id: this.form.id,
        pageNo: this.page,
        pageSize: this.pageSize,
      });
      console.log(data);
      this.xjList = data.data.records;
      this.total = data.data.total;
    },

    // 获取存储的图片给到页面
    getImg() {
      this.img = localStorage.getItem("image");
      console.log(this.img);

      localStorage.removeItem("image");
    },
    async print() {
      let data = await downloadInspectionForm([this.form]);
      console.log(data.data, "data326");
      if (data.code == 10000) {
        this.makeCode(data.data.barcodePath);
        const computerRoomCode = [];
        const computerRoomName = [];
        const serverNumber = [];
        const switchNumber = [];
        const address = [];
        const createByName = [];
        const printTime = [];
        data.data.inspectionOrderList.forEach((item) => {
          computerRoomCode.push(item.computerRoomCode);
          computerRoomName.push(item.computerRoomName);
          serverNumber.push(item.serverNumber);
          switchNumber.push(item.switchNumber);
          address.push(item.address);
          createByName.push(item.createByName);
          printTime.push(item.printTime);
        });
        this.xjgddyQbj = {
          computerRoomCode: computerRoomCode.join(","),
          computerRoomName: computerRoomName.join(","),
          serverNumber: serverNumber.join(","),
          switchNumber: switchNumber.join(","),
          address: address.join(","),
          createByName: createByName[0],
          printTime: printTime[0],
        }; // 将拼接后的字符串作为一个对象存储
        console.log(this.xjgddyQbj); // 打印拼接后的对象
        this.dialogVisible = true;
      }
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },
    async printToPDF() {
      let data = await downloadInspectionComputerRoom([this.form]);
      console.log(data, "data326");
      this.dom_download(data, "机房巡检工单.docx");
      // this.isPrinting = true; // 设置打印状态
      // setTimeout(() => {
      //   const element = this.$refs.dialog.$el;
      //   html2canvas(element).then((canvas) => {
      //     const imgData = canvas.toDataURL("image/png");
      //     const pdf = new jsPDF("p", "mm", "a4"); // 使用A4纸张，可以调整为其他尺寸
      //     const imgProps = pdf.getImageProperties(imgData);
      //     const pdfWidth = pdf.internal.pageSize.getWidth();
      //     const pdfHeight = pdf.internal.pageSize.getHeight();
      //
      //     // 设置图片宽度为PDF宽度的2倍
      //     const imgWidth = pdfWidth * 2;
      //     // 计算图片高度以保持比例
      //     const imgHeight = (imgProps.height / imgProps.width) * imgWidth;
      //
      //     // 添加图片到PDF，居中对齐
      //     const imgX = (pdfWidth - imgWidth) / 2;
      //     const imgY = 10; // 设置图片的Y位置
      //
      //     pdf.addImage(imgData, "PNG", imgX, imgY, imgWidth, imgHeight);
      //     pdf.save("机房巡检工单.pdf");
      //     this.isPrinting = false; // 重置打印状态
      //     this.dialogVisible = false; // 关闭弹框
      //   });
      // }, 1000); // 打印超时时间
    },
    async jfQueryByCondition() {
      let data = await jfQueryByCondition({ id: this.id });
      console.log(data, "data191");
      this.form = data.data;
    },
    async getComputerRoomCode() {
      this.show = false;
      let data = await getComputerRoomCode();
      this.form.computerRoomCode = data.data.computerRoomCode;
    },
    fh() {
      this.$router.push({ path: "/jfgl" });
    },
    async add() {
      let params = this.form;
      let data = await insertComputerRoom(params);
      console.log(data);
      if (data.code == 10000) {
        this.$message.success("提交成功");
        this.$router.push({ path: "/jfgl" });
      } else {
        this.$message.error("提交失败");
      }
    },
    async update() {
      let params = this.form;
      let data = await updateComputerRoom(params);
      console.log(data);
      if (data.code == 10000) {
        this.$message.success("提交成功");
        this.$router.push({ path: "/jfgl" });
      } else {
        this.$message.error("提交失败");
      }
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  padding: 10px;
}
.bg_con_top {
  width: 100%;
  height: 35px;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 20px;
}
/deep/.el-form-item .el-form-item__label {
  font-family: SourceHanSansSC-Regular !important;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
  text-align: left;
}
.flex {
  width: 950px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/deep/.el-input .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-select .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-textarea .el-textarea__inner {
  width: 830px !important;
  height: 129px !important;
  border-radius: 2px;
}
.ml-10 {
  background: #20bdd1;
  border: 1px solid #20bdd1;
  color: #ffffff;
}
/deep/.el-dialog__wrapper .el-dialog {
  background-image: linear-gradient(180deg, #f0f7fe 0%, #ffffff 32%);
  border: 1px solid rgba(151, 151, 151, 1);
  border-radius: 4px;
  padding-left: 6px;
  padding-right: 6px;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__header {
  padding: 15px 14.5px 13.5px 14px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  display: flex;
  align-items: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__body {
  padding: 0;
}
.formDialog {
  padding: 23.5px 45px 25px 45px;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #000116;
  font-weight: 400;
  position: relative;
}
.formDialog1 {
  padding: 38px 45px 49px 45px;
  margin-bottom: 10px;
}
.flexAlign {
  display: flex;
  align-items: center;
}
.formDialogItem {
  width: 100px;
  height: 24px;
  margin-right: 10px;
}
.formDialogCon {
  width: 320px;
  height: 24px;
}
.formDialogCon1 {
  width: 168px;
  height: 24px;
}
.fjx {
  width: 510px;
  height: 1px;
  background-color: rgba(229, 229, 229, 1);
  margin: 0 auto;
}
.ewm {
  position: absolute;
  width: 145px;
  height: 145px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 18px;
  right: 31.54px;
  border: 4px solid #d8e4fa;
  border-radius: 10px;
}
.btnRight {
  height: 40px;
  position: relative;
}
.btnRightItem {
  position: absolute;
  right: 31.54px;
}
.title {
  margin-left: 10px;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  position: relative;
}

.title1::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: -15px;
  background-color: #0077d2;
}
</style>
