<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div
      style="width: 100%; position: relative; overflow: hidden; height: 100%"
    >
      <div class="dabg" style="height: 100%">
        <div class="content" style="height: 100%">
          <div class="table" style="height: 100%">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form
                :inline="true"
                :model="formInline"
                size="medium"
                class="demo-form-inline"
              >
                <el-form-item style="font-weight: 700">
                  <el-input
                    v-model="formInline.ztbh"
                    clearable
                    placeholder="载体编号"
                    class="widths"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700">
                  <el-select
                    v-model="formInline.lx"
                    clearable
                    placeholder="类型"
                    class="widthx"
                  >
                    <el-option
                      v-for="item in sblxxz"
                      :label="item.mc"
                      :value="item.id"
                      :key="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700">
                  <el-select
                    v-model="formInline.smmj"
                    clearable
                    placeholder="密级"
                    class="widthx"
                  >
                    <el-option
                      v-for="item in sbmjxz"
                      :label="item.mc"
                      :value="item.id"
                      :key="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700">
                  <el-date-picker
                    v-model="formInline.qsrq"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="签收日期开始时间"
                    end-placeholder="签收日期结束时间"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item style="font-weight: 700">
                  <el-input
                    v-model="formInline.yjr"
                    clearable
                    placeholder="移交人"
                    class="widths"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700">
                  <el-cascader
                    v-model="formInline.jsdw"
                    :options="regionOption"
                    clearable
                    class="widths"
                    :props="regionParams"
                    filterable
                    ref="cascaderArr"
                    placeholder="接收单位"
                    @change="cxbm"
                  ></el-cascader>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    @click="onSubmit"
                    >查询</el-button
                  >
                </el-form-item>

                <div style="float: right">
                  <el-form-item style="float: right; margin-left: 5px">
                    <el-button
                      type="danger"
                      size="medium"
                      @click="shanchu"
                      v-if="this.dwjy"
                      icon="el-icon-delete-solid"
                    >
                      删除
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right; margin-left: 5px">
                    <el-button
                      type="primary"
                      size="medium"
                      icon="el-icon-download"
                      @click="exportList()"
                      >导出
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right">
                    <el-button
                      type="success"
                      size="medium"
                      v-if="this.dwjy"
                      @click="sendApplay()"
                      icon="el-icon-plus"
                    >
                      新增
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%">
              <div class="table_content" style="height: 100%">
                <el-table
                  :data="smzttzList"
                  ref="tableDiv"
                  border
                  @selection-change="selectRow"
                  :header-cell-style="{
                    background: '#EEF7FF',
                    color: '#4D91F8',
                  }"
                  style="width: 100%; border: 1px solid #ebeef5"
                  height="calc(100% - 34px - 40px)"
                  class="table"
                  stripe
                >
                  <el-table-column type="selection" width="55" align="center">
                  </el-table-column>
                  <el-table-column
                    type="index"
                    width="60"
                    label="序号"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="xmbh"
                    label="项目编号"
                  ></el-table-column>
                  <el-table-column prop="ztmc" label="载体名称">
                  </el-table-column>
                  <el-table-column
                    prop="ztbh"
                    label="载体编号"
                  ></el-table-column>
                  <el-table-column
                    prop="lx"
                    label="载体类型"
                    :formatter="forztlx"
                  ></el-table-column>
                  <el-table-column
                    prop="smmj"
                    label="密级"
                    :formatter="formj"
                  ></el-table-column>
                  <el-table-column
                    prop="bmqx"
                    label="保密期限"
                    width="90"
                  ></el-table-column>
                  <el-table-column
                    prop="fs"
                    label="份数"
                    width="70"
                  ></el-table-column>
                  <el-table-column
                    prop="ys"
                    label="页数"
                    width="70"
                  ></el-table-column>
                  <el-table-column prop="yjr" label="移交人"></el-table-column>
                  <el-table-column
                    prop="jsdw"
                    label="接收单位"
                    width="80"
                  ></el-table-column>
                  <el-table-column prop="qsr" label="签收人"></el-table-column>
                  <el-table-column
                    prop="qsrq"
                    label="签收日期"
                  ></el-table-column>
                  <el-table-column prop="" label="操作" width="140" v-if="this.dwjy">
                    <template slot-scope="scoped">
                      <el-button
                        size="medium"
                        type="text"
                        @click="getTrajectory(scoped.row)"
                        >签收单
                      </el-button>
                      <el-button
                        size="medium"
                        type="text"
                        @click="xgxx(scoped.row)"
                        >修改
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5">
                  <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    :pager-count="5"
                    :current-page="qspage"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="qspageSize"
                    layout="total, prev, pager, sizes,next, jumper"
                    :total="qstotal"
                  >
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 发起申请弹框start -->
    <el-dialog
      title="选择载体信息"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      width="70%"
    >
      <div class="dlFqsqContainer">
        <label for="">载体编号:</label>
        <el-input
          class="input2"
          v-model="ryChoose.ztbh"
          clearable
          placeholder="载体编号"
        ></el-input>
        <label for="">外发日期:</label>
        <el-date-picker
          v-model="ryChoose.wfrq"
          type="daterange"
          range-separator="至"
          start-placeholder="外发日期开始日期"
          end-placeholder="外发日期结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
        <el-button
          class="searchButton"
          type="primary"
          icon="el-icon-search"
          @click="searchRy"
          >查询</el-button
        >
        <BaseTable
          class="baseTable"
          :tableHeight="'300'"
          :showIndex="true"
          :tableData="ryDatas"
          :columns="applyColumns"
          :showSingleSelection="true"
          :handleColumn="handleColumnApply"
          :showPagination="true"
          :currentPage="page"
          :pageSize="pageSize"
          :totalCount="total"
          @handleCurrentChange="handleCurrentChangeRy"
          @handleSizeChange="handleSizeChangeRy"
          @handleSelectionChange="handleSelectionChange"
        >
        </BaseTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRy()">保 存</el-button>
        <el-button type="warning" @click="dialogVisible = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
    <!-- 发起申请弹框end -->
  </div>
</template>
<script>
import {
  getZtglList,
  getZzjgList,
  getAllYhxx,
  getLoginInfo,
  getFwdyidByFwlx,
} from "../../../api/index";
import {
  getAllSmztYy, //原因
  getSmztZt, //状态
  getSmztlx, //类型
  getAllSmsbmj, //密级
} from "../../../api/xlxz";
import { getCurZt } from "../../../api/zhyl";
import {
  // 获取注册信息
  getDwxx,
} from "../../../api/dwzc";
import { selectZtglWfcddjBySlid } from "../../../api/ztwf";
import {
  removeZtQsdj,
  exportZtqsExcel,
  selectZtQsdjPage,
} from "../../../api/ztqsdj";
import { dateFormatNYR } from "@/utils/moment";
import BaseTable from "../../components/common/baseTable.vue";
export default {
  components: { BaseTable },
  props: {},
  data() {
    return {
      ztbh: "",
      pdsmzt: 0,
      sbmjxz: [], //密级
      ztscyyxz: [], //生产原因
      sblxxz: [],
      sbsyqkxz: [],
      smzttzList: [],
      formInline: {},
      tjlist: {
        ztmc: "",
        ztbh: "",
        xmbh: "",
        scyy: "",
        smmj: "",
        bmqx: "",
        lx: "",
        fs: "",
        ys: "",
        zxfw: "",
        scrq: "",
        scbm: "",
        zrr: "",
        bgwz: "",
        zt: "",
        ztbgsj: "",
      },
      page: 1,
      pageSize: 10,
      total: 0,
      qspage: 1,
      qspageSize: 10,
      qstotal: 0,
      selectlistRow: [], //列表的值
      regionOption: [], //地域信息
      regionParams: {
        label: "label", //这里可以配置你们后端返回的属性
        value: "label",
        children: "childrenRegionVo",
        expandTrigger: "click",
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: "",
      year: "",
      yue: "",
      ri: "",
      Date: "",
      xh: [],
      dclist: [],
      // 弹框人员选择条件
      ryChoose: {
        ztbh: "",
        wfrq: "",
      },
      // 查询条件
      params: {
        name: "",
        tmjssj: "",
      },
      radioIdSelect: "", // 弹框人员单选
      dialogVisible: false, // 发起申请弹框
      ryDatas: [], // 弹框人员选择

      // 发起申请table
      applyColumns: [
        {
          name: "载体编号",
          prop: "ztbh",
          scopeType: "text",
          formatter: false,
        },
        {
          name: "项目编号",
          prop: "xmbh",
          scopeType: "text",
          formatter: false,
        },
        {
          name: "保密期限",
          prop: "bmqx",
          scopeType: "text",
          formatter: false,
        },
        {
          name: "页数/大小",
          prop: "ys",
          scopeType: "text",
          formatter: false,
        },
        {
          name: "份数",
          prop: "fs",
          scopeType: "text",
          formatter: false,
        },
      ],
      handleColumnApply: [],
      fwdyid: "",
      cxbmsj: "",
      sfqs: 0,
      dwjy: true,
    };
  },
  computed: {},
  mounted() {
    this.getLogin();
    this.ztyy();
    this.ztmj();
    this.ztlx();
    this.ztzt();
    this.zzjg();
    this.smry();
    this.smzttz();
    this.zhsj();
    this.rydata();
    this.onfwid();
    let anpd = localStorage.getItem('dwjy');
    console.log(anpd);
    if (anpd == 1) {
      this.dwjy = false
    }
    else {
      this.dwjy = true
    }
  },
  methods: {
    //获取登录信息
    async getLogin() {
      this.dwxxList = await getDwxx();
    },
    //全部组织机构List
    async zzjg() {
      let zzjgList = await getZzjgList();
      console.log(zzjgList);
      this.zzjgmc = zzjgList;
      let shu = [];
      console.log(this.zzjgmc);
      this.zzjgmc.forEach((item) => {
        let childrenRegionVo = [];
        this.zzjgmc.forEach((item1) => {
          if (item.bmm == item1.fbmm) {
            // console.log(item, item1);
            childrenRegionVo.push(item1);
            // console.log(childrenRegionVo);
            item.childrenRegionVo = childrenRegionVo;
          }
        });
        // console.log(item);
        shu.push(item);
      });

      console.log(shu);
      console.log(shu[0].childrenRegionVo);
      let shuList = [];
      let list = await getLoginInfo();
      if (list.fbmm == "") {
        shu.forEach((item) => {
          if (item.fbmm == "") {
            shuList.push(item);
          }
        });
      }
      if (list.fbmm != "") {
        shu.forEach((item) => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item);
          }
        });
      }
      console.log(shuList);
      shuList[0].childrenRegionVo.forEach((item) => {
        this.regionOption.push(item);
      });
    },

    async zhsj() {
      let sj = await getCurZt();
      if (sj != "") {
        this.tjlist = sj;
        this.tjlist.scbm = this.tjlist.scbm.split("/");
      }
    },
    async ztyy() {
      this.ztscyyxz = await getAllSmztYy();
    },
    async ztmj() {
      this.sbmjxz = await getAllSmsbmj();
    },
    async ztlx() {
      this.sblxxz = await getSmztlx();
    },
    async ztzt() {
      this.sbsyqkxz = await getSmztZt();
    },
    // 跳转到详情信息
    async getTrajectory(row) {
      this.$router.push({
        path: "/ztqsdjTable",
        query: {
          type: "xqxx",
          jlid: row.jlid,
        },
      });
    },
    xgxx(row) {
      this.$router.push({
        path: "/ztqsdjTable",
        query: {
          type: "updata",
          jlid: row.jlid,
          fwdyid: this.fwdyid,
          datas: row,
        },
      });
    },
    // 发起申请
    async sendApplay() {
      this.dialogVisible = true;
      let param = {
        page: this.page,
        pageSize: this.pageSize,
        sfqs: this.sfqs,
      };
      if (this.ryChoose.ztbh != "") {
        param.ztbh = this.ryChoose.ztbh;
      }
      if (this.ryChoose.wfrq != null) {
        param.wfqssj = this.ryChoose.wfrq[0];
        param.wfjzsj = this.ryChoose.wfrq[1];
      }
      let resData = await selectZtglWfcddjBySlid(param);
      if (resData.code == 10000) {
        this.ryDatas = resData.data.records;
        this.total = resData.data.total;
      } else {
        this.$message.error("数据获取失败！");
      }
    },
    //删除
    shanchu() {
      let that = this;
      if (this.selectlistRow != "") {
        this.$confirm("是否继续删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            let valArr = this.selectlistRow;
            // console.log("....", val);
            valArr.forEach(async (item) => {
              let data = await removeZtQsdj(item);
              if (data.code == 10000) {
                that.smzttz();
              }
              console.log("删除：", item);
              console.log("删除：", item);
            });
            let params = valArr;
            this.$message({
              message: "删除成功",
              type: "success",
            });
          })
          .catch(() => {
            this.$message("已取消删除");
          });
      } else {
        this.$message({
          message: "未选择删除记录，请选择下列列表",
          type: "warning",
        });
      }
    },
    // 人员搜索
    searchRy() {
      this.tableKey++
      this.page = 1;
      this.sendApplay();
    },
    handleCurrentChangeRy(val) {
      this.page = val;
      this.sendApplay();
    },
    //列表分页--更改每页显示个数
    handleSizeChangeRy(val) {
      this.page = 1;
      this.pageSize = val;
      this.sendApplay();
    },
    handleSelectionChange(index, row) {
      this.radioIdSelect = row;
      console.log(this.radioIdSelect);
    },
    async onfwid() {
      let params = {
        fwlx: 9,
      };
      let data = await getFwdyidByFwlx(params);
      console.log(data);
      this.fwdyid = data.data.fwdyid;
    },
    // 选择人员提交
    async submitRy() {
      this.loading = true;
      if (
        this.radioIdSelect != "" &&
        Object.keys(this.radioIdSelect).length > 0
      ) {
        this.loading = false;
        this.$router.push({
          path: "/ztqsdjTable",
          query: {
            type: "add",
            datas: this.radioIdSelect,
            fwdyid: this.fwdyid,
            slid: this.radioIdSelect.slid,
            ztid: this.radioIdSelect.ztid,
          },
        });
      } else {
        this.$message.error("请选择载体信息！");
        this.loading = false;
      }
    },

    //查询
    onSubmit() {
      this.page = 1;
      this.smzttz();
    },
    cxbm(item) {
      if (item != undefined) {
        this.cxbmsj = item.join("/");
      }
    },
    async smzttz() {
      let params = {
        ztbh: this.formInline.ztbh,
        lx: this.formInline.lx,
        smmj: this.formInline.smmj,
        jsdw: this.cxbmsj,
        yjr: this.formInline.yjr,
        page: this.page,
        pageSize: this.pageSize,
      };
      if (this.cxbmsj == "") {
        params.jsdw = this.formInline.jsdw;
      }
      if (this.formInline.qsrq != null) {
        params.kssj = this.formInline.qsrq[0];
        params.jssj = this.formInline.qsrq[1];
      }
      let resList = await selectZtQsdjPage(params);
      console.log("params", resList);
      this.smzttzList = resList.records;
      this.smzttzList.forEach((item) => {
        item.qsrq = dateFormatNYR(item.qsrq);
      });
      this.total = resList.total;
    },

    //导出
    async exportList() {
      let params = {
        ztbh: this.formInline.ztbh,
        lx: this.formInline.lx,
        smmj: this.formInline.smmj,
        jsdw: this.cxbmsj,
        yjr: this.formInline.yjr,
      };
      if (this.cxbmsj == "") {
        params.jsdw = this.formInline.jsdw;
      }
      if (this.formInline.qsrq != null) {
        params.kssj = this.formInline.qsrq[0];
        params.jssj = this.formInline.qsrq[1];
      }
      var returnData = await exportZtqsExcel(params);
      var date = new Date();
      var sj =
        date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate();
      this.dom_download(returnData, "涉密载体签收登记信息表-" + sj + ".xls");
    },

    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },

    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.qspage = val;
      this.smzttz();
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.qspage = 1;
      this.qspageSize = val;
      this.smzttz();
    },
    async smry() {
      let list = await getAllYhxx();
      this.restaurants = list;
    },
    async rydata() {
      let param = {
        bmid: this.bmm,
      };
      let list = await getAllYhxx(param);
      this.table1Data = list;
    },

    zxfw() {
      this.rydialogVisible = true;
    },
    onSubmitry() {
      this.rydata();
    },

    forsyzt(row) {
      let hxsj;
      this.sbsyqkxz.forEach((item) => {
        if (row.zt == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },
    formj(row) {
      let hxsj;
      this.sbmjxz.forEach((item) => {
        if (row.smmj == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },
    forztlx(row) {
      let hxsj;
      this.sblxxz.forEach((item) => {
        if (row.lx == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },
  },
  watch: {},
};
</script>
  
<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

/* 发起申请弹框 */
.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>> .dlFqsqContainer .input1 .el-input__inner,
>>> .dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
      display: block;
      margin-top: 10px;
      margin-bottom: 10px;
  } */

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.widthx {
  width: 8vw;
}

.cd {
  width: 191px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

.table ::-webkit-scrollbar {
  display: block !important;
  width: 8px;
  /*滚动条宽度*/
  height: 8px;
  /*滚动条高度*/
}

.table ::-webkit-scrollbar-track {
  border-radius: 10px;
  /*滚动条的背景区域的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(238, 238, 238, 0.3);
  background-color: #eeeeee;
  /*滚动条的背景颜色*/
}

.table ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /*滚动条的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.3);
  background-color: rgb(145, 143, 143);
  /*滚动条的背景颜色*/
}
</style>
  