<template>
  <div class="sec-container" v-loading="loading">
    <!-- 标题 -->
    <div class="sec-form-container">
      <el-form ref="formName" :model="tjlist" label-width="225px">
        <!-- 载体详细信息start -->
        <p class="sec-title">载体签收详细信息</p>
        <el-table border class="sec-el-table" :data="ztqsdjList"
          :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="ztmc" label="载体名称"></el-table-column>
          <el-table-column prop="xmbh" label="项目编号"></el-table-column>
          <el-table-column prop="ztbh" label="载体编号"></el-table-column>
          <el-table-column prop="lx" label="载体类型"></el-table-column>
          <el-table-column prop="smmj" label="密级"></el-table-column>
          <el-table-column prop="bmqx" label="保密期限"></el-table-column>
          <el-table-column prop="ys" label="页数/大小"></el-table-column>
          <el-table-column prop="fs" label="份数"></el-table-column>
        </el-table>
        <div class="sec-form-left">
          <el-form-item label="接收单位">
            <el-input placeholder="" v-model="tjlist.jsdw" clearable disabled></el-input>
          </el-form-item>
        </div>
        <div class="sec-form-left">
          <el-form-item label="签 收 人">
            <el-input placeholder="" :disabled="dis" v-model="tjlist.qsr" clearable></el-input>
          </el-form-item>
          <el-form-item label="签收日期">
            <el-date-picker v-model="tjlist.qsrq" :disabled="dis" style="width: 100%" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="sec-form-left">
          <el-form-item label="审批单扫描件">
            <el-input placeholder="" :disabled="dis" v-model="tjlist.wjm" clearable></el-input>
          </el-form-item>
          <!-- <el-button type="primary" :disabled="dis" @click="upload()">上传</el-button> -->
          <el-upload action="/posts" v-show="!dis" style="margin-right: 10px" :show-file-list="false" :data="{}"
            :http-request="scwj">
            <el-button slot="trigger" type="primary">上 传</el-button>
          </el-upload>
          <el-button slot="trigger" type="primary" @click="yl">预览</el-button>
          <el-dialog :visible.sync="dialogVisible_scyl">
            <img :src="scylImageUrl" alt="" style="width: 100%" />
            <div slot="footer" class="dialog-footer">
              <el-button size="small" @click="dialogVisible_scyl = false">取 消</el-button>
            </div>
          </el-dialog>
        </div>
        <!-- 载体详细信息end -->
        <!-- 底部操作按钮start -->
        <div class="sec-form-six haveBorderTop sec-footer">
          <el-button @click="returnIndex" class="fr ml10" plain>返回</el-button>
          <el-button @click="save" v-show="!dis" class="fr" type="primary">保存</el-button>
        </div>
        <!-- 底部操作按钮end -->
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getZzjgList,
  getLoginInfo,
  getFwdyidByFwlx,
  savaZtqdBatch,
} from "../../../api/index";
import { submitDjgwbg, updateDjgwbg } from "../../../api/djgwbg";
import { getZtqdListByYjlid } from "../../../api/ztjs";
import { deleteZtqdByYjlid } from "../../../api/ztwcxd";
import {
  addZtqsdj,
  updateZtqsdjByJlid,
  getJlidBySlid,
  getQsdjByJlid,
  updateZtglWfcddj,
} from "../../../api/ztqsdj";
import { dateFormatNYR } from "@/utils/moment";
import { getAllGwxx } from "../../../api/qblist";
import { getAllSmdj } from "../../../api/xlxz";
export default {
  components: {},
  props: {},
  data() {
    return {
      value1: "",
      loading: false,
      // 弹框人员选择条件
      ryChoose: {
        bm: "",
        xm: "",
      },
      gwmclist: [],
      smdjxz: [],
      regionOption: [], // 部门下拉
      page: 1, // 审批人弹框当前页
      pageSize: 10, // 审批人弹框每页条数
      radioIdSelect: "", // 审批人弹框人员单选
      ryDatas: [], // 弹框人员选择
      total: 0, // 弹框人员总数
      regionParams: {
        label: "label", //这里可以配置你们后端返回的属性
        value: "label",
        children: "childrenRegionVo",
        expandTrigger: "click",
        checkStrictly: true,
      }, //地域信息配置参数
      // table 行样式
      headerCellStyle: {
        background: "#EEF7FF",
        color: "#4D91F8",
      },
      // form表单提交数据
      tjlist: {
        xqr: "",
        szbm: "",
        wcqsrq: "",
        ztqsdjList: [],
        wjm: "",
        yt: "",
        jsdw: "",
        qsdd: "",
        mddd: "",
        fhcs: [],
        jtgj: [],
        jtlx: "",
        xdmmd: "",
        xdr: "",
        xmjl: "",
      },
      fwdyid: "",
      dis: false,
      dialogVisible_scyl: false,
      scylImageUrl: "",
      ztqsdjList: [],
      ryInfo: {},
      // 政治面貌下拉选项
      sltshow: "", // 文档的缩略图显示
      routeType: "",
      pdfBase64: "",
      fileList: [],
      dialogVisible: false,
      approvalDialogVisible: false, // 选择申请人弹框
      fileRow: "",
      disabled2: false,
      upjlid: "",
      jlid: "",
      slid: "",
      type: "",
      file: {},
      filename: "",
      sltshow: "", // 文档的缩略图显示
      fileRow: "",
      jsList: {},
      ztid: '',
    };
  },
  computed: {},
  mounted() {
    console.log(this.$route.query.list);
    this.type = this.$route.query.type;
    console.log('this.type ', this.type);

    this.fwdyid = this.$route.query.fwdyid;
    console.log("this.fwdyid", this.fwdyid);
    this.slid = this.$route.query.slid;
    this.ztid = this.$route.query.ztid;
    console.log("this.slid", this.slid);
    this.jlid = this.$route.query.jlid;
    // //审批信息初始化列表
    // this.spxxxgcc()

    
      this.jsList = JSON.parse(JSON.stringify(this.$route.query.datas));
      console.log("this.jsList", this.jsList);

    if (this.type == "add") {
      this.spxx();
    } else if (this.type == "updata") {
      this.jsList = JSON.parse(JSON.stringify(this.$route.query.datas));
      console.log("this.jsList", this.jsList);
      this.xglist();
      this.xgxx();
    } else if (this.type == "xqxx") {
      this.xglist();
      this.xgxx();
      this.dis = true;
    }
  },
  methods: {
    async xgxx() {
      let jlid = this.jlid
      console.log(jlid);
      let zt = {
        yjlid: jlid,
      };
      let ztqd = await getZtqdListByYjlid(zt);
      this.ztqsdjList = ztqd;
    },
    async xglist() {
      let jlid = this.jlid
      let data = await getQsdjByJlid({
        jlid: jlid,
      });
      this.tjlist = data;
      this.file = this.tjlist.spdsmj;
      console.log(this.file);
    },
    //审批信息
    async spxx() {
      let params = {
        slid: this.slid,
      };
      let data = await getJlidBySlid(params);
      console.log(data);
      this.upjlid = data;
      console.log(this.upjlid);
      let zt = {
        yjlid: this.upjlid,
      };
      let ztqd = await getZtqdListByYjlid(zt);
      this.ztqsdjList = ztqd;
      this.tjlist.jsdw = this.ztqsdjList[0].jsdw;
      this.file = this.tjlist.spdsmj;
      console.log(this.file);
      this.ztqsdjList.forEach((item) => {
        console.log(item);
        if (item.lx == 1) {
          item.lx = "纸介质";
        } else if (item.lx == 2) {
          item.lx = "光盘";
        } else if (item.lx == 3) {
          item.lx = "电磁介质";
        }
        if (item.smmj == 1) {
          item.smmj = "绝密";
        } else if (item.smmj == 2) {
          item.smmj = "机密";
        } else if (item.smmj == 3) {
          item.smmj = "秘密";
        } else if (item.smmj == 4) {
          item.smmj = "内部";
        }
      });
    },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    //上传文件
    scwj(item) {
      // this.file = item.file
      this.filename = item.file.name;
      // console.log("上传文件", "this.file", this.file, "this.filename", this.filename);
      this.tjlist.wjm = this.filename;
      this.sltshow = URL.createObjectURL(item.file);
      this.fileRow = item.file;
      this.blobToBase64(item.file, (dataurl) => {
        this.file = dataurl.split(",")[1];
        console.log(this.file);
      });
    },
    yl() {
      if (this.type == "add") {
        this.scylImageUrl = URL.createObjectURL(this.fileRow);
      } else {
        let zpxx;
        zpxx = this.zpzm(this.file);
        console.log(zpxx);
        this.scylImageUrl = zpxx;
        // this.scylImageUrl = this.sltshow
      }
      this.dialogVisible_scyl = true;
      // if (this.routeType == 'add') {
      //   this.scylImageUrl = URL.createObjectURL(this.fileRow)
      // } else {
      //   // this.scylImageUrl = URL.createObjectURL(this.tjlist.spdsmj)
      //   // this.scylImageUrl = this.sltshow
      //   let zpxx
      //   zpxx = this.zpzm(this.tjlist.spdsmj)
      //   this.scylImageUrl = zpxx
      // }
      // this.dialogVisible_scyl = true
    },
    zpzm(zp) {
      const iamgeBase64 = "data:image/jpeg;base64," + zp;
      let zpxx;
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          // let that = this;

          function previwImg(item) {
            zpxx = item;
          }
          previwImg(iamgeBase64);
        }
      }
      return zpxx;
    },
    // 保存
    async save() {
      console.log(this.tjlist.qsrq);
      // let params = new FormData();
      let params = {};
      params.wjm = this.tjlist.wjm;
      params.jsdw = this.tjlist.jsdw;
      params.qsr = this.tjlist.qsr;
      params.ztid = this.ztid;
      // params.append('spdsmj', this.file)
      // params.append('wjm', this.tjlist.wjm)
      // params.append('jsdw', this.tjlist.jsdw);
      // params.append('qsr', this.tjlist.jsdw);
      this.ztqsdjList.forEach((item) => {
        if (item.lx == "纸介质") {
          item.lx = 1;
        } else if (item.lx == "光盘") {
          item.lx = 2;
        } else if (item.lx == "电磁介质") {
          item.lx = 3;
        }
        if (item.smmj == "绝密") {
          item.smmj = 1;
        } else if (item.smmj == "机密") {
          item.smmj = 2;
        } else if (item.smmj == "秘密") {
          item.smmj = 3;
        } else if (item.smmj == "内部") {
          item.smmj = 4;
        }
      });
      if (this.type == "add") {
        let wfqsrq = dateFormatNYR(this.jsList.wfqsrq);
        let wfjzrq = dateFormatNYR(this.jsList.wfjzrq);
        let qsrq = dateFormatNYR(this.tjlist.qsrq);

        console.log(wfqsrq);
        console.log(wfjzrq);
        params.wfqsrq = wfqsrq;
        params.wfjzrq = wfjzrq;
        params.spdsmj = this.file;
        params.xqr = this.jsList.xqr;
        params.szbm = this.jsList.szbm;
        params.yt = this.jsList.yt;
        params.zfdw = this.jsList.zfdw;
        params.qsdd = this.jsList.qsdd;
        params.mddd = this.jsList.mddd;
        params.fhcs = this.jsList.fhcs;
        params.jtgj = this.jsList.jtgj;
        params.jtxl = this.jsList.jtxl;
        params.yjr = this.jsList.yjr;
        params.cdr = this.jsList.cdr;
        params.xmjl = this.jsList.xmjl;
        params.qsrq = qsrq;
        let data = await addZtqsdj(params);
        if (data.code == 10000) {
          let sfqsdj = {
            sfqs: 1,
            jlid: this.jsList.jlid,
          };
          let data1 = await updateZtglWfcddj(sfqsdj);
          if (data1.code == 10000) {
            this.ztqsdjList.forEach((item) => {
              item.splx = 9;
              item.yjlid = data.data;
            });
            let sava = await savaZtqdBatch(this.ztqsdjList);
            if (sava.code == 10000) {
              this.$router.push("/smztqsdj");
              this.$message({
                message: "保存成功",
                type: "success",
              });
            }
          }
        }
      } else if (this.type == "updata") {
        params = this.tjlist;
        params.spdsmj = this.file;
        params.jlid = this.jlid;
        let resDatas = await updateZtqsdjByJlid(params);
        if (resDatas.code == 10000) {
          this.ztqsdjList.forEach((item) => {
            item.splx = 9;
            item.yjlid = resDatas.data;
          });
          let del = await deleteZtqdByYjlid({ yjlid: this.tjlist.jlid });
          if (del.code == 10000) {
            let data = await savaZtqdBatch(this.ztqsdjList);
            if (data.code == 10000) {
              this.$router.push("/smztqsdj");
              this.$message({
                message: "保存成功",
                type: "success",
              });
            }
          }
        }
      }
    },
    // 返回
    returnIndex() {
      this.$router.push("/smztqsdj");
    },
  },
  watch: {},
};
</script>

<style scoped>
.sec-container {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: overlay;
}

.sec-title {
  border-left: 5px solid #1b72d8;
  color: #1b72d8;
  font-size: 20px;
  font-weight: 700;
  text-indent: 10px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.sec-form-container {
  width: 100%;
  height: 100%;
}

.sec-form-left {
  /* width: 100%; */
  border: 1px solid #cdd2d9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
}

.sec-form-left:not(:first-child) {
  border-top: 0;
}

.sec-form-left .el-form-item {
  float: left;
  width: 100%;
}

.sec-header-section {
  width: 100%;
  position: relative;
}

.sec-header-pic {
  width: 258px;
  position: absolute;
  right: 0px;
  top: 0;
  height: 163px;
  border: 1px solid #cdd2d9;
  border-left: 0;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sec-form-second {
  /* width: 100%; */
  border: 1px solid #cdd2d9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
  border-top: 0;
}

.sec-form-third {
  border: 1px solid #cdd2d9;
  /* height: 40px;  */
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

/deep/.el-checkbox-group {
  display: flex;
  justify-content: center;
  background-color: #f5f7fa;
  border-right: 1px solid #cdd2d9;
}

.checkbox {
  display: inline-block !important;
  background-color: rgba(255, 255, 255, 0) !important;
  border-right: none !important;
}

.sec-form-four {
  border: 1px solid #cdd2d9;
  height: auto;
  min-height: 100px;
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-five {
  border: 1px solid #cdd2d9;
  height: auto;
  min-height: 100px;
  overflow: hidden;
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.yulan {
  text-align: center;
  cursor: pointer;
  color: #3874d5;
  font-weight: 600;
  float: left;
  margin-left: 10px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 2px solid #ebebeb;
}

.sec-form-six {
  border: 1px solid #cdd2d9;
  overflow: hidden;
  background: #ffffff;
  padding: 10px;
}

.ml10 {
  margin-left: 10px;
}

.sec-footer {
  margin-top: 10px;
  border-right: 1px solid #cdd2d9;
  background: #ffffff;
}

.sec-form-left-textarea {
  height: 54px !important;
}

.sec-form-left-textarea>>>.el-form-item__label {
  line-height: 54px !important;
}

>>>.sec-form-four .el-textarea__inner {
  border: none;
}

.sec-left-text {
  float: left;
  margin-right: 130px;
}

.haveBorderTop {
  border-top: 1px solid #cdd2d9;
}

>>>.longLabel .el-form-item__label {
  width: 500px !important;
}

>>>.longLabel .el-form-item__content {
  margin-left: 500px !important;
  padding-left: 20px;
  border-right: 1px solid #cdd2d9;
  background: #ffffff;
}

/* .sec-form-second:not(:first-child){
  border-top: 0;
} */
.sec-form-second .el-form-item {
  float: left;
  width: 100%;
}

.sec-el-table {
  width: 100%;
  border: 1px solid #ebeef5;
  height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
  padding-left: 15px;
  background-color: #f5f7fa;
  width: calc(100% - 16px);
  border-right: 1px solid #cdd2d9;
  color: #c0c4cc;
}

>>>.sec-el-table .el-input__inner {
  border: none !important;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
  width: 200px;
  text-align: center;
  font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
  border: none;
  border-right: 1px solid #cdd2d9;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item {
  margin-bottom: 0px;
}

/* >>>.el-form > div {
  border: 1px solid #CDD2D9;;
} */
>>>.el-form-item__label {
  border-right: 1px solid #cdd2d9;
}

/* /deep/.sec-form-container .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
} */
.riq {
  width: 100% !important;
}

.widthw {
  width: 6vw;
}

.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}
</style>
