<template>
  <div>
    <hsoft_top_title>
      <template #left>注册信息维护</template>
    </hsoft_top_title>
    <!---->
    <div class="out-card">
      <div class="out-card-div dwxx">
        <div @mouseenter="divMouseEnter('dwmcEdit')" @mouseleave="divMouseLeave('dwmcEdit')">
          <label>单位名称</label>
          <div class="article">
            <span v-show="dwxx.dwmcEdit == 0 || dwxx.dwmcEdit == 1">{{ dwxx.dwmc }}</span>
            <el-input v-show="dwxx.dwmcEdit == 2" v-model="dwxx.dwmc" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwmcEdit == 1" @click="changeFlag('dwmcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwmcEdit == 2" @click="changeDwxx('dwmcEdit', 'dwmc')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwzchEdit')" @mouseleave="divMouseLeave('dwzchEdit')">
          <label>单位注册号</label>
          <div class="article">
            <span v-show="dwxx.dwzchEdit == 0 || dwxx.dwzchEdit == 1">{{ dwxx.dwzch }}</span>
            <el-input v-show="dwxx.dwzchEdit == 2" v-model="dwxx.dwzch" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwzchEdit == 1" @click="changeFlag('dwzchEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwzchEdit == 2" @click="changeDwxx('dwzchEdit', 'dwzch')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('tydmEdit')" @mouseleave="divMouseLeave('tydmEdit')">
          <label>社会信用统一代码</label>
          <div class="article">
            <span v-show="dwxx.tydmEdit == 0 || dwxx.tydmEdit == 1">{{ dwxx.tydm }}</span>
            <el-input v-show="dwxx.tydmEdit == 2" v-model="dwxx.tydm" size="mini"></el-input>
          </div>
          <i v-show="dwxx.tydmEdit == 1" @click="changeFlag('tydmEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.tydmEdit == 2" @click="changeDwxx('tydmEdit', 'tydm')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlxmcEdit')" @mouseleave="divMouseLeave('dwlxmcEdit')">
          <label>单位类型</label>
          <div class="article">
            <span v-show="dwxx.dwlxmcEdit == 0 || dwxx.dwlxmcEdit == 1">{{ dwxx.dwlx }}</span>
            <el-select v-show="dwxx.dwlxmcEdit == 2" v-model="dwxx.dwlx" size="mini">
              <el-option v-for="(item, index) in dwlxList" :key="index" :value="item.csm" :label="item.csm"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.dwlxmcEdit == 1" @click="changeFlag('dwlxmcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlxmcEdit == 2" @click="changeDwxx('dwlxmcEdit', 'dwlx', dwxx)" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlx2mcEdit')" @mouseleave="divMouseLeave('dwlx2mcEdit')">
          <label>单位级别</label>
          <div class="article">
            <span v-show="dwxx.dwlx2mcEdit == 0 || dwxx.dwlx2mcEdit == 1">{{ dwxx.dwlx2 }}</span>
            <el-select v-show="dwxx.dwlx2mcEdit == 2" v-model="dwxx.dwlx2" size="mini">
              <el-option v-for="(item, index) in dwjbList" :key="index" :value="item.csm" :label="item.csm"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.dwlx2mcEdit == 1" @click="changeFlag('dwlx2mcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlx2mcEdit == 2" @click="changeDwxx('dwlx2mcEdit', 'dwlx2', dwxx)" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('sslymcEdit')" @mouseleave="divMouseLeave('sslymcEdit')">
          <label>所属领域</label>
          <div class="article">
            <span v-show="dwxx.sslymcEdit == 0 || dwxx.sslymcEdit == 1">{{ dwxx.ssly }}</span>
            <el-select v-show="dwxx.sslymcEdit == 2" v-model="dwxx.ssly" size="mini">
              <el-option v-for="(item, index) in sslyList" :key="index" :value="item.csm" :label="item.csm"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.sslymcEdit == 1" @click="changeFlag('sslymcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.sslymcEdit == 2" @click="changeDwxx('sslymcEdit', 'ssly')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('ssccmcEdit')" @mouseleave="divMouseLeave('ssccmcEdit')">
          <label>所属层次</label>
          <div class="article">
            <span v-show="dwxx.ssccmcEdit == 0 || dwxx.ssccmcEdit == 1">{{ dwxx.sscj }}</span>
            <el-select v-show="dwxx.ssccmcEdit == 2" v-model="dwxx.sscj" size="mini">
              <el-option v-for="(item, index) in ssccList" :key="index" :value="item.csz" :label="item.csm"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.ssccmcEdit == 1" @click="changeFlag('ssccmcEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.ssccmcEdit == 2" @click="changeDwxx('ssccmcEdit', 'sscj')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlxrEdit')" @mouseleave="divMouseLeave('dwlxrEdit')">
          <label>单位联系人</label>
          <div class="article">
            <span v-show="dwxx.dwlxrEdit == 0 || dwxx.dwlxrEdit == 1">{{ dwxx.lxr }}</span>
            <el-input v-show="dwxx.dwlxrEdit == 2" v-model="dwxx.lxr" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwlxrEdit == 1" @click="changeFlag('dwlxrEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlxrEdit == 2" @click="changeDwxx('dwlxrEdit', 'lxr')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlxdhEdit')" @mouseleave="divMouseLeave('dwlxdhEdit')">
          <label>单位联系电话号码</label>
          <div class="article">
            <span v-show="dwxx.dwlxdhEdit == 0 || dwxx.dwlxdhEdit == 1">{{ dwxx.lxdh }}</span>
            <el-input v-show="dwxx.dwlxdhEdit == 2" v-model="dwxx.lxdh" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwlxdhEdit == 1" @click="changeFlag('dwlxdhEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlxdhEdit == 2" @click="changeDwxx('dwlxdhEdit', 'lxdh')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('dwlxyxEdit')" @mouseleave="divMouseLeave('dwlxyxEdit')">
          <label>单位联系邮箱</label>
          <div class="article">
            <span v-show="dwxx.dwlxyxEdit == 0 || dwxx.dwlxyxEdit == 1">{{ dwxx.lxyx }}</span>
            <el-input v-show="dwxx.dwlxyxEdit == 2" v-model="dwxx.lxyx" size="mini"></el-input>
          </div>
          <i v-show="dwxx.dwlxyxEdit == 1" @click="changeFlag('dwlxyxEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.dwlxyxEdit == 2" @click="changeDwxx('dwlxyxEdit', 'lxyx')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
        <div @mouseenter="divMouseEnter('ssqhEdit')" @mouseleave="divMouseLeave('ssqhEdit')">
          <label>单位所在省市区</label>
          <div class="article">
            <span v-show="dwxx.ssqhEdit == 0 || dwxx.ssqhEdit == 1">{{ dwxx.province }}/{{ dwxx.city }}/{{ dwxx.district
              }}</span>
            <el-select v-show="dwxx.ssqhEdit == 2" v-model="dwxx.province" @change="provinceChanged" size="mini"
              style="width: 32%;">
              <el-option v-for="(item, index) in provinceList" :key="'province' + index" :label="item.name"
                :value="item.name"></el-option>
            </el-select>
            <el-select v-show="dwxx.ssqhEdit == 2" v-model="dwxx.city" @change="cityChanged" size="mini"
              style="width: 32%;">
              <el-option v-for="(item, index) in cityList" :key="'city' + index" :label="item.name"
                :value="item.name"></el-option>
            </el-select>
            <el-select v-show="dwxx.ssqhEdit == 2" v-model="dwxx.district" @change="districtChanged" size="mini"
              style="width: 32%;">
              <el-option v-for="(item, index) in districtList" :key="'district' + index" :label="item.name"
                :value="item.name"></el-option>
            </el-select>
          </div>
          <i v-show="dwxx.ssqhEdit == 1" @click="changeFlag('ssqhEdit')" class="el-icon-edit"></i>
          <i v-show="dwxx.ssqhEdit == 2" @click="changeDwxx('ssqhEdit', '')" class="el-icon-check"
            style="color: #67C23A;"></i>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import { getWindowLocation, setWindowLocation } from '../../../utils/windowLocation'

import { dateFormatChinese } from '../../../utils/moment'

import {
  updateRegisteInfo,
  getDwxx,
  getAllDwlx,
  getAllDwjb,
  getAllSsly,
  getAllSscj,
  getProvinceList,
  getCityByProvincecode,
  getAreaByCitycode
} from '../../../api/dwzc'
// import { writeSystemOptionsLog } from '../../../utils/logUtils'

// // 省市区信息
// import address from '../../../utils/address.json'

// // 单位类型
// import { getDmbDwlxDB } from '../../../db/dmbDwlxDb'
// // 单位所属领域
// import { getDmbSslyDB } from '../../../db/dmbSslyDb'
// // 单位所属层次
// import { getDmbSsccDB } from '../../../db/dmbSsccDb'
// // 单位信息
// import { updateDwxx } from '../../../db/dwxxDb'

export default {
  data() {
    return {
      // 单位信息
      dwxx: {},
      dwlxList: [],
      dwjbList: [],
      sslyList: [],
      ssccList: [],
      // 省集合
      provinceList: [],
      provincecode: '',
      // 市集合
      cityList: [],
      citycode: '',
      // 区集合
      districtList: [],
      districtcode: '',
    }
  },
  components: {
    hsoft_top_title
  },
  methods: {
    // 格式化时间
    formatTime(time) {
      return dateFormatChinese(new Date(time))
    },
    // // 通过ID获取所属层次
    // async getSsccBySsccId () {
    //   this.ssccList = await getAllSscj()
    // },
    // // 通过ID获取所属领域
    // async getSslyBySslyId () {
    //   this.sslyList = await getAllSsly()
    // },
    // // 通过ID获取单位类型
    // async getDwlxByDwlxId () {
    //   this.dwlxList = await getAllDwlx()
    // },
    // 通过ID获取所属领域
    // 通过ID获取所属层次
    // 获取单位信息
    async getDwxx() {
      let loginUserDwxx = await getDwxx()

      console.log(loginUserDwxx)
      // // localstore中获取登录单位的信息
      // let loginUserDwxx = getWindowLocation()
      console.log('loginUserDwxx', loginUserDwxx, Object.prototype.toString.call(loginUserDwxx))
      if (!loginUserDwxx) {
        this.$message.error('单位信息获取失败')
        return
      }
      try {
        if (Object.prototype.toString.call(loginUserDwxx) == '[object Object]') {
          // 什么也不用做
        } else {
          loginUserDwxx = JSON.parse(loginUserDwxx)
        }
      } catch (error) {
        this.$message.error('单位信息获取解析失败')
        return
      }
      // 获取单位类型名称
      let dwlxObj = this.dwlxList
      if (dwlxObj) {
        dwlxObj.forEach((item) => {
          if (item.csz == loginUserDwxx.dwlx) {
            console.log(item);
            loginUserDwxx.dwlx = item.csm
            console.log(loginUserDwxx.dwlx,'111111111111111111111111');
          }
        })
      }
      // 获取单位级别名称
      let dwjbObj = this.dwjbList
      if (dwjbObj) {
        dwjbObj.forEach((item) => {
          if (item.csz == loginUserDwxx.dwlx2) {
            console.log(item);
            loginUserDwxx.dwlx2 = item.csm
            console.log(loginUserDwxx.dwlx2);
          }
        })
      }
      // // // 获取所属领域名称
      let sslyObj = this.sslyList
      if (sslyObj) {
        sslyObj.forEach((item) => {
          if (item.csz == loginUserDwxx.ssly) {
            console.log(item);
            loginUserDwxx.ssly = item.csm
            console.log(loginUserDwxx.ssly);
          }
        })
      }
      // // // 获取所属层次名称
      let ssccObj = this.ssccList
      if (ssccObj) {
        ssccObj.forEach((item) => {
          if (item.csz == loginUserDwxx.sscj) {
            console.log(item);
            loginUserDwxx.sscj = item.csm
            console.log(loginUserDwxx.sscj);
          }
        })
      }
      this.initSsq()
      this.setFlagDefault(loginUserDwxx)
      this.dwxx = loginUserDwxx
      // this.dwxx.province = loginUserDwxx.szsid
    },
    divMouseEnter(target) {
      if (this.dwxx[target] == 0) {
        this.dwxx[target] = 1
      }
    },
    divMouseLeave(target) {
      if (this.dwxx[target] == 1) {
        this.dwxx[target] = 0
      }
    },
    changeFlag(target) {
      this.dwxx[target] = 2
    },
    async changeDwxx(target, field, dwxx) {
      console.log(this.dwxx.dwlx)
      console.log('target', target);
      if (target == 'dwlxdhEdit') {
        /* 手机号校验规则*/
        const telCheck = /^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/
        console.log(telCheck.test(this.dwxx.lxdh));
        if (telCheck.test(this.dwxx.lxdh) == false) {
          this.$message.warning('手机号码格式不正确！')
          return telCheck.test(this.dwxx.lxdh)
        }
      }
      if (target == 'dwlxyxEdit') {
        /* 邮箱校验规则*/
        const emailCheck = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
        console.log(emailCheck.test(this.dwxx.lxyx));
        if (emailCheck.test(this.dwxx.lxyx) == false) {
          this.$message.warning('邮箱格式不正确！')
          return emailCheck.test(this.dwxx.lxyx)
        }
      }
      // let dwid = this.dwxx.dwid
      // if (!dwid) {
      //   this.$message.warning('单位ID为空')
      //   return
      // }
      let params = {}
      if (target == 'ssccmcEdit') {
        params = {
          dwid: this.dwxx.dwid,
          dwmc: this.dwxx.dwmc,
          dwzch: this.dwxx.dwzch,
          tydm: this.dwxx.tydm,
          dwlx: this.dwxx.dwlx,
          dwjb: this.dwxx.dwlx2,
          ssly: this.dwxx.ssly,
          sscj: this.dwxx.sscj,
          lxr: this.dwxx.lxr,
          lxdh: this.dwxx.lxdh,
          lxyx: this.dwxx.lxyx,
        }
      }
      if (target != 'ssccmcEdit') {
        params = {
          dwid: this.dwxx.dwid,
          dwmc: this.dwxx.dwmc,
          dwzch: this.dwxx.dwzch,
          tydm: this.dwxx.tydm,
          dwlx: this.dwxx.dwlx,
          dwjb: this.dwxx.dwlx2,
          ssly: this.dwxx.ssly,
          // sscj: this.dwxx.sscj,
          lxr: this.dwxx.lxr,
          lxdh: this.dwxx.lxdh,
          lxyx: this.dwxx.lxyx,
        }
      }
      // 省市区特殊处理
      if (target == 'ssqhEdit') {
        // 省
        params.province = this.dwxx.province
        params.szsid = this.provincecode
        // 市
        params.city = this.dwxx.city
        params.szsdid = this.citycode
        // 区
        params.district = this.dwxx.district
        params.szqxid = this.districtcode
        // // 区划
        // params.regionalNumber = this.dwxx.regionalNumber
        // 数据校验
        if (!params.province || params.province == '') {
          this.$message.warning('单位所在省市区[省]未选择')
          return
        }
        if (!params.city || params.city == '') {
          this.$message.warning('单位所在省市区[市]未选择')
          return
        }
        if (!params.district || params.district == '') {
          this.$message.warning('单位所在省市区[区]未选择')
          return
        }
        // if (!params.regionalNumber || params.regionalNumber == '') {
        //   this.$message.warning('单位所在省市区[区]未选择，所属区划为空')
        //   return
        // }
      } else {
        params[field] = this.dwxx[field]
      }
      console.log('params', params)
      let res = await updateRegisteInfo(params)
      this.getDwxx()
      // // 写入日志
      // let logParams = {
      //   xyybs: 'yybs_zcxx',
      //   ymngnmc: '注册信息维护',
      //   extraParams: params
      // }
      // writeSystemOptionsLog(logParams)
      this.dwxx[target] = 0
      // 刷新缓存数据
      // this.refreshWindowLocation(params)
      // this.getDwxx()
    },
    // 设置显隐控制标记默认值（防止undefined导致双向绑定失效）
    setFlagDefault(obj) {
      obj.dwmcEdit = 0
      obj.dwzchEdit = 0
      obj.tydmEdit = 0
      obj.dwlxmcEdit = 0
      obj.dwlx2mcEdit = 0
      obj.sslymcEdit = 0
      obj.ssccmcEdit = 0
      obj.dwlxrEdit = 0
      obj.dwlxdhEdit = 0
      obj.dwlxyxEdit = 0
      obj.ssqhEdit = 0
    },
    // 获取所有单位类型
    async getAllDwlx() {
      this.dwlxList = await getAllDwlx()
    },
    // 获取所有单位名称
    async getAllDwjb() {
      this.dwjbList = await getAllDwjb()
    },
    // 获取所有所属领域
    async getAllSsly() {
      this.sslyList = await getAllSsly()
    },
    // 获取所有所属层次
    async getAllSscc() {
      this.ssccList = await getAllSscj()
    },
    // 初始化省市区数据
    async initSsq() {
      //省级
      this.provinceList = await getProvinceList();
      console.log('全国各个省份：', this.provinceList);
      this.provinceList.forEach((item, index) => {
        if (this.dwxx.szsid == item.code) {
          console.log('省份item', item);
          this.dwxx.province = item.name
          this.provincecode = item.code
        }
      })
      //市级
      let cityparam = {
        provincecode: this.provincecode
      }
      this.cityList = await getCityByProvincecode(cityparam)
      console.log('各个省份下属地级市：', this.cityList);
      this.cityList.forEach((item, index) => {
        if (this.dwxx.szsdid == item.code) {
          console.log('地级市item', item);
          this.dwxx.city = item.name
          this.citycode = item.code
        }
      })
      //区级
      let districtparam = {
        citycode: this.citycode
      }
      this.districtList = await getAreaByCitycode(districtparam)
      console.log('地级市下属市区：', this.districtList);
      this.districtList.forEach((item, index) => {
        if (this.dwxx.szqxid == item.code) {
          console.log('市区item', item);
          this.dwxx.district = item.name
          this.districtcode = item.code
        }
      })
    },
    // 省改变事件
    async provinceChanged(province) {
      console.log(province);
      this.provinceList = await getProvinceList();
      console.log('全国各个省份：', this.provinceList);
      this.provinceList.forEach((item, index) => {
        if (item.name == province) {
          console.log('省份item', item);
          this.dwxx.province = item.name
          this.provincecode = item.code
          console.log(this.provincecode);
        }
      })
      // 重置区
      this.districtList = []
      // 重置区划
      this.dwxx.regionalNumber = ''
      // 重置市区数据
      this.dwxx.city = ''
      this.dwxx.district = ''
      this.cityChanged()
    },
    // 市改变事件
    async cityChanged(city) {
      // // 重新初始化区
      //市级
      let cityparam = {
        provincecode: this.provincecode
      }
      this.cityList = await getCityByProvincecode(cityparam)
      console.log('各个省份下属地级市：', this.cityList);
      this.cityList.forEach((item, index) => {
        if (city == item.name) {
          console.log('地级市item', item);
          this.dwxx.city = item.name
          this.citycode = item.code
        }
      })
      // 重置区划
      this.dwxx.regionalNumber = ''
      // 重置区数据
      this.dwxx.district = ''
      this.districtChanged()
    },
    // 区改变事件
    async districtChanged(district) {
      //区级
      let districtparam = {
        citycode: this.citycode
      }
      this.districtList = await getAreaByCitycode(districtparam)
      console.log('地级市下属市区：', this.districtList);
      this.districtList.forEach((item, index) => {
        if (district == item.name) {
          console.log('市区item', item);
          this.dwxx.district = item.name
          this.districtcode = item.code
        }
      })
    },
    // 刷新缓存数据
    refreshWindowLocation(params) {
      let localObj = getWindowLocation()
      console.log('localObj', localObj)
      Object.keys(params).forEach(item => {
        localObj[item] = params[item]
      })
      setWindowLocation(localObj)
    }
  },
  mounted() {
    // 获取单位信息
    this.getDwxx()
    //
    this.getAllDwlx()
    this.getAllDwjb()
    this.getAllSsly()
    this.getAllSscc()
    // 初始化省市区数据
    this.initSsq()
  }
}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
  text-align: center;
}

/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
  width: 60%;
  margin: 0 auto;
}

.out-card .out-card-div>div {
  padding: 10px 5px;
  display: flex;
  cursor: pointer;
}

.out-card .dwxx div:hover {
  /* background: #f4f4f5; */
  /* background: rgba(255, 255, 255, 0.6); */
  border-radius: 8px;
}

.out-card .dwxx div label {
  /* background-color: red; */
  /* width: 125px; */
  width: 50%;
  height: 28px;
  line-height: 28px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}

.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  /* flex: 1; */
  display: inline-block;
  height: 28px;
  line-height: 28px;
}

.out-card .dwxx div .article {
  padding-left: 20px;
  display: inline-block;
}

.out-card .dwxx div i {
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  cursor: pointer;
  color: #409eff;
  font-size: 16px;
}
</style>