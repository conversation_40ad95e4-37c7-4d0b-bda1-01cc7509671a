<template>
  <div>
    <QRCode :value="qrValue" :options="qrOptions" />
  </div>
</template>

<script>
import QRCode from 'qrcode.vue';

export default {
  components: {
    QRCode
  },
  data() {
    return {
      qrValue: 'https://www.example.com',
      qrOptions: {
        size: 300,
        margin: 20,
        color: {
          dark: '#333', // 深色部分颜色
          light: '#ffcc00' // 浅色部分颜色
        },
        errorCorrectionLevel: 'H' // 高级错误纠正级别
      }
    };
  }
};
</script>
