<template>
    <div class="sec-container">
        <el-tabs v-model="activeName">
            <el-tab-pane label="审批指南" name="first">
                <div class="sec-form-six haveBorderTop sec-footer">
                    <el-button @click="ljbl" class="fr" type="success">立即办理</el-button>
                </div>
                <el-table border class="sec-el-table" :data="spznList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                    <el-table-column prop="hjmc" label="办理流程"></el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="审批信息" name="second">
                <!-- 标题 -->
                <p class="sec-title-big">涉密人员保密审查表</p>
                <p class="sec-title">基本信息</p>
                <div class="sec-form-container">
                    <el-form ref="formName" :model="tjlist" label-width="225px">
                        <!-- 第一部分包括姓名到常住地公安start -->
                        <div class="sec-header-section">
                            <div class="sec-form-left">
                                <el-form-item label="姓名" prop="xm">
                                    <el-input placeholder="" v-model="tjlist.xm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="性别" prop="xb">
                                    <el-input placeholder="" v-model="tjlist.xb" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="国籍" prop="gj">
                                    <el-input placeholder="" v-model="tjlist.gj" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="曾用名" prop="cym">
                                    <el-input placeholder="" v-model="tjlist.cym" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="民族" prop="mz">
                                    <el-input placeholder="" v-model="tjlist.mz" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="婚姻状况" prop="hyzk">
                                    <el-input placeholder="" v-model="tjlist.hyzk" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="政治面貌" prop="zzmm">
                                    <el-input placeholder="" v-model="tjlist.zzmm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="联系电话" prop="lxdh">
                                    <el-input placeholder="" v-model="tjlist.lxdh" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="身份证号" prop="sfzhm">
                                    <el-input placeholder="" v-model="tjlist.sfzhm" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="户籍地址" prop="hjdz">
                                    <el-input placeholder="（详细）" v-model="tjlist.hjdz" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="户籍地公安机关" prop="hjgajg">
                                    <el-input placeholder="街道派出所" v-model="tjlist.hjgajg" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="常住地址" prop="czdz">
                                    <el-input placeholder="（详细）" v-model="tjlist.czdz" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="常住地公安机关" prop="czgajg">
                                    <el-input placeholder="街道派出所" v-model="tjlist.czgajg" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <!-- 电子照片 -->
                            <div class="sec-header-pic sec-header-flex">
                                <img v-if="imageUrl" :src="imageUrl" class="avatar" style="">
                            </div>
                        </div>
                        <!-- 第一部分包括姓名到常住地公安end -->
                        <!-- 部门及职务、职称到涉密等级start -->
                        <div class="sec-form-second">
                            <el-form-item label="部门及职务、职称" prop="jbzc">
                                <!-- <el-input placeholder="" v-model="tjlist.jbzc" clearable disabled></el-input> -->
                                <template slot-scope="scope">
                                    <p class="hyzk">
                                        <span v-if="tjlist.bmmc">部门：</span>
                                        <span v-if="tjlist.bmmc">{{ tjlist.bmmc }}、</span>
                                        <span v-if="tjlist.zw">职务：</span>
                                        <span v-if="tjlist.zw">{{ tjlist.zw }}、</span>
                                        <span v-if="tjlist.jbzc != undefined && tjlist.jbzc != ''">职称：</span>
                                        <span v-if="tjlist.jbzc == 1">省部级</span> <span v-if="tjlist.jbzc == 2">厅局级</span>
                                        <span v-if="tjlist.jbzc == 3">县处级</span> <span v-if="tjlist.jbzc == 4">乡科级及以下</span>
                                        <span v-if="tjlist.jbzc == 5">高级(含正高、副高)</span> <span
                                            v-if="tjlist.jbzc == 6">中级</span> <span v-if="tjlist.jbzc == 7">初级及以下</span>
                                        <span v-if="tjlist.jbzc == 8">试用期人员</span>
                                        <span v-if="tjlist.jbzc == 9">工勤人员</span> <span v-if="tjlist.jbzc == 10">企业职员</span>
                                        <span v-if="tjlist.jbzc == 11">其他</span>
                                    </p>
                                </template>
                            </el-form-item>
                        </div>
                        <div class="sec-form-second">
                            <el-form-item label="已（拟）任涉密岗位" prop="gwmc">
                                <el-input placeholder="" v-model="tjlist.gwmc" clearable disabled></el-input>
                            </el-form-item>
                            <el-form-item label="涉密等级" prop="smdj">
                                <el-input placeholder="" v-model="tjlist.smdj" clearable disabled></el-input>
                            </el-form-item>
                        </div>
                        <!-- 部门及职务、职称到涉密等级end -->
                        <!-- 主要学习及工作经历start -->
                        <p class="sec-title">主要学习及工作经历</p>
                        <el-table border class="sec-el-table" :data="tjlist.xxjlList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="qssj" label="起始日期"></el-table-column>
                            <el-table-column prop="zzsj" label="终止日期"></el-table-column>
                            <el-table-column prop="szdw" label="主要学习经历、工作单位"></el-table-column>
                            <el-table-column prop="zw" label="任职情况"></el-table-column>
                            <el-table-column prop="zmr" label="证明人"></el-table-column>
                        </el-table>
                        <!-- 主要学习及工作经历end -->
                        <!-- 家庭成员及主要社会关系情况start -->
                        <p class="sec-title">家庭成员及主要社会关系情况</p>
                        <el-table border class="sec-el-table" :data="tjlist.cyjshgxList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="gxms" label="与本人关系"></el-table-column>
                            <el-table-column prop="xm" label="姓名"> </el-table-column>
                            <el-table-column prop="jwjlqk" label="是否有外籍、境外居留权、长期居留许可"></el-table-column>
                            <el-table-column prop="cgszd" label="单位"> </el-table-column>
                            <el-table-column prop="zw" label="职务"> </el-table-column>
                            <el-table-column prop="zzmm" label="政治面貌"></el-table-column>
                        </el-table>
                        <!-- 家庭成员及主要社会关系情况end -->
                        <!-- 移居国(境)外情况start -->
                        <p class="sec-title">移居国(境)外情况</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="拥有外籍、境外永久居留权或者长期居留许可情况" prop="cqjlxkqk">
                                <el-radio v-model="tjlist.sfcrj" v-for="item in yjgwqk" :label="item.id" :key="item.id"
                                    disabled>{{ item.yw }}</el-radio>
                            </el-form-item>
                        </div>
                        <!-- 移居国(境)外情况end -->
                        <!-- 持有因公出入境证件情况start -->
                        <p class="sec-title">持有因公出入境证件情况</p>
                        <el-table border class="sec-el-table" :data="tjlist.ygrjzjqkList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="fjlb" label="证件名称"></el-table-column>
                            <el-table-column prop="cyqk" label="持有情况"></el-table-column>
                            <el-table-column prop="zjhm" label="证件号码"></el-table-column>
                            <el-table-column prop="yxq" label="有效期"></el-table-column>
                        </el-table>
                        <!-- 持有因公出入境证件情况end -->
                        <!-- 持有因私出入境证件情况start -->
                        <p class="sec-title">持有因私出入境证件情况</p>
                        <el-table border class="sec-el-table" :data="tjlist.ysrjzjqkList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="fjlb" label="证件名称"></el-table-column>
                            <el-table-column prop="cyqk" label="持有情况"></el-table-column>
                            <el-table-column prop="zjhm" label="证件号码"></el-table-column>
                            <el-table-column prop="yxq" label="有效期"></el-table-column>
                        </el-table>
                        <!-- 持有因私出入境证件情况end -->
                        <!-- 因私出国(境)情况start -->
                        <p class="sec-title">因私出国(境)情况</p>
                        <el-table border class="sec-el-table" :data="tjlist.yscgqkList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="qssj" label="起始日期"></el-table-column>
                            <el-table-column prop="zzsj" label="终止日期"></el-table-column>
                            <el-table-column prop="cggj" label="近3年所到国家或地区"> </el-table-column>
                            <el-table-column prop="sy" label="事由"></el-table-column>
                        </el-table>
                        <!-- 因私出国(境)情况end -->
                        <!-- 接受境外资助情况start -->
                        <p class="sec-title">接受境外资助情况</p>
                        <el-table border class="sec-el-table" :data="tjlist.jsjwzzqkList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="zzsj" label="起始日期"></el-table-column>
                            <el-table-column prop="gj" label="国家地区"></el-table-column>
                            <el-table-column prop="jgmc" label="机构名称"></el-table-column>
                            <el-table-column prop="zznr" label="资助内容"></el-table-column>
                        </el-table>
                        <!-- 接受境外资助情况end -->
                        <!-- 处分或者违法犯罪情况start -->
                        <p class="sec-title">处分或者违法犯罪情况</p>
                        <el-table border class="sec-el-table" :data="tjlist.clhwffzqkList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="cfsj" label="起始日期"></el-table-column>
                            <el-table-column prop="cfjg" label="处理结果"> </el-table-column>
                            <el-table-column prop="cfyy" label="处理原因"></el-table-column>
                            <el-table-column prop="cfdw" label="处理机构"> </el-table-column>
                        </el-table>
                        <!-- 处分或者违法犯罪情况end -->
                        <!-- 配偶子女有关情况start -->
                        <p class="sec-title">配偶子女有关情况</p>
                        <div class="sec-form-third haveBorderTop">
                            <div class="sec-left-text">
                                <p>1.在国境内外从事反对、攻击党和国家或者颠覆国家政权活动</p>
                                <p>2.被列为影响国家安全重点管控人员</p>
                                <p>3.因危害国家安全的行为收到处分或者处罚</p>
                            </div>
                            <el-form-item label="" prop="qscfqk">
                                <el-radio v-model="tjlist.qscfqk" v-for="item in yjgwqk" :label="item.id" :key="item.id"
                                    disabled>{{ item.yw }}</el-radio>
                            </el-form-item>
                        </div>
                        <!-- 配偶子女有关情况end -->
                        <!-- 其他需要说明的情况start -->
                        <p class="sec-title">其他需要说明的情况</p>
                        <div class="sec-form-four haveBorderTop">
                            <el-input type="textarea" autosize placeholder="请输入内容" v-model="tjlist.qtqk" disabled>
                            </el-input>
                        </div>
                        <!-- 其他需要说明的情况end -->
                        <!-- 本人承诺start -->
                        <p class="sec-title">本人承诺</p>
                        <div class="sec-form-five haveBorderTop" style="display:flex;align-items: center;">
                            <!-- <img v-if="imageUrlbrcn" :src="imageUrlbrcn" class="avatar" style=""> -->
                            <img v-if="!ylxy" :src="imageUrlbrcn" class="avatar" style="">
                            <el-button size="small" type="primary" v-if="ylxy" @click="yulan">预 览</el-button>
                        </div>
                        <!-- 本人承诺end -->
                        <p class="sec-title">上岗保密教育、签订保密承诺书</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="保密教育培训" prop="pxqk">
                                <el-radio v-model="tjlist.pxqk" v-for="item in bmjysfwc" :label="item.id" :key="item.id"
                                    disabled>{{
                                        item.sfwc }}</el-radio>
                            </el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="签订保密承诺书" prop="cnsqk">
                                <el-radio v-model="tjlist.cnsqk" v-for="item in bmjysfwc" :label="item.id" :key="item.id"
                                    disabled>{{
                                        item.sfwc }}</el-radio>
                            </el-form-item>
                            <div class="sec-form-fddw sec-header-flex">
                                <el-button type="primary" @click="bmcnsyl" size="mini"  v-if="show">预览</el-button>
                                <el-dialog :visible.sync="dialogVisible_bmcns">
                                    <img :src="bmcnsImageUrl" alt="" style="width: 100%">
                                    <div slot="footer" class="dialog-footer">
                                        <el-button size="small" @click="dialogVisible_bmcns = false">取 消</el-button>
                                    </div>
                                </el-dialog>
                            </div>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="签订保密协议书" prop="xysqk">
                                <el-radio v-model="tjlist.xysqk" v-for="item in bmjysfwc" :label="item.id" :key="item.id"
                                    disabled>{{
                                        item.sfwc }}</el-radio>
                            </el-form-item>
                            <div class="sec-form-fddw sec-header-flex">
                                <el-button type="primary" @click="bmxysyl" size="mini" v-if="show1">预览</el-button>
                                <el-dialog :visible.sync="dialogVisible_bmxys">
                                    <img :src="bmxysImageUrl" alt="" style="width: 100%">
                                    <div slot="footer" class="dialog-footer">
                                        <el-button size="small" @click="dialogVisible_bmxys = false">取 消</el-button>
                                    </div>
                                </el-dialog>
                            </div>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="人力资源部审批人" prop="rlspr">
                                <el-input placeholder="" v-model="tjlist.rlspr" clearable disabled></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="cnsrq">
                                <el-date-picker v-model="tjlist.cnsrq" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                    disabled type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">所在部门审查情况</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmsc">
                                <!-- <el-radio v-model="tjlist.bmsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    :disabled="disabled1" :key="item.id">{{
                                        item.sfty }}</el-radio> -->
                                <el-radio v-model="tjlist.bmsc" v-for="item in scqk" :label="item.id" disabled
                                    :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="该同志在涉密岗位工作" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门领导审批人" prop="bmspr">
                                <!-- <el-input placeholder="" :disabled="disabled1" v-model="tjlist.bmspr"
                                    clearable></el-input> -->
                                <el-input placeholder="" disabled v-model="tjlist.bmspr" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmscrq">
                                <!-- <el-date-picker :disabled="disabled1" v-model="tjlist.bmscrq" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker> -->
                                <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">人力资源部审查情况</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="rlsc">
                                <!-- <el-radio v-model="tjlist.rlsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    :disabled="disabled2" :key="item.id">{{
                                        item.sfty }}</el-radio> -->
                                <el-radio v-model="tjlist.rlsc" v-for="item in scqk" :label="item.id" disabled
                                    :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="该同志在涉密岗位工作" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="人力资源部领导审批人" prop="rlldspr">
                                <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.rlldspr"
                                    clearable></el-input> -->
                                <el-input placeholder="" disabled v-model="tjlist.rlldspr" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="rlscrq">
                                <!-- <el-date-picker :disabled="disabled2" v-model="tjlist.rlscrq" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker> -->
                                <el-date-picker disabled v-model="tjlist.rlscrq" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">保密办意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmbsc">
                                <!-- <el-radio v-model="tjlist.bmbsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    :disabled="disabled3" :key="item.id">{{
                                        item.sfty }}</el-radio> -->
                                <el-radio v-model="tjlist.bmbsc" v-for="item in scqk" :label="item.id" disabled
                                    :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="该同志在涉密岗位工作" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="保密办领导审批人" prop="bmbldspr">
                                <!-- <el-input placeholder="" :disabled="disabled3" v-model="tjlist.bmbldspr"
                                    clearable></el-input> -->
                                <el-input placeholder="" disabled v-model="tjlist.bmbldspr" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmbscrq">
                                <!-- <el-date-picker :disabled="disabled3" v-model="tjlist.bmbscrq" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker> -->
                                <el-date-picker disabled v-model="tjlist.bmbscrq" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">备注：涉密人员上岗审查、在岗复审均填本表</p>

                        <p class="sec-title">轨迹处理</p>
                        <el-table border class="sec-el-table" :data="gjclList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                            <el-table-column prop="clrid" label="办理人"></el-table-column>
                            <el-table-column prop="bllx" label="办理类型"></el-table-column>
                            <el-table-column prop="clyj" label="办理意见"></el-table-column>
                            <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                            <el-table-column prop="clsj" label="办理时间"></el-table-column>
                        </el-table>

                        <!-- 底部操作按钮start -->
                        <!-- <div class="sec-form-six haveBorderTop sec-footer">
                            <el-dropdown class="fr ml10">
                                <el-button type="primary" :disabled="btnsfth">
                                    退回
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item @click.native="save(2)">至上步办理人</el-dropdown-item>
                                    <el-dropdown-item @click.native="save(3)">至发起人</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <el-button @click="save(1)" :disabled="btnsftg" class="fr" type="success">通过</el-button>
                        </div> -->
                        <!-- 底部操作按钮end -->
                    </el-form>
                </div>
                <!-- 涉密人员任用审查列表end -->
                <!-- 发起申请弹框start -->
                <!-- <el-dialog title="人员选择" :close-on-click-modal="false" :visible.sync="dialogVisible" width="40%">
                    <div class="dlFqsqContainer">
                        <label for="">部门:</label>
                        <el-input class="input1" v-model="formInline.bmmc" clearable placeholder="部门"></el-input>
                        <label for="">姓名:</label>
                        <el-input class="input2" v-model="formInline.xm" clearable placeholder="姓名"></el-input>
                        <el-button class="searchButton" type="primary" icon="el-icon-search"
                            @click="onSubmit">查询</el-button>
                        <el-table class="tb-container" ref="multipleTable" :data="smryList" border
                            :header-cell-style="headerCellStyle" stripe @selection-change="selectRow" @select="handleSelect"
                            @row-click="handleRowClick">
                            <el-table-column type="selection" width="55" align="center"> </el-table-column>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="xm" label="姓名"></el-table-column>
                            <el-table-column prop="bmmc" label="部门"></el-table-column>
                            <el-table-column prop="gwmc" label="岗位"></el-table-column>
                        </el-table>
                        <el-pagination class="paginationContainer" background @current-change="handleCurrentChange"
                            @size-change="handleSizeChange" :pager-count="5" :current-page="page"
                            :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                            layout="total, prev, pager, sizes,next, jumper" :total="total">
                        </el-pagination>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" v-if="xsyc" @click="submit('formName')">确 定</el-button>
                        <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
                    </span>
                </el-dialog> -->
                <!-- 发起申请弹框end -->
            </el-tab-pane>
            <el-tab-pane label="流程跟踪" name="third">
                <el-table border class="sec-el-table" :data="lcgzList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                    <el-table-column prop="clrid" label="办理人"></el-table-column>
                    <el-table-column prop="bllx" label="办理类型"></el-table-column>
                    <el-table-column prop="clyj" label="办理意见"></el-table-column>
                    <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                    <el-table-column prop="clsj" label="办理时间"></el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>

    </div>
</template>
<script>
import {
    //审批指南
    getBlzn,
    //审批信息
    getRyscInfoBySlid,
    //审批信息
    getZgfsInfoBySlid,
    //判断实例所处环节
    getSchj,
    //事项审核
    getSxsh,
    //查询审批用户列表
    getSpUserList,
    //非第一环节选择审批人
    tjclr,
    //修改任用审查详情记录
    updateRysc,
    //流程跟踪
    getSpGjxx,
} from '../../../../api/wdgz'
import AddLineTable from "../../../components/common/addLineTable.vue";   //人工纠错组件
export default {
    components: {
        AddLineTable
    },
    props: {},
    data() {
        return {
            // table 行样式
            headerCellStyle: {
                background: '#EEF7FF',
                color: '#4D91F8'
            },
            fwdyid: '',
            slid: '',
            activeName: 'second',
            //审批指南
            spznList: [],
            // form表单提交数据
            //审批信息
            tjlist: {
                // 主要学习及工作经历
                xxjlList: [],
                // 家庭成员及社会关系
                cyjshgxList: [],
                // 持有因公出入境证件情况
                ygrjzjqkList: [],
                // 持有因私出入境证件情况
                ysrjzjqkList: [],
                // 因私出国(境)情况
                yscgqkList: [],
                // 接受境外资助情况
                jsjwzzqkList: [],
                // 处分或者违法犯罪情况
                clhwffzqkList: [],
            },
            //轨迹处理
            gjclList: [],
            upccLsit: {},
            //判断实例所处环节
            disabled1: false,
            disabled2: false,
            disabled3: false,
            btnsftg: true,
            btnsfth: true,
            jgyf: '',
            //性别
            xb: [{
                xb: '男',
                id: 1
            },
            {
                xb: '女',
                id: 2
            },
            ],
            //移居国(境)外情况
            yjgwqk: [{
                yw: '有',
                id: 1
            },
            {
                yw: '无',
                id: 0
            },
            ],
            //上岗保密教育、签订保密承诺书
            bmjysfwc: [
                {
                    sfwc: '已完成',
                    id: 1
                },
                {
                    sfwc: '未完成',
                    id: 0
                },
            ],
            scqk: [
                {
                    sfty: '同意',
                    id: 1
                },
                {
                    sfty: '不同意',
                    id: 0
                },
            ],
            // 政治面貌下拉选项
            zzmmoptions: [],
            sltshow: '', // 文档的缩略图显示
            fileList: [],
            dialogVisible: false,
            fileRow: '',
            //人员任用
            smryList: [],
            page: 1,
            pageSize: 10,
            total: 0,
            formInline: {
                'bmmc': '',
                'xm': ''
            }, // 搜索条件
            selectlistRow: [], //列表的值
            xsyc: true,
            mbhjid: '',
            imageUrl: '',
            imageUrlbrcn: '',
            ylxy: true,
            file: {},
            bmcnssmj: '',
            bmxyssmj: '',
            //保密承诺书预览
            dialogVisible_bmcns: false,
            bmcnsImageUrl: '',
            //保密承诺书预览
            dialogVisible_bmxys: false,
            bmxysImageUrl: '',
            //流程跟踪
            lcgzList: [],

        }
    },
    computed: {},
    mounted() {
        this.fwdyid = this.$route.query.fwdyid
        console.log("this.fwdyid", this.fwdyid);
        this.slid = this.$route.query.slid
        console.log("this.slid", this.slid);
        //审批指南初始化列表
        this.spzn()
        //审批信息初始化列表
        // this.spxxxgcc()
        this.spxx()
        //判断实例所处环节
        // this.pdschj()
        // //事项审核
        // this.sxsh()
        //初始化el-dialog列表数据
        // this.splist()
        //流程跟踪初始化列表
        this.lcgz()

    },
    methods: {
        //审批指南
        //审批指南初始化列表
        async spzn() {
            let params = {
                fwdyid: this.fwdyid,
            }
            let data = await getBlzn(params)
            if (data.code == 10000) {
                this.spznList = data.data.content
            }
        },
        //审批信息
        //审批信息初始化数据
        // async spxxxgcc() {
        //     // let params = {
        //     //     slid: this.slid
        //     // }
        //     // let data = await getRyscInfoBySlid(params)
        //     // this.upccLsit = data
        //     // console.log('this.upccLsit', this.upccLsit);
        //     // this.chRadio()
        // },
        // async spxx() {
        //     let params = {
        //         slid: this.slid
        //     }
        //     let data ;
        //     data= await getRyscInfoBySlid(params);
        //     this.tjlist = data.rysc
        //     if (data == '') {
        //         data= await getZgfsInfoBySlid(params);
        //         this.tjlist = data.zgfs
        //     }
        //     console.log(data);


        //     const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.zp;
        //     if (typeof iamgeBase64 === "string") {
        //         // 复制某条消息
        //         if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        //         function validDataUrl(s) {
        //             return validDataUrl.regex.test(s);
        //         }
        //         validDataUrl.regex =
        //             /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        //         if (validDataUrl(iamgeBase64)) {
        //             // debugger;
        //             let that = this;

        //             function previwImg(item) {
        //                 that.imageUrl = item;
        //             }
        //             previwImg(iamgeBase64);
        //         }
        //     }
        //     if (this.tjlist.xb == 1) {
        //         this.tjlist.xb = '男'
        //     } else if (this.tjlist.xb == 2) {
        //         this.tjlist.xb = '女'
        //     }
        //     if (this.tjlist.hyzk == 1) {
        //         this.tjlist.hyzk = '已婚'
        //     } else if (this.tjlist.hyzk == 0) {
        //         this.tjlist.hyzk = '未婚'
        //     }
        //     if (this.tjlist.zzmm == 1) {
        //         this.tjlist.zzmm = '中共党员'
        //     } else if (this.tjlist.zzmm == 2) {
        //         this.tjlist.zzmm = '团员'
        //     } else if (this.tjlist.zzmm == 3) {
        //         this.tjlist.zzmm = '民主党派'
        //     } else if (this.tjlist.zzmm == 4) {
        //         this.tjlist.zzmm = '群众'
        //     }
        //     if (this.tjlist.smdj == 1) {
        //         this.tjlist.smdj = '核心'
        //     } else if (this.tjlist.smdj == 2) {
        //         this.tjlist.smdj = '重要'
        //     } else if (this.tjlist.smdj == 3) {
        //         this.tjlist.smdj = '一般'
        //     }
        //     //主要学习及工作经历
        //     this.tjlist.xxjlList = data.ryglRyscScjlList
        //     //家庭成员及主要社会关系情况
        //     let jtarr = []
        //     data.ryglRyscJtcyList.forEach((item) => {
        //         if (item.jwjlqk == 0) {
        //             item.jwjlqk = '否'
        //         } else if (item.jwjlqk == 1) {
        //             item.jwjlqk = '是'
        //         }
        //         jtarr.push(item)
        //         this.tjlist.cyjshgxList = jtarr
        //     })
        //     //持有因公出入境证件情况
        //     let arr = []
        //     let list = []
        //     data.ryglRyscSwzjList.forEach((item) => {
        //         if (item.cyqk == 0) {
        //             item.cyqk = '无'
        //         } else if (item.cyqk == 1) {
        //             item.cyqk = '有'
        //         }
        //         if (item.fjlb == 1 || item.fjlb == 2 || item.fjlb == 3) {
        //             if (item.fjlb == 1) {
        //                 item.fjlb = '护照'
        //             } else if (item.fjlb == 2) {
        //                 item.fjlb = '港澳通行证'
        //             } else if (item.fjlb == 3) {
        //                 item.fjlb = '台湾通行证'
        //             }
        //             arr.push(item)
        //             this.tjlist.ygrjzjqkList = arr
        //         }
        //         //持有因公出入境证件情况
        //         else if (item.fjlb == 4 || item.fjlb == 5 || item.fjlb == 6 || item.fjlb == 7) {
        //             if (item.fjlb == 4) {
        //                 item.fjlb = '护照'
        //             } else if (item.fjlb == 5) {
        //                 item.fjlb = '港澳通行证'
        //             } else if (item.fjlb == 6) {
        //                 item.fjlb = '台湾通行证'
        //             } else if (item.fjlb == 7) {
        //                 item.fjlb = '境外永久居留权长期居留许可证件'
        //             }
        //             list.push(item)
        //             this.tjlist.ysrjzjqkList = list
        //         }
        //     })
        //     //因私出国(境)情况
        //     this.tjlist.yscgqkList = data.ryglRyscYccgList
        //     //接受境外资助情况
        //     this.tjlist.jsjwzzqkList = data.ryglRyscJwzzqkList
        //     //处分或者违法犯罪情况
        //     this.tjlist.clhwffzqkList = data.ryglRyscCfjlList

        // },
        async spxx() {
            let params = {
                slid: this.slid
            }
            let data;
            let lbxx = {};
            let ryglScjlList = [];
            let ryglJtcyList = [];
            let ryglSwzjList = [];
            let ryglYccgList = [];
            let ryglJwzzqkList = [];
            let ryglCfjlList = [];
            console.log(this.lx);

            data = await getRyscInfoBySlid(params);
            console.log(data);
            if (data.code == 9999 || data == '') {
                data = await getZgfsInfoBySlid(params)
                lbxx = data.zgfs;
                ryglScjlList = data.ryglZgfsScjlList;
                ryglJtcyList = data.ryglZgfsJtcyList;
                ryglSwzjList = data.ryglZgfsSwzjList;
                ryglYccgList = data.ryglZgfsYccgList;
                ryglJwzzqkList = data.ryglZgfsJwzzqkList;
                ryglCfjlList = data.ryglZgfsCfjlList;
            } else {
                lbxx = data.rysc;
                ryglScjlList = data.ryglRyscScjlList;
                ryglJtcyList = data.ryglRyscJtcyList;
                ryglSwzjList = data.ryglRyscSwzjList;
                ryglYccgList = data.ryglRyscYccgList;
                ryglJwzzqkList = data.ryglRyscJwzzqkList;
                ryglCfjlList = data.ryglRyscCfjlList;
            }
            console.log(lbxx);
            lbxx.jbzcComputed = lbxx.jbzc == 1 ? '省部级' : lbxx.jbzc == 2 ? '厅局级' : lbxx.jbzc == 3 ?
                '县处级' : lbxx.jbzc == 4 ? '乡科级及以下' : lbxx.jbzc == 5 ? '高级(含正高、副高)' : lbxx.jbzc == 6 ? '中级' :
                    lbxx.jbzc == 7 ? '初级及以下' : lbxx.jbzc == 8 ? '试用期人员' : lbxx.jbzc == 9 ? '工勤人员' : lbxx
                        .jbzc == 10 ? '企业职员' : lbxx.jbzc == 11 ? '其他' : ''
            let bmC = lbxx.bmmc != '' ? '部门：' + lbxx.bmmc + '、' : ''
            let zwC = lbxx.zw != '' ? '职务：' + lbxx.zw + '、' : ''
            lbxx.bmzwzcComputed = bmC + zwC + '职称：' + lbxx.jbzcComputed
            this.tjlist = lbxx
            //主要学习及工作经历
            this.tjlist.xxjlList = ryglScjlList
            //家庭成员及主要社会关系情况
            let jtarr = []
            ryglJtcyList.forEach((item) => {
                if (item.jwjlqk == 0) {
                    item.jwjlqk = '否'
                } else if (item.jwjlqk == 1) {
                    item.jwjlqk = '是'
                }
                jtarr.push(item)
                this.tjlist.cyjshgxList = jtarr
            })
            //持有因公出入境证件情况
            let arr = []
            let list = []
            ryglSwzjList.forEach((item) => {
                if (item.cyqk == 0) {
                    item.cyqk = '无'
                } else if (item.cyqk == 1) {
                    item.cyqk = '有'
                }
                if (item.fjlb == 1 || item.fjlb == 2 || item.fjlb == 3) {
                    if (item.fjlb == 1) {
                        item.fjlb = '护照'
                    } else if (item.fjlb == 2) {
                        item.fjlb = '港澳通行证'
                    } else if (item.fjlb == 3) {
                        item.fjlb = '台湾通行证'
                    }
                    arr.push(item)
                    this.tjlist.ygrjzjqkList = arr
                }
                //持有因公出入境证件情况
                else if (item.fjlb == 4 || item.fjlb == 5 || item.fjlb == 6 || item.fjlb == 7) {
                    if (item.fjlb == 4) {
                        item.fjlb = '护照'
                    } else if (item.fjlb == 5) {
                        item.fjlb = '港澳通行证'
                    } else if (item.fjlb == 6) {
                        item.fjlb = '台湾通行证'
                    } else if (item.fjlb == 7) {
                        item.fjlb = '境外永久居留权长期居留许可证件'
                    }
                    list.push(item)
                    this.tjlist.ysrjzjqkList = list
                }
            })
            //因私出国(境)情况
            this.tjlist.yscgqkList = ryglYccgList
            //接受境外资助情况
            this.tjlist.jsjwzzqkList = ryglJwzzqkList
            //处分或者违法犯罪情况
            this.tjlist.clhwffzqkList = ryglCfjlList
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.zp;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.imageUrl = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
            if (this.tjlist.brcn != '') {
                this.yldis = false
            } else {
                this.yldis = true
            }
            if (this.tjlist.xb == 1) {
                this.tjlist.xb = '男'
            } else if (this.tjlist.xb == 2) {
                this.tjlist.xb = '女'
            }
            if (this.tjlist.hyzk == 1) {
                this.tjlist.hyzk = '已婚'
            } else if (this.tjlist.hyzk == 0) {
                this.tjlist.hyzk = '未婚'
            }
            if (this.tjlist.zzmm == 1) {
                this.tjlist.zzmm = '中共党员'
            } else if (this.tjlist.zzmm == 2) {
                this.tjlist.zzmm = '团员'
            } else if (this.tjlist.zzmm == 3) {
                this.tjlist.zzmm = '民主党派'
            } else if (this.tjlist.zzmm == 4) {
                this.tjlist.zzmm = '群众'
            }
            if (this.tjlist.smdj == 1) {
                this.tjlist.smdj = '核心'
            } else if (this.tjlist.smdj == 2) {
                this.tjlist.smdj = '重要'
            } else if (this.tjlist.smdj == 3) {
                this.tjlist.smdj = '一般'
            }
            if (this.tjlist.cnsqk == 1) {
                this.show = true
            } else {
                this.show = false
            }
            if (this.tjlist.xysqk == 1) {
                this.show1 = true
            } else {
                this.show1 = false
            }
        },
        // 预览
        yulan() {
            this.ylxy = false
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.brcn;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.imageUrlbrcn = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        // chRadio(val) {
        //     console.log(val);
        //     if (val == 1) {
        //         this.btnsftg = false
        //         this.btnsfth = true
        //     } else if (val == 0) {
        //         this.btnsftg = true
        //         this.btnsfth = false
        //     } else if (this.upccLsit.rysc.bmsc == 1) {
        //         this.btnsftg = false
        //         this.btnsfth = true
        //     } else if (this.upccLsit.rysc.bmsc == 0) {
        //         this.btnsftg = true
        //         this.btnsfth = false
        //     } else if (this.upccLsit.rysc.rlsc == 1) {
        //         this.btnsftg = false
        //         this.btnsfth = true
        //     } else if (this.upccLsit.rysc.rlsc == 0) {
        //         this.btnsftg = true
        //         this.btnsfth = false
        //     } else if (this.upccLsit.rysc.bmbsc == 1) {
        //         this.btnsftg = false
        //         this.btnsfth = true
        //     } else if (this.upccLsit.rysc.bmbsc == 0) {
        //         this.btnsftg = true
        //         this.btnsfth = false
        //     }
        // },

        // 通过
        // async save(index) {
        //     // let jgbz = index
        //     // if (this.tjlist.pxqk != undefined) {
        //     //     if (this.tjlist.cnsqk != undefined) {
        //     //         if (this.tjlist.xysqk != undefined) {
        //     //             if (this.tjlist.cnsrq != undefined) {
        //     //                 let obj = {
        //     //                     pxqk: this.tjlist.pxqk,
        //     //                     cnsqk: this.tjlist.cnsqk,
        //     //                     xysqk: this.tjlist.xysqk,
        //     //                     cnsrq: this.tjlist.cnsrq,
        //     //                     bmsc: this.tjlist.bmsc,
        //     //                     bmscrq: this.tjlist.bmscrq,
        //     //                     rlsc: this.tjlist.rlsc,
        //     //                     rlscrq: this.tjlist.rlscrq,
        //     //                     scqk: this.tjlist.scqk,
        //     //                     bmbscrq: this.tjlist.bmbscrq,
        //     //                 }
        //     //                 let params = {
        //     //                     ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,
        //     //                     ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,
        //     //                     ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,
        //     //                     ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,
        //     //                     ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,
        //     //                     ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,
        //     //                     rysc: Object.assign(this.upccLsit.rysc, obj)
        //     //                 }
        //     //                 let data = await updateRysc(params)
        //     //                 if (data.code == 10000) {
        //     //                     if (this.tjlist.bmscrq != undefined || this.tjlist.rlscrq != undefined || this.tjlist.bmbscrq != undefined) {
        //     //                         if (this.tjlist.bmsc == 1 || this.tjlist.rlsc == 1 || this.tjlist.bmbsc == 1) {
        //     //                             if (jgbz == 1) {
        //     //                                 this.jgyf = 1
        //     //                             }
        //     //                             this.sxsh()
        //     //                             this.spxx()
        //     //                         }
        //     //                         if (this.tjlist.bmsc == 2 || this.tjlist.rlsc == 2 || this.tjlist.bmbsc == 2) {
        //     //                             if (jgbz == 2) {
        //     //                                 this.jgyf = 2
        //     //                             } else if (jgbz == 3) {
        //     //                                 this.jgyf = 3
        //     //                             }
        //     //                             this.sxsh()
        //     //                             this.spxx()
        //     //                         }
        //     //                     } else { this.$message.warning('请选择日期') }
        //     //                 }
        //     //             } else { this.$message.warning('请选择日期') }
        //     //         } else { this.$message.warning('是否签订保密协议书') }
        //     //     } else { this.$message.warning('是否签订保密承诺书') }
        //     // } else { this.$message.warning('是否进行保密教育培训') }
        // },
        //立即办理
        ljbl() {
            this.activeName = 'second'
        },
        // //判断实例所处环节
        // async pdschj() {
        //     // let params = {
        //     //     fwdyid: this.fwdyid,
        //     //     slid: this.slid
        //     // }
        //     // let data = await getSchj(params)
        //     // if (data.code == 10000) {
        //     //     if (data.data.content == 1) {
        //     //         this.disabled2 = true
        //     //         this.disabled3 = true
        //     //     }
        //     //     if (data.data.content == 2) {
        //     //         this.disabled1 = true
        //     //         this.disabled3 = true
        //     //     }
        //     //     if (data.data.content == 3) {
        //     //         this.disabled1 = true
        //     //         this.disabled2 = true
        //     //     }
        //     // }
        // },
        // //事项审核
        // async sxsh() {
        //     // let params = {
        //     //     fwdyid: this.fwdyid,
        //     //     slid: this.slid,
        //     //     jg: this.jgyf,
        //     //     smryid: this.tjlist.smryid
        //     // }
        //     // let data = await getSxsh(params)
        //     // if (data.code == 10000) {
        //     //     if (data.data.zt == 0) {
        //     //         this.$message({
        //     //             message: data.data.msg,
        //     //             type: 'success'
        //     //         });
        //     //         // this.smryList = data.data.blrarr
        //     //         this.mbhjid = data.data.mbhjid
        //     //         this.splist()
        //     //         this.dialogVisible = true
        //     //     } else if (data.data.zt == 1) {
        //     //         this.$message({
        //     //             message: data.data.msg,
        //     //             type: 'success'
        //     //         });
        //     //     } else if (data.data.zt == 2) {
        //     //         this.$message({
        //     //             message: data.data.msg
        //     //         });
        //     //     } else if (data.data.zt == 3) {
        //     //         this.$message({
        //     //             message: data.data.msg
        //     //         });
        //     //     }
        //     // }
        // },
        //初始化el-dialog列表数据
        // async splist() {
        //     // let params = {
        //     //     fwdyid: this.fwdyid,
        //     //     'xm': this.formInline.xm,
        //     //     'bmmc': this.formInline.bmmc,
        //     //     page: this.page,
        //     //     pageSize: this.pageSize,
        //     //     qshjid: this.mbhjid,
        //     // }
        //     // let data = await getSpUserList(params)
        //     // this.smryList = data.records
        //     // this.total = data.total
        // },
        // onSubmit() {
        //     this.splist()
        // },
        // selectRow(selection) {
        //     if (selection.length <= 1) {
        //         console.log('点击选中数据：', selection);
        //         this.selectlistRow = selection
        //         this.xsyc = true
        //     } else if (selection.length > 1) {
        //         this.$message.warning('只能选中一条数据')
        //         this.xsyc = false
        //     }

        // },
        // handleSelect(selection, val) {
        //     //只能选择一行，选择其他，清除上一行
        //     if (selection.length > 1) {
        //         let del_row = selection.shift()
        //         this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中
        //     }
        // },
        // // 点击行触发，选中或不选中复选框
        // handleRowClick(row, column, event) {
        //     this.$refs.multipleTable.toggleRowSelection(row)
        //     this.selectChange(this.selectlistRow)
        // },
        // async submit() {
        //     let params = {
        //         fwdyid: this.fwdyid,
        //         slid: this.slid,
        //         shry: this.selectlistRow[0].yhid,
        //         mbhjid: this.mbhjid,
        //     }
        //     let data = await tjclr(params)
        //     if (data.code == 10000) {
        //         this.$message({
        //             message: data.message,
        //             type: 'success'
        //         });
        //         this.dialogVisible = false
        //     }
        // },
        //上传文件
        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isPNG = file.type === 'image/png';
            if (!isJPG && !isPNG) {
                this.$message.error('上传缩略图只能是 JPG/PNG 格式!');
            }
            return isJPG || isPNG;
        },
        // 64码
        blobToBase64(blob, callback) {
            const fileReader = new FileReader();
            fileReader.onload = (e) => {
                callback(e.target.result);
            };
            fileReader.readAsDataURL(blob);
        },
        //保密承诺书预览
        bmcnsyl() {
            this.dialogVisible_bmcns = true
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.cnssmj;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.bmcnsImageUrl = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        //保密协议书预览
        bmxysyl() {
            this.dialogVisible_bmxys = true
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.xyssmj;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.bmxysImageUrl = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        // //保密承诺书扫描件
        // async bmcns(item) {
        //     // this.file = item.file
        //     this.file = URL.createObjectURL(item.file);
        //     this.bmcnssmj = item.file
        //     this.blobToBase64(item.file, async (dataurl) => {
        //         this.upccLsit.rysc.cnssmj = dataurl.split(',')[1]
        //         let params = {
        //             ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,
        //             ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,
        //             ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,
        //             ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,
        //             ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,
        //             ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,
        //             rysc: this.upccLsit.rysc
        //         }
        //         let data = await updateRysc(params)
        //         if (data.code == 10000) {
        //             this.$message.success('上传保密承诺书成功！')
        //             this.spxx()
        //         }
        //     });
        // },
        // //签订保密协议书
        // async bmxys(item) {
        //     // this.file = item.file
        //     this.file = URL.createObjectURL(item.file);
        //     this.bmxyssmj = item.file
        //     this.blobToBase64(item.file, async (dataurl) => {
        //         this.upccLsit.rysc.xyssmj = dataurl.split(',')[1]
        //         let params = {
        //             ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,
        //             ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,
        //             ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,
        //             ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,
        //             ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,
        //             ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,
        //             rysc: this.upccLsit.rysc
        //         }
        //         let data = await updateRysc(params)
        //         if (data.code == 10000) {
        //             this.$message.success('上传保密承诺书成功！')
        //             this.spxx()
        //         }
        //     });
        // },
        // //列表分页--跳转页数
        // handleCurrentChange(val) {
        //     this.page = val
        //     this.splist()
        // },
        // //列表分页--更改每页显示个数
        // handleSizeChange(val) {
        //     this.page = 1
        //     this.pageSize = val
        //     this.splist()
        // },
        //流程跟踪
        //流程跟踪初始化列表
        async lcgz() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSpGjxx(params)
            if (data.code == 10000) {
                this.lcgzList = data.data.content
                this.gjclList = data.data.content
            }
        },
    },
    watch: {

    }
}

</script>
  
<style scoped>
.sec-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: overlay;
}

.sec-title-big {
    width: 100%;
    color: #1b72d8;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 26px;
}

.sec-title {
    border-left: 5px solid #1b72d8;
    color: #1b72d8;
    font-size: 20px;
    font-weight: 700;
    text-indent: 10px;
    margin-bottom: 20px;
    margin-top: 10px;
}

.sec-form-container {
    width: 100%;
    height: 100%;
}

.sec-form-left {
    width: calc(100% - 260px);
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
}

.sec-form-left:not(:first-child) {
    border-top: 0;
}

.sec-form-left .el-form-item {
    float: left;
    width: 100%;
}

.sec-header-section {
    width: 100%;
    position: relative;
}

.sec-header-pic {
    width: 258px;
    position: absolute;
    right: 0px;
    top: 0;
    height: 245px;
    border: 1px solid #CDD2D9;
    border-left: 0;
    background: #ffffff;
}

.sec-header-flex {
    display: flex;
    align-items: center;
    justify-content: center;
}

.sec-header-mar {
    margin-right: 10px;
}

.sec-form-second {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
    border-top: 0;
    position: relative;
}

.sec-form-fddw {
    height: 100%;
    position: absolute;
    top: 0;
    right: 46.5%;
}

.sec-form-third {
    border: 1px solid #CDD2D9;
    /* height: 40px;  */
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.hyzk {
    padding-left: 15px;
    background-color: #F5F7FA;
    width: calc(100% - 16px);
    border-right: 1px solid #CDD2D9;
    color: #000;
}

.sec-form-four {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-five {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    overflow: hidden;
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.yulan {
    text-align: center;
    cursor: pointer;
    color: #3874D5;
    font-weight: 600;
    float: left;
    margin-left: 10px;
}

.avatar {
    width: 100px;
    height: 100px;
    display: block;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    border: 2px solid #EBEBEB;
}

.sec-form-six {
    border: 1px solid #CDD2D9;
    overflow: hidden;
    background: #ffffff;
    padding: 10px;
}

.ml10 {
    margin-left: 10px;
}

.sec-footer {
    margin-top: 10px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
    border: none;
}

.sec-left-text {
    float: left;
    margin-right: 130px;
}

.haveBorderTop {
    border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
    width: 500px !important;
}

>>>.longLabel .el-form-item__content {
    margin-left: 500px !important;
    padding-left: 20px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

.gtzzsmgwgz {
    border-right: 1px solid #CDD2D9;
}

.gtzzsmgwgz>>>.el-form-item__content {
    display: none !important;
}

.gtzzsmgwgz>>>.el-form-item__label {
    border: none;
    text-align: left !important;
}

/* .sec-form-second:not(:first-child){
    border-top: 0;
  } */
.sec-form-second .el-form-item {
    float: left;
    width: 100%;
}

.sec-el-table {
    width: 100%;
    border: 1px solid #EBEEF5;
    height: calc(100% - 34px - 44px - 10px);
}

>>>.sec-el-table .el-input__inner {
    border: none !important;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
    width: 200px;
    text-align: center;
    font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
    border: none;
    border-right: 1px solid #CDD2D9;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item {
    margin-bottom: 0px;
}

/* >>>.el-form > div {
    border: 1px solid #CDD2D9;;
  } */
>>>.el-form-item__label {
    border-right: 1px solid #CDD2D9;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
    width: 150px;
    margin-left: 10px;
}

.dlFqsqContainer .searchButton {
    margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
    height: 40px;
}

.dlFqsqContainer .input1 {
    margin-right: 20px;
    margin-bottom: 10px;
}

/deep/ .el-input.is-disabled .el-input__inner {
    color: #000 !important;
}
</style>
  