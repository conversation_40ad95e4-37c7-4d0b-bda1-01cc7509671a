<template>
  <div style="padding: 0px">
    <el-menu
      :default-active="this.$route.path"
      class="el-menu-vertical-demo"
      router
      @open="handleOpen"
      @close="handleClose"
      @select="handleSelect"
      background-color="#3874D5"
      text-color="#FFFFFF"
      active-text-color="#FFFFFF"
    >
      <el-submenu
        v-for="(item, index) in enumShowList"
        :index="index + ''"
        :key="index"
        :style="{ '--background': item.background }"
      >
        <template slot="title">
          <div>
            <i v-show="!item.selected" :class="item.icon"></i>
            <i v-show="item.selected" :class="item.icon_selected"></i>
            <!-- <i :class="item.icon" style="color:#fff"></i> -->
            <span>{{ item.name }}</span>
          </div>
        </template>
        <el-menu-item
          v-for="(itemChild, indexChild) in item.children"
          :index="itemChild.path"
          :key="index + '-' + indexChild"
          :route="itemChild.path"
          class="ejcd"
        >
          <div style="display: flex; align-items: center; font-size: 16px">
            <!-- <img src="../../assets/icons/menu_child_icon.png" style="width: 6px; margin-right: 5px;height: 6px;" /> -->
            <span v-show="!itemChild.selected" class="xtb"></span>
            <span v-show="itemChild.selected" class="xtb-selected"></span>
            <!-- <div v-if="itemChild.name == '继续自查自评'">
              {{ itemChild.name }}111111111
            </div>
            <div v-else-if="itemChild.name == '新建自查自评'">
              {{ itemChild.name }}22222222222
            </div> -->
            <div>
              {{ itemChild.name }}
            </div>
          </div>
        </el-menu-item>
      </el-submenu>
    </el-menu>
  </div>
</template>

<script>
import { getAllMenu } from "../../../api/submeu";
export default {
  data() {
    return {
      // 需要显示的菜单，由父组件传递过来
      showRoutePathList: [],
      // 菜单路由的总集，新增的菜单需要在这里进行配置一下
      enumList: [
        {
          name: "场所管理",
          icon: "icon-01",
          icon_selected: "icon-01-selected",
          selected: true,
          children: [
            {
              name: "场所管理",
              path: "/jfgl",
            },
          ],
        },
        // {
        //   name: "机柜管理",
        //   icon: "icon-02",
        //   icon_selected: "icon-02-selected",
        //   selected: true,
        //   children: [
        //     {
        //       name: "机柜管理",
        //       path: "/jggl",
        //     },
        //   ],
        // },
        {
          name: "资产管理",
          icon: "icon-03",
          icon_selected: "icon-03-selected",
          selected: true,
          children: [
            {
              name: "资产管理",
              path: "/sbgl",
            },
          ],
        },
        {
          name: "介质管理",
          icon: "icon-06",
          icon_selected: "icon-06-selected",
          selected: true,
          children: [
            {
              name: "巡检操作管理",
              path: "/xjczgl",
            },
            {
              name: "迁移操作管理",
              path: "/qyczgl",
            },
            {
              name: "资产变更管理",
              path: "/zcbggl",
            },
            {
              name: "故障处理管理",
              path: "/gzclgl",
            },
            // {
            //   name: "载体管理",
            //   path: "/smzttz",
            // },
            // {
            //   name: "载体制作登记",
            //   path: "/smztzz",
            // },
            // {
            //   name: "载体复制登记",
            //   path: "/smztfz",
            // },
            // {
            //   name: "载体接收传递登记",
            //   path: "/smztjs",
            // },
            // {
            //   name: "载体外发传递登记",
            //   path: "/smztwf",
            // },
            // {
            //   name: "载体签收登记",
            //   path: "/smztqsdj",
            // },
            // {
            //   name: "载体外出携带登记",
            //   path: "/smztwcxd",
            // },
            // {
            //   name: "载体借阅登记",
            //   path: "/smztjy",
            // },
            // {
            //   name: "载体销毁登记",
            //   path: "/smztxh",
            // },
          ],
        },
        {
          name: "数据统计",
          icon: "icon-05",
          icon_selected: "icon-05-selected",
          selected: true,
          children: [
            {
              name: "巡检操作数据统计",
              path: "/jfxjsjtj",
            },
            {
              name: "资产迁移数据统计",
              path: "/sbqysjtj",
            },
            {
              name: "资产变更数据统计",
              path: "/sbxhsjtj",
            },
            {
              name: "故障处理数据统计",
              path: "/gzclsjtj",
            },
            {
              name: "非合规操作",
              path: "/fhgcz",
            },
          ],
        },
        {
          name: "我的工作",
          icon: "icon-03",
          icon_selected: "icon-03-selected",
          selected: false,
          children: [
            {
              name: "待办事项",
              path: "/dbsx",
            },
            {
              name: "已办事项",
              path: "/ybsx",
            },
            {
              name: "我的发起",
              path: "/wdfq",
            },
          ],
        },
      ],
      // 显示菜单
      enumShowList: [],
      enumHiddenList: [],
      dwjy: true,
    };
  },
  props: {},
  methods: {
    async submenu() {
      // let data = await getChildMenu(params)
      let data1 = await getAllMenu();
      let shu = [];
      let shuList = [];
      let data = [];
      // console.log(data1);
      data1.forEach((item) => {
        // console.log(item);
        let children = [];
        data1.forEach((item1) => {
          if (item1.fcdid == item.gnzyid) {
            // console.log(item1);
            children.push(item1);
            item.children = children;
          }
        });
        shu.push(item);
      });
      // console.log(shu);
      shu.forEach((item) => {
        if (item.fcdid == "") {
          // console.log(item);
          shuList.push(item);
        }
      });
      // console.log(shuList);
      shuList.forEach((item) => {
        console.log(item);
        if (item.children != undefined) {
          item.children.forEach((item1) => {
            data.push(item1);
          });
        }
      });
      // console.log(data);
      // console.log(data);
      // this.enumList = data
      // console.log(this.enumList);
    },
    handleOpen() {},
    handleClose() {},
    handleSelect(index, path) {
      // console.log('handleSelect', index, path)
      // 更改父菜单的icon，这里是简单的写法，只支持两级的菜单
      let tempList = JSON.parse(JSON.stringify(this.enumShowList));
      // 标记被选中的父菜单
      let selectedFcdIndex;
      // 标记被选中的子菜单
      let selectedZcdIndex;
      // path[0] == 0 是特殊项，单独处理
      console.log(path);
      console.log(tempList[0]);
      if (path[0] == 0) {
        // 校验index==0的子项，判断是否是子项被选中
        if (tempList[0]) {
          tempList[0].children.some((item, index) => {
            if (item.path == path[path.length - 1]) {
              selectedFcdIndex = 0;
              selectedZcdIndex = index;
              return true;
            }
          });
        }
      } else {
        tempList.forEach((item, index) => {
          // 跳过第一项父菜单
          if (index == 0) {
            return true;
          }
          // console.log('handleSelect item', item)
          // 校验子菜单是否被选中
          item.children.forEach((childItem, childIndex) => {
            if (childItem.path == path[path.length - 1]) {
              selectedFcdIndex = index;
              selectedZcdIndex = childIndex;
              // childItem.selected = true
              // childItem.background = 'rgb(217, 236, 255)'
              // item.selected = true
              // item.background = 'rgb(217, 236, 255)'
              // selectedFcd = item
            } else {
              // childItem.selected = false
              // childItem.background = 'rgba(255,255,255,0)'
              // item.selected = false
              // item.background = 'rgba(255,255,255,0)'
            }
          });
        });
      }

      // // 判断父菜单选中情况
      // tempList.forEach((item, index) => {
      //   if (index == path[0]) {
      //     console.log('handleSelect找到', index, item)
      //     item.selected = true
      //     item.background = 'rgb(217, 236, 255)'
      //   } else {
      //     console.log('handleSelect不是一级菜单')
      //     item.selected = false
      //     item.background = 'rgba(255,255,255,0)'
      //   }
      // })

      // 改变菜单颜色
      if (selectedFcdIndex !== undefined && selectedZcdIndex !== undefined) {
        // console.log('handleSelect 改变菜单颜色')
        tempList.forEach((item, index) => {
          if (index == selectedFcdIndex) {
            item.selected = true;
            // item.background = 'rgb(217, 236, 255)'
            item.background = "#115CC1";
            item.children.forEach((childItem, childIndex) => {
              if (childIndex == selectedZcdIndex) {
                childItem.selected = true;
                // childItem.background = 'rgb(217, 236, 255)'
                childItem.background = "#115CC1";
              } else {
                childItem.selected = false;
                childItem.background = "rgba(255,255,255,0)";
              }
            });
          } else {
            item.selected = false;
            item.background = "rgba(255,255,255,0)";
            item.children.forEach((childItem, childIndex) => {
              childItem.selected = false;
              childItem.background = "rgba(255,255,255,0)";
            });
          }
        });
      }

      // console.log('handleSelect index', selectedFcdIndex, selectedZcdIndex)
      // console.log('handleSelect tempList', tempList)
      // console.log('handleSelect this.enumShowList', this.enumShowList)
      this.enumShowList = tempList;
    },
    // 菜单构建方法
    menuBuild() {
      const PubSub = require("pubsub-js");
      PubSub.subscribe("dataNext", (msg, data) => {
        if (data == "next") {
          this.dwjy = false;
          this.enumShowList.find((item) => {
            if (item.name == "自查自评") {
              item.children = [
                {
                  name: "自查自评历史",
                  path: "/zczpls",
                },
              ];
            }
          });
        }
      });
      PubSub.subscribe("dataFh", (msg, data) => {
        if (data == "fh") {
          this.dwjy = true;
          this.enumShowList.find((item) => {
            if (item.name == "自查自评") {
              item.children = [
                {
                  name: "自查自评历史",
                  path: "/zczpls",
                },
                {
                  name: "继续自查自评",
                  path: "/jxzczp",
                },
                {
                  name: "新建自查自评",
                  path: "/xjzczp",
                },
              ];
            }
          });
        }
      });
      let enumList = JSON.parse(JSON.stringify(this.enumList));
      // let enumList = this.enumLists
      console.log(this.enumList);
      console.log("菜单逻辑 构建菜单 enumList old", enumList);
      this.recursiveTree(enumList);
      // 递归完成，再次筛选一次根节点
      let len = enumList.length - 1;
      for (let i = len; i >= 0; i--) {
        let children = enumList[i];
        if (
          children &&
          (children.children === undefined ||
            children.children == [] ||
            children.children.length <= 0)
        ) {
          // console.log('找到了', children)
          enumList.splice(i, 1);
        }
      }
      this.enumShowList = enumList;
      // this.enumShowList[0].icon = 'el-icon-eleme'
      // this.enumShowList[1].icon = 'el-icon-phone'
      // this.enumShowList[2].icon = 'el-icon-user-solid'
      // this.enumShowList[3].icon = 'el-icon-eleme'
      // this.enumShowList[4].icon = 'el-icon-eleme'
      // this.enumShowList[5].icon = 'el-icon-eleme'
      // this.enumShowList[6].icon = 'el-icon-eleme'
      // this.enumShowList[7].icon = 'el-icon-eleme'
      console.log(this.enumShowList);
      // console.log('菜单逻辑 构建菜单结果', this.enumShowList)
    },
    // 树递归方法开始
    // 优先修剪叶子，没有叶子就剪去树枝，没有树枝则砍去树干
    recursiveTree(treeList) {
      // console.log('treeList', JSON.parse(JSON.stringify(treeList)))

      let len = treeList.length - 1;

      for (let i = len; i >= 0; i--) {
        let item = treeList[i];
        let itemChildren = item.children;
        if (itemChildren && itemChildren != []) {
          this.recursiveTree(itemChildren);
        } else {
          // console.log(item.name, item.path, this.showRoutePathList.indexOf(item.path))
          if (this.showRoutePathList.indexOf(item.path) == -1) {
            treeList.splice(i, 1);
          }
        }
      }
    },
    // 树递归方法结束
  },
  mounted() {
    this.submenu();
    console.log(this.$store.default.state.Counter);
  },
  watch: {
    "$store.default.state.Counter": {
      handler(newVal, oldVal) {
        console.log(
          "菜单逻辑 aside显隐发生变化",
          newVal,
          newVal.elAsideMenuList
        );
        if (newVal.elAsideMenuList) {
          // 更新菜单数据
          this.showRoutePathList = newVal.elAsideMenuList;
          // 重构菜单
          this.menuBuild();
        }
        // if (!newVal.elAsideIsShow) {
        //   // 重置菜单数据
        //   this.showRoutePathList = []
        //   // console.log('重置菜单数据', this.enumActive, newVal.elAsideIsShow)
        // }
      },
      deep: true,
    },
    showRoutePathList(newVal, oldVal) {
      // console.log('菜单逻辑 showRoutePathList changed', newVal)
      // console.log('准备跳转默认路由')
      // 默认打开第一个菜单
      // this.$router.push(newVal.elAsideMenuList[0])
      // console.log('菜单逻辑 默认路由', newVal[0], this.$route.path, oldVal)
      this.$nextTick(() => {
        /**
         * 特别注意：
         * 菜单的跳转不再在这儿去处理
         * 以下注释代码请勿解开
         */
        // this.$router.push(newVal[0])
        // if (this.$route.path == '/ztqksy') {
        //   this.$router.push(newVal[0])
        // } else {
        //   this.$router.push(this.$route.path)
        // }
        // 回抛新菜单
        this.$emit("getNewMenuList", newVal);
      });
    },
    $route: {
      handler(to, from) {
        // console.log('handleSelect 路由变更', ['0', to.path])
        // 确认路由在左侧菜单的位置及其父菜单索引
        let target = [];
        this.enumShowList.forEach((item, index) => {
          item.children.forEach((childItem) => {
            if (childItem.path == to.path) {
              target[0] = index;
              target[1] = childItem;
            }
          });
        });
        console.log("target", target);
        //
        this.handleSelect(to.path, ["0", to.path]);
      },
    },
  },
};
</script>


<style scoped>
.icon-01 {
  /* background-image: url(../../assets/icons/left_icon1_a.png); */
  background-image: url(../../assets/icons/s-icon-17.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-01-selected {
  background-image: url(../../assets/icons/s-icon-17.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-02 {
  background-image: url(../../assets/icons/s-icon-18.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-02-selected {
  background-image: url(../../assets/icons/s-icon-18.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-03 {
  background-image: url(../../assets/icons/s-icon-20.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-03-selected {
  background-image: url(../../assets/icons/s-icon-20.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-04 {
  background-image: url(../../assets/icons/s-icon-22.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-04-selected {
  background-image: url(../../assets/icons/s-icon-22.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-05 {
  background-image: url(../../assets/icons/s-icon-23.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-05-selected {
  background-image: url(../../assets/icons/s-icon-23.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-06 {
  background-image: url(../../assets/icons/s-icon-24.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-06-selected {
  background-image: url(../../assets/icons/s-icon-24.png);
  background-repeat: no-repeat;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-07 {
  background-image: url(../../assets/icons/s-icon-21.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-07-selected {
  background-image: url(../../assets/icons/s-icon-21.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-08 {
  background-image: url(../../assets/icons/s-icon-25.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-08-selected {
  background-image: url(../../assets/icons/s-icon-25.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-zczp {
  background-image: url(../../assets/icons/zczp.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-zczp-selected {
  background-image: url(../../assets/icons/zczp.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-sjrz {
  background-image: url(../../assets/icons/s-icon-24.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-sjrz-selected {
  background-image: url(../../assets/icons/s-icon-24.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

/**菜单样式**/
:deep(.el-menu-item).is-active {
  /* background: rgb(217, 236, 255) !important; */
  background: #084daa !important;
}

:deep(.el-menu) .el-menu-item:hover {
  /* background: rgb(217, 236, 255) !important; */
  background: #084daa !important;
}

/* //对应子级，父级的样式 */
:deep(.el-submenu).is-active > .el-submenu__title {
  background: #115bc1 !important;
  /* background: var(--background) !important; */
  color: white !important;
}

>>> .el-submenu__title {
  font-size: 18px !important;
}

:deep(.el-submenu).is-active > .el-submenu__title span {
  /* font-size: 20px; */
}

:deep(.el-submenu__title):hover {
  /* background: rgb(160, 207, 255) !important; */
  background: #115bc1 !important;
}

:deep(.el-submenu__title) i {
  color: white;
}

/***/
.xtb {
  display: inline-block;
  width: 7px;
  height: 7px;
  margin-right: 10px;
  background: white;
}

.xtb-selected {
  display: inline-block;
  width: 7px;
  height: 7px;
  margin-right: 10px;
  background: white;
}
</style>
