<template>
  <div>
    <hsoft_top_title>
      <template #left>关于我们</template>
    </hsoft_top_title>
    <!---->
    <div class="Alert">
      <div class="dialogAlert">
        <img src="./images/hsoftLogo.png" alt="">
        <div class="alertFontDiv">
          <p class="p1"></p>
          <p class="p2">联系人：张先生</p>
          <p class="p2">手机号码：18545181691</p>
          <p class="p2">联系电话：0451-88070960</p>
          <p class="p2">联系地址：哈尔滨市松北区创新路1599号</p>
        </div>
        <div class="alertFooter">
          <p class="p1">版权所有 哈尔滨思和信息技术股份有限公司</p>
          <div class="footerErweima">
            <img src="./images/hsoftMa.png" alt="">
            <p>思和信息</p>
            <p>微信服务公众号</p>
          </div>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

export default {
  data() {
    return {}
  },
  components: {
    hsoft_top_title
  },
  methods: {},
  mounted() {
  }
}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}

/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
}

.out-card .out-card-div div {
  padding: 10px 5px;
  display: flex;
}

.out-card .dwxx div:hover {
  background: #f4f4f5;
  border-radius: 20px;
}

.out-card .dwxx div label {
  /* background-color: red; */
  width: 125px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}

.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  flex: 1;
  display: inline-block;
  padding-left: 20px;
}

/**操作区域**/
.out-card .user-options {
  /* background: red; */
  height: 500px;
  text-align: center;
}

.out-card .card-option {
  width: 150px;
  height: 150px;
  background: var(--background);
  font-weight: 700;
  opacity: 0.85;
  display: inline-block;
}

.out-card .card-option:hover {
  cursor: pointer;
  opacity: 1;
}

.Alert {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 700px;
}

.dialogAlert {
  /* margin: auto 100px; */
  width: 1000px;
  height: 470px;
  background-image: url("./images/alert1.png");
  background-size: 100% 100%;
}

.dialogAlert img {
  margin-top: 10px;
}

.alertFontDiv {
  width: 100%;
  height: 280px;
  /*text-align: center;*/
  font-size: 20px;
}

.alertFontDiv .p1 {
  font-size: 20px;
  padding-top: 80px;
  padding-left: 130px;
  padding-bottom: 50px;
  color: #000000;
}

.alertFontDiv .p2 {
  font-size: 16px;
  padding-left: 400px;
  color: #000000;
}

.alertFooter {
  height: calc(100% - 345px);
  position: relative;
}

.alertFooter .p1 {
  font-size: 12px;
  color: #4472C4;
  font-weight: 600;
  position: absolute;
  bottom: 10px;
  left: 10px;
}

.alertFooter .footerErweima {
  position: absolute;
  right: 10px;
  bottom: 10px;
}

.alertFooter .footerErweima img {
  margin-left: 12px;
  width: 60px;
}

.alertFooter .footerErweima p {
  font-size: 12px;
  text-align: center;
}
</style>