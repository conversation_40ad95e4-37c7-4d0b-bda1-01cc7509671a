{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/ztqsdjTable.vue", "webpack:///./src/renderer/view/rcgz/ztqsdjTable.vue?2555", "webpack:///./src/renderer/view/rcgz/ztqsdjTable.vue", "webpack:///./src/api/ztqsdj.js"], "names": ["ztqsdjTable", "components", "props", "data", "_ref", "value1", "loading", "ry<PERSON><PERSON>ose", "bm", "xm", "gwmclist", "smdjxz", "regionOption", "page", "pageSize", "radioIdSelect", "ryDatas", "total", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "headerCellStyle", "background", "color", "tjlist", "xqr", "szbm", "wcqsrq", "ztqsdjList", "wjm", "yt", "jsdw", "qsdd", "mddd", "fhcs", "jtgj", "jtlx", "xdmmd", "xdr", "xmjl", "fwdyid", "dis", "dialogVisible_scyl", "scylImageUrl", "ryInfo", "sltshow", "routeType", "pdfBase64", "fileList", "dialogVisible", "approvalDialogVisible", "fileRow", "disabled2", "upjlid", "j<PERSON>", "slid", "type", "file", "filename", "defineProperty_default", "computed", "mounted", "console", "log", "this", "$route", "query", "list", "ztid", "spxx", "jsList", "JSON", "parse", "stringify_default", "datas", "xglist", "xgxx", "methods", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "zt", "ztqd", "wrap", "_context", "prev", "next", "yj<PERSON>", "Object", "ztjs", "sent", "stop", "_this2", "_callee2", "_context2", "ztqsdj", "spdsmj", "_this3", "_callee3", "params", "_context3", "for<PERSON>ach", "item", "lx", "smmj", "blobToBase64", "blob", "callback", "fileReader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "scwj", "_this4", "name", "URL", "createObjectURL", "dataurl", "split", "yl", "zpxx", "zpzm", "zp", "iamgeBase64", "_validDataUrl", "s", "regex", "test", "save", "_this5", "_callee4", "wfqsrq", "wfjzrq", "qsrq", "sfqsdj", "resDatas", "_context4", "qsr", "moment", "zfdw", "jtxl", "yjr", "cdr", "code", "sfqs", "splx", "api", "$router", "push", "$message", "message", "ztwcxd", "returnIndex", "watch", "rcgz_ztqsdjTable", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "ref", "attrs", "model", "label-width", "_v", "border", "header-cell-style", "stripe", "width", "align", "prop", "placeholder", "clearable", "disabled", "$$v", "$set", "staticStyle", "format", "value-format", "margin-right", "action", "show-file-list", "http-request", "slot", "on", "click", "visible", "update:visible", "$event", "src", "alt", "size", "plain", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__", "d", "addZtqsdj", "removeZtQsdj", "updateZtqsdjByJlid", "selectZtQsdjPage", "exportZtqsExcel", "getJlidBySlid", "getQsdjByJlid", "updateZtglWfcddj", "__WEBPACK_IMPORTED_MODULE_0__request__", "createAPI", "BASE_URL", "createDown"], "mappings": "wQAkFAA,wBACAC,cACAC,SACAC,KAHA,WAGA,IAAAC,EACA,OAAAA,GACAC,OAAA,GACAC,SAAA,EAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,YACAC,UACAC,gBACAC,KAAA,EACAC,SAAA,GACAC,cAAA,GACAC,WACAC,MAAA,EACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,iBACAC,WAAA,UACAC,MAAA,WAGAC,QACAC,IAAA,GACAC,KAAA,GACAC,OAAA,GACAC,cACAC,IAAA,GACAC,GAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,QACAC,QACAC,KAAA,GACAC,MAAA,GACAC,IAAA,GACAC,KAAA,IAEAC,OAAA,GACAC,KAAA,EACAC,oBAAA,EACAC,aAAA,GACAf,cACAgB,UAEAC,QAAA,GACAC,UAAA,GACAC,UAAA,GACAC,YACAC,eAAA,EACAC,uBAAA,EACAC,QAAA,GACAC,WAAA,EACAC,OAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,QACAC,SAAA,IAlEAC,IAAA1D,EAAA,UAmEA,IAnEA0D,IAAA1D,EAAA,UAoEA,IApEA0D,IAAA1D,EAAA,aAAA0D,IAAA1D,EAAA,OAsEA,IAtEAA,GAyEA2D,YACAC,QA9EA,WA+EAC,QAAAC,IAAAC,KAAAC,OAAAC,MAAAC,MACAH,KAAAR,KAAAQ,KAAAC,OAAAC,MAAAV,KACAM,QAAAC,IAAA,aAAAC,KAAAR,MAEAQ,KAAAxB,OAAAwB,KAAAC,OAAAC,MAAA1B,OACAsB,QAAAC,IAAA,cAAAC,KAAAxB,QACAwB,KAAAT,KAAAS,KAAAC,OAAAC,MAAAX,KACAS,KAAAI,KAAAJ,KAAAC,OAAAC,MAAAE,KACAN,QAAAC,IAAA,YAAAC,KAAAT,MACAS,KAAAV,KAAAU,KAAAC,OAAAC,MAAAZ,KAIA,OAAAU,KAAAR,KACAQ,KAAAK,OACA,UAAAL,KAAAR,MACAQ,KAAAM,OAAAC,KAAAC,MAAAC,IAAAT,KAAAC,OAAAC,MAAAQ,QACAZ,QAAAC,IAAA,cAAAC,KAAAM,QACAN,KAAAW,SACAX,KAAAY,QACA,QAAAZ,KAAAR,OACAQ,KAAAW,SACAX,KAAAY,OACAZ,KAAAvB,KAAA,IAGAoC,SACAD,KADA,WACA,IAAAE,EAAAd,KAAA,OAAAe,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAA7B,EAAA8B,EAAAC,EAAA,OAAAL,EAAAC,EAAAK,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACAnC,EAAAwB,EAAAxB,KACAQ,QAAAC,IAAAT,GACA8B,GACAM,MAAApC,GAJAiC,EAAAE,KAAA,EAMAE,OAAAC,EAAA,EAAAD,CAAAP,GANA,OAMAC,EANAE,EAAAM,KAOAf,EAAAlD,WAAAyD,EAPA,wBAAAE,EAAAO,SAAAX,EAAAL,KAAAC,IASAJ,OAVA,WAUA,IAAAoB,EAAA/B,KAAA,OAAAe,IAAAC,EAAAC,EAAAC,KAAA,SAAAc,IAAA,IAAA1C,EAAAtD,EAAA,OAAAgF,EAAAC,EAAAK,KAAA,SAAAW,GAAA,cAAAA,EAAAT,KAAAS,EAAAR,MAAA,cACAnC,EAAAyC,EAAAzC,KADA2C,EAAAR,KAAA,EAEAE,OAAAO,EAAA,EAAAP,EACArC,SAHA,OAEAtD,EAFAiG,EAAAJ,KAKAE,EAAAvE,OAAAxB,EACA+F,EAAAtC,KAAAsC,EAAAvE,OAAA2E,OACArC,QAAAC,IAAAgC,EAAAtC,MAPA,wBAAAwC,EAAAH,SAAAE,EAAAD,KAAAhB,IAUAV,KApBA,WAoBA,IAAA+B,EAAApC,KAAA,OAAAe,IAAAC,EAAAC,EAAAC,KAAA,SAAAmB,IAAA,IAAAC,EAAAtG,EAAAoF,EAAAC,EAAA,OAAAL,EAAAC,EAAAK,KAAA,SAAAiB,GAAA,cAAAA,EAAAf,KAAAe,EAAAd,MAAA,cACAa,GACA/C,KAAA6C,EAAA7C,MAFAgD,EAAAd,KAAA,EAIAE,OAAAO,EAAA,EAAAP,CAAAW,GAJA,cAIAtG,EAJAuG,EAAAV,KAKA/B,QAAAC,IAAA/D,GACAoG,EAAA/C,OAAArD,EACA8D,QAAAC,IAAAqC,EAAA/C,QACA+B,GACAM,MAAAU,EAAA/C,QATAkD,EAAAd,KAAA,GAWAE,OAAAC,EAAA,EAAAD,CAAAP,GAXA,QAWAC,EAXAkB,EAAAV,KAYAO,EAAAxE,WAAAyD,EACAe,EAAA5E,OAAAO,KAAAqE,EAAAxE,WAAA,GAAAG,KACAqE,EAAA3C,KAAA2C,EAAA5E,OAAA2E,OACArC,QAAAC,IAAAqC,EAAA3C,MACA2C,EAAAxE,WAAA4E,QAAA,SAAAC,GACA3C,QAAAC,IAAA0C,GACA,GAAAA,EAAAC,GACAD,EAAAC,GAAA,MACA,GAAAD,EAAAC,GACAD,EAAAC,GAAA,KACA,GAAAD,EAAAC,KACAD,EAAAC,GAAA,QAEA,GAAAD,EAAAE,KACAF,EAAAE,KAAA,KACA,GAAAF,EAAAE,KACAF,EAAAE,KAAA,KACA,GAAAF,EAAAE,KACAF,EAAAE,KAAA,KACA,GAAAF,EAAAE,OACAF,EAAAE,KAAA,QAhCA,yBAAAJ,EAAAT,SAAAO,EAAAD,KAAArB,IAoCA6B,aAxDA,SAwDAC,EAAAC,GACA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAA,SAAAC,GACAJ,EAAAI,EAAAC,OAAAC,SAEAL,EAAAM,cAAAR,IAGAS,KAhEA,SAgEAb,GAAA,IAAAc,EAAAvD,KAEAA,KAAAN,SAAA+C,EAAAhD,KAAA+D,KAEAxD,KAAAxC,OAAAK,IAAAmC,KAAAN,SACAM,KAAAnB,QAAA4E,IAAAC,gBAAAjB,EAAAhD,MACAO,KAAAb,QAAAsD,EAAAhD,KACAO,KAAA4C,aAAAH,EAAAhD,KAAA,SAAAkE,GACAJ,EAAA9D,KAAAkE,EAAAC,MAAA,QACA9D,QAAAC,IAAAwD,EAAA9D,SAGAoE,GA5EA,WA6EA,UAAA7D,KAAAR,KACAQ,KAAArB,aAAA8E,IAAAC,gBAAA1D,KAAAb,aACA,CACA,IAAA2E,EACAA,EAAA9D,KAAA+D,KAAA/D,KAAAP,MACAK,QAAAC,IAAA+D,GACA9D,KAAArB,aAAAmF,EAGA9D,KAAAtB,oBAAA,GAYAqF,KAlGA,SAkGAC,GACA,IAAAC,EAAA,0BAAAD,EACAF,OAAA,EACA,oBAAAG,EAAA,KAGAC,EAAA,SAAAA,EAAAC,GACA,OAAAD,EAAAE,MAAAC,KAAAF,IAFA,IAAAF,EAAA,OAMA,GAFAC,EAAAE,MACA,6GACAF,EAAAD,GAAA,CAKAH,EAEAG,GAGA,OAAAH,GAGAQ,KA1HA,WA0HA,IAAAC,EAAAvE,KAAA,OAAAe,IAAAC,EAAAC,EAAAC,KAAA,SAAAsD,IAAA,IAAAlC,EAAAmC,EAAAC,EAAAC,EAAA3I,EAAA4I,EAAAC,EAAA,OAAA7D,EAAAC,EAAAK,KAAA,SAAAwD,GAAA,cAAAA,EAAAtD,KAAAsD,EAAArD,MAAA,UACA3B,QAAAC,IAAAwE,EAAA/G,OAAAmH,OAEArC,MACAzE,IAAA0G,EAAA/G,OAAAK,IACAyE,EAAAvE,KAAAwG,EAAA/G,OAAAO,KACAuE,EAAAyC,IAAAR,EAAA/G,OAAAuH,IACAzC,EAAAlC,KAAAmE,EAAAnE,KAKAmE,EAAA3G,WAAA4E,QAAA,SAAAC,GACA,OAAAA,EAAAC,GACAD,EAAAC,GAAA,EACA,MAAAD,EAAAC,GACAD,EAAAC,GAAA,EACA,QAAAD,EAAAC,KACAD,EAAAC,GAAA,GAEA,MAAAD,EAAAE,KACAF,EAAAE,KAAA,EACA,MAAAF,EAAAE,KACAF,EAAAE,KAAA,EACA,MAAAF,EAAAE,KACAF,EAAAE,KAAA,EACA,MAAAF,EAAAE,OACAF,EAAAE,KAAA,KAGA,OAAA4B,EAAA/E,KA9BA,CAAAsF,EAAArD,KAAA,gBA+BAgD,EAAA9C,OAAAqD,EAAA,EAAArD,CAAA4C,EAAAjE,OAAAmE,QACAC,EAAA/C,OAAAqD,EAAA,EAAArD,CAAA4C,EAAAjE,OAAAoE,QACAC,EAAAhD,OAAAqD,EAAA,EAAArD,CAAA4C,EAAA/G,OAAAmH,MAEA7E,QAAAC,IAAA0E,GACA3E,QAAAC,IAAA2E,GACApC,EAAAmC,SACAnC,EAAAoC,SACApC,EAAAH,OAAAoC,EAAA9E,KACA6C,EAAA7E,IAAA8G,EAAAjE,OAAA7C,IACA6E,EAAA5E,KAAA6G,EAAAjE,OAAA5C,KACA4E,EAAAxE,GAAAyG,EAAAjE,OAAAxC,GACAwE,EAAA2C,KAAAV,EAAAjE,OAAA2E,KACA3C,EAAAtE,KAAAuG,EAAAjE,OAAAtC,KACAsE,EAAArE,KAAAsG,EAAAjE,OAAArC,KACAqE,EAAApE,KAAAqG,EAAAjE,OAAApC,KACAoE,EAAAnE,KAAAoG,EAAAjE,OAAAnC,KACAmE,EAAA4C,KAAAX,EAAAjE,OAAA4E,KACA5C,EAAA6C,IAAAZ,EAAAjE,OAAA6E,IACA7C,EAAA8C,IAAAb,EAAAjE,OAAA8E,IACA9C,EAAA/D,KAAAgG,EAAAjE,OAAA/B,KACA+D,EAAAqC,OApDAG,EAAArD,KAAA,GAqDAE,OAAAO,EAAA,EAAAP,CAAAW,GArDA,WAsDA,MADAtG,EArDA8I,EAAAjD,MAsDAwD,KAtDA,CAAAP,EAAArD,KAAA,gBAuDAmD,GACAU,KAAA,EACAhG,KAAAiF,EAAAjE,OAAAhB,MAzDAwF,EAAArD,KAAA,GA2DAE,OAAAO,EAAA,EAAAP,CAAAiD,GA3DA,WA4DA,KA5DAE,EAAAjD,KA4DAwD,KA5DA,CAAAP,EAAArD,KAAA,gBA6DA8C,EAAA3G,WAAA4E,QAAA,SAAAC,GACAA,EAAA8C,KAAA,EACA9C,EAAAf,MAAA1F,SA/DA8I,EAAArD,KAAA,GAiEAE,OAAA6D,EAAA,IAAA7D,CAAA4C,EAAA3G,YAjEA,QAkEA,KAlEAkH,EAAAjD,KAkEAwD,OACAd,EAAAkB,QAAAC,KAAA,aACAnB,EAAAoB,UACAC,QAAA,OACApG,KAAA,aAtEA,QAAAsF,EAAArD,KAAA,oBA2EA,UAAA8C,EAAA/E,KA3EA,CAAAsF,EAAArD,KAAA,gBA4EAa,EAAAiC,EAAA/G,QACA2E,OAAAoC,EAAA9E,KACA6C,EAAAhD,KAAAiF,EAAAjF,KA9EAwF,EAAArD,KAAA,GA+EAE,OAAAO,EAAA,EAAAP,CAAAW,GA/EA,WAgFA,MADAuC,EA/EAC,EAAAjD,MAgFAwD,KAhFA,CAAAP,EAAArD,KAAA,gBAiFA8C,EAAA3G,WAAA4E,QAAA,SAAAC,GACAA,EAAA8C,KAAA,EACA9C,EAAAf,MAAAmD,EAAA7I,OAnFA8I,EAAArD,KAAA,GAqFAE,OAAAkE,EAAA,EAAAlE,EAAAD,MAAA6C,EAAA/G,OAAA8B,OArFA,WAsFA,KAtFAwF,EAAAjD,KAsFAwD,KAtFA,CAAAP,EAAArD,KAAA,gBAAAqD,EAAArD,KAAA,GAuFAE,OAAA6D,EAAA,IAAA7D,CAAA4C,EAAA3G,YAvFA,QAwFA,KAxFAkH,EAAAjD,KAwFAwD,OACAd,EAAAkB,QAAAC,KAAA,aACAnB,EAAAoB,UACAC,QAAA,OACApG,KAAA,aA5FA,yBAAAsF,EAAAhD,SAAA0C,EAAAD,KAAAxD,IAoGA+E,YA9NA,WA+NA9F,KAAAyF,QAAAC,KAAA,eAGAK,WC1ZeC,GADEC,OAFjB,WAA0B,IAAAC,EAAAlG,KAAamG,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAa/C,KAAA,UAAAgD,QAAA,YAAAvJ,MAAAiJ,EAAA,QAAAO,WAAA,YAA4EC,YAAA,kBAA8BL,EAAA,OAAYK,YAAA,uBAAiCL,EAAA,WAAgBM,IAAA,WAAAC,OAAsBC,MAAAX,EAAA1I,OAAAsJ,cAAA,WAA0CT,EAAA,KAAUK,YAAA,cAAwBR,EAAAa,GAAA,cAAAb,EAAAa,GAAA,KAAAV,EAAA,YAAkDK,YAAA,eAAAE,OAAkCI,OAAA,GAAAhL,KAAAkK,EAAAtI,WAAAqJ,qBAAuD3J,WAAA,UAAAC,MAAA,WAA0C2J,OAAA,MAAcb,EAAA,mBAAwBO,OAAOpH,KAAA,QAAA2H,MAAA,KAAAnK,MAAA,KAAAoK,MAAA,YAA2DlB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOS,KAAA,OAAArK,MAAA,UAA8BkJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOS,KAAA,OAAArK,MAAA,UAA8BkJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOS,KAAA,OAAArK,MAAA,UAA8BkJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOS,KAAA,KAAArK,MAAA,UAA4BkJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOS,KAAA,OAAArK,MAAA,QAA4BkJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOS,KAAA,OAAArK,MAAA,UAA8BkJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOS,KAAA,KAAArK,MAAA,WAA6BkJ,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOS,KAAA,KAAArK,MAAA,SAA0B,GAAAkJ,EAAAa,GAAA,KAAAV,EAAA,OAA4BK,YAAA,kBAA4BL,EAAA,gBAAqBO,OAAO5J,MAAA,UAAgBqJ,EAAA,YAAiBO,OAAOU,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CX,OAAQ5J,MAAAiJ,EAAA1I,OAAA,KAAAsF,SAAA,SAAA2E,GAAiDvB,EAAAwB,KAAAxB,EAAA1I,OAAA,OAAAiK,IAAkChB,WAAA,kBAA2B,OAAAP,EAAAa,GAAA,KAAAV,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBO,OAAO5J,MAAA,WAAiBqJ,EAAA,YAAiBO,OAAOU,YAAA,GAAAE,SAAAtB,EAAAzH,IAAA8I,UAAA,IAAmDV,OAAQ5J,MAAAiJ,EAAA1I,OAAA,IAAAsF,SAAA,SAAA2E,GAAgDvB,EAAAwB,KAAAxB,EAAA1I,OAAA,MAAAiK,IAAiChB,WAAA,iBAA0B,GAAAP,EAAAa,GAAA,KAAAV,EAAA,gBAAqCO,OAAO5J,MAAA,UAAgBqJ,EAAA,kBAAuBsB,aAAaR,MAAA,QAAeP,OAAQY,SAAAtB,EAAAzH,IAAAmJ,OAAA,aAAAC,eAAA,aAAArI,KAAA,OAAA8H,YAAA,QAAwGT,OAAQ5J,MAAAiJ,EAAA1I,OAAA,KAAAsF,SAAA,SAAA2E,GAAiDvB,EAAAwB,KAAAxB,EAAA1I,OAAA,OAAAiK,IAAkChB,WAAA,kBAA2B,OAAAP,EAAAa,GAAA,KAAAV,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBO,OAAO5J,MAAA,YAAkBqJ,EAAA,YAAiBO,OAAOU,YAAA,GAAAE,SAAAtB,EAAAzH,IAAA8I,UAAA,IAAmDV,OAAQ5J,MAAAiJ,EAAA1I,OAAA,IAAAsF,SAAA,SAAA2E,GAAgDvB,EAAAwB,KAAAxB,EAAA1I,OAAA,MAAAiK,IAAiChB,WAAA,iBAA0B,GAAAP,EAAAa,GAAA,KAAAV,EAAA,aAAkCE,aAAa/C,KAAA,OAAAgD,QAAA,SAAAvJ,OAAAiJ,EAAAzH,IAAAgI,WAAA,SAAgEkB,aAAeG,eAAA,QAAsBlB,OAAQmB,OAAA,SAAAC,kBAAA,EAAAhM,QAAkDiM,eAAA/B,EAAA5C,QAA0B+C,EAAA,aAAkBO,OAAOsB,KAAA,UAAA1I,KAAA,WAAkC0I,KAAA,YAAgBhC,EAAAa,GAAA,aAAAb,EAAAa,GAAA,KAAAV,EAAA,aAAkDO,OAAOsB,KAAA,UAAA1I,KAAA,WAAkC2I,IAAKC,MAAAlC,EAAArC,IAAeqE,KAAA,YAAgBhC,EAAAa,GAAA,QAAAb,EAAAa,GAAA,KAAAV,EAAA,aAA6CO,OAAOyB,QAAAnC,EAAAxH,oBAAiCyJ,IAAKG,iBAAA,SAAAC,GAAkCrC,EAAAxH,mBAAA6J,MAAgClC,EAAA,OAAYsB,aAAaR,MAAA,QAAeP,OAAQ4B,IAAAtC,EAAAvH,aAAA8J,IAAA,MAAiCvC,EAAAa,GAAA,KAAAV,EAAA,OAAwBK,YAAA,gBAAAE,OAAmCsB,KAAA,UAAgBA,KAAA,WAAe7B,EAAA,aAAkBO,OAAO8B,KAAA,SAAeP,IAAKC,MAAA,SAAAG,GAAyBrC,EAAAxH,oBAAA,MAAiCwH,EAAAa,GAAA,mBAAAb,EAAAa,GAAA,KAAAV,EAAA,OAAkDK,YAAA,0CAAoDL,EAAA,aAAkBK,YAAA,UAAAE,OAA6B+B,MAAA,IAAWR,IAAKC,MAAAlC,EAAAJ,eAAyBI,EAAAa,GAAA,QAAAb,EAAAa,GAAA,KAAAV,EAAA,aAA6CE,aAAa/C,KAAA,OAAAgD,QAAA,SAAAvJ,OAAAiJ,EAAAzH,IAAAgI,WAAA,SAAgEC,YAAA,KAAAE,OAA0BpH,KAAA,WAAiB2I,IAAKC,MAAAlC,EAAA5B,QAAkB4B,EAAAa,GAAA,uBAEzmH6B,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACElN,EACAmK,GATF,EAVA,SAAAgD,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB,gEC1BhCC,EAAAI,EAAAD,EAAA,sBAAAE,IAAAL,EAAAI,EAAAD,EAAA,sBAAAG,IAAAN,EAAAI,EAAAD,EAAA,sBAAAI,IAAAP,EAAAI,EAAAD,EAAA,sBAAAK,IAAAR,EAAAI,EAAAD,EAAA,sBAAAM,IAAAT,EAAAI,EAAAD,EAAA,sBAAAO,IAAAV,EAAAI,EAAAD,EAAA,sBAAAQ,IAAAX,EAAAI,EAAAD,EAAA,sBAAAS,IAAA,IAAAC,EAAAb,EAAA,QAIaK,EAAY,SAAAnN,GAAA,OAAQ4N,YAAUC,IAAS,yBAA0B,OAAO7N,IAExEoN,EAAe,SAAApN,GAAA,OAAQ4N,YAAUC,IAAS,4BAA6B,OAAO7N,IAE9EqN,EAAqB,SAAArN,GAAA,OAAQ4N,YAAUC,IAAS,kCAAmC,OAAO7N,IAE1FsN,EAAmB,SAAAtN,GAAA,OAAQ4N,YAAUC,IAAS,gCAAiC,MAAM7N,IAErFuN,EAAkB,SAAAvN,GAAA,OAAQ8N,YAAWD,IAAS,+BAAgC,MAAM7N,IAEpFwN,EAAgB,SAAAxN,GAAA,OAAQ4N,YAAUC,IAAS,4BAA6B,MAAM7N,IAE9EyN,EAAgB,SAAAzN,GAAA,OAAQ4N,YAAUC,IAAS,6BAA8B,MAAM7N,IAC/E0N,EAAmB,SAAA1N,GAAA,OAAQ4N,YAAUC,IAAS,+BAAgC,OAAO7N", "file": "js/24.058e0a2a32659c1070d7.js", "sourcesContent": ["<template>\r\n  <div class=\"sec-container\" v-loading=\"loading\">\r\n    <!-- 标题 -->\r\n    <div class=\"sec-form-container\">\r\n      <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n        <!-- 载体详细信息start -->\r\n        <p class=\"sec-title\">载体签收详细信息</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ztqsdjList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"ztmc\" label=\"载体名称\"></el-table-column>\r\n          <el-table-column prop=\"xmbh\" label=\"项目编号\"></el-table-column>\r\n          <el-table-column prop=\"ztbh\" label=\"载体编号\"></el-table-column>\r\n          <el-table-column prop=\"lx\" label=\"载体类型\"></el-table-column>\r\n          <el-table-column prop=\"smmj\" label=\"密级\"></el-table-column>\r\n          <el-table-column prop=\"bmqx\" label=\"保密期限\"></el-table-column>\r\n          <el-table-column prop=\"ys\" label=\"页数/大小\"></el-table-column>\r\n          <el-table-column prop=\"fs\" label=\"份数\"></el-table-column>\r\n        </el-table>\r\n        <div class=\"sec-form-left\">\r\n          <el-form-item label=\"接收单位\">\r\n            <el-input placeholder=\"\" v-model=\"tjlist.jsdw\" clearable disabled></el-input>\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"sec-form-left\">\r\n          <el-form-item label=\"签 收 人\">\r\n            <el-input placeholder=\"\" :disabled=\"dis\" v-model=\"tjlist.qsr\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"签收日期\">\r\n            <el-date-picker v-model=\"tjlist.qsrq\" :disabled=\"dis\" style=\"width: 100%\" format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"sec-form-left\">\r\n          <el-form-item label=\"审批单扫描件\">\r\n            <el-input placeholder=\"\" :disabled=\"dis\" v-model=\"tjlist.wjm\" clearable></el-input>\r\n          </el-form-item>\r\n          <!-- <el-button type=\"primary\" :disabled=\"dis\" @click=\"upload()\">上传</el-button> -->\r\n          <el-upload action=\"/posts\" v-show=\"!dis\" style=\"margin-right: 10px\" :show-file-list=\"false\" :data=\"{}\"\r\n            :http-request=\"scwj\">\r\n            <el-button slot=\"trigger\" type=\"primary\">上 传</el-button>\r\n          </el-upload>\r\n          <el-button slot=\"trigger\" type=\"primary\" @click=\"yl\">预览</el-button>\r\n          <el-dialog :visible.sync=\"dialogVisible_scyl\">\r\n            <img :src=\"scylImageUrl\" alt=\"\" style=\"width: 100%\" />\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button size=\"small\" @click=\"dialogVisible_scyl = false\">取 消</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n        <!-- 载体详细信息end -->\r\n        <!-- 底部操作按钮start -->\r\n        <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n          <el-button @click=\"returnIndex\" class=\"fr ml10\" plain>返回</el-button>\r\n          <el-button @click=\"save\" v-show=\"!dis\" class=\"fr\" type=\"primary\">保存</el-button>\r\n        </div>\r\n        <!-- 底部操作按钮end -->\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  getZzjgList,\r\n  getLoginInfo,\r\n  getFwdyidByFwlx,\r\n  savaZtqdBatch,\r\n} from \"../../../api/index\";\r\nimport { submitDjgwbg, updateDjgwbg } from \"../../../api/djgwbg\";\r\nimport { getZtqdListByYjlid } from \"../../../api/ztjs\";\r\nimport { deleteZtqdByYjlid } from \"../../../api/ztwcxd\";\r\nimport {\r\n  addZtqsdj,\r\n  updateZtqsdjByJlid,\r\n  getJlidBySlid,\r\n  getQsdjByJlid,\r\n  updateZtglWfcddj,\r\n} from \"../../../api/ztqsdj\";\r\nimport { dateFormatNYR } from \"@/utils/moment\";\r\nimport { getAllGwxx } from \"../../../api/qblist\";\r\nimport { getAllSmdj } from \"../../../api/xlxz\";\r\nexport default {\r\n  components: {},\r\n  props: {},\r\n  data() {\r\n    return {\r\n      value1: \"\",\r\n      loading: false,\r\n      // 弹框人员选择条件\r\n      ryChoose: {\r\n        bm: \"\",\r\n        xm: \"\",\r\n      },\r\n      gwmclist: [],\r\n      smdjxz: [],\r\n      regionOption: [], // 部门下拉\r\n      page: 1, // 审批人弹框当前页\r\n      pageSize: 10, // 审批人弹框每页条数\r\n      radioIdSelect: \"\", // 审批人弹框人员单选\r\n      ryDatas: [], // 弹框人员选择\r\n      total: 0, // 弹框人员总数\r\n      regionParams: {\r\n        label: \"label\", //这里可以配置你们后端返回的属性\r\n        value: \"label\",\r\n        children: \"childrenRegionVo\",\r\n        expandTrigger: \"click\",\r\n        checkStrictly: true,\r\n      }, //地域信息配置参数\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: \"#EEF7FF\",\r\n        color: \"#4D91F8\",\r\n      },\r\n      // form表单提交数据\r\n      tjlist: {\r\n        xqr: \"\",\r\n        szbm: \"\",\r\n        wcqsrq: \"\",\r\n        ztqsdjList: [],\r\n        wjm: \"\",\r\n        yt: \"\",\r\n        jsdw: \"\",\r\n        qsdd: \"\",\r\n        mddd: \"\",\r\n        fhcs: [],\r\n        jtgj: [],\r\n        jtlx: \"\",\r\n        xdmmd: \"\",\r\n        xdr: \"\",\r\n        xmjl: \"\",\r\n      },\r\n      fwdyid: \"\",\r\n      dis: false,\r\n      dialogVisible_scyl: false,\r\n      scylImageUrl: \"\",\r\n      ztqsdjList: [],\r\n      ryInfo: {},\r\n      // 政治面貌下拉选项\r\n      sltshow: \"\", // 文档的缩略图显示\r\n      routeType: \"\",\r\n      pdfBase64: \"\",\r\n      fileList: [],\r\n      dialogVisible: false,\r\n      approvalDialogVisible: false, // 选择申请人弹框\r\n      fileRow: \"\",\r\n      disabled2: false,\r\n      upjlid: \"\",\r\n      jlid: \"\",\r\n      slid: \"\",\r\n      type: \"\",\r\n      file: {},\r\n      filename: \"\",\r\n      sltshow: \"\", // 文档的缩略图显示\r\n      fileRow: \"\",\r\n      jsList: {},\r\n      ztid: '',\r\n    };\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    console.log(this.$route.query.list);\r\n    this.type = this.$route.query.type;\r\n    console.log('this.type ', this.type);\r\n\r\n    this.fwdyid = this.$route.query.fwdyid;\r\n    console.log(\"this.fwdyid\", this.fwdyid);\r\n    this.slid = this.$route.query.slid;\r\n    this.ztid = this.$route.query.ztid;\r\n    console.log(\"this.slid\", this.slid);\r\n    this.jlid = this.$route.query.jlid;\r\n    // //审批信息初始化列表\r\n    // this.spxxxgcc()\r\n\r\n    if (this.type == \"add\") {\r\n      this.spxx();\r\n    } else if (this.type == \"updata\") {\r\n      this.jsList = JSON.parse(JSON.stringify(this.$route.query.datas));\r\n      console.log(\"this.jsList\", this.jsList);\r\n      this.xglist();\r\n      this.xgxx();\r\n    } else if (this.type == \"xqxx\") {\r\n      this.xglist();\r\n      this.xgxx();\r\n      this.dis = true;\r\n    }\r\n  },\r\n  methods: {\r\n    async xgxx() {\r\n      let jlid = this.jlid\r\n      console.log(jlid);\r\n      let zt = {\r\n        yjlid: jlid,\r\n      };\r\n      let ztqd = await getZtqdListByYjlid(zt);\r\n      this.ztqsdjList = ztqd;\r\n    },\r\n    async xglist() {\r\n      let jlid = this.jlid\r\n      let data = await getQsdjByJlid({\r\n        jlid: jlid,\r\n      });\r\n      this.tjlist = data;\r\n      this.file = this.tjlist.spdsmj;\r\n      console.log(this.file);\r\n    },\r\n    //审批信息\r\n    async spxx() {\r\n      let params = {\r\n        slid: this.slid,\r\n      };\r\n      let data = await getJlidBySlid(params);\r\n      console.log(data);\r\n      this.upjlid = data;\r\n      console.log(this.upjlid);\r\n      let zt = {\r\n        yjlid: this.upjlid,\r\n      };\r\n      let ztqd = await getZtqdListByYjlid(zt);\r\n      this.ztqsdjList = ztqd;\r\n      this.tjlist.jsdw = this.ztqsdjList[0].jsdw;\r\n      this.file = this.tjlist.spdsmj;\r\n      console.log(this.file);\r\n      this.ztqsdjList.forEach((item) => {\r\n        console.log(item);\r\n        if (item.lx == 1) {\r\n          item.lx = \"纸介质\";\r\n        } else if (item.lx == 2) {\r\n          item.lx = \"光盘\";\r\n        } else if (item.lx == 3) {\r\n          item.lx = \"电磁介质\";\r\n        }\r\n        if (item.smmj == 1) {\r\n          item.smmj = \"绝密\";\r\n        } else if (item.smmj == 2) {\r\n          item.smmj = \"机密\";\r\n        } else if (item.smmj == 3) {\r\n          item.smmj = \"秘密\";\r\n        } else if (item.smmj == 4) {\r\n          item.smmj = \"内部\";\r\n        }\r\n      });\r\n    },\r\n    blobToBase64(blob, callback) {\r\n      const fileReader = new FileReader();\r\n      fileReader.onload = (e) => {\r\n        callback(e.target.result);\r\n      };\r\n      fileReader.readAsDataURL(blob);\r\n    },\r\n    //上传文件\r\n    scwj(item) {\r\n      // this.file = item.file\r\n      this.filename = item.file.name;\r\n      // console.log(\"上传文件\", \"this.file\", this.file, \"this.filename\", this.filename);\r\n      this.tjlist.wjm = this.filename;\r\n      this.sltshow = URL.createObjectURL(item.file);\r\n      this.fileRow = item.file;\r\n      this.blobToBase64(item.file, (dataurl) => {\r\n        this.file = dataurl.split(\",\")[1];\r\n        console.log(this.file);\r\n      });\r\n    },\r\n    yl() {\r\n      if (this.type == \"add\") {\r\n        this.scylImageUrl = URL.createObjectURL(this.fileRow);\r\n      } else {\r\n        let zpxx;\r\n        zpxx = this.zpzm(this.file);\r\n        console.log(zpxx);\r\n        this.scylImageUrl = zpxx;\r\n        // this.scylImageUrl = this.sltshow\r\n      }\r\n      this.dialogVisible_scyl = true;\r\n      // if (this.routeType == 'add') {\r\n      //   this.scylImageUrl = URL.createObjectURL(this.fileRow)\r\n      // } else {\r\n      //   // this.scylImageUrl = URL.createObjectURL(this.tjlist.spdsmj)\r\n      //   // this.scylImageUrl = this.sltshow\r\n      //   let zpxx\r\n      //   zpxx = this.zpzm(this.tjlist.spdsmj)\r\n      //   this.scylImageUrl = zpxx\r\n      // }\r\n      // this.dialogVisible_scyl = true\r\n    },\r\n    zpzm(zp) {\r\n      const iamgeBase64 = \"data:image/jpeg;base64,\" + zp;\r\n      let zpxx;\r\n      if (typeof iamgeBase64 === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64)) {\r\n          // debugger;\r\n          // let that = this;\r\n\r\n          function previwImg(item) {\r\n            zpxx = item;\r\n          }\r\n          previwImg(iamgeBase64);\r\n        }\r\n      }\r\n      return zpxx;\r\n    },\r\n    // 保存\r\n    async save() {\r\n      console.log(this.tjlist.qsrq);\r\n      // let params = new FormData();\r\n      let params = {};\r\n      params.wjm = this.tjlist.wjm;\r\n      params.jsdw = this.tjlist.jsdw;\r\n      params.qsr = this.tjlist.qsr;\r\n      params.ztid = this.ztid;\r\n      // params.append('spdsmj', this.file)\r\n      // params.append('wjm', this.tjlist.wjm)\r\n      // params.append('jsdw', this.tjlist.jsdw);\r\n      // params.append('qsr', this.tjlist.jsdw);\r\n      this.ztqsdjList.forEach((item) => {\r\n        if (item.lx == \"纸介质\") {\r\n          item.lx = 1;\r\n        } else if (item.lx == \"光盘\") {\r\n          item.lx = 2;\r\n        } else if (item.lx == \"电磁介质\") {\r\n          item.lx = 3;\r\n        }\r\n        if (item.smmj == \"绝密\") {\r\n          item.smmj = 1;\r\n        } else if (item.smmj == \"机密\") {\r\n          item.smmj = 2;\r\n        } else if (item.smmj == \"秘密\") {\r\n          item.smmj = 3;\r\n        } else if (item.smmj == \"内部\") {\r\n          item.smmj = 4;\r\n        }\r\n      });\r\n      if (this.type == \"add\") {\r\n        let wfqsrq = dateFormatNYR(this.jsList.wfqsrq);\r\n        let wfjzrq = dateFormatNYR(this.jsList.wfjzrq);\r\n        let qsrq = dateFormatNYR(this.tjlist.qsrq);\r\n\r\n        console.log(wfqsrq);\r\n        console.log(wfjzrq);\r\n        params.wfqsrq = wfqsrq;\r\n        params.wfjzrq = wfjzrq;\r\n        params.spdsmj = this.file;\r\n        params.xqr = this.jsList.xqr;\r\n        params.szbm = this.jsList.szbm;\r\n        params.yt = this.jsList.yt;\r\n        params.zfdw = this.jsList.zfdw;\r\n        params.qsdd = this.jsList.qsdd;\r\n        params.mddd = this.jsList.mddd;\r\n        params.fhcs = this.jsList.fhcs;\r\n        params.jtgj = this.jsList.jtgj;\r\n        params.jtxl = this.jsList.jtxl;\r\n        params.yjr = this.jsList.yjr;\r\n        params.cdr = this.jsList.cdr;\r\n        params.xmjl = this.jsList.xmjl;\r\n        params.qsrq = qsrq;\r\n        let data = await addZtqsdj(params);\r\n        if (data.code == 10000) {\r\n          let sfqsdj = {\r\n            sfqs: 1,\r\n            jlid: this.jsList.jlid,\r\n          };\r\n          let data1 = await updateZtglWfcddj(sfqsdj);\r\n          if (data1.code == 10000) {\r\n            this.ztqsdjList.forEach((item) => {\r\n              item.splx = 9;\r\n              item.yjlid = data.data;\r\n            });\r\n            let sava = await savaZtqdBatch(this.ztqsdjList);\r\n            if (sava.code == 10000) {\r\n              this.$router.push(\"/smztqsdj\");\r\n              this.$message({\r\n                message: \"保存成功\",\r\n                type: \"success\",\r\n              });\r\n            }\r\n          }\r\n        }\r\n      } else if (this.type == \"updata\") {\r\n        params = this.tjlist;\r\n        params.spdsmj = this.file;\r\n        params.jlid = this.jlid;\r\n        let resDatas = await updateZtqsdjByJlid(params);\r\n        if (resDatas.code == 10000) {\r\n          this.ztqsdjList.forEach((item) => {\r\n            item.splx = 9;\r\n            item.yjlid = resDatas.data;\r\n          });\r\n          let del = await deleteZtqdByYjlid({ yjlid: this.tjlist.jlid });\r\n          if (del.code == 10000) {\r\n            let data = await savaZtqdBatch(this.ztqsdjList);\r\n            if (data.code == 10000) {\r\n              this.$router.push(\"/smztqsdj\");\r\n              this.$message({\r\n                message: \"保存成功\",\r\n                type: \"success\",\r\n              });\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // 返回\r\n    returnIndex() {\r\n      this.$router.push(\"/smztqsdj\");\r\n    },\r\n  },\r\n  watch: {},\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.sec-container {\r\n  width: 100%;\r\n  height: calc(100% - 50px);\r\n  overflow-y: overlay;\r\n}\r\n\r\n.sec-title {\r\n  border-left: 5px solid #1b72d8;\r\n  color: #1b72d8;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  text-indent: 10px;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n  /* width: 100%; */\r\n  border: 1px solid #cdd2d9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n  width: 258px;\r\n  position: absolute;\r\n  right: 0px;\r\n  top: 0;\r\n  height: 163px;\r\n  border: 1px solid #cdd2d9;\r\n  border-left: 0;\r\n  background: #ffffff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.sec-form-second {\r\n  /* width: 100%; */\r\n  border: 1px solid #cdd2d9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-third {\r\n  border: 1px solid #cdd2d9;\r\n  /* height: 40px;  */\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n/deep/.el-checkbox-group {\r\n  display: flex;\r\n  justify-content: center;\r\n  background-color: #f5f7fa;\r\n  border-right: 1px solid #cdd2d9;\r\n}\r\n\r\n.checkbox {\r\n  display: inline-block !important;\r\n  background-color: rgba(255, 255, 255, 0) !important;\r\n  border-right: none !important;\r\n}\r\n\r\n.sec-form-four {\r\n  border: 1px solid #cdd2d9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n  border: 1px solid #cdd2d9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  overflow: hidden;\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.yulan {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  color: #3874d5;\r\n  font-weight: 600;\r\n  float: left;\r\n  margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n  width: 178px;\r\n  height: 178px;\r\n  display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 178px;\r\n  height: 178px;\r\n  line-height: 178px;\r\n  text-align: center;\r\n  border: 2px solid #ebebeb;\r\n}\r\n\r\n.sec-form-six {\r\n  border: 1px solid #cdd2d9;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.ml10 {\r\n  margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n  margin-top: 10px;\r\n  border-right: 1px solid #cdd2d9;\r\n  background: #ffffff;\r\n}\r\n\r\n.sec-form-left-textarea {\r\n  height: 54px !important;\r\n}\r\n\r\n.sec-form-left-textarea>>>.el-form-item__label {\r\n  line-height: 54px !important;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n  border: none;\r\n}\r\n\r\n.sec-left-text {\r\n  float: left;\r\n  margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n  border-top: 1px solid #cdd2d9;\r\n}\r\n\r\n>>>.longLabel .el-form-item__label {\r\n  width: 500px !important;\r\n}\r\n\r\n>>>.longLabel .el-form-item__content {\r\n  margin-left: 500px !important;\r\n  padding-left: 20px;\r\n  border-right: 1px solid #cdd2d9;\r\n  background: #ffffff;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n  border-top: 0;\r\n} */\r\n.sec-form-second .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n  width: 100%;\r\n  border: 1px solid #ebeef5;\r\n  height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n.hyzk {\r\n  padding-left: 15px;\r\n  background-color: #f5f7fa;\r\n  width: calc(100% - 16px);\r\n  border-right: 1px solid #cdd2d9;\r\n  color: #c0c4cc;\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n  border: none !important;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n  width: 200px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n  border: none;\r\n  border-right: 1px solid #cdd2d9;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n  border: 1px solid #CDD2D9;;\r\n} */\r\n>>>.el-form-item__label {\r\n  border-right: 1px solid #cdd2d9;\r\n}\r\n\r\n/* /deep/.sec-form-container .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n} */\r\n.riq {\r\n  width: 100% !important;\r\n}\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/ztqsdjTable.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"sec-container\"},[_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('p',{staticClass:\"sec-title\"},[_vm._v(\"载体签收详细信息\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ztqsdjList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"ztmc\",\"label\":\"载体名称\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xmbh\",\"label\":\"项目编号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"ztbh\",\"label\":\"载体编号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"lx\",\"label\":\"载体类型\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"smmj\",\"label\":\"密级\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bmqx\",\"label\":\"保密期限\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"ys\",\"label\":\"页数/大小\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"fs\",\"label\":\"份数\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"接收单位\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.jsdw),callback:function ($$v) {_vm.$set(_vm.tjlist, \"jsdw\", $$v)},expression:\"tjlist.jsdw\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"签 收 人\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":_vm.dis,\"clearable\":\"\"},model:{value:(_vm.tjlist.qsr),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qsr\", $$v)},expression:\"tjlist.qsr\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"签收日期\"}},[_c('el-date-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"disabled\":_vm.dis,\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.tjlist.qsrq),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qsrq\", $$v)},expression:\"tjlist.qsrq\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"审批单扫描件\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":_vm.dis,\"clearable\":\"\"},model:{value:(_vm.tjlist.wjm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"wjm\", $$v)},expression:\"tjlist.wjm\"}})],1),_vm._v(\" \"),_c('el-upload',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.dis),expression:\"!dis\"}],staticStyle:{\"margin-right\":\"10px\"},attrs:{\"action\":\"/posts\",\"show-file-list\":false,\"data\":{},\"http-request\":_vm.scwj}},[_c('el-button',{attrs:{\"slot\":\"trigger\",\"type\":\"primary\"},slot:\"trigger\"},[_vm._v(\"上 传\")])],1),_vm._v(\" \"),_c('el-button',{attrs:{\"slot\":\"trigger\",\"type\":\"primary\"},on:{\"click\":_vm.yl},slot:\"trigger\"},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogVisible_scyl},on:{\"update:visible\":function($event){_vm.dialogVisible_scyl=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.scylImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogVisible_scyl = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"plain\":\"\"},on:{\"click\":_vm.returnIndex}},[_vm._v(\"返回\")]),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.dis),expression:\"!dis\"}],staticClass:\"fr\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.save}},[_vm._v(\"保存\")])],1)],1)],1)])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-202a9124\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/ztqsdjTable.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-202a9124\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ztqsdjTable.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ztqsdjTable.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ztqsdjTable.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-202a9124\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ztqsdjTable.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-202a9124\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/ztqsdjTable.vue\n// module id = null\n// module chunks = ", "import {createAPI, createFileAPI,createDown, createUploadAPI,BASE_URL} from './request'\r\n// var BASE_URL = '/api'\r\n// var BASE_URL = ''\r\n//1.添加载体签收登记记录\r\nexport const addZtqsdj = data => createAPI(BASE_URL+\"/ztgl/qscddj/addZtqsdj\", 'post',data)\r\n//删除载体签收登记记录\r\nexport const removeZtQsdj = data => createAPI(BASE_URL+\"/ztgl/qscddj/removeZtQsdj\", 'post',data)\r\n//修改载体签收登记记录\r\nexport const updateZtqsdjByJlid = data => createAPI(BASE_URL+\"/ztgl/qscddj/updateZtqsdjByJlid\", 'post',data)\r\n//查询载体签收登记带分页\r\nexport const selectZtQsdjPage = data => createAPI(BASE_URL+\"/ztgl/qscddj/selectZtQsdjPage\", 'get',data)\r\n//载体签收登记导出\r\nexport const exportZtqsExcel = data => createDown(BASE_URL+\"/ztgl/qscddj/exportZtqsExcel\", 'get',data)\r\n\r\nexport const getJlidBySlid = data => createAPI(BASE_URL+\"/ZtglWfcddj/getJlidBySlid\", 'get',data)\r\n\r\nexport const getQsdjByJlid = data => createAPI(BASE_URL+\"/ztgl/qscddj/getQsdjByJlid\", 'get',data)\r\nexport const updateZtglWfcddj = data => createAPI(BASE_URL+\"/ZtglWfcddj/updateZtglWfcddj\", 'post',data)\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/api/ztqsdj.js"], "sourceRoot": ""}