<template>
    <div class="sec-container">
        <el-tabs v-model="activeName">
            <el-tab-pane label="审批指南" name="first">
                <div class="sec-form-six haveBorderTop sec-footer">
                    <el-button @click="ljbl" class="fr" type="success">立即办理</el-button>
                </div>
                <el-table border class="sec-el-table" :data="spznList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                    <el-table-column prop="hjmc" label="办理流程"></el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="审批信息" name="second">
                <!-- 标题 -->
                <p class="sec-title">非密重点人员审批</p>
                <div class="sec-form-container">
                    <el-form ref="formName" :model="tjlist" label-width="225px">
                        <!-- 第一部分包括姓名到常住地公安start -->
                        <div class="sec-header-section">
                            <div class="sec-form-left">
                                <el-form-item label="姓名">
                                    <el-input placeholder="" v-model="tjlist.xm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="曾用名">
                                    <!-- <el-input placeholder="" v-model="tjlist.xb" clearable disabled></el-input> -->
                                    <template slot-scope="scope">
                                        <el-input placeholder="" v-model="tjlist.cym" clearable disabled></el-input>
                                    </template>
                                </el-form-item>
                                <el-form-item label="身份证号">
                                    <template slot-scope="scope">
                                        <el-input placeholder="" v-model="tjlist.sfzhm" clearable disabled></el-input>
                                    </template>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="性别" class="longLabel">
                                    <el-radio-group v-model="tjlist.xb" disabled>
                                        <el-radio v-for="item in xb" :v-model="tjlist.xb" :label="item.id" :value="item.id"
                                            :key="item.id">
                                            {{ item.xb }}</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="年龄">
                                    <el-input type="number" placeholder="" v-model="tjlist.nl" clearable
                                        oninput="value = value.replace(/[^0-9]/g,'' )" disabled></el-input>
                                </el-form-item>
                                <el-form-item label="民族">
                                    <template slot-scope="scope">
                                        <el-input placeholder="" v-model="tjlist.mz" clearable disabled></el-input>
                                    </template>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="籍贯">
                                    <el-input placeholder="" v-model="tjlist.jg" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="家庭住址">
                                    <el-input placeholder="" v-model="tjlist.jtdz" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="所在地派出所">
                                    <el-input placeholder="" v-model="tjlist.szdpcs" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="最高学历" class="longLabel">
                                    <el-radio-group v-model="tjlist.zgxl" disabled>
                                        <el-radio v-for="item in zgxl" :v-model="tjlist.zgxl" :label="item.id"
                                            :value="item.id" :key="item.id">
                                            {{ item.zgxl }}</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="工作地点">
                                    <el-input placeholder="" v-model="tjlist.gzdd" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="工作岗位">
                                    <el-input placeholder="" v-model="tjlist.gwmc" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="联系方式">
                                    <el-input placeholder="（详细）" v-model="tjlist.lxdh" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="是否审查" class="longLabel">
                                    <el-radio-group v-model="tjlist.sfsc" disabled>
                                        <el-radio v-for="item in sfsc" :v-model="tjlist.sfsc" :label="item.id"
                                            :value="item.id" :key="item.id">
                                            {{ item.sfsc }}</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </div>
                            <!-- 电子照片 -->
                            <div class="sec-header-pic">
                                <div>
                                    <img v-if="imageUrl" :src="imageUrl" class="avatarimg" style="">
                                </div>
                            </div>
                        </div>
                        <!-- 第一部分包括姓名到常住地公安end -->
                        <p class="sec-title">现实表现</p>
                        <div class="sec-form-third haveBorderTop">
                            <div class="sec-left-text">
                                <div>
                                    政治态度：热爱祖国，热爱社会主义，拥护党的方针、政策:<el-radio-group v-model="tjlist.zztd" disabled>
                                        <el-radio v-for="item in zztdlist" :v-model="tjlist.zztd" :label="item.id"
                                            :value="item.id" :key="item.id">{{ item.mc }}</el-radio>
                                    </el-radio-group>
                                </div>
                                <div style="margin-top: 10px;">思想状况：
                                    <el-radio-group v-model="tjlist.sxzk" disabled>
                                        <el-radio v-for="item in sszklist" :v-model="tjlist.sxzk" :label="item.id"
                                            :value="item.id" :key="item.id">{{ item.mc }}</el-radio>
                                    </el-radio-group>
                                </div>
                            </div>
                        </div>
                        <!-- 家庭成员及主要社会关系情况start -->
                        <p class="sec-title">家庭成员及主要社会关系情况</p>
                        <el-table border class="sec-el-table" :data="ryglRyscJtcyList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="gxms" label="与本人关系">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.gxms" placeholder="" disabled></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="xm" label="姓名">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.xm" placeholder="" disabled></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="jwjlqk" label="是否有外籍、境外居留权、长期居留许可">
                                <template slot-scope="scope">
                                    <!-- <el-input v-model="scope.row.jwjlqk" placeholder=""></el-input> -->
                                    <el-select v-model="scope.row.jwjlqk" placeholder="请选择" disabled>
                                        <el-option v-for="item in ynoptions" :key="item.value" :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column prop="cgszd" label="单位">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.cgszd" placeholder="" disabled></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="zzmm" label="政治面貌">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.zzmm" placeholder="请选择" disabled>
                                        <el-option v-for="item in zzmmoptions" :key="item.value" :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!-- 家庭成员及主要社会关系情况end -->
                        <!-- 本人承诺start -->
                        <p class="sec-title">本人承诺</p>
                        <div class="sec-form-five haveBorderTop" style="position: relative;">

                            <img v-if="sltshow" :src="sltshow" class="avatar">
                            <!-- <i v-else class="el-icon-plus avatar-uploader-icon"></i>  -->

                            <p v-if="sltshow" class="yulan" @click="yulan">预览</p>
                            <!-- 预览本人承诺扫描件 -->
                            <el-dialog :visible.sync="dialogVisible_brcn">
                                <img :src="imageUrlbrcn" alt="" style="width: 100%">
                                <div slot="footer" class="dialog-footer">
                                    <el-button size="small" @click="dialogVisible_brcn = false">取 消</el-button>
                                </div>
                            </el-dialog>
                        </div>
                        <p class="sec-title">人力审查意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmsc">
                                <el-radio v-model="tjlist.rlsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    :disabled="disabled1" :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="非密重点人员审查" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="人力审查意见" prop="bmspr">
                                <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
                                <el-input placeholder="" v-model="tjlist.rlscxm" clearable disabled></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmscrq">
                                <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
                                <el-date-picker :disabled="disabled1" v-model="tjlist.rlscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">保密办意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmsc">
                                <el-radio v-model="tjlist.bmbsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    :disabled="disabled2" :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="非密重点人员审查" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="保密办意见" prop="bmspr">
                                <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
                                <el-input placeholder="" v-model="tjlist.bmbscxm" clearable disabled></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmscrq">
                                <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
                                <el-date-picker :disabled="disabled2" v-model="tjlist.bmbscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <!-- <p class="sec-title">备注：涉密人员上岗审查、在岗复审均填本表</p> -->

                        <p class="sec-title">轨迹处理</p>
                        <el-table border class="sec-el-table" :data="gjclList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                            <el-table-column prop="clrid" label="办理人"></el-table-column>
                            <el-table-column prop="bllx" label="办理类型"></el-table-column>
                            <el-table-column prop="clyj" label="办理意见"></el-table-column>
                            <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                            <el-table-column prop="clsj" label="办理时间"></el-table-column>
                        </el-table>

                       
                    </el-form>
                </div>
                <!-- 涉密人员任用审查列表end -->
                <!-- 发起申请弹框start -->
                <el-dialog title="人员选择" :close-on-click-modal="false" :visible.sync="dialogVisible" width="40%">
                    <div class="dlFqsqContainer">
                        <label for="">部门:</label>
                        <el-input class="input1" v-model="formInline.bmmc" clearable placeholder="部门"></el-input>
                        <label for="">姓名:</label>
                        <el-input class="input2" v-model="formInline.xm" clearable placeholder="姓名"></el-input>
                        <el-button class="searchButton" type="primary" icon="el-icon-search"
                            @click="onSubmit">查询</el-button>
                        <el-table class="tb-container" ref="multipleTable" :data="smryList" border
                            :header-cell-style="headerCellStyle" stripe @selection-change="selectRow" @select="handleSelect"
                            @row-click="handleRowClick" height="300px">
                            <el-table-column type="selection" width="55" align="center"> </el-table-column>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="xm" label="姓名"></el-table-column>
                            <el-table-column prop="bmmc" label="部门"></el-table-column>
                            <el-table-column prop="gwmc" label="岗位"></el-table-column>
                        </el-table>
                        <el-pagination class="paginationContainer" background @current-change="handleCurrentChange"
                            @size-change="handleSizeChange" :pager-count="5" :current-page="page"
                            :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                            layout="total, prev, pager, sizes,next, jumper" :total="total">
                        </el-pagination>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" v-if="xsyc" @click="submit('formName')">确 定</el-button>
                        <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
                    </span>
                </el-dialog>
                <!-- 发起申请弹框end -->
            </el-tab-pane>
            <el-tab-pane label="流程跟踪" name="third">
                <el-table border class="sec-el-table" :data="lcgzList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                    <el-table-column prop="clrid" label="办理人"></el-table-column>
                    <el-table-column prop="bllx" label="办理类型"></el-table-column>
                    <el-table-column prop="clyj" label="办理意见"></el-table-column>
                    <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                    <el-table-column prop="clsj" label="办理时间"></el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>

    </div>
</template>
<script>
import {
    //审批指南
    getBlzn,
    //判断实例所处环节
    getSchj,
    //事项审核
    getSxsh,
    //查询审批用户列表
    getSpUserList,
    //非第一环节选择审批人
    tjclr,
    //流程跟踪
    getSpGjxx,

} from '../../../../api/wdgz'
import {
    getAllSmsbmj
} from '../../../../api/xlxz'
import {
    getJlidcssdsc,
    getCssdInfo,
    updateCssd
} from '../../../../api/cssdsc'
import {
    selectZdryglRyscByLcslid,
    selectZdryJtcy,
    updateZdryglRysc,
    updateZdry
} from '../../../../api/fmzdrydj'
import {
    saveCsdj
} from '../../../../api/index'
import {
    verifySfjshj
} from '../../../../api/djgwbg'
import { getUserInfo } from '../../../../api/dwzc'
import {
    zp
} from '../../../../utils/zpcl'
import AddLineTable from "../../../components/common/addLineTable.vue";   //人工纠错组件
export default {
    components: {
        AddLineTable
    },
    props: {},
    data() {
        return {
            imageUrl: '',
            dialogImageUrl: '',
            sltshow: '',
            scylImageUrl: '',
            xb: [{
                xb: '男',
                id: 1
            },
            {
                xb: '女',
                id: 2
            },
            ],
            sfsc: [{
                id: 1,
                sfsc: '是'
            },
            {
                id: 0,
                sfsc: '否'
            },
            ],
            zgxl: [
                {
                    id: 1,
                    zgxl: '研究生'
                },
                {
                    id: 2,
                    zgxl: '大学本科'
                },
                {
                    id: 3,
                    zgxl: '大学专科及以下'
                },
            ],
            zztdlist: [
                {
                    id: 1,
                    mc: '端正'
                },
                {
                    id: 2,
                    mc: '不端正'
                },
            ],
            sszklist: [
                {
                    id: 1,
                    mc: '稳定'
                },
                {
                    id: 2,
                    mc: '基本稳定'
                },
                {
                    id: 3,
                    mc: '不稳定'
                },
            ],
            ryglRyscJtcyList: [],
            dialogVisible_scyl: false,
            checkList: [],
            zzhmList: [
                {
                    zzid: 1,
                    fjlb: '信息输出专用红盘',
                    zjhm: '',
                    yxq: '',
                    checked: 0
                },
                {
                    zzid: 2,
                    fjlb: '信息输出专用单导盒',
                    zjhm: '',
                    yxq: '',
                    checked: 0
                },
                {
                    zzid: 3,
                    fjlb: '公司专用涉密信息输出机',
                    zjhm: '',
                    yxq: '',
                    checked: 0
                },
                {
                    zzid: 4,
                    fjlb: '其他',
                    zjhm: '',
                    yxq: '',
                    checked: 0
                }
            ],
            radio: '',
            // 载体详细信息
            ztqsQsscScjlList: [],
            sbmjxz: [],//设备密级
            ztlxList: [
                {
                    lxid: 1,
                    lxmc: '纸介质'
                },
                {
                    lxid: 2,
                    lxmc: '光盘'
                },
                {
                    lxid: 3,
                    lxmc: '电磁介质'
                },
            ],
            smdjList: [
                {
                    smdjid: 1,
                    smdjmc: '绝密'
                },
                {
                    smdjid: 2,
                    smdjmc: '机密'
                },
                {
                    smdjid: 3,
                    smdjmc: '秘密'
                },
                {
                    smdjid: 4,
                    smdjmc: '内部'
                },
            ],
            // table 行样式
            headerCellStyle: {
                background: '#EEF7FF',
                color: '#4D91F8'
            },
            fwdyid: '',
            slid: '',
            activeName: 'second',
            //审批指南
            spznList: [],
            // form表单提交数据
            // 持有因公出入境证件情况
            ryglRyscSwzjList: [{
                'zjmc': '涉密载体（含纸质、光盘等）',
                'fjlb': 1,
                'cyqk': '0',
                'zjhm': '',
                'yxq': '',
                'qzmc': '部门保密员核定签字：'
            }, {
                'zjmc': '信息设备（含计算机、存储介质等）',
                'fjlb': 2,
                'cyqk': '0',
                'zjhm': '',
                'yxq': '',
                'qzmc': '部门保密员核定签字：'
            }, {
                'zjmc': '涉密信息系统访问权限回收情况',
                'fjlb': 3,
                'cyqk': '0',
                'zjhm': '',
                'yxq': '',
                'qzmc': '系统管理员(三员)核定签字：'
            }, {
                'zjmc': '涉密场所出入权限回收情况',
                'fjlb': 4,
                'cyqk': '0',
                'zjhm': '',
                'yxq': '',
                'qzmc': '涉密场所管理员核定签字：  '
            }],
            //审批信息
            tjlist: {
                fhcs: [],
                cnsrq: '',
                bmscrq: '',
                rlscrq: '',
                bmbscrq: '',
                // 主要学习及工作经历
                xxjlList: [],
                // 家庭成员及社会关系
                cyjshgxList: [],
                // 持有因公出入境证件情况
                ygrjzjqkList: [],
                // 持有因私出入境证件情况
                ysrjzjqkList: [],
                // 因私出国(境)情况
                yscgqkList: [],
                // 接受境外资助情况
                jsjwzzqkList: [],
                // 处分或者违法犯罪情况
                clhwffzqkList: [],
                value1: [],
            },
            //轨迹处理
            gjclList: [],
            upccLsit: {},
            //判断实例所处环节
            disabled1: true,
            disabled2: true,
            disabled3: true,
            disabled4: true,
            btnsftg: true,
            btnsfth: true,
            yldis: false,
            jgyf: '',
            //性别
            xb: [{
                xb: '男',
                id: 1
            },
            {
                xb: '女',
                id: 2
            },
            ],
            //移居国(境)外情况
            yjgwqk: [{
                yw: '有',
                id: 1
            },
            {
                yw: '无',
                id: 0
            },
            ],
            //上岗保密教育、签订保密承诺书
            bmjysfwc: [
                {
                    sfwc: '已完成',
                    id: 1
                },
                {
                    sfwc: '未完成',
                    id: 0
                },
            ],
            scqk: [
                {
                    sfty: '同意',
                    id: 1
                },
                {
                    sfty: '不同意',
                    id: 0
                },
            ],
            ynoptions: [{
                value: 1,
                label: '是'
            }, {
                value: 0,
                label: '否'
            }],
            // 政治面貌下拉选项
            zzmmoptions: [],
            sltshow: '', // 文档的缩略图显示
            fileList: [],
            dialogVisible: false,
            fileRow: '',
            //人员任用
            smryList: [],
            page: 1,
            pageSize: 10,
            total: 0,
            formInline: {
                'bmmc': '',
                'xm': ''
            }, // 搜索条件
            selectlistRow: [], //列表的值
            xsyc: true,
            mbhjid: '',
            imageUrl: '',
            imageUrlbrcn: '',
            ylxy: true,
            file: {},
            bmcnssmj: '',
            bmxyssmj: '',
            //本人承诺
            dialogVisible_brcn: false,
            //保密承诺书预览
            dialogVisible_bmcns: false,
            bmcnsImageUrl: '',
            //保密承诺书预览
            dialogVisible_bmxys: false,
            bmxysImageUrl: '',
            //审批状态码 1 2 3 4
            zplcztm: '',
            //上传扫描件按钮显示隐藏
            show: true,
            show1: true,
            xm: '',
            //通过
            tgdis: false,
            //流程跟踪
            lcgzList: [],

        }
    },
    computed: {},
    mounted() {
        this.getNowTime()
        // let date = new Date()
        // console.log(date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + (date.getDate()));
        console.log(this.$route.query.list);
        this.fwdyid = this.$route.query.fwdyid
        console.log("this.fwdyid", this.fwdyid);
        this.slid = this.$route.query.slid
        console.log("this.slid", this.slid);
        this.dqlogin()
        // setTimeout(() => {
        //     this.pdschj()
        // }, 500)
        // return
        //审批指南初始化列表
        this.spzn()
        //审批信息初始化列表
        this.spxxxgcc()
        this.spxx()
        //判断实例所处环节
        // //事项审核
        // this.sxsh()
        //初始化el-dialog列表数据
        this.splist()
        //流程跟踪初始化列表
        this.lcgz()
        this.smmjxz()

    },
    methods: {
        //图片预览
        yl() {
            let zpxx
            zpxx = this.zpzm(this.file)
            this.scylImageUrl = zpxx
            this.dialogVisible_scyl = true
        },
        zpzm(zp) {
            const iamgeBase64 = "data:image/jpeg;base64," + zp;
            let zpxx
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    // let that = this;

                    function previwImg(item) {
                        zpxx = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
            return zpxx
        },
        getNowTime() {
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            console.log(defaultDate)
            return defaultDate;
            this.$set(this.info, "stockDate", defaultDate);
        },
        //当前登录用户
        async dqlogin() {
            let data = await getUserInfo()
            this.xm = data.xm
        },
        //审批指南
        //审批指南初始化列表
        async spzn() {
            let params = {
                fwdyid: this.fwdyid,
            }
            let data = await getBlzn(params)
            if (data.code == 10000) {
                this.spznList = data.data.content
            }
        },
        //审批信息
        //审批信息初始化数据
        async spxxxgcc() {
            let params = {
                jlid: this.jlid
            }
            let data = await getCssdInfo(params)
            this.upccLsit = data
            console.log('this.upccLsit', this.upccLsit);
            this.chRadio()
            this.xzbmcns()
            this.xzbmxys()
        },
        sjcf(val) {
            console.log(val)

            console.log(this.tjlist.cnsrq);
            console.log(typeof (this.tjlist.cnsrq));
        },
        async spxx() {
            let data = await selectZdryglRyscByLcslid({ lcslid: this.$route.query.slid })
            console.log(data);
            this.tjlist = data
            if (data.zp != '' && data.zp != undefined) {
                this.imageUrl = zp(data.zp)
            }
            if (data.brcn != '' && data.brcn != undefined) {
                this.sltshow = zp(data.brcn)
            }
            let jtcylist = await selectZdryJtcy({ rwid: data.rwid })
            this.ryglRyscJtcyList = jtcylist
        },
        // 预览
        yulan() {
            this.dialogVisible_brcn = true
            // this.ylxy = false
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.brcn;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.imageUrlbrcn = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        // 删除
        shanchu() {
            this.tjlist.brcn = ''
            this.sltshow = ''
        },
        chRadio(val) {

        },
        xzbmcns(val) {

        },
        xzbmxys(val) {

        },
        // 通过
        async save(index) {

            let jgbz = index
            if (jgbz == 1) {
                let params1 = {
                    fwdyid: this.fwdyid,
                    slid: this.slid,
                }
                let data1 = await verifySfjshj(params1)
                if (data1 == true) {
                    let xx = {
                        rwid:this.tjlist.rwid,
                        sfsc:1
                    }
                    updateZdry(xx)
                }
                let params = {
                    ryid: this.tjlist.ryid
                }
                if (this.zplcztm == 1) {
                    if (this.tjlist.rlsc != undefined) {
                        if (this.tjlist.rlscsj != undefined) {
                            params.rlsc = this.tjlist.rlsc;
                            params.rlscxm = this.tjlist.rlscxm;
                            params.rlscsj = this.tjlist.rlscsj;
                        } else {
                            this.$message.warning('请选择日期')
                            return
                        }
                    } else {
                        this.$message.warning('是否同意')
                        return
                    }

                } else if (this.zplcztm == 2) {
                    if (this.tjlist.bmbsc != undefined) {
                        if (this.tjlist.bmbscsj != undefined) {
                            params.bmbsc = this.tjlist.bmbsc;
                            params.bmbscxm = this.tjlist.bmbscxm;
                            params.bmbscsj = this.tjlist.bmbscsj;
                        } else {
                            this.$message.warning('请选择日期')
                            return
                        }
                    } else {
                        this.$message.warning('是否同意')
                        return
                    }

                }
                console.log(params);
                let data = await updateZdryglRysc(params)
                if (data.code == 10000) {
                    // if (jgbz == 1) {
                    this.jgyf = 1
                    // }
                    this.sxsh()
                    this.spxx()
                }
                this.tgdis = true
            }
            else if (jgbz == 2) {
                this.jgyf = 2
                this.sxsh()
                this.spxx()
            } else if (jgbz == 3) {
                this.jgyf = 3
                this.sxsh()
                this.spxx()
            }
        },
        //立即办理
        ljbl() {
            this.activeName = 'second'
        },
        //判断实例所处环节
        async pdschj() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            let data = await getSchj(params)
            this.zplcztm = data.data.content
            if (data.code == 10000) {
                if (data.data.content == 1) {
                    console.log(this.xm);
                    this.tjlist.rlscxm = this.xm
                    this.$set(this.tjlist, 'rlscsj', defaultDate)
                    this.disabled2 = true
                    this.disabled3 = true
                    this.disabled4 = true
                }
                if (data.data.content == 2) {
                    this.tjlist.bmbscxm = this.xm
                    this.$set(this.tjlist, 'bmbscsj', defaultDate)
                    this.disabled1 = true
                    this.disabled3 = true
                    this.disabled4 = true
                }
                if (data.data.content == 3) {
                    this.tjlist.bmbscxm = this.xm
                    this.$set(this.tjlist, 'bmbscsj', defaultDate)
                    this.disabled1 = true
                    this.disabled2 = true
                    this.disabled4 = true
                }
                if (data.data.content == 4) {
                    this.tjlist.bmgzldscxm = this.xm
                    this.$set(this.tjlist, 'bmgzldscsj', defaultDate)
                    this.disabled1 = true
                    this.disabled3 = true
                    this.disabled2 = true
                }
            }
        },
        //事项审核
        async sxsh() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                jg: this.jgyf,
                smryid: ''
            }
            let data = await getSxsh(params)
            if (data.code == 10000) {
                this.tgdis = false
                if (data.data.zt == 0) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // this.smryList = data.data.blrarr
                    this.mbhjid = data.data.mbhjid
                    this.splist()
                    this.dialogVisible = true
                } else if (data.data.zt == 1) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 2) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 3) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
                else if (data.data.zt == 4) {
                    this.$message({
                        message: data.data.msg
                    });
                    console.log(1111111111111);
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
            }
        },
        //初始化el-dialog列表数据
        async splist() {
            let params = {
                fwdyid: this.fwdyid,
                'xm': this.formInline.xm,
                'bmmc': this.formInline.bmmc,
                page: this.page,
                pageSize: this.pageSize,
                qshjid: this.mbhjid,
            }
            let data = await getSpUserList(params)
            this.smryList = data.records
            this.total = data.total


        },
        onSubmit() {
            this.splist()
        },
        selectRow(selection) {
            if (selection.length <= 1) {
                console.log('点击选中数据：', selection);
                this.selectlistRow = selection
                this.xsyc = true
            } else if (selection.length > 1) {
                this.$message.warning('只能选中一条数据')
                this.xsyc = false
            }

        },
        handleSelect(selection, val) {
            //只能选择一行，选择其他，清除上一行
            if (selection.length > 1) {
                let del_row = selection.shift()
                this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中
            }
        },
        // 点击行触发，选中或不选中复选框
        handleRowClick(row, column, event) {
            this.$refs.multipleTable.toggleRowSelection(row)
            this.selectChange(this.selectlistRow)
        },
        async submit() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                shry: this.selectlistRow[0].yhid,
                mbhjid: this.mbhjid,
            }
            let data = await tjclr(params)
            if (data.code == 10000) {
                this.$message({
                    message: data.message,
                    type: 'success'
                });
                this.dialogVisible = false
                setTimeout(() => {
                    this.$router.push('/dbsx')
                }, 500)
            }
        },
        //上传文件
        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isPNG = file.type === 'image/png';
            if (!isJPG && !isPNG) {
                this.$message.error('上传缩略图只能是 JPG/PNG 格式!');
            }
            return isJPG || isPNG;
        },
        // 64码
        blobToBase64(blob, callback) {
            const fileReader = new FileReader();
            fileReader.onload = (e) => {
                callback(e.target.result);
            };
            fileReader.readAsDataURL(blob);
        },
        //保密承诺书预览
        bmcnsyl() {
            this.dialogVisible_bmcns = true
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.cnssmj;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.bmcnsImageUrl = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        //
        bmxysyl() {
            this.dialogVisible_bmxys = true
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.xyssmj;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.bmxysImageUrl = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        //列表分页--跳转页数
        handleCurrentChange(val) {
            this.page = val
            this.splist()
        },
        //列表分页--更改每页显示个数
        handleSizeChange(val) {
            this.page = 1
            this.pageSize = val
            this.splist()
        },
        //流程跟踪
        //流程跟踪初始化列表
        async lcgz() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSpGjxx(params)
            if (data.code == 10000) {
                this.lcgzList = data.data.content
                this.gjclList = data.data.content
                console.log(this.gjclList);
            }
        },
        //设备密级获取
        async smmjxz() {
            this.sbmjxz = await getAllSmsbmj()
        },
        formj(row) {
            console.log(row);
            let smmj
            this.sbmjxz.forEach(item => {
                if (row.mj == item.id) {
                    smmj = item.mc
                }
            })
            return smmj
        }
    },
    watch: {

    }
}

</script>
  
<style scoped>
.sec-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: overlay;
}

.sec-title {
    border-left: 5px solid #1b72d8;
    color: #1b72d8;
    font-size: 20px;
    font-weight: 700;
    text-indent: 10px;
    margin-bottom: 20px;
    margin-top: 10px;
}

.sec-form-container {
    width: 100%;
    height: 100%;
}

.sec-form-left {
    width: calc(100% - 260px);
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
}

.sec-form-left:not(:first-child) {
    border-top: 0;
}

.sec-form-left .el-form-item {
    float: left;
    width: 100%;
}

.sec-header-section {
    width: 100%;
    position: relative;
}

.tb-container {
    height: 300px;
    /* overflow-y: scroll; */
}



.sec-header-pic {
    width: 258px;
    position: absolute;
    right: 0px;
    top: 0;
    height: 245px;
    border: 1px solid #CDD2D9;
    border-left: 0;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sec-header-flex {
    display: flex;
    align-items: center;
    justify-content: center;
}

.sec-header-mar {
    margin-right: 10px;
}

.sec-form-second {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
    border-top: 0;
    position: relative;
}

.sec-form-fddw {
    height: 100%;
    position: absolute;
    top: 0;
    right: 40%;
}

.sec-form-third {
    border: 1px solid #CDD2D9;
    /* height: 40px;  */
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-four {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-five {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    overflow: hidden;
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.yulan {
    text-align: center;
    cursor: pointer;
    color: #3874D5;
    font-weight: 600;
    float: left;
    margin-left: 10px;
}

.avatar {
    width: 100px;
    height: 100px;
    display: block;
}

.avatarimg {
    width: 150px;
    height: 180px;
    display: block;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    border: 2px solid #EBEBEB;
}

.sec-form-six {
    border: 1px solid #CDD2D9;
    overflow: hidden;
    background: #ffffff;
    padding: 10px;
}

.ml10 {
    margin-left: 10px;
}

.sec-footer {
    margin-top: 10px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
    border: none;
}

.sec-left-text {
    float: left;
    margin-right: 130px;
}

.haveBorderTop {
    border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
    width: 225px !important;
}

>>>.longLabel .el-form-item__content {
    margin-left: 225px !important;
    padding-left: 20px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

.gtzzsmgwgz {
    border-right: 1px solid #CDD2D9;
}

.hyzk {
    padding-left: 15px;
    background-color: #F5F7FA;
    width: calc(100% - 16px);
    border-right: 1px solid #CDD2D9;
    color: #000;
}

.gtzzsmgwgz>>>.el-form-item__content {
    display: none !important;
}

.gtzzsmgwgz>>>.el-form-item__label {
    border: none;
    text-align: left !important;
}

/* .sec-form-second:not(:first-child){
    border-top: 0;
  } */
.sec-form-second .el-form-item {
    float: left;
    width: 100%;
}

.sec-el-table {
    width: 100%;
    border: 1px solid #EBEEF5;
    height: calc(100% - 34px - 44px - 10px);
}

>>>.sec-el-table .el-input__inner {
    border: none !important;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
    width: 200px;
    text-align: center;
    font-size: 16px;
}

.gtzzsmgwgz {
    text-align: left !important;
}

>>>.sec-form-container .el-input__inner {
    border: none;
    border-right: 1px solid #CDD2D9;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item {
    margin-bottom: 0px;
}

/* >>>.el-form > div {
    border: 1px solid #CDD2D9;;
  } */
>>>.el-form-item__label {
    border-right: 1px solid #CDD2D9;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
    width: 150px;
    margin-left: 10px;
}

.dlFqsqContainer .searchButton {
    margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
    height: 40px;
}

.dlFqsqContainer .input1 {
    margin-right: 20px;
    margin-bottom: 10px;
}

/deep/ .el-input.is-disabled .el-input__inner {
    color: #000 !important;
}

>>>.brno .el-input__inner {
    border-right: none;
}

>>>.wd .el-radio {
    display: block;
    margin: 10px 0;
}

>>>.lh .el-radio {
    line-height: 48px;
}

>>>.wd .el-form-item__label {
    height: 184px;
    line-height: 184px;
}

>>>.cs .el-input__inner {
    border-right: 0 !important;
    width: 100%;
}

.rip {
    width: 100% !important;
}
</style>
  