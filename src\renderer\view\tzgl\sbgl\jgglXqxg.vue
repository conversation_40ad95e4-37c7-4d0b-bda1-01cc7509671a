<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div class="bg_con_top">
      <img src="./img/title.png" alt="" />
      <span
        class="title"
        :class="[sbxxQhVal == 1 ? 'title1' : '']"
        @click="sbxxClick(1)"
        >机柜信息</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 2 ? 'title1' : '']"
        @click="sbxxClick(2)"
        v-if="tjType == '3'"
        >机柜巡检信息</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 3 ? 'title1' : '']"
        @click="sbxxClick(3)"
        >开锁记录</span
      >
    </div>
    <div v-if="sbxxQhVal == 1">
      <el-form ref="form" :model="form" size="mini" label-width="120px">
        <!-- <div class="flex">
          <el-form-item label="机房编号">
            <el-select :disabled="disabled" v-model="selectedItem" clearable @change="jfbhChange" placeholder="请选择机房"
              class="widthx">
              <el-option v-for="item in jfList" :label="item.computerRoomCode" :value="item"
                :key="item.computerRoomCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机房名称">
            <el-input v-model="form.computerRoomName" disabled></el-input>
          </el-form-item>
        </div> -->
        <div class="flex">
          <el-form-item label="机房编号">
            <el-select
              v-if="tjType === '1'"
              :disabled="disabled"
              v-model="form.computerRoomCode"
              clearable
              @change="jfbhChange"
              placeholder="请选择机房"
              class="widthx"
            >
              <el-option
                v-for="item in jfList"
                :label="item.computerRoomCode"
                :value="item"
                :key="item.computerRoomCode"
              ></el-option>
            </el-select>
            <el-input
              v-else
              v-model="form.computerRoomCode"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="机房名称">
            <el-input v-model="form.computerRoomName" disabled></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="机柜编号">
            <el-input v-model="form.cabinetCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="机柜名称">
            <el-input
              v-model="form.cabinetName"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <!--        <div class="flex">
          <el-form-item label="服务器数量">
            <el-input v-model="form.serverNumber" :disabled="disabled"></el-input>
          </el-form-item>
          <el-form-item label="交换机数量">
            <el-input v-model="form.switchNumber" :disabled="disabled"></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="路由器数量">
            <el-input v-model="form.routerNumber" :disabled="disabled"></el-input>
          </el-form-item>
          <el-form-item label="上次巡检时间">
            <el-date-picker :disabled="disabled" v-model="form.lastInspectionTime" style="width: 100%" clearable
              type="date" placeholder="选择时间" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="上次巡检结果">
            <el-input v-model="form.lastInspectionResult" :disabled="disabled"></el-input>
          </el-form-item>
          <el-form-item label="使用状态">
            <el-select :disabled="disabled" v-model="form.useStatus" clearable placeholder="请选择类型" class="widthx">
              <el-option v-for="item in syztList" :label="item.mc" :value="item.id" :key="item.id"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="审批状态">
            <el-input v-model="form.approveStatus" :disabled="disabled"></el-input>
          </el-form-item>
        </div>-->
        <div class="flex">
          <el-form-item
            label="使用状态"
            v-show="tjType === '2' || tjType === '3'"
          >
            <el-select
              :disabled="disabled"
              v-model="form.useStatus"
              clearable
              placeholder="请选择类型"
              class="widthx"
              v-show="tjType === '2' || tjType === '3'"
            >
              <el-option
                v-for="item in syztList"
                :label="item.mc"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="form.remark"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div style="width: 950px">
          <el-form-item style="float: right">
            <el-button type="success" @click="add" v-if="tjType == '1'"
              >提交</el-button
            >
            <el-button type="primary" @click="update" v-if="tjType == '2'"
              >修改</el-button
            >
            <el-button type="primary" @click="fh">返回</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div style="height: 100%" v-if="sbxxQhVal == 2">
      <el-table
        :data="xjList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="cabinetCode"
          label="机柜编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cabinetName"
          label="机柜名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="temperature"
          label="机柜温度"
          sortable
        ></el-table-column>
        <el-table-column
          prop="dust"
          label="机柜湿度"
          sortable
        ></el-table-column>
        <el-table-column
          prop="close"
          label="柜门关闭"
          sortable
        ></el-table-column>
        <el-table-column
          prop="humidity"
          label="机柜电力"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cleanliness"
          label="柜内灰尘"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionTime"
          label="巡检时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionCode"
          label="巡检人员编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionName"
          label="巡检人员名称"
          sortable
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <div style="height: 100%" v-if="sbxxQhVal == 3">
      <!-- <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="时间段">
          <el-date-picker
            v-model="formInline.timeList"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="非合规操作">
          <el-select v-model="formInline.region" placeholder="非合规操作">
            <el-option label="U位异常记录" value="1"></el-option>
            <el-option label="门磁信息记录" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
      </el-form> -->
      <el-table
        :data="ksList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 128px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="computerRoomId"
          label="机房编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cabinetId"
          label="机柜编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="computerRoomName"
          label="机柜名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockTypeName"
          label="开锁原因"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockName"
          label="开锁人员"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockTime"
          label="开锁时间"
          sortable
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getComputerRoomList, // 添加机柜时获取机房编号和名称
  getCabinetCode, // 添加机柜时获取机柜编号
  insertCabinet,
  updateCabinet,
  jgQueryByCondition,
  downloadInspectionForm,
  selectEquipmentInspectionProcessPage,
  selectCabinetInspectionProcessPage,
} from "../../../../api/shma";
import { mcQueryPage } from "../../../../api/index";

export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        id: "",
        computerRoomId: "",
        cabinetCode: "",
        cabinetName: "",
        serverNumber: "",
        switchNumber: "",
        routerNumber: "",
        lastInspectionTime: "",
        lastInspectionResult: "",
        remark: "",
        useStatus: 1,
        approveStatus: "",
        computerRoomCode: "",
        computerRoomName: "",
      },
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" },
      ],
      sbxxQhVal: 1,
      xjList: [],
      formInline: {
        timeList: [],
        region: "",
      },
      page: 1,
      pageSize: 10,
      total: 0,
      tjType: "",
      id: "",
      jfList: [],
      selectedItem: null, // 初始化为 null
      disabled: false,
    };
  },
  computed: {},
  mounted() {
    this.tjType = this.$route.query.routeType;
    if (this.tjType == "1") {
      this.getComputerRoomList();
    } else if (this.tjType == "2") {
      this.id = this.$route.query.id;
      this.jgQueryByCondition();
    } else if (this.tjType == "3") {
      this.disabled = true;
      this.id = this.$route.query.id;
      this.jgQueryByCondition();
    }
  },
  methods: {
    // async jgQueryByCondition() {
    //   let data = await jgQueryByCondition({ id: this.id });
    //   this.form = data.data;
    // },
    async jgQueryByCondition() {
      try {
        let data = await jgQueryByCondition({ id: this.id });
        if (data.code === 10000) {
          this.form = data.data;
          // 确保selectedItem不为null
          this.selectedItem = this.jfList.find(
            (item) => item.id === this.form.computerRoomId
          );
        } else {
          this.$message.error("查询机柜信息失败：" + data.message);
        }
      } catch (error) {
        console.error("查询机柜信息失败:", error);
        this.$message.error("查询机柜信息失败：" + error.message);
      }
    },
    //获取机房列表
    // async getComputerRoomList() {
    //   const res = await getComputerRoomList();
    //   this.jfList = res.data;
    // },
    async getComputerRoomList() {
      try {
        const res = await getComputerRoomList();
        console.log("getComputerRoomList response:", res);
        if (Array.isArray(res.data)) {
          this.jfList = res.data;
        } else {
          console.error(
            "getComputerRoomList response is not an array:",
            res.data
          );
          this.$message.error("获取机房列表失败：返回数据格式不正确");
        }
      } catch (error) {
        console.error("获取机房列表失败:", error);
        this.$message.error("获取机房列表失败：" + error.message);
      }
    },
    // 获取机房编号和名称
    async getComputerRoomCode() {
      try {
        const res = await getComputerRoomCode();
        this.jfList = res.data;
      } catch (error) {
        console.error("获取机房信息失败:", error);
        this.$message.error("获取机房信息失败：" + error.message);
      }
    },
    // 机房编号选择变化时的处理
    jfbhChange(val) {
      if (val) {
        this.form.id = val.id;
        this.form.computerRoomCode = val.computerRoomCode;
        this.form.computerRoomName = val.computerRoomName;
        this.form.computerRoomId = val.id;
        this.getCabinetCode(val.computerRoomCode, val.id);
      } else {
        this.form.id = "";
        this.form.computerRoomCode = "";
        this.form.computerRoomName = "";
        this.form.computerRoomId = "";
        this.form.cabinetCode = "";
      }
    },
    // 获取机柜编号
    async getCabinetCode(computerRoomCode, id) {
      try {
        let params = { computerRoomCode, id };
        const res = await getCabinetCode(params);
        if (res.code === 10000) {
          this.form.cabinetCode = res.data.cabinetCode;
        } else {
          this.$message.error("获取机柜编号失败：" + res.message);
        }
      } catch (error) {
        console.error("获取机柜编号失败:", error);
        this.$message.error("获取机柜编号失败：" + error.message);
      }
    },
    sbxxClick(index) {
      console.log(index);
      this.sbxxQhVal = index;
      if (this.sbxxQhVal == 2) {
        this.selectCabinetInspectionProcessPage();
      } else if (this.sbxxQhVal == 3) {
        this.mcQueryPage();
      }
    },
    async mcQueryPage() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        computerRoomCode: this.form.computerRoomCode,
      };
      let data = await mcQueryPage(params);
      if (data.code === 10000) {
        this.McycList = data.data.records;
        this.total = data.data.total;
      } else {
        this.$message.error("查询详情信息失败：" + data.message);
      }
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      if (this.sbxxQhVal == 2) {
        this.selectCabinetInspectionProcessPage();
      } else if (this.sbxxQhVal == 3) {
        this.mcQueryPage();
      }
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      if (this.sbxxQhVal == 2) {
        this.selectCabinetInspectionProcessPage();
      } else if (this.sbxxQhVal == 3) {
        this.mcQueryPage();
      }
    },
    async selectCabinetInspectionProcessPage() {
      let data = await selectCabinetInspectionProcessPage({
        id: this.form.id,
        pageNo: this.page,
        pageSize: this.pageSize,
      });
      console.log(data);
      this.xjList = data.data.records;
      this.total = data.data.total;
    },
    // 提交表单
    async add() {
      console.log("form:", this.form);
      this.form.computerRoomId = this.form.id;
      let params = this.form;
      try {
        let data = await insertCabinet(params);
        if (data.code == 10000) {
          this.$message.success("提交成功");
          this.$router.push({ path: "/sbgl" });
        } else {
          this.$message.error("提交失败：" + data.message);
        }
      } catch (error) {
        console.error("提交失败:", error);
        this.$message.error("提交失败：" + error.message);
      }
    },
    // 修改表单
    async update() {
      let params = this.form;
      try {
        let data = await updateCabinet(params);
        if (data.code == 10000) {
          this.$message.success("修改成功");
          this.$router.push({ path: "/sbgl" });
        } else {
          this.$message.error("修改失败：" + data.message);
        }
      } catch (error) {
        console.error("修改失败:", error);
        this.$message.error("修改失败：" + error.message);
      }
    },
    // 返回
    fh() {
      this.$router.push({ path: "/sbgl" });
    },
  },
  watch: {},
};
</script>


<style scoped>
.bg_con {
  padding: 10px;
}

.bg_con_top {
  width: 100%;
  height: 35px;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 20px;
}

/deep/.el-form-item .el-form-item__label {
  font-family: SourceHanSansSC-Regular !important;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
  text-align: left;
}

.flex {
  width: 950px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/deep/.el-input .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}

/deep/.el-select .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}

/deep/.el-textarea .el-textarea__inner {
  width: 830px !important;
  height: 129px !important;
  border-radius: 2px;
}
.title {
  margin-left: 10px;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  position: relative;
}

.title1::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: -15px;
  background-color: #0077d2;
}
</style>
