{"version": 3, "sources": ["webpack:///./src/api/lhgl.js", "webpack:///src/renderer/view/xtsz/lhglSetting.vue", "webpack:///./src/renderer/view/xtsz/lhglSetting.vue?afb1", "webpack:///./src/renderer/view/xtsz/lhglSetting.vue"], "names": ["getFwlxList", "data", "createAPI", "BASE_URL", "gzlJbxxAdd", "gzlJbxxUpdate", "gzlJbxxDelete", "lhglSetting", "_this", "this", "formInline", "fwmc", "fwlx", "fwzt", "pageInfo", "page", "pageSize", "total", "regionOption", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "dialogVisibleSettingModify", "dialogVisibleSetting", "settingForm", "settingFormOld", "cszlx", "settingList", "pickerOptions", "disabledDate", "time", "selectDate", "getFullYear", "onPick", "date", "minDate", "maxDate", "jldwList", "zt", "id", "components", "hsoft_top_title", "methods", "fwlxList", "_this2", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "lhgl_getFwlxList", "sent", "stop", "zzjg", "_this3", "_callee2", "zzjgList", "shu", "shuList", "_context2", "Object", "api", "console", "log", "zzjgmc", "for<PERSON>ach", "item", "childrenRegionVo", "item1", "bmm", "fbmm", "push", "getjldw", "_this4", "_callee3", "_context3", "cssz", "showAddDialog", "formatTime", "moment", "Date", "onSubmit", "getSettingList", "cz", "handleCurrentChange", "val", "handleSizeChange", "modifySetting", "row", "JSON", "parse", "stringify_default", "lcpzSetting", "$router", "path", "query", "fwdyid", "modifySettingDialog", "_this5", "_callee4", "params", "_context4", "pxh", "lhgl_gzlJbxxUpdate", "code", "$message", "error", "message", "deleteSetting", "_this6", "_callee5", "_context5", "lhgl_gzlJbxxDelete", "type", "_this7", "_callee6", "settingPage", "_context6", "content", "addSetting", "_this8", "_callee7", "_context7", "lhgl_gzlJbxxAdd", "forfwlx", "hxsj", "fwid", "forfwzt", "forcjsj", "querySearchxm", "queryString", "cb", "restaurants", "restaurantsxm", "results", "filter", "createFilterzw", "createFilterxm", "restaurant", "xm", "toLowerCase", "indexOf", "xmmh", "_this9", "_callee8", "resList", "_context8", "restaurantszj", "mounted", "xtsz_lhglSetting", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "height", "scopedSlots", "_u", "key", "fn", "_v", "proxy", "text-align", "staticClass", "float", "attrs", "inline", "model", "placeholder", "callback", "$$v", "$set", "expression", "_l", "icon", "on", "click", "width", "border", "header-cell-style", "background", "color", "stripe", "align", "prop", "formatter", "scoped", "size", "$event", "padding-top", "pager-count", "current-page", "page-sizes", "page-size", "layout", "current-change", "size-change", "title", "visible", "update:visible", "label-position", "label-width", "display", "oninput", "slot", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "2PAIaA,EAAc,SAAAC,GAAA,OAAQC,YAAUC,IAAS,0BAA2B,MAAMF,IAE1EG,EAAa,SAAAH,GAAA,OAAQC,YAAUC,IAAS,6BAA8B,OAAOF,IAE7EI,EAAgB,SAAAJ,GAAA,OAAQC,YAAUC,IAAS,gCAAiC,OAAOF,IAEnFK,EAAgB,SAAAL,GAAA,OAAQC,YAAUC,IAAS,gCAAiC,OAAOF,gBCyKhGM,GACAN,KADA,WACA,IAAAO,EAAAC,KACA,OAEAC,YACAC,KAAA,GACAC,KAAA,GACAC,KAAA,IAGAC,UACAC,KAAA,EACAC,SAAA,GACAC,MAAA,GAEAC,gBAEAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,4BAAA,EAEAC,sBAAA,EACAC,eACAC,kBACAC,MAAA,EAEAC,eACAC,eACAC,aAAA,SAAAC,GACA,aAAAzB,EAAA0B,YAGA1B,EAAA0B,WAAAC,eAAAF,EAAAE,eAGAC,OAAA,SAAAC,GAEAA,EAAAC,UAAAD,EAAAE,QACA/B,EAAA0B,WAAAG,EAAAC,QAEA9B,EAAA0B,WAAA,OAIAM,YAEA5B,QAEAC,OACA4B,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,MAKAC,YACAC,kBAAA,GAEAC,SAEAC,SAFA,WAEA,IAAAC,EAAAtC,KAAA,OAAAuC,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAnD,EAAA,OAAAgD,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,IADA,OACAxD,EADAqD,EAAAI,KAEAX,EAAAnC,KAAAX,OAFA,wBAAAqD,EAAAK,SAAAP,EAAAL,KAAAC,IAKAY,KAPA,WAOA,IAAAC,EAAApD,KAAA,OAAAuC,IAAAC,EAAAC,EAAAC,KAAA,SAAAW,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAhB,EAAAC,EAAAG,KAAA,SAAAa,GAAA,cAAAA,EAAAX,KAAAW,EAAAV,MAAA,cAAAU,EAAAV,KAAA,EACAW,OAAAC,EAAA,IAAAD,GADA,OACAJ,EADAG,EAAAR,KAEAW,QAAAC,IAAAP,GACAF,EAAAU,OAAAR,EACAC,KACAK,QAAAC,IAAAT,EAAAU,QACAV,EAAAU,OAAAC,QAAA,SAAAC,GACA,IAAAC,KACAb,EAAAU,OAAAC,QAAA,SAAAG,GACAF,EAAAG,KAAAD,EAAAE,OAEAH,EAAAI,KAAAH,GAEAF,EAAAC,sBAIAV,EAAAc,KAAAL,KAGAJ,QAAAC,IAAAN,GACAK,QAAAC,IAAAN,EAAA,GAAAU,kBACAT,KACAD,EAAAQ,QAAA,SAAAC,GACA,IAAAA,EAAAI,MACAZ,EAAAa,KAAAL,KAGAJ,QAAAC,IAAAL,GACAA,EAAA,GAAAS,iBAAAF,QAAA,SAAAC,GACAZ,EAAA3C,aAAA4D,KAAAL,KA9BA,yBAAAP,EAAAP,SAAAG,EAAAD,KAAAb,IAkCA+B,QAzCA,WAyCA,IAAAC,EAAAvE,KAAA,OAAAuC,IAAAC,EAAAC,EAAAC,KAAA,SAAA8B,IAAA,OAAAhC,EAAAC,EAAAG,KAAA,SAAA6B,GAAA,cAAAA,EAAA3B,KAAA2B,EAAA1B,MAAA,cAAA0B,EAAA1B,KAAA,EACAW,OAAAgB,EAAA,EAAAhB,GADA,OACAa,EAAAxC,SADA0C,EAAAxB,KAAA,wBAAAwB,EAAAvB,SAAAsB,EAAAD,KAAAhC,IAGAoC,cA5CA,WA6CA3E,KAAAkB,eACAlB,KAAAiB,sBAAA,GAGA2D,WAjDA,SAiDApD,GACA,OAAAkC,OAAAmB,EAAA,EAAAnB,CAAA,IAAAoB,KAAAtD,KAGAuD,SArDA,WAsDA/E,KAAAgF,kBAGAC,GAzDA,WA0DAjF,KAAAC,eAEAiF,oBA5DA,SA4DAC,GACAnF,KAAAK,SAAAC,KAAA6E,EACAnF,KAAAgF,kBAEAI,iBAhEA,SAgEAD,GACAnF,KAAAK,SAAAE,SAAA4E,EACAnF,KAAAgF,kBAGAK,cArEA,SAqEAC,GAOAtF,KAAAkB,YAAAqE,KAAAC,MAAAC,IAAAH,IACAtF,KAAAmB,eAAAoE,KAAAC,MAAAC,IAAAH,IACAtF,KAAAgB,4BAAA,GAGA0E,YAjFA,SAiFAJ,GAEAtF,KAAA2F,QAAAtB,MACAuB,KAAA,eACAC,OACAC,OAAAR,EAAAQ,WAKAC,oBA3FA,WA2FA,IAAAC,EAAAhG,KAAA,OAAAuC,IAAAC,EAAAC,EAAAC,KAAA,SAAAuD,IAAA,IAAAC,EAAA1G,EAAA,OAAAgD,EAAAC,EAAAG,KAAA,SAAAuD,GAAA,cAAAA,EAAArD,KAAAqD,EAAApD,MAAA,cAEAmD,EAAAX,KAAAC,MAAAC,IAAAO,EAAA9E,eACAkF,IAAA,EAAAF,EAAAE,IAHAD,EAAApD,KAAA,EAKAsD,EAAAH,GALA,OAMA,MADA1G,EALA2G,EAAAlD,MAMAqD,KACAN,EAAAhB,iBACA,OAAAxF,EAAA8G,MACAN,EAAAO,SAAAC,MAAAhH,EAAAiH,SAiBAT,EAAAhF,4BAAA,EA1BA,wBAAAmF,EAAAjD,SAAA+C,EAAAD,KAAAzD,IA6BAmE,cAxHA,SAwHApB,GAAA,IAAAqB,EAAA3G,KAAA,OAAAuC,IAAAC,EAAAC,EAAAC,KAAA,SAAAkE,IAAA,IAAAV,EAAA1G,EAAA,OAAAgD,EAAAC,EAAAG,KAAA,SAAAiE,GAAA,cAAAA,EAAA/D,KAAA+D,EAAA9D,MAAA,cAEAmD,GACAJ,OAAAR,EAAAQ,QAHAe,EAAA9D,KAAA,EAKA+D,EAAAZ,GALA,OAMA,MADA1G,EALAqH,EAAA5D,MAMAqD,MACAK,EAAA3B,iBAEA2B,EAAAJ,UACAE,QAAA,OACAM,KAAA,aAGA,OAAAvH,EAAA8G,MACAK,EAAAJ,SAAAC,MAAAhH,EAAAiH,SAfA,wBAAAI,EAAA3D,SAAA0D,EAAAD,KAAApE,IAkCAyC,eA1JA,WA0JA,IAAAgC,EAAAhH,KAAA,OAAAuC,IAAAC,EAAAC,EAAAC,KAAA,SAAAuE,IAAA,IAAAf,EAAAgB,EAAA,OAAA1E,EAAAC,EAAAG,KAAA,SAAAuE,GAAA,cAAAA,EAAArE,KAAAqE,EAAApE,MAAA,cAOAmD,GACA5F,KAAA0G,EAAA3G,SAAAC,KACAC,SAAAyG,EAAA3G,SAAAE,SACAL,KAAA8G,EAAA/G,WAAAC,KACAC,KAAA6G,EAAA/G,WAAAE,KACAC,KAAA4G,EAAA/G,WAAAG,MAZA+G,EAAApE,KAAA,EDzYmCvD,ECuZnC0G,EDvZ2CzG,YAAUC,IAAS,sCAAuC,MAAMF,GCyY3G,OAcA0H,EAdAC,EAAAlE,KAeA+D,EAAA3F,YAAA6F,EAAA1H,KAAA4H,QAOAJ,EAAA3G,SAAAG,MAAA0G,EAAA1H,KAAAa,SAAAG,MAtBA,wBAAA2G,EAAAjE,ODzYmC,IAAA1D,GCyYnCyH,EAAAD,KAAAzE,IAyBA8E,WAnLA,WAmLA,IAAAC,EAAAtH,KAAA,OAAAuC,IAAAC,EAAAC,EAAAC,KAAA,SAAA6E,IAAA,IAAArB,EAAA1G,EAAA,OAAAgD,EAAAC,EAAAG,KAAA,SAAA4E,GAAA,cAAAA,EAAA1E,KAAA0E,EAAAzE,MAAA,cACAmD,EAAAX,KAAAC,MAAAC,IAAA6B,EAAApG,eACAkF,IAAA,EAAAF,EAAAE,IAFAoB,EAAAzE,KAAA,EAGA0E,EAAAvB,GAHA,OAIA,MADA1G,EAHAgI,EAAAvE,MAIAqD,KACAgB,EAAAtC,iBACA,OAAAxF,EAAA8G,MACAgB,EAAAf,SAAAC,MAAAhH,EAAAiH,SAiBAa,EAAArG,sBAAA,EAxBA,wBAAAuG,EAAAtE,SAAAqE,EAAAD,KAAA/E,IA0BAmF,QA7MA,SA6MApC,GACA1B,QAAAC,IAAAyB,GACA,IAAAqC,OAAA,EAMA,OALA3H,KAAAG,KAAA4D,QAAA,SAAAC,GACAsB,EAAAnF,MAAA6D,EAAA4D,OACAD,EAAA3D,EAAA7D,QAGAwH,GAEAE,QAvNA,SAuNAvC,GACA1B,QAAAC,IAAAyB,GACA,IAAAqC,OAAA,EAMA,OALA3H,KAAAI,KAAA2D,QAAA,SAAAC,GACAsB,EAAAlF,MAAA4D,EAAA/B,KACA0F,EAAA3D,EAAAhC,MAGA2F,GAEAG,QAjOA,SAiOAxC,GACA,OAAA5B,OAAAmB,EAAA,EAAAnB,CAAA4B,IAGAyC,cArOA,SAqOAC,EAAAC,GACA,IAAAC,EAAAlI,KAAAmI,cACAvE,QAAAC,IAAA,cAAAqE,GACA,IAAAE,EAAAJ,EAAAE,EAAAG,OAAArI,KAAAsI,eAAAN,IAAAE,EACAtE,QAAAC,IAAA,UAAAuE,GAUAH,EAAAG,GACAxE,QAAAC,IAAA,iBAAAuE,IAEAG,eAtPA,SAsPAP,GACA,gBAAAQ,GACA,OAAAA,EAAAC,GAAAC,cAAAC,QAAAX,EAAAU,gBAAA,IAGAE,KA3PA,WA2PA,IAAAC,EAAA7I,KAAA,OAAAuC,IAAAC,EAAAC,EAAAC,KAAA,SAAAoG,IAAA,IAAAC,EAAA,OAAAvG,EAAAC,EAAAG,KAAA,SAAAoG,GAAA,cAAAA,EAAAlG,KAAAkG,EAAAjG,MAAA,cAAAiG,EAAAjG,KAAA,EACAW,OAAAC,EAAA,EAAAD,GADA,OACAqF,EADAC,EAAA/F,KAGA4F,EAAAV,cAAAY,EACAF,EAAAI,cAAAF,EAJA,wBAAAC,EAAA9F,SAAA4F,EAAAD,KAAAtG,KASA2G,QA5UA,WA6UAlJ,KAAAqC,WACArC,KAAAmD,OACAnD,KAAA4I,OACA5I,KAAAsE,UAEAtE,KAAAgF,iBAEApB,QAAAC,IAAA,IAAAiB,KAAA,cCpgBeqE,GADEC,OAFjB,WAA0B,IAAAC,EAAArJ,KAAasJ,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,OAAA,UAAiBH,EAAA,mBAAwBI,YAAAP,EAAAQ,KAAqBC,IAAA,OAAAC,GAAA,WAAyB,OAAAV,EAAAW,GAAA,UAAwBC,OAAA,OAAeZ,EAAAW,GAAA,KAAAR,EAAA,OAAwBE,aAAaQ,aAAA,WAAsBV,EAAA,WAAgBW,YAAA,mBAAAT,aAA4CU,MAAA,QAAeC,OAAQC,QAAA,EAAAC,MAAAlB,EAAApJ,cAAsCuJ,EAAA,gBAAqBa,OAAO1J,MAAA,UAAgB6I,EAAA,YAAiBa,OAAOG,YAAA,QAAqBD,OAAQ3J,MAAAyI,EAAApJ,WAAA,KAAAwK,SAAA,SAAAC,GAAqDrB,EAAAsB,KAAAtB,EAAApJ,WAAA,OAAAyK,IAAsCE,WAAA,sBAA+B,GAAAvB,EAAAW,GAAA,KAAAR,EAAA,gBAAqCa,OAAO1J,MAAA,UAAgB6I,EAAA,aAAkBa,OAAOG,YAAA,QAAqBD,OAAQ3J,MAAAyI,EAAApJ,WAAA,KAAAwK,SAAA,SAAAC,GAAqDrB,EAAAsB,KAAAtB,EAAApJ,WAAA,OAAAyK,IAAsCE,WAAA,oBAA+BvB,EAAAwB,GAAAxB,EAAA,cAAArF,GAAkC,OAAAwF,EAAA,aAAuBM,IAAA9F,EAAA4D,KAAAyC,OAAqB1J,MAAAqD,EAAA7D,KAAAS,MAAAoD,EAAA4D,UAAuC,OAAAyB,EAAAW,GAAA,KAAAR,EAAA,gBAAwCa,OAAO1J,MAAA,UAAgB6I,EAAA,aAAkBa,OAAOG,YAAA,QAAqBD,OAAQ3J,MAAAyI,EAAApJ,WAAA,KAAAwK,SAAA,SAAAC,GAAqDrB,EAAAsB,KAAAtB,EAAApJ,WAAA,OAAAyK,IAAsCE,WAAA,oBAA+BvB,EAAAwB,GAAAxB,EAAA,cAAArF,GAAkC,OAAAwF,EAAA,aAAuBM,IAAA9F,EAAA/B,GAAAoI,OAAmB1J,MAAAqD,EAAAhC,GAAApB,MAAAoD,EAAA/B,QAAmC,OAAAoH,EAAAW,GAAA,KAAAR,EAAA,aAAqCa,OAAOtD,KAAA,UAAA+D,KAAA,kBAAyCC,IAAKC,MAAA3B,EAAAtE,YAAsBsE,EAAAW,GAAA,QAAAX,EAAAW,GAAA,KAAAR,EAAA,aAA6Ca,OAAOtD,KAAA,UAAA+D,KAAA,wBAA+CC,IAAKC,MAAA3B,EAAApE,MAAgBoE,EAAAW,GAAA,YAAAX,EAAAW,GAAA,KAAAR,EAAA,aAAiDa,OAAOtD,KAAA,WAAiBgE,IAAKC,MAAA3B,EAAA1E,iBAA2B0E,EAAAW,GAAA,YAAAX,EAAAW,GAAA,KAAAR,EAAA,YAAgDW,YAAA,QAAAT,aAAiCuB,MAAA,OAAAC,OAAA,qBAA4Cb,OAAQ7K,KAAA6J,EAAAhI,YAAA6J,OAAA,GAAAC,qBAAwDC,WAAA,UAAAC,MAAA,WAA0C1B,OAAA,gDAAA2B,OAAA,MAAuE9B,EAAA,mBAAwBa,OAAOtD,KAAA,QAAAkE,MAAA,KAAAtK,MAAA,KAAA4K,MAAA,YAA2DlC,EAAAW,GAAA,KAAAR,EAAA,mBAAoCa,OAAOmB,KAAA,OAAA7K,MAAA,OAAAsK,MAAA,MAAyC5B,EAAAW,GAAA,KAAAR,EAAA,mBAAoCa,OAAOmB,KAAA,OAAA7K,MAAA,OAAA8K,UAAApC,EAAA3B,WAAsD2B,EAAAW,GAAA,KAAAR,EAAA,mBAAoCa,OAAOmB,KAAA,OAAA7K,MAAA,OAAAsK,MAAA,GAAAM,MAAA,SAAAE,UAAApC,EAAAxB,WAAkFwB,EAAAW,GAAA,KAAAR,EAAA,mBAAoCa,OAAOmB,KAAA,MAAA7K,MAAA,MAAAsK,MAAA,MAAuC5B,EAAAW,GAAA,KAAAR,EAAA,mBAAoCa,OAAOmB,KAAA,MAAA7K,MAAA,MAAAsK,MAAA,MAAuC5B,EAAAW,GAAA,KAAAR,EAAA,mBAAoCa,OAAOmB,KAAA,OAAA7K,MAAA,OAAAsK,MAAA,GAAAQ,UAAApC,EAAAvB,WAAiEuB,EAAAW,GAAA,KAAAR,EAAA,mBAAoCa,OAAOmB,KAAA,GAAA7K,MAAA,KAAAsK,MAAA,IAAkCrB,YAAAP,EAAAQ,KAAsBC,IAAA,UAAAC,GAAA,SAAA2B,GAAkC,OAAAlC,EAAA,aAAwBa,OAAOsB,KAAA,QAAA5E,KAAA,QAA6BgE,IAAKC,MAAA,SAAAY,GAAyB,OAAAvC,EAAAhE,cAAAqG,EAAApG,SAAuC+D,EAAAW,GAAA,QAAAX,EAAAW,GAAA,KAAAR,EAAA,aAA6Ca,OAAOsB,KAAA,QAAA5E,KAAA,QAA6BgE,IAAKC,MAAA,SAAAY,GAAyB,OAAAvC,EAAA3D,YAAAgG,EAAApG,SAAqC+D,EAAAW,GAAA,UAAAX,EAAAW,GAAA,KAAAR,EAAA,aAA+CE,aAAa2B,MAAA,WAAkBhB,OAAQsB,KAAA,QAAA5E,KAAA,QAA6BgE,IAAKC,MAAA,SAAAY,GAAyB,OAAAvC,EAAA3C,cAAAgF,EAAApG,SAAuC+D,EAAAW,GAAA,gBAAsB,GAAAX,EAAAW,GAAA,KAAAR,EAAA,iBAAsCE,aAAamC,cAAA,QAAqBxB,OAAQe,WAAA,GAAAU,cAAA,EAAAC,eAAA1C,EAAAhJ,SAAAC,KAAA0L,cAAA,YAAAC,YAAA5C,EAAAhJ,SAAAE,SAAA2L,OAAA,yCAAA1L,MAAA6I,EAAAhJ,SAAAG,OAA6MuK,IAAKoB,iBAAA9C,EAAAnE,oBAAAkH,cAAA/C,EAAAjE,oBAA6EiE,EAAAW,GAAA,KAAAR,EAAA,aAA8Ba,OAAOgC,MAAA,OAAAC,QAAAjD,EAAApI,qBAAAgK,MAAA,OAAgEF,IAAKwB,iBAAA,SAAAX,GAAkCvC,EAAApI,qBAAA2K,MAAkCpC,EAAA,OAAAA,EAAA,WAA0Ba,OAAOE,MAAAlB,EAAAnI,YAAAsL,iBAAA,QAAAC,cAAA,QAAAd,KAAA,UAAsFnC,EAAA,OAAYE,aAAagD,QAAA,UAAkBlD,EAAA,gBAAqBW,YAAA,WAAAE,OAA8B1J,MAAA,UAAgB6I,EAAA,YAAiBe,OAAO3J,MAAAyI,EAAAnI,YAAA,KAAAuJ,SAAA,SAAAC,GAAsDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,OAAAwJ,IAAuCE,WAAA,uBAAgC,OAAAvB,EAAAW,GAAA,KAAAR,EAAA,OAAgCE,aAAagD,QAAA,UAAkBlD,EAAA,gBAAqBW,YAAA,WAAAE,OAA8B1J,MAAA,UAAgB6I,EAAA,aAAkBa,OAAOG,YAAA,QAAqBD,OAAQ3J,MAAAyI,EAAAnI,YAAA,KAAAuJ,SAAA,SAAAC,GAAsDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,OAAAwJ,IAAuCE,WAAA,qBAAgCvB,EAAAwB,GAAAxB,EAAA,cAAArF,GAAkC,OAAAwF,EAAA,aAAuBM,IAAA9F,EAAA4D,KAAAyC,OAAqB1J,MAAAqD,EAAA7D,KAAAS,MAAAoD,EAAA4D,UAAuC,WAAAyB,EAAAW,GAAA,KAAAR,EAAA,OAAmCE,aAAagD,QAAA,UAAkBlD,EAAA,gBAAqBW,YAAA,WAAAE,OAA8B1J,MAAA,UAAgB6I,EAAA,YAAiBe,OAAO3J,MAAAyI,EAAAnI,YAAA,KAAAuJ,SAAA,SAAAC,GAAsDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,OAAAwJ,IAAuCE,WAAA,uBAAgC,OAAAvB,EAAAW,GAAA,KAAAR,EAAA,OAAgCE,aAAagD,QAAA,UAAkBlD,EAAA,gBAAqBW,YAAA,WAAAE,OAA8B1J,MAAA,QAAc6I,EAAA,aAAkBa,OAAOG,YAAA,MAAmBD,OAAQ3J,MAAAyI,EAAAnI,YAAA,KAAAuJ,SAAA,SAAAC,GAAsDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,OAAAwJ,IAAuCE,WAAA,qBAAgCvB,EAAAwB,GAAAxB,EAAA,cAAArF,GAAkC,OAAAwF,EAAA,aAAuBM,IAAA9F,EAAA/B,GAAAoI,OAAmB1J,MAAAqD,EAAAhC,GAAApB,MAAAoD,EAAA/B,QAAmC,WAAAoH,EAAAW,GAAA,KAAAR,EAAA,gBAA4CW,YAAA,oBAAAE,OAAuC1J,MAAA,SAAe6I,EAAA,YAAiBa,OAAOsC,QAAA,sCAA+CpC,OAAQ3J,MAAAyI,EAAAnI,YAAA,IAAAuJ,SAAA,SAAAC,GAAqDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,MAAAwJ,IAAsCE,WAAA,sBAA+B,WAAAvB,EAAAW,GAAA,KAAAR,EAAA,QAAqCW,YAAA,gBAAAE,OAAmCuC,KAAA,UAAgBA,KAAA,WAAepD,EAAA,aAAkBa,OAAOtD,KAAA,WAAiBgE,IAAKC,MAAA,SAAAY,GAAyB,OAAAvC,EAAAhC,iBAA0BgC,EAAAW,GAAA,SAAAX,EAAAW,GAAA,KAAAR,EAAA,aAA8Ca,OAAOtD,KAAA,WAAiBgE,IAAKC,MAAA,SAAAY,GAAyBvC,EAAApI,sBAAA,MAAmCoI,EAAAW,GAAA,eAAAX,EAAAW,GAAA,KAAAR,EAAA,aAAoDa,OAAOgC,MAAA,SAAAC,QAAAjD,EAAArI,2BAAAiK,MAAA,OAAwEF,IAAKwB,iBAAA,SAAAX,GAAkCvC,EAAArI,2BAAA4K,MAAwCpC,EAAA,WAAgBa,OAAOmC,iBAAA,QAAAC,cAAA,QAAAd,KAAA,UAA8DnC,EAAA,OAAYE,aAAagD,QAAA,UAAkBlD,EAAA,gBAAqBW,YAAA,WAAAE,OAA8B1J,MAAA,UAAgB6I,EAAA,YAAiBe,OAAO3J,MAAAyI,EAAAnI,YAAA,KAAAuJ,SAAA,SAAAC,GAAsDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,OAAAwJ,IAAuCE,WAAA,uBAAgC,OAAAvB,EAAAW,GAAA,KAAAR,EAAA,OAAgCE,aAAagD,QAAA,UAAkBlD,EAAA,gBAAqBW,YAAA,WAAAE,OAA8B1J,MAAA,UAAgB6I,EAAA,aAAkBa,OAAOG,YAAA,QAAqBD,OAAQ3J,MAAAyI,EAAAnI,YAAA,KAAAuJ,SAAA,SAAAC,GAAsDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,OAAAwJ,IAAuCE,WAAA,qBAAgCvB,EAAAwB,GAAAxB,EAAA,cAAArF,GAAkC,OAAAwF,EAAA,aAAuBM,IAAA9F,EAAA4D,KAAAyC,OAAqB1J,MAAAqD,EAAA7D,KAAAS,MAAAoD,EAAA4D,UAAuC,WAAAyB,EAAAW,GAAA,KAAAR,EAAA,OAAmCE,aAAagD,QAAA,UAAkBlD,EAAA,gBAAqBW,YAAA,WAAAE,OAA8B1J,MAAA,UAAgB6I,EAAA,YAAiBe,OAAO3J,MAAAyI,EAAAnI,YAAA,KAAAuJ,SAAA,SAAAC,GAAsDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,OAAAwJ,IAAuCE,WAAA,uBAAgC,OAAAvB,EAAAW,GAAA,KAAAR,EAAA,OAAgCE,aAAagD,QAAA,UAAkBlD,EAAA,gBAAqBW,YAAA,WAAAE,OAA8B1J,MAAA,QAAc6I,EAAA,aAAkBa,OAAOG,YAAA,MAAmBD,OAAQ3J,MAAAyI,EAAAnI,YAAA,KAAAuJ,SAAA,SAAAC,GAAsDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,OAAAwJ,IAAuCE,WAAA,qBAAgCvB,EAAAwB,GAAAxB,EAAA,cAAArF,GAAkC,OAAAwF,EAAA,aAAuBM,IAAA9F,EAAA/B,GAAAoI,OAAmB1J,MAAAqD,EAAAhC,GAAApB,MAAAoD,EAAA/B,QAAmC,WAAAoH,EAAAW,GAAA,KAAAR,EAAA,gBAA4CW,YAAA,oBAAAE,OAAuC1J,MAAA,SAAe6I,EAAA,YAAiBa,OAAOsC,QAAA,sCAA+CpC,OAAQ3J,MAAAyI,EAAAnI,YAAA,IAAAuJ,SAAA,SAAAC,GAAqDrB,EAAAsB,KAAAtB,EAAAnI,YAAA,MAAAwJ,IAAsCE,WAAA,sBAA+B,OAAAvB,EAAAW,GAAA,KAAAR,EAAA,QAAiCW,YAAA,gBAAAE,OAAmCuC,KAAA,UAAgBA,KAAA,WAAepD,EAAA,aAAkBa,OAAOtD,KAAA,WAAiBgE,IAAKC,MAAA,SAAAY,GAAyB,OAAAvC,EAAAtD,0BAAmCsD,EAAAW,GAAA,SAAAX,EAAAW,GAAA,KAAAR,EAAA,aAA8Ca,OAAOtD,KAAA,WAAiBgE,IAAKC,MAAA,SAAAY,GAAyBvC,EAAArI,4BAAA,MAAyCqI,EAAAW,GAAA,sBAE/jQ6C,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACElN,EACAqJ,GATF,EAVA,SAAA8D,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/149.246023ef2722031f7fd7.js", "sourcesContent": ["import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'\r\n// var BASE_URL = '/api'\r\n// var BASE_URL = ''\r\n//服务类型查询\r\nexport const getFwlxList = data => createAPI(BASE_URL+\"/api/select/getFwlxList\", 'get',data)\r\n//新增流程\r\nexport const gzlJbxxAdd = data => createAPI(BASE_URL+\"/api/gzl_01_01/gzlJbxx/add\", 'post',data)\r\n//修改流程\r\nexport const gzlJbxxUpdate = data => createAPI(BASE_URL+\"/api/gzl_01_01/gzlJbxx/update\", 'post',data)\r\n//删除流程\r\nexport const gzlJbxxDelete = data => createAPI(BASE_URL+\"/api/gzl_01_01/gzlJbxx/delete\", 'post',data)\r\n//流程查询\r\nexport const gzlJbxxFindPageList = data => createAPI(BASE_URL+\"/api/gzl_01_01/gzlJbxx/findPageList\", 'get',data)\r\n\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/api/lhgl.js", "<template>\r\n  <div style=\"height: 100%;\">\r\n    <hsoft_top_title>\r\n      <template #left>流程配置</template>\r\n    </hsoft_top_title>\r\n    <!---->\r\n    <div style=\"text-align: right;\">\r\n      <el-form :inline=\"true\" :model=\"formInline\" class=\"demo-form-inline\" style=\"float:left\">\r\n        <el-form-item label=\"服务名称\">\r\n          <el-input v-model=\"formInline.fwmc\" placeholder=\"服务名称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"服务类型\">\r\n          <el-select v-model=\"formInline.fwlx\" placeholder=\"服务类型\">\r\n            <el-option v-for=\"item in fwlx\" :label=\"item.fwlx\" :value=\"item.fwid\" :key=\"item.fwid\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"服务状态\">\r\n          <el-select v-model=\"formInline.fwzt\" placeholder=\"服务状态\">\r\n            <el-option v-for=\"item in fwzt\" :label=\"item.zt\" :value=\"item.id\" :key=\"item.id\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"onSubmit\">查询</el-button>\r\n        <el-button type=\"warning\" icon=\"el-icon-circle-close\" @click=\"cz\">重置</el-button>\r\n      </el-form>\r\n      <el-button type=\"success\" @click=\"showAddDialog\">添加</el-button>\r\n      <!-- <el-button type=\"primary\" @click=\"getSettingList()\">查询</el-button> -->\r\n    </div>\r\n    <el-table class=\"table\" :data=\"settingList\" border :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\"\r\n      style=\"width: 100%;border:1px solid #EBEEF5;\" height=\"calc(100% - 32px - 60px - 32px - 10px - 10px)\" stripe>\r\n      <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"fwmc\" label=\"服务名称\" width=\"\"></el-table-column>\r\n      <el-table-column prop=\"fwlx\" label=\"服务类型\" :formatter=\"forfwlx\"></el-table-column>\r\n\r\n      <el-table-column prop=\"fwzt\" label=\"服务状态\" width=\"\" align=\"center\" :formatter=\"forfwzt\"></el-table-column>\r\n      <el-table-column prop=\"pxh\" label=\"排序号\" width=\"\"></el-table-column>\r\n      <el-table-column prop=\"cjr\" label=\"创建人\" width=\"\"></el-table-column>\r\n      <el-table-column prop=\"cjsj\" label=\"创建时间\" width=\"\" :formatter=\"forcjsj\"></el-table-column>\r\n      <el-table-column prop=\"\" label=\"操作\" width=\"\">\r\n        <template slot-scope=\"scoped\">\r\n          <el-button size=\"small\" type=\"text\" @click=\"modifySetting(scoped.row)\">修改</el-button>\r\n          <el-button size=\"small\" type=\"text\" @click=\"lcpzSetting(scoped.row)\">流程配置</el-button>\r\n          <el-button size=\"small\" type=\"text\" @click=\"deleteSetting(scoped.row)\" style=\"color:#F56C6C;\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-pagination background @current-change=\"handleCurrentChange\" @size-change=\"handleSizeChange\" :pager-count=\"5\"\r\n      :current-page=\"pageInfo.page\" :page-sizes=\"[5, 10, 20, 30]\" :page-size=\"pageInfo.pageSize\"\r\n      layout=\"total, prev, pager, sizes,next, jumper\" :total=\"pageInfo.total\" style=\"    padding-top: 10px;\">\r\n    </el-pagination>\r\n    <!---->\r\n    <!-- 添加流程 -->\r\n    <el-dialog title=\"新建流程\" :visible.sync=\"dialogVisibleSetting\" width=\"35%\">\r\n      <div>\r\n        <el-form :model=\"settingForm\" :label-position=\"'right'\" label-width=\"120px\" size=\"mini\">\r\n          <div style=\"display:flex\">\r\n            <el-form-item label=\"服务名称\" class=\"one-line\">\r\n              <el-input v-model=\"settingForm.fwmc\"></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div style=\"display:flex\">\r\n            <el-form-item label=\"服务类型\" class=\"one-line\">\r\n              <el-select v-model=\"settingForm.fwlx\" placeholder=\"服务类型\">\r\n                <el-option v-for=\"item in fwlx\" :label=\"item.fwlx\" :value=\"item.fwid\" :key=\"item.fwid\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n          <div style=\"display:flex\">\r\n            <el-form-item label=\"服务说明\" class=\"one-line\">\r\n              <el-input v-model=\"settingForm.fwsm\"></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div style=\"display:flex\">\r\n            <el-form-item label=\"状态\" class=\"one-line\">\r\n              <el-select v-model=\"settingForm.fwzt\" placeholder=\"状态\">\r\n                <el-option v-for=\"item in fwzt\" :label=\"item.zt\" :value=\"item.id\" :key=\"item.id\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <el-form-item label=\"排序号\" class=\"one-line-textarea\">\r\n            <el-input v-model=\"settingForm.pxh\" oninput=\"value=value.replace(/[^\\d.]/g,'')\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"addSetting()\">保 存</el-button>\r\n        <el-button type=\"warning\" @click=\"dialogVisibleSetting = false\">关 闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 修改系统参数 -->\r\n    <el-dialog title=\"修改用户密码\" :visible.sync=\"dialogVisibleSettingModify\" width=\"35%\">\r\n\r\n      <el-form :label-position=\"'right'\" label-width=\"120px\" size=\"mini\">\r\n        <div style=\"display:flex\">\r\n          <el-form-item label=\"服务名称\" class=\"one-line\">\r\n            <el-input v-model=\"settingForm.fwmc\"></el-input>\r\n          </el-form-item>\r\n        </div>\r\n        <div style=\"display:flex\">\r\n          <el-form-item label=\"服务类型\" class=\"one-line\">\r\n            <el-select v-model=\"settingForm.fwlx\" placeholder=\"服务类型\">\r\n              <el-option v-for=\"item in fwlx\" :label=\"item.fwlx\" :value=\"item.fwid\" :key=\"item.fwid\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </div>\r\n        <div style=\"display:flex\">\r\n          <el-form-item label=\"服务说明\" class=\"one-line\">\r\n            <el-input v-model=\"settingForm.fwsm\"></el-input>\r\n          </el-form-item>\r\n        </div>\r\n        <div style=\"display:flex\">\r\n          <el-form-item label=\"状态\" class=\"one-line\">\r\n            <el-select v-model=\"settingForm.fwzt\" placeholder=\"状态\">\r\n              <el-option v-for=\"item in fwzt\" :label=\"item.zt\" :value=\"item.id\" :key=\"item.id\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </div>\r\n\r\n        <el-form-item label=\"排序号\" class=\"one-line-textarea\">\r\n          <el-input v-model=\"settingForm.pxh\" oninput=\"value=value.replace(/[^\\d.]/g,'')\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"modifySettingDialog()\">确 定</el-button>\r\n        <el-button type=\"warning\" @click=\"dialogVisibleSettingModify = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!---->\r\n    <!---->\r\n    <!---->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'\r\n\r\n  import {\r\n    getWindowLocation\r\n  } from '../../../utils/windowLocation'\r\n\r\n  import {\r\n    dateFormatChinese,\r\n    dateFormatNYRChinese\r\n  } from '../../../utils/moment'\r\n  import {\r\n    getAllYhxx, //获取全部sm人员\r\n    getZzjgList, //获取全部机构\r\n  } from '../../../api/index'\r\n  import {\r\n    getFwlxList, //服务类型查询\r\n    gzlJbxxFindPageList, //流程管理查询\r\n    gzlJbxxAdd, //流程管理添加\r\n    gzlJbxxUpdate, //流程管理修改\r\n    gzlJbxxDelete, //流程管理删除\r\n  } from '../../../api/lhgl'\r\n  // import { writeSystemOptionsLog } from '../../../utils/logUtils'\r\n\r\n  // import { checkArr, decideChange } from '../../../utils/utils'\r\n\r\n  // // 系统参数设置表\r\n  // import {\r\n  //   // 插入系统参数表\r\n  //   insertSettingList,\r\n  //   // 查询系统参数表\r\n  //   selectSettingList,\r\n  //   // 删除系统参数设置\r\n  //   deleteSettingList,\r\n  //   // 修改系统参数设置\r\n  //   updateSettingList\r\n  // } from '../../../db/zczpSystem/zczpSysyemDb'\r\n\r\n  import {\r\n    addXtcs,\r\n    deleteXtcs,\r\n    updateXtcs,\r\n    getXtcsPage,\r\n    getcszjldw\r\n  } from '../../../api/cssz'\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        //查询\r\n        formInline: {\r\n          fwmc: '',\r\n          fwlx: '',\r\n          fwzt: '',\r\n        },\r\n        // 分页信息\r\n        pageInfo: {\r\n          page: 1,\r\n          pageSize: 10,\r\n          total: 0\r\n        },\r\n        regionOption: [], //部门数据\r\n        //部门tree设置\r\n        regionParams: {\r\n          label: 'label', //这里可以配置你们后端返回的属性\r\n          value: 'label',\r\n          children: 'childrenRegionVo',\r\n          expandTrigger: 'click',\r\n          checkStrictly: true,\r\n        }, //地域信息配置参数\r\n        // 更新系统参数dialog\r\n        dialogVisibleSettingModify: false,\r\n        // 添加系统参数dialog\r\n        dialogVisibleSetting: false,\r\n        settingForm: {},\r\n        settingFormOld: {},\r\n        cszlx: 1,\r\n        // 系统参数设置表格数据\r\n        settingList: [],\r\n        pickerOptions: {\r\n          disabledDate: time => {\r\n            if (this.selectDate == null) {\r\n              return false\r\n            } else {\r\n              return (this.selectDate.getFullYear() != time.getFullYear())\r\n            }\r\n          },\r\n          onPick: date => {\r\n            // 如果只选择一个则保存至selectDate 否则selectDate 为空\r\n            if (date.minDate && !date.maxDate) {\r\n              this.selectDate = date.minDate\r\n            } else {\r\n              this.selectDate = null\r\n            }\r\n          }\r\n        },\r\n        jldwList: [],\r\n        //服务类型\r\n        fwlx: [],\r\n        //服务状态\r\n        fwzt: [{\r\n            zt: '不可用',\r\n            id: 0\r\n          },\r\n          {\r\n            zt: '草稿',\r\n            id: 1,\r\n          },\r\n          {\r\n            zt: '发布',\r\n            id: 2,\r\n          },\r\n        ]\r\n      }\r\n    },\r\n    components: {\r\n      hsoft_top_title\r\n    },\r\n    methods: {\r\n      //服务类型查询\r\n      async fwlxList() {\r\n        let data = await getFwlxList()\r\n        this.fwlx = data.data\r\n      },\r\n      //全部组织机构List\r\n      async zzjg() {\r\n        let zzjgList = await getZzjgList()\r\n        console.log(zzjgList);\r\n        this.zzjgmc = zzjgList\r\n        let shu = []\r\n        console.log(this.zzjgmc);\r\n        this.zzjgmc.forEach(item => {\r\n          let childrenRegionVo = []\r\n          this.zzjgmc.forEach(item1 => {\r\n            if (item.bmm == item1.fbmm) {\r\n              // console.log(item, item1);\r\n              childrenRegionVo.push(item1)\r\n              // console.log(childrenRegionVo);\r\n              item.childrenRegionVo = childrenRegionVo\r\n            }\r\n          });\r\n          // console.log(item);\r\n          shu.push(item)\r\n        })\r\n\r\n        console.log(shu);\r\n        console.log(shu[0].childrenRegionVo);\r\n        let shuList = []\r\n        shu.forEach(item => {\r\n          if (item.fbmm == '') {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n        console.log(shuList);\r\n        shuList[0].childrenRegionVo.forEach(item => {\r\n          this.regionOption.push(item)\r\n        })\r\n      },\r\n      //获取计量单位\r\n      async getjldw() {\r\n        this.jldwList = await getcszjldw()\r\n      },\r\n      showAddDialog() {\r\n        this.settingForm = {}\r\n        this.dialogVisibleSetting = true\r\n      },\r\n      // 格式化时间\r\n      formatTime(time) {\r\n        return dateFormatChinese(new Date(time))\r\n      },\r\n      //查询按钮\r\n      onSubmit() {\r\n        this.getSettingList()\r\n      },\r\n      //重置按钮\r\n      cz() {\r\n        this.formInline = {}\r\n      },\r\n      handleCurrentChange(val) {\r\n        this.pageInfo.page = val\r\n        this.getSettingList()\r\n      },\r\n      handleSizeChange(val) {\r\n        this.pageInfo.pageSize = val\r\n        this.getSettingList()\r\n      },\r\n      // 修改(表格)\r\n      modifySetting(row) {\r\n        // let csz = row.csz\r\n        // console.log(csz,checkArr(csz))\r\n        // if (checkArr(csz)) {\r\n        //   row.csz[0] = csz[0].month + '-' + csz[0].day\r\n        //   row.csz[1] = csz[1].month + '-' + csz[1].day\r\n        // }\r\n        this.settingForm = JSON.parse(JSON.stringify(row))\r\n        this.settingFormOld = JSON.parse(JSON.stringify(row))\r\n        this.dialogVisibleSettingModify = true\r\n      },\r\n      //状态流程配置按钮\r\n      lcpzSetting(row) {\r\n        // this.$router.push('/lhpzSetting');\r\n        this.$router.push({\r\n          path: '/lhpzSetting',\r\n          query: {\r\n            fwdyid: row.fwdyid\r\n          }\r\n        })\r\n      },\r\n      // 修改（dialog）\r\n      async modifySettingDialog() {\r\n\r\n        let params = JSON.parse(JSON.stringify(this.settingForm))\r\n        params.pxh = params.pxh * 1\r\n\r\n        let data = await gzlJbxxUpdate(params)\r\n        if (data.code == 10000) {\r\n          this.getSettingList()\r\n        } else if (data.code == 10002) {\r\n          this.$message.error(data.message);\r\n        }\r\n        // // 写入日志\r\n        // // 加入审计日志需要显示的内容\r\n        // let paramsExtra = {\r\n        //   bs: params.csbs,\r\n        //   modifyArr: []\r\n        // }\r\n        // // 判定修改\r\n        // paramsExtra.modifyArr = decideChange(this.settingFormOld, params, ['settingid', 'gxsj'])\r\n        // Object.assign(params, paramsExtra)\r\n        // let logParams = {\r\n        //   xyybs: 'yybs_cssz',\r\n        //   ymngnmc: '修改',\r\n        //   extraParams: params\r\n        // }\r\n        // writeSystemOptionsLog(logParams)\r\n        this.dialogVisibleSettingModify = false\r\n      },\r\n      // 删除参数设置\r\n      async deleteSetting(row) {\r\n        // console.log(row);\r\n        let params = {\r\n          fwdyid: row.fwdyid\r\n        }\r\n        let data = await gzlJbxxDelete(params)\r\n        if (data.code == 10000) {\r\n          this.getSettingList()\r\n\r\n          this.$message({\r\n            message: '删除成功',\r\n            type: 'success'\r\n          });\r\n\r\n        }else if(data.code == 20002){\r\n          this.$message.error(data.message);\r\n        }\r\n        // // 写入日志\r\n        // // 加入审计日志需要显示的内容\r\n        // let paramsExtra = {\r\n        //   bs: row.csbs,\r\n        //   modifyArr: []\r\n        // }\r\n        // // 判定修改\r\n        // paramsExtra.modifyArr = decideChange(row, {}, ['settingid', 'gxsj'])\r\n        // Object.assign(row, paramsExtra)\r\n        // let logParams = {\r\n        //   xyybs: 'yybs_cssz',\r\n        //   ymngnmc: '删除',\r\n        //   extraParams: row\r\n        // }\r\n        // writeSystemOptionsLog(logParams)\r\n      },\r\n      // 获取参数设置集合\r\n      async getSettingList() {\r\n        // this.settingForm = {}\r\n        // let params = {}\r\n        // Object.assign(params, this.pageInfo)\r\n        // let settingPage = selectSettingList(params)\r\n        // this.settingList = settingPage.list\r\n        // this.pageInfo.total = settingPage.total\r\n        let params = {\r\n          page: this.pageInfo.page,\r\n          pageSize: this.pageInfo.pageSize,\r\n          fwmc: this.formInline.fwmc,\r\n          fwlx: this.formInline.fwlx,\r\n          fwzt: this.formInline.fwzt,\r\n        }\r\n        let settingPage = await gzlJbxxFindPageList(params)\r\n        this.settingList = settingPage.data.content\r\n        // this.settingList.forEach((item) => {\r\n        //   if (item.cszlx != 1) {\r\n        //     item.cszDate = item.cszDate.slice(5, 11)\r\n        //     item.cszDate2 = item.cszDate2.slice(5, 11)\r\n        //   }\r\n        // })\r\n        this.pageInfo.total = settingPage.data.pageInfo.total\r\n      },\r\n      // 添加参数设置\r\n      async addSetting() {\r\n        let params = JSON.parse(JSON.stringify(this.settingForm))\r\n        params.pxh = params.pxh * 1\r\n        let data = await gzlJbxxAdd(params)\r\n        if (data.code == 10000) {\r\n          this.getSettingList()\r\n        } else if (data.code == 10002) {\r\n          this.$message.error(data.message);\r\n        }\r\n        // 写入日志\r\n        // 加入审计日志需要显示的内容\r\n        // let paramsExtra = {\r\n        //   bs: params.csbs,\r\n        //   modifyArr: []\r\n        // }\r\n        // // 判定修改\r\n        // paramsExtra.modifyArr = decideChange({}, params, ['settingid', 'gxsj'])\r\n        // Object.assign(params, paramsExtra)\r\n        // let logParams = {\r\n        //   xyybs: 'yybs_cssz',\r\n        //   ymngnmc: '添加',\r\n        //   extraParams: params\r\n        // }\r\n        // writeSystemOptionsLog(logParams)\r\n        this.dialogVisibleSetting = false\r\n      },\r\n      forfwlx(row) {\r\n        console.log(row);\r\n        let hxsj\r\n        this.fwlx.forEach(item => {\r\n          if (row.fwlx == item.fwid) {\r\n            hxsj = item.fwlx\r\n          }\r\n        })\r\n        return hxsj\r\n      },\r\n      forfwzt(row) {\r\n        console.log(row);\r\n        let hxsj\r\n        this.fwzt.forEach(item => {\r\n          if (row.fwzt == item.id) {\r\n            hxsj = item.zt\r\n          }\r\n        })\r\n        return hxsj\r\n      },\r\n      forcjsj(row) {\r\n        return dateFormatNYRChinese(row)\r\n      },\r\n      //模糊匹配姓名\r\n      querySearchxm(queryString, cb) {\r\n        var restaurants = this.restaurantsxm;\r\n        console.log(\"restaurants\", restaurants);\r\n        var results = queryString ? restaurants.filter(this.createFilterzw(queryString)) : restaurants;\r\n        console.log(\"results\", results);\r\n        // 调用 callback 返回建议列表的数据\r\n        // for (var i = 0; i < results.length; i++) {\r\n        //   for (var j = i + 1; j < results.length; j++) {\r\n        //     if (results[i].xm === results[j].xm) {\r\n        //       results.splice(j, 1);\r\n        //       j--;\r\n        //     }\r\n        //   }\r\n        // }\r\n        cb(results);\r\n        console.log(\"cb(results.zw)\", results);\r\n      },\r\n      createFilterxm(queryString) {\r\n        return (restaurant) => {\r\n          return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);\r\n        };\r\n      },\r\n      async xmmh() {\r\n        let resList = await getAllYhxx()\r\n        // console.log(resList);\r\n        this.restaurantsxm = resList;\r\n        this.restaurantszj = resList;\r\n        // console.log(\"this.restaurants\", this.restaurantsbm);\r\n        // console.log(resList)\r\n      },\r\n    },\r\n    mounted() {\r\n      this.fwlxList()\r\n      this.zzjg()\r\n      this.xmmh()\r\n      this.getjldw()\r\n      //\r\n      this.getSettingList()\r\n      //\r\n      console.log(new Date(2022, 11, 1))\r\n    }\r\n  }\r\n\r\n</script>\r\n\r\n<style scoped>\r\n  .out-card {\r\n    /* margin-bottom: 10px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */\r\n  }\r\n\r\n  /**单位信息区域**/\r\n  .out-card .out-card-div {\r\n    font-size: 13px;\r\n    padding: 5px 20px;\r\n  }\r\n\r\n  .out-card .out-card-div div {\r\n    padding: 10px 5px;\r\n    display: flex;\r\n  }\r\n\r\n  .out-card .dwxx div:hover {\r\n    background: #f4f4f5;\r\n    border-radius: 20px;\r\n  }\r\n\r\n  .out-card .dwxx div label {\r\n    /* background-color: red; */\r\n    width: 125px;\r\n    display: inline-block;\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #909399;\r\n  }\r\n\r\n  .out-card .dwxx div span {\r\n    /* background-color: rgb(33, 92, 79); */\r\n    flex: 1;\r\n    display: inline-block;\r\n    padding-left: 20px;\r\n  }\r\n\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/xtsz/lhglSetting.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"height\":\"100%\"}},[_c('hsoft_top_title',{scopedSlots:_vm._u([{key:\"left\",fn:function(){return [_vm._v(\"流程配置\")]},proxy:true}])}),_vm._v(\" \"),_c('div',{staticStyle:{\"text-align\":\"right\"}},[_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"left\"},attrs:{\"inline\":true,\"model\":_vm.formInline}},[_c('el-form-item',{attrs:{\"label\":\"服务名称\"}},[_c('el-input',{attrs:{\"placeholder\":\"服务名称\"},model:{value:(_vm.formInline.fwmc),callback:function ($$v) {_vm.$set(_vm.formInline, \"fwmc\", $$v)},expression:\"formInline.fwmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"服务类型\"}},[_c('el-select',{attrs:{\"placeholder\":\"服务类型\"},model:{value:(_vm.formInline.fwlx),callback:function ($$v) {_vm.$set(_vm.formInline, \"fwlx\", $$v)},expression:\"formInline.fwlx\"}},_vm._l((_vm.fwlx),function(item){return _c('el-option',{key:item.fwid,attrs:{\"label\":item.fwlx,\"value\":item.fwid}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"服务状态\"}},[_c('el-select',{attrs:{\"placeholder\":\"服务状态\"},model:{value:(_vm.formInline.fwzt),callback:function ($$v) {_vm.$set(_vm.formInline, \"fwzt\", $$v)},expression:\"formInline.fwzt\"}},_vm._l((_vm.fwzt),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.zt,\"value\":item.id}})}),1)],1),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"查询\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\",\"icon\":\"el-icon-circle-close\"},on:{\"click\":_vm.cz}},[_vm._v(\"重置\")])],1),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"success\"},on:{\"click\":_vm.showAddDialog}},[_vm._v(\"添加\")])],1),_vm._v(\" \"),_c('el-table',{staticClass:\"table\",staticStyle:{\"width\":\"100%\",\"border\":\"1px solid #EBEEF5\"},attrs:{\"data\":_vm.settingList,\"border\":\"\",\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"height\":\"calc(100% - 32px - 60px - 32px - 10px - 10px)\",\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"fwmc\",\"label\":\"服务名称\",\"width\":\"\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"fwlx\",\"label\":\"服务类型\",\"formatter\":_vm.forfwlx}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"fwzt\",\"label\":\"服务状态\",\"width\":\"\",\"align\":\"center\",\"formatter\":_vm.forfwzt}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"pxh\",\"label\":\"排序号\",\"width\":\"\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cjr\",\"label\":\"创建人\",\"width\":\"\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cjsj\",\"label\":\"创建时间\",\"width\":\"\",\"formatter\":_vm.forcjsj}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"\",\"label\":\"操作\",\"width\":\"\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scoped){return [_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.modifySetting(scoped.row)}}},[_vm._v(\"修改\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.lcpzSetting(scoped.row)}}},[_vm._v(\"流程配置\")]),_vm._v(\" \"),_c('el-button',{staticStyle:{\"color\":\"#F56C6C\"},attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.deleteSetting(scoped.row)}}},[_vm._v(\"删除\")])]}}])})],1),_vm._v(\" \"),_c('el-pagination',{staticStyle:{\"padding-top\":\"10px\"},attrs:{\"background\":\"\",\"pager-count\":5,\"current-page\":_vm.pageInfo.page,\"page-sizes\":[5, 10, 20, 30],\"page-size\":_vm.pageInfo.pageSize,\"layout\":\"total, prev, pager, sizes,next, jumper\",\"total\":_vm.pageInfo.total},on:{\"current-change\":_vm.handleCurrentChange,\"size-change\":_vm.handleSizeChange}}),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"新建流程\",\"visible\":_vm.dialogVisibleSetting,\"width\":\"35%\"},on:{\"update:visible\":function($event){_vm.dialogVisibleSetting=$event}}},[_c('div',[_c('el-form',{attrs:{\"model\":_vm.settingForm,\"label-position\":'right',\"label-width\":\"120px\",\"size\":\"mini\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"服务名称\"}},[_c('el-input',{model:{value:(_vm.settingForm.fwmc),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fwmc\", $$v)},expression:\"settingForm.fwmc\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"服务类型\"}},[_c('el-select',{attrs:{\"placeholder\":\"服务类型\"},model:{value:(_vm.settingForm.fwlx),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fwlx\", $$v)},expression:\"settingForm.fwlx\"}},_vm._l((_vm.fwlx),function(item){return _c('el-option',{key:item.fwid,attrs:{\"label\":item.fwlx,\"value\":item.fwid}})}),1)],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"服务说明\"}},[_c('el-input',{model:{value:(_vm.settingForm.fwsm),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fwsm\", $$v)},expression:\"settingForm.fwsm\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"状态\"}},[_c('el-select',{attrs:{\"placeholder\":\"状态\"},model:{value:(_vm.settingForm.fwzt),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fwzt\", $$v)},expression:\"settingForm.fwzt\"}},_vm._l((_vm.fwzt),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.zt,\"value\":item.id}})}),1)],1)],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line-textarea\",attrs:{\"label\":\"排序号\"}},[_c('el-input',{attrs:{\"oninput\":\"value=value.replace(/[^\\\\d.]/g,'')\"},model:{value:(_vm.settingForm.pxh),callback:function ($$v) {_vm.$set(_vm.settingForm, \"pxh\", $$v)},expression:\"settingForm.pxh\"}})],1)],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.addSetting()}}},[_vm._v(\"保 存\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.dialogVisibleSetting = false}}},[_vm._v(\"关 闭\")])],1)]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"修改用户密码\",\"visible\":_vm.dialogVisibleSettingModify,\"width\":\"35%\"},on:{\"update:visible\":function($event){_vm.dialogVisibleSettingModify=$event}}},[_c('el-form',{attrs:{\"label-position\":'right',\"label-width\":\"120px\",\"size\":\"mini\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"服务名称\"}},[_c('el-input',{model:{value:(_vm.settingForm.fwmc),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fwmc\", $$v)},expression:\"settingForm.fwmc\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"服务类型\"}},[_c('el-select',{attrs:{\"placeholder\":\"服务类型\"},model:{value:(_vm.settingForm.fwlx),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fwlx\", $$v)},expression:\"settingForm.fwlx\"}},_vm._l((_vm.fwlx),function(item){return _c('el-option',{key:item.fwid,attrs:{\"label\":item.fwlx,\"value\":item.fwid}})}),1)],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"服务说明\"}},[_c('el-input',{model:{value:(_vm.settingForm.fwsm),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fwsm\", $$v)},expression:\"settingForm.fwsm\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"状态\"}},[_c('el-select',{attrs:{\"placeholder\":\"状态\"},model:{value:(_vm.settingForm.fwzt),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fwzt\", $$v)},expression:\"settingForm.fwzt\"}},_vm._l((_vm.fwzt),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.zt,\"value\":item.id}})}),1)],1)],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line-textarea\",attrs:{\"label\":\"排序号\"}},[_c('el-input',{attrs:{\"oninput\":\"value=value.replace(/[^\\\\d.]/g,'')\"},model:{value:(_vm.settingForm.pxh),callback:function ($$v) {_vm.$set(_vm.settingForm, \"pxh\", $$v)},expression:\"settingForm.pxh\"}})],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.modifySettingDialog()}}},[_vm._v(\"确 定\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.dialogVisibleSettingModify = false}}},[_vm._v(\"取 消\")])],1)],1)],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-5bb6f2ef\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/xtsz/lhglSetting.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-5bb6f2ef\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./lhglSetting.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./lhglSetting.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./lhglSetting.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-5bb6f2ef\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./lhglSetting.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-5bb6f2ef\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/xtsz/lhglSetting.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}