webpackJsonp([273],{XT5k:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n("fZjL"),r=n.n(a),s=n("mvHQ"),i=n.n(s),o=n("Xxa5"),c=n.n(o),l=n("exGp"),d=n.n(l),u=n("gyMJ"),m=n("jmdr"),p=n("kCU4"),h=n("dwgX"),f=n("UdKG"),g={components:{BaseHeader:h.a,BaseTable:f.a},props:{},data:function(){var e=this;return{loading:!1,headerCellStyle:{background:"#EEF7FF",color:"#4D91F8"},formInline:{},dialogVisible:!1,ryDatas:[],page:1,pageSize:5,page1:1,pageSize1:10,ryChoose:{bm:"",xm:""},total:0,total1:0,radioIdSelect:"",smryList:[],scjtlist:[{mc:"审批中",id:0},{mc:"通过",id:1},{mc:"已驳回",id:2},{mc:"草稿",id:3}],dqztlist:[{mc:"审批中",id:0},{mc:"已结束",id:1},{mc:"已驳回",id:2},{mc:"草稿",id:3}],rowdata:[],regionOption:[],regionParams:{label:"label",value:"label",children:"childrenRegionVo",expandTrigger:"click",checkStrictly:!0},params:{name:"",tmjssj:""},columns:[{type:"searchInput",name:"申请人",value:"name",placeholder:"申请人"},{type:"dataRange",name:"变更时间",value:"tmjssj",startPlaceholder:"变更起始时间",rangeSeparator:"至",endPlaceholder:"变更结束时间",format:"yyyy-MM-dd"},{type:"button",name:"查询",disabled:!1,icon:"el-icon-search",mold:"primary"},{type:"button",name:"重置",disabled:!1,icon:"el-icon-circle-close",mold:"warning"}],tableColumns:[{name:"申请人",prop:"xqr",scopeType:"text",formatter:!1},{name:"申请人部门",prop:"sqbm",scopeType:"text",formatter:!1},{name:"场所名称",prop:"csmc",scopeType:"text",formatter:!1,showOverflowTooltip:!0},{name:"审查时间",prop:"bgrq",scopeType:"text",formatter:!1},{name:"审查结果",prop:"Lcfwslzt",scopeType:"text",formatter:function(e,t,n,a){var r=[{mc:"审批中",id:0},{mc:"通过",id:1},{mc:"已驳回",id:2},{mc:"草稿",id:3}].find(function(e){return e.id===n});return r?r.mc:""}},{name:"当前状态",prop:"Lcfwslzt",scopeType:"text",formatter:function(e,t,n,a){var r=[{mc:"审批中",id:0},{mc:"已结束",id:1},{mc:"已驳回",id:2},{mc:"草稿",id:3}].find(function(e){return e.id===n});return r?r.mc:""}}],handleColumn:[{name:"编辑",disabled:!1,show:!0,formatter:function(t,n){return 3==t.Lcfwslzt&&t.cjrid==e.loginName?"编辑":0==t.Lcfwslzt||1==t.Lcfwslzt||2==t.Lcfwslzt?"查看":void 0}}],handleColumnProp:{label:"操作",width:"230",align:"left"},handleColumnApply:[],loginName:""}},computed:{},mounted:function(){this.onfwid(),this.getLoginYhm(),this.rysclist(),this.zzjg()},methods:{getLoginYhm:function(){var e=this;return d()(c.a.mark(function t(){var n;return c.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(p.j)();case 2:n=t.sent,e.loginName=n.yhm;case 4:case"end":return t.stop()}},t,e)}))()},handleSizeChange:function(e){this.page1=1,this.pageSize1=e,this.rysclist()},handleCurrentChange:function(e){this.page1=e,this.rysclist()},selectBtn:function(e){this.rowdata=e,console.log(e)},shanchu:function(){var e=this;0==this.rowdata.length?this.$message({message:"未选择想要删除的数据",type:"warning"}):this.$confirm("此操作将永久删除该申请, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t;e.rowdata.forEach((t=d()(c.a.mark(function t(n){var a;return c.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a={slid:n.slid},t.next=3,Object(m.a)(a);case 3:1e4==t.sent.code&&(e.$message({message:"删除成功",type:"success"}),e.rysclist());case 5:case"end":return t.stop()}},t,e)})),function(e){return t.apply(this,arguments)}))}).catch(function(){e.$message({type:"info",message:"已取消删除"})})},handleBtnAll:function(e,t){"查询"==t.name?(this.params=JSON.parse(i()(e)),this.page1=1,this.rysclist()):"重置"==t.name&&(this.params={name:"",tmjssj:""})},rysclist:function(e){var t=this;return d()(c.a.mark(function e(){var n,a;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n={xqr:t.params.name,page:t.page1,pageSize:t.pageSize1},null!=t.params.tmjssj&&(n.kssj=t.params.tmjssj[0],n.jssj=t.params.tmjssj[1]),e.next=4,Object(m.e)(n);case 4:(a=e.sent).records?(t.smryList=a.records,t.total1=a.total):t.$message.error("数据获取失败！");case 6:case"end":return e.stop()}},e,t)}))()},searchRy:function(){this.tableKey++,this.page=1,this.sendApplay()},sendApplay:function(){var e=this;return d()(c.a.mark(function t(){return c.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:e.$router.push({path:"/csbgscTable",query:{type:"add"}});case 1:case"end":return t.stop()}},t,e)}))()},handleCurrentChangeRy:function(e){this.page=e,this.sendApplay()},handleSizeChangeRy:function(e){this.page=1,this.pageSize=e,this.sendApplay()},handleSelectionChange:function(e,t){this.radioIdSelect=t},submitRy:function(){var e=this;return d()(c.a.mark(function t(){var n;return c.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.loading=!0,!(""!=e.radioIdSelect&&r()(e.radioIdSelect).length>0)){t.next=10;break}return e.loading=!1,t.next=5,Object(u._11)({smryid:e.radioIdSelect.smryid});case 5:n=t.sent,e.radioIdSelect.zp=n,e.$router.push({path:"/ztqsscTable",query:{type:"add",datas:e.radioIdSelect}}),t.next=12;break;case 10:e.$message.error("请选择涉密人员"),e.loading=!1;case 12:case"end":return t.stop()}},t,e)}))()},scjgsj:function(e){var t=void 0;return this.scjtlist.forEach(function(n){n.id==e.Lcfwslzt&&(t=n.mc)}),t},dqztsj:function(e){var t=void 0;return this.dqztlist.forEach(function(n){n.id==e.Lcfwslzt&&(t=n.mc)}),t},onfwid:function(){var e=this;return d()(c.a.mark(function t(){var n,a;return c.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n={fwlx:6},t.next=3,Object(u.P)(n);case 3:a=t.sent,console.log(a),e.fwdyid=a.data.fwdyid;case 6:case"end":return t.stop()}},t,e)}))()},operateBtn:function(e,t){var n=this;return d()(c.a.mark(function a(){var r;return c.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:console.log(e),"编辑"==t?(n.loading=!1,n.$router.push({path:"/csbgscTable",query:{type:"update",jlid:e.jlid,slid:e.slid}})):"查看"==t&&(r=n.fwdyid,console.log(r),""==n.fwdyid||void 0==n.fwdyid?n.$message.error("请到流程管理进行配置"):n.$router.push({path:"/csbgscblxxscb",query:{lx:"场所变更",list:e,fwdyid:r,slid:e.slid}}));case 2:case"end":return a.stop()}},a,n)}))()},zzjg:function(){var e=this;return d()(c.a.mark(function t(){var n,a,r,s;return c.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(u._14)();case 2:return n=t.sent,e.zzjgmc=n,a=[],e.zzjgmc.forEach(function(t){var n=[];e.zzjgmc.forEach(function(e){t.bmm==e.fbmm&&(n.push(e),t.childrenRegionVo=n)}),a.push(t)}),r=[],t.next=9,Object(u.U)();case 9:""==(s=t.sent).fbmm&&a.forEach(function(e){""==e.fbmm&&r.push(e)}),""!=s.fbmm&&a.forEach(function(e){console.log(e),e.fbmm==s.fbmm&&r.push(e)}),r[0].childrenRegionVo.forEach(function(t){e.regionOption.push(t)});case 13:case"end":return t.stop()}},t,e)}))()},bmSelectChange:function(e){console.log(e),void 0!=e&&(this.ryChoose.bm=e.join("/"))}},watch:{}},y={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"bg_con"},[n("div",{staticClass:"container"},[n("BaseHeader",{attrs:{columns:e.columns,params:e.params},on:{handleBtn:e.handleBtnAll}}),e._v(" "),n("el-form",{staticClass:"fr",attrs:{inline:!0,model:e.formInline,size:"medium"}},[n("el-form-item",{staticClass:"fr"},[n("el-button",{attrs:{type:"danger",size:"medium",icon:"el-icon-delete-solid"},on:{click:e.shanchu}},[e._v("\n                    删除\n                ")])],1),e._v(" "),n("el-form-item",{staticClass:"fr"},[n("el-button",{attrs:{type:"success",size:"medium",icon:"el-icon-plus"},on:{click:e.sendApplay}},[e._v("\n                    变更申请\n                ")])],1)],1),e._v(" "),n("BaseTable",{attrs:{showSelection:!0,selectionWidth:"55",showIndex:!0,tableData:e.smryList,columns:e.tableColumns,handleColumn:e.handleColumn,handleColumnProp:e.handleColumnProp,showPagination:!0,currentPage:e.page1,pageSize:e.pageSize1,totalCount:e.total1},on:{operateBtn:e.operateBtn,selectBtn:e.selectBtn,handleCurrentChange:e.handleCurrentChange,handleSizeChange:e.handleSizeChange}})],1)])},staticRenderFns:[]};var v=n("VU/8")(g,y,!1,function(e){n("oH+x")},"data-v-09645ee9",null);t.default=v.exports},"oH+x":function(e,t){}});
//# sourceMappingURL=273.c20c71863d538a4c9742.js.map