<template>
  <div style="height: 100%;">
    <hsoft_top_title>
      <template #left>流程配置</template>
    </hsoft_top_title>
    <!---->
    <div style="text-align: right;">
      <el-form :inline="true" :model="formInline" class="demo-form-inline" style="float:left">
        <el-form-item label="服务名称">
          <el-input v-model="formInline.fwmc" placeholder="服务名称"></el-input>
        </el-form-item>
        <el-form-item label="服务类型">
          <el-select v-model="formInline.fwlx" placeholder="服务类型">
            <el-option v-for="item in fwlx" :label="item.fwlx" :value="item.fwid" :key="item.fwid"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="服务状态">
          <el-select v-model="formInline.fwzt" placeholder="服务状态">
            <el-option v-for="item in fwzt" :label="item.zt" :value="item.id" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
        <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
      </el-form>
      <el-button type="success" @click="showAddDialog">添加</el-button>
      <!-- <el-button type="primary" @click="getSettingList()">查询</el-button> -->
    </div>
    <el-table class="table" :data="settingList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
      style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 32px - 60px - 32px - 10px - 10px)" stripe>
      <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
      <el-table-column prop="fwmc" label="服务名称" width=""></el-table-column>
      <el-table-column prop="fwlx" label="服务类型" :formatter="forfwlx"></el-table-column>

      <el-table-column prop="fwzt" label="服务状态" width="" align="center" :formatter="forfwzt"></el-table-column>
      <el-table-column prop="pxh" label="排序号" width=""></el-table-column>
      <el-table-column prop="cjr" label="创建人" width=""></el-table-column>
      <el-table-column prop="cjsj" label="创建时间" width="" :formatter="forcjsj"></el-table-column>
      <el-table-column prop="" label="操作" width="">
        <template slot-scope="scoped">
          <el-button size="small" type="text" @click="modifySetting(scoped.row)">修改</el-button>
          <el-button size="small" type="text" @click="lcpzSetting(scoped.row)">流程配置</el-button>
          <el-button size="small" type="text" @click="deleteSetting(scoped.row)" style="color:#F56C6C;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5"
      :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize"
      layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total" style="    padding-top: 10px;">
    </el-pagination>
    <!---->
    <!-- 添加流程 -->
    <el-dialog title="新建流程" :visible.sync="dialogVisibleSetting" width="35%">
      <div>
        <el-form :model="settingForm" :label-position="'right'" label-width="120px" size="mini">
          <div style="display:flex">
            <el-form-item label="服务名称" class="one-line">
              <el-input v-model="settingForm.fwmc"></el-input>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="服务类型" class="one-line">
              <el-select v-model="settingForm.fwlx" placeholder="服务类型">
                <el-option v-for="item in fwlx" :label="item.fwlx" :value="item.fwid" :key="item.fwid"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="服务说明" class="one-line">
              <el-input v-model="settingForm.fwsm"></el-input>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="状态" class="one-line">
              <el-select v-model="settingForm.fwzt" placeholder="状态">
                <el-option v-for="item in fwzt" :label="item.zt" :value="item.id" :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </div>

          <el-form-item label="排序号" class="one-line-textarea">
            <el-input v-model="settingForm.pxh" oninput="value=value.replace(/[^\d.]/g,'')"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSetting = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 修改系统参数 -->
    <el-dialog title="修改用户密码" :visible.sync="dialogVisibleSettingModify" width="35%">

      <el-form :label-position="'right'" label-width="120px" size="mini">
        <div style="display:flex">
          <el-form-item label="服务名称" class="one-line">
            <el-input v-model="settingForm.fwmc"></el-input>
          </el-form-item>
        </div>
        <div style="display:flex">
          <el-form-item label="服务类型" class="one-line">
            <el-select v-model="settingForm.fwlx" placeholder="服务类型">
              <el-option v-for="item in fwlx" :label="item.fwlx" :value="item.fwid" :key="item.fwid"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div style="display:flex">
          <el-form-item label="服务说明" class="one-line">
            <el-input v-model="settingForm.fwsm"></el-input>
          </el-form-item>
        </div>
        <div style="display:flex">
          <el-form-item label="状态" class="one-line">
            <el-select v-model="settingForm.fwzt" placeholder="状态">
              <el-option v-for="item in fwzt" :label="item.zt" :value="item.id" :key="item.id"></el-option>
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="排序号" class="one-line-textarea">
          <el-input v-model="settingForm.pxh" oninput="value=value.replace(/[^\d.]/g,'')"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="modifySettingDialog()">确 定</el-button>
        <el-button type="warning" @click="dialogVisibleSettingModify = false">取 消</el-button>
      </span>
    </el-dialog>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
  import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

  import {
    getWindowLocation
  } from '../../../utils/windowLocation'

  import {
    dateFormatChinese,
    dateFormatNYRChinese
  } from '../../../utils/moment'
  import {
    getAllYhxx, //获取全部sm人员
    getZzjgList, //获取全部机构
  } from '../../../api/index'
  import {
    getFwlxList, //服务类型查询
    gzlJbxxFindPageList, //流程管理查询
    gzlJbxxAdd, //流程管理添加
    gzlJbxxUpdate, //流程管理修改
    gzlJbxxDelete, //流程管理删除
  } from '../../../api/lhgl'
  // import { writeSystemOptionsLog } from '../../../utils/logUtils'

  // import { checkArr, decideChange } from '../../../utils/utils'

  // // 系统参数设置表
  // import {
  //   // 插入系统参数表
  //   insertSettingList,
  //   // 查询系统参数表
  //   selectSettingList,
  //   // 删除系统参数设置
  //   deleteSettingList,
  //   // 修改系统参数设置
  //   updateSettingList
  // } from '../../../db/zczpSystem/zczpSysyemDb'

  import {
    addXtcs,
    deleteXtcs,
    updateXtcs,
    getXtcsPage,
    getcszjldw
  } from '../../../api/cssz'

  export default {
    data() {
      return {
        //查询
        formInline: {
          fwmc: '',
          fwlx: '',
          fwzt: '',
        },
        // 分页信息
        pageInfo: {
          page: 1,
          pageSize: 10,
          total: 0
        },
        regionOption: [], //部门数据
        //部门tree设置
        regionParams: {
          label: 'label', //这里可以配置你们后端返回的属性
          value: 'label',
          children: 'childrenRegionVo',
          expandTrigger: 'click',
          checkStrictly: true,
        }, //地域信息配置参数
        // 更新系统参数dialog
        dialogVisibleSettingModify: false,
        // 添加系统参数dialog
        dialogVisibleSetting: false,
        settingForm: {},
        settingFormOld: {},
        cszlx: 1,
        // 系统参数设置表格数据
        settingList: [],
        pickerOptions: {
          disabledDate: time => {
            if (this.selectDate == null) {
              return false
            } else {
              return (this.selectDate.getFullYear() != time.getFullYear())
            }
          },
          onPick: date => {
            // 如果只选择一个则保存至selectDate 否则selectDate 为空
            if (date.minDate && !date.maxDate) {
              this.selectDate = date.minDate
            } else {
              this.selectDate = null
            }
          }
        },
        jldwList: [],
        //服务类型
        fwlx: [],
        //服务状态
        fwzt: [{
            zt: '不可用',
            id: 0
          },
          {
            zt: '草稿',
            id: 1,
          },
          {
            zt: '发布',
            id: 2,
          },
        ]
      }
    },
    components: {
      hsoft_top_title
    },
    methods: {
      //服务类型查询
      async fwlxList() {
        let data = await getFwlxList()
        this.fwlx = data.data
      },
      //全部组织机构List
      async zzjg() {
        let zzjgList = await getZzjgList()
        console.log(zzjgList);
        this.zzjgmc = zzjgList
        let shu = []
        console.log(this.zzjgmc);
        this.zzjgmc.forEach(item => {
          let childrenRegionVo = []
          this.zzjgmc.forEach(item1 => {
            if (item.bmm == item1.fbmm) {
              // console.log(item, item1);
              childrenRegionVo.push(item1)
              // console.log(childrenRegionVo);
              item.childrenRegionVo = childrenRegionVo
            }
          });
          // console.log(item);
          shu.push(item)
        })

        console.log(shu);
        console.log(shu[0].childrenRegionVo);
        let shuList = []
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
        console.log(shuList);
        shuList[0].childrenRegionVo.forEach(item => {
          this.regionOption.push(item)
        })
      },
      //获取计量单位
      async getjldw() {
        this.jldwList = await getcszjldw()
      },
      showAddDialog() {
        this.settingForm = {}
        this.dialogVisibleSetting = true
      },
      // 格式化时间
      formatTime(time) {
        return dateFormatChinese(new Date(time))
      },
      //查询按钮
      onSubmit() {
        this.getSettingList()
      },
      //重置按钮
      cz() {
        this.formInline = {}
      },
      handleCurrentChange(val) {
        this.pageInfo.page = val
        this.getSettingList()
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.getSettingList()
      },
      // 修改(表格)
      modifySetting(row) {
        // let csz = row.csz
        // console.log(csz,checkArr(csz))
        // if (checkArr(csz)) {
        //   row.csz[0] = csz[0].month + '-' + csz[0].day
        //   row.csz[1] = csz[1].month + '-' + csz[1].day
        // }
        this.settingForm = JSON.parse(JSON.stringify(row))
        this.settingFormOld = JSON.parse(JSON.stringify(row))
        this.dialogVisibleSettingModify = true
      },
      //状态流程配置按钮
      lcpzSetting(row) {
        // this.$router.push('/lhpzSetting');
        this.$router.push({
          path: '/lhpzSetting',
          query: {
            fwdyid: row.fwdyid
          }
        })
      },
      // 修改（dialog）
      async modifySettingDialog() {

        let params = JSON.parse(JSON.stringify(this.settingForm))
        params.pxh = params.pxh * 1

        let data = await gzlJbxxUpdate(params)
        if (data.code == 10000) {
          this.getSettingList()
        } else if (data.code == 10002) {
          this.$message.error(data.message);
        }
        // // 写入日志
        // // 加入审计日志需要显示的内容
        // let paramsExtra = {
        //   bs: params.csbs,
        //   modifyArr: []
        // }
        // // 判定修改
        // paramsExtra.modifyArr = decideChange(this.settingFormOld, params, ['settingid', 'gxsj'])
        // Object.assign(params, paramsExtra)
        // let logParams = {
        //   xyybs: 'yybs_cssz',
        //   ymngnmc: '修改',
        //   extraParams: params
        // }
        // writeSystemOptionsLog(logParams)
        this.dialogVisibleSettingModify = false
      },
      // 删除参数设置
      async deleteSetting(row) {
        // console.log(row);
        let params = {
          fwdyid: row.fwdyid
        }
        let data = await gzlJbxxDelete(params)
        if (data.code == 10000) {
          this.getSettingList()

          this.$message({
            message: '删除成功',
            type: 'success'
          });

        }else if(data.code == 20002){
          this.$message.error(data.message);
        }
        // // 写入日志
        // // 加入审计日志需要显示的内容
        // let paramsExtra = {
        //   bs: row.csbs,
        //   modifyArr: []
        // }
        // // 判定修改
        // paramsExtra.modifyArr = decideChange(row, {}, ['settingid', 'gxsj'])
        // Object.assign(row, paramsExtra)
        // let logParams = {
        //   xyybs: 'yybs_cssz',
        //   ymngnmc: '删除',
        //   extraParams: row
        // }
        // writeSystemOptionsLog(logParams)
      },
      // 获取参数设置集合
      async getSettingList() {
        // this.settingForm = {}
        // let params = {}
        // Object.assign(params, this.pageInfo)
        // let settingPage = selectSettingList(params)
        // this.settingList = settingPage.list
        // this.pageInfo.total = settingPage.total
        let params = {
          page: this.pageInfo.page,
          pageSize: this.pageInfo.pageSize,
          fwmc: this.formInline.fwmc,
          fwlx: this.formInline.fwlx,
          fwzt: this.formInline.fwzt,
        }
        let settingPage = await gzlJbxxFindPageList(params)
        this.settingList = settingPage.data.content
        // this.settingList.forEach((item) => {
        //   if (item.cszlx != 1) {
        //     item.cszDate = item.cszDate.slice(5, 11)
        //     item.cszDate2 = item.cszDate2.slice(5, 11)
        //   }
        // })
        this.pageInfo.total = settingPage.data.pageInfo.total
      },
      // 添加参数设置
      async addSetting() {
        let params = JSON.parse(JSON.stringify(this.settingForm))
        params.pxh = params.pxh * 1
        let data = await gzlJbxxAdd(params)
        if (data.code == 10000) {
          this.getSettingList()
        } else if (data.code == 10002) {
          this.$message.error(data.message);
        }
        // 写入日志
        // 加入审计日志需要显示的内容
        // let paramsExtra = {
        //   bs: params.csbs,
        //   modifyArr: []
        // }
        // // 判定修改
        // paramsExtra.modifyArr = decideChange({}, params, ['settingid', 'gxsj'])
        // Object.assign(params, paramsExtra)
        // let logParams = {
        //   xyybs: 'yybs_cssz',
        //   ymngnmc: '添加',
        //   extraParams: params
        // }
        // writeSystemOptionsLog(logParams)
        this.dialogVisibleSetting = false
      },
      forfwlx(row) {
        console.log(row);
        let hxsj
        this.fwlx.forEach(item => {
          if (row.fwlx == item.fwid) {
            hxsj = item.fwlx
          }
        })
        return hxsj
      },
      forfwzt(row) {
        console.log(row);
        let hxsj
        this.fwzt.forEach(item => {
          if (row.fwzt == item.id) {
            hxsj = item.zt
          }
        })
        return hxsj
      },
      forcjsj(row) {
        return dateFormatNYRChinese(row)
      },
      //模糊匹配姓名
      querySearchxm(queryString, cb) {
        var restaurants = this.restaurantsxm;
        console.log("restaurants", restaurants);
        var results = queryString ? restaurants.filter(this.createFilterzw(queryString)) : restaurants;
        console.log("results", results);
        // 调用 callback 返回建议列表的数据
        // for (var i = 0; i < results.length; i++) {
        //   for (var j = i + 1; j < results.length; j++) {
        //     if (results[i].xm === results[j].xm) {
        //       results.splice(j, 1);
        //       j--;
        //     }
        //   }
        // }
        cb(results);
        console.log("cb(results.zw)", results);
      },
      createFilterxm(queryString) {
        return (restaurant) => {
          return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        };
      },
      async xmmh() {
        let resList = await getAllYhxx()
        // console.log(resList);
        this.restaurantsxm = resList;
        this.restaurantszj = resList;
        // console.log("this.restaurants", this.restaurantsbm);
        // console.log(resList)
      },
    },
    mounted() {
      this.fwlxList()
      this.zzjg()
      this.xmmh()
      this.getjldw()
      //
      this.getSettingList()
      //
      console.log(new Date(2022, 11, 1))
    }
  }

</script>

<style scoped>
  .out-card {
    /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
  }

  /**单位信息区域**/
  .out-card .out-card-div {
    font-size: 13px;
    padding: 5px 20px;
  }

  .out-card .out-card-div div {
    padding: 10px 5px;
    display: flex;
  }

  .out-card .dwxx div:hover {
    background: #f4f4f5;
    border-radius: 20px;
  }

  .out-card .dwxx div label {
    /* background-color: red; */
    width: 125px;
    display: inline-block;
    text-align: right;
    font-weight: 600;
    color: #909399;
  }

  .out-card .dwxx div span {
    /* background-color: rgb(33, 92, 79); */
    flex: 1;
    display: inline-block;
    padding-left: 20px;
  }

</style>
