<template>
    <div class="sec-container" v-loading="loading">
        <!-- 标题 -->
        <el-tabs v-model="activeName">
            <el-button class="fhry" v-show="deb" type="primary" size="small" @click="fhry">返回</el-button>
            <el-tab-pane label="审批指南" name="first">
                <div class="sec-form-six haveBorderTop sec-footer">
                    <el-button @click="ljbl" class="fr" type="success">立即办理</el-button>
                </div>
                <el-table border class="sec-el-table" :data="spznList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                    <el-table-column prop="hjmc" label="办理流程"></el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="审批信息" name="second">
                <p class="sec-title">基本信息</p>
                <div class="sec-form-container">
                    <el-form ref="formName" :model="tjlist" label-width="225px">
                        <div class="sec-header-section">
                            <div class="sec-form-left">
                                <el-form-item label="所在部门">
                                    <el-input v-model="tjlist.szbm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="申请人">
                                    <el-input v-model="tjlist.sqr" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="进门时间">
                                    <el-date-picker v-model="tjlist.jmsj" class="riq" type="datetime" disabled
                                        format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="选择日期时间">
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item label="出门时间">
                                    <el-date-picker v-model="tjlist.cmsj" class="riq" type="datetime" disabled
                                        format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="选择日期时间">
                                    </el-date-picker>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="陪同人部门">
                                    <el-input v-model="tjlist.ptrbm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="陪同人">
                                    <el-input v-model="tjlist.ptr" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left longLabel1 wd" style="height:212px;line-height: 212px;">
                                <el-form-item label="申请原因">
                                    <div style="display: flex; flex-wrap: wrap;">
                                        <div v-for="(item, index) in drsbList" :key="item.drid">
                                            <div style="display: flex; align-items: center;">
                                                <el-checkbox-group disabled v-model="checkList" @change="drsbbh">
                                                    <el-checkbox :label="item.drid" style="width: 450px;">{{ item.sblb
                                                    }}</el-checkbox></el-checkbox-group>
                                                <div v-show="item.qtxz" style="margin:-318px">
                                                    <el-input v-model="item.qtbz" style="width: 200px;" disabled></el-input>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-form-item>
                            </div>


                            <p class="sec-title">内部无授权人员列表</p>
                            <el-table border class="sec-el-table" :data="smcsSpsqList"
                                :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                                <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                                <el-table-column prop="szbm" label="部门"></el-table-column>
                                <el-table-column prop="sqr" label="姓名"></el-table-column>
                                <el-table-column prop="sqcs" label="涉密场所">
                                    <template slot-scope="scope">
                                        <el-select v-model="scope.row.sqcs" style="width: 100%;" disabled multiple
                                            placeholder="请选择">
                                            <el-option v-for="item in csList" :key="item.csid" :label="item.csmc"
                                                :value="item.csid">
                                            </el-option>
                                        </el-select>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="xdwp" label="携带设备"></el-table-column>
                            </el-table>

                            <p class="sec-title">外单位无授权人员列表</p>
                            <el-table border class="sec-el-table" :data="fmcsSpsqList"
                                :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                                <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                                <el-table-column prop="szbm" label="单位名称"></el-table-column>
                                <el-table-column prop="sqr" label="姓名"></el-table-column>
                                <el-table-column prop="sfzhm" label="身份证号码"></el-table-column>
                                <el-table-column prop="sqcs" label="涉密场所">
                                    <template slot-scope="scope">
                                        <el-select v-model="scope.row.sqcs" style="width: 100%;" disabled multiple
                                            placeholder="请选择">
                                            <el-option v-for="item in csList" :key="item.csid" :label="item.csmc"
                                                :value="item.csid">
                                            </el-option>
                                        </el-select>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="xdwp" label="携带设备"></el-table-column>
                            </el-table>
                        </div>
                        <!-- 载体详细信息end -->
                        <p class="sec-title">申请责任部门领导审批情况</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="sqbmsc">
                                <el-radio v-model="tjlist.sqbmsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="无授权人员进入涉密场所" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="申请责任部门领导审批人" prop="sqbmscxm">
                                <el-input placeholder="" disabled v-model="tjlist.sqbmscxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="sqbmscsj">
                                <el-date-picker disabled v-model="tjlist.sqbmscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">责任部门领导审批</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="zrbmsc">
                                <el-radio v-model="tjlist.zrbmsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="无授权人员进入涉密场所" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="责任部门领导审批人" prop="zrbmscxm">
                                <el-input placeholder="" disabled v-model="tjlist.zrbmscxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="zrbmscsj">
                                <el-date-picker disabled v-model="tjlist.zrbmscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">保密办意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmbsc">
                                <el-radio v-model="tjlist.bmbsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="无授权人员进入涉密场所" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="保密办领导审批人" prop="bmbscxm">
                                <el-input placeholder="" disabled v-model="tjlist.bmbscxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmbscsj">
                                <el-date-picker disabled v-model="tjlist.bmbscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">轨迹处理</p>
                        <el-table border class="sec-el-table" :data="gjclList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                            <el-table-column prop="clrid" label="办理人"></el-table-column>
                            <el-table-column prop="bllx" label="办理类型"></el-table-column>
                            <el-table-column prop="clyj" label="办理意见"></el-table-column>
                            <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                            <el-table-column prop="clsj" label="办理时间"></el-table-column>
                        </el-table>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="流程跟踪" name="third">
                <el-table border class="sec-el-table" :data="lcgzList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                    <el-table-column prop="clrid" label="办理人"></el-table-column>
                    <el-table-column prop="bllx" label="办理类型"></el-table-column>
                    <el-table-column prop="clyj" label="办理意见"></el-table-column>
                    <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                    <el-table-column prop="clsj" label="办理时间"></el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
        <!-- 发起申请弹框start -->
        <el-dialog title="人员选择" :close-on-click-modal="false" :visible.sync="dialogVisible" width="40%">
            <div class="dlFqsqContainer">
                <label for="">部门:</label>
                <el-input class="input1" v-model="formInline.bmmc" clearable placeholder="部门"></el-input>
                <label for="">姓名:</label>
                <el-input class="input2" v-model="formInline.xm" clearable placeholder="姓名"></el-input>
                <el-button class="searchButton" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                <el-table class="tb-container" ref="multipleTable" :data="smryList" border
                    :header-cell-style="headerCellStyle" stripe @selection-change="selectRow" @select="handleSelect"
                    @row-click="handleRowClick" height="300px">
                    <el-table-column type="selection" width="55" align="center"> </el-table-column>
                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                    <el-table-column prop="xm" label="姓名"></el-table-column>
                    <el-table-column prop="bmmc" label="部门"></el-table-column>
                    <el-table-column prop="gwmc" label="岗位"></el-table-column>
                </el-table>
                <el-pagination class="paginationContainer" background @current-change="handleCurrentChange"
                    @size-change="handleSizeChange" :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]"
                    :page-size="pageSize" layout="total, prev, pager, sizes,next, jumper" :total="total">
                </el-pagination>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" v-if="xsyc" @click="submit('formName')">确 定</el-button>
                <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
            </span>
        </el-dialog>
        <!-- 发起申请弹框end -->
    </div>
</template>
<script>
import {
    getSpUserList,
    getAllCsdjList
} from '../../../../api/index'

import {
    updateWsqjr,
    selectWsqjrJlidBySlid,
    selectWsqjrBySlid,
    selectWsqjrrynbqdBySqid,
    selectWsqjrrywbqdBySqid
} from '../../../../api/wsq'
import {
    //审批指南
    getBlzn,
    //审批信息
    //判断实例所处环节
    getSchj,
    //事项审核
    getSxsh,
    //非第一环节选择审批人
    tjclr,
    //流程跟踪
    getSpGjxx,
} from '../../../../api/wdgz'
import { getUserInfo } from '../../../../api/dwzc'
import AddLineTable from "../../../components/common/addLineTable.vue"; //人工纠错组件
export default {
    components: {
        AddLineTable,
    },
    props: {},
    data() {
        return {
            deb: true,
            typezt: '',
            activeName: 'second',
            // table 行样式
            headerCellStyle: {
                background: '#EEF7FF',
                color: '#4D91F8'
            },
            //审批指南
            spznList: [],
            formInline: {
                'bmmc': '',
                'xm': ''
            }, // 搜索条件
            loading: false,
            page: 1, // 审批人弹框当前页
            pageSize: 10, // 审批人弹框每页条数
            radioIdSelect: '', // 审批人弹框人员单选
            total: 0, // 弹框人员总数
            regionParams: {
                label: 'label', //这里可以配置你们后端返回的属性
                value: 'label',
                children: 'childrenRegionVo',
                expandTrigger: 'click',
                checkStrictly: true
            }, //地域信息配置参数
            selectlistRow: [], //列表的值
            mbhjid: '',

            // form表单提交数据
            tjlist: {
                xqr: '',
                szbm: '',
                jscdqx: [],
                smcsSpsqList: [],
                zxfw: '',
                yt: '',
                yjr: '',
                zfdw: '',
                yztbh: '',
                qsdd: '',
                mddd: '',
                fhcs: [],
                jtgj: [],
                jtxl: '',
                gdr: '',
                bgbm: '',
                bcwz: ''
            },
            smcsSpsqList: [],
            fmcsSpsqList: [],
            checkList: [],
            drsbList: [
                {
                    drid: 1,
                    sblb: '载体打印',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 2,
                    sblb: '载体刻录',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 3,
                    sblb: '载体复制',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 4,
                    sblb: '载体销毁',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 5,
                    sblb: '信息导入',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 6,
                    sblb: '系统维护',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 7,
                    sblb: '保密检查',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 8,
                    sblb: '会议培训',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 9,
                    sblb: '打扫卫生',
                    qtxz: false,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                },
                {
                    drid: 10,
                    sblb: '其他',
                    qtxz: true,
                    qtbz: '',
                    yxq: '',
                    checked: 0
                }
            ],
            scqk: [
                {
                    sfty: '同意',
                    id: 1
                },
                {
                    sfty: '不同意',
                    id: 0
                },
            ],
            csList: [],
            ztlxList: [
                {
                    lxid: '1',
                    lxmc: '纸介质'
                },
                {
                    lxid: '2',
                    lxmc: '光盘'
                },
                {
                    lxid: '3',
                    lxmc: '电磁介质'
                },
            ],
            smdjList: [
                {
                    smdjid: '1',
                    smdjmc: '绝密'
                },
                {
                    smdjid: '2',
                    smdjmc: '机密'
                },
                {
                    smdjid: '3',
                    smdjmc: '秘密'
                },
                {
                    smdjid: '4',
                    smdjmc: '内部'
                },
            ],
            xdfsList: [
                {
                    xdfsid: '1',
                    xdfsmc: '包装密封，封口处加盖密封章'
                },
                {
                    xdfsid: '2',
                    xdfsmc: '指派专人传递'
                },
                {
                    xdfsid: '3',
                    xdfsmc: '密码箱防护'
                },
            ],
            jtgjList: [
                {
                    jtgjid: '1',
                    jtgjmc: '飞机'
                },
                {
                    jtgjid: '2',
                    jtgjmc: '火车'
                },
                {
                    jtgjid: '3',
                    jtgjmc: '专车'
                },
            ],
            //轨迹处理
            gjclList: [],
            //人员任用
            smryList: [],
            disabled2: false,
            disabled3: false,
            disabled4: false,
            //通过
            tgdis: false,
            dialogVisible: false,
            fileRow: '',
            fwdyid: '',
            slid: '',
            jlid: '',
            xsyc: true,
            zhsp: true,
            jgyf: '',
            xm: '',
            //审批状态码 1 2 3 4
            zplcztm: null,
            //流程跟踪
            lcgzList: [],
        }
    },
    computed: {

    },
    mounted() {
        this.typezt = this.$route.query.typezt
        if (this.typezt != 'fhxq') {
            this.deb = false
        }
        this.getNowTime()
        console.log(this.$route.query.list);
        this.fwdyid = this.$route.query.fwdyid
        console.log("this.fwdyid", this.fwdyid);
        this.slid = this.$route.query.slid
        console.log("this.slid", this.slid);
        this.getjlid()
        this.getCsgl()
        this.dqlogin()
        //审批指南初始化列表
        this.spzn()
        // //审批信息初始化列表
        // this.spxxxgcc()
        setTimeout(() => {
            this.spxx()
        }, 500)
        // // //事项审核
        this.sxsh()
        // //初始化el-dialog列表数据
        this.splist()
        //流程跟踪初始化列表
        this.lcgz()
    },
    methods: {
        async getCsgl() {
            let csList = await getAllCsdjList()
            this.csList = csList
            console.log(this.csList);
        },
        drsbbh() {
            console.log(this.checkList);
            console.log(this.drsbList);
            this.drsbList.forEach(item => {
                item.checked = 0
            })
            this.checkList.forEach((item, index) => {
                this.drsbList.forEach((item1, index1) => {
                    if (item == item1.drid) {
                        item1.checked = 1
                    }
                })
            })
            this.drsbList.forEach(item => {
                if (item.checked == 0) {
                    item.qtbz = ''
                    item.yxq = ''
                }
            })
            console.log(this.drsbList);
        },
        async getjlid() {
            let params = {
                slid: this.slid
            }
            let data = await selectWsqjrJlidBySlid(params)
            console.log(data);
            this.jlid = data
        },
        getNowTime() {
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            console.log(defaultDate)
            return defaultDate;
            this.$set(this.info, "stockDate", defaultDate);
        },

        //当前登录用户
        async dqlogin() {
            let data = await getUserInfo()
            this.xm = data.xm
            console.log('this.dqlogin', this.xm);
        },
        //立即办理
        ljbl() {
            this.activeName = 'second'
        },
        //审批指南
        //审批指南初始化列表
        async spzn() {
            let params = {
                fwdyid: this.fwdyid,
            }
            let data = await getBlzn(params)
            if (data.code == 10000) {
                this.spznList = data.data.content
            }
        },
        //审批信息
        async spxx() {
            let params = {
                slid: this.slid
            }
            let data = await selectWsqjrBySlid(params)
            console.log(data);
            this.tjlist = data
            if (this.tjlist.ztdy == 1) {
                this.checkList.push(1)
                this.drsbList[0].checked = this.tjlist.ztdy
            }
            if (this.tjlist.ztkl == 1) {
                this.checkList.push(2)
                this.drsbList[1].checked = this.tjlist.ztkl
            }
            if (this.tjlist.ztfz == 1) {
                this.checkList.push(3)
                this.drsbList[2].checked = this.tjlist.ztfz
            }
            if (this.tjlist.ztxh == 1) {
                this.checkList.push(4)
                this.drsbList[3].checked = this.tjlist.ztxh
            }
            if (this.tjlist.xxdr == 1) {
                this.checkList.push(5)
                this.drsbList[4].checked = this.tjlist.xxdr
            }
            if (this.tjlist.xtwh == 1) {
                this.checkList.push(6)
                this.drsbList[5].checked = this.tjlist.xtwh
            }
            if (this.tjlist.bmjc == 1) {
                this.checkList.push(7)
                this.drsbList[6].checked = this.tjlist.bmjc
            }
            if (this.tjlist.hypx == 1) {
                this.checkList.push(8)
                this.drsbList[7].checked = this.tjlist.hypx
            }
            if (this.tjlist.dsws == 1) {
                this.checkList.push(9)
                this.drsbList[8].checked = this.tjlist.dsws
            }
            if (this.tjlist.qt != '' && this.tjlist.qt != undefined) {
                this.checkList.push(10)
                this.drsbList[9].qtbz = this.tjlist.qt
            }
            let zt = {
                sqid: this.jlid
            }
            console.log(zt);
            let ztqd = await selectWsqjrrynbqdBySqid(zt)
            this.smcsSpsqList = ztqd
            let wsq = await selectWsqjrrywbqdBySqid(zt)
            this.fmcsSpsqList = wsq
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            console.log('this.spxx', this.xm);
            if (this.zplcztm == 1) {
                this.tjlist.sqbmscxm = this.xm
                this.$set(this.tjlist, 'sqbmscsj', defaultDate)
                console.log(this.tjlist.sqbmscxm);

            } else if (this.zplcztm == 2) {
                this.tjlist.sqbmscxm = this.tjlist.sqbmscxm
                this.tjlist.zrbmscxm = this.xm
                console.log(this.tjlist.zrbmscxm);

                this.$set(this.tjlist, 'zrbmscsj', defaultDate)
            } else if (this.zplcztm == 3) {
                this.tjlist.sqbmscxm = this.tjlist.sqbmscxm
                this.tjlist.zrbmscxm = this.tjlist.zrbmscxm
                this.tjlist.bmbscxm = this.xm
                console.log(this.tjlist.bmbscxm);

                this.$set(this.tjlist, 'bmbscsj', defaultDate)
            }
        },

        chRadio() { },
        //初始化el-dialog列表数据
        async splist() {
            let params = {
                fwdyid: this.fwdyid,
                'xm': this.formInline.xm,
                'bmmc': this.formInline.bmmc,
                page: this.page,
                pageSize: this.pageSize,
                qshjid: this.mbhjid,
            }
            let data = await getSpUserList(params)
            this.smryList = data.records
            this.total = data.total


        },
        onSubmit() {
            this.splist()
        },
        async submit() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                shry: this.selectlistRow[0].yhid,
                mbhjid: this.mbhjid,
            }
            let data = await tjclr(params)
            if (data.code == 10000) {
                this.$message({
                    message: data.message,
                    type: 'success'
                });
                this.dialogVisible = false
                setTimeout(() => {
                    this.$router.push('/dbsx')
                }, 500)
            }
        },
        handleSelectionChange(index, row) {
            this.radioIdSelect = row
        },

        //事项审核
        async sxsh() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                jg: this.jgyf,
                smryid: ''
            }
            let data = await getSxsh(params)
            if (data.code == 10000) {
                this.tgdis = false
                if (data.data.zt == 0) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // this.smryList = data.data.blrarr
                    this.mbhjid = data.data.mbhjid
                    this.splist()
                    this.dialogVisible = true
                } else if (data.data.zt == 1) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 2) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 3) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
                else if (data.data.zt == 4) {
                    this.$message({
                        message: data.data.msg
                    });
                    console.log(1111111111111);
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
            }
        },
        //列表分页--跳转页数
        handleCurrentChange(val) {
            this.page = val
            this.splist()
        },
        //列表分页--更改每页显示个数
        handleSizeChange(val) {
            this.page = 1
            this.pageSize = val
            this.splist()
        },
        // 点击行触发，选中或不选中复选框
        handleRowClick(row, column, event) {
            this.$refs.multipleTable.toggleRowSelection(row)
            this.selectChange(this.selectlistRow)
        },
        handleSelect(selection, val) {
            // //只能选择一行，选择其他，清除上一行
            if (selection.length > 1) {
                let del_row = selection.shift()
                this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中
            }
        },
        selectRow(selection) {
            if (selection.length <= 1) {
                console.log('点击选中数据：', selection);
                this.selectlistRow = selection
                this.xsyc = true
            } else if (selection.length > 1) {
                this.$message.warning('只能选中一条数据')
                this.xsyc = false
            }
        },
        // 返回
        returnIndex() {
            this.$router.push('/gwbgscb')
        },
        //流程跟踪
        //流程跟踪初始化列表
        async lcgz() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSpGjxx(params)
            if (data.code == 10000) {
                this.lcgzList = data.data.content
                this.gjclList = data.data.content
                console.log(this.gjclList);
            }
        },
        fhry() {
            this.$router.push({
                path: '/csspxqy',
                query: {
                    row: this.$route.query.row
                }
            })
        },
    },
    watch: {

    }
}

</script>
  
<style scoped>
.sec-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: overlay;
}

.sec-title {
    border-left: 5px solid #1b72d8;
    color: #1b72d8;
    font-size: 20px;
    font-weight: 700;
    text-indent: 10px;
    margin-bottom: 20px;
    margin-top: 10px;
}

.sec-form-container {
    width: 100%;
    height: 100%;
}

.sec-form-left {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
}

.sec-form-left:not(:first-child) {
    border-top: 0;
}

.sec-form-left .el-form-item {
    float: left;
    width: 100%;
}

.sec-header-section {
    width: 100%;
    position: relative;
}

.sec-header-pic {
    width: 258px;
    position: absolute;
    right: 0px;
    top: 0;
    height: 163px;
    border: 1px solid #CDD2D9;
    border-left: 0;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sec-form-second {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
    border-top: 0;
}

.sec-form-third {
    border: 1px solid #CDD2D9;
    /* height: 40px;  */
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-four {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-five {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    overflow: hidden;
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.yulan {
    text-align: center;
    cursor: pointer;
    color: #3874D5;
    font-weight: 600;
    float: left;
    margin-left: 10px;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 2px solid #EBEBEB;
}

.sec-form-six {
    border: 1px solid #CDD2D9;
    overflow: hidden;
    background: #ffffff;
    padding: 10px;
}

.ml10 {
    margin-left: 10px;
}

.sec-footer {
    margin-top: 10px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
    border: none;
}

.sec-left-text {
    float: left;
    margin-right: 130px;
}

.haveBorderTop {
    border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__content {
    padding-left: 20px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

/* .sec-form-second:not(:first-child){
    border-top: 0;
  } */
.sec-form-second .el-form-item {
    float: left;
    width: 100%;
}

.sec-el-table {
    width: 100%;
    border: 1px solid #EBEEF5;
    height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
    padding-left: 15px;
    background-color: #F5F7FA;
    width: calc(100% - 16px);
    border-right: 1px solid #CDD2D9;
    color: #C0C4CC;
}

>>>.sec-el-table .el-input__inner {
    border: none !important;
    border-radius: 0;
}

.riq {
    width: 100% !important;
}

>>>.sec-form-container .el-form-item__label {
    width: 200px;
    text-align: center;
    font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
    border: none;
    border-right: 1px solid #CDD2D9;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item {
    margin-bottom: 0px;
}

/* >>>.el-form > div {
    border: 1px solid #CDD2D9;;
  } */
>>>.el-form-item__label {
    border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
    margin-top: 5px;
    margin-bottom: 5px;
  } */

.widthw {
    width: 6vw;
}

.dlFqsqContainer {
    width: 100%;
    height: 100%;
}

.dlFqsqContainer label {
    font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
    width: 150px;
    margin-left: 10px;
}

.dlFqsqContainer .searchButton {
    margin-left: 10px;
}

.gtzzsmgwgz>>>.el-form-item__label {
    text-align: left !important;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
    height: 40px;
}

.dlFqsqContainer .input1 {
    margin-right: 20px;
}

.dlFqsqContainer .tb-container {
    margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
    margin-top: 20px;
}

>>>.longLabel1 .el-form-item__label {
    width: 225px !important;
    line-height: 212px;
}

>>>.longLabel1 .el-form-item__content {
    margin-left: 225px !important;
    padding-left: 20px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
    line-height: 48px;
}

.fhry {
    float: right;
    z-index: 99;
    margin-top: 5px;
    position: relative;
}
</style>
  