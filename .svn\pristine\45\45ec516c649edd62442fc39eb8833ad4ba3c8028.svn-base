<template>
  <div style="height: 100%;">
    <hsoft_top_title>
      <template #left>用户管理</template>
    </hsoft_top_title>
    <!---->
    <div style="padding: 10px 0;text-align: right;">
      <el-button type="success" @click="showAddDialog">添加</el-button>
      <el-button type="danger" @click="showDeleDialog">删除</el-button>
      <!-- <el-button type="primary" @click="getSettingList()">查询</el-button> -->
    </div>
    <el-table class="table" :data="settingList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
      style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 32px - 60px - 32px - 10px - 10px)" stripe
      @select="selectDatelb">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
      <el-table-column prop="bmmc" label="所在部门"></el-table-column>
      <el-table-column prop="xm" label="姓名"></el-table-column>
      <el-table-column prop="yhm" label="用户名" align="center"></el-table-column>
      <el-table-column prop="gwmc" label="岗位" width=""></el-table-column>
      <el-table-column prop="yhlx" label="角色"></el-table-column>
      <el-table-column prop="" label="操作" width="150">
        <template slot-scope="scoped">
          <el-button size="small" type="text" @click="jsgl(scoped.row)">角色管理</el-button>
          <el-button size="small" type="text" @click="modifySetting(scoped.row)">密码重置</el-button>
          <!-- <el-button size="small" type="text" @click="deleteSetting(scoped.row)" style="color:#F56C6C;">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5"
      :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize"
      layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total" style="    padding-top: 10px;">
    </el-pagination>
    <!---->
    <!-- 添加系统参数 -->
    <el-dialog title="添加系统用户" :visible.sync="dialogVisibleSetting" width="35%">
      <div>
        <el-form :model="settingForm" :label-position="'right'" label-width="120px" size="mini">
          <div style="display:flex">
            <el-form-item label="所在部门" class="one-line">
              <el-cascader v-model="settingForm.bmmc" :options="regionOption" :props="regionParams" style="width: 100%;"
                filterable ref="cascaderArr" @visible-change="bmmcxz($event)"></el-cascader>
            </el-form-item>
          </div>
          <el-form-item label="姓名" class="one-line">
            <el-select v-model="settingForm.xm" placeholder="请选择" @change="xmxz($event)" :disabled="tjjy">
              <el-option v-for="item in xmlist" :key="item.smryid" :label="item.xm" :value="item.smryid">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="角色" class="one-line">
            <el-select v-model="settingForm.yhlx" placeholder="请选择" :disabled="tjjy">
              <el-option v-for="item in yhlxlist" :key="item.yhlxid" :label="item.yhlxmc" :value="item.yhlxid">
              </el-option>
            </el-select>
          </el-form-item>
          <div style="display:flex">
            <el-form-item label="岗位" class="one-line">
              <el-input v-model="settingForm.gwmc" disabled></el-input>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="用户名" class="one-line">
              <el-input v-model="settingForm.yhm" @blur="onInputBlur" @input="change($event)"
                oninput="value=value.replace(/[^a-z0-9]/g, '')" :disabled="tjjy"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="初始密码" class="one-line-textarea">
            <el-input v-model="settingForm.mm" disabled></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSetting = false">关 闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="修改系统用户" :visible.sync="dialogVisiblejs" width="35%">
      <div>
        <el-form :model="xglist" :label-position="'right'" label-width="120px" size="mini">
          <el-form-item label="角色" class="one-line">
            <el-select v-model="xglist.yhlx" placeholder="请选择" >
              <el-option v-for="item in yhlxlist" :key="item.yhlxid" :label="item.yhlxmc" :value="item.yhlxid">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updataSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogVisiblejs = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 修改系统参数 -->
    <el-dialog title="密码重置" :visible.sync="dialogVisibleSettingModify" width="35%">
      <el-form>
        <el-form-item label="密码" class="one-line-textarea">
          <el-input v-model="settingForm.mm" disabled></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer" style="margin-top:-40px;display: block;">
        <el-button type="primary" @click="xgmm()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSettingModify = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import {
  getWindowLocation
} from '../../../utils/windowLocation'
import {
  getYhxxListByPage, //获取人员管理信息
  saveYhxx, //添加人员管理信息
  deleteYhxxByIds, //删除人员管理信息
  resetPasswordById, //密码重置人员管理信息
  selectYhmXx, //用户名校验
  updateYhlxAndJs,
} from '../../../api/yhgl'
import {
  dateFormatChinese
} from '../../../utils/moment'
import {
  getAllYhxx, //获取全部sm人员
  getZzjgList, //获取全部机构
} from '../../../api/index'

import {
  addXtcs,
  deleteXtcs,
  updateXtcs,
  getXtcsPage,
  getcszjldw
} from '../../../api/cssz'
import vPinyin from '../../../utils/vue-py'
import {
  // 获取注册信息
  getDwxx,
} from '../../../api/dwzc'
export default {
  data() {
    return {
      tjjy: true,
      //用户数据
      xmlist: [],
      // 分页信息
      pageInfo: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      xglist: {},
      regionOption: [], //部门数据
      //部门tree设置
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      // 更新系统参数dialog
      dialogVisibleSettingModify: false,
      dialogVisiblejs: false,
      // 添加系统参数dialog
      dialogVisibleSetting: false,
      settingForm: {
        mm: '12345678',
        yhm: '',
      },
      yhlxlist: [
        {
          yhlxid: 1,
          yhlxmc: '管理员'
        }, {
          yhlxid: 2,
          yhlxmc: '普通用户'
        }
      ],
      // jslist: [
      //   {
      //     jsid: '1',
      //     jsmc: '单位保密管理员'
      //   }, {
      //     jsid: '2',
      //     jsmc: '部门保密管理员'
      //   },
      // ],
      settingFormOld: {},
      cszlx: 1,
      // 系统参数设置表格数据
      settingList: [],
      pickerOptions: {
        disabledDate: time => {
          if (this.selectDate == null) {
            return false
          } else {
            return (this.selectDate.getFullYear() != time.getFullYear())
          }
        },
        onPick: date => {
          // 如果只选择一个则保存至selectDate 否则selectDate 为空
          if (date.minDate && !date.maxDate) {
            this.selectDate = date.minDate
          } else {
            this.selectDate = null
          }
        }
      },
      jldwList: [],
    }
  },
  components: {
    hsoft_top_title
  },
  methods: {
    //修改密码
    xgmm() {
      let row = this.xgmmxx
      let passwordreg = /(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{10,16}/
      if (this.settingForm.mm != '12345678') {
        let isValid = passwordreg.test(this.settingForm.mm);
        console.log(isValid);
        if (isValid != true) {
          this.$message.error("密码必须是大写字母，小写字母，数字，特殊字符组成，且长度为10到16位！");
          return;
        } else {
          let params = {
            userId: row.yhid,
            password: this.settingForm.mm
          }
          if (row.yhm == 'root') {
            this.$message.warning('超级管理员账号不能修改密码')
            return
          }
          let updateBool = resetPasswordById(params)
          this.$message({
            message: row.yhm + '用户密码重置成功',
            type: 'success'
          });
          this.dialogVisibleSettingModify = false
        }
      } else {
        let params = {
          userId: row.yhid,
          password: this.settingForm.mm
        }
        if (row.yhm == 'root') {
          this.$message.warning('超级管理员账号不能修改密码')
          return
        }
        let updateBool = resetPasswordById(params)
        this.$message({
          message: row.yhm + '用户密码重置成功',
          type: 'success'
        });
        this.dialogVisibleSettingModify = false
      }

    },
    //全部组织机构List
    async zzjg() {
      let zzjgList = await getZzjgList()
      console.log(zzjgList);
      this.zzjgmc = zzjgList
      let shu = []
      console.log(this.zzjgmc);
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            // console.log(item, item1);
            childrenRegionVo.push(item1)
            // console.log(childrenRegionVo);
            item.childrenRegionVo = childrenRegionVo
          }
        });
        // console.log(item);
        shu.push(item)
      })
      let shuList = []
      shu.forEach(item => {
        if (item.fbmm == '') {
          shuList.push(item)
        }
      })
      console.log(shuList);
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    //部门获取用户
    bmmcxz(callback) {
      console.log(callback);
      // console.log(this.$refs['cascaderArr'].getCheckedNodes()[0].data);
      if (!callback) {

        let nodesObj = this.$refs['cascaderArr'].getCheckedNodes()[0].data
        console.log(nodesObj);
        this.settingForm.bmid = nodesObj.bmm
        this.xmmh().then(() => {
          let yhxx = this.restaurantsxm
          if (yhxx.length == 0) {
            this.$message.error('选择部门没有用户');
            this.tjjy = true
          } else {
            this.tjjy = false
          }
          this.xmlist = yhxx
        })
      }


    },

    //姓名获取岗位
    async xmxz(row) {
      console.log(row);
      let obj = {}
      obj = this.xmlist.find(function (i) {
        return i.smryid === row
      });
      console.log(obj);
      this.settingForm.gwmc = this.settingForm.bmmc.join('/') + '-' + obj.gwmc.join(',')
      this.smryid = obj.smryid
      this.settingForm.yhm = vPinyin.chineseToPinYin(obj.xm).toLocaleLowerCase()
      let params = new FormData()
      params.append('yhm', this.settingForm.yhm)
      let data = await selectYhmXx(params)
      if (data.data.content != 0) {
        this.$message.error('用户名已存在，请手动修改用户名')
      }
      this.settingForm.xm = obj.xm
    },
    change() {
      this.$forceUpdate()
    },
    async onInputBlur() {
      // console.log(this.settingForm.yhm);
      let params = new FormData()
      params.append('yhm', this.settingForm.yhm)
      let data = await selectYhmXx(params)
      if (data.data.content != 0) {
        this.$message.error('用户名已存在，请手动修改用户名')
      }
    },
    //获取登录信息
    async getLogin() {
      this.dwxxList = await getDwxx()
    },
    //获取计量单位
    async getjldw() {
      this.jldwList = await getcszjldw()
    },
    showAddDialog() {
      this.tjjy = true
      this.settingForm = {
        // yhlx: '',
        gwmc: '',
        dwid: this.dwxxList.dwid,
        bmid: '',
        mm: '12345678'
      }
      console.log(this.settingForm)
      this.dialogVisibleSetting = true
    },
    //删除按钮
    showDeleDialog() {
      this.$confirm('是否继续删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let ids = []
        this.coplb.forEach(item => {
          ids.push(item.yhid)
        })
        let data = await deleteYhxxByIds(ids)
        if (data.code == 10000) {
          this.getSettingList()
        }
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      }).catch(() => {
        this.$message('已取消删除')
      })

    },
    //列表选择
    selectDatelb(row) {
      console.log(row);
      this.coplb = row //选择的值赋给coplb
    },
    // 格式化时间
    formatTime(time) {
      return dateFormatChinese(new Date(time))
    },
    handleCurrentChange(val) {
      this.pageInfo.page = val
      this.getSettingList()
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.getSettingList()
    },
    jsgl(row) {
      console.log(row);
   
      this.xglist = JSON.parse(JSON.stringify(row))
      this.dialogVisiblejs = true
    },
    async updataSetting() {
      console.log(this.xglist);
        let params = {
          yhm: this.xglist.yhm,
          yhlx: this.xglist.yhlx,
        }
        let data = await updateYhlxAndJs(params)
        if (data.code == 10000) {
          this.$message.success('修改成功')
          this.dialogVisiblejs = false
          this.getSettingList()
        }
    },
    // 修改(表格)
    modifySetting(row) {
      this.xgmmxx = row
      this.dialogVisibleSettingModify = true
      this.settingForm = JSON.parse(JSON.stringify(row))
      this.settingForm.mm = '12345678'
      if (row.yhm == 'root') {
        this.$message.warning('超级管理员账号不能修改密码')
        return
      }
      // let params = {
      //   userId: row.yhid,
      //   password: 'csmm123456'
      // 2023.0203 1114修改重置后的密码为 qwer1234
      // password: 'qwer1234'
      // }
      // console.log('重置密码入参', params)
      // let updateBool = resetPasswordById(params)
      // if (updateBool) {
      //   //
      //   this.resultObj.resultIcon = 'success'
      //   this.resultObj.resultTitle = '重置密码成功'
      //   this.resultObj.showResultPassword = true
      //   // // 写入日志
      //   // let logParams = {
      //   //   xyybs: 'yybs_mmcz',
      //   //   ymngnmc: '确认重置'
      //   // }
      //   // writeSystemOptionsLog(logParams)
      //   return
      // }
      // this.resultObj.resultIcon = 'error'
      // this.resultObj.resultTitle = '重置密码失败'
      // this.resultObj.showResultPassword = false
      // this.$message({
      //   message: row.yhm + '用户密码重置成功',
      //   type: 'success'
      // });
    },
    // 修改（dialog）
    async modifySettingDialog() {

      let params = JSON.parse(JSON.stringify(this.settingForm))
      // let settingid = params.settingid
      // if (!settingid) {
      //   this.$message.warning('系统参数设置ID为空')
      //   return
      // }
      if (params.cszlx == 2) {
        params.cszDate2 = undefined
      }
      if (params.cszlx == 3) {
        // let csz = params.csz
        // // 放入月份和日期的对象
        // params.csz = []
        // // 开始月日
        // let time = csz[0].split('-')
        // params.csz.push({
        //   month: time[0],
        //   day: time[1]
        // })
        // // 结束月日
        // time = csz[1].split('-')
        // params.csz.push({
        //   month: time[0],
        //   day: time[1]
        // })
        params.cszDate = this.settingForm.cszDate[0]
        params.cszDate2 = this.settingForm.cszDate[1]
        console.log(params)
        console.log(' this.settingForm.cszDate', this.settingForm.cszDate);
      }
      let data = await updateXtcs(params)
      if (data.code = 10000) {
        this.getSettingList()
      }
      // // 写入日志
      // // 加入审计日志需要显示的内容
      // let paramsExtra = {
      //   bs: params.csbs,
      //   modifyArr: []
      // }
      // // 判定修改
      // paramsExtra.modifyArr = decideChange(this.settingFormOld, params, ['settingid', 'gxsj'])
      // Object.assign(params, paramsExtra)
      // let logParams = {
      //   xyybs: 'yybs_cssz',
      //   ymngnmc: '修改',
      //   extraParams: params
      // }
      // writeSystemOptionsLog(logParams)
      this.dialogVisibleSettingModify = false
    },
    // 删除参数设置
    async deleteSetting(row) {
      let params = {
        settingid: row.settingid
      }
      let data = await deleteXtcs(params)
      if (data.code = 10000) {
        this.getSettingList()
      }
      // // 写入日志
      // // 加入审计日志需要显示的内容
      // let paramsExtra = {
      //   bs: row.csbs,
      //   modifyArr: []
      // }
      // // 判定修改
      // paramsExtra.modifyArr = decideChange(row, {}, ['settingid', 'gxsj'])
      // Object.assign(row, paramsExtra)
      // let logParams = {
      //   xyybs: 'yybs_cssz',
      //   ymngnmc: '删除',
      //   extraParams: row
      // }
      // writeSystemOptionsLog(logParams)
    },
    // 获取参数设置集合
    async getSettingList() {
      // this.settingForm = {}
      // let params = {}
      // Object.assign(params, this.pageInfo)
      // let settingPage = selectSettingList(params)
      // this.settingList = settingPage.list
      // this.pageInfo.total = settingPage.total
      let params = {
        page: this.pageInfo.page,
        pageSize: this.pageInfo.pageSize
      }
      let settingPage = await getYhxxListByPage(params)
      console.log(settingPage);
      this.settingList = settingPage.records
      this.settingList.forEach((item) => {
        if (item.yhlx == 1) {
          item.yhlx = '管理员'
        } else {
          item.yhlx = '普通用户'
        }
        // item.cszDate = item.cszDate.slice(5, 11)
        // item.cszDate2 = item.cszDate2.slice(5, 11)
      })
      this.pageInfo.total = settingPage.total
    },
    // 添加参数设置
    async addSetting() {
      let params = new FormData();
      params.append('bmmc', this.settingForm.bmmc.join('/'));
      params.append('dwid', this.settingForm.dwid);
      params.append('yhm', this.settingForm.yhm);
      params.append('bmid', this.settingForm.bmid);
      params.append('yhlx', this.settingForm.yhlx);
      params.append('xm', this.settingForm.xm);
      params.append('mm', this.settingForm.mm);
      params.append('gwmc', this.settingForm.gwmc);
      params.append('smryid', this.smryid)
      let params1 = new FormData();
      params1.append('yhm', this.settingForm.yhm);
      let data1 = await selectYhmXx(params1)
      if (data1.data.content != 0) {
        this.$message.error('用户名已存在，请手动修改用户名')
      } else if (data1.code == 10013) {
        this.$message.error(this.settingForm.xm + '已存在')
      } else {
        console.log('没有人员');
        let data = await saveYhxx(params)
        if (data.code = 10000) {
          this.getSettingList()
        }
        this.dialogVisibleSetting = false
      }
    },
    fordw(row) {
      let hxsj
      this.jldwList.forEach(item => {
        if (row.cszdw == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    //模糊匹配姓名
    querySearchxm(queryString, cb) {
      var restaurants = this.restaurantsxm;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterxm(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      // for (var i = 0; i < results.length; i++) {
      //   for (var j = i + 1; j < results.length; j++) {
      //     if (results[i].xm === results[j].xm) {
      //       results.splice(j, 1);
      //       j--;
      //     }
      //   }
      // }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterxm(queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async xmmh() {
      let params = {
        bmid: this.settingForm.bmid
      }
      let resList = await getAllYhxx(params)
      console.log(resList);
      this.restaurantsxm = resList;
      this.restaurantszj = resList;
      this.xmlist = resList;
      // console.log("this.restaurants", this.restaurantsbm);
      // console.log(resList)
    },
  },
  mounted() {
    this.zzjg()
    // this.xmmh()
    this.getjldw()
    //
    this.getSettingList()
    this.getLogin()
    //
    console.log(new Date(2022, 11, 1))
  }
}

</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}

/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
}

.out-card .out-card-div div {
  padding: 10px 5px;
  display: flex;
}

.out-card .dwxx div:hover {
  background: #f4f4f5;
  border-radius: 20px;
}

.out-card .dwxx div label {
  /* background-color: red; */
  width: 125px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}

.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  flex: 1;
  display: inline-block;
  padding-left: 20px;
}
</style>
