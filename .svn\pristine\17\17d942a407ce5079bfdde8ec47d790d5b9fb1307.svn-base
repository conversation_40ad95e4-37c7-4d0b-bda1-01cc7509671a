webpackJsonp([188],{IQGS:function(t,e){},f2qL:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n("Xxa5"),a=n.n(i),r=n("mvHQ"),s=n.n(r),o=n("exGp"),c=n.n(o),u=n("l/JR"),d=function(t){return Object(u.b)("/bmai/quest_api","post",t)},f={components:{},props:{},data:function(){return{textarea:"",inputxx:""}},computed:{},watch:{},methods:{buttonClicked:function(){var t=this;return c()(a.a.mark(function e(){var n,i;return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(""!=t.textarea&&(t.inputxx=JSON.parse(s()(t.textarea))),setTimeout(function(){t.textarea=""},50),!t.textarea){e.next=12;break}return t.$refs.hgroups.innerHTML+='\n          <div class="bmaiWdtw" >\n            <div class="bmaiRwtb">\n              <div class="bmaiWdImg"></div>\n            </div>\n            <div class="bmaiWz">'+t.textarea+"</div>\n          </div>\n        ",n={question:t.inputxx},e.next=7,d(n);case 7:i=e.sent,t.$refs.hgroups.scrollTop=t.$refs.hgroups.scrollHeight,setTimeout(function(){t.$refs.hgroups.innerHTML+='\n          <div class="bmaiWdtw bmaiWdtw1">\n            <div class="bmaiRwtb bmaiRwtb1">\n              <div class="bmaiFyImg"></div>\n            </div>\n            <div class="bmaiWz">'+i.answer+"</div>\n          </div>",t.$refs.hgroups.scrollTop=t.$refs.hgroups.scrollHeight},1e3),e.next=13;break;case 12:t.$message.error("消息不可为空");case 13:case"end":return e.stop()}},e,t)}))()},handleEnter:function(t){console.log(t),"Enter"!=t.key||t.shiftKey?this.textarea+="\n":(t.preventDefault(),this.buttonClicked())}},mounted:function(){},created:function(){},beforeCreated:function(){},beforeMount:function(){},beforUpdate:function(){},updated:function(){},beforeDestory:function(){},destoryed:function(){},activated:function(){}},l={render:function(){this.$createElement;this._self._c;return this._m(0)},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"bmaiContainer"},[e("iframe",{staticClass:"bmaiAiIframe",staticStyle:{width:"100%"},attrs:{src:"http://221.212.111.71:28501/"}})])}]};var p=n("VU/8")(f,l,!1,function(t){n("IQGS")},"data-v-3e633fa9",null);e.default=p.exports}});
//# sourceMappingURL=188.c2748faefa106c1fb4bf.js.map