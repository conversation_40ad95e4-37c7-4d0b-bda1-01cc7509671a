<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">定密培训信息</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="台账时间" style="font-weight: 700;">
                  <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widthw">
                  </el-input> -->
                  <el-select v-model="formInline.tzsj" placeholder="台账时间">
                    <el-option v-for="item in yearSelect" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.sbnf" clearable placeholder="年度"
                    oninput="value=value.replace(/[^\d.]/g,'')" @blur="sbnf = $event.target.value" class="widths">
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <!-- <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item> -->
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" @click="fh()">返回
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <!-- <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="dialogVisible = true" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item> -->
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="dmpxList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 41px - 3px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="sbnf" label="年度"></el-table-column>
                  <el-table-column prop="pxsj" label="培训时间"></el-table-column>
                  <el-table-column prop="dmyj" label="学时（小时）"></el-table-column>
                  <el-table-column prop="pxrs" label="培训人数（人）"></el-table-column>
                  <el-table-column prop="pxdx" label="培训对象"></el-table-column>
                  <el-table-column prop="tznf" label="台账时间"></el-table-column>
                  <!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
                  <el-table-column label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button> -->
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入定密培训信息" class="scbg-dialog" :visible.sync="dialogVisible_dr"
          show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="学时（小时）" label="学时（小时）"></el-table-column>
              <el-table-column prop="培训人数" label="培训人数"></el-table-column>
              <el-table-column prop="培训对象" label="培训对象"></el-table-column>
              <el-table-column prop="备注" label="备注"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>
        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->

        <el-dialog title="新增定密培训信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="年度" prop="sbnf" class="one-line">
              <el-input placeholder="年度" disabled v-model="tjlist.sbnf" clearable></el-input>
            </el-form-item>
            <el-form-item label="培训时间" prop="pxsj" class="one-line">
              <el-date-picker v-model="tjlist.pxsj" style="width: 100%;" clearable type="date" placeholder="选择培训时间"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="学时（小时）" prop="dmyj" class="one-line">
              <el-input placeholder="学时（小时）" oninput="value=value.replace(/[^\d.]/g,'')"
                @blur="dmyj = $event.target.value" v-model="tjlist.dmyj" clearable></el-input>
            </el-form-item>
            <el-form-item label="培训人数（人）" prop="pxrs" class="one-line">
              <el-input placeholder="培训人数（人）" oninput="value=value.replace(/[^\d.]/g,'')"
                @blur="pxrs = $event.target.value" v-model="tjlist.pxrs" clearable></el-input>
            </el-form-item>
            <el-form-item label="培训对象" prop="pxdx" class="one-line">
              <el-input placeholder="培训对象" v-model="tjlist.pxdx" clearable></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改定密培训信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%" class="xg"
          @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="年度" prop="sbnf" class="one-line">
              <el-input placeholder="年度" disabled v-model="xglist.sbnf" clearable></el-input>
            </el-form-item>
            <el-form-item label="培训时间" prop="pxsj" class="one-line">
              <el-date-picker v-model="xglist.pxsj" style="width: 100%;" clearable type="date" placeholder="选择培训时间"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="学时（小时）" prop="dmyj" class="one-line">
              <el-input placeholder="学时（小时）" v-model="xglist.dmyj" oninput="value=value.replace(/[^\d.]/g,'')"
                @blur="dmyj = $event.target.value" clearable>
              </el-input>
            </el-form-item>
            <el-form-item label="培训人数（人）" prop="pxrs" class="one-line">
              <el-input placeholder="培训人数（人）" v-model="xglist.pxrs" @blur="pxrs = $event.target.value" clearable>
              </el-input>
            </el-form-item>
            <el-form-item label="培训对象" prop="pxdx" class="one-line">
              <el-input placeholder="培训对象" v-model="xglist.pxdx" clearable></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <!-- 详情 -->
        <el-dialog title="定密培训信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%" class="xg"
          @close="close">
          <el-form ref="form" :model="xglist" label-width="120px" size="mini" disabled>
            <el-form-item label="年度" prop="sbnf" class="one-line">
              <el-input placeholder="年度" disabled v-model="xglist.sbnf" clearable></el-input>
            </el-form-item>
            <el-form-item label="培训时间" prop="pxsj" class="one-line">
              <el-date-picker v-model="xglist.pxsj" style="width: 100%;" clearable type="date" placeholder="选择培训时间"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="学时（小时）" prop="dmyj" class="one-line">
              <el-input placeholder="学时（小时）" v-model="xglist.dmyj" clearable></el-input>
            </el-form-item>
            <el-form-item label="培训人数（人）" prop="pxrs" class="one-line">
              <el-input placeholder="培训人数（人）" v-model="xglist.pxrs" clearable></el-input>
            </el-form-item>
            <el-form-item label="培训对象" prop="pxdx" class="one-line">
              <el-input placeholder="培训对象" v-model="xglist.pxdx" clearable></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">

            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

      </div>
    </div>
  </div>
</template>
<script>
import {
  saveDmpx,
  removeDmpx,
  updateDmpx,
  getDmpxList
} from '../../../api/index'
import {
  getDmpxHistoryPage
} from '../../../api/lstz'
import {
  exportLsDmpxData
} from '../../../api/dcwj'
export default {
  components: {},
  props: {},
  data() {
    return {
      yearSelect: [],
      dmpxList: [],
      tableDataCopy: [],
      xglist: {},
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      formInline: {
        tzsj: new Date().getFullYear().toString()
      },
      tjlist: {
        sbnf: new Date().getFullYear().toString(),
        pxsj: '',
        sfzhm: '',
        dmyj: '',
        pxrs: '',
        pxdx: '',
        bz: '',
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        sbnf: [{
          required: true,
          message: '请输入年度',
          trigger: 'blur'
        },],
        pxsj: [{
          required: true,
          message: '请输入培训时间',
          trigger: ['blur', 'change'],
        },],
        dmyj: [{
          required: true,
          message: '请输入学时（小时）',
          trigger: ['blur', 'change'],
        },],
        pxrs: [{
          required: true,
          message: '请选择培训人数（人）',
          trigger: ['blur', 'change'],
        },],
        pxdx: [{
          required: true,
          message: '请输入培训对象',
          trigger: 'blur'
        },],
        // bz: [{
        // 	required: true,
        // 	message: '请输入备注',
        // 	trigger: 'blur'
        // },],
      },
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      pxrslxxz: [],
      dwmc: '',
      dwdm: '',
      dwlxr: '',
      dwlxdh: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    }
  },
  computed: {},
  mounted() {
    //获取最近十年的年份
    let yearArr = []
    for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
      yearArr.push(
        {
          label: i.toString(),
          value: i.toString()
        })
    }
    yearArr.unshift({
      label: "全部",
      value: ""
    })
    this.yearSelect = yearArr
    this.dmpx()
  },
  methods: {

    Radio(val) {

    },
    mbxzgb() {

    },
    mbdc() {

    },
    //导入
    chooseFile() {

    },
    //----成员组选择
    handleSelectionChange(val) {

    },
    //---确定导入成员组
    drcy() {

    },
    //----表格导入方法
    readExcel(e) {

    },
    //详情
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xqdialogVisible = true
    },

    updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xgdialogVisible = true
    },
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          //删除旧的
          // deletedmpx(this.updateItemOld)
          // 插入新的
          let that = this
          updateDmpx(this.xglist).then(() => {
            that.dmpx()
          })
          // 刷新页面表格数据

          // 关闭dialog
          this.$message.success('修改成功')
          this.xgdialogVisible = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

    //查询
    onSubmit() {
      this.page = 1
      this.dmpx()
      // //  form是查询条件
      // console.log(this.formInline);
      // // 备份了一下数据
      // let arr = this.tableDataCopy
      // // 通过遍历key值来循环处理
      // Object.keys(this.formInline).forEach(e => {
      // 	// 调用自己定义好的筛选方法
      // 	console.log(this.formInline[e]);
      // 	arr = this.filterFunc(this.formInline[e], e, arr)
      // })
      // // 为表格赋值
      // this.dmpxList = arr
      // // this.dmpx()
    },

    filterFunc(val, target, filterArr) {

    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    async dmpx() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        sbnf: this.formInline.sbnf,
        // tznf: this.formInline.tzsj
      }
      if(this.formInline.tzsj){
        params.tznf = this.formInline.tzsj
      }
      if (this.formInline.sbnf == '') {
        params.sbnf = undefined
      }
      let resList = await getDmpxHistoryPage(params)
      this.dmpxList = resList.records
      this.total = resList.total
    },
    //删除
    shanchu(id) {
      let that = this
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            let params = {
              sxid: item.sxid
            }
            removeDmpx(params).then(() => {
              that.dmpx()
            })
            console.log("删除：", item);
            console.log("删除：", item);
          })
          // let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });

        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {

    },

    //导出
    async exportList() {
      var param = {
        sbnf: this.formInline.sbnf,
        nf: this.formInline.tzsj
      }

      var returnData = await exportLsDmpxData(param);
      var date = new Date()
      var sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "定密培训信息表-" + sj + ".xls");
    },

    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            dwid: '111',
            dwmc: '111',
            sbnf: this.tjlist.sbnf,
            pxsj: this.tjlist.pxsj,
            sfzhm: this.tjlist.sfzhm,
            dmyj: this.tjlist.dmyj,
            pxrs: this.tjlist.pxrs,
            pxdx: this.tjlist.pxdx,
            bz: this.tjlist.bz,
            cjrid: "111"
            // dmpxid: getUuid()
          }
          let that = this
          saveDmpx(params).then(() => {
            that.resetForm()
            that.dmpx()
          })
          this.dialogVisible = false
          this.$message({
            message: '添加成功',
            type: 'success'
          });

        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },

    deleteTkglBtn() {

    },

    selectRow(val) {
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.dmpx()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.dmpx()
    },
    fh() {
      this.$router.go(-1)
    },
    //添加重置
    resetForm() {
      this.tjlist.pxsj = ''
      this.tjlist.dmyj = ''
      this.tjlist.pxrs = ''
      this.tjlist.pxdx = ''
      this.tjlist.qxnd = ''
      this.tjlist.sx = ''
      this.tjlist.bz = ''
      this.tjlist.sfzhm = ''
    },
    handleClose() {
      this.resetForm()
      this.dialogVisible = false
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
  },
  watch: {

  }
}

</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.widthx {
  width: 8vw;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}


.dialog-footer {
  display: block;
  margin-top: 10px;
}
</style>
