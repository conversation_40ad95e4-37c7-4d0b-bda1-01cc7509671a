import { createAPI, createFileAPI, createDown, createUploadAPI, BASE_URL } from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//查询人员信息
export const getAllYhxx = data => createAPI(BASE_URL + "/rygl/yhxx/getAllYhxx", 'get', data) // 涉密等级
    // 查询全部岗位信息
export const getAllGwxx = data => createAPI(BASE_URL + "/rygl/gwdj/getAllGwxx", 'get', data)
    //查询全部保密制度
export const getBmzdList = data => createAPI(BASE_URL + "/bmzt/djb/getBmzdList", 'get', data)
    //添加保密制度
export const saveBmzd = data => createAPI(BASE_URL + "/bmzt/djb/saveBmzd", 'post', data)
    //批量删除保密制度
export const removeBatch = data => createAPI(BASE_URL + "/bmzt/djb/removeBatch", 'post', data)
    //删除保密制度
export const removeBmzd = data => createAPI(BASE_URL + "/bmzt/djb/removeBmzd", 'post', data)
    //修改保密制度
export const updateBmzd = data => createAPI(BASE_URL + "/bmzt/djb/updateBmzd", 'post', data)
    //涉密岗位添加
export const saveSmgw = data => createAPI(BASE_URL + "/rygl/gwdj/saveSmgw", 'post', data)
    //涉密岗位删除
export const removeSmGw = data => createAPI(BASE_URL + "/rygl/gwdj/removeSmGw", 'post', data)
    //批量删除涉密岗位
export const removeBatchGw = data => createAPI(BASE_URL + "/rygl/gwdj/removeBatch", 'post', data)
    //修改涉密岗位
export const updateSmgw = data => createAPI(BASE_URL + "/rygl/gwdj/updateSmgw", 'post', data)
    //根据gwid和dwid查询涉密岗位信息
export const getGwxxById = data => createAPI(BASE_URL + "/rygl/gwdj/getGwxxById", 'get', data)
    //添加在岗涉密人员
export const saveSmry = data => createAPI(BASE_URL + "/rygl/yhxx/saveSmry", 'post', data)
    //获取人员照片
export const getZpBySmryid = data => createAPI(BASE_URL + "/rygl/yhxx/getZpBySmryid", 'get', data)
    //在岗涉密人员删除
export const removeSmry = data => createAPI(BASE_URL + "/rygl/yhxx/removeSmry", 'post', data)
    //批量删除在岗涉密人员
export const removeBatchRy = data => createAPI(BASE_URL + "/rygl/yhxx/removeBatch", 'post', data)
    //修改在岗涉密人员信息
export const updateYhxx = data => createAPI(BASE_URL + "/rygl/yhxx/updateYhxx", 'post', data)
    //根据smryid查询在岗涉密人员信息
export const getYhxxById = data => createAPI(BASE_URL + "/rygl/yhxx/getYhxxById", 'get', data)
    //根据smryid查询在岗涉密人员信息
export const getYhxxList = data => createAPI(BASE_URL + "/rygl/yhxx/getYhxxList", 'get', data)
    //根据smryid查询在岗涉密人员信息
export const getSpYhxxPage = data => createAPI(BASE_URL + "/rygl/yhxx/getSpYhxxPage", 'get', data)
    //查询全部在岗涉密人员带分页
export const getGwxxList = data => createAPI(BASE_URL + "/rygl/gwdj/getGwxxList", 'get', data)
    //查询全部涉密岗位变更记录带分页
export const getMjbgList = data => createAPI(BASE_URL + "/rygl/mjbg/getMjbgList", 'get', data)
    //查询全部涉密岗位变更记录不带分页
export const getAllMjbg = data => createAPI(BASE_URL + "/rygl/mjbg/getAllMjbg", 'get', data)
    //添加涉密岗位变更记录
export const saveMjbg = data => createAPI(BASE_URL + "/rygl/mjbg/saveMjbg", 'post', data)
    //添加涉密岗位变更记录
export const removeMjbg = data => createAPI(BASE_URL + "/rygl/mjbg/removeMjbg", 'post', data)
    //添加离职离岗记录
export const saveLzlg = data => createAPI(BASE_URL + "/rygl/lghz/saveLzlg", 'post', data)
    //修改离职离岗记录
export const updateLzlg = data => createAPI(BASE_URL + "/rygl/lghz/updateLzlg", 'post', data)
    //删除离职离岗记录
export const removeLzlg = data => createAPI(BASE_URL + "/rygl/lghz/removeLzlg", 'post', data)
    //查询全部离职离岗记录带分页
export const getLzlgList = data => createAPI(BASE_URL + "/rygl/lghz/getLzlgList", 'get', data)
    //查询全部离职离岗记录不带分页
export const getAllLzlg = data => createAPI(BASE_URL + "/rygl/lghz/getAllLzlg", 'get', data)

//添加涉密计算机
export const saveSmjsj = data => createAPI(BASE_URL + "/sbgl/smjsj/saveSmjsj", 'post', data)
    //删除涉密计算机
export const removeSmjsj = data => createAPI(BASE_URL + "/sbgl/smjsj/removeSmjsj", 'post', data)
    //批量删除涉密计算机
export const removeBatchSmjsj = data => createAPI(BASE_URL + "/sbgl/smjsj/removeBatch", 'post', data)
    //修改涉密计算机
export const updateSmjsj = data => createAPI(BASE_URL + "/sbgl/smjsj/updateSmjsj", 'post', data)
    //根据记录id和单位id查询涉密计算机
export const getSmjsjById = data => createAPI(BASE_URL + "/sbgl/smjsj/getSmjsjById", 'get', data)
    //查询全部涉密计算机带分页
export const getSmjsjList = data => createAPI(BASE_URL + "/sbgl/smjsj/getSmjsjList", 'get', data)

//添加非涉密计算机
export const saveFmjsj = data => createAPI(BASE_URL + "/sbgl/fmjsj/saveFmjsj", 'post', data)
    //删除非涉密计算机
export const removeFmjsj = data => createAPI(BASE_URL + "/sbgl/fmjsj/removeFmjsj", 'post', data)
    //批量删除非涉密计算机
export const removeBatchFsmjsj = data => createAPI(BASE_URL + "/sbgl/fmjsj/removeBatch", 'post', data)
    //修改非涉密计算机
export const updateFmjsj = data => createAPI(BASE_URL + "/sbgl/fmjsj/updateFmjsj", 'post', data)
    //根据记录id和单位id查询非涉密计算机
export const getFmjsjById = data => createAPI(BASE_URL + "/sbgl/fmjsj/getFmjsjById", 'get', data)
    //查询全部非涉密计算机带分页
export const getFmjsjList = data => createAPI(BASE_URL + "/sbgl/fmjsj/getFmjsjList", 'get', data)

//添加涉密移动存储介质
export const saveYdccjz = data => createAPI(BASE_URL + "/sbgl/ydccjz/saveYdccjz", 'post', data)
    //删除涉密移动存储介质
export const removeYdccjz = data => createAPI(BASE_URL + "/sbgl/ydccjz/removeYdccjz", 'post', data)
    //批量删除涉密移动存储介质
export const removeBatchSmydcczj = data => createAPI(BASE_URL + "/sbgl/ydccjz/removeBatch", 'post', data)
    //修改涉密移动存储介质
export const updateYdccjz = data => createAPI(BASE_URL + "/sbgl/ydccjz/updateYdccjz", 'post', data)
    //根据记录id和单位id查询涉密移动存储介质
export const getYdccjzById = data => createAPI(BASE_URL + "/sbgl/ydccjz/getYdccjzById", 'get', data)
    //查询全部涉密移动存储介质带分页
export const getYdccjzList = data => createAPI(BASE_URL + "/sbgl/ydccjz/getYdccjzList", 'get', data)

//添加涉密信息设备
export const saveSmxxsb = data => createAPI(BASE_URL + "/sbgl/smxxsb/saveSmxxsb", 'post', data)
    //删除涉密信息设备
export const removeSmxxsb = data => createAPI(BASE_URL + "/sbgl/smxxsb/removeSmxxsb", 'post', data)
    //批量删除涉密信息设备
export const removeBatchSmwlxs = data => createAPI(BASE_URL + "/sbgl/smxxsb/removeBatch", 'post', data)
    //修改涉密信息设备
export const updateSmxxsb = data => createAPI(BASE_URL + "/sbgl/smxxsb/updateSmxxsb", 'post', data)
    //根据记录id和单位id查询涉密信息设备
export const getSmxxsbById = data => createAPI(BASE_URL + "/sbgl/smxxsb/getSmxxsbById", 'get', data)
    //查询全部涉密信息设备带分页
export const getSmxxsbList = data => createAPI(BASE_URL + "/sbgl/smxxsb/getSmxxsbList", 'get', data)

//添加非密信息设备
export const saveFmxxsb = data => createAPI(BASE_URL + "/sbgl/fmxxsb/saveFmxxsb", 'post', data)
    //删除非密信息设备
export const removeFmxxsb = data => createAPI(BASE_URL + "/sbgl/fmxxsb/removeFmxxsb", 'post', data)
    //批量删除非密信息设备
export const removeBatchFmbgzdh = data => createAPI(BASE_URL + "/sbgl/fmxxsb/removeBatch", 'post', data)
    //修改非密信息设备
export const updateFmxxsb = data => createAPI(BASE_URL + "/sbgl/fmxxsb/updateFmxxsb", 'post', data)
    //根据记录id和单位id查询涉密信息设备
export const getFmxxsbById = data => createAPI(BASE_URL + "/sbgl/fmxxsb/getFmxxsbById", 'get', data)
    //查询全部非密信息设备带分页
export const getFmxxsbList = data => createAPI(BASE_URL + "/sbgl/fmxxsb/getFmxxsbList", 'get', data)

//添加涉密网络设备
export const saveSmwlsb = data => createAPI(BASE_URL + "/sbgl/smwlsb/saveSmwlsb", 'post', data)
    //删除涉密网络设备
export const removeSmwlsb = data => createAPI(BASE_URL + "/sbgl/smwlsb/removeSmwlsb", 'post', data)
    //批量删除涉密网络设备
export const removeBatchSmwlsb = data => createAPI(BASE_URL + "/sbgl/smwlsb/removeBatch", 'post', data)
    //修改涉密网络设备
export const updateSmwlsb = data => createAPI(BASE_URL + "/sbgl/smwlsb/updateSmwlsb", 'post', data)
    //根据记录id和单位id查询涉密网络设备信息
export const getSmwlsbById = data => createAPI(BASE_URL + "/sbgl/smwlsb/getSmwlsbById", 'get', data)
    //查询全部涉密网络设备带分页
export const getSmwlsbList = data => createAPI(BASE_URL + "/sbgl/smwlsb/getSmwlsbList", 'get', data)

//非密网络设备台账
export const saveFmwlsb = data => createAPI(BASE_URL + "/sbgl/fmwlsb/saveFmwlsb", 'post', data)
    //删除非密网络设备
export const removeFmwlsb = data => createAPI(BASE_URL + "/sbgl/fmwlsb/removeFmwlsb", 'post', data)
    //修改非密网络设备
export const updateFmwlsb = data => createAPI(BASE_URL + "/sbgl/fmwlsb/updateFmwlsb", 'post', data)
    //查询全部非密网络设备带分页
export const getFmwlsbList = data => createAPI(BASE_URL + "/sbgl/fmwlsb/getFmwlsbList", 'get', data)

//添加安全产品
export const saveAqfhcp = data => createAPI(BASE_URL + "/sbgl/xxsb/saveAqfhcp", 'post', data)
    //删除安全产品
export const removeAqfhcp = data => createAPI(BASE_URL + "/sbgl/xxsb/removeAqfhcp", 'post', data)
    //修改安全产品
export const updateAqfhcp = data => createAPI(BASE_URL + "/sbgl/xxsb/updateAqfhcp", 'post', data)
    //查询全部安全产品带分页
export const getAqfhcpList = data => createAPI(BASE_URL + "/sbgl/xxsb/getAqfhcpList", 'get', data)


//查询组织机构登录信息bmm
export const getLoginInfo = data => createAPI(BASE_URL + "/jggl/glxx/getLoginInfo", 'get', data)
    //查询组织机构下属组织机构信息
export const getZzjgList = data => createAPI(BASE_URL + "/jggl/glxx/getZzjgList", 'get', data)
    //查询组织机构下属组织机构信息
export const getZzjgPage = data => createAPI(BASE_URL + "/jggl/glxx/getZzjgPage", 'get', data)
    //添加组织机构下属组织机构信息
export const saveZzjg = data => createAPI(BASE_URL + "/jggl/glxx/saveZzjg", 'post', data)
    //根据部门码查询组织机构信息
export const getZzjgById = data => createAPI(BASE_URL + "/jggl/glxx/getZzjgById", 'get', data)
    //修改组织机构
export const updateZzjg = data => createAPI(BASE_URL + "/jggl/glxx/updateZzjg", 'post', data)
    //根据部门码查询部门下的子部门
export const getChildrenZzjgBybmm = data => createAPI(BASE_URL + "/jggl/glxx/getChildrenZzjgBybmm", 'get', data)
    //根据部门码查询部门下的组织机构用广
export const getYhxxPageByBmm = data => createAPI(BASE_URL + "/jggl/glxx/getYhxxPageByBmm", 'get', data)
    //删除组织机构
export const removeZzjg = data => createAPI(BASE_URL + "/jggl/glxx/removeZzjg", 'post', data)
    //删除组织机构
export const removeZzjgBatch = data => createAPI(BASE_URL + "/jggl/glxx/removeBatch", 'post', data)
    //组织机构拖拽
export const moveZzjg = data => createAPI(BASE_URL + "/jggl/glxx/moveZzjg", 'post', data)

//查询全部场所信息带分页
export const getCsdjxxList = data => createAPI(BASE_URL + "/csgl/csdj/getCsdjxxList", 'get', data)
    //添加场所
export const saveCsdj = data => createAPI(BASE_URL + "/csgl/csdj/saveCsdj", 'post', data)
    //修改场所信息
export const updateCsdj = data => createAPI(BASE_URL + "/csgl/csdj/updateCsdj", 'post', data)
    //删除场所
export const removeCsdj = data => createAPI(BASE_URL + "/csgl/csdj/removeCsdj", 'post', data)
    //获取全部场所信息list
export const getAllCsdjList = data => createAPI(BASE_URL + "/csgl/csdj/getAllCsdjList", 'get', data)
    //场所变更
    //查询全部场所变更信息带分页
export const getCsbgdjxxList = data => createAPI(BASE_URL + "/csgl/csbgdj/getCsbgdjxxList", 'get', data)
    //查询全部场所变更信息不带分页
export const getAllCsbgdj = data => createAPI(BASE_URL + "/csgl/csbgdj/getAllCsbgdj", 'get', data)
    //添加场所变更记录
export const saveCsbgdj = data => createAPI(BASE_URL + "/csgl/csbgdj/saveCsbgdj", 'post', data)
    //修改场所信息
export const updateCsbgdj = data => createAPI(BASE_URL + "/csgl/csbgdj/updateCsbgdj", 'post', data)
    //删除场所变更记录
export const removeCsbgdj = data => createAPI(BASE_URL + "/csgl/csbgdj/removeCsbgdj", 'post', data)

//定密事项
//定密培训
//添加
export const saveDmpx = data => createAPI(BASE_URL + "/dmgl/dmpx/saveDmpx", 'post', data)
    //删除
export const removeDmpx = data => createAPI(BASE_URL + "/dmgl/dmpx/removeDmpx", 'post', data)
    //修改
export const updateDmpx = data => createAPI(BASE_URL + "/dmgl/dmpx/updateDmpx", 'post', data)
    //查询
export const getDmpxList = data => createAPI(BASE_URL + "/dmgl/dmpx/getDmpxList", 'get', data)
    //不带分页
export const getAllDmpx = data => createAPI(BASE_URL + "/dmgl/dmpx/getAllDmpx", 'get', data)
    //定密情况年度统计
    //添加
export const saveNdtj = data => createAPI(BASE_URL + "/dmgl/ndtj/saveNdtj", 'post', data)
    //删除
export const removeNdtj = data => createAPI(BASE_URL + "/dmgl/ndtj/removeNdtj", 'post', data)
    //修改
export const updateNdtj = data => createAPI(BASE_URL + "/dmgl/ndtj/updateNdtj", 'post', data)
    //查询
export const getNdtjList = data => createAPI(BASE_URL + "/dmgl/ndtj/getNdtjList", 'get', data)
    //查询 不带分页
export const getAllNdtj = data => createAPI(BASE_URL + "/dmgl/ndtj/getAllNdtj", 'get', data)

//不明确事项确定情况
//添加
export const saveBmqsxqdqk = data => createAPI(BASE_URL + "/dmgl/bmqsxqdqk/saveBmqsxqdqk", 'post', data)
    //删除
export const removeBmqsxqdqk = data => createAPI(BASE_URL + "/dmgl/bmqsxqdqk/removeBmqsxqdqk", 'post', data)
    //修改
export const updateBmqsxqdqk = data => createAPI(BASE_URL + "/dmgl/bmqsxqdqk/updateBmqsxqdqk", 'post', data)
    //查询
export const getBmqsxqdqkList = data => createAPI(BASE_URL + "/dmgl/bmqsxqdqk/getBmqsxqdqkList", 'get', data)
    //查询 不带分页
export const getAllBmqsxqdqk = data => createAPI(BASE_URL + "/dmgl/bmqsxqdqk/getAllBmqsxqdqk", 'get', data)
    //政府采购项目
    //添加
export const saveSmzfcgxmqk = data => createAPI(BASE_URL + "/dmgl/smzfcgxmqk/saveSmzfcgxmqk", 'post', data)
    //删除
export const removeSmzfcgxmqk = data => createAPI(BASE_URL + "/dmgl/smzfcgxmqk/removeSmzfcgxmqk", 'post', data)
    //修改
export const updateSmzfcgxmqk = data => createAPI(BASE_URL + "/dmgl/smzfcgxmqk/updateSmzfcgxmqk", 'post', data)
    //查询
export const getSmzfcgxmqkList = data => createAPI(BASE_URL + "/dmgl/smzfcgxmqk/getSmzfcgxmqkList", 'get', data)
    //查询 不带分页
export const getAllSmzfcgxmqk = data => createAPI(BASE_URL + "/dmgl/smzfcgxmqk/getAllSmzfcgxmqk", 'get', data)

//涉密载体
//添加
export const saveZtgl = data => createAPI(BASE_URL + "/ztgl/zt/saveZtgl", 'post', data)
    //删除
export const removeZtgl = data => createAPI(BASE_URL + "/ztgl/zt/removeZtgl", 'post', data)
    //修改
export const updateZtgl = data => createAPI(BASE_URL + "/ztgl/zt/updateZtgl", 'post', data)
    //查询
export const getZtglList = data => createAPI(BASE_URL + "/ztgl/zt/getZtglList", 'get', data)
    //查询 不带分页
export const getAllZt = data => createAPI(BASE_URL + "/ztgl/zt/getAllZt", 'get', data)
    //查询 
export const selectZtglPage = data => createAPI(BASE_URL + "/ztgl/zt/selectZtglPage", 'get', data)

//培训清单
//添加
export const savePxdj = data => createAPI(BASE_URL + "/jypx/pxdj/savePxdj", 'post', data)
    //删除
export const removePxdj = data => createAPI(BASE_URL + "/jypx/pxdj/removePxdj", 'post', data)
    //修改
export const updatePxdj = data => createAPI(BASE_URL + "/jypx/pxdj/updatePxdj", 'post', data)
    //查询
export const getPxdjxxList = data => createAPI(BASE_URL + "/jypx/pxdj/getPxdjxxList", 'get', data)
    //人员添加
export const saveRypxdj = data => createAPI(BASE_URL + "/jypx/rypxdj/saveRypxdj", 'post', data)
    //根据id查询人员
export const getPxdjById = data => createAPI(BASE_URL + "/jypx/pxdj/getPxdjById", 'get', data)
    //根据id查询人员 
export const getAllPxdj = data => createAPI(BASE_URL + "/jypx/pxdj/getAllPxdj", 'get', data)
    //上移/下移-修改排序号接口
export const changePxh = data => createAPI(BASE_URL + "/jggl/glxx/changePxh", 'post', data)







//定密责任人
//根据上报年份和身份证号查询定密责任人
export const getDmzrrById = data => createAPI(BASE_URL + "/dmgl/dmzrr/getDmzrrById", 'get', data)
    //查询全部定密责任人带分页
export const getDmzrrList = data => createAPI(BASE_URL + "/dmgl/dmzrr/getDmzrrList", 'get', data)
    //查询全部定密责任人不带分页
export const getAllDmzrr = data => createAPI(BASE_URL + "/dmgl/dmzrr/getAllDmzrr", 'get', data)
    //添加定密责任人
export const saveDmzrr = data => createAPI(BASE_URL + "/dmgl/dmzrr/saveDmzrr", 'post', data)
    //修改定密责任人
export const updateDmzrr = data => createAPI(BASE_URL + "/dmgl/dmzrr/updateDmzrr", 'post', data)
    //删除定密责任人
export const removeDmzrr = data => createAPI(BASE_URL + "/dmgl/dmzrr/removeDmzrr", 'post', data)
    //获取定密事项定密权限
export const getDmqx = data => createAPI(BASE_URL + "/dmb/dmsx/getDmqx", 'get', data)
    //17.获取定密责任人类别
export const getzrrlb = data => createAPI(BASE_URL + "/dmb/dmsx/getzrrlb", 'get', data)

//定密授权
//查询全部定密授权带分页
export const getDmsqList = data => createAPI(BASE_URL + "/dmgl/dmsq/getDmsqList", 'get', data)
    //查询全部定密授权不带分页
export const getAllDmsq = data => createAPI(BASE_URL + "/dmgl/dmsq/getAllDmsq", 'get', data)
    //根据名录id和单位id查询定密授权
export const getDmsqById = data => createAPI(BASE_URL + "/dmgl/dmsq/getDmsqById", 'get', data)
    //添加定密授权
export const saveDmsq = data => createAPI(BASE_URL + "/dmgl/dmsq/saveDmsq", 'post', data)
    //删除定密授权
export const removeDmsq = data => createAPI(BASE_URL + "/dmgl/dmsq/removeDmsq", 'post', data)
    //修改定密授权
export const updateDmsq = data => createAPI(BASE_URL + "/dmgl/dmsq/updateDmsq", 'post', data)

//获取涉密等级
export const getAllSmsbmj = data => createAPI(BASE_URL + "/dmb/smsbmj/getAllSmsbmj", 'get', data)
    //获取场所变更撤销原因
export const getCxyy = data => createAPI(BASE_URL + "/dmb/csbg/getCxyy", 'get', data)

//国家秘密事项
//查询全部国家秘密事项带分页
export const getGjmmsxList = data => createAPI(BASE_URL + "/dmgl/gjmmsx/getGjmmsxList", 'get', data)
    //查询全部国家秘密事项不带分页
export const getAllGjmmsx = data => createAPI(BASE_URL + "/dmgl/gjmmsx/getAllGjmmsx", 'get', data)
    //查询全部定密事项一览表附件 不带分页
export const getDmsxfjList = data => createAPI(BASE_URL + "/dmgl/dmsxylb/fj/getDmsxfjList", 'get', data)
    //根据事项id和单位id查询国家秘密事项
export const getGjmmsxById = data => createAPI(BASE_URL + "/dmgl/gjmmsx/getGjmmsxById", 'get', data)
    //添加国家秘密事项
export const saveGjmmsx = data => createAPI(BASE_URL + "/dmgl/gjmmsx/saveGjmmsx", 'post', data)
    //修改国家秘密事项
export const updateGjmmsx = data => createAPI(BASE_URL + "/dmgl/gjmmsx/updateGjmmsx", 'post', data)
    //删除国家秘密事项
export const removeGjmmsx = data => createAPI(BASE_URL + "/dmgl/gjmmsx/removeGjmmsx", 'post', data)
    //定密事项密级
export const getmj = data => createAPI(BASE_URL + "/dmb/smsx/getmj", 'get', data)
    //获取是否涉密部门
export const getSfsmbm = data => createAPI(BASE_URL + "/dmb/zzjg/getSfsmbm", 'get', data)

// 总体情况
export const getAllCount = data => createAPI(BASE_URL + "/dmb/ztqk/getAllCount", 'get', data)
export const getAllCountBybmid = data => createAPI(BASE_URL + "/dmb/ztqk/getAllCountBybmid", 'get', data)


//获取设备轨迹
export const getGjxx = data => createAPI(BASE_URL + "/sbgl/gjxx/getGjxx", 'get', data)

// 任用审查获取用户列表
export const getSpUserList = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/getSpUserList", 'post', data)
export const submitRyrysc = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/submitRyrysc", 'post', data)
export const getLcSLid = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/createSlxx", 'post', data)
export const selectRyscPage = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/selectRyscPage", 'get', data)
export const removeRysc = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/removeRysc", 'post', data)
    // 根据rwid获取任用审查详情记录
export const getRyscInfo = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/getRyscInfo", 'get', data)
    // 更新任用审查列表
export const updateRysc = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/updateRysc", 'post', data)
    // 修改实例状态
export const updateSlzt = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/updateSlzt", 'post', data)
    //在岗复审
export const submitZgfs = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlZgfs/submitZgfs", 'post', data)

export const selectZgfsPage = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlZgfs/selectZgfsPage", 'get', data)

// 根据rwid获取任用审查详情记录
export const getZgfsInfo = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlZgfs/getZgfsInfo", 'get', data)

export const removeZgfs = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlZgfs/removeZgfs", 'post', data)
export const updateZgfs = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlZgfs/updateZgfs", 'post', data)
export const getCurZgfsjl = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlZgfs/getCurZgfsjl", 'get', data)
    //获取服务类型id
export const getFwdyidByFwlx = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlJbxx/getFwdyidByFwlx", 'get', data)
    //离职离岗
    //离职离岗查询带分页
export const selectLzlgPage = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlLzlg/selectLzlgPage", 'get', data)
    //离职离岗记录删除
export const removeLzlgscb = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlLzlg/removeLzlg", 'post', data)
    //离职离岗记录删除
export const submitLzlg = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlLzlg/submitLzlg", 'post', data)
    //离职离岗记录删除
export const getLzlgInfo = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlLzlg/getLzlgInfo", 'get', data)
    //离职离岗记录修改
export const updateLzlgscb = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlLzlg/updateLzlg", 'post', data)
    //任用审查and在岗复审下载
export const downloadRyscwdZip = data => createDown(BASE_URL + "/api/gzl_01_01/gzlSxbl/downloadRyscwdZip", 'get', data)
    //离职离岗下载
export const downloadLzlgwdZip = data => createDown(BASE_URL + "/api/gzl_01_01/gzlSxbl/downloadLzlgwdZip", 'get', data)
    //出国出境下载
export const downloadCgspwdZip = data => createDown(BASE_URL + "/api/gzl_01_01/gzlSxbl/downloadCgspwdZip", 'get', data)
    //非密重点人员下载
export const downloadFmzdryCns = data => createDown(BASE_URL + "/rygl-fmry/downloadFmzdryCns", 'get', data)
    //判断是否是涉密人员
export const judgeRylg = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlLzlg/judgeRylg", 'get', data)
    //判断是否在审查阶段
export const verifySfzzsp = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlSxbl/verifySfzzsp", 'get', data)
    //批量添加载体清单
export const savaZtqdBatch = data => createAPI(BASE_URL + "/ztgl/ztqd/savaZtqdBatch", 'post', data)
    //根据原jlid查询载体清单
export const getZtqdListByYjlid = data => createAPI(BASE_URL + "/ztgl/ztqd/getZtqdListByYjlid", 'get', data)
    //根据原jlid删除原记录id下的载体清单
export const deleteZtqdByYjlid = data => createAPI(BASE_URL + "/ztgl/ztqd/deleteZtqdByYjlid", 'post', data)
    //根据jlid修改载体清单
export const updateZtqdByJlid = data => createAPI(BASE_URL + "/ztgl/ztqd/updateZtqdByJlid", 'post', data)
    //通过slid获取jlid
export const selectjlidBySlid = data => createAPI(BASE_URL + "/api/gzl_01_01/ZtglZz/selectjlidBySlid", 'get', data)
    //删除slxx
export const deleteSlxxBySlid = data => createAPI(BASE_URL + "/api/gzl_01_01/gzlJbxx/deleteSlxxBySlid", 'post', data)
export const deleteZdry = data => createAPI(BASE_URL + "/rygl-zdry/deleteZdry", 'post', data)
export const deleteZdryglRysc = data => createAPI(BASE_URL + "/api/gzl_01_01/zdrygl_rysc/deleteZdryglRysc", 'post', data)

//数据统计
//机房巡检数据统计
//机房巡检分页查询
export const jfqueryPage = data => createAPI(BASE_URL + "/inspectionComputerRoom/queryPage", 'get', data)
    //机房巡检信息详情
    // export const queryInspectionPage = data => createAPI(BASE_URL + "/inspectionComputerRoom/queryInspectionPage", 'get', data)
export const getInspectionCabinetPage = data => createAPI(BASE_URL + "/shmaInspectionCabinet/getInspectionCabinetPage", 'get', data)
    //机房巡检数据统计导出所有数据
export const exportAll = data => createDown(BASE_URL + "/inspectionComputerRoom/exportAll", 'post', data)
    //机房巡检数据统计导出当前页数据
export const exportOnePage = data => createDown(BASE_URL + "/inspectionComputerRoom/exportOnePage", 'post', data)
    //机房巡检数据统计导出当前条数据
export const exportMessage = data => createDown(BASE_URL + "/inspectionComputerRoom/exportMessage", 'post', data)

//设备迁移数据统计
//设备迁移数据统计分页查询
export const queryPage = data => createAPI(BASE_URL + "/shmaRelocationEquipment/queryPage", 'get', data)
    //设备迁移信息详情
export const getInspectionEquipmentPage = data => createAPI(BASE_URL + "/shmaInspectionEquipment/getInspectionEquipmentPage", 'get', data)
    //设备迁移数据统计导出所有数据
export const sbqyexportAll = data => createDown(BASE_URL + "/shmaRelocationEquipment/exportAll", 'post', data)
    //设备迁移数据统计导出当前页数据
export const sbqyexportOnePage = data => createDown(BASE_URL + "/shmaRelocationEquipment/exportOnePage", 'post', data)

//设备销毁数据统计
//设备销毁分页
export const sbxhqueryPage = data => createAPI(BASE_URL + "/shmaDestroyEquipment/queryPage", 'get', data)
    //设备销毁数据统计导出所有数据
export const sbxhexportAll = data => createDown(BASE_URL + "/shmaDestroyEquipment/exportAll", 'post', data)
    //设备销毁数据统计导出当前页数据
export const sbxhexportOnePage = data => createDown(BASE_URL + "/shmaDestroyEquipment/exportOnePage", 'post', data)

//故障处理
//故障处理分页
export const gzclqueryPage = data => createAPI(BASE_URL + "/shmaMaintenanceEquipment/queryPage", 'get', data)
    //故障处理详情
export const gzclxqqueryPage = data => createAPI(BASE_URL + "/shmaMaintenanceEquipmentDetails/queryPage", 'get', data)
    //机房巡检数据统计导出所有数据
export const gzclexportAll = data => createDown(BASE_URL + "/shmaMaintenanceEquipment/exportAll", 'post', data)
    //机房巡检数据统计导出当前页数据
export const gzclexportOnePage = data => createDown(BASE_URL + "/shmaMaintenanceEquipment/exportOnePage", 'post', data)
    //机房巡检数据统计导出当前条数据
export const gzclexportMessage = data => createDown(BASE_URL + "/shmaMaintenanceEquipmentDetails/exportAll", 'post', data)

//总体情况首页
//获取统计设备类型数量
export const getEquipmentTypeCount = data => createAPI(BASE_URL + "/largeScreen/getEquipmentTypeCount", 'get', data)
    //获取评分
export const getScore = data => createAPI(BASE_URL + "/largeScreen/getScore", 'get', data)

//门磁
export const mcQueryPage = data => createAPI(BASE_URL + "/shma-unlock-cabinet-log/queryPage", 'get', data)
    //U位
export const uwQueryPage = data => createAPI(BASE_URL + "/shma-monitor-alert/queryPage", 'get', data)