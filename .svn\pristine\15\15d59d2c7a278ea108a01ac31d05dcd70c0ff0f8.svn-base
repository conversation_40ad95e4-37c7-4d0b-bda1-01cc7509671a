{"version": 3, "sources": ["webpack:///src/renderer/view/xtsz/signIn.vue", "webpack:///./src/renderer/view/xtsz/signIn.vue?d996", "webpack:///./src/renderer/view/xtsz/signIn.vue"], "names": ["signIn", "data", "dwxx", "sjdw", "dwzch", "dwmc", "yhm", "dlmm", "qrmm", "dwlx", "ssly", "sscc", "tydm", "province", "city", "district", "dwlxr", "dwlxdh", "dwyx", "sjdwList", "dwlxList", "sslyList", "ssccList", "provinceList", "provincecode", "cityList", "citycode", "districtList", "districtcode", "components", "hsoft_top_title", "mounted", "this", "getAllDwlx", "getAllSjdw", "getAllSsly", "getAllSscc", "initSsq", "watch", "methods", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "Object", "dwzc", "sent", "stop", "_this2", "_callee2", "_context2", "_this3", "_callee3", "_context3", "_this4", "_callee4", "_context4", "provinceChanged", "_this5", "_callee5", "_context5", "console", "log", "for<PERSON>ach", "item", "index", "name", "code", "regionalNumber", "cityChanged", "_this6", "_callee6", "cityparam", "_context6", "districtChanged", "_this7", "_callee7", "districtparam", "_context7", "_this8", "_callee8", "_context8", "szsid", "szsdid", "szqxid", "onSubmit", "formName", "_this9", "_callee9", "_params", "params", "_context9", "dwid", "mm", "defineProperty_default", "cz", "$message", "message", "type", "warning", "resetForm", "$refs", "resetFields", "xtsz_signIn", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "height", "scopedSlots", "_u", "key", "fn", "_v", "proxy", "staticClass", "ref", "attrs", "model", "label-width", "label", "width", "placeholder", "value", "callback", "$$v", "$set", "expression", "_l", "placement", "trigger", "display", "margin-bottom", "color", "position", "top", "right", "slot", "csm", "csz", "on", "change", "click", "$event", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "wNAoKAA,GACAC,KADA,WAEA,OAEAC,MACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,IAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,SAAA,GACAC,KAAA,GACAC,SAAA,GACAC,MAAA,GACAC,OAAA,GACAC,KAAA,IAEAC,YACAC,YACAC,YACAC,YAEAC,gBACAC,aAAA,GAEAC,YACAC,SAAA,GAEAC,gBACAC,aAAA,KAGAC,YACAC,kBAAA,GAEAC,QAxCA,WAyCAC,KAAAC,aACAD,KAAAE,aACAF,KAAAG,aACAH,KAAAI,aAEAJ,KAAAK,WAGAC,SAGAC,SAEAL,WAFA,WAEA,IAAAM,EAAAR,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,OAAAH,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAV,EAAArB,SADA4B,EAAAK,KAAA,wBAAAL,EAAAM,SAAAR,EAAAL,KAAAC,IAIAR,WANA,WAMA,IAAAqB,EAAAtB,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAW,IAAA,OAAAb,EAAAC,EAAAG,KAAA,SAAAU,GAAA,cAAAA,EAAAR,KAAAQ,EAAAP,MAAA,cAAAO,EAAAP,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAI,EAAAlC,SADAoC,EAAAJ,KAAA,wBAAAI,EAAAH,SAAAE,EAAAD,KAAAb,IAIAN,WAVA,WAUA,IAAAsB,EAAAzB,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAc,IAAA,OAAAhB,EAAAC,EAAAG,KAAA,SAAAa,GAAA,cAAAA,EAAAX,KAAAW,EAAAV,MAAA,cAAAU,EAAAV,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAO,EAAApC,SADAsC,EAAAP,KAAA,wBAAAO,EAAAN,SAAAK,EAAAD,KAAAhB,IAIAL,WAdA,WAcA,IAAAwB,EAAA5B,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAiB,IAAA,OAAAnB,EAAAC,EAAAG,KAAA,SAAAgB,GAAA,cAAAA,EAAAd,KAAAc,EAAAb,MAAA,cAAAa,EAAAb,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAU,EAAAtC,SADAwC,EAAAV,KAAA,wBAAAU,EAAAT,SAAAQ,EAAAD,KAAAnB,IAIAsB,gBAlBA,SAkBAlD,GAAA,IAAAmD,EAAAhC,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAqB,IAAA,OAAAvB,EAAAC,EAAAG,KAAA,SAAAoB,GAAA,cAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,cACAkB,QAAAC,IAAAvD,GADAqD,EAAAjB,KAAA,EAEAC,OAAAC,EAAA,EAAAD,GAFA,OAEAc,EAAAzC,aAFA2C,EAAAd,KAGAe,QAAAC,IAAA,UAAAJ,EAAAzC,cACAyC,EAAAzC,aAAA8C,QAAA,SAAAC,EAAAC,GACAD,EAAAE,MAAA3D,IACAsD,QAAAC,IAAA,SAAAE,GACAN,EAAA9D,KAAAW,SAAAyD,EAAAE,KACAR,EAAAxC,aAAA8C,EAAAG,KACAN,QAAAC,IAAAJ,EAAAxC,iBAIAwC,EAAArC,gBAEAqC,EAAA9D,KAAAwE,eAAA,GAEAV,EAAA9D,KAAAY,KAAA,GACAkD,EAAA9D,KAAAa,SAAA,GACAiD,EAAAW,cAnBA,yBAAAT,EAAAb,SAAAY,EAAAD,KAAAvB,IAsBAkC,YAxCA,SAwCA7D,GAAA,IAAA8D,EAAA5C,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAiC,IAAA,IAAAC,EAAA,OAAApC,EAAAC,EAAAG,KAAA,SAAAiC,GAAA,cAAAA,EAAA/B,KAAA+B,EAAA9B,MAAA,cAGA6B,GACAtD,aAAAoD,EAAApD,cAJAuD,EAAA9B,KAAA,EAMAC,OAAAC,EAAA,EAAAD,CAAA4B,GANA,OAMAF,EAAAnD,SANAsD,EAAA3B,KAOAe,QAAAC,IAAA,aAAAQ,EAAAnD,UACAmD,EAAAnD,SAAA4C,QAAA,SAAAC,EAAAC,GACAzD,GAAAwD,EAAAE,OACAL,QAAAC,IAAA,UAAAE,GACAM,EAAA1E,KAAAY,KAAAwD,EAAAE,KACAI,EAAAlD,SAAA4C,EAAAG,QAIAG,EAAA1E,KAAAwE,eAAA,GAEAE,EAAA1E,KAAAa,SAAA,GACA6D,EAAAI,kBAnBA,wBAAAD,EAAA1B,SAAAwB,EAAAD,KAAAnC,IAsBAuC,gBA9DA,SA8DAjE,GAAA,IAAAkE,EAAAjD,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAsC,IAAA,IAAAC,EAAA,OAAAzC,EAAAC,EAAAG,KAAA,SAAAsC,GAAA,cAAAA,EAAApC,KAAAoC,EAAAnC,MAAA,cAEAkC,GACAzD,SAAAuD,EAAAvD,UAHA0D,EAAAnC,KAAA,EAKAC,OAAAC,EAAA,EAAAD,CAAAiC,GALA,OAKAF,EAAAtD,aALAyD,EAAAhC,KAMAe,QAAAC,IAAA,WAAAa,EAAAtD,cACAsD,EAAAtD,aAAA0C,QAAA,SAAAC,EAAAC,GACAxD,GAAAuD,EAAAE,OACAL,QAAAC,IAAA,SAAAE,GACAW,EAAA/E,KAAAa,SAAAuD,EAAAE,KACAS,EAAArD,aAAA0C,EAAAG,QAXA,wBAAAW,EAAA/B,SAAA6B,EAAAD,KAAAxC,IAgBAJ,QA9EA,WA8EA,IAAAgD,EAAArD,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAA0C,IAAA,IAAAR,EAAAK,EAAA,OAAAzC,EAAAC,EAAAG,KAAA,SAAAyC,GAAA,cAAAA,EAAAvC,KAAAuC,EAAAtC,MAAA,cAAAsC,EAAAtC,KAAA,EAEAC,OAAAC,EAAA,EAAAD,GAFA,cAEAmC,EAAA9D,aAFAgE,EAAAnC,KAGAe,QAAAC,IAAA,UAAAiB,EAAA9D,cACA8D,EAAA9D,aAAA8C,QAAA,SAAAC,EAAAC,GACAc,EAAAnF,KAAAsF,OAAAlB,EAAAG,OACAN,QAAAC,IAAA,SAAAE,GACAe,EAAAnF,KAAAW,SAAAyD,EAAAE,KACAa,EAAA7D,aAAA8C,EAAAG,QAIAK,GACAtD,aAAA6D,EAAA7D,cAbA+D,EAAAtC,KAAA,EAeAC,OAAAC,EAAA,EAAAD,CAAA4B,GAfA,cAeAO,EAAA5D,SAfA8D,EAAAnC,KAgBAe,QAAAC,IAAA,aAAAiB,EAAA5D,UACA4D,EAAA5D,SAAA4C,QAAA,SAAAC,EAAAC,GACAc,EAAAnF,KAAAuF,QAAAnB,EAAAG,OACAN,QAAAC,IAAA,UAAAE,GACAe,EAAAnF,KAAAY,KAAAwD,EAAAE,KACAa,EAAA3D,SAAA4C,EAAAG,QAIAU,GACAzD,SAAA2D,EAAA3D,UA1BA6D,EAAAtC,KAAA,GA4BAC,OAAAC,EAAA,EAAAD,CAAAiC,GA5BA,QA4BAE,EAAA1D,aA5BA4D,EAAAnC,KA6BAe,QAAAC,IAAA,WAAAiB,EAAA1D,cACA0D,EAAA1D,aAAA0C,QAAA,SAAAC,EAAAC,GACAc,EAAAnF,KAAAwF,QAAApB,EAAAG,OACAN,QAAAC,IAAA,SAAAE,GACAe,EAAAnF,KAAAa,SAAAuD,EAAAE,KACAa,EAAAzD,aAAA0C,EAAAG,QAlCA,yBAAAc,EAAAlC,SAAAiC,EAAAD,KAAA5C,IAsCAkD,SApHA,SAoHAC,GAAA,IAAAC,EAAA7D,KAAA,OAAAS,IAAAC,EAAAC,EAAAC,KAAA,SAAAkD,IAAA,IAAAC,EAAAC,EAAA/F,EAAA,OAAAyC,EAAAC,EAAAG,KAAA,SAAAmD,GAAA,cAAAA,EAAAjD,KAAAiD,EAAAhD,MAAA,UACA,IAAA4C,EAAA3F,KAAAC,KADA,CAAA8F,EAAAhD,KAAA,YAEA,IAAA4C,EAAA3F,KAAAE,MAFA,CAAA6F,EAAAhD,KAAA,YAGA,IAAA4C,EAAA3F,KAAAK,KAHA,CAAA0F,EAAAhD,KAAA,YAIA,IAAA4C,EAAA3F,KAAAM,KAJA,CAAAyF,EAAAhD,KAAA,YAKA4C,EAAA3F,KAAAK,MAAAsF,EAAA3F,KAAAM,KALA,CAAAyF,EAAAhD,KAAA,YAMA,IAAA4C,EAAA3F,KAAAO,KANA,CAAAwF,EAAAhD,KAAA,YAOA,IAAA4C,EAAA3F,KAAAQ,KAPA,CAAAuF,EAAAhD,KAAA,YAQA,IAAA4C,EAAA3F,KAAAS,KARA,CAAAsF,EAAAhD,KAAA,YASA,IAAA4C,EAAA3F,KAAAU,KATA,CAAAqF,EAAAhD,KAAA,YAUA,IAAA4C,EAAA3F,KAAAW,SAVA,CAAAoF,EAAAhD,KAAA,YAWA,IAAA4C,EAAA3F,KAAAY,KAXA,CAAAmF,EAAAhD,KAAA,YAYA,IAAA4C,EAAA3F,KAAAa,SAZA,CAAAkF,EAAAhD,KAAA,gBAAA8C,GAcA1F,KAAAwF,EAAA3F,KAAAG,KACA6F,KAAAL,EAAA3F,KAAAC,KACAC,MAAAyF,EAAA3F,KAAAE,MACAE,IAAAuF,EAAA3F,KAAAI,IACA6F,GAAAN,EAAA3F,KAAAK,MAlBA6F,IAAAL,EAAA,KAmBAF,EAAA3F,KAAAM,MAnBA4F,IAAAL,EAAA,OAoBAF,EAAA3F,KAAAO,MApBA2F,IAAAL,EAAA,OAqBAF,EAAA3F,KAAAQ,MArBA0F,IAAAL,EAAA,OAsBAF,EAAA3F,KAAAS,MAtBAyF,IAAAL,EAAA,OAuBAF,EAAA3F,KAAAU,MAvBAwF,IAAAL,EAAA,QAwBAF,EAAArE,cAxBA4E,IAAAL,EAAA,SAyBAF,EAAAnE,UAzBA0E,IAAAL,EAAA,SA0BAF,EAAAjE,cA1BAwE,IAAAL,EAAA,MA2BAF,EAAA3F,KAAAc,OA3BAoF,IAAAL,EAAA,OA4BAF,EAAA3F,KAAAe,QA5BAmF,IAAAL,EAAA,OA6BAF,EAAA3F,KAAAgB,MAhBA8E,EAbAD,EAAAE,EAAAhD,KAAA,GA+BAC,OAAAC,EAAA,EAAAD,CAAA8C,GA/BA,QAgCA,MADA/F,EA/BAgG,EAAA7C,MAgCAqB,MACAoB,EAAAQ,KACAR,EAAAS,UACAC,QAAAtG,EAAAsG,QACAC,KAAA,aAEA,KAAAvG,EAAAwE,MACAoB,EAAAS,UACAC,QAAAtG,EAAAsG,QACAC,KAAA,YAzCAP,EAAAhD,KAAA,iBA6CA4C,EAAAS,SAAAG,QAAA,WA7CA,QAAAR,EAAAhD,KAAA,iBAgDA4C,EAAAS,SAAAG,QAAA,WAhDA,QAAAR,EAAAhD,KAAA,iBAmDA4C,EAAAS,SAAAG,QAAA,WAnDA,QAAAR,EAAAhD,KAAA,iBAsDA4C,EAAAS,SAAAG,QAAA,eAtDA,QAAAR,EAAAhD,KAAA,iBAyDA4C,EAAAS,SAAAG,QAAA,WAzDA,QAAAR,EAAAhD,KAAA,iBA4DA4C,EAAAS,SAAAG,QAAA,WA5DA,QAAAR,EAAAhD,KAAA,iBA+DA4C,EAAAS,SAAAG,QAAA,WA/DA,QAAAR,EAAAhD,KAAA,iBAkEA4C,EAAAS,SAAAG,QAAA,aAlEA,QAAAR,EAAAhD,KAAA,iBAqEA4C,EAAAS,SAAAG,QAAA,WArEA,QAAAR,EAAAhD,KAAA,iBAwEA4C,EAAAS,SAAAG,QAAA,WAxEA,QAAAR,EAAAhD,KAAA,iBA2EA4C,EAAAS,SAAAG,QAAA,YA3EA,QAAAR,EAAAhD,KAAA,iBA8EA4C,EAAAS,SAAAG,QAAA,WA9EA,yBAAAR,EAAA5C,SAAAyC,EAAAD,KAAApD,IAkFAiE,UAtMA,SAsMAd,GACA5D,KAAAqE,KACArE,KAAA2E,MAAAf,GAAAgB,eAEAP,GA1MA,WA2MArE,KAAA9B,KAAAC,KAAA,GACA6B,KAAA9B,KAAAE,MAAA,GACA4B,KAAA9B,KAAAG,KAAA,GACA2B,KAAA9B,KAAAI,IAAA,GACA0B,KAAA9B,KAAAK,KAAA,GACAyB,KAAA9B,KAAAM,KAAA,GACAwB,KAAA9B,KAAAO,KAAA,GACAuB,KAAA9B,KAAAQ,KAAA,GACAsB,KAAA9B,KAAAS,KAAA,GACAqB,KAAA9B,KAAAU,KAAA,GACAoB,KAAA9B,KAAAW,SAAA,GACAmB,KAAA9B,KAAAY,KAAA,GACAkB,KAAA9B,KAAAa,SAAA,GACAiB,KAAA9B,KAAAc,MAAA,GACAgB,KAAA9B,KAAAe,OAAA,GACAe,KAAA9B,KAAAgB,KAAA,MC/ae2F,GADEC,OAFjB,WAA0B,IAAAC,EAAA/E,KAAagF,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,OAAA,UAAiBH,EAAA,mBAAwBI,YAAAP,EAAAQ,KAAqBC,IAAA,OAAAC,GAAA,WAAyB,OAAAV,EAAAW,GAAA,YAA0BC,OAAA,OAAeZ,EAAAW,GAAA,KAAAR,EAAA,OAAwBU,YAAA,QAAkBV,EAAA,WAAgBW,IAAA,WAAAC,OAAsBC,MAAAhB,EAAA7G,KAAA8H,cAAA,WAAwCd,EAAA,gBAAqBY,OAAOG,MAAA,UAAgBf,EAAA,aAAkBE,aAAac,MAAA,QAAeJ,OAAQK,YAAA,WAAwBJ,OAAQK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,cAAyBzB,EAAA0B,GAAA1B,EAAA,kBAAAzC,EAAAC,GAA4C,OAAA2C,EAAA,aAAuBM,IAAAjD,EAAAuD,OAAiBM,MAAA9D,EAAA4B,KAAA+B,MAAA3D,EAAAjE,UAAuC,OAAA0G,EAAAW,GAAA,KAAAR,EAAA,gBAAwCY,OAAOG,MAAA,WAAiBf,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,MAAAmI,SAAA,SAAAC,GAAgDvB,EAAAwB,KAAAxB,EAAA7G,KAAA,QAAAoI,IAAiCE,WAAA,gBAA0BzB,EAAAW,GAAA,KAAAR,EAAA,cAA+BY,OAAOY,UAAA,QAAAR,MAAA,MAAAS,QAAA,WAAqDzB,EAAA,OAAAA,EAAA,OAAsBE,aAAawB,QAAA,OAAAC,gBAAA,UAAyC3B,EAAA,KAAUU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,SAAqDjC,EAAAW,GAAA,KAAAR,EAAA,OAAwBU,YAAA,SAAmBb,EAAAW,GAAA,UAAAX,EAAAW,GAAA,KAAAR,EAAA,OAAyCU,YAAA,SAAmBb,EAAAW,GAAA,oDAAAX,EAAAW,GAAA,KAAAR,EAAA,KAAiFU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,OAAAC,MAAA,SAAqEnB,OAAQoB,KAAA,aAAmBA,KAAA,iBAAkB,GAAAnC,EAAAW,GAAA,KAAAR,EAAA,gBAAuCY,OAAOG,MAAA,UAAgBf,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,eAAyBzB,EAAAW,GAAA,KAAAR,EAAA,cAA+BY,OAAOY,UAAA,QAAAR,MAAA,MAAAS,QAAA,WAAqDzB,EAAA,OAAAA,EAAA,OAAsBE,aAAawB,QAAA,OAAAC,gBAAA,UAAyC3B,EAAA,KAAUU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,SAAqDjC,EAAAW,GAAA,KAAAR,EAAA,OAAwBU,YAAA,SAAmBb,EAAAW,GAAA,UAAAX,EAAAW,GAAA,KAAAR,EAAA,OAAyCU,YAAA,SAAmBb,EAAAW,GAAA,2IAAAX,EAAAW,GAAA,KAAAR,EAAA,KAAwKU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,OAAAC,MAAA,SAAqEnB,OAAQoB,KAAA,aAAmBA,KAAA,iBAAkB,GAAAnC,EAAAW,GAAA,KAAAR,EAAA,gBAAuCY,OAAOG,MAAA,SAAef,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,IAAAmI,SAAA,SAAAC,GAA8CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,MAAAoI,IAA+BE,WAAA,eAAwB,GAAAzB,EAAAW,GAAA,KAAAR,EAAA,gBAAqCY,OAAOG,MAAA,UAAgBf,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,gBAAyB,GAAAzB,EAAAW,GAAA,KAAAR,EAAA,gBAAqCY,OAAOG,MAAA,UAAgBf,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,gBAAyB,GAAAzB,EAAAW,GAAA,KAAAR,EAAA,gBAAqCY,OAAOG,MAAA,UAAgBf,EAAA,aAAkBE,aAAac,MAAA,QAAeJ,OAAQK,YAAA,WAAwBJ,OAAQK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,cAAyBzB,EAAA0B,GAAA1B,EAAA,kBAAAzC,EAAAC,GAA4C,OAAA2C,EAAA,aAAuBM,IAAAjD,EAAAuD,OAAiBM,MAAA9D,EAAA6E,IAAAlB,MAAA3D,EAAA6E,SAAqC,GAAApC,EAAAW,GAAA,KAAAR,EAAA,cAAkCY,OAAOY,UAAA,QAAAR,MAAA,MAAAS,QAAA,WAAqDzB,EAAA,OAAAA,EAAA,OAAsBE,aAAawB,QAAA,OAAAC,gBAAA,UAAyC3B,EAAA,KAAUU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,SAAqDjC,EAAAW,GAAA,KAAAR,EAAA,OAAwBU,YAAA,SAAmBb,EAAAW,GAAA,UAAAX,EAAAW,GAAA,KAAAR,EAAA,OAAyCU,YAAA,SAAmBb,EAAAW,GAAA,4UAAAX,EAAAW,GAAA,KAAAR,EAAA,KAAyWU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,OAAAC,MAAA,SAAqEnB,OAAQoB,KAAA,aAAmBA,KAAA,iBAAkB,GAAAnC,EAAAW,GAAA,KAAAR,EAAA,gBAAuCY,OAAOG,MAAA,UAAgBf,EAAA,aAAkBE,aAAac,MAAA,QAAeJ,OAAQK,YAAA,WAAwBJ,OAAQK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,cAAyBzB,EAAA0B,GAAA1B,EAAA,kBAAAzC,EAAAC,GAA4C,OAAA2C,EAAA,aAAuBM,IAAAjD,EAAAuD,OAAiBM,MAAA9D,EAAA6E,IAAAlB,MAAA3D,EAAA6E,SAAqC,GAAApC,EAAAW,GAAA,KAAAR,EAAA,cAAkCY,OAAOY,UAAA,QAAAR,MAAA,MAAAS,QAAA,WAAqDzB,EAAA,OAAAA,EAAA,OAAsBE,aAAawB,QAAA,OAAAC,gBAAA,UAAyC3B,EAAA,KAAUU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,SAAqDjC,EAAAW,GAAA,KAAAR,EAAA,OAAwBU,YAAA,SAAmBb,EAAAW,GAAA,UAAAX,EAAAW,GAAA,KAAAR,EAAA,OAAyCU,YAAA,SAAmBb,EAAAW,GAAA,iGAAAX,EAAAW,GAAA,KAAAR,EAAA,KAA8HU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,OAAAC,MAAA,SAAqEnB,OAAQoB,KAAA,aAAmBA,KAAA,iBAAkB,GAAAnC,EAAAW,GAAA,KAAAR,EAAA,gBAAuCY,OAAOG,MAAA,UAAgBf,EAAA,aAAkBE,aAAac,MAAA,QAAeJ,OAAQK,YAAA,WAAwBJ,OAAQK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,cAAyBzB,EAAA0B,GAAA1B,EAAA,kBAAAzC,EAAAC,GAA4C,OAAA2C,EAAA,aAAuBM,IAAAjD,EAAAuD,OAAiBM,MAAA9D,EAAA8E,IAAAnB,MAAA3D,EAAA6E,SAAqC,GAAApC,EAAAW,GAAA,KAAAR,EAAA,cAAkCY,OAAOY,UAAA,QAAAR,MAAA,MAAAS,QAAA,WAAqDzB,EAAA,OAAAA,EAAA,OAAsBE,aAAawB,QAAA,OAAAC,gBAAA,UAAyC3B,EAAA,KAAUU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,SAAqDjC,EAAAW,GAAA,KAAAR,EAAA,OAAwBU,YAAA,SAAmBb,EAAAW,GAAA,UAAAX,EAAAW,GAAA,KAAAR,EAAA,OAAyCU,YAAA,SAAmBb,EAAAW,GAAA,qOAAAX,EAAAW,GAAA,KAAAR,EAAA,KAAkQU,YAAA,eAAAR,aAAwC0B,MAAA,UAAAC,SAAA,WAAAC,IAAA,OAAAC,MAAA,SAAqEnB,OAAQoB,KAAA,aAAmBA,KAAA,iBAAkB,GAAAnC,EAAAW,GAAA,KAAAR,EAAA,gBAAuCY,OAAOG,MAAA,cAAoBf,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,gBAAyB,GAAAzB,EAAAW,GAAA,KAAAR,EAAA,gBAAqCY,OAAOG,MAAA,cAAoBf,EAAA,aAAkBE,aAAac,MAAA,SAAgBmB,IAAKC,OAAAvC,EAAAhD,iBAA6BgE,OAAQK,MAAArB,EAAA7G,KAAA,SAAAmI,SAAA,SAAAC,GAAmDvB,EAAAwB,KAAAxB,EAAA7G,KAAA,WAAAoI,IAAoCE,WAAA,kBAA6BzB,EAAA0B,GAAA1B,EAAA,sBAAAzC,EAAAC,GAAgD,OAAA2C,EAAA,aAAuBM,IAAA,WAAAjD,EAAAuD,OAA8BG,MAAA3D,EAAAE,KAAA4D,MAAA9D,EAAAE,UAAuC,GAAAuC,EAAAW,GAAA,KAAAR,EAAA,aAAiCE,aAAac,MAAA,SAAgBmB,IAAKC,OAAAvC,EAAApC,aAAyBoD,OAAQK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,cAAyBzB,EAAA0B,GAAA1B,EAAA,kBAAAzC,EAAAC,GAA4C,OAAA2C,EAAA,aAAuBM,IAAA,OAAAjD,EAAAuD,OAA0BG,MAAA3D,EAAAE,KAAA4D,MAAA9D,EAAAE,UAAuC,GAAAuC,EAAAW,GAAA,KAAAR,EAAA,aAAiCE,aAAac,MAAA,SAAgBmB,IAAKC,OAAAvC,EAAA/B,iBAA6B+C,OAAQK,MAAArB,EAAA7G,KAAA,SAAAmI,SAAA,SAAAC,GAAmDvB,EAAAwB,KAAAxB,EAAA7G,KAAA,WAAAoI,IAAoCE,WAAA,kBAA6BzB,EAAA0B,GAAA1B,EAAA,sBAAAzC,EAAAC,GAAgD,OAAA2C,EAAA,aAAuBM,IAAA,WAAAjD,EAAAuD,OAA8BG,MAAA3D,EAAAE,KAAA4D,MAAA9D,EAAAE,UAAuC,OAAAuC,EAAAW,GAAA,KAAAR,EAAA,gBAAwCY,OAAOG,MAAA,WAAiBf,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,MAAAmI,SAAA,SAAAC,GAAgDvB,EAAAwB,KAAAxB,EAAA7G,KAAA,QAAAoI,IAAiCE,WAAA,iBAA0B,GAAAzB,EAAAW,GAAA,KAAAR,EAAA,gBAAqCY,OAAOG,MAAA,cAAoBf,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,OAAAmI,SAAA,SAAAC,GAAiDvB,EAAAwB,KAAAxB,EAAA7G,KAAA,SAAAoI,IAAkCE,WAAA,kBAA2B,GAAAzB,EAAAW,GAAA,KAAAR,EAAA,gBAAqCY,OAAOG,MAAA,YAAkBf,EAAA,YAAiBa,OAAOK,MAAArB,EAAA7G,KAAA,KAAAmI,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAA7G,KAAA,OAAAoI,IAAgCE,WAAA,gBAAyB,GAAAzB,EAAAW,GAAA,KAAAR,EAAA,gBAAAA,EAAA,aAAqDY,OAAOtB,KAAA,WAAiB6C,IAAKE,MAAA,SAAAC,GAAyB,OAAAzC,EAAApB,SAAA,gBAAkCoB,EAAAW,GAAA,2BAEhvQ+B,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACE5J,EACA6G,GATF,EAVA,SAAAgD,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/88.394f7e359aece3109af1.js", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%\">\r\n    <hsoft_top_title>\r\n      <template #left>注册登录信息</template>\r\n    </hsoft_top_title>\r\n    <div class=\"con\">\r\n      <el-form ref=\"ruleForm\" :model=\"dwxx\" label-width=\"150px\">\r\n        <el-form-item label=\"上级单位\">\r\n          <el-select v-model=\"dwxx.sjdw\" style=\"width: 100%;\" placeholder=\"请选择上级单位\">\r\n            <el-option v-for=\"(item, index) in sjdwList\" :key=\"index\" :value=\"item.dwid\" :label=\"item.dwmc\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位注册号\">\r\n          <el-input v-model=\"dwxx.dwzch\"></el-input>\r\n          <el-popover placement=\"right\" width=\"200\" trigger=\"hover\">\r\n            <div>\r\n              <div style=\"display:flex;margin-bottom:10px\">\r\n                <i class=\"el-icon-info\" style=\"color:#409eef;    position: relative;\r\n                                            top: 2px;\"></i>\r\n                <div class=\"tszt\">提示</div>\r\n              </div>\r\n              <div class=\"smzt\">\r\n                单位注册号由程序下发单位下发\r\n              </div>\r\n            </div>\r\n            <i class=\"el-icon-info\" style=\"color:#409eef;position: absolute;    top: 14px; right: -22px\"\r\n              slot=\"reference\"></i>\r\n          </el-popover>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位名称\">\r\n          <el-input v-model=\"dwxx.dwmc\"></el-input>\r\n          <el-popover placement=\"right\" width=\"200\" trigger=\"hover\">\r\n            <div>\r\n              <div style=\"display:flex;margin-bottom:10px\">\r\n                <i class=\"el-icon-info\" style=\"color:#409eef;    position: relative;\r\n                                            top: 2px;\"></i>\r\n                <div class=\"tszt\">提示</div>\r\n              </div>\r\n              <div class=\"smzt\">\r\n                应填写全称，以统一社会信用代码登记信息为准，统一社会信用代码及其他登记信息可过全国组织机构统一社会信用代码数据服务中心（www.cods.org.cn）官网或官方APP、微信小程序“统一代码查询”查询。\r\n              </div>\r\n            </div>\r\n            <i class=\"el-icon-info\" style=\"color:#409eef;position: absolute;    top: 14px; right: -22px\"\r\n              slot=\"reference\"></i>\r\n\r\n          </el-popover>\r\n        </el-form-item>\r\n        <el-form-item label=\"用户名\">\r\n          <el-input v-model=\"dwxx.yhm\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"登录密码\">\r\n          <el-input v-model=\"dwxx.dlmm\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"确认密码\">\r\n          <el-input v-model=\"dwxx.qrmm\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位类型\">\r\n          <el-select v-model=\"dwxx.dwlx\" style=\"width: 100%;\" placeholder=\"请选择单位类型\">\r\n            <el-option v-for=\"(item, index) in dwlxList\" :key=\"index\" :value=\"item.csm\" :label=\"item.csm\"></el-option>\r\n          </el-select>\r\n          <el-popover placement=\"right\" width=\"200\" trigger=\"hover\">\r\n            <div>\r\n              <div style=\"display:flex;margin-bottom:10px\">\r\n                <i class=\"el-icon-info\" style=\"color:#409eef;    position: relative;\r\n                                            top: 2px;\"></i>\r\n                <div class=\"tszt\">提示</div>\r\n              </div>\r\n              <div class=\"smzt\">\r\n                从下拉栏中选择：党政机关主要包括党的机关、人大机关、行政机关、政协机关、监察机关、审判机关、检察机关、民主党派机关；人民团体主要包括工会组织、共青团组织、妇女联合会组织等人民团体，也包括学会、协会、研究会等社团组织；参公事业单位指参照公务员法管理事业单位；事业单位指接受政府领导，主要提供教育、科技、文化、卫生等活动非物质生产和劳务服务的社会公共组织或机构；国有企业指由中央管理或者地方政府监管的国有企业，包括国有独资企业、国有控股企业（不包括国有参股企业）；民营保密资质（资格）企业指民间资本作为投资主体，具有印制、集成或者军工保密资质（资格）的非公有制企业；其他指上述类型之外的单位。\r\n              </div>\r\n            </div>\r\n            <i class=\"el-icon-info\" style=\"color:#409eef;position: absolute;    top: 14px; right: -22px\"\r\n              slot=\"reference\"></i>\r\n\r\n          </el-popover>\r\n        </el-form-item>\r\n        <el-form-item label=\"所属领域\">\r\n          <el-select v-model=\"dwxx.ssly\" style=\"width: 100%;\" placeholder=\"请选择所属领域\">\r\n            <el-option v-for=\"(item, index) in sslyList\" :key=\"index\" :value=\"item.csm\" :label=\"item.csm\"></el-option>\r\n          </el-select>\r\n          <el-popover placement=\"right\" width=\"200\" trigger=\"hover\">\r\n            <div>\r\n              <div style=\"display:flex;margin-bottom:10px\">\r\n                <i class=\"el-icon-info\" style=\"color:#409eef;    position: relative;\r\n                                            top: 2px;\"></i>\r\n                <div class=\"tszt\">提示</div>\r\n              </div>\r\n              <div class=\"smzt\">\r\n                从下拉栏中选择：国防军工、外交外事、安全、政法、经济金融、科技、教育、能源、测绘、其他。如涉及多个领域，选择主要领域。\r\n              </div>\r\n            </div>\r\n            <i class=\"el-icon-info\" style=\"color:#409eef;position: absolute;    top: 14px; right: -22px\"\r\n              slot=\"reference\"></i>\r\n\r\n          </el-popover>\r\n        </el-form-item>\r\n        <el-form-item label=\"所属层次\">\r\n          <el-select v-model=\"dwxx.sscc\" style=\"width: 100%;\" placeholder=\"请选择所属层次\">\r\n            <el-option v-for=\"(item, index) in ssccList\" :key=\"index\" :value=\"item.csz\" :label=\"item.csm\"></el-option>\r\n          </el-select>\r\n          <el-popover placement=\"right\" width=\"200\" trigger=\"hover\">\r\n            <div>\r\n              <div style=\"display:flex;margin-bottom:10px\">\r\n                <i class=\"el-icon-info\" style=\"color:#409eef;    position: relative;\r\n                                            top: 2px;\"></i>\r\n                <div class=\"tszt\">提示</div>\r\n              </div>\r\n              <div class=\"smzt\">\r\n                从下拉栏中选择：中央机关、企业本级/中央机关、企业下属驻京单位/中央机关、企业京外单位/省直机关、企业本级/省直机关、企业下属单位/市直机关、企业本级/市直机关、企业下属单位/县直和乡镇机关、企业本级/县直和乡镇机关、企业下属单位/私营企业保密资质单位（甲级）/私营企业保密资质单位（乙级）/私营企业保密资格单位（一级）/私营企业保密资格单位（二级）/私营企业保密资格单位（三级）。\r\n              </div>\r\n            </div>\r\n            <i class=\"el-icon-info\" style=\"color:#409eef;position: absolute;    top: 14px; right: -22px\"\r\n              slot=\"reference\"></i>\r\n\r\n          </el-popover>\r\n        </el-form-item>\r\n        <el-form-item label=\"统一社会信用代码\">\r\n          <el-input v-model=\"dwxx.tydm\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位所在行政区域\">\r\n          <el-select v-model=\"dwxx.province\" @change=\"provinceChanged\" style=\"width: 32.8%;\">\r\n            <el-option v-for=\"(item, index) in provinceList\" :key=\"'province' + index\" :label=\"item.name\"\r\n              :value=\"item.name\"></el-option>\r\n          </el-select>\r\n          <el-select v-model=\"dwxx.city\" @change=\"cityChanged\" style=\"width: 32.9%;\">\r\n            <el-option v-for=\"(item, index) in cityList\" :key=\"'city' + index\" :label=\"item.name\"\r\n              :value=\"item.name\"></el-option>\r\n          </el-select>\r\n          <el-select v-model=\"dwxx.district\" @change=\"districtChanged\" style=\"width: 32.8%;\">\r\n            <el-option v-for=\"(item, index) in districtList\" :key=\"'district' + index\" :label=\"item.name\"\r\n              :value=\"item.name\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位联系人\">\r\n          <el-input v-model=\"dwxx.dwlxr\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位联系电话号码\">\r\n          <el-input v-model=\"dwxx.dwlxdh\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位联系邮箱\">\r\n          <el-input v-model=\"dwxx.dwyx\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"onSubmit('ruleForm')\">立即创建</el-button>\r\n          <!-- <el-button @click=\"resetForm('ruleForm')\">取消</el-button> -->\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'\r\n\r\nimport {\r\n  getDwxxList,\r\n  getAllDwlx,\r\n  getAllSsly,\r\n  getAllSscj,\r\n  getProvinceList,\r\n  getCityByProvincecode,\r\n  getAreaByCitycode,\r\n  registerDwxx\r\n} from '../../../api/dwzc'\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 单位信息\r\n      dwxx: {\r\n        sjdw: '', // 上级单位\r\n        dwzch: '', // 单位注册号\r\n        dwmc: '', // 单位名称\r\n        yhm: '', // 用户名\r\n        dlmm: '', // 登录密码\r\n        qrmm: '', // 确认密码\r\n        dwlx: '', // 单位类型\r\n        ssly: '', // 所属领域\r\n        sscc: '', // 所属层次\r\n        tydm: '', // 统一社会信用代码\r\n        province: \"\", // 省\r\n        city: \"\", // 市\r\n        district: \"\", // 区\r\n        dwlxr: '', // 单位联系人\r\n        dwlxdh: '', // 单位联系电话\r\n        dwyx: '', // 单位联系邮箱\r\n      },\r\n      sjdwList: [],\r\n      dwlxList: [],\r\n      sslyList: [],\r\n      ssccList: [],\r\n      // 省集合\r\n      provinceList: [],\r\n      provincecode: '',\r\n      // 市集合\r\n      cityList: [],\r\n      citycode: '',\r\n      // 区集合\r\n      districtList: [],\r\n      districtcode: '',\r\n    };\r\n  },\r\n  components: {\r\n    hsoft_top_title\r\n  },\r\n  mounted() {\r\n    this.getAllDwlx()\r\n    this.getAllSjdw()\r\n    this.getAllSsly()\r\n    this.getAllSscc()\r\n    // 初始化省市区数据\r\n    this.initSsq()\r\n  },\r\n  // 监听\r\n  watch: {\r\n\r\n  },\r\n  methods: {\r\n    // 获取上级单位\r\n    async getAllSjdw() {\r\n      this.sjdwList = await getDwxxList()\r\n    },\r\n    // 获取所有单位类型\r\n    async getAllDwlx() {\r\n      this.dwlxList = await getAllDwlx()\r\n    },\r\n    // 获取所有所属领域\r\n    async getAllSsly() {\r\n      this.sslyList = await getAllSsly()\r\n    },\r\n    // 获取所有所属层次\r\n    async getAllSscc() {\r\n      this.ssccList = await getAllSscj()\r\n    },\r\n    // 省改变事件\r\n    async provinceChanged(province) {\r\n      console.log(province);\r\n      this.provinceList = await getProvinceList();\r\n      console.log('全国各个省份：', this.provinceList);\r\n      this.provinceList.forEach((item, index) => {\r\n        if (item.name == province) {\r\n          console.log('省份item', item);\r\n          this.dwxx.province = item.name\r\n          this.provincecode = item.code\r\n          console.log(this.provincecode);\r\n        }\r\n      })\r\n      // 重置区\r\n      this.districtList = []\r\n      // 重置区划\r\n      this.dwxx.regionalNumber = ''\r\n      // 重置市区数据\r\n      this.dwxx.city = ''\r\n      this.dwxx.district = ''\r\n      this.cityChanged()\r\n    },\r\n    // 市改变事件\r\n    async cityChanged(city) {\r\n      // // 重新初始化区\r\n      //市级\r\n      let cityparam = {\r\n        provincecode: this.provincecode\r\n      }\r\n      this.cityList = await getCityByProvincecode(cityparam)\r\n      console.log('各个省份下属地级市：', this.cityList);\r\n      this.cityList.forEach((item, index) => {\r\n        if (city == item.name) {\r\n          console.log('地级市item', item);\r\n          this.dwxx.city = item.name\r\n          this.citycode = item.code\r\n        }\r\n      })\r\n      // 重置区划\r\n      this.dwxx.regionalNumber = ''\r\n      // 重置区数据\r\n      this.dwxx.district = ''\r\n      this.districtChanged()\r\n    },\r\n    // 区改变事件\r\n    async districtChanged(district) {\r\n      //区级\r\n      let districtparam = {\r\n        citycode: this.citycode\r\n      }\r\n      this.districtList = await getAreaByCitycode(districtparam)\r\n      console.log('地级市下属市区：', this.districtList);\r\n      this.districtList.forEach((item, index) => {\r\n        if (district == item.name) {\r\n          console.log('市区item', item);\r\n          this.dwxx.district = item.name\r\n          this.districtcode = item.code\r\n        }\r\n      })\r\n    },\r\n    // 初始化省市区数据\r\n    async initSsq() {\r\n      //省级\r\n      this.provinceList = await getProvinceList();\r\n      console.log('全国各个省份：', this.provinceList);\r\n      this.provinceList.forEach((item, index) => {\r\n        if (this.dwxx.szsid == item.code) {\r\n          console.log('省份item', item);\r\n          this.dwxx.province = item.name\r\n          this.provincecode = item.code\r\n        }\r\n      })\r\n      //市级\r\n      let cityparam = {\r\n        provincecode: this.provincecode\r\n      }\r\n      this.cityList = await getCityByProvincecode(cityparam)\r\n      console.log('各个省份下属地级市：', this.cityList);\r\n      this.cityList.forEach((item, index) => {\r\n        if (this.dwxx.szsdid == item.code) {\r\n          console.log('地级市item', item);\r\n          this.dwxx.city = item.name\r\n          this.citycode = item.code\r\n        }\r\n      })\r\n      //区级\r\n      let districtparam = {\r\n        citycode: this.citycode\r\n      }\r\n      this.districtList = await getAreaByCitycode(districtparam)\r\n      console.log('地级市下属市区：', this.districtList);\r\n      this.districtList.forEach((item, index) => {\r\n        if (this.dwxx.szqxid == item.code) {\r\n          console.log('市区item', item);\r\n          this.dwxx.district = item.name\r\n          this.districtcode = item.code\r\n        }\r\n      })\r\n    },\r\n    async onSubmit(formName) {\r\n      if (this.dwxx.sjdw != '') {\r\n        if (this.dwxx.dwzch != '') {\r\n          if (this.dwxx.dlmm != '') {\r\n            if (this.dwxx.qrmm != '') {\r\n              if (this.dwxx.dlmm == this.dwxx.qrmm) {\r\n                if (this.dwxx.dwlx != '') {\r\n                  if (this.dwxx.ssly != '') {\r\n                    if (this.dwxx.sscc != '') {\r\n                      if (this.dwxx.tydm != '') {\r\n                        if (this.dwxx.province != '') {\r\n                          if (this.dwxx.city != '') {\r\n                            if (this.dwxx.district != '') {\r\n                              let params = {\r\n                                dwmc: this.dwxx.dwmc,\r\n                                dwid: this.dwxx.sjdw,\r\n                                dwzch: this.dwxx.dwzch,\r\n                                yhm: this.dwxx.yhm,\r\n                                mm: this.dwxx.dlmm,\r\n                                mm: this.dwxx.qrmm,\r\n                                dwlx: this.dwxx.dwlx,\r\n                                ssly: this.dwxx.ssly,\r\n                                sscj: this.dwxx.sscc,\r\n                                tydm: this.dwxx.tydm,\r\n                                szsid: this.provincecode,\r\n                                szsdid: this.citycode,\r\n                                szqxid: this.districtcode,\r\n                                lxr: this.dwxx.dwlxr,\r\n                                lxdh: this.dwxx.dwlxdh,\r\n                                lxyx: this.dwxx.dwyx,\r\n                              }\r\n                              let data = await registerDwxx(params)\r\n                              if (data.code == 10000) {\r\n                                this.cz()\r\n                                this.$message({\r\n                                  message: data.message,\r\n                                  type: 'success'\r\n                                });\r\n                              } else if (data.code == 10000) {\r\n                                this.$message({\r\n                                  message: data.message,\r\n                                  type: 'warning'\r\n                                });\r\n                              }\r\n                            } else {\r\n                              this.$message.warning('请选择所在区县')\r\n                            }\r\n                          } else {\r\n                            this.$message.warning('请选择所在市区')\r\n                          }\r\n                        } else {\r\n                          this.$message.warning('请选择所在省份')\r\n                        }\r\n                      } else {\r\n                        this.$message.warning('请输入统一社会信用代码')\r\n                      }\r\n                    } else {\r\n                      this.$message.warning('请选择所属层次')\r\n                    }\r\n                  } else {\r\n                    this.$message.warning('请选择所属领域')\r\n                  }\r\n                } else {\r\n                  this.$message.warning('请选择单位类型')\r\n                }\r\n              } else {\r\n                this.$message.warning('两次输入密码不相符')\r\n              }\r\n            } else {\r\n              this.$message.warning('请输入确认密码')\r\n            }\r\n          } else {\r\n            this.$message.warning('请输入登录密码')\r\n          }\r\n        } else {\r\n          this.$message.warning('请输入单位注册号')\r\n        }\r\n      } else {\r\n        this.$message.warning('请选择上级单位')\r\n      }\r\n\r\n    },\r\n    resetForm(formName) {\r\n      this.cz()\r\n      this.$refs[formName].resetFields();\r\n    },\r\n    cz() {\r\n      this.dwxx.sjdw = '' // 上级单位\r\n      this.dwxx.dwzch = '' // 单位注册号\r\n      this.dwxx.dwmc = '' // 单位名称\r\n      this.dwxx.yhm = '' // 用户名\r\n      this.dwxx.dlmm = '' // 登录密码\r\n      this.dwxx.qrmm = '' // 确认密码\r\n      this.dwxx.dwlx = '' // 单位类型\r\n      this.dwxx.ssly = '' // 所属领域\r\n      this.dwxx.sscc = '' // 所属层次\r\n      this.dwxx.tydm = '' // 统一社会信用代码\r\n      this.dwxx.province = '' // 省\r\n      this.dwxx.city = '' // 市\r\n      this.dwxx.district = '' // 区\r\n      this.dwxx.dwlxr = '' // 单位联系人\r\n      this.dwxx.dwlxdh = '' // 单位联系电话\r\n      this.dwxx.dwyx = '' // 单位联系邮箱\r\n    }\r\n  },\r\n\r\n\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.con {\r\n  width: 800px;\r\n  height: 100%;\r\n  margin: 0 auto;\r\n}\r\n\r\n/deep/.el-form-item {\r\n  margin-bottom: 8px !important;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/xtsz/signIn.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"height\":\"100%\"}},[_c('hsoft_top_title',{scopedSlots:_vm._u([{key:\"left\",fn:function(){return [_vm._v(\"注册登录信息\")]},proxy:true}])}),_vm._v(\" \"),_c('div',{staticClass:\"con\"},[_c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.dwxx,\"label-width\":\"150px\"}},[_c('el-form-item',{attrs:{\"label\":\"上级单位\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择上级单位\"},model:{value:(_vm.dwxx.sjdw),callback:function ($$v) {_vm.$set(_vm.dwxx, \"sjdw\", $$v)},expression:\"dwxx.sjdw\"}},_vm._l((_vm.sjdwList),function(item,index){return _c('el-option',{key:index,attrs:{\"value\":item.dwid,\"label\":item.dwmc}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"单位注册号\"}},[_c('el-input',{model:{value:(_vm.dwxx.dwzch),callback:function ($$v) {_vm.$set(_vm.dwxx, \"dwzch\", $$v)},expression:\"dwxx.dwzch\"}}),_vm._v(\" \"),_c('el-popover',{attrs:{\"placement\":\"right\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"margin-bottom\":\"10px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"relative\",\"top\":\"2px\"}}),_vm._v(\" \"),_c('div',{staticClass:\"tszt\"},[_vm._v(\"提示\")])]),_vm._v(\" \"),_c('div',{staticClass:\"smzt\"},[_vm._v(\"\\n              单位注册号由程序下发单位下发\\n            \")])]),_vm._v(\" \"),_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"absolute\",\"top\":\"14px\",\"right\":\"-22px\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"})])],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"单位名称\"}},[_c('el-input',{model:{value:(_vm.dwxx.dwmc),callback:function ($$v) {_vm.$set(_vm.dwxx, \"dwmc\", $$v)},expression:\"dwxx.dwmc\"}}),_vm._v(\" \"),_c('el-popover',{attrs:{\"placement\":\"right\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"margin-bottom\":\"10px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"relative\",\"top\":\"2px\"}}),_vm._v(\" \"),_c('div',{staticClass:\"tszt\"},[_vm._v(\"提示\")])]),_vm._v(\" \"),_c('div',{staticClass:\"smzt\"},[_vm._v(\"\\n              应填写全称，以统一社会信用代码登记信息为准，统一社会信用代码及其他登记信息可过全国组织机构统一社会信用代码数据服务中心（www.cods.org.cn）官网或官方APP、微信小程序“统一代码查询”查询。\\n            \")])]),_vm._v(\" \"),_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"absolute\",\"top\":\"14px\",\"right\":\"-22px\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"})])],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"用户名\"}},[_c('el-input',{model:{value:(_vm.dwxx.yhm),callback:function ($$v) {_vm.$set(_vm.dwxx, \"yhm\", $$v)},expression:\"dwxx.yhm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"登录密码\"}},[_c('el-input',{model:{value:(_vm.dwxx.dlmm),callback:function ($$v) {_vm.$set(_vm.dwxx, \"dlmm\", $$v)},expression:\"dwxx.dlmm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"确认密码\"}},[_c('el-input',{model:{value:(_vm.dwxx.qrmm),callback:function ($$v) {_vm.$set(_vm.dwxx, \"qrmm\", $$v)},expression:\"dwxx.qrmm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"单位类型\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择单位类型\"},model:{value:(_vm.dwxx.dwlx),callback:function ($$v) {_vm.$set(_vm.dwxx, \"dwlx\", $$v)},expression:\"dwxx.dwlx\"}},_vm._l((_vm.dwlxList),function(item,index){return _c('el-option',{key:index,attrs:{\"value\":item.csm,\"label\":item.csm}})}),1),_vm._v(\" \"),_c('el-popover',{attrs:{\"placement\":\"right\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"margin-bottom\":\"10px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"relative\",\"top\":\"2px\"}}),_vm._v(\" \"),_c('div',{staticClass:\"tszt\"},[_vm._v(\"提示\")])]),_vm._v(\" \"),_c('div',{staticClass:\"smzt\"},[_vm._v(\"\\n              从下拉栏中选择：党政机关主要包括党的机关、人大机关、行政机关、政协机关、监察机关、审判机关、检察机关、民主党派机关；人民团体主要包括工会组织、共青团组织、妇女联合会组织等人民团体，也包括学会、协会、研究会等社团组织；参公事业单位指参照公务员法管理事业单位；事业单位指接受政府领导，主要提供教育、科技、文化、卫生等活动非物质生产和劳务服务的社会公共组织或机构；国有企业指由中央管理或者地方政府监管的国有企业，包括国有独资企业、国有控股企业（不包括国有参股企业）；民营保密资质（资格）企业指民间资本作为投资主体，具有印制、集成或者军工保密资质（资格）的非公有制企业；其他指上述类型之外的单位。\\n            \")])]),_vm._v(\" \"),_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"absolute\",\"top\":\"14px\",\"right\":\"-22px\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"})])],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"所属领域\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择所属领域\"},model:{value:(_vm.dwxx.ssly),callback:function ($$v) {_vm.$set(_vm.dwxx, \"ssly\", $$v)},expression:\"dwxx.ssly\"}},_vm._l((_vm.sslyList),function(item,index){return _c('el-option',{key:index,attrs:{\"value\":item.csm,\"label\":item.csm}})}),1),_vm._v(\" \"),_c('el-popover',{attrs:{\"placement\":\"right\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"margin-bottom\":\"10px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"relative\",\"top\":\"2px\"}}),_vm._v(\" \"),_c('div',{staticClass:\"tszt\"},[_vm._v(\"提示\")])]),_vm._v(\" \"),_c('div',{staticClass:\"smzt\"},[_vm._v(\"\\n              从下拉栏中选择：国防军工、外交外事、安全、政法、经济金融、科技、教育、能源、测绘、其他。如涉及多个领域，选择主要领域。\\n            \")])]),_vm._v(\" \"),_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"absolute\",\"top\":\"14px\",\"right\":\"-22px\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"})])],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"所属层次\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择所属层次\"},model:{value:(_vm.dwxx.sscc),callback:function ($$v) {_vm.$set(_vm.dwxx, \"sscc\", $$v)},expression:\"dwxx.sscc\"}},_vm._l((_vm.ssccList),function(item,index){return _c('el-option',{key:index,attrs:{\"value\":item.csz,\"label\":item.csm}})}),1),_vm._v(\" \"),_c('el-popover',{attrs:{\"placement\":\"right\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"margin-bottom\":\"10px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"relative\",\"top\":\"2px\"}}),_vm._v(\" \"),_c('div',{staticClass:\"tszt\"},[_vm._v(\"提示\")])]),_vm._v(\" \"),_c('div',{staticClass:\"smzt\"},[_vm._v(\"\\n              从下拉栏中选择：中央机关、企业本级/中央机关、企业下属驻京单位/中央机关、企业京外单位/省直机关、企业本级/省直机关、企业下属单位/市直机关、企业本级/市直机关、企业下属单位/县直和乡镇机关、企业本级/县直和乡镇机关、企业下属单位/私营企业保密资质单位（甲级）/私营企业保密资质单位（乙级）/私营企业保密资格单位（一级）/私营企业保密资格单位（二级）/私营企业保密资格单位（三级）。\\n            \")])]),_vm._v(\" \"),_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"absolute\",\"top\":\"14px\",\"right\":\"-22px\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"})])],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"统一社会信用代码\"}},[_c('el-input',{model:{value:(_vm.dwxx.tydm),callback:function ($$v) {_vm.$set(_vm.dwxx, \"tydm\", $$v)},expression:\"dwxx.tydm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"单位所在行政区域\"}},[_c('el-select',{staticStyle:{\"width\":\"32.8%\"},on:{\"change\":_vm.provinceChanged},model:{value:(_vm.dwxx.province),callback:function ($$v) {_vm.$set(_vm.dwxx, \"province\", $$v)},expression:\"dwxx.province\"}},_vm._l((_vm.provinceList),function(item,index){return _c('el-option',{key:'province' + index,attrs:{\"label\":item.name,\"value\":item.name}})}),1),_vm._v(\" \"),_c('el-select',{staticStyle:{\"width\":\"32.9%\"},on:{\"change\":_vm.cityChanged},model:{value:(_vm.dwxx.city),callback:function ($$v) {_vm.$set(_vm.dwxx, \"city\", $$v)},expression:\"dwxx.city\"}},_vm._l((_vm.cityList),function(item,index){return _c('el-option',{key:'city' + index,attrs:{\"label\":item.name,\"value\":item.name}})}),1),_vm._v(\" \"),_c('el-select',{staticStyle:{\"width\":\"32.8%\"},on:{\"change\":_vm.districtChanged},model:{value:(_vm.dwxx.district),callback:function ($$v) {_vm.$set(_vm.dwxx, \"district\", $$v)},expression:\"dwxx.district\"}},_vm._l((_vm.districtList),function(item,index){return _c('el-option',{key:'district' + index,attrs:{\"label\":item.name,\"value\":item.name}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"单位联系人\"}},[_c('el-input',{model:{value:(_vm.dwxx.dwlxr),callback:function ($$v) {_vm.$set(_vm.dwxx, \"dwlxr\", $$v)},expression:\"dwxx.dwlxr\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"单位联系电话号码\"}},[_c('el-input',{model:{value:(_vm.dwxx.dwlxdh),callback:function ($$v) {_vm.$set(_vm.dwxx, \"dwlxdh\", $$v)},expression:\"dwxx.dwlxdh\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"单位联系邮箱\"}},[_c('el-input',{model:{value:(_vm.dwxx.dwyx),callback:function ($$v) {_vm.$set(_vm.dwxx, \"dwyx\", $$v)},expression:\"dwxx.dwyx\"}})],1),_vm._v(\" \"),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.onSubmit('ruleForm')}}},[_vm._v(\"立即创建\")])],1)],1)],1)],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-8eb959de\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/xtsz/signIn.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-8eb959de\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./signIn.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./signIn.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./signIn.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-8eb959de\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./signIn.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-8eb959de\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/xtsz/signIn.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}