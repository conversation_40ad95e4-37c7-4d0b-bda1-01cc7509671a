<template>
  <div class="sec-container" v-loading="loading">
    <!-- 标题 -->
    <p class="sec-title">基本信息</p>
    <div class="sec-form-container">
      <el-form ref="formName" :model="tjlist" label-width="225px">
        <!-- 第一部分包括姓名到常住地公安start -->
        <div class="sec-header-section">
          <div class="sec-form-left">
            <el-form-item label="姓名">
              <el-input placeholder="" v-model="tjlist.xm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <!-- <el-input placeholder="" v-model="tjlist.xb" clearable disabled></el-input> -->
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.xingbie" clearable disabled></el-input>
              </template>
            </el-form-item>
            <el-form-item label="出生年月日">
              <el-input placeholder="" v-model="tjlist.csny" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="政治面貌">
              <el-input placeholder="" v-model="tjlist.zzmmxx" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="涉密岗位">
              <el-input placeholder="" v-model="tjlist.gwmc" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="涉密等级">
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.smdjxx" clearable disabled></el-input>
                <!-- <p class="hyzk" v-if="tjlist.hyzk == 0">未婚</p>
                <p class="hyzk" v-if="tjlist.hyzk == 1">已婚</p> -->
              </template>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="工作单位及职务">
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.bmzwzc" clearable disabled></el-input>
                <!-- <p class="hyzk" v-if="tjlist.zzmm == 1">中共党员</p>
                <p class="hyzk" v-if="tjlist.zzmm == 2">团员</p>
                <p class="hyzk" v-if="tjlist.zzmm == 3">民主党派</p>
                <p class="hyzk" v-if="tjlist.zzmm == 4">群众</p> -->
              </template>
            </el-form-item>
            <el-form-item label="身份证号">
              <el-input placeholder="" v-model="tjlist.sfzhm" clearable disabled></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 单位及职务、职称到涉密等级end -->
        <!-- 主要学习及工作经历start -->
        <p class="sec-title">审批事项</p>
        <div class="sec-form-container">
          <el-form ref="formName" :model="tjlist" label-width="225px">
            <div class="sec-header-section">
              <div class="sec-form-left longLabel">
                <el-form-item label="审批事件类型">
                  <el-radio v-model="tjlist.splx" label="1">新申办从出入境证件</el-radio>
                  <el-radio v-model="tjlist.splx" label="0">申请出国（境）</el-radio>
                </el-form-item>
              </div>
              <div class="sec-form-left longLabel widthzz">
                <el-form-item label="证件类型">
                  <div style="display: flex; flex-direction: column;">
                    <div v-for="(item, index) in zzhmList" :key="item.zzid">
                      <div style="display: flex; align-items: center;justify-content: space-between;">
                        <el-checkbox-group v-model="checkList" @change="zzlxxz">
                          <el-checkbox :label="item.zzid" style="width: 200px;">{{ item.fjlb
                            }}</el-checkbox></el-checkbox-group>
                        <div>证件号码:<el-input v-model="item.zjhm" style="width: 200px;" @blur="zzxx"
                            :disabled="!checkList.includes(index + 1)"></el-input></div>
                        <div>有效期:<el-date-picker v-model="item.yxq" type="date" placeholder="选择日期" style="width: 200px;"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" :disabled="!checkList.includes(index + 1)">
                          </el-date-picker></div>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </div>
              <div class="sec-form-left longLabel">
                <el-form-item label="本年度因私出国(境)次数">
                  <el-input placeholder="" v-model="tjlist.cs" clearable></el-input>
                </el-form-item>
                <el-form-item label="前往国家(地区)">
                  <el-input placeholder="" v-model="tjlist.qwgj" clearable></el-input>
                </el-form-item>
              </div>
              <div class="sec-form-left longLabel">
                <el-form-item label="起止日期">
                  <el-date-picker v-model="tjlist.value1" style="width:100%" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </div>
              <div class="sec-form-left longLabel">
                <el-form-item label="出国（境）事由">
                  <el-input placeholder="" v-model="tjlist.sy" clearable></el-input>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>
        <!-- 主要学习及工作经历end -->
        <!-- 家庭成员及主要社会关系情况start -->
        <p class="sec-title">同行人员情况</p>
        <el-table border class="sec-el-table" :data="ryglRyscJtcyList"
          :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="gx" label="与本人关系">
            <template slot-scope="scope">
              <el-input v-model="scope.row.gx" placeholder=""></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="xm" label="姓名">
            <template slot-scope="scope">
              <el-input v-model="scope.row.xm" placeholder=""></el-input>
            </template>
          </el-table-column>

          <el-table-column prop="nl" label="年龄">
            <template slot-scope="scope">
              <el-input v-model="scope.row.nl" placeholder="" @blur="scope.row.nl = $event.target.value" oninput="value=value.replace(/[^\d.]/g,'')"
                @input="handleInput"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="zzmm" label="政治面貌">
            <template slot-scope="scope">
              <el-select v-model="scope.row.zzmm" placeholder="请选择">
                <el-option v-for="item in zzmmoptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="zw" label="工作单位,职务及居住地">
            <template slot-scope="scope">
              <el-input v-model="scope.row.zw" placeholder=""></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="140">
            <template slot-scope="scope">
              <el-button v-if="scope.row.czbtn1 != ''" size="medium" type="text"
                @click="cyjshgxAddRow(ryglRyscJtcyList)">{{ scope.row.czbtn1 }}
              </el-button>
              <el-button v-if="scope.row.czbtn2 != ''" size="medium" type="text"
                @click="cyjshgxDelRow(scope.$index, ryglRyscJtcyList)">{{ scope.row.czbtn2 }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 下载start -->
        <p class="sec-title">下载</p>
        <div class="sec-form-third haveBorderTop">
          <div class="sec-left-text">
            <p>1.涉密人员出国（境）保密承诺书</p>
            <p>2.涉密人员出国（境）行前保密教育情况表</p>
            <p>3.涉密人员出国（境）回访记录表</p>
          </div>
          <el-button size="small" type="primary" @click="wdzlxz">下载</el-button>
        </div>
        <p class="sec-title">上传扫描件</p>
        <div class="sec-form-five haveBorderTop" style="position: relative;">
          <div class="sec-left-text">
            <div class="flex">
              1.上传涉密人员出国（境）保密承诺书扫描件
              <el-upload class="upload-demo" action="#" :http-request="httpRequest" :show-file-list="false">
                <el-button size="mini" type="primary">上传</el-button>
              </el-upload>
              <el-button style="margin-left: 10px;" v-show="ylth" size="mini" type="primary"
                @click="ylbmtxth">预览</el-button>
              <el-dialog :visible.sync="dialogVisible">
                <img :src="dialogImageUrl" alt="" style="width: 100%">
                <div slot="footer" class="dialog-footer">
                  <el-button size="small" @click="dialogVisible = false">取 消</el-button>
                </div>
              </el-dialog>
            </div>
            <div class="flex">
              2.上传涉密人员出国（境）行前保密教育情况表扫描件
              <el-upload class="upload-demo2" action="#" :http-request="httpBmcnsRequest" :show-file-list="false">
                <el-button size="mini" type="primary">上传</el-button>
              </el-upload>
              <el-button style="margin-left: 10px;" v-show="ylcn" size="mini" type="primary"
                @click="ylbmcns">预览</el-button>
              <el-dialog :visible.sync="dialogBmcnsVisible">
                <img :src="dialogBmcnsImageUrl" alt="" style="width: 100%">
                <div slot="footer" class="dialog-footer">
                  <el-button size="small" @click="dialogBmcnsVisible = false">取 消</el-button>
                </div>
              </el-dialog>
            </div>
            <div class="flex">
              3.上传涉密人员出国（境）回访记录表扫描件
              <el-upload class="upload-demo3" action="#" :http-request="httpWtsRequest" :show-file-list="false">
                <el-button size="mini" type="primary">上传</el-button>
              </el-upload>
              <el-button style="margin-left: 10px;" v-show="ylwt" size="mini" type="primary"
                @click="ylwts">预览</el-button>
              <el-dialog :visible.sync="dialogWtsVisible">
                <img :src="dialogWtsImageUrl" alt="" style="width: 100%">
                <div slot="footer" class="dialog-footer">
                  <el-button size="small" @click="dialogWtsVisible = false">取 消</el-button>
                </div>
              </el-dialog>
            </div>
          </div>
        </div>
        <!-- 下载end -->
        <!-- 底部操作按钮start -->
        <div class="sec-form-six haveBorderTop sec-footer">
          <el-button @click="returnIndex" class="fr ml10" plain>返回</el-button>
          <el-button @click="chooseApproval" class="fr" type="success">保存并提交</el-button>
          <!-- <el-button @click="saveAndSubmit" class="fr" type="success">保存并提交</el-button>
          <el-button @click="save" class="fr" type="primary">保存</el-button> -->
          <el-button @click="save" class="fr" type="primary">临时保存</el-button>
        </div>
        <!-- 底部操作按钮end -->

      </el-form>
    </div>
    <!-- 发起申请弹框start -->
    <el-dialog title="选择审批人" :close-on-click-modal="false" :visible.sync="approvalDialogVisible" width="40%"
      :destroy-on-close="true">
      <div class="dlFqsqContainer">
        <label for="">部门:</label>
        <el-cascader v-model="ryChoose.bm" :options="regionOption" :props="regionParams" filterable clearable
          ref="cascaderArr" @change="bmSelectChange"></el-cascader>
        <label for="">姓名:</label>
        <el-input class="input2" v-model="ryChoose.xm" clearable placeholder="姓名"></el-input>
        <el-button class="searchButton" type="primary" icon="el-icon-search" @click="searchRy">查询</el-button>
        <BaseTable class="baseTable" :tableHeight="'300'" :key="tableKey" :showIndex=true :tableData="ryDatas" :columns="applyColumns"
          :showSingleSelection="true" :handleColumn="handleColumnApply" :showPagination=true :currentPage="page"
          :pageSize="pageSize" :totalCount="total" @handleCurrentChange="handleCurrentChangeRy"
          @handleSizeChange="handleSizeChangeRy" @handleSelectionChange="handleSelectionChange">
        </BaseTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" class="fr ml10" @click="approvalDialogVisible = false">关 闭</el-button>
        <el-button @click="saveAndSubmit" class="fr" type="success">提交</el-button>
        <!-- <el-button @click="save" class="fr" type="primary">保存</el-button> -->
        <div style="clear:both"></div>
      </span>
    </el-dialog>
    <!-- 发起申请弹框end -->
  </div>
</template>
<script>
import {
  submitRyrysc,
  getLcSLid,
  updateRysc,
  updateSlzt,
  getZzjgList,
  getSpUserList,
  getFwdyidByFwlx,
  getLoginInfo,
  downloadCgspwdZip,

  deleteSlxxBySlid
} from '../../../api/index'
import {
  addRyglCgcj,
  updateRyglCgcj,
  selectRyglCgcjBySlId,
} from '../../../api/cgjsc'
import BaseTable from '../../components/common/baseTable.vue'
import AddLineTable from "../../components/common/addLineTable.vue";   //人工纠错组件
export default {
  components: {
    AddLineTable,
    BaseTable
  },
  props: {},
  data() {
    return {
      tableKey:1,
      checkList: [],
      loading: false,
      // 弹框人员选择条件
      ryChoose: {
        'bm': '',
        'xm': ''
      },
      regionOption: [], // 部门下拉
      page: 1, // 审批人弹框当前页
      pageSize: 10, // 审批人弹框每页条数
      radioIdSelect: '', // 审批人弹框人员单选
      ryDatas: [], // 弹框人员选择
      total: 0, // 弹框人员总数
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true
      }, //地域信息配置参数
      // table 行样式
      headerCellStyle: {
        background: '#EEF7FF',
        color: '#4D91F8'
      },
      // form表单提交数据
      tjlist: {
        smryid: '',
        xm: '',
        xb: '',
        gj: '中国',
        dwzwzc: '',
        yrsmgw: '',
        cym: '',
        mz: '',
        hyzk: '',
        zzmm: '',
        lxdh: '',
        sfzhm: '',
        hjdz: '',
        hjdgajg: '',
        czdz: '',
        czgajg: '',
        imageUrl: '',
        yjqk: '0', // 拥有外籍、境外永久居留权或者长期居留许可情况
        qscfqk: '0', // 配偶子女有关情况
        qtqk: '', // 其他需要说明的情况
        brcn: '',
        splx: '',
        bmcns: '',
        xqbmjyqkb: '',
        hfjlb: '',
        value1: [],
      },
      // 主要学习及工作经历
      ryglRyscScjlList: [{
        'qssj': '',
        'zzsj': '',
        'szdw': '',
        'zw': '',
        'zmr': '',
        'czbtn1': '增加行',
        'czbtn2': '',
      }],
      // 家庭成员及社会关系
      ryglRyscJtcyList: [{
        "gx": "",//关系描述
        "nl": "",//年龄
        "zzmm": "",//政治面貌
        "jwjlqk": '',//是否有外籍、境外居留权、长期居留许可
        "xm": "",//姓名
        "cgszd": "",//工作(学习)单位
        "zw": "",//职务
        'czbtn1': '增加行',
        'czbtn2': ''
      }],
      zzhmList: [
        {
          zzid: 1,
          fjlb: '因公护照',
          zjhm: '',
          yxq: '',
          checked: 0
        },
        {
          zzid: 2,
          fjlb: '因公港澳通行证',
          zjhm: '',
          yxq: '',
          checked: 0
        },
        {
          zzid: 3,
          fjlb: '因公台湾通行证',
          zjhm: '',
          yxq: '',
          checked: 0
        },
        {
          zzid: 4,
          fjlb: '因私护照',
          zjhm: '',
          yxq: '',
          checked: 0
        },
        {
          zzid: 5,
          fjlb: '因私港澳通行证',
          zjhm: '',
          yxq: '',
          checked: 0
        },
        {
          zzid: 6,
          fjlb: '因私台湾通行证',
          zjhm: '',
          yxq: '',
          checked: 0
        },
      ],
      // 因私出国(境)情况
      ryglRyscYccgList: [{
        "cggj": "",//出国国家
        "sy": "",//事由
        "zzsj": "",//终止时间
        "qssj": "",//起始时间
        // "bz": "",//备注
        'czbtn1': '增加行',
        'czbtn2': ''
      }],
      // 接受境外资助情况
      ryglRyscJwzzqkList: [{
        "zzsj": "",//时间
        "jgmc": "",//机构名称
        // "bz": "",//备注
        "zznr": "",//资助内容
        "gj": "",//国家
        'czbtn1': '增加行',
        'czbtn2': ''
      }],
      // 处分或者违法犯罪情况
      ryglRyscCfjlList: [{
        "cfdw": "",//处罚单位
        "cfsj": "",//处罚时间
        "cfjg": "",//处罚结果
        "cfyy": "",//处罚原因
        'czbtn1': '增加行',
        'czbtn2': '',
      }],
      // 持有因公出入境证件情况
      ryglRyscSwzjList: [{
        'zjmc': '护照',
        'fjlb': 1,
        'cyqk': '0',
        'zjhm': '',
        'yxq': ''
      }, {
        'zjmc': '港澳通行证',
        'fjlb': 2,
        'cyqk': '0',
        'zjhm': '',
        'yxq': ''
      }, {
        'zjmc': '台湾通行证',
        'fjlb': 3,
        'cyqk': '0',
        'zjhm': '',
        'yxq': ''
      }, {
        'zjmc': '护照',
        'fjlb': 4,
        'cyqk': '0',
        'zjhm': '',
        'yxq': ''
      }, {
        'zjmc': '港澳通行证',
        'fjlb': 5,
        'cyqk': '0',
        'zjhm': '',
        'yxq': ''
      }, {
        'zjmc': '台湾通行证',
        'fjlb': 6,
        'cyqk': '0',
        'zjhm': '',
        'yxq': ''
      }],
      ryInfo: {}, // 当前任用审查的人员信息
      // 政治面貌下拉选项
      zzmmoptions: [{
        value: '中央党员',
        label: '中央党员'
      }, {
        value: '团员',
        label: '团员'
      }, {
        value: '民主党派',
        label: '民主党派'
      }, {
        value: '群众',
        label: '群众'
      }],
      // 是否有外籍、境外居留权、长期居留许可
      ynoptions: [{
        value: '1',
        label: '是'
      }, {
        value: '0',
        label: '否'
      }],
      sltshow: '', // 文档的缩略图显示
      routeType: '', // 当前的类型（编辑或者首次新增）
      dialogImageUrl: '', // 预览本人承诺扫描件展示
      dialogVisible: false, // 预览本人承诺扫描件弹框显隐
      approvalDialogVisible: false, // 选择申请人弹框弹框显隐
      fileRow: '', // 本人承诺凭证的file数据
      // 选择审核人table
      applyColumns: [
        {
          name: '姓名',
          prop: 'xm',
          scopeType: 'text',
          formatter: false
        },
        {
          name: '部门',
          prop: 'bmmc',
          scopeType: 'text',
          formatter: false
        },
        {
          name: '岗位',
          prop: 'gwmc',
          scopeType: 'text',
          formatter: false
        }
      ],
      handleColumnApply: [],
      scqk: [
        {
          sfty: '同意',
          id: 1
        },
        {
          sfty: '不同意',
          id: 0
        },
      ],
      disabled2: false,
      sltshow: '', // 文档的缩略图显示
      sltbmcnsshow: '', // 文档的缩略图显示
      sltwtsshow: '', // 文档的缩略图显示
      dialogImageUrl: '',
      dialogVisible: false,
      dialogBmcnsImageUrl: '',
      dialogBmcnsVisible: false,
      dialogWtsImageUrl: '',
      dialogWtsVisible: false,
      fileRow: '',
      filebmcnsRow: '',
      filewtsRow: '',
      fileryxxRow: '',
      filexzglRow: '',
      ylth: false,
      ylcn: false,
      ylwt: false,
    }
  },
  computed: {
  },
  mounted() {
    this.onfwid()
    this.getOrganization()
    this.yhDatas = this.$route.query.datas
    this.ryInfo = this.$route.query.datas.ryglCgcj // 当type为update时，获取人员的基本信息
    this.routeType = this.$route.query.type // type : update/add  编辑时或者首次创建时
    console.log(this.yhDatas);
    console.log(this.ryInfo);
    console.log(this.routeType);
    let result = {}
    let iamgeBase64 = ''
    let iamgeBase64Brcn = ''
    if (this.$route.query.type == 'add') {
      // 首次发起申请
      result = { ...this.tjlist, ...this.$route.query.datas }
      iamgeBase64 = "data:image/jpeg;base64," + this.$route.query.datas.zp
    } else {
      // 保存 继续编辑
      result = { ...this.tjlist, ...this.$route.query.datas.ryglCgcj }
      console.log(result)
      iamgeBase64 = "data:image/jpeg;base64," + this.$route.query.datas.ryglCgcj.zp
      iamgeBase64Brcn = "data:image/jpeg;base64," + this.$route.query.datas.ryglCgcj.brcn
      if (typeof iamgeBase64Brcn === "string") {
        // 复制某条消息
        if (!iamgeBase64Brcn) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64Brcn)) {
          let that = this;
          function previwImg(item) {
            that.sltshow = item;
          }
          previwImg(iamgeBase64Brcn);
        }
      }
      console.log(this.$route.query.datas);
      // 家庭成员及主要社会关系情况
      if (this.$route.query.datas.ryglCgcjTxqkList.length == 0) {
        this.ryglRyscJtcyList = [{
          "gx": "",//关系描述
          "nl": "",//年龄
          "zzmm": "",//政治面貌
          "jwjlqk": '',//是否有外籍、境外居留权、长期居留许可
          "xm": "",//姓名
          "cgszd": "",//工作(学习)单位
          "zw": "",//职务
          'czbtn1': '增加行',
          'czbtn2': ''
        }]
      } else {
        this.ryglRyscJtcyList = this.$route.query.datas.ryglCgcjTxqkList.map((data) => {
          // if (data.jwjlqk == 0) {
          //   data.jwjlqk = '否'
          // } else if (data.jwjlqk == 1) {
          //   data.jwjlqk = '是'
          // }
          data.czbtn1 = '增加行'
          data.czbtn2 = '删除'
          return data
        })
      }
    }
    // 初始化各状态值显示其代表的中文名
    result.xingbie = result.xb == 2 ? '女' : result.xb == 1 ? '男' : ''
    result.hyzk = result.hyzk == 0 ? '未婚' : result.hyzk == 1 ? '已婚' : ''
    result.zzmmxx = result.zzmm == 1 ? '中共党员' : result.zzmm == 2 ? '团员' : result.zzmm == 3 ? '民主党派' : result.zzmm == 4 ? '群众' : ''
    result.jbzc = result.jbzc == 1 ? '省部级' : result.jbzc == 2 ? '厅局级' : result.jbzc == 3 ? '县处级' : result.jbzc == 4 ? '乡科级及以下' : result.jbzc == 5 ? '高级(含正高、副高)' : result.jbzc == 6 ? '中级' : result.jbzc == 7 ? '初级及以下' : result.jbzc == 8 ? '试用期人员' : result.jbzc == 9 ? '工勤人员' : result.jbzc == 10 ? '企业职员' : result.jbzc == 11 ? '其他' : ''
    let bmC = result.bmmc != '' ? '部门：' + result.bmmc + '、' : ''
    let zwC = result.zw != '' ? '职务：' + result.zw + '、' : ''
    result.bmzwzc = bmC + zwC + '职称：' + result.jbzc
    // result.gwmc = result.gwmc && result.gwmc.length === 1 ? result.gwmc.toString() : result.gwmc && result.gwmc.length > 1 ? result.gwmc.join('/') : ''
    result.smdjxx = result.smdj == 1 ? '核心' : result.smdj == 2 ? '重要' : result.smdj == 3 ? '一般' : ''
    this.tjlist = result
    if (this.tjlist.sfzhm == '' || this.tjlist.sfzhm == undefined) {
      this.tjlist.csny = result.csny
    } else {
      this.tjlist.csny = this.tjlist.sfzhm.substring(6, 10) + "-" + this.tjlist.sfzhm.substring(10, 12) + "-" + this.tjlist.sfzhm.substring(12, 14);
    }
    this.tjlist.yjqk = result.yjqk.toString()
    this.tjlist.qscfqk = result.qscfqk.toString()
    console.log(this.tjlist);
    console.log(result.splx);
    if (result.splx == 1 || result.splx == 0) {
      this.tjlist.splx = result.splx.toString()
    } else {
      console.log(123);
      this.tjlist.splx = ''
    }
    if (this.$route.query.datas.ryglCgcjSwzjList != undefined) {
      this.zzhmList = this.$route.query.datas.ryglCgcjSwzjList
    }
    this.zzhmList.forEach((item) => {
      if (item.checked == 1) {
        this.checkList.push(item.zzid)
      }
    })

    if (result.qssj != '' || result.qssj != undefined) {
      this.tjlist.value1 = []
      this.tjlist.value1.push(result.qssj);
      this.tjlist.value1.push(result.jssj);
    }
    console.log(this.tjlist);
    if (typeof iamgeBase64 === "string") {
      // 复制某条消息
      if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
      function validDataUrl(s) {
        return validDataUrl.regex.test(s);
      }
      validDataUrl.regex =
        /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
      if (validDataUrl(iamgeBase64)) {
        let that = this;
        function previwImg(item) {
          that.tjlist.imageUrl = item;
        }
        previwImg(iamgeBase64);
      }
    }
    if (this.tjlist.bmcns != '') {
      this.ylth = true
    }
    if (this.tjlist.xqbmjyqkb != '') {
      this.ylcn = true
    }
    if (this.tjlist.hfjlb != '') {
      this.ylwt = true
    }
  },
  methods: {
    handleInput(value) {
      if (value < 0 || value > 999) {
        this.$message.warning('请输入实际年龄！')
      }
    },
    zzlxxz() {
      console.log(this.checkList);
      console.log(this.zzhmList);
      this.zzhmList.forEach(item => {
        item.checked = 0
      })
      this.checkList.forEach((item, index) => {
        this.zzhmList.forEach((item1, index1) => {
          if (item == item1.zzid) {
            item1.checked = 1
          }
        })
      })
      this.zzhmList.forEach(item => {
        if (item.checked == 0) {
          item.zjhm = ''
          item.yxq = ''
        }
      })
      console.log(this.zzhmList);
    },
    zzxx() {
      console.log(this.checkList);
    },
    chRadio() { },
    // blob格式转base64
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    handleSelectionChange(index, row) {
      this.radioIdSelect = row
    },
    zpzm(zp) {
      const iamgeBase64 = "data:image/jpeg;base64," + zp;
      let zpxx
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          // let that = this;

          function previwImg(item) {
            zpxx = item;
          }
          previwImg(iamgeBase64);
        }
      }
      return zpxx
    },
    // 上传保密谈话记录表凭证
    httpRequest(data) {
      this.sltshow = URL.createObjectURL(data.file);
      this.fileRow = data.file
      this.blobToBase64(data.file, (dataurl) => {
        this.tjlist.bmcns = dataurl.split(',')[1]
        console.log(this.tjlist.bmcns);
        if (this.tjlist.bmcns != '') {
          this.ylth = true
        }
      });
    },
    // 预览
    ylbmtxth() {
      let zpxx
      console.log(this.routeType)
      if (this.routeType == 'add') {
        this.dialogImageUrl = URL.createObjectURL(this.fileRow)
      } else {
        zpxx = this.zpzm(this.tjlist.bmcns)
        this.dialogImageUrl = zpxx
      }
      this.dialogVisible = true
    },
    // 上传离岗离职涉密人员保密承诺书凭证
    httpBmcnsRequest(data) {
      this.sltbmcnsshow = URL.createObjectURL(data.file);
      this.filebmcnsRow = data.file
      this.blobToBase64(data.file, (dataurl) => {
        this.tjlist.xqbmjyqkb = dataurl.split(',')[1]
        console.log(this.tjlist.xqbmjyqkb);
        if (this.tjlist.xqbmjyqkb != '') {
          this.ylcn = true
        }
      });
    },
    // 预览
    ylbmcns() {
      let zpxx
      if (this.routeType == 'add') {
        this.dialogBmcnsImageUrl = URL.createObjectURL(this.filebmcnsRow)
      } else {
        zpxx = this.zpzm(this.tjlist.xqbmjyqkb)
        this.dialogBmcnsImageUrl = zpxx
      }
      this.dialogBmcnsVisible = true
    },
    // 上传脱密期委托管理书凭证
    httpWtsRequest(data) {
      this.sltwtsshow = URL.createObjectURL(data.file);
      this.filewtsRow = data.file
      this.blobToBase64(data.file, (dataurl) => {
        this.tjlist.hfjlb = dataurl.split(',')[1]
        console.log(this.tjlist.hfjlb);
        if (this.tjlist.hfjlb != '') {
          this.ylwt = true
        }
      });
    },
    // 预览
    ylwts() {
      let zpxx
      console.log(this.routeType)
      if (this.routeType == 'add') {
        this.dialogWtsImageUrl = URL.createObjectURL(this.filewtsRow)
      } else {
        zpxx = this.zpzm(this.tjlist.hfjlb)
        this.dialogWtsImageUrl = zpxx
      }
      this.dialogWtsVisible = true
    },
    // 主要学习及工作经历增加行
    addRow(data) {
      data.push({
        'qssj': '',
        'zzsj': '',
        'szdw': '',
        'zw': '',
        'zmr': '',
        'czbtn1': '增加行',
        'czbtn2': '删除',
      })
    },
    // 主要学习及工作经历删除行
    delRow(index, rows) {
      rows.splice(index, 1)
    },
    // 家庭成员及主要社会关系情况 添加行
    cyjshgxAddRow(data) {
      data.push({
        'ybrgx': '',
        'xm': '',
        'sfywjjwjlqcqjlxk': '',
        'dw': '',
        'zw': '',
        'zzmm': '',
        'czbtn1': '增加行',
        'czbtn2': '删除'
      })
    },
    // 家庭成员及主要社会关系情况 删除行
    cyjshgxDelRow(index, rows) {
      rows.splice(index, 1)
    },
    // 因私出国(境)情况 添加行
    yscgqkAddRow(data) {
      data.push({
        'qsrq': '',
        'zzrq': '',
        'jsnsdgjhdq': '',
        'sy': '',
        'czbtn1': '增加行',
        'czbtn2': '删除'
      })
    },
    // 因私出国(境)情况 删除行
    yscgqkDelRow(index, rows) {
      rows.splice(index, 1)
    },
    // 接受境外资助情况 添加行
    jsjwzzqkAddRow(data) {
      data.push({
        'qsrq': '',
        'gjdq': '',
        'jgmc': '',
        'zznr': '',
        'czbtn1': '增加行',
        'czbtn2': '删除'
      })
    },
    // 接受境外资助情况 删除行
    jsjwzzqkDelRow(index, rows) {
      rows.splice(index, 1)
    },
    // 处分或者违法犯罪情况 添加行
    clhwffzqkAddRow(data) {
      data.push({
        'qsrq': '',
        'cljg': '',
        'clyy': '',
        'cljg': '',
        'czbtn1': '增加行',
        'czbtn2': '删除'
      })
    },
    async wdzlxz() {
      var returnData = await downloadCgspwdZip();
      var date = new Date()
      var sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, '出国出境' + '-' + sj + ".zip");
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    // 处分或者违法犯罪情况 删除行
    clhwffzqkDelRow(index, rows) {
      rows.splice(index, 1)
    },
    // 预览
    yulan() {
      console.log(this.routeType)
      if (this.routeType == 'add') {
        this.dialogImageUrl = URL.createObjectURL(this.fileRow)
      } else {
        this.dialogImageUrl = this.sltshow
      }
      this.dialogVisible = true
    },
    // 删除
    shanchu() {
      this.tjlist.brcn = ''
      this.sltshow = ''
    },
    async onfwid() {
      let params = {
        fwlx: 27
      }
      let data = await getFwdyidByFwlx(params)
      console.log(data);
      this.fwdyid = data.data.fwdyid
    },
    jxfa() {
      if (this.tjlist.splx == '') {
        this.$message.error('请选择审批类型')
        return true
      }
      if (this.checkList.length == 0) {
        this.$message.error('请选择证件')
        return true
      }
      if (this.tjlist.cs == '' || this.tjlist.cs == undefined) {
        this.$message.error('请输入本年度因私出国(境)次数')
        return true
      }
      if (this.tjlist.qwgj == '' || this.tjlist.qwgj == undefined) {
        this.$message.error('请输入前往国家(地区)')
        return true
      }
      if (this.tjlist.value1 == [] || this.tjlist.value1 == undefined) {
        this.$message.error('请输入起止日期')
        return true
      }
      let txry = false
      this.ryglRyscJtcyList.forEach(item => {
        if (item.gx == '' || item.gx == undefined) {
          this.$message.error('请输入与本人关系')
          txry = true
          return
        }
        if (item.xm == '' || item.xm == undefined) {
          this.$message.error('请输入同行人姓名')
          txry = true
          return
        }
        if (item.nl == '' || item.nl == undefined) {
          this.$message.error('请输入同行人年龄')
          txry = true
          return
        }
        if (item.zzmm == '' || item.zzmm == undefined) {
          this.$message.error('请输入同行人政治面貌')
          txry = true
          return
        }
        if (item.zw == '' || item.zw == undefined) {
          this.$message.error('请输入同行人工作单位,职务及居住地')
          txry = true
          return
        }
      })
      if (txry) {
        return true
      }
      this.checkList.forEach((item, index) => {
        this.zzhmList.forEach((item1, index1) => {
          if (item == item1.zzid) {
            item1.checked = 1
          }
        })
      })
      let pd
      this.zzhmList.forEach(item => {
        if (item.checked == 1) {
          if (item.zjhm == '') {
            this.$message.error('请填写' + item.fjlb + '的证件号码')
            pd = true
            return
          }
          if (item.yxq == '') {
            this.$message.error('请填写' + item.fjlb + '的有限期')
            pd = true
            return
          }
        }
      })
      if (pd) {
        return true
      }
    },
    // 保存
    async save() {
      let param = {
        'fwdyid': this.fwdyid,
        'lcslclzt': 3
      }
      if (this.jxfa()) {
        return
      }
      this.tjlist.qssj = this.tjlist.value1[0]
      this.tjlist.jssj = this.tjlist.value1[1]
      // this.ryglRyscJtcyList.forEach((e) => {
      //   if (e.jwjlqk == '否') {
      //     e.jwjlqk = 0
      //   } else if (e.jwjlqk == '是') {
      //     e.jwjlqk = 1
      //   }
      // })
      if (this.routeType == 'update') {
        this.tjlist.dwid = this.ryInfo.dwid
        this.tjlist.lcslid = this.ryInfo.lcslid
        let params = {
          'ryglCgcj': this.tjlist,
          'ryglCgcjSwzjList': this.zzhmList,
          'ryglCgcjTxqkList': this.ryglRyscJtcyList,
        }
        let resDatas = await updateRyglCgcj(params)
        if (resDatas.code == 10000) {
          this.$router.push('/cgjsc')
          this.$message({
            message: '保存成功',
            type: 'success'
          })
        }
      } else {
        param.smryid = this.yhDatas.smryid
        this.tjlist.dwid = this.yhDatas.dwid
        let res = await getLcSLid(param)
        if (res.code == 10000) {
          this.tjlist.lcslid = res.data.slid
          let params = {
            'ryglCgcj': this.tjlist,
            'ryglCgcjSwzjList': this.zzhmList,
            'ryglCgcjTxqkList': this.ryglRyscJtcyList,
          }
          let resDatas = await addRyglCgcj(params)
          if (resDatas.code == 10000) {
            this.$router.push('/cgjsc')
            this.$message({
              message: '保存成功',
              type: 'success'
            })
          } else {
            deleteSlxxBySlid({ slid: res.data.slid })
          }
        }
      }
    },
    //全部组织机构List
    async getOrganization() {
      let zzjgList = await getZzjgList()
      this.zzjgmc = zzjgList
      let shu = []
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            childrenRegionVo.push(item1)
            item.childrenRegionVo = childrenRegionVo
          }
        });
        shu.push(item)
      })
      let shuList = []
      let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    handleSelectionChange1(index, row) {
      this.radioIdSelect = row
    },
    handleCurrentChangeRy(val) {
      this.page = val
      this.chooseApproval()
    },
    //列表分页--更改每页显示个数
    handleSizeChangeRy(val) {
      this.page = 1
      this.pageSize = val
      this.chooseApproval()
    },
    // 人员搜索
    searchRy() {
      this.tableKey++
      this.chooseApproval()
    },
    // 发起申请选择人员 人员下拉
    bmSelectChange(item) {
      if (item != undefined) {
        this.ryChoose.bm = item.join('/')
      }
    },
    // 选择审批人
    async chooseApproval() {
      // this.getOrganization()
      this.approvalDialogVisible = true
      let param = {
        'page': this.page,
        'pageSize': this.pageSize,
        'fwdyid': this.fwdyid,
        'bmmc': this.ryChoose.bm,
        'xm': this.ryChoose.xm
      }
      let resData = await getSpUserList(param)
      if (resData.records) {
        // this.loading = false
        this.ryDatas = resData.records
        this.total = resData.total
      } else {
        this.$message.error('数据获取失败！')
      }
    },
    // 保存并提交
    async saveAndSubmit() {
      if (this.jxfa()) {
        return
      }
      this.tjlist.qssj = this.tjlist.value1[0]
      this.tjlist.jssj = this.tjlist.value1[1]
      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {
        let param = {
          'fwdyid': this.fwdyid
        }
        if (this.routeType == 'update') {
          param.lcslclzt = 2
          param.smryid = this.ryInfo.smryid
          param.slid = this.ryInfo.lcslid
          param.clrid = this.radioIdSelect.yhid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.dwid = this.ryInfo.dwid
            this.tjlist.lcslid = this.ryInfo.lcslid
            let params = {
              'ryglCgcj': this.tjlist,
              'ryglCgcjSwzjList': this.zzhmList,
              'ryglCgcjTxqkList': this.ryglRyscJtcyList,
            }
            let resDatas = await updateRyglCgcj(params)
            if (resDatas.code == 10000) {
              let paramStatus = {
                'fwdyid': this.fwdyid,
                'slid': this.tjlist.lcslid
              }
              let resStatus = await updateSlzt(paramStatus)
              if (resStatus.code == 10000) {
                this.$router.push('/cgjsc')
                this.$message({
                  message: '保存并提交成功',
                  type: 'success'
                })
              }
            }
          }
        } else {
          param.lcslclzt = 0
          param.clrid = this.radioIdSelect.yhid
          param.smryid = this.yhDatas.smryid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.dwid = this.yhDatas.dwid
            this.tjlist.lcslid = res.data.slid
            let params = {
              'ryglCgcj': this.tjlist,
              'ryglCgcjSwzjList': this.zzhmList,
              'ryglCgcjTxqkList': this.ryglRyscJtcyList,
            }
            let resDatas = await addRyglCgcj(params)
            if (resDatas.code == 10000) {
              this.$router.push('/cgjsc')
              this.$message({
                message: '保存并提交成功',
                type: 'success'
              })
            } else {
              deleteSlxxBySlid({ slid: res.data.slid })
            }
          }
        }
      } else {
        this.$message({
          message: '请选择审批人',
          type: 'warning'
        })
      }
    },
    // 返回
    returnIndex() {
      this.$router.push('/cgjsc')
    }
  },
  watch: {

  }
}

</script>

<style scoped>
.sec-container {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: overlay;
}

.sec-container>>>.el-input.is-disabled .el-input__inner {
  color: #000000;
}

.sec-title {
  border-left: 5px solid #1b72d8;
  color: #1b72d8;
  font-size: 20px;
  font-weight: 700;
  text-indent: 10px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.sec-form-container {
  width: 100%;
  height: 100%;
}

.sec-form-left {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  /* height: 40px; */
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
}

.sec-form-left:not(:first-child) {
  border-top: 0;
}

.sec-form-left .el-form-item {
  float: left;
  width: 100%;
}

.sec-header-section {
  width: 100%;
  position: relative;
}

.sec-header-pic {
  width: 258px;
  position: absolute;
  right: 0px;
  top: 0;
  height: 245px;
  border: 1px solid #CDD2D9;
  border-left: 0;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sec-form-second {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
  border-top: 0;
}

.sec-form-third {
  border: 1px solid #CDD2D9;
  /* height: 40px;  */
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-four {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-five {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  overflow: hidden;
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.yulan {
  text-align: center;
  cursor: pointer;
  color: #3874D5;
  font-weight: 600;
  float: left;
  margin-left: 10px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 2px solid #EBEBEB;
}

.sec-form-six {
  border: 1px solid #CDD2D9;
  overflow: hidden;
  background: #ffffff;
  padding: 10px;
}

.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.upload-demo {
  margin-left: 114px;
}

.upload-demo2 {
  margin-left: 50px;
}

.upload-demo3 {
  margin-left: 114px;
}

.ml10 {
  margin-left: 10px;
}

.sec-footer {
  margin-top: 10px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
  border: none;
}

.sec-left-text {
  float: left;
  margin-right: 130px;
}

.haveBorderTop {
  border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
  width: 500px !important;
}

>>>.longLabel .el-form-item__content {
  margin-left: 500px !important;
  padding-left: 20px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

/* .sec-form-second:not(:first-child){
  border-top: 0;
} */
.sec-form-second .el-form-item {
  float: left;
  width: 100%;
}

.sec-el-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
  padding-left: 15px;
  background-color: #F5F7FA;
  width: calc(100% - 16px);
  border-right: 1px solid #CDD2D9;
  color: #000000;
}

>>>.sec-el-table .el-input__inner {
  border: none !important;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
  width: 200px;
  text-align: center;
  font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
  border: none;
  border-right: 1px solid #CDD2D9;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item {
  margin-bottom: 0px;
}

/* >>>.el-form > div {
  border: 1px solid #CDD2D9;;
} */
>>>.el-form-item__label {
  border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
} */

.widthw {
  width: 6vw;
}

.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}

>>>.widthzz .el-form-item__label {
  height: 270px;
  line-height: 270px;
}

>>>.widthzz .el-input__inner {
  border-right: none;
}
</style>
