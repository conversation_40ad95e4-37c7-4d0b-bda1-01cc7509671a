{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/cgjscTable.vue", "webpack:///./src/renderer/view/rcgz/cgjscTable.vue?1a02", "webpack:///./src/renderer/view/rcgz/cgjscTable.vue"], "names": ["cgjscTable", "components", "AddLineTable", "BaseTable", "baseTable", "props", "data", "_ref", "table<PERSON><PERSON>", "checkList", "loading", "ry<PERSON><PERSON>ose", "bm", "xm", "regionOption", "page", "pageSize", "radioIdSelect", "ryDatas", "total", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "headerCellStyle", "background", "color", "tjlist", "sm<PERSON><PERSON>", "xb", "gj", "dwzwzc", "yrsmgw", "cym", "mz", "hyzk", "zzmm", "lxdh", "sfzhm", "hjdz", "hjdgajg", "czdz", "czgajg", "imageUrl", "yjqk", "qscfqk", "qtqk", "brcn", "splx", "bmcns", "xqbmjyqkb", "hfjlb", "value1", "ryglRyscScjlList", "qssj", "zzsj", "szdw", "zw", "zmr", "czbtn1", "czbtn2", "ryglRyscJtcyList", "gx", "nl", "jwjlqk", "cgszd", "zzhmList", "zzid", "fjlb", "zjhm", "yxq", "checked", "ryglRyscYccgList", "cggj", "sy", "ryglRyscJwzzqkList", "jgmc", "zznr", "ryglRyscCfjlList", "cfdw", "cfsj", "cfjg", "cfyy", "ryglRyscSwzjList", "zjmc", "cyqk", "ryInfo", "zzmmoptions", "ynoptions", "sltshow", "routeType", "dialogImageUrl", "dialogVisible", "approvalDialogVisible", "fileRow", "applyColumns", "name", "prop", "scopeType", "formatter", "handleColumnApply", "scqk", "sfty", "id", "disabled2", "defineProperty_default", "computed", "mounted", "_this", "this", "onfwid", "getOrganization", "yhDatas", "$route", "query", "datas", "ryglCgcj", "type", "console", "log", "item", "result", "iamgeBase64", "iamgeBase64Brcn", "extends_default", "zp", "_validDataUrl", "s", "regex", "test", "ryglCgcjTxqkList", "length", "map", "<PERSON><PERSON><PERSON>", "zzmmxx", "jbzc", "bmC", "bmmc", "zwC", "bmzwzc", "smdjxx", "smdj", "undefined", "csny", "substring", "toString", "ryglCgcjSwzjList", "for<PERSON>ach", "push", "jssj", "_validDataUrl2", "_that", "_previwImg", "ylth", "ylcn", "ylwt", "methods", "handleInput", "$message", "warning", "zzlxxz", "_this2", "index", "item1", "index1", "zzxx", "chRadio", "blobToBase64", "blob", "callback", "fileReader", "FileReader", "onload", "e", "target", "readAsDataURL", "handleSelectionChange", "row", "zpzm", "zpxx", "_validDataUrl3", "httpRequest", "_this3", "URL", "createObjectURL", "file", "dataurl", "split", "ylbmtxth", "httpBmcnsRequest", "_this4", "sltbmcnsshow", "filebmcnsRow", "ylbmcns", "dialogBmcnsImageUrl", "dialogBmcnsVisible", "httpWtsRequest", "_this5", "sltwtsshow", "filewtsRow", "ylwts", "dialogWtsImageUrl", "dialogWtsVisible", "addRow", "delRow", "rows", "splice", "cyjshgxAddRow", "ybrgx", "sfywjjwjlqcqjlxk", "dw", "cyjshgxDelRow", "yscgqkAddRow", "qsrq", "zzrq", "jsnsdgjhdq", "yscgqkDelRow", "jsjwzzqkAddRow", "gjdq", "jsjwzzqkDelRow", "clhwffzqkAddRow", "_data$push", "cljg", "clyy", "wdzlxz", "_this6", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "returnData", "date", "sj", "wrap", "_context", "prev", "next", "Object", "api", "sent", "Date", "getFullYear", "getMonth", "getDate", "dom_download", "stop", "content", "fileName", "Blob", "url", "window", "dom", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "clhwffzqkDelRow", "yulan", "shanchu", "_this7", "_callee2", "params", "_context2", "fwlx", "fwdyid", "jxfa", "_this8", "error", "cs", "qwgj", "txry", "pd", "save", "_this9", "_callee3", "param", "res", "_params", "_context3", "lcslclzt", "abrupt", "dwid", "lcslid", "cgjsc", "code", "$router", "message", "slid", "_this10", "_callee4", "zzjgList", "shu", "shuList", "list", "_context4", "zzjgmc", "childrenRegionVo", "bmm", "fbmm", "handleSelectionChange1", "handleCurrentChangeRy", "val", "chooseApproval", "handleSizeChangeRy", "searchRy", "bmSelectChange", "join", "_this11", "_callee5", "resData", "_context5", "records", "saveAndSubmit", "_this12", "_callee6", "paramStatus", "_res", "_params2", "_context6", "keys_default", "clrid", "yhid", "returnIndex", "watch", "rcgz_cgjscTable", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "_v", "ref", "attrs", "model", "label-width", "placeholder", "clearable", "disabled", "$$v", "$set", "scopedSlots", "_u", "key", "fn", "scope", "staticStyle", "flex-direction", "_l", "align-items", "justify-content", "on", "change", "width", "_s", "includes", "blur", "format", "value-format", "range-separator", "start-placeholder", "end-placeholder", "border", "header-cell-style", "stripe", "align", "oninput", "$event", "input", "size", "_e", "$index", "position", "action", "http-request", "show-file-list", "margin-left", "visible", "update:visible", "src", "alt", "slot", "plain", "title", "close-on-click-modal", "destroy-on-close", "for", "options", "filterable", "icon", "tableHeight", "showIndex", "tableData", "columns", "showSingleSelection", "handleColumn", "showPagination", "currentPage", "totalCount", "handleCurrentChange", "handleSizeChange", "clear", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "wPA8QAA,GACAC,YACAC,uBAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAMA,IAAAC,EACA,OAAAA,GACAC,SAAA,EACAC,aACAC,SAAA,EAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,gBACAC,KAAA,EACAC,SAAA,GACAC,cAAA,GACAC,WACAC,MAAA,EACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,iBACAC,WAAA,UACAC,MAAA,WAGAC,QACAC,OAAA,GACAjB,GAAA,GACAkB,GAAA,GACAC,GAAA,KACAC,OAAA,GACAC,OAAA,GACAC,IAAA,GACAC,GAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,QAAA,GACAC,KAAA,GACAC,OAAA,GACAC,SAAA,GACAC,KAAA,IACAC,OAAA,IACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,UAAA,GACAC,MAAA,GACAC,WAGAC,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,KAGAC,mBACAC,GAAA,GACAC,GAAA,GACA3B,KAAA,GACA4B,OAAA,GACArD,GAAA,GACAsD,MAAA,GACAR,GAAA,GACAE,OAAA,MACAC,OAAA,KAEAM,WAEAC,KAAA,EACAC,KAAA,OACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,UACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,UACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,OACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,UACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,UACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAIAC,mBACAC,KAAA,GACAC,GAAA,GACAnB,KAAA,GACAD,KAAA,GAEAK,OAAA,MACAC,OAAA,KAGAe,qBACApB,KAAA,GACAqB,KAAA,GAEAC,KAAA,GACA/C,GAAA,GACA6B,OAAA,MACAC,OAAA,KAGAkB,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAvB,OAAA,MACAC,OAAA,KAGAuB,mBACAC,KAAA,KACAhB,KAAA,EACAiB,KAAA,IACAhB,KAAA,GACAC,IAAA,KAEAc,KAAA,QACAhB,KAAA,EACAiB,KAAA,IACAhB,KAAA,GACAC,IAAA,KAEAc,KAAA,QACAhB,KAAA,EACAiB,KAAA,IACAhB,KAAA,GACAC,IAAA,KAEAc,KAAA,KACAhB,KAAA,EACAiB,KAAA,IACAhB,KAAA,GACAC,IAAA,KAEAc,KAAA,QACAhB,KAAA,EACAiB,KAAA,IACAhB,KAAA,GACAC,IAAA,KAEAc,KAAA,QACAhB,KAAA,EACAiB,KAAA,IACAhB,KAAA,GACAC,IAAA,KAEAgB,UAEAC,cACAnE,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAEAC,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAGAqE,YACApE,MAAA,IACAD,MAAA,MAEAC,MAAA,IACAD,MAAA,MAEAsE,QAAA,GACAC,UAAA,GACAC,eAAA,GACAC,eAAA,EACAC,uBAAA,EACAC,QAAA,GAEAC,eAEAC,KAAA,KACAC,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAC,qBACAC,OAEAC,KAAA,KACAC,GAAA,IAGAD,KAAA,MACAC,GAAA,IAGAC,WAAA,GA1PAC,IAAApG,EAAA,UA2PA,IA3PAoG,IAAApG,EAAA,eA4PA,IA5PAoG,IAAApG,EAAA,aA6PA,IA7PAoG,IAAApG,EAAA,iBA8PA,IA9PAoG,IAAApG,EAAA,iBA+PA,GA/PAoG,IAAApG,EAAA,sBAgQA,IAhQAoG,IAAApG,EAAA,sBAiQA,GAjQAoG,IAAApG,EAAA,oBAkQA,IAlQAoG,IAAApG,EAAA,oBAmQA,GAnQAoG,IAAApG,EAAA,UAoQA,IApQAoG,IAAApG,EAAA,eAqQA,IArQAoG,IAAApG,EAAA,aAsQA,IAtQAoG,IAAApG,EAAA,cAuQA,IAvQAoG,IAAApG,EAAA,cAwQA,IAxQAoG,IAAApG,EAAA,QAyQA,GAzQAoG,IAAApG,EAAA,QA0QA,GA1QAoG,IAAApG,EAAA,QA2QA,GA3QAA,GA8QAqG,YAEAC,QAvRA,WAuRA,IAAAC,EAAAC,KACAA,KAAAC,SACAD,KAAAE,kBACAF,KAAAG,QAAAH,KAAAI,OAAAC,MAAAC,MACAN,KAAAvB,OAAAuB,KAAAI,OAAAC,MAAAC,MAAAC,SACAP,KAAAnB,UAAAmB,KAAAI,OAAAC,MAAAG,KACAC,QAAAC,IAAAV,KAAAG,SACAM,QAAAC,IAAAV,KAAAvB,QACAgC,QAAAC,IAAAV,KAAAnB,WACA,IAuBA8B,EAvBAC,KACAC,EAAA,GACAC,EAAA,GACA,UAAAd,KAAAI,OAAAC,MAAAG,KAEAI,EAAeG,OAAff,KAAAlF,OAAAkF,KAAAI,OAAAC,MAAAC,OACAO,EAAA,0BAAAb,KAAAI,OAAAC,MAAAC,MAAAU,OACA,CAMA,GAJAJ,EAAeG,OAAff,KAAAlF,OAAAkF,KAAAI,OAAAC,MAAAC,MAAAC,UACAE,QAAAC,IAAAE,GACAC,EAAA,0BAAAb,KAAAI,OAAAC,MAAAC,MAAAC,SAAAS,GAEA,iBADAF,EAAA,0BAAAd,KAAAI,OAAAC,MAAAC,MAAAC,SAAArE,MACA,KAGA+E,EAAA,SAAAA,EAAAC,GACA,OAAAD,EAAAE,MAAAC,KAAAF,IAFA,IAAAJ,EAAA,OAMA,GAFAG,EAAAE,MACA,6GACAF,EAAAH,GAAA,CAEAH,EAGAG,EAJAd,KAEApB,QAAA+B,GAKAF,QAAAC,IAAAV,KAAAI,OAAAC,MAAAC,OAEA,GAAAN,KAAAI,OAAAC,MAAAC,MAAAe,iBAAAC,OACAtB,KAAAhD,mBACAC,GAAA,GACAC,GAAA,GACA3B,KAAA,GACA4B,OAAA,GACArD,GAAA,GACAsD,MAAA,GACAR,GAAA,GACAE,OAAA,MACAC,OAAA,KAGAiD,KAAAhD,iBAAAgD,KAAAI,OAAAC,MAAAC,MAAAe,iBAAAE,IAAA,SAAAhI,GAQA,OAFAA,EAAAuD,OAAA,MACAvD,EAAAwD,OAAA,KACAxD,IAKAqH,EAAAY,QAAA,GAAAZ,EAAA5F,GAAA,OAAA4F,EAAA5F,GAAA,OACA4F,EAAAtF,KAAA,GAAAsF,EAAAtF,KAAA,QAAAsF,EAAAtF,KAAA,QACAsF,EAAAa,OAAA,GAAAb,EAAArF,KAAA,UAAAqF,EAAArF,KAAA,QAAAqF,EAAArF,KAAA,UAAAqF,EAAArF,KAAA,QACAqF,EAAAc,KAAA,GAAAd,EAAAc,KAAA,SAAAd,EAAAc,KAAA,SAAAd,EAAAc,KAAA,SAAAd,EAAAc,KAAA,YAAAd,EAAAc,KAAA,gBAAAd,EAAAc,KAAA,QAAAd,EAAAc,KAAA,WAAAd,EAAAc,KAAA,WAAAd,EAAAc,KAAA,WAAAd,EAAAc,KAAA,WAAAd,EAAAc,KAAA,QACA,IAAAC,EAAA,IAAAf,EAAAgB,KAAA,MAAAhB,EAAAgB,KAAA,OACAC,EAAA,IAAAjB,EAAAhE,GAAA,MAAAgE,EAAAhE,GAAA,OAmCA,GAlCAgE,EAAAkB,OAAAH,EAAAE,EAAA,MAAAjB,EAAAc,KAEAd,EAAAmB,OAAA,GAAAnB,EAAAoB,KAAA,QAAApB,EAAAoB,KAAA,QAAApB,EAAAoB,KAAA,QACAhC,KAAAlF,OAAA8F,EACA,IAAAZ,KAAAlF,OAAAW,YAAAwG,GAAAjC,KAAAlF,OAAAW,MACAuE,KAAAlF,OAAAoH,KAAAtB,EAAAsB,KAEAlC,KAAAlF,OAAAoH,KAAAlC,KAAAlF,OAAAW,MAAA0G,UAAA,UAAAnC,KAAAlF,OAAAW,MAAA0G,UAAA,WAAAnC,KAAAlF,OAAAW,MAAA0G,UAAA,OAEAnC,KAAAlF,OAAAiB,KAAA6E,EAAA7E,KAAAqG,WACApC,KAAAlF,OAAAkB,OAAA4E,EAAA5E,OAAAoG,WACA3B,QAAAC,IAAAV,KAAAlF,QACA2F,QAAAC,IAAAE,EAAAzE,MACA,GAAAyE,EAAAzE,MAAA,GAAAyE,EAAAzE,KACA6D,KAAAlF,OAAAqB,KAAAyE,EAAAzE,KAAAiG,YAEA3B,QAAAC,IAAA,KACAV,KAAAlF,OAAAqB,KAAA,SAEA8F,GAAAjC,KAAAI,OAAAC,MAAAC,MAAA+B,mBACArC,KAAA3C,SAAA2C,KAAAI,OAAAC,MAAAC,MAAA+B,kBAEArC,KAAA3C,SAAAiF,QAAA,SAAA3B,GACA,GAAAA,EAAAjD,SACAqC,EAAArG,UAAA6I,KAAA5B,EAAArD,QAIA,IAAAsD,EAAAnE,WAAAwF,GAAArB,EAAAnE,OACAuD,KAAAlF,OAAAyB,UACAyD,KAAAlF,OAAAyB,OAAAgG,KAAA3B,EAAAnE,MACAuD,KAAAlF,OAAAyB,OAAAgG,KAAA3B,EAAA4B,OAEA/B,QAAAC,IAAAV,KAAAlF,QACA,iBAAA+F,EAAA,KAGA4B,EAAA,SAAAA,EAAAvB,GACA,OAAAuB,EAAAtB,MAAAC,KAAAF,IAFA,IAAAL,EAAA,OAMA,GAFA4B,EAAAtB,MACA,6GACAsB,EAAA5B,GAAA,KACA6B,EAAA1C,MACA,SAAAW,GACA+B,EAAA5H,OAAAgB,SAAA6E,EAEAgC,CAAA9B,IAGA,IAAAb,KAAAlF,OAAAsB,QACA4D,KAAA4C,MAAA,GAEA,IAAA5C,KAAAlF,OAAAuB,YACA2D,KAAA6C,MAAA,GAEA,IAAA7C,KAAAlF,OAAAwB,QACA0D,KAAA8C,MAAA,IAGAC,SACAC,YADA,SACAzI,IACAA,EAAA,GAAAA,EAAA,MACAyF,KAAAiD,SAAAC,QAAA,aAGAC,OANA,WAMA,IAAAC,EAAApD,KACAS,QAAAC,IAAAV,KAAAtG,WACA+G,QAAAC,IAAAV,KAAA3C,UACA2C,KAAA3C,SAAAiF,QAAA,SAAA3B,GACAA,EAAAjD,QAAA,IAEAsC,KAAAtG,UAAA4I,QAAA,SAAA3B,EAAA0C,GACAD,EAAA/F,SAAAiF,QAAA,SAAAgB,EAAAC,GACA5C,GAAA2C,EAAAhG,OACAgG,EAAA5F,QAAA,OAIAsC,KAAA3C,SAAAiF,QAAA,SAAA3B,GACA,GAAAA,EAAAjD,UACAiD,EAAAnD,KAAA,GACAmD,EAAAlD,IAAA,MAGAgD,QAAAC,IAAAV,KAAA3C,WAEAmG,KA3BA,WA4BA/C,QAAAC,IAAAV,KAAAtG,YAEA+J,QA9BA,aAgCAC,aAhCA,SAgCAC,EAAAC,GACA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAA,SAAAC,GACAJ,EAAAI,EAAAC,OAAArD,SAEAiD,EAAAK,cAAAP,IAEAQ,sBAvCA,SAuCAd,EAAAe,GACApE,KAAA9F,cAAAkK,GAEAC,KA1CA,SA0CArD,GACA,IAAAH,EAAA,0BAAAG,EACAsD,OAAA,EACA,oBAAAzD,EAAA,KAGA0D,EAAA,SAAAA,EAAArD,GACA,OAAAqD,EAAApD,MAAAC,KAAAF,IAFA,IAAAL,EAAA,OAMA,GAFA0D,EAAApD,MACA,6GACAoD,EAAA1D,GAAA,CAKAyD,EAEAzD,GAGA,OAAAyD,GAGAE,YAlEA,SAkEAjL,GAAA,IAAAkL,EAAAzE,KACAA,KAAApB,QAAA8F,IAAAC,gBAAApL,EAAAqL,MACA5E,KAAAf,QAAA1F,EAAAqL,KACA5E,KAAA0D,aAAAnK,EAAAqL,KAAA,SAAAC,GACAJ,EAAA3J,OAAAsB,MAAAyI,EAAAC,MAAA,QACArE,QAAAC,IAAA+D,EAAA3J,OAAAsB,OACA,IAAAqI,EAAA3J,OAAAsB,QACAqI,EAAA7B,MAAA,MAKAmC,SA9EA,WA+EA,IAAAT,OAAA,EACA7D,QAAAC,IAAAV,KAAAnB,WACA,OAAAmB,KAAAnB,UACAmB,KAAAlB,eAAA4F,IAAAC,gBAAA3E,KAAAf,UAEAqF,EAAAtE,KAAAqE,KAAArE,KAAAlF,OAAAsB,OACA4D,KAAAlB,eAAAwF,GAEAtE,KAAAjB,eAAA,GAGAiG,iBA1FA,SA0FAzL,GAAA,IAAA0L,EAAAjF,KACAA,KAAAkF,aAAAR,IAAAC,gBAAApL,EAAAqL,MACA5E,KAAAmF,aAAA5L,EAAAqL,KACA5E,KAAA0D,aAAAnK,EAAAqL,KAAA,SAAAC,GACAI,EAAAnK,OAAAuB,UAAAwI,EAAAC,MAAA,QACArE,QAAAC,IAAAuE,EAAAnK,OAAAuB,WACA,IAAA4I,EAAAnK,OAAAuB,YACA4I,EAAApC,MAAA,MAKAuC,QAtGA,WAuGA,IAAAd,OAAA,EACA,OAAAtE,KAAAnB,UACAmB,KAAAqF,oBAAAX,IAAAC,gBAAA3E,KAAAmF,eAEAb,EAAAtE,KAAAqE,KAAArE,KAAAlF,OAAAuB,WACA2D,KAAAqF,oBAAAf,GAEAtE,KAAAsF,oBAAA,GAGAC,eAjHA,SAiHAhM,GAAA,IAAAiM,EAAAxF,KACAA,KAAAyF,WAAAf,IAAAC,gBAAApL,EAAAqL,MACA5E,KAAA0F,WAAAnM,EAAAqL,KACA5E,KAAA0D,aAAAnK,EAAAqL,KAAA,SAAAC,GACAW,EAAA1K,OAAAwB,MAAAuI,EAAAC,MAAA,QACArE,QAAAC,IAAA8E,EAAA1K,OAAAwB,OACA,IAAAkJ,EAAA1K,OAAAwB,QACAkJ,EAAA1C,MAAA,MAKA6C,MA7HA,WA8HA,IAAArB,OAAA,EACA7D,QAAAC,IAAAV,KAAAnB,WACA,OAAAmB,KAAAnB,UACAmB,KAAA4F,kBAAAlB,IAAAC,gBAAA3E,KAAA0F,aAEApB,EAAAtE,KAAAqE,KAAArE,KAAAlF,OAAAwB,OACA0D,KAAA4F,kBAAAtB,GAEAtE,KAAA6F,kBAAA,GAGAC,OAzIA,SAyIAvM,GACAA,EAAAgJ,MACA9F,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,QAIAgJ,OArJA,SAqJA1C,EAAA2C,GACAA,EAAAC,OAAA5C,EAAA,IAGA6C,cAzJA,SAyJA3M,GACAA,EAAAgJ,MACA4D,MAAA,GACArM,GAAA,GACAsM,iBAAA,GACAC,GAAA,GACAzJ,GAAA,GACArB,KAAA,GACAuB,OAAA,MACAC,OAAA,QAIAuJ,cAtKA,SAsKAjD,EAAA2C,GACAA,EAAAC,OAAA5C,EAAA,IAGAkD,aA1KA,SA0KAhN,GACAA,EAAAgJ,MACAiE,KAAA,GACAC,KAAA,GACAC,WAAA,GACA7I,GAAA,GACAf,OAAA,MACAC,OAAA,QAIA4J,aArLA,SAqLAtD,EAAA2C,GACAA,EAAAC,OAAA5C,EAAA,IAGAuD,eAzLA,SAyLArN,GACAA,EAAAgJ,MACAiE,KAAA,GACAK,KAAA,GACA9I,KAAA,GACAC,KAAA,GACAlB,OAAA,MACAC,OAAA,QAIA+J,eApMA,SAoMAzD,EAAA2C,GACAA,EAAAC,OAAA5C,EAAA,IAGA0D,gBAxMA,SAwMAxN,GAAA,IAAAyN,EACAzN,EAAAgJ,MAAAyE,GACAR,KAAA,GACAS,KAAA,GACAC,KAAA,IAHAtH,IAAAoH,EAAA,OAIA,IAJApH,IAAAoH,EAKA,gBALApH,IAAAoH,EAMA,eANAA,KASAG,OAlNA,WAkNA,IAAAC,EAAApH,KAAA,OAAAqH,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAN,EAAAC,EAAAM,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAP,EADAI,EAAAK,KAEAR,EAAA,IAAAS,KACAR,EAAAD,EAAAU,cAAA,IAAAV,EAAAW,WAAA,GAAAX,EAAAY,UACAnB,EAAAoB,aAAAd,EAAA,QAAAE,EAAA,QAJA,wBAAAE,EAAAW,SAAAhB,EAAAL,KAAAC,IAOAmB,aAzNA,SAyNAE,EAAAC,GACA,IAAAhF,EAAA,IAAAiF,MAAAF,IACAG,EAAAC,OAAApE,IAAAC,gBAAAhB,GACAoF,EAAAC,SAAAC,cAAA,KACAF,EAAAG,MAAAC,QAAA,OACAJ,EAAAK,KAAAP,EACAE,EAAAM,aAAA,WAAAV,GACAK,SAAAM,KAAAC,YAAAR,GACAA,EAAAS,SAGAC,gBApOA,SAoOApG,EAAA2C,GACAA,EAAAC,OAAA5C,EAAA,IAGAqG,MAxOA,WAyOAjJ,QAAAC,IAAAV,KAAAnB,WACA,OAAAmB,KAAAnB,UACAmB,KAAAlB,eAAA4F,IAAAC,gBAAA3E,KAAAf,SAEAe,KAAAlB,eAAAkB,KAAApB,QAEAoB,KAAAjB,eAAA,GAGA4K,QAlPA,WAmPA3J,KAAAlF,OAAAoB,KAAA,GACA8D,KAAApB,QAAA,IAEAqB,OAtPA,WAsPA,IAAA2J,EAAA5J,KAAA,OAAAqH,IAAAC,EAAAC,EAAAC,KAAA,SAAAqC,IAAA,IAAAC,EAAAvQ,EAAA,OAAA+N,EAAAC,EAAAM,KAAA,SAAAkC,GAAA,cAAAA,EAAAhC,KAAAgC,EAAA/B,MAAA,cACA8B,GACAE,KAAA,IAFAD,EAAA/B,KAAA,EAIAC,OAAAC,EAAA,EAAAD,CAAA6B,GAJA,OAIAvQ,EAJAwQ,EAAA5B,KAKA1H,QAAAC,IAAAnH,GACAqQ,EAAAK,OAAA1Q,OAAA0Q,OANA,wBAAAF,EAAAtB,SAAAoB,EAAAD,KAAAvC,IAQA6C,KA9PA,WA8PA,IAAAC,EAAAnK,KACA,OAAAA,KAAAlF,OAAAqB,KAEA,OADA6D,KAAAiD,SAAAmH,MAAA,YACA,EAEA,MAAApK,KAAAtG,UAAA4H,OAEA,OADAtB,KAAAiD,SAAAmH,MAAA,UACA,EAEA,OAAApK,KAAAlF,OAAAuP,SAAApI,GAAAjC,KAAAlF,OAAAuP,GAEA,OADArK,KAAAiD,SAAAmH,MAAA,oBACA,EAEA,OAAApK,KAAAlF,OAAAwP,WAAArI,GAAAjC,KAAAlF,OAAAwP,KAEA,OADAtK,KAAAiD,SAAAmH,MAAA,gBACA,EAEA,GAAApK,KAAAlF,OAAAyB,iBAAA0F,GAAAjC,KAAAlF,OAAAyB,OAEA,OADAyD,KAAAiD,SAAAmH,MAAA,YACA,EAEA,IAAAG,GAAA,EA4BA,GA3BAvK,KAAAhD,iBAAAsF,QAAA,SAAA3B,GACA,UAAAA,EAAA1D,SAAAgF,GAAAtB,EAAA1D,IACAkN,EAAAlH,SAAAmH,MAAA,iBACAG,GAAA,IAGA,IAAA5J,EAAA7G,SAAAmI,GAAAtB,EAAA7G,IACAqQ,EAAAlH,SAAAmH,MAAA,iBACAG,GAAA,IAGA,IAAA5J,EAAAzD,SAAA+E,GAAAtB,EAAAzD,IACAiN,EAAAlH,SAAAmH,MAAA,iBACAG,GAAA,IAGA,IAAA5J,EAAApF,WAAA0G,GAAAtB,EAAApF,MACA4O,EAAAlH,SAAAmH,MAAA,mBACAG,GAAA,IAGA,IAAA5J,EAAA/D,SAAAqF,GAAAtB,EAAA/D,IACAuN,EAAAlH,SAAAmH,MAAA,0BACAG,GAAA,SAFA,IAMAA,EACA,SAEAvK,KAAAtG,UAAA4I,QAAA,SAAA3B,EAAA0C,GACA8G,EAAA9M,SAAAiF,QAAA,SAAAgB,EAAAC,GACA5C,GAAA2C,EAAAhG,OACAgG,EAAA5F,QAAA,OAIA,IAAA8M,OAAA,EAeA,OAdAxK,KAAA3C,SAAAiF,QAAA,SAAA3B,GACA,MAAAA,EAAAjD,QAAA,CACA,OAAAiD,EAAAnD,KAGA,OAFA2M,EAAAlH,SAAAmH,MAAA,MAAAzJ,EAAApD,KAAA,cACAiN,GAAA,GAGA,OAAA7J,EAAAlD,IAGA,OAFA0M,EAAAlH,SAAAmH,MAAA,MAAAzJ,EAAApD,KAAA,aACAiN,GAAA,QAKAA,QAAA,GAKAC,KA7UA,WA6UA,IAAAC,EAAA1K,KAAA,OAAAqH,IAAAC,EAAAC,EAAAC,KAAA,SAAAmD,IAAA,IAAAC,EAAAd,EAAAe,EAAAC,EAAA,OAAAxD,EAAAC,EAAAM,KAAA,SAAAkD,GAAA,cAAAA,EAAAhD,KAAAgD,EAAA/C,MAAA,UACA4C,GACAX,OAAAS,EAAAT,OACAe,SAAA,IAEAN,EAAAR,OALA,CAAAa,EAAA/C,KAAA,eAAA+C,EAAAE,OAAA,oBAQAP,EAAA5P,OAAA2B,KAAAiO,EAAA5P,OAAAyB,OAAA,GACAmO,EAAA5P,OAAA0H,KAAAkI,EAAA5P,OAAAyB,OAAA,GAQA,UAAAmO,EAAA7L,UAjBA,CAAAkM,EAAA/C,KAAA,gBAkBA0C,EAAA5P,OAAAoQ,KAAAR,EAAAjM,OAAAyM,KACAR,EAAA5P,OAAAqQ,OAAAT,EAAAjM,OAAA0M,OACArB,GACAvJ,SAAAmK,EAAA5P,OACAuH,iBAAAqI,EAAArN,SACAgE,iBAAAqJ,EAAA1N,kBAvBA+N,EAAA/C,KAAA,GAyBAC,OAAAmD,EAAA,EAAAnD,CAAA6B,GAzBA,QA0BA,KA1BAiB,EAAA5C,KA0BAkD,OACAX,EAAAY,QAAA/I,KAAA,UACAmI,EAAAzH,UACAsI,QAAA,OACA/K,KAAA,aA9BAuK,EAAA/C,KAAA,wBAkCA4C,EAAA7P,OAAA2P,EAAAvK,QAAApF,OACA2P,EAAA5P,OAAAoQ,KAAAR,EAAAvK,QAAA+K,KAnCAH,EAAA/C,KAAA,GAoCAC,OAAAC,EAAA,EAAAD,CAAA2C,GApCA,WAqCA,MADAC,EApCAE,EAAA5C,MAqCAkD,KArCA,CAAAN,EAAA/C,KAAA,gBAsCA0C,EAAA5P,OAAAqQ,OAAAN,EAAAtR,KAAAiS,KACAV,GACAvK,SAAAmK,EAAA5P,OACAuH,iBAAAqI,EAAArN,SACAgE,iBAAAqJ,EAAA1N,kBA1CA+N,EAAA/C,KAAA,GA4CAC,OAAAmD,EAAA,EAAAnD,CAAA6C,GA5CA,QA6CA,KA7CAC,EAAA5C,KA6CAkD,MACAX,EAAAY,QAAA/I,KAAA,UACAmI,EAAAzH,UACAsI,QAAA,OACA/K,KAAA,aAGAyH,OAAAC,EAAA,EAAAD,EAAAuD,KAAAX,EAAAtR,KAAAiS,OApDA,yBAAAT,EAAAtC,SAAAkC,EAAAD,KAAArD,IA0DAnH,gBAvYA,WAuYA,IAAAuL,EAAAzL,KAAA,OAAAqH,IAAAC,EAAAC,EAAAC,KAAA,SAAAkE,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAxE,EAAAC,EAAAM,KAAA,SAAAkE,GAAA,cAAAA,EAAAhE,KAAAgE,EAAA/D,MAAA,cAAA+D,EAAA/D,KAAA,EACAC,OAAAC,EAAA,IAAAD,GADA,cACA0D,EADAI,EAAA5D,KAEAsD,EAAAO,OAAAL,EACAC,KACAH,EAAAO,OAAA1J,QAAA,SAAA3B,GACA,IAAAsL,KACAR,EAAAO,OAAA1J,QAAA,SAAAgB,GACA3C,EAAAuL,KAAA5I,EAAA6I,OACAF,EAAA1J,KAAAe,GACA3C,EAAAsL,sBAGAL,EAAArJ,KAAA5B,KAEAkL,KAdAE,EAAA/D,KAAA,EAeAC,OAAAC,EAAA,EAAAD,GAfA,OAgBA,KADA6D,EAfAC,EAAA5D,MAgBAgE,MACAP,EAAAtJ,QAAA,SAAA3B,GACA,IAAAA,EAAAwL,MACAN,EAAAtJ,KAAA5B,KAIA,IAAAmL,EAAAK,MACAP,EAAAtJ,QAAA,SAAA3B,GACAF,QAAAC,IAAAC,GACAA,EAAAwL,MAAAL,EAAAK,MACAN,EAAAtJ,KAAA5B,KAIAkL,EAAA,GAAAI,iBAAA3J,QAAA,SAAA3B,GACA8K,EAAA1R,aAAAwI,KAAA5B,KAhCA,yBAAAoL,EAAAtD,SAAAiD,EAAAD,KAAApE,IAmCA+E,uBA1aA,SA0aA/I,EAAAe,GACApE,KAAA9F,cAAAkK,GAEAiI,sBA7aA,SA6aAC,GACAtM,KAAAhG,KAAAsS,EACAtM,KAAAuM,kBAGAC,mBAlbA,SAkbAF,GACAtM,KAAAhG,KAAA,EACAgG,KAAA/F,SAAAqS,EACAtM,KAAAuM,kBAGAE,SAxbA,WAybAzM,KAAAvG,WACAuG,KAAAuM,kBAGAG,eA7bA,SA6bA/L,QACAsB,GAAAtB,IACAX,KAAApG,SAAAC,GAAA8G,EAAAgM,KAAA,OAIAJ,eAncA,WAmcA,IAAAK,EAAA5M,KAAA,OAAAqH,IAAAC,EAAAC,EAAAC,KAAA,SAAAqF,IAAA,IAAAjC,EAAAkC,EAAA,OAAAxF,EAAAC,EAAAM,KAAA,SAAAkF,GAAA,cAAAA,EAAAhF,KAAAgF,EAAA/E,MAAA,cAEA4E,EAAA5N,uBAAA,EACA4L,GACA5Q,KAAA4S,EAAA5S,KACAC,SAAA2S,EAAA3S,SACAgQ,OAAA2C,EAAA3C,OACArI,KAAAgL,EAAAhT,SAAAC,GACAC,GAAA8S,EAAAhT,SAAAE,IARAiT,EAAA/E,KAAA,EAUAC,OAAAC,EAAA,GAAAD,CAAA2C,GAVA,QAUAkC,EAVAC,EAAA5E,MAWA6E,SAEAJ,EAAAzS,QAAA2S,EAAAE,QACAJ,EAAAxS,MAAA0S,EAAA1S,OAEAwS,EAAA3J,SAAAmH,MAAA,WAhBA,wBAAA2C,EAAAtE,SAAAoE,EAAAD,KAAAvF,IAoBA4F,cAvdA,WAudA,IAAAC,EAAAlN,KAAA,OAAAqH,IAAAC,EAAAC,EAAAC,KAAA,SAAA2F,IAAA,IAAAvC,EAAAd,EAAAsD,EAAAC,EAAAC,EAAA,OAAAhG,EAAAC,EAAAM,KAAA,SAAA0F,GAAA,cAAAA,EAAAxF,KAAAwF,EAAAvF,MAAA,WACAkF,EAAAhD,OADA,CAAAqD,EAAAvF,KAAA,eAAAuF,EAAAtC,OAAA,oBAIAiC,EAAApS,OAAA2B,KAAAyQ,EAAApS,OAAAyB,OAAA,GACA2Q,EAAApS,OAAA0H,KAAA0K,EAAApS,OAAAyB,OAAA,KACA,IAAA2Q,EAAAhT,eAAAsT,IAAAN,EAAAhT,eAAAoH,OAAA,GANA,CAAAiM,EAAAvF,KAAA,YAOA4C,GACAX,OAAAiD,EAAAjD,QAEA,UAAAiD,EAAArO,UAVA,CAAA0O,EAAAvF,KAAA,gBAWA4C,EAAAI,SAAA,EACAJ,EAAA7P,OAAAmS,EAAAzO,OAAA1D,OACA6P,EAAAY,KAAA0B,EAAAzO,OAAA0M,OACAP,EAAA6C,MAAAP,EAAAhT,cAAAwT,KAdAH,EAAAvF,KAAA,GAeAC,OAAAC,EAAA,EAAAD,CAAA2C,GAfA,WAgBA,KAhBA2C,EAAApF,KAgBAkD,KAhBA,CAAAkC,EAAAvF,KAAA,gBAiBAkF,EAAApS,OAAAoQ,KAAAgC,EAAAzO,OAAAyM,KACAgC,EAAApS,OAAAqQ,OAAA+B,EAAAzO,OAAA0M,OACArB,GACAvJ,SAAA2M,EAAApS,OACAuH,iBAAA6K,EAAA7P,SACAgE,iBAAA6L,EAAAlQ,kBAtBAuQ,EAAAvF,KAAA,GAwBAC,OAAAmD,EAAA,EAAAnD,CAAA6B,GAxBA,WAyBA,KAzBAyD,EAAApF,KAyBAkD,KAzBA,CAAAkC,EAAAvF,KAAA,gBA0BAoF,GACAnD,OAAAiD,EAAAjD,OACAuB,KAAA0B,EAAApS,OAAAqQ,QA5BAoC,EAAAvF,KAAA,GA8BAC,OAAAC,EAAA,IAAAD,CAAAmF,GA9BA,QA+BA,KA/BAG,EAAApF,KA+BAkD,OACA6B,EAAA5B,QAAA/I,KAAA,UACA2K,EAAAjK,UACAsI,QAAA,UACA/K,KAAA,aAnCA,QAAA+M,EAAAvF,KAAA,wBAyCA4C,EAAAI,SAAA,EACAJ,EAAA6C,MAAAP,EAAAhT,cAAAwT,KACA9C,EAAA7P,OAAAmS,EAAA/M,QAAApF,OA3CAwS,EAAAvF,KAAA,GA4CAC,OAAAC,EAAA,EAAAD,CAAA2C,GA5CA,WA6CA,MADAyC,EA5CAE,EAAApF,MA6CAkD,KA7CA,CAAAkC,EAAAvF,KAAA,gBA8CAkF,EAAApS,OAAAoQ,KAAAgC,EAAA/M,QAAA+K,KACAgC,EAAApS,OAAAqQ,OAAAkC,EAAA9T,KAAAiS,KACA8B,GACA/M,SAAA2M,EAAApS,OACAuH,iBAAA6K,EAAA7P,SACAgE,iBAAA6L,EAAAlQ,kBAnDAuQ,EAAAvF,KAAA,GAqDAC,OAAAmD,EAAA,EAAAnD,CAAAqF,GArDA,QAsDA,KAtDAC,EAAApF,KAsDAkD,MACA6B,EAAA5B,QAAA/I,KAAA,UACA2K,EAAAjK,UACAsI,QAAA,UACA/K,KAAA,aAGAyH,OAAAC,EAAA,EAAAD,EAAAuD,KAAA6B,EAAA9T,KAAAiS,OA7DA,QAAA+B,EAAAvF,KAAA,iBAkEAkF,EAAAjK,UACAsI,QAAA,SACA/K,KAAA,YApEA,yBAAA+M,EAAA9E,SAAA0E,EAAAD,KAAA7F,IAyEAsG,YAhiBA,WAiiBA3N,KAAAsL,QAAA/I,KAAA,YAGAqL,UC1sCeC,GADEC,OAFjB,WAA0B,IAAAC,EAAA/N,KAAagO,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAajP,KAAA,UAAAkP,QAAA,YAAA9T,MAAAwT,EAAA,QAAAO,WAAA,YAA4EC,YAAA,kBAA8BL,EAAA,KAAUK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,uBAAiCL,EAAA,WAAgBO,IAAA,WAAAC,OAAsBC,MAAAZ,EAAAjT,OAAA8T,cAAA,WAA0CV,EAAA,OAAYK,YAAA,uBAAiCL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOpU,MAAA,QAAc4T,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQpU,MAAAwT,EAAAjT,OAAA,GAAA8I,SAAA,SAAAoL,GAA+CjB,EAAAkB,KAAAlB,EAAAjT,OAAA,KAAAkU,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOpU,MAAA,MAAa4U,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQpU,MAAAwT,EAAAjT,OAAA,QAAA8I,SAAA,SAAAoL,GAAoDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,UAAAkU,IAAqCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAOpU,MAAA,WAAiB4T,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQpU,MAAAwT,EAAAjT,OAAA,KAAA8I,SAAA,SAAAoL,GAAiDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,OAAAkU,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOpU,MAAA,UAAgB4T,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQpU,MAAAwT,EAAAjT,OAAA,OAAA8I,SAAA,SAAAoL,GAAmDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,SAAAkU,IAAoCV,WAAA,oBAA6B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOpU,MAAA,UAAgB4T,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQpU,MAAAwT,EAAAjT,OAAA,KAAA8I,SAAA,SAAAoL,GAAiDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,OAAAkU,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOpU,MAAA,QAAe4U,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQpU,MAAAwT,EAAAjT,OAAA,OAAA8I,SAAA,SAAAoL,GAAmDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,SAAAkU,IAAoCV,WAAA,2BAAoC,GAAAP,EAAAS,GAAA,KAAAN,EAAA,OAA4BK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOpU,MAAA,WAAkB4U,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQpU,MAAAwT,EAAAjT,OAAA,OAAA8I,SAAA,SAAAoL,GAAmDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,SAAAkU,IAAoCV,WAAA,0BAAoCP,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAOpU,MAAA,UAAgB4T,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQpU,MAAAwT,EAAAjT,OAAA,MAAA8I,SAAA,SAAAoL,GAAkDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,QAAAkU,IAAmCV,WAAA,mBAA4B,SAAAP,EAAAS,GAAA,KAAAN,EAAA,KAAgCK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,uBAAiCL,EAAA,WAAgBO,IAAA,WAAAC,OAAsBC,MAAAZ,EAAAjT,OAAA8T,cAAA,WAA0CV,EAAA,OAAYK,YAAA,uBAAiCL,EAAA,OAAYK,YAAA,4BAAsCL,EAAA,gBAAqBQ,OAAOpU,MAAA,YAAkB4T,EAAA,YAAiBQ,OAAOpU,MAAA,KAAYqU,OAAQpU,MAAAwT,EAAAjT,OAAA,KAAA8I,SAAA,SAAAoL,GAAiDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,OAAAkU,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,eAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAmDQ,OAAOpU,MAAA,KAAYqU,OAAQpU,MAAAwT,EAAAjT,OAAA,KAAA8I,SAAA,SAAAoL,GAAiDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,OAAAkU,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,qBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAoDK,YAAA,oCAA8CL,EAAA,gBAAqBQ,OAAOpU,MAAA,UAAgB4T,EAAA,OAAYqB,aAAapG,QAAA,OAAAqG,iBAAA,WAA4CzB,EAAA0B,GAAA1B,EAAA,kBAAApN,EAAA0C,GAA4C,OAAA6K,EAAA,OAAiBkB,IAAAzO,EAAArD,OAAc4Q,EAAA,OAAYqB,aAAapG,QAAA,OAAAuG,cAAA,SAAAC,kBAAA,mBAA2EzB,EAAA,qBAA0B0B,IAAIC,OAAA9B,EAAA5K,QAAoBwL,OAAQpU,MAAAwT,EAAA,UAAAnK,SAAA,SAAAoL,GAA+CjB,EAAArU,UAAAsV,GAAkBV,WAAA,eAAyBJ,EAAA,eAAoBqB,aAAaO,MAAA,SAAgBpB,OAAQpU,MAAAqG,EAAArD,QAAmByQ,EAAAS,GAAAT,EAAAgC,GAAApP,EAAApD,UAAA,GAAAwQ,EAAAS,GAAA,KAAAN,EAAA,OAAAH,EAAAS,GAAA,SAAAN,EAAA,YAAuFqB,aAAaO,MAAA,SAAgBpB,OAAQK,UAAAhB,EAAArU,UAAAsW,SAAA3M,EAAA,IAA8CuM,IAAKK,KAAAlC,EAAAvK,MAAgBmL,OAAQpU,MAAAoG,EAAA,KAAAiD,SAAA,SAAAoL,GAA2CjB,EAAAkB,KAAAtO,EAAA,OAAAqO,IAA4BV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAAH,EAAAS,GAAA,QAAAN,EAAA,kBAAgEqB,aAAaO,MAAA,SAAgBpB,OAAQlO,KAAA,OAAAqO,YAAA,OAAAqB,OAAA,aAAAC,eAAA,aAAApB,UAAAhB,EAAArU,UAAAsW,SAAA3M,EAAA,IAAmIsL,OAAQpU,MAAAoG,EAAA,IAAAiD,SAAA,SAAAoL,GAA0CjB,EAAAkB,KAAAtO,EAAA,MAAAqO,IAA2BV,WAAA,eAAwB,WAAY,SAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAiCK,YAAA,4BAAsCL,EAAA,gBAAqBQ,OAAOpU,MAAA,kBAAwB4T,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,IAAgCH,OAAQpU,MAAAwT,EAAAjT,OAAA,GAAA8I,SAAA,SAAAoL,GAA+CjB,EAAAkB,KAAAlB,EAAAjT,OAAA,KAAAkU,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOpU,MAAA,cAAoB4T,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,IAAgCH,OAAQpU,MAAAwT,EAAAjT,OAAA,KAAA8I,SAAA,SAAAoL,GAAiDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,OAAAkU,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,4BAAsCL,EAAA,gBAAqBQ,OAAOpU,MAAA,UAAgB4T,EAAA,kBAAuBqB,aAAaO,MAAA,QAAepB,OAAQlO,KAAA,YAAA4P,kBAAA,IAAAC,oBAAA,OAAAC,kBAAA,OAAAJ,OAAA,aAAAC,eAAA,cAA+IxB,OAAQpU,MAAAwT,EAAAjT,OAAA,OAAA8I,SAAA,SAAAoL,GAAmDjB,EAAAkB,KAAAlB,EAAAjT,OAAA,SAAAkU,IAAoCV,WAAA,oBAA6B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,4BAAsCL,EAAA,gBAAqBQ,OAAOpU,MAAA,aAAmB4T,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,IAAgCH,OAAQpU,MAAAwT,EAAAjT,OAAA,GAAA8I,SAAA,SAAAoL,GAA+CjB,EAAAkB,KAAAlB,EAAAjT,OAAA,KAAAkU,IAAgCV,WAAA,gBAAyB,eAAAP,EAAAS,GAAA,KAAAN,EAAA,KAAsCK,YAAA,cAAwBR,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAgDK,YAAA,eAAAG,OAAkC6B,OAAA,GAAAhX,KAAAwU,EAAA/Q,iBAAAwT,qBAA6D5V,WAAA,UAAAC,MAAA,WAA0C4V,OAAA,MAAcvC,EAAA,mBAAwBQ,OAAOlO,KAAA,QAAAsP,MAAA,KAAAxV,MAAA,KAAAoW,MAAA,YAA2D3C,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOtP,KAAA,KAAA9E,MAAA,SAA4B4U,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQpU,MAAA+U,EAAAlL,IAAA,GAAAR,SAAA,SAAAoL,GAA8CjB,EAAAkB,KAAAK,EAAAlL,IAAA,KAAA4K,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOtP,KAAA,KAAA9E,MAAA,MAAyB4U,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQpU,MAAA+U,EAAAlL,IAAA,GAAAR,SAAA,SAAAoL,GAA8CjB,EAAAkB,KAAAK,EAAAlL,IAAA,KAAA4K,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOtP,KAAA,KAAA9E,MAAA,MAAyB4U,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAA8B,QAAA,sCAAgEf,IAAKK,KAAA,SAAAW,GAAwBtB,EAAAlL,IAAAlH,GAAA0T,EAAA3M,OAAA1J,OAAmCsW,MAAA9C,EAAA/K,aAAyB2L,OAAQpU,MAAA+U,EAAAlL,IAAA,GAAAR,SAAA,SAAAoL,GAA8CjB,EAAAkB,KAAAK,EAAAlL,IAAA,KAAA4K,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOtP,KAAA,OAAA9E,MAAA,QAA6B4U,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,aAAwBQ,OAAOG,YAAA,OAAoBF,OAAQpU,MAAA+U,EAAAlL,IAAA,KAAAR,SAAA,SAAAoL,GAAgDjB,EAAAkB,KAAAK,EAAAlL,IAAA,OAAA4K,IAAiCV,WAAA,mBAA8BP,EAAA0B,GAAA1B,EAAA,qBAAApN,GAAyC,OAAAuN,EAAA,aAAuBkB,IAAAzO,EAAApG,MAAAmU,OAAsBpU,MAAAqG,EAAArG,MAAAC,MAAAoG,EAAApG,WAAyC,UAAUwT,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOtP,KAAA,KAAA9E,MAAA,eAAkC4U,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQpU,MAAA+U,EAAAlL,IAAA,GAAAR,SAAA,SAAAoL,GAA8CjB,EAAAkB,KAAAK,EAAAlL,IAAA,KAAA4K,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOpU,MAAA,KAAAwV,MAAA,OAA2BZ,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,WAAAA,EAAAlL,IAAAtH,OAAAoR,EAAA,aAAiDQ,OAAOoC,KAAA,SAAAtQ,KAAA,QAA8BoP,IAAKpG,MAAA,SAAAoH,GAAyB,OAAA7C,EAAA7H,cAAA6H,EAAA/Q,sBAAiD+Q,EAAAS,GAAAT,EAAAgC,GAAAT,EAAAlL,IAAAtH,QAAA,oBAAAiR,EAAAgD,KAAAhD,EAAAS,GAAA,SAAAc,EAAAlL,IAAArH,OAAAmR,EAAA,aAAoHQ,OAAOoC,KAAA,SAAAtQ,KAAA,QAA8BoP,IAAKpG,MAAA,SAAAoH,GAAyB,OAAA7C,EAAAzH,cAAAgJ,EAAA0B,OAAAjD,EAAA/Q,sBAA+D+Q,EAAAS,GAAAT,EAAAgC,GAAAT,EAAAlL,IAAArH,QAAA,oBAAAgR,EAAAgD,aAAoE,GAAAhD,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCK,YAAA,iCAA2CL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,KAAAH,EAAAS,GAAA,sBAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,0BAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,wBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA2KQ,OAAOoC,KAAA,QAAAtQ,KAAA,WAAgCoP,IAAKpG,MAAAuE,EAAA5G,UAAoB4G,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAyCK,YAAA,cAAwBR,EAAAS,GAAA,WAAAT,EAAAS,GAAA,KAAAN,EAAA,OAA0CK,YAAA,8BAAAgB,aAAuD0B,SAAA,cAAuB/C,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,OAAYK,YAAA,SAAmBR,EAAAS,GAAA,qDAAAN,EAAA,aAA8EK,YAAA,cAAAG,OAAiCwC,OAAA,IAAAC,eAAApD,EAAAvJ,YAAA4M,kBAAA,KAAoElD,EAAA,aAAkBQ,OAAOoC,KAAA,OAAAtQ,KAAA,aAAgCuN,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAiDE,aAAajP,KAAA,OAAAkP,QAAA,SAAA9T,MAAAwT,EAAA,KAAAO,WAAA,SAAgEiB,aAAe8B,cAAA,QAAqB3C,OAAQoC,KAAA,OAAAtQ,KAAA,WAA+BoP,IAAKpG,MAAAuE,EAAAhJ,YAAsBgJ,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CQ,OAAO4C,QAAAvD,EAAAhP,eAA4B6Q,IAAK2B,iBAAA,SAAAX,GAAkC7C,EAAAhP,cAAA6R,MAA2B1C,EAAA,OAAYqB,aAAaO,MAAA,QAAepB,OAAQ8C,IAAAzD,EAAAjP,eAAA2S,IAAA,MAAmC1D,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCgD,KAAA,UAAgBA,KAAA,WAAexD,EAAA,aAAkBQ,OAAOoC,KAAA,SAAelB,IAAKpG,MAAA,SAAAoH,GAAyB7C,EAAAhP,eAAA,MAA4BgP,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkDK,YAAA,SAAmBR,EAAAS,GAAA,yDAAAN,EAAA,aAAkFK,YAAA,eAAAG,OAAkCwC,OAAA,IAAAC,eAAApD,EAAA/I,iBAAAoM,kBAAA,KAAyElD,EAAA,aAAkBQ,OAAOoC,KAAA,OAAAtQ,KAAA,aAAgCuN,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAiDE,aAAajP,KAAA,OAAAkP,QAAA,SAAA9T,MAAAwT,EAAA,KAAAO,WAAA,SAAgEiB,aAAe8B,cAAA,QAAqB3C,OAAQoC,KAAA,OAAAtQ,KAAA,WAA+BoP,IAAKpG,MAAAuE,EAAA3I,WAAqB2I,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CQ,OAAO4C,QAAAvD,EAAAzI,oBAAiCsK,IAAK2B,iBAAA,SAAAX,GAAkC7C,EAAAzI,mBAAAsL,MAAgC1C,EAAA,OAAYqB,aAAaO,MAAA,QAAepB,OAAQ8C,IAAAzD,EAAA1I,oBAAAoM,IAAA,MAAwC1D,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCgD,KAAA,UAAgBA,KAAA,WAAexD,EAAA,aAAkBQ,OAAOoC,KAAA,SAAelB,IAAKpG,MAAA,SAAAoH,GAAyB7C,EAAAzI,oBAAA,MAAiCyI,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkDK,YAAA,SAAmBR,EAAAS,GAAA,qDAAAN,EAAA,aAA8EK,YAAA,eAAAG,OAAkCwC,OAAA,IAAAC,eAAApD,EAAAxI,eAAA6L,kBAAA,KAAuElD,EAAA,aAAkBQ,OAAOoC,KAAA,OAAAtQ,KAAA,aAAgCuN,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAiDE,aAAajP,KAAA,OAAAkP,QAAA,SAAA9T,MAAAwT,EAAA,KAAAO,WAAA,SAAgEiB,aAAe8B,cAAA,QAAqB3C,OAAQoC,KAAA,OAAAtQ,KAAA,WAA+BoP,IAAKpG,MAAAuE,EAAApI,SAAmBoI,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CQ,OAAO4C,QAAAvD,EAAAlI,kBAA+B+J,IAAK2B,iBAAA,SAAAX,GAAkC7C,EAAAlI,iBAAA+K,MAA8B1C,EAAA,OAAYqB,aAAaO,MAAA,QAAepB,OAAQ8C,IAAAzD,EAAAnI,kBAAA6L,IAAA,MAAsC1D,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCgD,KAAA,UAAgBA,KAAA,WAAexD,EAAA,aAAkBQ,OAAOoC,KAAA,SAAelB,IAAKpG,MAAA,SAAAoH,GAAyB7C,EAAAlI,kBAAA,MAA+BkI,EAAAS,GAAA,uBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAsDK,YAAA,0CAAoDL,EAAA,aAAkBK,YAAA,UAAAG,OAA6BiD,MAAA,IAAW/B,IAAKpG,MAAAuE,EAAAJ,eAAyBI,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CK,YAAA,KAAAG,OAAwBlO,KAAA,WAAiBoP,IAAKpG,MAAAuE,EAAAxB,kBAA4BwB,EAAAS,GAAA,WAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAgDK,YAAA,KAAAG,OAAwBlO,KAAA,WAAiBoP,IAAKpG,MAAAuE,EAAAtD,QAAkBsD,EAAAS,GAAA,sBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA2DQ,OAAOkD,MAAA,QAAAC,wBAAA,EAAAP,QAAAvD,EAAA/O,sBAAA8Q,MAAA,MAAAgC,oBAAA,GAAuHlC,IAAK2B,iBAAA,SAAAX,GAAkC7C,EAAA/O,sBAAA4R,MAAmC1C,EAAA,OAAYK,YAAA,oBAA8BL,EAAA,SAAcQ,OAAOqD,IAAA,MAAUhE,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,eAAgDO,IAAA,cAAAC,OAAyBsD,QAAAjE,EAAAhU,aAAAT,MAAAyU,EAAA1T,aAAA4X,WAAA,GAAAnD,UAAA,IAAmFc,IAAKC,OAAA9B,EAAArB,gBAA4BiC,OAAQpU,MAAAwT,EAAAnU,SAAA,GAAAgK,SAAA,SAAAoL,GAAiDjB,EAAAkB,KAAAlB,EAAAnU,SAAA,KAAAoV,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,SAA0BQ,OAAOqD,IAAA,MAAUhE,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA6CK,YAAA,SAAAG,OAA4BI,UAAA,GAAAD,YAAA,MAAkCF,OAAQpU,MAAAwT,EAAAnU,SAAA,GAAAgK,SAAA,SAAAoL,GAAiDjB,EAAAkB,KAAAlB,EAAAnU,SAAA,KAAAoV,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,aAA8BK,YAAA,eAAAG,OAAkClO,KAAA,UAAA0R,KAAA,kBAAyCtC,IAAKpG,MAAAuE,EAAAtB,YAAsBsB,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CkB,IAAArB,EAAAtU,SAAA8U,YAAA,YAAAG,OAAgDyD,YAAA,MAAAC,WAAA,EAAAC,UAAAtE,EAAA5T,QAAAmY,QAAAvE,EAAA7O,aAAAqT,qBAAA,EAAAC,aAAAzE,EAAAxO,kBAAAkT,gBAAA,EAAAC,YAAA3E,EAAA/T,KAAAC,SAAA8T,EAAA9T,SAAA0Y,WAAA5E,EAAA3T,OAAoPwV,IAAKgD,oBAAA7E,EAAA1B,sBAAAwG,iBAAA9E,EAAAvB,mBAAArI,sBAAA4J,EAAA5J,0BAA6I,GAAA4J,EAAAS,GAAA,KAAAN,EAAA,QAA6BK,YAAA,gBAAAG,OAAmCgD,KAAA,UAAgBA,KAAA,WAAexD,EAAA,aAAkBK,YAAA,UAAAG,OAA6BlO,KAAA,WAAiBoP,IAAKpG,MAAA,SAAAoH,GAAyB7C,EAAA/O,uBAAA,MAAoC+O,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA8CK,YAAA,KAAAG,OAAwBlO,KAAA,WAAiBoP,IAAKpG,MAAAuE,EAAAd,iBAA2Bc,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCqB,aAAauD,MAAA,WAAgB,UAEzpcC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEja,EACA4U,GATF,EAVA,SAAAsF,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/246.b61214413f6f55be607b.js", "sourcesContent": ["<template>\r\n  <div class=\"sec-container\" v-loading=\"loading\">\r\n    <!-- 标题 -->\r\n    <p class=\"sec-title\">基本信息</p>\r\n    <div class=\"sec-form-container\">\r\n      <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n        <!-- 第一部分包括姓名到常住地公安start -->\r\n        <div class=\"sec-header-section\">\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"姓名\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.xm\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"性别\">\r\n              <!-- <el-input placeholder=\"\" v-model=\"tjlist.xb\" clearable disabled></el-input> -->\r\n              <template slot-scope=\"scope\">\r\n                <el-input placeholder=\"\" v-model=\"tjlist.xingbie\" clearable disabled></el-input>\r\n              </template>\r\n            </el-form-item>\r\n            <el-form-item label=\"出生年月日\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.csny\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"政治面貌\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.zzmmxx\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"涉密岗位\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.gwmc\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"涉密等级\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input placeholder=\"\" v-model=\"tjlist.smdjxx\" clearable disabled></el-input>\r\n                <!-- <p class=\"hyzk\" v-if=\"tjlist.hyzk == 0\">未婚</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.hyzk == 1\">已婚</p> -->\r\n              </template>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"工作单位及职务\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input placeholder=\"\" v-model=\"tjlist.bmzwzc\" clearable disabled></el-input>\r\n                <!-- <p class=\"hyzk\" v-if=\"tjlist.zzmm == 1\">中共党员</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 2\">团员</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 3\">民主党派</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 4\">群众</p> -->\r\n              </template>\r\n            </el-form-item>\r\n            <el-form-item label=\"身份证号\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.sfzhm\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 单位及职务、职称到涉密等级end -->\r\n        <!-- 主要学习及工作经历start -->\r\n        <p class=\"sec-title\">审批事项</p>\r\n        <div class=\"sec-form-container\">\r\n          <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n            <div class=\"sec-header-section\">\r\n              <div class=\"sec-form-left longLabel\">\r\n                <el-form-item label=\"审批事件类型\">\r\n                  <el-radio v-model=\"tjlist.splx\" label=\"1\">新申办从出入境证件</el-radio>\r\n                  <el-radio v-model=\"tjlist.splx\" label=\"0\">申请出国（境）</el-radio>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"sec-form-left longLabel widthzz\">\r\n                <el-form-item label=\"证件类型\">\r\n                  <div style=\"display: flex; flex-direction: column;\">\r\n                    <div v-for=\"(item, index) in zzhmList\" :key=\"item.zzid\">\r\n                      <div style=\"display: flex; align-items: center;justify-content: space-between;\">\r\n                        <el-checkbox-group v-model=\"checkList\" @change=\"zzlxxz\">\r\n                          <el-checkbox :label=\"item.zzid\" style=\"width: 200px;\">{{ item.fjlb\r\n                            }}</el-checkbox></el-checkbox-group>\r\n                        <div>证件号码:<el-input v-model=\"item.zjhm\" style=\"width: 200px;\" @blur=\"zzxx\"\r\n                            :disabled=\"!checkList.includes(index + 1)\"></el-input></div>\r\n                        <div>有效期:<el-date-picker v-model=\"item.yxq\" type=\"date\" placeholder=\"选择日期\" style=\"width: 200px;\"\r\n                            format=\"yyyy-MM-dd\" value-format=\"yyyy-MM-dd\" :disabled=\"!checkList.includes(index + 1)\">\r\n                          </el-date-picker></div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"sec-form-left longLabel\">\r\n                <el-form-item label=\"本年度因私出国(境)次数\">\r\n                  <el-input placeholder=\"\" v-model=\"tjlist.cs\" clearable></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"前往国家(地区)\">\r\n                  <el-input placeholder=\"\" v-model=\"tjlist.qwgj\" clearable></el-input>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"sec-form-left longLabel\">\r\n                <el-form-item label=\"起止日期\">\r\n                  <el-date-picker v-model=\"tjlist.value1\" style=\"width:100%\" type=\"daterange\" range-separator=\"至\"\r\n                    start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" format=\"yyyy-MM-dd\" value-format=\"yyyy-MM-dd\">\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"sec-form-left longLabel\">\r\n                <el-form-item label=\"出国（境）事由\">\r\n                  <el-input placeholder=\"\" v-model=\"tjlist.sy\" clearable></el-input>\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n        <!-- 主要学习及工作经历end -->\r\n        <!-- 家庭成员及主要社会关系情况start -->\r\n        <p class=\"sec-title\">同行人员情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscJtcyList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"gx\" label=\"与本人关系\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.gx\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"xm\" label=\"姓名\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.xm\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"nl\" label=\"年龄\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.nl\" placeholder=\"\" @blur=\"scope.row.nl = $event.target.value\" oninput=\"value=value.replace(/[^\\d.]/g,'')\"\r\n                @input=\"handleInput\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zzmm\" label=\"政治面貌\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.zzmm\" placeholder=\"请选择\">\r\n                <el-option v-for=\"item in zzmmoptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zw\" label=\"工作单位,职务及居住地\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.zw\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.czbtn1 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"cyjshgxAddRow(ryglRyscJtcyList)\">{{ scope.row.czbtn1 }}\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.czbtn2 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"cyjshgxDelRow(scope.$index, ryglRyscJtcyList)\">{{ scope.row.czbtn2 }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 下载start -->\r\n        <p class=\"sec-title\">下载</p>\r\n        <div class=\"sec-form-third haveBorderTop\">\r\n          <div class=\"sec-left-text\">\r\n            <p>1.涉密人员出国（境）保密承诺书</p>\r\n            <p>2.涉密人员出国（境）行前保密教育情况表</p>\r\n            <p>3.涉密人员出国（境）回访记录表</p>\r\n          </div>\r\n          <el-button size=\"small\" type=\"primary\" @click=\"wdzlxz\">下载</el-button>\r\n        </div>\r\n        <p class=\"sec-title\">上传扫描件</p>\r\n        <div class=\"sec-form-five haveBorderTop\" style=\"position: relative;\">\r\n          <div class=\"sec-left-text\">\r\n            <div class=\"flex\">\r\n              1.上传涉密人员出国（境）保密承诺书扫描件\r\n              <el-upload class=\"upload-demo\" action=\"#\" :http-request=\"httpRequest\" :show-file-list=\"false\">\r\n                <el-button size=\"mini\" type=\"primary\">上传</el-button>\r\n              </el-upload>\r\n              <el-button style=\"margin-left: 10px;\" v-show=\"ylth\" size=\"mini\" type=\"primary\"\r\n                @click=\"ylbmtxth\">预览</el-button>\r\n              <el-dialog :visible.sync=\"dialogVisible\">\r\n                <img :src=\"dialogImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                <div slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\r\n                </div>\r\n              </el-dialog>\r\n            </div>\r\n            <div class=\"flex\">\r\n              2.上传涉密人员出国（境）行前保密教育情况表扫描件\r\n              <el-upload class=\"upload-demo2\" action=\"#\" :http-request=\"httpBmcnsRequest\" :show-file-list=\"false\">\r\n                <el-button size=\"mini\" type=\"primary\">上传</el-button>\r\n              </el-upload>\r\n              <el-button style=\"margin-left: 10px;\" v-show=\"ylcn\" size=\"mini\" type=\"primary\"\r\n                @click=\"ylbmcns\">预览</el-button>\r\n              <el-dialog :visible.sync=\"dialogBmcnsVisible\">\r\n                <img :src=\"dialogBmcnsImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                <div slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button size=\"small\" @click=\"dialogBmcnsVisible = false\">取 消</el-button>\r\n                </div>\r\n              </el-dialog>\r\n            </div>\r\n            <div class=\"flex\">\r\n              3.上传涉密人员出国（境）回访记录表扫描件\r\n              <el-upload class=\"upload-demo3\" action=\"#\" :http-request=\"httpWtsRequest\" :show-file-list=\"false\">\r\n                <el-button size=\"mini\" type=\"primary\">上传</el-button>\r\n              </el-upload>\r\n              <el-button style=\"margin-left: 10px;\" v-show=\"ylwt\" size=\"mini\" type=\"primary\"\r\n                @click=\"ylwts\">预览</el-button>\r\n              <el-dialog :visible.sync=\"dialogWtsVisible\">\r\n                <img :src=\"dialogWtsImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                <div slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button size=\"small\" @click=\"dialogWtsVisible = false\">取 消</el-button>\r\n                </div>\r\n              </el-dialog>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 下载end -->\r\n        <!-- 底部操作按钮start -->\r\n        <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n          <el-button @click=\"returnIndex\" class=\"fr ml10\" plain>返回</el-button>\r\n          <el-button @click=\"chooseApproval\" class=\"fr\" type=\"success\">保存并提交</el-button>\r\n          <!-- <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">保存并提交</el-button>\r\n          <el-button @click=\"save\" class=\"fr\" type=\"primary\">保存</el-button> -->\r\n          <el-button @click=\"save\" class=\"fr\" type=\"primary\">临时保存</el-button>\r\n        </div>\r\n        <!-- 底部操作按钮end -->\r\n\r\n      </el-form>\r\n    </div>\r\n    <!-- 发起申请弹框start -->\r\n    <el-dialog title=\"选择审批人\" :close-on-click-modal=\"false\" :visible.sync=\"approvalDialogVisible\" width=\"40%\"\r\n      :destroy-on-close=\"true\">\r\n      <div class=\"dlFqsqContainer\">\r\n        <label for=\"\">部门:</label>\r\n        <el-cascader v-model=\"ryChoose.bm\" :options=\"regionOption\" :props=\"regionParams\" filterable clearable\r\n          ref=\"cascaderArr\" @change=\"bmSelectChange\"></el-cascader>\r\n        <label for=\"\">姓名:</label>\r\n        <el-input class=\"input2\" v-model=\"ryChoose.xm\" clearable placeholder=\"姓名\"></el-input>\r\n        <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\" @click=\"searchRy\">查询</el-button>\r\n        <BaseTable class=\"baseTable\" :tableHeight=\"'300'\" :key=\"tableKey\" :showIndex=true :tableData=\"ryDatas\" :columns=\"applyColumns\"\r\n          :showSingleSelection=\"true\" :handleColumn=\"handleColumnApply\" :showPagination=true :currentPage=\"page\"\r\n          :pageSize=\"pageSize\" :totalCount=\"total\" @handleCurrentChange=\"handleCurrentChangeRy\"\r\n          @handleSizeChange=\"handleSizeChangeRy\" @handleSelectionChange=\"handleSelectionChange\">\r\n        </BaseTable>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"warning\" class=\"fr ml10\" @click=\"approvalDialogVisible = false\">关 闭</el-button>\r\n        <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">提交</el-button>\r\n        <!-- <el-button @click=\"save\" class=\"fr\" type=\"primary\">保存</el-button> -->\r\n        <div style=\"clear:both\"></div>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 发起申请弹框end -->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  submitRyrysc,\r\n  getLcSLid,\r\n  updateRysc,\r\n  updateSlzt,\r\n  getZzjgList,\r\n  getSpUserList,\r\n  getFwdyidByFwlx,\r\n  getLoginInfo,\r\n  downloadCgspwdZip,\r\n\r\n  deleteSlxxBySlid\r\n} from '../../../api/index'\r\nimport {\r\n  addRyglCgcj,\r\n  updateRyglCgcj,\r\n  selectRyglCgcjBySlId,\r\n} from '../../../api/cgjsc'\r\nimport BaseTable from '../../components/common/baseTable.vue'\r\nimport AddLineTable from \"../../components/common/addLineTable.vue\";   //人工纠错组件\r\nexport default {\r\n  components: {\r\n    AddLineTable,\r\n    BaseTable\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      tableKey:1,\r\n      checkList: [],\r\n      loading: false,\r\n      // 弹框人员选择条件\r\n      ryChoose: {\r\n        'bm': '',\r\n        'xm': ''\r\n      },\r\n      regionOption: [], // 部门下拉\r\n      page: 1, // 审批人弹框当前页\r\n      pageSize: 10, // 审批人弹框每页条数\r\n      radioIdSelect: '', // 审批人弹框人员单选\r\n      ryDatas: [], // 弹框人员选择\r\n      total: 0, // 弹框人员总数\r\n      regionParams: {\r\n        label: 'label', //这里可以配置你们后端返回的属性\r\n        value: 'label',\r\n        children: 'childrenRegionVo',\r\n        expandTrigger: 'click',\r\n        checkStrictly: true\r\n      }, //地域信息配置参数\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: '#EEF7FF',\r\n        color: '#4D91F8'\r\n      },\r\n      // form表单提交数据\r\n      tjlist: {\r\n        smryid: '',\r\n        xm: '',\r\n        xb: '',\r\n        gj: '中国',\r\n        dwzwzc: '',\r\n        yrsmgw: '',\r\n        cym: '',\r\n        mz: '',\r\n        hyzk: '',\r\n        zzmm: '',\r\n        lxdh: '',\r\n        sfzhm: '',\r\n        hjdz: '',\r\n        hjdgajg: '',\r\n        czdz: '',\r\n        czgajg: '',\r\n        imageUrl: '',\r\n        yjqk: '0', // 拥有外籍、境外永久居留权或者长期居留许可情况\r\n        qscfqk: '0', // 配偶子女有关情况\r\n        qtqk: '', // 其他需要说明的情况\r\n        brcn: '',\r\n        splx: '',\r\n        bmcns: '',\r\n        xqbmjyqkb: '',\r\n        hfjlb: '',\r\n        value1: [],\r\n      },\r\n      // 主要学习及工作经历\r\n      ryglRyscScjlList: [{\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 家庭成员及社会关系\r\n      ryglRyscJtcyList: [{\r\n        \"gx\": \"\",//关系描述\r\n        \"nl\": \"\",//年龄\r\n        \"zzmm\": \"\",//政治面貌\r\n        \"jwjlqk\": '',//是否有外籍、境外居留权、长期居留许可\r\n        \"xm\": \"\",//姓名\r\n        \"cgszd\": \"\",//工作(学习)单位\r\n        \"zw\": \"\",//职务\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      zzhmList: [\r\n        {\r\n          zzid: 1,\r\n          fjlb: '因公护照',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 2,\r\n          fjlb: '因公港澳通行证',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 3,\r\n          fjlb: '因公台湾通行证',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 4,\r\n          fjlb: '因私护照',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 5,\r\n          fjlb: '因私港澳通行证',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 6,\r\n          fjlb: '因私台湾通行证',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n      ],\r\n      // 因私出国(境)情况\r\n      ryglRyscYccgList: [{\r\n        \"cggj\": \"\",//出国国家\r\n        \"sy\": \"\",//事由\r\n        \"zzsj\": \"\",//终止时间\r\n        \"qssj\": \"\",//起始时间\r\n        // \"bz\": \"\",//备注\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 接受境外资助情况\r\n      ryglRyscJwzzqkList: [{\r\n        \"zzsj\": \"\",//时间\r\n        \"jgmc\": \"\",//机构名称\r\n        // \"bz\": \"\",//备注\r\n        \"zznr\": \"\",//资助内容\r\n        \"gj\": \"\",//国家\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 处分或者违法犯罪情况\r\n      ryglRyscCfjlList: [{\r\n        \"cfdw\": \"\",//处罚单位\r\n        \"cfsj\": \"\",//处罚时间\r\n        \"cfjg\": \"\",//处罚结果\r\n        \"cfyy\": \"\",//处罚原因\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 持有因公出入境证件情况\r\n      ryglRyscSwzjList: [{\r\n        'zjmc': '护照',\r\n        'fjlb': 1,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '港澳通行证',\r\n        'fjlb': 2,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '台湾通行证',\r\n        'fjlb': 3,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '护照',\r\n        'fjlb': 4,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '港澳通行证',\r\n        'fjlb': 5,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '台湾通行证',\r\n        'fjlb': 6,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }],\r\n      ryInfo: {}, // 当前任用审查的人员信息\r\n      // 政治面貌下拉选项\r\n      zzmmoptions: [{\r\n        value: '中央党员',\r\n        label: '中央党员'\r\n      }, {\r\n        value: '团员',\r\n        label: '团员'\r\n      }, {\r\n        value: '民主党派',\r\n        label: '民主党派'\r\n      }, {\r\n        value: '群众',\r\n        label: '群众'\r\n      }],\r\n      // 是否有外籍、境外居留权、长期居留许可\r\n      ynoptions: [{\r\n        value: '1',\r\n        label: '是'\r\n      }, {\r\n        value: '0',\r\n        label: '否'\r\n      }],\r\n      sltshow: '', // 文档的缩略图显示\r\n      routeType: '', // 当前的类型（编辑或者首次新增）\r\n      dialogImageUrl: '', // 预览本人承诺扫描件展示\r\n      dialogVisible: false, // 预览本人承诺扫描件弹框显隐\r\n      approvalDialogVisible: false, // 选择申请人弹框弹框显隐\r\n      fileRow: '', // 本人承诺凭证的file数据\r\n      // 选择审核人table\r\n      applyColumns: [\r\n        {\r\n          name: '姓名',\r\n          prop: 'xm',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '部门',\r\n          prop: 'bmmc',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '岗位',\r\n          prop: 'gwmc',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        }\r\n      ],\r\n      handleColumnApply: [],\r\n      scqk: [\r\n        {\r\n          sfty: '同意',\r\n          id: 1\r\n        },\r\n        {\r\n          sfty: '不同意',\r\n          id: 0\r\n        },\r\n      ],\r\n      disabled2: false,\r\n      sltshow: '', // 文档的缩略图显示\r\n      sltbmcnsshow: '', // 文档的缩略图显示\r\n      sltwtsshow: '', // 文档的缩略图显示\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n      dialogBmcnsImageUrl: '',\r\n      dialogBmcnsVisible: false,\r\n      dialogWtsImageUrl: '',\r\n      dialogWtsVisible: false,\r\n      fileRow: '',\r\n      filebmcnsRow: '',\r\n      filewtsRow: '',\r\n      fileryxxRow: '',\r\n      filexzglRow: '',\r\n      ylth: false,\r\n      ylcn: false,\r\n      ylwt: false,\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted() {\r\n    this.onfwid()\r\n    this.getOrganization()\r\n    this.yhDatas = this.$route.query.datas\r\n    this.ryInfo = this.$route.query.datas.ryglCgcj // 当type为update时，获取人员的基本信息\r\n    this.routeType = this.$route.query.type // type : update/add  编辑时或者首次创建时\r\n    console.log(this.yhDatas);\r\n    console.log(this.ryInfo);\r\n    console.log(this.routeType);\r\n    let result = {}\r\n    let iamgeBase64 = ''\r\n    let iamgeBase64Brcn = ''\r\n    if (this.$route.query.type == 'add') {\r\n      // 首次发起申请\r\n      result = { ...this.tjlist, ...this.$route.query.datas }\r\n      iamgeBase64 = \"data:image/jpeg;base64,\" + this.$route.query.datas.zp\r\n    } else {\r\n      // 保存 继续编辑\r\n      result = { ...this.tjlist, ...this.$route.query.datas.ryglCgcj }\r\n      console.log(result)\r\n      iamgeBase64 = \"data:image/jpeg;base64,\" + this.$route.query.datas.ryglCgcj.zp\r\n      iamgeBase64Brcn = \"data:image/jpeg;base64,\" + this.$route.query.datas.ryglCgcj.brcn\r\n      if (typeof iamgeBase64Brcn === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64Brcn) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64Brcn)) {\r\n          let that = this;\r\n          function previwImg(item) {\r\n            that.sltshow = item;\r\n          }\r\n          previwImg(iamgeBase64Brcn);\r\n        }\r\n      }\r\n      console.log(this.$route.query.datas);\r\n      // 家庭成员及主要社会关系情况\r\n      if (this.$route.query.datas.ryglCgcjTxqkList.length == 0) {\r\n        this.ryglRyscJtcyList = [{\r\n          \"gx\": \"\",//关系描述\r\n          \"nl\": \"\",//年龄\r\n          \"zzmm\": \"\",//政治面貌\r\n          \"jwjlqk\": '',//是否有外籍、境外居留权、长期居留许可\r\n          \"xm\": \"\",//姓名\r\n          \"cgszd\": \"\",//工作(学习)单位\r\n          \"zw\": \"\",//职务\r\n          'czbtn1': '增加行',\r\n          'czbtn2': ''\r\n        }]\r\n      } else {\r\n        this.ryglRyscJtcyList = this.$route.query.datas.ryglCgcjTxqkList.map((data) => {\r\n          // if (data.jwjlqk == 0) {\r\n          //   data.jwjlqk = '否'\r\n          // } else if (data.jwjlqk == 1) {\r\n          //   data.jwjlqk = '是'\r\n          // }\r\n          data.czbtn1 = '增加行'\r\n          data.czbtn2 = '删除'\r\n          return data\r\n        })\r\n      }\r\n    }\r\n    // 初始化各状态值显示其代表的中文名\r\n    result.xingbie = result.xb == 2 ? '女' : result.xb == 1 ? '男' : ''\r\n    result.hyzk = result.hyzk == 0 ? '未婚' : result.hyzk == 1 ? '已婚' : ''\r\n    result.zzmmxx = result.zzmm == 1 ? '中共党员' : result.zzmm == 2 ? '团员' : result.zzmm == 3 ? '民主党派' : result.zzmm == 4 ? '群众' : ''\r\n    result.jbzc = result.jbzc == 1 ? '省部级' : result.jbzc == 2 ? '厅局级' : result.jbzc == 3 ? '县处级' : result.jbzc == 4 ? '乡科级及以下' : result.jbzc == 5 ? '高级(含正高、副高)' : result.jbzc == 6 ? '中级' : result.jbzc == 7 ? '初级及以下' : result.jbzc == 8 ? '试用期人员' : result.jbzc == 9 ? '工勤人员' : result.jbzc == 10 ? '企业职员' : result.jbzc == 11 ? '其他' : ''\r\n    let bmC = result.bmmc != '' ? '部门：' + result.bmmc + '、' : ''\r\n    let zwC = result.zw != '' ? '职务：' + result.zw + '、' : ''\r\n    result.bmzwzc = bmC + zwC + '职称：' + result.jbzc\r\n    // result.gwmc = result.gwmc && result.gwmc.length === 1 ? result.gwmc.toString() : result.gwmc && result.gwmc.length > 1 ? result.gwmc.join('/') : ''\r\n    result.smdjxx = result.smdj == 1 ? '核心' : result.smdj == 2 ? '重要' : result.smdj == 3 ? '一般' : ''\r\n    this.tjlist = result\r\n    if (this.tjlist.sfzhm == '' || this.tjlist.sfzhm == undefined) {\r\n      this.tjlist.csny = result.csny\r\n    } else {\r\n      this.tjlist.csny = this.tjlist.sfzhm.substring(6, 10) + \"-\" + this.tjlist.sfzhm.substring(10, 12) + \"-\" + this.tjlist.sfzhm.substring(12, 14);\r\n    }\r\n    this.tjlist.yjqk = result.yjqk.toString()\r\n    this.tjlist.qscfqk = result.qscfqk.toString()\r\n    console.log(this.tjlist);\r\n    console.log(result.splx);\r\n    if (result.splx == 1 || result.splx == 0) {\r\n      this.tjlist.splx = result.splx.toString()\r\n    } else {\r\n      console.log(123);\r\n      this.tjlist.splx = ''\r\n    }\r\n    if (this.$route.query.datas.ryglCgcjSwzjList != undefined) {\r\n      this.zzhmList = this.$route.query.datas.ryglCgcjSwzjList\r\n    }\r\n    this.zzhmList.forEach((item) => {\r\n      if (item.checked == 1) {\r\n        this.checkList.push(item.zzid)\r\n      }\r\n    })\r\n\r\n    if (result.qssj != '' || result.qssj != undefined) {\r\n      this.tjlist.value1 = []\r\n      this.tjlist.value1.push(result.qssj);\r\n      this.tjlist.value1.push(result.jssj);\r\n    }\r\n    console.log(this.tjlist);\r\n    if (typeof iamgeBase64 === \"string\") {\r\n      // 复制某条消息\r\n      if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n      function validDataUrl(s) {\r\n        return validDataUrl.regex.test(s);\r\n      }\r\n      validDataUrl.regex =\r\n        /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n      if (validDataUrl(iamgeBase64)) {\r\n        let that = this;\r\n        function previwImg(item) {\r\n          that.tjlist.imageUrl = item;\r\n        }\r\n        previwImg(iamgeBase64);\r\n      }\r\n    }\r\n    if (this.tjlist.bmcns != '') {\r\n      this.ylth = true\r\n    }\r\n    if (this.tjlist.xqbmjyqkb != '') {\r\n      this.ylcn = true\r\n    }\r\n    if (this.tjlist.hfjlb != '') {\r\n      this.ylwt = true\r\n    }\r\n  },\r\n  methods: {\r\n    handleInput(value) {\r\n      if (value < 0 || value > 999) {\r\n        this.$message.warning('请输入实际年龄！')\r\n      }\r\n    },\r\n    zzlxxz() {\r\n      console.log(this.checkList);\r\n      console.log(this.zzhmList);\r\n      this.zzhmList.forEach(item => {\r\n        item.checked = 0\r\n      })\r\n      this.checkList.forEach((item, index) => {\r\n        this.zzhmList.forEach((item1, index1) => {\r\n          if (item == item1.zzid) {\r\n            item1.checked = 1\r\n          }\r\n        })\r\n      })\r\n      this.zzhmList.forEach(item => {\r\n        if (item.checked == 0) {\r\n          item.zjhm = ''\r\n          item.yxq = ''\r\n        }\r\n      })\r\n      console.log(this.zzhmList);\r\n    },\r\n    zzxx() {\r\n      console.log(this.checkList);\r\n    },\r\n    chRadio() { },\r\n    // blob格式转base64\r\n    blobToBase64(blob, callback) {\r\n      const fileReader = new FileReader();\r\n      fileReader.onload = (e) => {\r\n        callback(e.target.result);\r\n      };\r\n      fileReader.readAsDataURL(blob);\r\n    },\r\n    handleSelectionChange(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    zpzm(zp) {\r\n      const iamgeBase64 = \"data:image/jpeg;base64,\" + zp;\r\n      let zpxx\r\n      if (typeof iamgeBase64 === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64)) {\r\n          // debugger;\r\n          // let that = this;\r\n\r\n          function previwImg(item) {\r\n            zpxx = item;\r\n          }\r\n          previwImg(iamgeBase64);\r\n        }\r\n      }\r\n      return zpxx\r\n    },\r\n    // 上传保密谈话记录表凭证\r\n    httpRequest(data) {\r\n      this.sltshow = URL.createObjectURL(data.file);\r\n      this.fileRow = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.bmcns = dataurl.split(',')[1]\r\n        console.log(this.tjlist.bmcns);\r\n        if (this.tjlist.bmcns != '') {\r\n          this.ylth = true\r\n        }\r\n      });\r\n    },\r\n    // 预览\r\n    ylbmtxth() {\r\n      let zpxx\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogImageUrl = URL.createObjectURL(this.fileRow)\r\n      } else {\r\n        zpxx = this.zpzm(this.tjlist.bmcns)\r\n        this.dialogImageUrl = zpxx\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // 上传离岗离职涉密人员保密承诺书凭证\r\n    httpBmcnsRequest(data) {\r\n      this.sltbmcnsshow = URL.createObjectURL(data.file);\r\n      this.filebmcnsRow = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.xqbmjyqkb = dataurl.split(',')[1]\r\n        console.log(this.tjlist.xqbmjyqkb);\r\n        if (this.tjlist.xqbmjyqkb != '') {\r\n          this.ylcn = true\r\n        }\r\n      });\r\n    },\r\n    // 预览\r\n    ylbmcns() {\r\n      let zpxx\r\n      if (this.routeType == 'add') {\r\n        this.dialogBmcnsImageUrl = URL.createObjectURL(this.filebmcnsRow)\r\n      } else {\r\n        zpxx = this.zpzm(this.tjlist.xqbmjyqkb)\r\n        this.dialogBmcnsImageUrl = zpxx\r\n      }\r\n      this.dialogBmcnsVisible = true\r\n    },\r\n    // 上传脱密期委托管理书凭证\r\n    httpWtsRequest(data) {\r\n      this.sltwtsshow = URL.createObjectURL(data.file);\r\n      this.filewtsRow = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.hfjlb = dataurl.split(',')[1]\r\n        console.log(this.tjlist.hfjlb);\r\n        if (this.tjlist.hfjlb != '') {\r\n          this.ylwt = true\r\n        }\r\n      });\r\n    },\r\n    // 预览\r\n    ylwts() {\r\n      let zpxx\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogWtsImageUrl = URL.createObjectURL(this.filewtsRow)\r\n      } else {\r\n        zpxx = this.zpzm(this.tjlist.hfjlb)\r\n        this.dialogWtsImageUrl = zpxx\r\n      }\r\n      this.dialogWtsVisible = true\r\n    },\r\n    // 主要学习及工作经历增加行\r\n    addRow(data) {\r\n      data.push({\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除',\r\n      })\r\n    },\r\n    // 主要学习及工作经历删除行\r\n    delRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 家庭成员及主要社会关系情况 添加行\r\n    cyjshgxAddRow(data) {\r\n      data.push({\r\n        'ybrgx': '',\r\n        'xm': '',\r\n        'sfywjjwjlqcqjlxk': '',\r\n        'dw': '',\r\n        'zw': '',\r\n        'zzmm': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 家庭成员及主要社会关系情况 删除行\r\n    cyjshgxDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 因私出国(境)情况 添加行\r\n    yscgqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'zzrq': '',\r\n        'jsnsdgjhdq': '',\r\n        'sy': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 因私出国(境)情况 删除行\r\n    yscgqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 接受境外资助情况 添加行\r\n    jsjwzzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'gjdq': '',\r\n        'jgmc': '',\r\n        'zznr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 接受境外资助情况 删除行\r\n    jsjwzzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 处分或者违法犯罪情况 添加行\r\n    clhwffzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'cljg': '',\r\n        'clyy': '',\r\n        'cljg': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    async wdzlxz() {\r\n      var returnData = await downloadCgspwdZip();\r\n      var date = new Date()\r\n      var sj = date.getFullYear() + \"\" + (date.getMonth() + 1) + \"\" + date.getDate()\r\n      this.dom_download(returnData, '出国出境' + '-' + sj + \".zip\");\r\n    },\r\n    //处理下载流\r\n    dom_download(content, fileName) {\r\n      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象\r\n      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象\r\n      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download\r\n      dom.style.display = 'none'\r\n      dom.href = url\r\n      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件\r\n      document.body.appendChild(dom)\r\n      dom.click()\r\n    },\r\n    // 处分或者违法犯罪情况 删除行\r\n    clhwffzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 预览\r\n    yulan() {\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogImageUrl = URL.createObjectURL(this.fileRow)\r\n      } else {\r\n        this.dialogImageUrl = this.sltshow\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除\r\n    shanchu() {\r\n      this.tjlist.brcn = ''\r\n      this.sltshow = ''\r\n    },\r\n    async onfwid() {\r\n      let params = {\r\n        fwlx: 27\r\n      }\r\n      let data = await getFwdyidByFwlx(params)\r\n      console.log(data);\r\n      this.fwdyid = data.data.fwdyid\r\n    },\r\n    jxfa() {\r\n      if (this.tjlist.splx == '') {\r\n        this.$message.error('请选择审批类型')\r\n        return true\r\n      }\r\n      if (this.checkList.length == 0) {\r\n        this.$message.error('请选择证件')\r\n        return true\r\n      }\r\n      if (this.tjlist.cs == '' || this.tjlist.cs == undefined) {\r\n        this.$message.error('请输入本年度因私出国(境)次数')\r\n        return true\r\n      }\r\n      if (this.tjlist.qwgj == '' || this.tjlist.qwgj == undefined) {\r\n        this.$message.error('请输入前往国家(地区)')\r\n        return true\r\n      }\r\n      if (this.tjlist.value1 == [] || this.tjlist.value1 == undefined) {\r\n        this.$message.error('请输入起止日期')\r\n        return true\r\n      }\r\n      let txry = false\r\n      this.ryglRyscJtcyList.forEach(item => {\r\n        if (item.gx == '' || item.gx == undefined) {\r\n          this.$message.error('请输入与本人关系')\r\n          txry = true\r\n          return\r\n        }\r\n        if (item.xm == '' || item.xm == undefined) {\r\n          this.$message.error('请输入同行人姓名')\r\n          txry = true\r\n          return\r\n        }\r\n        if (item.nl == '' || item.nl == undefined) {\r\n          this.$message.error('请输入同行人年龄')\r\n          txry = true\r\n          return\r\n        }\r\n        if (item.zzmm == '' || item.zzmm == undefined) {\r\n          this.$message.error('请输入同行人政治面貌')\r\n          txry = true\r\n          return\r\n        }\r\n        if (item.zw == '' || item.zw == undefined) {\r\n          this.$message.error('请输入同行人工作单位,职务及居住地')\r\n          txry = true\r\n          return\r\n        }\r\n      })\r\n      if (txry) {\r\n        return true\r\n      }\r\n      this.checkList.forEach((item, index) => {\r\n        this.zzhmList.forEach((item1, index1) => {\r\n          if (item == item1.zzid) {\r\n            item1.checked = 1\r\n          }\r\n        })\r\n      })\r\n      let pd\r\n      this.zzhmList.forEach(item => {\r\n        if (item.checked == 1) {\r\n          if (item.zjhm == '') {\r\n            this.$message.error('请填写' + item.fjlb + '的证件号码')\r\n            pd = true\r\n            return\r\n          }\r\n          if (item.yxq == '') {\r\n            this.$message.error('请填写' + item.fjlb + '的有限期')\r\n            pd = true\r\n            return\r\n          }\r\n        }\r\n      })\r\n      if (pd) {\r\n        return true\r\n      }\r\n    },\r\n    // 保存\r\n    async save() {\r\n      let param = {\r\n        'fwdyid': this.fwdyid,\r\n        'lcslclzt': 3\r\n      }\r\n      if (this.jxfa()) {\r\n        return\r\n      }\r\n      this.tjlist.qssj = this.tjlist.value1[0]\r\n      this.tjlist.jssj = this.tjlist.value1[1]\r\n      // this.ryglRyscJtcyList.forEach((e) => {\r\n      //   if (e.jwjlqk == '否') {\r\n      //     e.jwjlqk = 0\r\n      //   } else if (e.jwjlqk == '是') {\r\n      //     e.jwjlqk = 1\r\n      //   }\r\n      // })\r\n      if (this.routeType == 'update') {\r\n        this.tjlist.dwid = this.ryInfo.dwid\r\n        this.tjlist.lcslid = this.ryInfo.lcslid\r\n        let params = {\r\n          'ryglCgcj': this.tjlist,\r\n          'ryglCgcjSwzjList': this.zzhmList,\r\n          'ryglCgcjTxqkList': this.ryglRyscJtcyList,\r\n        }\r\n        let resDatas = await updateRyglCgcj(params)\r\n        if (resDatas.code == 10000) {\r\n          this.$router.push('/cgjsc')\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n        }\r\n      } else {\r\n        param.smryid = this.yhDatas.smryid\r\n        this.tjlist.dwid = this.yhDatas.dwid\r\n        let res = await getLcSLid(param)\r\n        if (res.code == 10000) {\r\n          this.tjlist.lcslid = res.data.slid\r\n          let params = {\r\n            'ryglCgcj': this.tjlist,\r\n            'ryglCgcjSwzjList': this.zzhmList,\r\n            'ryglCgcjTxqkList': this.ryglRyscJtcyList,\r\n          }\r\n          let resDatas = await addRyglCgcj(params)\r\n          if (resDatas.code == 10000) {\r\n            this.$router.push('/cgjsc')\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n          } else {\r\n            deleteSlxxBySlid({ slid: res.data.slid })\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //全部组织机构List\r\n    async getOrganization() {\r\n      let zzjgList = await getZzjgList()\r\n      this.zzjgmc = zzjgList\r\n      let shu = []\r\n      this.zzjgmc.forEach(item => {\r\n        let childrenRegionVo = []\r\n        this.zzjgmc.forEach(item1 => {\r\n          if (item.bmm == item1.fbmm) {\r\n            childrenRegionVo.push(item1)\r\n            item.childrenRegionVo = childrenRegionVo\r\n          }\r\n        });\r\n        shu.push(item)\r\n      })\r\n      let shuList = []\r\n      let list = await getLoginInfo()\r\n      if (list.fbmm == '') {\r\n        shu.forEach(item => {\r\n          if (item.fbmm == '') {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      if (list.fbmm != '') {\r\n        shu.forEach(item => {\r\n          console.log(item);\r\n          if (item.fbmm == list.fbmm) {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      shuList[0].childrenRegionVo.forEach(item => {\r\n        this.regionOption.push(item)\r\n      })\r\n    },\r\n    handleSelectionChange1(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    handleCurrentChangeRy(val) {\r\n      this.page = val\r\n      this.chooseApproval()\r\n    },\r\n    //列表分页--更改每页显示个数\r\n    handleSizeChangeRy(val) {\r\n      this.page = 1\r\n      this.pageSize = val\r\n      this.chooseApproval()\r\n    },\r\n    // 人员搜索\r\n    searchRy() {\r\n      this.tableKey++\r\n      this.chooseApproval()\r\n    },\r\n    // 发起申请选择人员 人员下拉\r\n    bmSelectChange(item) {\r\n      if (item != undefined) {\r\n        this.ryChoose.bm = item.join('/')\r\n      }\r\n    },\r\n    // 选择审批人\r\n    async chooseApproval() {\r\n      // this.getOrganization()\r\n      this.approvalDialogVisible = true\r\n      let param = {\r\n        'page': this.page,\r\n        'pageSize': this.pageSize,\r\n        'fwdyid': this.fwdyid,\r\n        'bmmc': this.ryChoose.bm,\r\n        'xm': this.ryChoose.xm\r\n      }\r\n      let resData = await getSpUserList(param)\r\n      if (resData.records) {\r\n        // this.loading = false\r\n        this.ryDatas = resData.records\r\n        this.total = resData.total\r\n      } else {\r\n        this.$message.error('数据获取失败！')\r\n      }\r\n    },\r\n    // 保存并提交\r\n    async saveAndSubmit() {\r\n      if (this.jxfa()) {\r\n        return\r\n      }\r\n      this.tjlist.qssj = this.tjlist.value1[0]\r\n      this.tjlist.jssj = this.tjlist.value1[1]\r\n      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {\r\n        let param = {\r\n          'fwdyid': this.fwdyid\r\n        }\r\n        if (this.routeType == 'update') {\r\n          param.lcslclzt = 2\r\n          param.smryid = this.ryInfo.smryid\r\n          param.slid = this.ryInfo.lcslid\r\n          param.clrid = this.radioIdSelect.yhid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = this.ryInfo.dwid\r\n            this.tjlist.lcslid = this.ryInfo.lcslid\r\n            let params = {\r\n              'ryglCgcj': this.tjlist,\r\n              'ryglCgcjSwzjList': this.zzhmList,\r\n              'ryglCgcjTxqkList': this.ryglRyscJtcyList,\r\n            }\r\n            let resDatas = await updateRyglCgcj(params)\r\n            if (resDatas.code == 10000) {\r\n              let paramStatus = {\r\n                'fwdyid': this.fwdyid,\r\n                'slid': this.tjlist.lcslid\r\n              }\r\n              let resStatus = await updateSlzt(paramStatus)\r\n              if (resStatus.code == 10000) {\r\n                this.$router.push('/cgjsc')\r\n                this.$message({\r\n                  message: '保存并提交成功',\r\n                  type: 'success'\r\n                })\r\n              }\r\n            }\r\n          }\r\n        } else {\r\n          param.lcslclzt = 0\r\n          param.clrid = this.radioIdSelect.yhid\r\n          param.smryid = this.yhDatas.smryid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = this.yhDatas.dwid\r\n            this.tjlist.lcslid = res.data.slid\r\n            let params = {\r\n              'ryglCgcj': this.tjlist,\r\n              'ryglCgcjSwzjList': this.zzhmList,\r\n              'ryglCgcjTxqkList': this.ryglRyscJtcyList,\r\n            }\r\n            let resDatas = await addRyglCgcj(params)\r\n            if (resDatas.code == 10000) {\r\n              this.$router.push('/cgjsc')\r\n              this.$message({\r\n                message: '保存并提交成功',\r\n                type: 'success'\r\n              })\r\n            } else {\r\n              deleteSlxxBySlid({ slid: res.data.slid })\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: '请选择审批人',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 返回\r\n    returnIndex() {\r\n      this.$router.push('/cgjsc')\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.sec-container {\r\n  width: 100%;\r\n  height: calc(100% - 50px);\r\n  overflow-y: overlay;\r\n}\r\n\r\n.sec-container>>>.el-input.is-disabled .el-input__inner {\r\n  color: #000000;\r\n}\r\n\r\n.sec-title {\r\n  border-left: 5px solid #1b72d8;\r\n  color: #1b72d8;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  text-indent: 10px;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  /* height: 40px; */\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n  width: 258px;\r\n  position: absolute;\r\n  right: 0px;\r\n  top: 0;\r\n  height: 245px;\r\n  border: 1px solid #CDD2D9;\r\n  border-left: 0;\r\n  background: #ffffff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.sec-form-second {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-third {\r\n  border: 1px solid #CDD2D9;\r\n  /* height: 40px;  */\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-four {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  overflow: hidden;\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.yulan {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  color: #3874D5;\r\n  font-weight: 600;\r\n  float: left;\r\n  margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n  width: 178px;\r\n  height: 178px;\r\n  display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 178px;\r\n  height: 178px;\r\n  line-height: 178px;\r\n  text-align: center;\r\n  border: 2px solid #EBEBEB;\r\n}\r\n\r\n.sec-form-six {\r\n  border: 1px solid #CDD2D9;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.flex {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-demo {\r\n  margin-left: 114px;\r\n}\r\n\r\n.upload-demo2 {\r\n  margin-left: 50px;\r\n}\r\n\r\n.upload-demo3 {\r\n  margin-left: 114px;\r\n}\r\n\r\n.ml10 {\r\n  margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n  margin-top: 10px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n  border: none;\r\n}\r\n\r\n.sec-left-text {\r\n  float: left;\r\n  margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n  border-top: 1px solid #CDD2D9;\r\n}\r\n\r\n>>>.longLabel .el-form-item__label {\r\n  width: 500px !important;\r\n}\r\n\r\n>>>.longLabel .el-form-item__content {\r\n  margin-left: 500px !important;\r\n  padding-left: 20px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n  border-top: 0;\r\n} */\r\n.sec-form-second .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n  width: 100%;\r\n  border: 1px solid #EBEEF5;\r\n  height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n.hyzk {\r\n  padding-left: 15px;\r\n  background-color: #F5F7FA;\r\n  width: calc(100% - 16px);\r\n  border-right: 1px solid #CDD2D9;\r\n  color: #000000;\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n  border: none !important;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n  width: 200px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n  border: none;\r\n  border-right: 1px solid #CDD2D9;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n  border: 1px solid #CDD2D9;;\r\n} */\r\n>>>.el-form-item__label {\r\n  border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n/* /deep/.sec-form-container .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n} */\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n\r\n>>>.widthzz .el-form-item__label {\r\n  height: 270px;\r\n  line-height: 270px;\r\n}\r\n\r\n>>>.widthzz .el-input__inner {\r\n  border-right: none;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/cgjscTable.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"sec-container\"},[_c('p',{staticClass:\"sec-title\"},[_vm._v(\"基本信息\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"姓名\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xm\", $$v)},expression:\"tjlist.xm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"性别\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xingbie),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xingbie\", $$v)},expression:\"tjlist.xingbie\"}})]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"出生年月日\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.csny),callback:function ($$v) {_vm.$set(_vm.tjlist, \"csny\", $$v)},expression:\"tjlist.csny\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"政治面貌\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.zzmmxx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zzmmxx\", $$v)},expression:\"tjlist.zzmmxx\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"涉密岗位\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.gwmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gwmc\", $$v)},expression:\"tjlist.gwmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"涉密等级\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.smdjxx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"smdjxx\", $$v)},expression:\"tjlist.smdjxx\"}})]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"工作单位及职务\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.bmzwzc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmzwzc\", $$v)},expression:\"tjlist.bmzwzc\"}})]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"身份证号\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.sfzhm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sfzhm\", $$v)},expression:\"tjlist.sfzhm\"}})],1)],1)]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"审批事项\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"审批事件类型\"}},[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.tjlist.splx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"splx\", $$v)},expression:\"tjlist.splx\"}},[_vm._v(\"新申办从出入境证件\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"0\"},model:{value:(_vm.tjlist.splx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"splx\", $$v)},expression:\"tjlist.splx\"}},[_vm._v(\"申请出国（境）\")])],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left longLabel widthzz\"},[_c('el-form-item',{attrs:{\"label\":\"证件类型\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"column\"}},_vm._l((_vm.zzhmList),function(item,index){return _c('div',{key:item.zzid},[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"justify-content\":\"space-between\"}},[_c('el-checkbox-group',{on:{\"change\":_vm.zzlxxz},model:{value:(_vm.checkList),callback:function ($$v) {_vm.checkList=$$v},expression:\"checkList\"}},[_c('el-checkbox',{staticStyle:{\"width\":\"200px\"},attrs:{\"label\":item.zzid}},[_vm._v(_vm._s(item.fjlb))])],1),_vm._v(\" \"),_c('div',[_vm._v(\"证件号码:\"),_c('el-input',{staticStyle:{\"width\":\"200px\"},attrs:{\"disabled\":!_vm.checkList.includes(index + 1)},on:{\"blur\":_vm.zzxx},model:{value:(item.zjhm),callback:function ($$v) {_vm.$set(item, \"zjhm\", $$v)},expression:\"item.zjhm\"}})],1),_vm._v(\" \"),_c('div',[_vm._v(\"有效期:\"),_c('el-date-picker',{staticStyle:{\"width\":\"200px\"},attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"disabled\":!_vm.checkList.includes(index + 1)},model:{value:(item.yxq),callback:function ($$v) {_vm.$set(item, \"yxq\", $$v)},expression:\"item.yxq\"}})],1)],1)])}),0)])],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"本年度因私出国(境)次数\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\"},model:{value:(_vm.tjlist.cs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"cs\", $$v)},expression:\"tjlist.cs\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"前往国家(地区)\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\"},model:{value:(_vm.tjlist.qwgj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qwgj\", $$v)},expression:\"tjlist.qwgj\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"起止日期\"}},[_c('el-date-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(_vm.tjlist.value1),callback:function ($$v) {_vm.$set(_vm.tjlist, \"value1\", $$v)},expression:\"tjlist.value1\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"出国（境）事由\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\"},model:{value:(_vm.tjlist.sy),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sy\", $$v)},expression:\"tjlist.sy\"}})],1)],1)])])],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"同行人员情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscJtcyList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gx\",\"label\":\"与本人关系\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.gx),callback:function ($$v) {_vm.$set(scope.row, \"gx\", $$v)},expression:\"scope.row.gx\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xm\",\"label\":\"姓名\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.xm),callback:function ($$v) {_vm.$set(scope.row, \"xm\", $$v)},expression:\"scope.row.xm\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"nl\",\"label\":\"年龄\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"oninput\":\"value=value.replace(/[^\\\\d.]/g,'')\"},on:{\"blur\":function($event){scope.row.nl = $event.target.value},\"input\":_vm.handleInput},model:{value:(scope.row.nl),callback:function ($$v) {_vm.$set(scope.row, \"nl\", $$v)},expression:\"scope.row.nl\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzmm\",\"label\":\"政治面貌\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-select',{attrs:{\"placeholder\":\"请选择\"},model:{value:(scope.row.zzmm),callback:function ($$v) {_vm.$set(scope.row, \"zzmm\", $$v)},expression:\"scope.row.zzmm\"}},_vm._l((_vm.zzmmoptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zw\",\"label\":\"工作单位,职务及居住地\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.zw),callback:function ($$v) {_vm.$set(scope.row, \"zw\", $$v)},expression:\"scope.row.zw\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"140\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.czbtn1 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.cyjshgxAddRow(_vm.ryglRyscJtcyList)}}},[_vm._v(_vm._s(scope.row.czbtn1)+\"\\n            \")]):_vm._e(),_vm._v(\" \"),(scope.row.czbtn2 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.cyjshgxDelRow(scope.$index, _vm.ryglRyscJtcyList)}}},[_vm._v(_vm._s(scope.row.czbtn2)+\"\\n            \")]):_vm._e()]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"下载\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-third haveBorderTop\"},[_c('div',{staticClass:\"sec-left-text\"},[_c('p',[_vm._v(\"1.涉密人员出国（境）保密承诺书\")]),_vm._v(\" \"),_c('p',[_vm._v(\"2.涉密人员出国（境）行前保密教育情况表\")]),_vm._v(\" \"),_c('p',[_vm._v(\"3.涉密人员出国（境）回访记录表\")])]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":_vm.wdzlxz}},[_vm._v(\"下载\")])],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"上传扫描件\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-five haveBorderTop\",staticStyle:{\"position\":\"relative\"}},[_c('div',{staticClass:\"sec-left-text\"},[_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n            1.上传涉密人员出国（境）保密承诺书扫描件\\n            \"),_c('el-upload',{staticClass:\"upload-demo\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpRequest,\"show-file-list\":false}},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylth),expression:\"ylth\"}],staticStyle:{\"margin-left\":\"10px\"},attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylbmtxth}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogVisible},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n            2.上传涉密人员出国（境）行前保密教育情况表扫描件\\n            \"),_c('el-upload',{staticClass:\"upload-demo2\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpBmcnsRequest,\"show-file-list\":false}},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylcn),expression:\"ylcn\"}],staticStyle:{\"margin-left\":\"10px\"},attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylbmcns}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogBmcnsVisible},on:{\"update:visible\":function($event){_vm.dialogBmcnsVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogBmcnsImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogBmcnsVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n            3.上传涉密人员出国（境）回访记录表扫描件\\n            \"),_c('el-upload',{staticClass:\"upload-demo3\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpWtsRequest,\"show-file-list\":false}},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylwt),expression:\"ylwt\"}],staticStyle:{\"margin-left\":\"10px\"},attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylwts}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogWtsVisible},on:{\"update:visible\":function($event){_vm.dialogWtsVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogWtsImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogWtsVisible = false}}},[_vm._v(\"取 消\")])],1)])],1)])]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"plain\":\"\"},on:{\"click\":_vm.returnIndex}},[_vm._v(\"返回\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.chooseApproval}},[_vm._v(\"保存并提交\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.save}},[_vm._v(\"临时保存\")])],1)],1)],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"选择审批人\",\"close-on-click-modal\":false,\"visible\":_vm.approvalDialogVisible,\"width\":\"40%\",\"destroy-on-close\":true},on:{\"update:visible\":function($event){_vm.approvalDialogVisible=$event}}},[_c('div',{staticClass:\"dlFqsqContainer\"},[_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"部门:\")]),_vm._v(\" \"),_c('el-cascader',{ref:\"cascaderArr\",attrs:{\"options\":_vm.regionOption,\"props\":_vm.regionParams,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.bmSelectChange},model:{value:(_vm.ryChoose.bm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"bm\", $$v)},expression:\"ryChoose.bm\"}}),_vm._v(\" \"),_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"姓名:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"姓名\"},model:{value:(_vm.ryChoose.xm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"xm\", $$v)},expression:\"ryChoose.xm\"}}),_vm._v(\" \"),_c('el-button',{staticClass:\"searchButton\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchRy}},[_vm._v(\"查询\")]),_vm._v(\" \"),_c('BaseTable',{key:_vm.tableKey,staticClass:\"baseTable\",attrs:{\"tableHeight\":'300',\"showIndex\":true,\"tableData\":_vm.ryDatas,\"columns\":_vm.applyColumns,\"showSingleSelection\":true,\"handleColumn\":_vm.handleColumnApply,\"showPagination\":true,\"currentPage\":_vm.page,\"pageSize\":_vm.pageSize,\"totalCount\":_vm.total},on:{\"handleCurrentChange\":_vm.handleCurrentChangeRy,\"handleSizeChange\":_vm.handleSizeChangeRy,\"handleSelectionChange\":_vm.handleSelectionChange}})],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.approvalDialogVisible = false}}},[_vm._v(\"关 闭\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.saveAndSubmit}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-17207077\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/cgjscTable.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-17207077\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./cgjscTable.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./cgjscTable.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./cgjscTable.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-17207077\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./cgjscTable.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-17207077\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/cgjscTable.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}