<template>
    <div class="sec-container">
        <el-tabs v-model="activeName">
            <el-tab-pane label="审批指南" name="first">
                <div class="sec-form-six haveBorderTop sec-footer">
                    <el-button @click="ljbl" class="fr" type="success">立即办理</el-button>
                </div>
                <el-table border class="sec-el-table" :data="spznList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                    <el-table-column prop="hjmc" label="办理流程"></el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="审批信息" name="second">
                <!-- 标题 -->
                <p class="sec-title">基本信息</p>
                <div class="sec-form-container">
                    <el-form ref="formName" :model="tjlist" label-width="225px">
                        <!-- 第一部分包括姓名到常住地公安start -->
                        <div class="sec-header-section">
                            <div class="sec-form-left">
                                <el-form-item label="姓名">
                                    <el-input placeholder="" v-model="tjlist.xm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="政治面貌">
                                    <template slot-scope="scope">
                                        <!-- <p class="hyzk" v-if="tjlist.zzmm == 1">中共党员</p>
                <p class="hyzk" v-if="tjlist.zzmm == 2">团员</p>
                <p class="hyzk" v-if="tjlist.zzmm == 3">民主党派</p>
                <p class="hyzk" v-if="tjlist.zzmm == 4">群众</p> -->
                                        <el-input placeholder="" v-model="tjlist.zzmm" clearable disabled></el-input>
                                    </template>
                                </el-form-item>
                                <el-form-item label="出生年月">
                                    <el-input placeholder="" v-model="tjlist.csny" clearable></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="所在部门">
                                    <el-input placeholder="" v-model="tjlist.bmmc" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="职务（职称）">
                                    <el-input placeholder="" v-model="tjlist.zw" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="联系电话">
                                    <el-input placeholder="" v-model="tjlist.lxdh" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="涉密岗位">
                                    <el-input placeholder="" v-model="tjlist.gwmc" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="涉密等级">
                                    <el-input placeholder="" v-model="tjlist.smdj" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="初步进入涉密岗位日期">
                                    <el-input placeholder="" v-model="tjlist.sgsj" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left sec-form-left1">
                                <el-form-item label="流动方式">
                                    <template slot-scope="scope">
                                        <div style="border-right: 1px solid #CDD2D9;">
                                            <!-- <el-radio v-model="tjlist.ldfs" label="1" style="padding-left: 10px;"
                                                disabled>离职</el-radio>
                                            <el-radio v-model="tjlist.ldfs" label="0" disabled>离岗</el-radio>
                                            <el-radio v-model="tjlist.ldfs" label="2" disabled>退休</el-radio>
                                       
                                            <el-radio v-model="tjlist.ldfs" label="3" disabled>其他</el-radio> -->
                                            <el-radio v-model="tjlist.ldfs" label="1"
                                                style="padding-left: 10px;">本单位其他非涉密岗</el-radio>
                                            <el-radio v-model="tjlist.ldfs" label="2">其他机关、单位</el-radio>
                                            <el-radio v-model="tjlist.ldfs" label="3">民营资质企业</el-radio>
                                            <el-radio v-model="tjlist.ldfs" label="4"
                                                style="padding-left: 10px;">其他</el-radio>
                                            <el-radio v-model="tjlist.ldfs" label="5">退休</el-radio>
                                        </div>
                                    </template>
                                </el-form-item>
                                <el-form-item label="拟进入部门或单位">
                                    <el-input placeholder="" v-model="tjlist.qxdw" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                        </div>

                        <!-- 主要学习及工作经历start -->
                        <p class="sec-title">主要学习及工作经历</p>
                        <el-table border class="sec-el-table" :data="ryglRyscSwzjList.slice(0, 4)"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="zjmc" label="设备名称"></el-table-column>
                            <el-table-column prop="cyqk" label="设备情况">
                                <template slot-scope="scope">
                                    <div style="display: flex;
    align-items: center;">
                                        <el-radio v-model="scope.row.cyqk" label="1" disabled>是</el-radio>
                                        <el-radio v-model="scope.row.cyqk" label="0" disabled>否</el-radio>
                                        <p>已收回</p>
                                    </div>

                                </template>
                            </el-table-column>
                            <!-- <el-table-column prop="yxq" label="有效期">
            <template slot-scope="scope">
              <el-date-picker v-model="scope.row.yxq" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
              </el-date-picker>
            </template>
          </el-table-column> -->
                        </el-table>
                        <!-- 主要学习及工作经历end -->
                        <!-- 家庭成员及主要社会关系情况start -->
                        <!-- 家庭成员及主要社会关系情况start -->
                        <p class="sec-title">脱密期管理</p>
                        <div class="sec-form-third haveBorderTop">
                            <div class="sec-left-text">
                                <div>1.脱密期限为： <el-date-picker v-model="tjlist.value1" type="daterange"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                        style="border-right: 0;" disabled>
                                    </el-date-picker>
                                </div>
                                <div style="display: flex;align-items: center;" class="brno">2.脱密期管理委托单位：<el-input
                                        v-model="tjlist.tmqgldw" style="width: 150px;" disabled></el-input> </div>

                            </div>
                        </div>
                        <!-- 家庭成员及主要社会关系情况end -->
                        <div class="sec-form-five haveBorderTop" style="position: relative;">
                            <div class="sec-left-text">
                                <div class="flex">
                                    1.上传保密提醒谈话确认扫描件
                                    <el-button class="upload-demo" v-show="ylth" size="mini" type="primary"
                                        @click="ylbmtxth">预览</el-button>
                                    <el-dialog :visible.sync="dialogThtxVisible">
                                        <img :src="dialogImageUrl" alt="" style="width: 100%">
                                        <div slot="footer" class="dialog-footer">
                                            <el-button size="small" @click="dialogThtxVisible = false">取 消</el-button>
                                        </div>
                                    </el-dialog>
                                </div>
                                <div class="flex">
                                    2.上传离岗离职涉密人员保密承诺书扫描件
                                    <el-button class="upload-demo2" v-show="ylcn" size="mini" type="primary"
                                        @click="ylbmcns">预览</el-button>
                                    <el-dialog :visible.sync="dialogBmcnsVisible">
                                        <img :src="dialogBmcnsImageUrl" alt="" style="width: 100%">
                                        <div slot="footer" class="dialog-footer">
                                            <el-button size="small" @click="dialogBmcnsVisible = false">取 消</el-button>
                                        </div>
                                    </el-dialog>
                                </div>
                                <div class="flex">
                                    3.上传脱密期委托管理书扫描件
                                    <el-button class="upload-demo3" v-show="ylwt" size="mini" type="primary"
                                        @click="ylwts">预览</el-button>
                                    <el-dialog :visible.sync="dialogWtsVisible">
                                        <img :src="dialogWtsImageUrl" alt="" style="width: 100%">
                                        <div slot="footer" class="dialog-footer">
                                            <el-button size="small" @click="dialogWtsVisible = false">取 消</el-button>
                                        </div>
                                    </el-dialog>
                                </div>
                                <div class="flex">
                                    4.上传脱密期委托/协助管理涉密人员基本信息表扫描件
                                    <el-button class="upload-demo4" v-show="ylxz" size="mini" type="primary"
                                        @click="ylryxx">预览</el-button>
                                    <el-dialog :visible.sync="dialogRyxxVisible">
                                        <img :src="dialogRyxxImageUrl" alt="" style="width: 100%">
                                        <div slot="footer" class="dialog-footer">
                                            <el-button size="small" @click="dialogRyxxVisible = false">取 消</el-button>
                                        </div>
                                    </el-dialog>
                                </div>
                                <div class="flex">
                                    5.上传脱密期协助管理告知函描件
                                    <el-button class="upload-demo5" v-show="ylgz" size="mini" type="primary"
                                        @click="ylxzgl">预览</el-button>
                                    <el-dialog :visible.sync="dialogXzglVisible">
                                        <img :src="dialogXzglImageUrl" alt="" style="width: 100%">
                                        <div slot="footer" class="dialog-footer">
                                            <el-button size="small" @click="dialogXzglVisible = false">取 消</el-button>
                                        </div>
                                    </el-dialog>
                                </div>
                            </div>
                        </div>
                        <!-- 移居国(境)外情况start -->
                        <p class="sec-title">所在部门意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmsc">
                                <el-radio v-model="tjlist.bmsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    :disabled="disabled1" :key="item.id">{{
            item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="离职离岗" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门领导审批人" prop="bmspr">
                                <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
                                <el-input placeholder="" v-model="tjlist.bmscxm" clearable
                                    :disabled="disabled1"></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmscrq">
                                <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
                                <el-date-picker :disabled="disabled1" v-model="tjlist.bmscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">保密办意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmsc">
                                <el-radio v-model="tjlist.bmbsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    :disabled="disabled2" :key="item.id">{{
            item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="离职离岗" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门领导审批人" prop="bmspr">
                                <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
                                <el-input placeholder="" v-model="tjlist.bmbscxm" clearable
                                    :disabled="disabled2"></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmscrq">
                                <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
                                <el-date-picker :disabled="disabled2" v-model="tjlist.bmbscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">人力资源部意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmsc">
                                <el-radio v-model="tjlist.rlsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    :disabled="disabled3" :key="item.id">{{
            item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="离职离岗" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门领导审批人" prop="bmspr">
                                <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
                                <el-input placeholder="" v-model="tjlist.rlscxm" clearable
                                    :disabled="disabled2"></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmscrq">
                                <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
                                <el-date-picker :disabled="disabled3" v-model="tjlist.rlscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">保密工作领导小组意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmsc">
                                <el-radio v-model="tjlist.bmgzldxzsc" v-for="item in scqk" :label="item.id"
                                    @change="chRadio" :disabled="disabled4" :key="item.id">{{
            item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="离职离岗" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门领导审批人" prop="bmspr">
                                <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
                                <el-input placeholder="" disabled v-model="tjlist.bmgzldxzscxm" clearable
                                    :disabled="disabled4"></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmscrq">
                                <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
                                <el-date-picker :disabled="disabled4" v-model="tjlist.bmgzldxzscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <!-- <p class="sec-title">备注：涉密人员上岗审查、在岗复审均填本表</p> -->

                        <p class="sec-title">轨迹处理</p>
                        <el-table border class="sec-el-table" :data="gjclList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                            <el-table-column prop="clrid" label="办理人"></el-table-column>
                            <el-table-column prop="bllx" label="办理类型"></el-table-column>
                            <el-table-column prop="clyj" label="办理意见"></el-table-column>
                            <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                            <el-table-column prop="clsj" label="办理时间"></el-table-column>
                        </el-table>

                        <!-- 底部操作按钮start -->
                        <div class="sec-form-six haveBorderTop sec-footer">
                            <el-dropdown class="fr ml10">
                                <!-- <el-button type="primary" :disabled="btnsfth">退回</el-button> -->
                                <el-button type="primary">退回</el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item @click.native="save(3)">至上步办理人</el-dropdown-item>
                                    <el-dropdown-item @click.native="save(2)">至发起人</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <!-- <el-button @click="save(1)" :disabled="btnsftg" class="fr" type="success">通过</el-button> -->
                            <el-button @click="save(1)" class="fr" :disabled="tgdis" type="success">通过</el-button>
                        </div>
                        <!-- 底部操作按钮end -->
                    </el-form>
                </div>
                <!-- 涉密人员任用审查列表end -->
                <!-- 发起申请弹框start -->
                <el-dialog title="人员选择" :close-on-click-modal="false" :visible.sync="dialogVisible" width="40%">
                    <div class="dlFqsqContainer">
                        <label for="">部门:</label>
                        <el-input class="input1" v-model="formInline.bmmc" clearable placeholder="部门"></el-input>
                        <label for="">姓名:</label>
                        <el-input class="input2" v-model="formInline.xm" clearable placeholder="姓名"></el-input>
                        <el-button class="searchButton" type="primary" icon="el-icon-search"
                            @click="onSubmit">查询</el-button>
                        <el-table class="tb-container" ref="multipleTable" :data="smryList" border
                            :header-cell-style="headerCellStyle" stripe @selection-change="selectRow"
                            @select="handleSelect" @row-click="handleRowClick" height="300px">
                            <el-table-column type="selection" width="55" align="center"> </el-table-column>
                            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                            <el-table-column prop="xm" label="姓名"></el-table-column>
                            <el-table-column prop="bmmc" label="部门"></el-table-column>
                            <el-table-column prop="gwmc" label="岗位"></el-table-column>
                        </el-table>
                        <el-pagination class="paginationContainer" background @current-change="handleCurrentChange"
                            @size-change="handleSizeChange" :pager-count="5" :current-page="page"
                            :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                            layout="total, prev, pager, sizes,next, jumper" :total="total">
                        </el-pagination>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" v-if="xsyc" @click="submit('formName')">确 定</el-button>
                        <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
                    </span>
                </el-dialog>
                <!-- 发起申请弹框end -->
            </el-tab-pane>
            <el-tab-pane label="流程跟踪" name="third">
                <el-table border class="sec-el-table" :data="lcgzList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                    <el-table-column prop="clrid" label="办理人"></el-table-column>
                    <el-table-column prop="bllx" label="办理类型"></el-table-column>
                    <el-table-column prop="clyj" label="办理意见"></el-table-column>
                    <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                    <el-table-column prop="clsj" label="办理时间"></el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>

    </div>
</template>
<script>
import {
    //审批指南
    getBlzn,
    //审批信息
    getRyscInfoBySlid,
    //审批信息
    getZgfsInfoBySlid,
    //审批信息
    getLzlgInfoBySlid,
    //判断实例所处环节
    getSchj,
    //事项审核
    getSxsh,
    //查询审批用户列表
    getSpUserList,
    //非第一环节选择审批人
    tjclr,
    //修改任用审查详情记录
    updateRysc,
    updateLzlg,
    updateZgfs,
    //流程跟踪
    getSpGjxx,
} from '../../../../api/wdgz'
import { getZpBySmryid } from '../../../../api/index'
import { getUserInfo } from '../../../../api/dwzc'

import AddLineTable from "../../../components/common/addLineTable.vue";   //人工纠错组件
export default {
    components: {
        AddLineTable
    },
    props: {},
    data() {
        return {
            // table 行样式
            headerCellStyle: {
                background: '#EEF7FF',
                color: '#4D91F8'
            },
            fwdyid: '',
            slid: '',
            activeName: 'second',
            //审批指南
            spznList: [],
            // form表单提交数据
            // 持有因公出入境证件情况
            ryglRyscSwzjList: [{
                'zjmc': '涉密载体（含纸质、光盘等）',
                'fjlb': 1,
                'cyqk': '0',
                'zjhm': '',
                'yxq': '',
                'qzmc': '部门保密员核定签字：'
            }, {
                'zjmc': '信息设备（含计算机、存储介质等）',
                'fjlb': 2,
                'cyqk': '0',
                'zjhm': '',
                'yxq': '',
                'qzmc': '部门保密员核定签字：'
            }, {
                'zjmc': '涉密信息系统访问权限回收情况',
                'fjlb': 3,
                'cyqk': '0',
                'zjhm': '',
                'yxq': '',
                'qzmc': '系统管理员(三员)核定签字：'
            }, {
                'zjmc': '涉密场所出入权限回收情况',
                'fjlb': 4,
                'cyqk': '0',
                'zjhm': '',
                'yxq': '',
                'qzmc': '涉密场所管理员核定签字：  '
            }],
            //审批信息
            tjlist: {
                cnsrq: '',
                bmscrq: '',
                rlscrq: '',
                bmbscrq: '',
                // 主要学习及工作经历
                xxjlList: [],
                // 家庭成员及社会关系
                cyjshgxList: [],
                // 持有因公出入境证件情况
                ygrjzjqkList: [],
                // 持有因私出入境证件情况
                ysrjzjqkList: [],
                // 因私出国(境)情况
                yscgqkList: [],
                // 接受境外资助情况
                jsjwzzqkList: [],
                // 处分或者违法犯罪情况
                clhwffzqkList: [],
                value1: [],
            },
            //轨迹处理
            gjclList: [],
            upccLsit: {},
            //判断实例所处环节
            disabled1: false,
            disabled2: false,
            disabled3: false,
            disabled4: false,
            btnsftg: true,
            btnsfth: true,
            yldis: false,
            jgyf: '',
            //性别
            xb: [{
                xb: '男',
                id: 1
            },
            {
                xb: '女',
                id: 2
            },
            ],
            //移居国(境)外情况
            yjgwqk: [{
                yw: '有',
                id: 1
            },
            {
                yw: '无',
                id: 0
            },
            ],
            //上岗保密教育、签订保密承诺书
            bmjysfwc: [
                {
                    sfwc: '已完成',
                    id: 1
                },
                {
                    sfwc: '未完成',
                    id: 0
                },
            ],
            scqk: [
                {
                    sfty: '同意',
                    id: 1
                },
                {
                    sfty: '不同意',
                    id: 0
                },
            ],
            // 政治面貌下拉选项
            zzmmoptions: [],
            sltshow: '', // 文档的缩略图显示
            sltbmcnsshow: '', // 文档的缩略图显示
            sltwtsshow: '', // 文档的缩略图显示
            sltryxxshow: '', // 文档的缩略图显示
            sltxzglshow: '', // 文档的缩略图显示
            fileList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            dialogThtxVisible: false,
            dialogBmcnsImageUrl: '',
            dialogBmcnsVisible: false,
            dialogWtsImageUrl: '',
            dialogWtsVisible: false,
            dialogRyxxImageUrl: '',
            dialogRyxxVisible: false,
            dialogXzglImageUrl: '',
            dialogXzglVisible: false,
            fileRow: '',
            filebmcnsRow: '',
            filewtsRow: '',
            fileryxxRow: '',
            filexzglRow: '',
            //人员任用
            smryList: [],
            page: 1,
            pageSize: 10,
            total: 0,
            formInline: {
                'bmmc': '',
                'xm': ''
            }, // 搜索条件
            selectlistRow: [], //列表的值
            xsyc: true,
            mbhjid: '',
            imageUrl: '',
            imageUrlbrcn: '',
            ylxy: true,
            file: {},
            bmcnssmj: '',
            bmxyssmj: '',
            //本人承诺
            dialogVisible_brcn: false,
            //保密承诺书预览
            dialogVisible_bmcns: false,
            bmcnsImageUrl: '',
            //保密承诺书预览
            dialogVisible_bmxys: false,
            bmxysImageUrl: '',
            //审批状态码 1 2 3 4
            zplcztm: '',
            //上传扫描件按钮显示隐藏
            show: true,
            show1: true,
            xm: '',
            //通过
            tgdis: false,
            ylth: false,
            ylcn: false,
            ylwt: false,
            ylxz: false,
            ylgz: false,
            //流程跟踪
            lcgzList: [],
        }
    },
    computed: {},
    mounted() {
        this.getNowTime()
        // let date = new Date()
        // console.log(date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + (date.getDate()));
        console.log(this.$route.query.list);
        this.fwdyid = this.$route.query.fwdyid
        console.log("this.fwdyid", this.fwdyid);
        this.slid = this.$route.query.slid
        console.log("this.slid", this.slid);
        this.dqlogin()
        setTimeout(() => {
            this.pdschj()
        }, 500)
        // return
        //审批指南初始化列表
        this.spzn()
        //审批信息初始化列表
        this.spxxxgcc()
        this.spxx()
        //判断实例所处环节
        // //事项审核
        // this.sxsh()
        //初始化el-dialog列表数据
        this.splist()

        //流程跟踪初始化列表
        this.lcgz()

    },
    methods: {
        getNowTime() {
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            console.log(defaultDate)
            return defaultDate;
            this.$set(this.info, "stockDate", defaultDate);
        },
        //当前登录用户
        async dqlogin() {
            let data = await getUserInfo()
            this.xm = data.xm
        },
        //审批指南
        //审批指南初始化列表
        async spzn() {
            let params = {
                fwdyid: this.fwdyid,
            }
            let data = await getBlzn(params)
            if (data.code == 10000) {
                this.spznList = data.data.content
            }
        },
        //审批信息
        //审批信息初始化数据
        async spxxxgcc() {
            let params = {
                lcslid: this.slid
            }
            let data = await getLzlgInfoBySlid(params)
            this.upccLsit = data
            console.log('this.upccLsit', this.upccLsit);
            this.chRadio()
            this.xzbmcns()
            this.xzbmxys()
        },
        sjcf(val) {
            console.log(val)

            console.log(this.tjlist.cnsrq);
            console.log(typeof (this.tjlist.cnsrq));
        },
        async spxx() {
            let params = {
                lcslid: this.slid
            }
            let data;
            let lbxx = {};
            let ryglScjlList = [];
            let ryglJtcyList = [];
            let ryglSwzjList = [];
            let ryglYccgList = [];
            let ryglJwzzqkList = [];
            let ryglCfjlList = [];
            data = await getLzlgInfoBySlid(params);
            this.tjlist = data
            this.tjlist.value1 = []
            this.ryglRyscSwzjList[0].cyqk = data.smztsfqt.toString()
            this.ryglRyscSwzjList[1].cyqk = data.xtqxsfhs.toString()
            this.ryglRyscSwzjList[2].cyqk = data.xxsbsfqt.toString()
            this.ryglRyscSwzjList[3].cyqk = data.csqxsfhs.toString()
            this.tjlist.value1.push(data.tmqssj)
            this.tjlist.value1.push(data.tmjssj)
            console.log("this.tjlistthis.tjlistthis.tjlistthis.tjlistthis.tjlistthis.tjlist", this.tjlist);
            let zp = await getZpBySmryid({ smryid: this.tjlist.smryid })
            const iamgeBase64 = "data:image/jpeg;base64," + zp;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.imageUrl = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            if (this.zplcztm == 1) {
                this.tjlist.bmscxm = this.xm
                console.log(this.getNowTime())
                console.log(defaultDate)
                // this.$nextTick(function () {
                this.$set(this.tjlist, 'bmscsj', defaultDate)
                // this.tjlist.cnsrq = defaultDate //输出：修改后的值
                // });

                // this.tjlist.cnsrq = new Date()
            } else if (this.zplcztm == 2) {
                this.tjlist.rlspr = this.tjlist.rlspr
                this.tjlist.bmbscxm = this.xm
                this.$set(this.tjlist, 'bmbscsj', defaultDate)
                // this.tjlist.bmscrq = this.getNowTime()
            } else if (this.zplcztm == 3) {
                this.tjlist.rlspr = this.tjlist.rlspr
                this.tjlist.bmspr = this.tjlist.bmspr
                this.tjlist.rlscxm = this.xm
                this.$set(this.tjlist, 'rlscsj', defaultDate)
                // this.tjlist.rlscrq = this.getNowTime()
            } else if (this.zplcztm == 4) {
                this.tjlist.rlspr = this.tjlist.rlspr
                this.tjlist.bmspr = this.tjlist.bmspr
                this.tjlist.rlldspr = this.tjlist.rlldspr
                this.tjlist.bmgzldxzscxm = this.xm
                this.$set(this.tjlist, 'bmgzldxzscsj', defaultDate)
                // this.tjlist.bmbscrq = this.getNowTime()
            }
            if (this.tjlist.txthsmj != '') {
                this.ylth = true
            }
            if (this.tjlist.bmcnssmj != '') {
                this.ylcn = true
            }
            if (this.tjlist.wtssmj != '') {
                this.ylwt = true
            }
            if (this.tjlist.ryjbxxbsmj != '') {
                this.ylxz = true
            }
            if (this.tjlist.wtsfjsmj != '') {
                this.ylgz = true
            }
            if (this.tjlist.xb == 1) {
                this.tjlist.xb = '男'
            } else if (this.tjlist.xb == 2) {
                this.tjlist.xb = '女'
            }
            if (this.tjlist.hyzk == 1) {
                this.tjlist.hyzk = '已婚'
            } else if (this.tjlist.hyzk == 0) {
                this.tjlist.hyzk = '未婚'
            }
            if (this.tjlist.zzmm == 1) {
                this.tjlist.zzmm = '中共党员'
            } else if (this.tjlist.zzmm == 2) {
                this.tjlist.zzmm = '团员'
            } else if (this.tjlist.zzmm == 3) {
                this.tjlist.zzmm = '民主党派'
            } else if (this.tjlist.zzmm == 4) {
                this.tjlist.zzmm = '群众'
            }
            if (this.tjlist.smdj == 1) {
                this.tjlist.smdj = '核心'
            } else if (this.tjlist.smdj == 2) {
                this.tjlist.smdj = '重要'
            } else if (this.tjlist.smdj == 3) {
                this.tjlist.smdj = '一般'
            }
            //主要学习及工作经历
            this.tjlist.xxjlList = ryglScjlList
            //家庭成员及主要社会关系情况
            let jtarr = []
            ryglJtcyList.forEach((item) => {
                if (item.jwjlqk == 0) {
                    item.jwjlqk = '否'
                } else if (item.jwjlqk == 1) {
                    item.jwjlqk = '是'
                }
                jtarr.push(item)
                this.tjlist.cyjshgxList = jtarr
            })
            //持有因公出入境证件情况
            let arr = []
            let list = []
            ryglSwzjList.forEach((item) => {
                if (item.cyqk == 0) {
                    item.cyqk = '无'
                } else if (item.cyqk == 1) {
                    item.cyqk = '有'
                }
                if (item.fjlb == 1 || item.fjlb == 2 || item.fjlb == 3) {
                    if (item.fjlb == 1) {
                        item.fjlb = '护照'
                    } else if (item.fjlb == 2) {
                        item.fjlb = '港澳通行证'
                    } else if (item.fjlb == 3) {
                        item.fjlb = '台湾通行证'
                    }
                    arr.push(item)
                    this.tjlist.ygrjzjqkList = arr
                }
                //持有因公出入境证件情况
                else if (item.fjlb == 4 || item.fjlb == 5 || item.fjlb == 6 || item.fjlb == 7) {
                    if (item.fjlb == 4) {
                        item.fjlb = '护照'
                    } else if (item.fjlb == 5) {
                        item.fjlb = '港澳通行证'
                    } else if (item.fjlb == 6) {
                        item.fjlb = '台湾通行证'
                    } else if (item.fjlb == 7) {
                        item.fjlb = '境外永久居留权长期居留许可证件'
                    }
                    list.push(item)
                    this.tjlist.ysrjzjqkList = list
                }
            })
            //因私出国(境)情况
            this.tjlist.yscgqkList = ryglYccgList
            //接受境外资助情况
            this.tjlist.jsjwzzqkList = ryglJwzzqkList
            //处分或者违法犯罪情况
            this.tjlist.clhwffzqkList = ryglCfjlList

        },
        // 预览
        yulan() {
            this.dialogVisible_brcn = true
            // this.ylxy = false
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.brcn;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.imageUrlbrcn = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        zpzm(zp) {
            const iamgeBase64 = "data:image/jpeg;base64," + zp;
            let zpxx
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    // let that = this;

                    function previwImg(item) {
                        zpxx = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
            return zpxx
        },
        // 预览
        ylbmtxth() {
            let zpxx
            console.log(this.routeType)
            zpxx = this.zpzm(this.tjlist.txthsmj)
            this.dialogImageUrl = zpxx
            this.dialogThtxVisible = true
        },
        // 预览
        ylbmcns() {
            let zpxx
            zpxx = this.zpzm(this.tjlist.bmcnssmj)
            this.dialogBmcnsImageUrl = zpxx
            this.dialogBmcnsVisible = true
        },
        // 预览
        ylwts() {
            let zpxx
            console.log(this.routeType)
            zpxx = this.zpzm(this.tjlist.wtssmj)
            this.dialogWtsImageUrl = zpxx
            this.dialogWtsVisible = true
        },
        // 预览
        ylryxx() {
            let zpxx
            console.log(this.routeType)
            zpxx = this.zpzm(this.tjlist.ryjbxxbsmj)
            this.dialogRyxxImageUrl = zpxx
            this.dialogRyxxVisible = true
        },
        // 预览
        ylxzgl() {
            let zpxx
            console.log(this.routeType)
            zpxx = this.zpzm(this.tjlist.wtsfjsmj)
            this.dialogXzglImageUrl = zpxx
            this.dialogXzglVisible = true
        },
        // 删除
        shanchu() {
            this.tjlist.brcn = ''
            this.sltshow = ''
        },
        chRadio(val) {

        },
        xzbmcns(val) {

        },
        xzbmxys(val) {

        },
        // 通过
        async save(index) {
            let jgbz = index
            if (jgbz == 1) {
                let params = {
                    rwid: this.tjlist.rwid
                }
                if (this.zplcztm == 1) {
                    if (this.tjlist.bmsc != undefined) {
                        if (this.tjlist.bmscsj != undefined) {
                            params.bmsc = this.tjlist.bmsc;
                            params.bmscxm = this.tjlist.bmscxm;
                            params.bmscsj = this.tjlist.bmscsj;
                        } else {
                            this.$message.warning('请选择日期')
                            return
                        }
                    } else {
                        this.$message.warning('是否同意')
                        return
                    }

                } else if (this.zplcztm == 2) {
                    if (this.tjlist.bmbsc != undefined) {
                        if (this.tjlist.bmbscsj != undefined) {
                            params.bmbsc = this.tjlist.bmbsc;
                            params.bmbscxm = this.tjlist.bmbscxm;
                            params.bmbscsj = this.tjlist.bmbscsj;
                        } else {
                            this.$message.warning('请选择日期')
                            return
                        }
                    } else {
                        this.$message.warning('是否同意')
                        return
                    }

                } else if (this.zplcztm == 3) {
                    if (this.tjlist.rlsc != undefined) {
                        if (this.tjlist.rlscsj != undefined) {
                            params.rlsc = this.tjlist.rlsc;
                            params.rlscxm = this.tjlist.rlscxm;
                            params.rlscsj = this.tjlist.rlscsj;
                        } else {
                            this.$message.warning('请选择日期')
                            return
                        }
                    } else {
                        this.$message.warning('是否同意')
                        return
                    }

                } else if (this.zplcztm == 4) {
                    if (this.tjlist.bmgzldxzsc != undefined) {
                        if (this.tjlist.bmgzldxzscsj != undefined) {
                            params.bmgzldxzsc = this.tjlist.bmgzldxzsc;
                            params.bmgzldxzscxm = this.tjlist.bmgzldxzscxm;
                            params.bmgzldxzscsj = this.tjlist.bmgzldxzscsj;
                        } else {
                            this.$message.warning('请选择日期')
                            return
                        }
                    } else {
                        this.$message.warning('是否同意')
                        return
                    }

                }
                console.log(params);
                let data = await updateLzlg(params)
                if (data.code == 10000) {
                    // if (jgbz == 1) {
                    this.jgyf = 1
                    // }
                    this.sxsh()
                    this.spxx()
                }
                this.tgdis = true


            }
            else if (jgbz == 2) {
                this.jgyf = 2
                this.sxsh()
                this.spxx()
            } else if (jgbz == 3) {
                this.jgyf = 3
                this.sxsh()
                this.spxx()
            }
        },
        //立即办理
        ljbl() {
            this.activeName = 'second'
        },
        //判断实例所处环节
        async pdschj() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSchj(params)
            this.zplcztm = data.data.content
            if (data.code == 10000) {
                if (data.data.content == 1) {
                    console.log(this.xm);
                    this.tjlist.bmscxm = this.xm
                    this.disabled2 = true
                    this.disabled3 = true
                    this.disabled4 = true
                }
                if (data.data.content == 2) {
                    this.tjlist.bmbscxm = this.xm
                    this.disabled1 = true
                    this.disabled3 = true
                    this.disabled4 = true
                }
                if (data.data.content == 3) {
                    this.tjlist.rlscxm = this.xm
                    this.disabled1 = true
                    this.disabled2 = true
                    this.disabled4 = true
                }
                if (data.data.content == 4) {
                    this.tjlist.bmgzldxzscxm = this.xm
                    this.disabled1 = true
                    this.disabled2 = true
                    this.disabled3 = true
                }
            }
        },
        //事项审核
        async sxsh() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                jg: this.jgyf,
                smryid: this.tjlist.smryid
            }
            let data = await getSxsh(params)
            if (data.code == 10000) {
                this.tgdis = false
                if (data.data.zt == 0) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // this.smryList = data.data.blrarr
                    this.mbhjid = data.data.mbhjid
                    this.splist()
                    this.dialogVisible = true
                } else if (data.data.zt == 1) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 2) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 3) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
                else if (data.data.zt == 4) {
                    this.$message({
                        message: data.data.msg
                    });
                    console.log(1111111111111);
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
            }
        },
        //初始化el-dialog列表数据
        async splist() {
            let params = {
                fwdyid: this.fwdyid,
                'xm': this.formInline.xm,
                'bmmc': this.formInline.bmmc,
                page: this.page,
                pageSize: this.pageSize,
                qshjid: this.mbhjid,
            }
            let data = await getSpUserList(params)
            this.smryList = data.records
            this.total = data.total


        },
        onSubmit() {
            this.splist()
        },
        selectRow(selection) {
            if (selection.length <= 1) {
                console.log('点击选中数据：', selection);
                this.selectlistRow = selection
                this.xsyc = true
            } else if (selection.length > 1) {
                this.$message.warning('只能选中一条数据')
                this.xsyc = false
            }

        },
        handleSelect(selection, val) {
            //只能选择一行，选择其他，清除上一行
            if (selection.length > 1) {
                let del_row = selection.shift()
                this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中
            }
        },
        // 点击行触发，选中或不选中复选框
        handleRowClick(row, column, event) {
            this.$refs.multipleTable.toggleRowSelection(row)
            this.selectChange(this.selectlistRow)
        },
        async submit() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                shry: this.selectlistRow[0].yhid,
                mbhjid: this.mbhjid,
            }
            let data = await tjclr(params)
            if (data.code == 10000) {
                this.$message({
                    message: data.message,
                    type: 'success'
                });
                this.dialogVisible = false
                setTimeout(() => {
                    this.$router.push('/dbsx')
                }, 500)
            }
        },
        //上传文件
        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isPNG = file.type === 'image/png';
            if (!isJPG && !isPNG) {
                this.$message.error('上传缩略图只能是 JPG/PNG 格式!');
            }
            return isJPG || isPNG;
        },
        // 64码
        blobToBase64(blob, callback) {
            const fileReader = new FileReader();
            fileReader.onload = (e) => {
                callback(e.target.result);
            };
            fileReader.readAsDataURL(blob);
        },
        //保密承诺书预览
        bmcnsyl() {
            this.dialogVisible_bmcns = true
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.cnssmj;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.bmcnsImageUrl = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        //
        bmxysyl() {
            this.dialogVisible_bmxys = true
            const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.xyssmj;
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    let that = this;

                    function previwImg(item) {
                        that.bmxysImageUrl = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
        },
        //列表分页--跳转页数
        handleCurrentChange(val) {
            this.page = val
            this.splist()
        },
        //列表分页--更改每页显示个数
        handleSizeChange(val) {
            this.page = 1
            this.pageSize = val
            this.splist()
        },
        //流程跟踪
        //流程跟踪初始化列表
        async lcgz() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSpGjxx(params)
            if (data.code == 10000) {
                this.lcgzList = data.data.content
                this.gjclList = data.data.content
                console.log(this.gjclList);
            }
        },
    },
    watch: {

    }
}

</script>

<style scoped>
.sec-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: overlay;
}

.sec-title {
    border-left: 5px solid #1b72d8;
    color: #1b72d8;
    font-size: 20px;
    font-weight: 700;
    text-indent: 10px;
    margin-bottom: 20px;
    margin-top: 10px;
}

.sec-form-container {
    width: 100%;
    height: 100%;
}

.sec-form-left {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    /* height: 40px; */
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
}

.sec-form-left:not(:first-child) {
    border-top: 0;
}

.sec-form-left .el-form-item {
    float: left;
    width: 100%;
}

.sec-header-section {
    width: 100%;
    position: relative;
}

.tb-container {
    height: 300px;
    /* overflow-y: scroll; */
}



.sec-header-pic {
    width: 258px;
    position: absolute;
    right: 0px;
    top: 0;
    height: 245px;
    border: 1px solid #CDD2D9;
    border-left: 0;
    background: #ffffff;
}

.sec-header-flex {
    display: flex;
    align-items: center;
    justify-content: center;
}

.sec-header-mar {
    margin-right: 10px;
}

.sec-form-second {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
    border-top: 0;
    position: relative;
}

.sec-form-fddw {
    height: 100%;
    position: absolute;
    top: 0;
    right: 40%;
}

.sec-form-third {
    border: 1px solid #CDD2D9;
    /* height: 40px;  */
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-four {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-five {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    overflow: hidden;
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.yulan {
    text-align: center;
    cursor: pointer;
    color: #3874D5;
    font-weight: 600;
    float: left;
    margin-left: 10px;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 2px solid #EBEBEB;
}

.sec-form-six {
    border: 1px solid #CDD2D9;
    overflow: hidden;
    background: #ffffff;
    padding: 10px;
}

.ml10 {
    margin-left: 10px;
}

.sec-footer {
    margin-top: 10px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

.flex {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.upload-demo {
    margin-left: 217px;
}

.upload-demo2 {
    margin-left: 137px;
}

.upload-demo3 {
    margin-left: 217px;
}

.upload-demo4 {
    margin-left: 49px;
}

.upload-demo5 {
    margin-left: 201px;
}

>>>.sec-form-four .el-textarea__inner {
    border: none;
}

.sec-left-text {
    float: left;
    margin-right: 130px;
}

.haveBorderTop {
    border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
    width: 500px !important;
}

>>>.longLabel .el-form-item__content {
    margin-left: 500px !important;
    padding-left: 20px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

.gtzzsmgwgz {
    border-right: 1px solid #CDD2D9;
}

.hyzk {
    padding-left: 15px;
    background-color: #F5F7FA;
    width: calc(100% - 16px);
    border-right: 1px solid #CDD2D9;
    color: #000;
}

.gtzzsmgwgz>>>.el-form-item__content {
    display: none !important;
}

.gtzzsmgwgz>>>.el-form-item__label {
    border: none;
    text-align: left !important;
}

/* .sec-form-second:not(:first-child){
    border-top: 0;
  } */
.sec-form-second .el-form-item {
    float: left;
    width: 100%;
}

.sec-el-table {
    width: 100%;
    border: 1px solid #EBEEF5;
    height: calc(100% - 34px - 44px - 10px);
}

>>>.sec-el-table .el-input__inner {
    border: none !important;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
    width: 200px;
    text-align: center;
    font-size: 16px;
}

.gtzzsmgwgz {
    text-align: left !important;
}

>>>.sec-form-container .el-input__inner {
    border: none;
    border-right: 1px solid #CDD2D9;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item {
    margin-bottom: 0px;
}

/* >>>.el-form > div {
    border: 1px solid #CDD2D9;;
  } */
>>>.el-form-item__label {
    border-right: 1px solid #CDD2D9;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
    width: 150px;
    margin-left: 10px;
}

.dlFqsqContainer .searchButton {
    margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
    height: 40px;
}

.dlFqsqContainer .input1 {
    margin-right: 20px;
    margin-bottom: 10px;
}

/deep/ .el-input.is-disabled .el-input__inner {
    color: #000 !important;
}

>>>.brno .el-input__inner {
    border-right: none;
}

.sec-form-left1 {
    height: 80px;
}

.sec-form-left1>>>.el-form-item__label {
    line-height: 80px;
}

.sec-form-left1>>>.el-input__inner {
    line-height: 80px;
    height: 80px;
}
</style>