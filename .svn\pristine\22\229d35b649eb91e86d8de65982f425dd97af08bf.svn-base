<template>
  <div class="dbgzContainer" id="dbgzContainer">
    <div class="zdwb">
      <!-- 注册信息 -->
      <!-- <div class="dbItem" v-if="zcxxIsPerfectShow">
          <p class="fonts">注册信息待完善-共{{ zcxxPerfectCount }}项</p>
          <div class="titleDiv">
              <div class="title" @click="$router.push('/zcdw')">
                  <img src="./images/s-icon-01.png" alt="">
                  <span>注册信息待完善</span>
              </div>
          </div>
      </div> -->
      <!-- 日常管理 -->
      <div class="dbItem" v-if="rcUpdateCounts != 0">
        <p class="fonts">日常管理待完善-共{{ rcUpdateCounts }}项</p>
        <div class="titleDiv">
          <!-- <div class="title" v-if="zzxxIsPerfectShow" @click="toIndex(2)">
            <img src="./images/s-icon-02.png" alt="">
            <span style="margin-top: -10px;">完善资质信息<br />（资质单位）</span>
          </div> -->
          <div class="title" v-if="bmzdIsPerfectShow" @click="$router.push('/tzglsy?activeName=bmzd')">
            <img src="./images/s-icon-03.png" alt="">
            <span>完善保密制度</span>
          </div>
          <div class="title" v-if="zzjgIsPerfectShow" @click="$router.push('/tzglsy?activeName=zzjg')">
            <img src="./images/s-icon-04.png" alt="">
            <span>完善组织机构</span>
          </div>
          <div class="title" v-if="ryxxIsPerfectShow" @click="$router.push('/tzglsy?activeName=smry')">
            <img src="./images/s-icon-05.png" alt="">
            <span>完善人员信息</span>
          </div>
          <div class="title" v-if="csxxIsPerfectShow" @click="$router.push('/tzglsy?activeName=csgl')">
            <img src="./images/s-icon-06.png" alt="">
            <span>完善场所信息</span>
          </div>
          <div class="title" v-if="sbxxIsPerfectShow" @click="$router.push('/tzglsy?activeName=smwlsb')">
            <img src="./images/s-icon-07.png" alt="">
            <span>完善设备信息</span>
          </div>
          <div class="title" v-if="ztxxIsPerfectShow" @click="$router.push('/tzglsy?activeName=smzttz')">
            <img src="./images/s-icon-08.png" alt="">
            <span>完善载体信息</span>
          </div>
        </div>
      </div>
      <!-- 年度涉密人员相关台账 -->
      <div class="dbItem" v-if="smryAllShow">
        <p class="fonts">
          {{ nowsYear }}年度涉密人员相关台账
          <el-tooltip class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
              <i class="el-icon-info scBtnFont"></i>
          </el-tooltip>
        </p>        
        <div class="buttons" v-if="smryTzScShow && smryStatus.isAgain == 0" @click="yjscClick('smry','yjsc')">一键生成</div>

        <div class="buttons" v-if="cxscSmryTzScShow && smryStatus.isAgain == 1" @click="yjscClick('smry','cxsc')">重新生成</div>
        <div class="titleDiv">
          <div class="title" v-if="smgwListLength > 0" @click="$router.push('/tzglsy?activeName=smgwgl')">
            <img src="./images/s-icon-09.png" alt="">
            <span>涉密岗位登记表</span>
          </div>
          <div class="title" v-if="zgsmryHzListLength > 0" @click="$router.push('/tzglsy?activeName=smry')">
            <img src="./images/s-icon-10.png" alt="">
            <span>涉密人员汇总表</span>
          </div>
          <div class="title" v-if="ryxzHzListLength > 0" @click="$router.push('/tzglsy?activeName=ryxz')">
            <img src="./images/s-icon-11.png" alt="">
            <span>人员新增汇总表</span>
          </div>
          <div class="title" v-if="rynjbgHzListLength > 0" @click="$router.push('/tzglsy?activeName=gwbg')">
            <img src="./images/s-icon-12.png" alt="">
            <span>人员密级变更汇总表</span>
          </div>
          <div class="title" v-if="lglzListLength > 0" @click="$router.push('/tzglsy?activeName=lglz')">
            <img src="./images/s-icon-13.png" alt="">
            <span>离岗汇总表</span>
          </div>
        </div>
      </div>
      <!-- 年度涉密场所相关台账 -->
      <div class="dbItem" v-if="smcsAllShow">
        <p class="fonts">
          {{ nowsYear }}年度涉密场所相关台账
          <el-tooltip class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
              <i class="el-icon-info scBtnFont"></i>
          </el-tooltip>
        </p>
        <div class="buttons" v-if="smcsTzScShow && smcsStatus.isAgain == 0" @click="yjscClick('smcs','yjsc')">一键生成</div>
        <div class="buttons" v-if="cxscSmcsTzScShow && smcsStatus.isAgain == 1" @click="yjscClick('smcs','cxsc')">重新生成</div>
        <div class="titleDiv">
          <div class="title" v-if="csglListLength > 0" @click="$router.push('/tzglsy?activeName=csgl')">
            <img src="./images/s-icon-15.png" alt="">
            <span>涉密场所登记表</span>
          </div>
          <div class="title" v-if="csbgListLength > 0" @click="$router.push('/tzglsy?activeName=csbg')">
            <img src="./images/s-icon-16.png" alt="">
            <span>场所变更登记表</span>
          </div>
        </div>
      </div>
      <!-- 年度涉密设备相关台账 -->
      <div class="dbItem" v-if="smsbAllShow">
        <p class="fonts">
          {{ nowsYear }}年度涉密设备相关台账
          <el-tooltip class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
              <i class="el-icon-info scBtnFont"></i>
          </el-tooltip>
        </p>
        <div class="buttons" v-if="smsbTzScShow && smsbStatus.isAgain == 0" @click="yjscClick('smsb','yjsc')">一键生成</div>
        <div class="buttons" v-if="cxscSmsbTzScShow && smsbStatus.isAgain == 1" @click="yjscClick('smsb','cxsc')">重新生成</div>
        <div class="titleDiv">
          <div class="title" v-if="smjsjListLength > 0" @click="$router.push('/tzglsy?activeName=smjsj')">
            <img src="./images/s-icon-17.png" alt="">
            <span>涉密计算机</span>
          </div>
          <div class="title" v-if="fsmjsjListLength > 0" @click="$router.push('/tzglsy?activeName=fsmjsj')">
            <img src="./images/s-icon-17.png" alt="">
            <span>非涉密计算机</span>
          </div>
          <div class="title" v-if="ydccjzListLength > 0" @click="$router.push('/tzglsy?activeName=smydccjz')">
            <img src="./images/s-icon-18.png" alt="">
            <span>涉密移动存储介质</span>
          </div>
          <div class="title" v-if="bgzdhsbListLength > 0"
               @click="$router.push('/tzglsy?activeName=smbgzdhsb')">
            <img src="./images/s-icon-19.png" alt="">
            <span>涉密办公自动化设备</span>
          </div>
          <div class="title" v-if="fsmbgzdhsbListLength > 0"
               @click="$router.push('/tzglsy?activeName=fsmbgzdhsb')">
            <img src="./images/s-icon-19.png" alt="">
            <span>非涉密办公自动化设备</span>
          </div>
          <div class="title" v-if="wlsbListLength > 0" @click="$router.push('/tzglsy?activeName=smwlsb')">
            <img src="./images/s-icon-20.png" alt="">
            <span>涉密网络设备</span>
          </div>
          <div class="title" v-if="fwlsbListLength > 0" @click="$router.push('/tzglsy?activeName=fmwlsb')">
            <img src="./images/s-icon-20.png" alt="">
            <span>非涉密网络设备</span>
          </div>
          <div class="title" v-if="aqcpListLength > 0" @click="$router.push('/tzglsy?activeName=aqcp')">
            <img src="./images/s-icon-21.png" alt="">
            <span>安全产品</span>
          </div>
        </div>
      </div>
      <!-- 年度涉密载体相关台账 -->
      <div class="dbItem" v-if="smztListLength > 0">
        <p class="fonts">
          {{ nowsYear }}年度涉密载体相关台账
          <el-tooltip class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
              <i class="el-icon-info scBtnFont"></i>
          </el-tooltip>
        </p>
        <div class="buttons" v-if="smztTzScShow && smztStatus.isAgain == 0" @click="yjscClick('smzt','yjsc')">一键生成</div>
        <div class="buttons" v-if="cxscSmztTzScShow && smztStatus.isAgain == 1" @click="yjscClick('smzt','cxsc')">重新生成</div>
        <div class="titleDiv">
          <div class="title" v-if="smztListLength > 0" @click="$router.push('/tzglsy?activeName=smzttz')">
            <img src="./images/s-icon-09.png" alt="">
            <span>涉密载体</span>
          </div>
        </div>
      </div>
      <!-- 年度涉密事项相关台账 -->
      <div class="dbItem" v-if="smsxAllShow">
        <p class="fonts">
          {{ nowsYear }}年度涉密事项相关台账
          <el-tooltip class="item" effect="dark" :content="dbscsJcomputed" placement="bottom-end">
              <i class="el-icon-info scBtnFont"></i>
          </el-tooltip>
        </p>
        <div class="buttons" v-if="smsxTzScShow && smsxStatus.isAgain == 0" @click="yjscClick('smsx','yjsc')">一键生成</div>
        <div class="buttons" v-if="cxscSmsxTzScShow && smsxStatus.isAgain == 1" @click="yjscClick('smsx','cxsc')">重新生成</div>
        <div class="titleDiv">
          <div class="title" v-if="dmzrrListLength > 0" @click="$router.push('/tzglsy?activeName=dmzrr')">
            <img src="./images/s-icon-09.png" alt="">
            <span>定密责任人</span>
          </div>
          <div class="title" v-if="dmsqListLength > 0" @click="$router.push('/tzglsy?activeName=dmsq')">
            <img src="./images/s-icon-10.png" alt="">
            <span>定密授权</span>
          </div>
          <div class="title" v-if="gjmmsxListLength > 0" @click="$router.push('/tzglsy?activeName=gjmmsx')">
            <img src="./images/s-icon-11.png" alt="">
            <span>国家秘密事项</span>
          </div>
          <div class="title" v-if="dmpxListLength > 0" @click="$router.push('/tzglsy?activeName=dmpx')">
            <img src="./images/s-icon-12.png" alt="">
            <span>定密培训</span>
          </div>
          <div class="title" v-if="dmqkndtjListLength > 0"
               @click="$router.push('/tzglsy?activeName=dmqkndtj')">
            <img src="./images/s-icon-13.png" alt="">
            <span>定密情况年度统计</span>
          </div>
          <div class="title" v-if="bmqsxqdqkListLength > 0"
               @click="$router.push('/tzglsy?activeName=bmqsxqdqk')">
            <img src="./images/s-icon-13.png" alt="">
            <span>不明确事项确定情况</span>
          </div>
          <div class="title" v-if="zfcgxmqkListLength > 0"
               @click="$router.push('/tzglsy?activeName=zfcgxmqk')">
            <img src="./images/s-icon-13.png" alt="">
            <span>政府采购项目情况</span>
          </div>
        </div>
      </div>
      <!-- 自检自查待完成 -->
      <!-- <div class="dbItem"
           v-if="zczp1IsPerfectShow && new Date().getMonth() + 1 > 0 && new Date().getMonth() + 1 < 13 || zczp2IsPerfectShow && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13 || zczp3IsPerfectShow && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13 || zczp4IsPerfectShow && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13">
        <p class="fonts">自检自查待完成-共{{ zczpUpdateCounts }}项</p>
        <div class="titleDiv">
          <div class="title"
               v-if="zczp1IsPerfectShow && new Date().getMonth() + 1 > 0 && new Date().getMonth() + 1 < 13"
               @click="$router.push('/zczpls?activeName=xjzczp')">
            <img src="./images/s-icon-22.png" alt="">
            <span>完成本年第一季度自查自评</span>
          </div>
          <div class="title"
               v-if="zczp2IsPerfectShow && new Date().getMonth() + 1 > 3 && new Date().getMonth() + 1 < 13"
               @click="$router.push('/zczpls?activeName=xjzczp')">
            <img src="./images/s-icon-22.png" alt="">
            <span>完成本年第二季度自查自评</span>
          </div>
          <div class="title"
               v-if="zczp3IsPerfectShow && new Date().getMonth() + 1 > 6 && new Date().getMonth() + 1 < 13"
               @click="$router.push('/zczpls?activeName=xjzczp')">
            <img src="./images/s-icon-22.png" alt="">
            <span>完成本年第三季度自查自评</span>
          </div>
          <div class="title"
               v-if="zczp4IsPerfectShow && new Date().getMonth() + 1 > 9 && new Date().getMonth() + 1 < 13"
               @click="$router.push('/zczpls?activeName=xjzczp')">
            <img src="./images/s-icon-22.png" alt="">
            <span>完成本年第四季度自查自评</span>
          </div>
        </div>
      </div> -->
      <!-- 年度涉密人员上报待完成 -->
      <div class="dbItem" v-if="smryDownLoadShow">
        <p class="fonts">年度涉密人员上报待完成</p>
        <div class="titleDiv">
          <div class="title" @click="smryDatasImport">
            <img src="./images/s-icon-23.png" alt="">
            <span>完成上报数据导出</span>
          </div>
        </div>
      </div>
      <!-- 年度定密事项上报待完成 -->
      <div class="dbItem" v-if="smsxDownloadShow">
        <p class="fonts">年度定密事项上报待完成</p>
        <div class="titleDiv">
          <div class="title" @click="dmsxDatasImport">
            <img src="./images/s-icon-23.png" alt="">
            <span>完成上报数据导出</span>
          </div>
        </div>
      </div>
      <div class="whiteKuai"></div>
    </div>
  </div>
</template>
<script>
import handle from './handle'
// import {
//     getlogin
// } from "../../../db/loginyhdb";
import {
    downloadZipSmry,
    downloadZipDmsx
} from "../../../api/qblist";
export default {
  mixins: [handle],
  data() {
    return {
      loading: '',
      dwmc: '',
      dwdm: '',
      dwlxr: '',
      dwlxdh: '',
    };
  },
  computed: {},
  created() {
  },
  methods: {
    // 涉密人员上报数据导出
    async smryDatasImport() {
      this.loading = this.$loading({
        lock: true,
        text: '涉密人员上报数据导出中，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let param = {"nf":new Date().getFullYear().toString()}
      let returnData = await downloadZipSmry(param);
      let date = new Date()
      let sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, new Date().getFullYear().toString() + "年涉密人员上报数据" + sj + ".zip");
    },
    //处理下载流
    dom_download(content, fileName) {
      console.log(content)
      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
      this.loading.close()
    },
    // 定密事项上报数据导出
    async dmsxDatasImport() {
      this.loading = this.$loading({
        lock: true,
        text: '定密事项上报数据导出中，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let param = {"nf":new Date().getFullYear().toString()}
      let returnData = await downloadZipDmsx(param);
      let date = new Date()
      let sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, new Date().getFullYear().toString() + "年定密事项上报数据" + sj + ".zip");
    },
  },
  watch: {},
  mounted() {

    // this.dwmc = getlogin()[0].dwmc
    // this.dwdm = getlogin()[0].xydm
    // this.dwlxr = getlogin()[0].dwlxr
    // this.dwlxdh = getlogin()[0].dwlxdh
  }
};
</script>
<style scoped>
/* 样式改版2022/12/13 */
.dbItem {
  padding-bottom: 40px;
  background: #FFFFFF;
  border: 1px solid rgba(219, 231, 255, 1);
  box-shadow: 0px 2px 10px 0px rgba(107, 117, 134, 0.15);
  overflow: hidden;
  margin-bottom: 20px;
  position: relative;
  border-radius: 6px;
}

.dbItem .fonts {
  font-family: 'SourceHanSansSCziti';
  font-size: 20px;
  color: #080808;
  font-weight: 400;
  padding-top: 18px;
  padding-left: 20px;
}

.dbItem .buttons {
  position: absolute;
  right: 20px;
  top: 25px;
  width: 100px;
  height: 32px;
  background: #FFFFFF;
  border: 1px solid rgba(2, 111, 222, 1);
  font-family: 'SourceHanSansSCziti';
  font-size: 16px;
  color: #1766D1;
  font-weight: 400;
  border-radius: 50px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
}

.dbItem .title {
  font-family: 'SourceHanSansSCziti';
  font-size: 18px;
  color: #666666;
  font-weight: 400;
  padding-top: 32px;
  padding-left: 23px;
  overflow: hidden;
  cursor: pointer;
  float: left;
  margin-left: 40px;
}

.titleDiv {
  margin-left: -40px;
}

.dbItem .title img {
  float: left;
  width: 24px;
  height: 24px;
}

.dbItem .title span {
  display: block;
  float: left;
  margin-left: 12px;
}


.zdwb {
  width: 100%;
  height: 100%;
  /* box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.1); */
  /* background: #FFFFFF; */
  /* overflow-y: scroll; */
}

.mk_dbgz {
  width: 100%;
  /* height: 12vw; */
  /* background-color: rgba(255, 255, 1, 0.5); */

}

.mk_bt {
  width: 100%;
  height: 3vw;
  border-bottom: 1px solid rgba(216, 216, 216, 1);

}

.mk_btl {
  display: flex;
  align-items: center;
  margin-left: 20px;
  font-size: .9vw;
  height: 100%;
}

.mk-nr {
  display: flex;
  align-items: center;
  padding: 15px 0px;
  /* margin-bottom: 10px; */
}

.mk-nr-div {
  width: 9vw;
  /* height: 9vw; */
  cursor: pointer;
}

.nr-div {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.yuan {
  width: 60px;
  height: 60px;
  /* background: url(./images/img1026_18.png) no-repeat center; */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  background: #EF6B43;
}

.ym-wz {
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 19.6px;
  font-weight: 400;
  margin-top: .5vw;
}

.dbTitle {
  margin-left: 10px;
}

.ywcFont {
  background: #21A566;
  text-align: center;
  border-radius: 50px;
  color: #ffffff;
  font-size: 0.7vw;
  width: 80px;
  padding: 3px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  /* margin-left: calc(100% - 126px); */
}

.wwcFont {
  background: #EF6B43;
  text-align: center;
  border-radius: 50px;
  color: #ffffff;
  font-size: 0.7vw;
  width: 80px;
  padding: 3px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  /* margin-left: calc(100% - 126px); */
}

.dwsBkg {
  background: #EF6B43;
}

.ywcBkg {
  background: #21A566;
}

.dbgzContainer {
  height: 81vh;
  width: 99%;
  margin: 20px auto;
  height: 100%;
  /* box-shadow: 0px 1px 12px 0px rgba(0,0,0,0.1); */
  overflow-y: scroll;
  margin-top: 20px;
  font-family: 'SourceHanSansSCziti';
}

.pfather {
  display: flex;
  justify-content: center;
}
.whiteKuai {
  height: 15px;
  background-color: #FFFFFF;
}
.scBtnFont {
    /* float: right;
    position: absolute;
    right: 135px;
    top: 35px; */
    color: #195BC7;
    cursor: pointer;
}
</style>
