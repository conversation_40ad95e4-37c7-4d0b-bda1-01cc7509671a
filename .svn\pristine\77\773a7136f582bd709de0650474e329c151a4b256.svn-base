<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">涉密计算机台账</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <div class="mhcxxxx">
                <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                  <el-form-item label="台账时间" style="font-weight: 700;">
                    <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widthw">
                    </el-input> -->
                    <el-select v-model="formInline.tzsj" placeholder="台账时间">
                      <el-option v-for="item in yearSelect" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-input v-model="formInline.bmbh" clearable placeholder="保密编号" class="widths">
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-input v-model="formInline.zrr" clearable placeholder="责任人" class="widths">
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <!-- <el-input v-model="formInline.sybm" clearable placeholder="使用部门" class="widths">
										</el-input> -->
                    <el-cascader v-model="formInline.sybm" :options="regionOption" clearable class="widths"
                      :props="regionParams" filterable ref="cascaderArr" placeholder="部门" @change="cxbm">
                    </el-cascader>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-select v-model="formInline.lx" clearable placeholder="类型" class="widthx">
                      <el-option v-for="item in sblxxz" :label="item.mc" :value="item.id" :key="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-select v-model="formInline.smmj" clearable placeholder="密级" class="widthx">
                      <el-option v-for="item in sbmjxz" :label="item.mc" :value="item.id" :key="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item style="font-weight: 700;">
                    <el-date-picker v-model="formInline.qyrq" type="daterange" range-separator="至"
                      start-placeholder="启用起始日期" end-placeholder="启用结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                  </el-form-item>

                </el-form>
              </div>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <!-- <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item> -->
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" @click="fh()">返回
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <!-- <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="drBtnClick">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" icon="el-icon-delete" @click="xhsb">销毁
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-position" @click="jcsb">外借
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" icon="el-icon-circle-close" @click="bfsb">报废
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="warning" size="medium" icon="el-icon-remove-outline" @click="tysb">
                    停用
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" icon="el-icon-circle-check" @click="zysb">启用
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="xzsmsb()" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item> -->
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="smjsjList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 41px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="lx" label="类型" :formatter="forlx"></el-table-column>
                  <el-table-column prop="ppxh" label="品牌型号"></el-table-column>
                  <el-table-column prop="zjxlh" label="主机序列号"></el-table-column>
                  <el-table-column prop="gdzcbh" label="固定资产编号"></el-table-column>
                  <el-table-column prop="bmbh" label="保密编号"></el-table-column>
                  <el-table-column prop="smmj" label="密级" :formatter="forsmmj"></el-table-column>
                  <el-table-column prop="qyrq" label="启用日期"></el-table-column>
                  <el-table-column prop="zrr" label="责任人"></el-table-column>
                  <el-table-column prop="syqk" label="使用状态" :formatter="forsylx"></el-table-column>
                  <el-table-column prop="tznf" label="台账时间"></el-table-column>
                  <el-table-column prop="" label="操作" width="140">
                    <template slot-scope="scoped">
                      <!-- <el-button slot="reference" icon="el-icon-timer" type="text" style="color:#E6A23C;" @click="getTrajectory(scoped.row)"></el-button> -->
                      <!-- <el-button size="medium" type="text" @click="getTrajectory(scoped.row)">轨迹
                      </el-button> -->
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button> -->
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密计算机台账" class="scbg-dialog" :visible.sync="dialogVisible_dr"
          show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;font-size:14px" height="100%" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column prop="类型" label="类型"></el-table-column>
              <el-table-column prop="品牌型号" label="品牌型号"></el-table-column>
              <el-table-column prop="主机序列号" label="主机序列号"></el-table-column>
              <el-table-column prop="固定资产编号" label="固定资产编号"></el-table-column>
              <el-table-column prop="保密编号" label="保密编号"></el-table-column>
              <el-table-column prop="密级" label="密级"></el-table-column>
              <el-table-column prop="责任人" label="责任人"></el-table-column>
              <el-table-column prop="管理部门" label="管理部门"></el-table-column>
              <el-table-column prop="使用部门" label="使用部门"></el-table-column>
              <el-table-column prop="使用状态" label="使用状态"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>
        <!-- -----------------追加模式已存在数据展示--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入[追加模式]已存在涉密计算机台账" class="scbg-dialog"
          :visible.sync="dialogVisible_dr_zj" show-close>
          <div style="height: 600px;">
            <el-table :data="existDrList" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;font-size:14px" height="100%" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column prop="类型" label="类型"></el-table-column>
              <el-table-column prop="品牌型号" label="品牌型号"></el-table-column>
              <el-table-column prop="主机序列号" label="主机序列号"></el-table-column>
              <el-table-column prop="固定资产编号" label="固定资产编号"></el-table-column>
              <el-table-column prop="保密编号" label="保密编号"></el-table-column>
              <el-table-column prop="密级" label="密级"></el-table-column>
              <el-table-column prop="责任人" label="责任人"></el-table-column>
              <el-table-column prop="管理部门" label="管理部门"></el-table-column>
              <el-table-column prop="使用部门" label="使用部门"></el-table-column>
              <el-table-column prop="使用状态" label="使用状态"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="fgDr" size="mini">覆 盖</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>
        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->

        <el-dialog title="涉密计算机详细信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="46%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="150px" size="mini">
            <div style="display:flex">
              <el-form-item label="保密编号" prop="bmbh">
                <el-input placeholder="保密编号" v-model="tjlist.bmbh" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
              <el-form-item label="资产编号" prop="gdzcbh">
                <el-input placeholder="资产编号" v-model="tjlist.gdzcbh" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="smmj" class="one-line">
              <el-radio-group v-model="tjlist.smmj">
                <el-radio v-for="item in sbmjxz" :v-model="tjlist.smmj" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="类型" prop="lx" class="one-line">
              <el-radio-group v-model="tjlist.lx" style="width:120%">
                <el-radio v-for="item in sblxxz" :v-model="tjlist.lx" :label="item.id" :value="item.id" :key="item.id">
                  {{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.qyrq" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" style="width:100%">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="品牌型号" prop="ppxh">
                <el-autocomplete class="inline-input" value-key="ppxh" v-model.trim="tjlist.ppxh"
                  :fetch-suggestions="querySearchppxh" placeholder="品牌型号" style="width:100%">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="主机序列号" prop="zjxlh">
                <el-input placeholder="主机序列号" v-model="tjlist.zjxlh" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
              <el-form-item label="硬盘序列号" prop="ypxlh">
                <el-input placeholder="硬盘序列号" v-model="tjlist.ypxlh" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="操作系统">
                <el-autocomplete class="inline-input" value-key="czxt" v-model.trim="tjlist.czxt"
                  :fetch-suggestions="querySearchczxt" placeholder="操作系统" style="width:100%">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="版本号">
                <el-input placeholder="版本号" v-model="tjlist.bbh" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="操作系统安装日期">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.czxtaz" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" style="width:100%">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="IP地址">
                <el-input placeholder="ip地址" v-model="tjlist.ipdz" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="MAC地址">
                <el-input placeholder="MAC地址" v-model="tjlist.macdz" clearable></el-input>
              </el-form-item>
              <el-form-item label="使用部门">
                <el-cascader v-model="tjlist.sybm" :options="regionOption" :props="regionParams" filterable
                  @change="sybmidhq" style="width: 100%;" ref="cascader"></el-cascader>
                <!-- @change="handleChange($event)" -->
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="管理部门" prop="glbm">
                <!-- <el-input placeholder="管理部门" v-model="tjlist.glbm" clearable></el-input> -->
                <el-cascader v-model="tjlist.glbm" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable ref="cascaderArr" @change="handleChange(1)">
                </el-cascader>
              </el-form-item>
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.zrr"
                  :fetch-suggestions="querySearch" placeholder="请输入责任人" style="width:100%">
                </el-autocomplete>
              </el-form-item>
            </div>
            <el-form-item label="使用情况" prop="syqk" class="one-line">
              <el-radio-group v-model="tjlist.syqk" style="width:120%">
                <el-radio v-for="item in sbsyqkxz" :v-model="tjlist.syqk" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="handleClose()">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改涉密计算机详细信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="46%"
          class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="150px" size="mini">
            <div style="display:flex">
              <el-form-item label="保密编号" prop="bmbh">
                <el-input placeholder="保密编号" v-model="xglist.bmbh" clearable @blur="onInputBlur(2)" disabled>
                </el-input>
              </el-form-item>
              <el-form-item label="资产编号" prop="gdzcbh">
                <el-input placeholder="资产编号" v-model="xglist.gdzcbh" clearable @blur="onInputBlur(2)" disabled>
                </el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="smmj" class="one-line">
              <el-radio-group v-model="xglist.smmj">
                <el-radio v-for="item in sbmjxz" :v-model="xglist.smmj" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="类型" prop="lx" class="one-line">
              <el-radio-group v-model="xglist.lx" style="width:120%">
                <el-radio v-for="item in sblxxz" :v-model="xglist.lx" :label="item.id" :value="item.id" :key="item.id">
                  {{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.qyrq" class="cd" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width:100%">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="品牌型号" prop="ppxh">
                <el-autocomplete class="inline-input" value-key="ppxh" v-model.trim="xglist.ppxh"
                  :fetch-suggestions="querySearchppxh" placeholder="品牌型号" style="width:100%">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="主机序列号" prop="zjxlh">
                <el-input disabled placeholder="主机序列号" v-model="xglist.zjxlh" clearable @blur="onInputBlur(2)">
                </el-input>
              </el-form-item>
              <el-form-item label="硬盘序列号" prop="ypxlh">
                <el-input placeholder="硬盘序列号" v-model="xglist.ypxlh" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="操作系统">
                <el-autocomplete class="inline-input" value-key="czxt" v-model.trim="xglist.czxt"
                  :fetch-suggestions="querySearchczxt" placeholder="操作系统" style="width:100%">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="版本号">
                <el-input placeholder="版本号" v-model="xglist.bbh" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="操作系统安装日期">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.czxtaz" class="cd" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width:100%">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="IP地址">
                <el-input placeholder="ip地址" v-model="xglist.ipdz" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="MAC地址">
                <el-input placeholder="MAC地址" v-model="xglist.macdz" clearable></el-input>
              </el-form-item>
              <el-form-item label="使用部门">
                <el-cascader v-model="xglist.sybm" :options="regionOption" :props="regionParams" filterable
                  @change="sybmidhq(2)" style="width: 100%;" ref="cascader"></el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="管理部门" prop="glbm">
                <el-cascader v-model="xglist.glbm" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable ref="cascaderArr" @change="handleChange(2)">
                </el-cascader>
              </el-form-item>
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.zrr"
                  :fetch-suggestions="querySearch" placeholder="请输入责任人" style="width:100%">
                </el-autocomplete>
              </el-form-item>
            </div>
            <el-form-item label="使用情况" prop="syqk" class="one-line">
              <el-radio-group v-model="xglist.syqk" style="width:120%">
                <el-radio v-for="item in sbsyqkxz" :v-model="xglist.syqk" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="涉密计算机详细信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="46%"
          class="xg">
          <el-form ref="form" :model="xglist" label-width="150px" size="mini" disabled>
            <div style="display:flex">
              <el-form-item label="保密编号" prop="bmbh">
                <el-input placeholder="保密编号" v-model="xglist.bmbh" clearable></el-input>
              </el-form-item>
              <el-form-item label="资产编号" prop="gdzcbh">
                <el-input placeholder="资产编号" v-model="xglist.gdzcbh" clearable></el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="smmj">
              <el-radio-group v-model="xglist.smmj">
                <el-radio v-for="item in sbmjxz" :v-model="xglist.smmj" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>

              </el-radio-group>
            </el-form-item>

            <el-form-item label="类型" prop="lx">
              <el-radio-group v-model="xglist.lx" style="width:120%">
                <el-radio v-for="item in sblxxz" :v-model="xglist.lx" :label="item.id" :value="item.id" :key="item.id">
                  {{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.qyrq" class="cd" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width:100%">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="品牌型号" prop="ppxh">
                <el-input placeholder="品牌型号" v-model="xglist.ppxh" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="主机序列号" prop="zjxlh">
                <el-input placeholder="主机序列号" v-model="xglist.zjxlh" clearable></el-input>
              </el-form-item>
              <el-form-item label="硬盘序列号" prop="ypxlh">
                <el-input placeholder="硬盘序列号" v-model="xglist.ypxlh" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="操作系统" prop="czxt">
                <el-input placeholder="操作系统" v-model="xglist.czxt" clearable></el-input>
              </el-form-item>
              <el-form-item label="版本号" prop="bbh">
                <el-input placeholder="版本号" v-model="xglist.bbh" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="操作系统安装日期" prop="czxtaz">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.czxtaz" class="cd" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width:100%">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="IP地址" prop="ipdz">
                <el-input placeholder="ip地址" v-model="xglist.ipdz" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="MAC地址" prop="macdz">
                <el-input placeholder="MAC地址" v-model="xglist.macdz" clearable></el-input>
              </el-form-item>
              <el-form-item label="使用部门" prop="sybm">
                <el-cascader v-model="xglist.sybm" :options="regionOption" :props="regionParams" filterable
                  style="width: 100%;" ref="cascaderArr"></el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="管理部门" prop="glbm">
                <el-cascader v-model="xglist.glbm" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable ref="cascaderArr" @change="handleChange(2)">
                </el-cascader>
              </el-form-item>
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.zrr"
                  :fetch-suggestions="querySearch" placeholder="请输入责任人" style="width:100%">
                </el-autocomplete>
              </el-form-item>
            </div>
            <el-form-item label="使用情况" prop="syqk">
              <el-radio-group v-model="xglist.syqk" style="width:120%">
                <el-radio v-for="item in sbsyqkxz" :v-model="xglist.syqk" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>

              </el-radio-group>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">

            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 历史轨迹 dialog -->
        <el-dialog title="历史轨迹" :close-on-click-modal="false" :visible.sync="lsgjDialogVisible" width="46%" class="xg">
          <div
            style="padding-left: 10px;background: #EBEEF5;height: 40px;line-height: 40px;font-size: 16px;border-radius: 5px;margin-bottom: 5px;">
            <span>保密编号：<span style="font-size: 14px;">{{ lsgjDialogData.bmbh }}</span></span>
            <span>资产编号：<span style="font-size: 14px;">{{ lsgjDialogData.gdzcbh }}</span></span>
          </div>
          <div style="max-height: 400px;overflow-y: scroll;padding: 10px;">
            <el-timeline>
              <el-timeline-item v-for="(activity, index) in lsgjDialogData.timelineList" :key="index"
                :icon="activity.icon" :color="activity.color" :size="'large'" :timestamp="activity.time">
                <div>
                  <p>{{ activity.ymngnmc }}</p>
                  <p>操作人：{{ activity.xm }}</p>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="lsgjDialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

      </div>
    </div>
  </div>
</template>
<script>
import {
  getSmjsjList,
  saveSmjsj,
  updateSmjsj,
  removeSmjsj,
  getZzjgList,
  getAllYhxx,
  getLoginInfo
} from '../../../api/index'
import {
  getAllSmsbmj,
  getAllSmsblx,
  getAllSyqk
} from '../../../api/xlxz'
import {
  getAllSmjsj
} from '../../../api/all'
import {
  smjsjverify
} from '../../../api/jy'
import { getCurSmjsj } from '../../../api/zhyl'
import {
  getSmjsjHistoryPage
} from '../../../api/lstz'
import {
  exportLsSmjsjData
} from '../../../api/dcwj'
export default {
  components: {},
  props: {},
  data() {
    return {
      yearSelect: [],
      // 保密编号+固定资产编号已存在记录集合(追加模式)
      existDrList: [],
      dialogVisible_dr_zj: false,
      // 历史轨迹dialog显隐
      lsgjDialogVisible: false,
      // 历史轨迹dialog数据
      lsgjDialogData: {
        bmbh: '',
        gdzcbh: '',
        // 历史轨迹时间线数据
        timelineList: [],
      },
      bmbh: '',
      gdzcbh: '',
      zjxlh: '',
      xh: [],
      pdsmjsj: {
        code: 0,
      },
      sbmjxz: [

      ],
      sblxxz: [

      ],
      sbsyqkxz: [

      ],
      smjsjList: [],
      // 修改dialog旧值对象，用来做修改情况比对的
      xglistOld: {},
      xglist: {},
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      formInline: {
        tzsj: new Date().getFullYear().toString()
      },
      tjlist: {
        bmbh: '',
        gdzcbh: '',
        smmj: '',
        qyrq: '',
        lx: '',
        ppxh: '',
        zjxlh: '',
        ypxlh: '',
        czxt: '',
        bbh: '',
        czxtaz: '',
        ipdz: '',
        macdz: '',
        sybm: '',
        glbm: '',
        zrr: '',
        syqk: '',
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        bmbh: [{
          required: true,
          message: '请输入保密编号',
          trigger: 'blur'
        },],
        gdzcbh: [{
          required: true,
          message: '请输入资产编号',
          trigger: 'blur'
        },],
        smmj: [{
          required: true,
          message: '请选择密级',
          trigger: 'blur'
        },],
        qyrq: [{
          required: true,
          message: '请选择启用日期',
          trigger: 'blur'
        },],
        lx: [{
          required: true,
          message: '请选择类型',
          trigger: 'blur'
        },],
        ppxh: [{
          required: true,
          message: '请输入品牌型号',
          trigger: ['blur', 'change'],
        },],
        zjxlh: [{
          required: true,
          message: '请输入序列号',
          trigger: 'blur'
        },],
        ypxlh: [{
          required: true,
          message: '请输入硬盘序列号',
          trigger: 'blur'
        },],
        czxt: [{
          required: true,
          message: '请输入操作系统',
          trigger: ['blur', 'change'],
        },],
        bbh: [{
          required: true,
          message: '请输入版本号',
          trigger: 'blur'
        },],
        czxtaz: [{
          required: true,
          message: '请选择操作系统安装日期',
          trigger: 'blur'
        },],
        ipdz: [{
          required: true,
          message: '请输入IP地址',
          trigger: 'blur'
        },],
        macdz: [{
          required: true,
          message: '请输入MAC地址',
          trigger: 'blur'
        },],
        sybm: [{
          required: true,
          message: '请输入使用部门',
          trigger: ['blur', 'change'],
        },],
        glbm: [{
          required: true,
          message: '请输入管理部门',
          trigger: ['blur', 'change'],
        },],
        zrr: [{
          required: true,
          message: '请输入责任人',
          trigger: ['blur', 'change'],
        },],
        syqk: [{
          required: true,
          message: '请选择使用情况',
          trigger: 'blur'
        },],
      },
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      tableDataCopy: [],
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: '',
      sybmid: '',
      glbmid: '',
      cxbmsj: '',
    }
  },
  computed: {},
  mounted() {
    //获取最近十年的年份
    let yearArr = []
    for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
      yearArr.push(
        {
          label: i.toString(),
          value: i.toString()
        })
    }
    yearArr.unshift({
      label: "全部",
      value: ""
    })
    this.yearSelect = yearArr
    this.smjsj()
    this.smmjxz()
    this.smsblx()
    this.syqkxz()
    this.zzjg()
    this.smry()
    this.ppxhlist()
    this.zhsj()
  },
  methods: {
    //全部组织机构List
    async zzjg() {
      let zzjgList = await getZzjgList()
      console.log(zzjgList);
      this.zzjgmc = zzjgList
      let shu = []
      console.log(this.zzjgmc);
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            // console.log(item, item1);
            childrenRegionVo.push(item1)
            // console.log(childrenRegionVo);
            item.childrenRegionVo = childrenRegionVo
          }
        });
        // console.log(item);
        shu.push(item)
      })

      console.log(shu);
      console.log(shu[0].childrenRegionVo);
      let shuList = []
      let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      console.log(shuList);
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    async zhsj() {
      let sj = await getCurSmjsj()
      if (sj != '') {
        this.tjlist = sj
        this.tjlist.glbm = this.tjlist.glbm.split('/')
        this.tjlist.sybm = this.tjlist.sybm.split('/')
      }
    },
    async smmjxz() {
      this.sbmjxz = await getAllSmsbmj()
    },
    async smsblx() {
      this.sblxxz = await getAllSmsblx()
    },
    async syqkxz() {
      this.sbsyqkxz = await getAllSyqk()
    },
    // 导入按钮点击事件
    drBtnClick() {

    },
    // 获取轨迹日志
    getTrajectory(row) { },
    xzsmsb() {
      this.dialogVisible = true
      console.log(111);
    },
    Radio(val) { },

    mbxzgb() {

    },
    mbdc() {

    },
    // 覆盖导入（追加模式筛选出来的重复数据覆盖添加）
    fgDr() {

    },
    //导入
    chooseFile() {

    },
    //----成员组选择
    handleSelectionChange(val) {

    },
    //---确定导入成员组
    drcy() {

    },
    //----表格导入方法
    readExcel(e) {

    },
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {

          //
          this.xglist.sybm = this.xglist.sybm.join('/')
          this.xglist.glbm = this.xglist.glbm.join('/')
          const that = this
          updateSmjsj(this.xglist).then(function () {
            that.smjsj()
          })
          // 判断修改并写入轨迹日志（只有有改动才写入轨迹日志）
          // let newObj = JSON.parse(JSON.stringify(this.xglist))
          // console.log(newObj.sybm, newObj.glbm)
          // if (checkArr(newObj.sybm)) {
          //   newObj.sybm = newObj.sybm.join("/")
          // }
          // if (checkArr(newObj.glbm)) {
          //   newObj.glbm = newObj.glbm.join("/")
          // }
          // let resArr = dataComparison(this.xglistOld, newObj)
          // console.log('resArr', resArr)
          // if (resArr[1].syqk) {
          //   // 添加日志
          //   let paramsLog = {
          //     xyybs: 'mk_smjsj',
          //     id: newObj.smjsjid,
          //     ymngnmc: resArr[1].syqk,
          //     extraParams: {
          //       zrr: newObj.zrr
          //     }
          //   }
          //   writeTrajectoryLog(paramsLog)
          // }
          // 刷新页面表格数据

          this.ppxhlist()
          // 关闭dialog
          this.$message.success('修改成功')
          this.xgdialogVisible = false

        } else {
          console.log('error submit!!');
          return false;
        }
      });


    },
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xglist.sybm = this.xglist.sybm.split('/')
      this.xglist.glbm = this.xglist.glbm.split('/')
      this.xqdialogVisible = true
    },

    updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.bmbh = this.xglist.bmbh
      // this.zcbh = this.xglist.zcbh
      // this.zjxlh = this.xglist.zjxlh
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xglist.sybm = this.xglist.sybm.split('/')
      this.xglist.glbm = this.xglist.glbm.split('/')
      // this.xglistOld = JSON.parse(JSON.stringify(row))
      this.xgdialogVisible = true
    },
    //查询
    onSubmit() {
      this.page = 1
      this.smjsj()
      // //  form是查询条件
      // // 备份了一下数据
      // let arr = this.tableDataCopy
      // // 通过遍历key值来循环处理

      // Object.keys(this.formInline).forEach((e, label) => {
      //   // 调用自己定义好的筛选方法
      //   if (typeof (this.formInline[e]) == 'object') {
      //     // console.log(this.formInline[e].length);
      //     if (this.formInline[e] == null || this.formInline[e].length == 0) {
      //       arr = this.filterFunc(this.formInline[e], e, arr)
      //       return
      //     }
      //     let timeArr1 = this.formInline[e][label].replace(/[\u4e00-\u9fa5]/g, '/')

      //     if (!(isNaN(timeArr1) && !isNaN(Date.parse(timeArr1)))) {
      //       this.formInline[e] = this.formInline[e].join('/')
      //       arr = this.filterFunc(this.formInline[e], e, arr)
      //       this.formInline[e] = this.formInline[e].split('/')
      //     } else {
      //       arr = this.filterFunc(this.formInline[e], e, arr)
      //     }
      //   } else {
      //     arr = this.filterFunc(this.formInline[e], e, arr)
      //   }
      // })
      // // 为表格赋值
      // this.smjsjList = arr



    },
    filterFunc(val, target, filterArr) {

    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    cxbm(item) {
      if (item != undefined) {
        this.cxbmsj = item.join('/')
      }

    },
    async smjsj() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        bmbh: this.formInline.bmbh,
        zrr: this.formInline.zrr,
        sybm: this.cxbmsj,
        lx: this.formInline.lx,
        smmj: this.formInline.smmj,
        // tznf: this.formInline.tzsj
      }
      if(this.formInline.tzsj){
        params.tznf = this.formInline.tzsj
      }
      if (this.cxbmsj == '') {
        params.sybm = this.formInline.sybm
      }
      if (this.formInline.qyrq != null) {
        params.kssj = this.formInline.qyrq[0]
        params.jssj = this.formInline.qyrq[1]
      }
      let resList = await getSmjsjHistoryPage(params)
      this.tableDataCopy = resList.records
      this.smjsjList = resList.records
      this.total = resList.total
    },
    //删除
    shanchu(id) {
      let that = this
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            console.log(item);
            let params = {
              jlid: item.jlid,
              dwid: item.dwid
            }
            removeSmjsj(params).then(() => {
              that.smjsj()
              that.ppxhlist()
            })
            console.log("删除：", item);
            console.log("删除：", item);
          })
          // let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });

        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {
      this.dialogVisible = true
    },
    //导出
    async exportList() {
      let params = {
        bmbh: this.formInline.bmbh,
        zrr: this.formInline.zrr,
        lx: this.formInline.lx,
        smmj: this.formInline.smmj,
        nf: this.formInline.tzsj
      }
      if (this.formInline.sybm != undefined) {
        param.sybm = this.formInline.sybm.join('/')
      }

      if (this.formInline.qyrq != null) {
        param.kssj = this.formInline.qyrq[0]
        param.jssj = this.formInline.qyrq[1]
      }
      let returnData = await exportLsSmjsjData(params);
      let date = new Date()
      let sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "涉密计算机信息表-" + sj + ".xls");
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) // 创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) // 指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {

          let params = {
            dwid: "901",

            bmbh: this.tjlist.bmbh,
            gdzcbh: this.tjlist.gdzcbh,
            smmj: this.tjlist.smmj,
            qyrq: this.tjlist.qyrq,
            lx: this.tjlist.lx,
            ppxh: this.tjlist.ppxh,
            zjxlh: this.tjlist.zjxlh,
            ypxlh: this.tjlist.ypxlh,
            czxt: this.tjlist.czxt,
            bbh: this.tjlist.bbh,
            czxtaz: this.tjlist.czxtaz,
            ipdz: this.tjlist.ipdz,
            macdz: this.tjlist.macdz,
            sybm: this.tjlist.sybm.join('/'),
            sybmid: this.sybmid,
            glbm: this.tjlist.glbm.join('/'),
            glbmid: this.glbmid,
            zrr: this.tjlist.zrr,
            syqk: this.tjlist.syqk,
            cjrid: '111'
            // smjsjid: uuid
          }
          this.onInputBlur(1)
          if (this.pdsmjsj.code == 10000) {
            let that = this
            saveSmjsj(params).then(() => {
              // that.resetForm()
              that.smjsj()
              that.ppxhlist()
            })
            this.dialogVisible = false
            this.$message({
              message: '添加成功',
              type: 'success'
            });
          }



        } else {
          console.log('error submit!!');
          return false;
        }

      });

    },
    deleteTkglBtn() {

    },

    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.smjsj()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.smjsj()
    },
    //添加重置
    resetForm() {
      this.tjlist.smmj = '秘密'
      this.tjlist.qyrq = this.Date
      this.tjlist.lx = '台式机'
      this.tjlist.czxtaz = this.Date
      this.tjlist.syqk = '在用'
    },
    handleClose(done) {
      // this.resetForm()
      this.dialogVisible = false

    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].clearValidate();
    },
    fh() {
      this.$router.go(-1)
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].clearValidate();
    },
    zysb() {
      let that = this
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        params.forEach(item => {
          item.syqk = 1
          updateSmjsj(item).then(function () {
            that.smjsj()
          })
        })
        console.log(this.selectlistRow);
        // xgsmjsjsyzt_zy(params)

        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }

    },
    tysb() {
      let that = this
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        params.forEach(item => {
          item.syqk = 2
          updateSmjsj(item).then(function () {
            that.smjsj()
          })
        })
        console.log(this.selectlistRow);
        // xgsmjsjsyzt_zy(params)

        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    bfsb() {
      let that = this
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        params.forEach(item => {
          item.syqk = 3
          updateSmjsj(item).then(function () {
            that.smjsj()
          })
        })
        console.log(this.selectlistRow);
        // xgsmjsjsyzt_zy(params)

        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    jcsb() {
      let that = this
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        params.forEach(item => {
          item.syqk = 4
          updateSmjsj(item).then(function () {
            that.smjsj()
          })
        })
        console.log(this.selectlistRow);
        // xgsmjsjsyzt_zy(params)

        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    xhsb() {
      let that = this
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: '操作失败',
          type: 'warning'
        });
      } else {
        let params = this.selectlistRow
        params.forEach(item => {
          item.syqk = 5
          updateSmjsj(item).then(function () {
            that.smjsj()
          })
        })
        console.log(this.selectlistRow);
        // xgsmjsjsyzt_zy(params)

        this.$message({
          message: '操作成功',
          type: 'success'
        });
      }
    },
    async onInputBlur(index) {
      if (index == 1) {
        let params = {
          bmbh: this.tjlist.bmbh,
          gdzcbh: this.tjlist.gdzcbh,
          zjxlh: this.tjlist.zjxlh
        }
        this.pdsmjsj = await smjsjverify(params)
        console.log(this.pdsmjsj);
        if (this.pdsmjsj.code == 40003) {
          this.$message.error('保密编号已存在');
          return
        } else if (this.pdsmjsj.code == 40004) {
          this.$message.error('资产编号已存在');
          return
        } else if (this.pdsmjsj.code == 40005) {
          this.$message.error('主机序列号已存在');
          return
        }
      }
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async smry() {
      this.restaurants = await getAllYhxx()
    },
    async handleChange(index) {
      let nodesObj = this.$refs['cascaderArr'].getCheckedNodes()[0].data
      this.glbmid = nodesObj.bmm
      console.log(nodesObj);
      let resList
      let params
      if (index == 1) {
        params = {
          bmmc: this.tjlist.glbm.join('/')
        }
        resList = await getAllYhxx(params)
      } else if (index == 2) {
        this.xglist.glbmid = nodesObj.bmm
        params = {
          bmmc: this.xglist.glbm.join('/')
        }
        resList = await getAllYhxx(params)
      }
      this.restaurants = resList;
      this.tjlist.zrr = "";
      this.xglist.zrr = "";

    },
    sybmidhq(index) {
      let nodesObj = this.$refs['cascader'].getCheckedNodes()[0].data
      console.log(nodesObj);
      this.sybmid = nodesObj.bmm
      if (index == 2) {
        this.xglist.sybmid = nodesObj.bmm
      }
    },
    //模糊查询品牌型号
    querySearchppxh(queryString, cb) {
      var restaurants = this.restaurantsppxh;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterppxh(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].ppxh === results[j].ppxh) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterppxh(queryString) {
      return (restaurant) => {
        return (restaurant.ppxh.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    //模糊查询操作系统
    querySearchczxt(queryString, cb) {
      var restaurants = this.restaurantsppxh;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterczxt(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].czxt === results[j].czxt) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterczxt(queryString) {
      return (restaurant) => {
        return (restaurant.czxt.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async ppxhlist() {
      let resList = await getAllSmjsj()
      this.restaurantsppxh = resList;
    },
    cz() {
      this.cxbmsj = ''
      this.formInline = {}
    },
    forlx(row) {
      let hxsj
      this.sblxxz.forEach(item => {
        if (row.lx == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    forsmmj(row) {
      let hxsj
      this.sbmjxz.forEach(item => {
        if (row.smmj == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    forsylx(row) {
      let hxsj
      this.sbsyqkxz.forEach(item => {
        if (row.syqk == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    }
  },
  watch: {

  }
}

</script>

<style scoped>
.bg_con {
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 7vw;
}

.widthx {
  width: 8vw;
}

.cd {
  width: 175px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

/deep/.mhcxxxx .el-form--inline .el-form-item {
  margin-right: 0px;
}

/deep/.el-dialog__body .el-form>div .el-form-item__label {
  width: 150px !important;
}

/deep/.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
</style>
