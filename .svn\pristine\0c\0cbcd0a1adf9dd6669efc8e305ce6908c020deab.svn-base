{"version": 3, "sources": ["webpack:///src/renderer/view/xtsz/filePathSetting.vue", "webpack:///./src/renderer/view/xtsz/filePathSetting.vue?fe33", "webpack:///./src/renderer/view/xtsz/filePathSetting.vue"], "names": ["filePathSetting", "data", "pageInfo", "page", "pageSize", "total", "dialogVisibleSetting", "settingForm", "settingList", "components", "hsoft_top_title", "methods", "choose<PERSON><PERSON>", "_this", "this", "dialog", "showOpenDialog", "title", "properties", "result", "console", "log", "csz", "JSON", "parse", "stringify_default", "formatTime", "time", "Object", "moment", "Date", "handleCurrentChange", "val", "getSettingList", "handleSizeChange", "modifySetting", "row", "_this2", "oldRow", "params", "oldCsz", "moveFilesByDirectory", "error", "code", "$notify", "message", "type", "offset", "duration", "err<PERSON><PERSON><PERSON>", "mark", "updateWjxgList", "cszNew", "writeSystemOptionsLog", "xyybs", "ymngnmc", "extraParams", "$confirm", "cancelButtonClass", "confirmButtonText", "cancelButtonText", "then", "deleteFilesByDirectory", "_err<PERSON>bj", "solvtion", "$message", "success", "catch", "warning", "deleteSetting", "filesettingid", "deleteWjxgList", "assign_default", "wjxgPage", "selectWjxgList", "list", "addSetting", "insertWjxgList", "logParams", "mounted", "xtsz_filePathSetting", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "height", "scopedSlots", "_u", "key", "fn", "_v", "proxy", "padding", "text-align", "attrs", "on", "click", "$event", "staticClass", "width", "border", "header-cell-style", "background", "color", "stripe", "label", "align", "prop", "scoped", "_s", "gxsj", "size", "padding-top", "pager-count", "current-page", "page-sizes", "page-size", "layout", "current-change", "size-change", "visible", "update:visible", "label-position", "label-width", "display", "model", "value", "callback", "$$v", "$set", "expression", "slot", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "2LA6FAA,GACAC,KADA,WAEA,OAEAC,UACAC,KAAA,EACAC,SAAA,GACAC,MAAA,GAGAC,sBAAA,EACAC,eAEAC,iBAGAC,YACAC,kBAAA,GAEAC,SAEAC,WAFA,WAGA,IAAAC,EAAAC,KAMAC,OAAAC,gBAHAC,MAAA,QACAC,YAAA,kBAEA,SAAAC,GACAC,QAAAC,IAAA,SAAAF,GACAN,EAAAN,YAAAe,IAAAH,EAAA,GACAN,EAAAN,YAAAgB,KAAAC,MAAAC,IAAAZ,EAAAN,iBAIAmB,WAhBA,SAgBAC,GACA,OAAAC,OAAAC,EAAA,EAAAD,CAAA,IAAAE,KAAAH,KAEAI,oBAnBA,SAmBAC,GACAlB,KAAAZ,SAAAC,KAAA6B,EACAlB,KAAAmB,kBAEAC,iBAvBA,SAuBAF,GACAlB,KAAAZ,SAAAE,SAAA4B,EACAlB,KAAAmB,kBAGAE,cA5BA,SA4BAC,GAAA,IAAAC,EAAAvB,KACAD,EAAAC,KACAwB,EAAAf,KAAAC,MAAAC,IAAAW,IACAG,EAAAhB,KAAAC,MAAAC,IAAAW,IAEAI,EAAAF,EAAAhB,IACA,GAAAkB,EAAA,CASAzB,OAAAC,gBAHAC,MAAA,QACAC,YAAA,kBAEA,SAAAC,GACAC,QAAAC,IAAA,SAAAF,GACA,IAAAG,EAAAH,EAAA,GACA,GAAAG,EAAA,CAEA,IACAmB,qBAAAD,EAAAlB,GACA,MAAAoB,GAEA,GADAtB,QAAAsB,UACAA,EAAAC,KASA,YAPAN,EAAAO,SACA3B,MAAA,OACA4B,QAAAH,EAAAG,QACAC,KAAA,QACAC,OAAA,IACAC,SAAA,IAIA,IAAAC,EAAA1B,KAAAC,MAAAkB,EAAAG,SASA,OARAzB,QAAAC,IAAA,SAAA4B,QACAZ,EAAAO,SACA3B,MAAA,OACA4B,QAAA,IAAAI,EAAAC,KAAA,MACAJ,KAAA,QACAC,OAAA,IACAC,SAAA,IAIAT,EAAAjB,MAEA6B,eAAAZ,GAEAH,EAAAgB,OAAAb,EAAAjB,IAMA+B,uBAJAC,MAAA,cACAC,QAAA,KACAC,YAAApB,IAIAvB,EAAAoB,iBAEApB,EAAA4C,SAAA,uCAAAjB,EAAA,eACAkB,kBAAA,oBACAC,kBAAA,OACAC,iBAAA,SACAd,KAAA,YAEAe,KAAA,WAEA,IACAC,uBAAAtB,GACA,MAAAE,GACA,IAAAA,EAAAC,KASA,YAPAN,EAAAO,SACA3B,MAAA,OACA4B,QAAAH,EAAAG,QACAC,KAAA,QACAC,OAAA,IACAC,SAAA,IAIA,IAAAe,EAAAxC,KAAAC,MAAAkB,EAAAG,SASA,OARAzB,QAAAC,IAAA,SAAA0C,QACA1B,EAAAO,SACA3B,MAAA,OACA4B,QAAA,IAAAkB,EAAAb,KAAA,MAAAa,EAAAC,SACAlB,KAAA,QACAC,OAAA,IACAC,SAAA,IAIAX,EAAA4B,SAAAC,QAAA,YACAC,MAAA,sBAvFArD,KAAAmD,SAAAG,QAAA,kBA8FAC,cAjIA,SAiIAjC,GACA,IAAAG,GACA+B,cAAAlC,EAAAkC,eAEAC,eAAAhC,GACAzB,KAAAmB,iBAOAoB,uBAJAC,MAAA,cACAC,QAAA,KACAC,YAAApB,KAKAH,eAhJA,WAiJAnB,KAAAP,eACA,IAAAgC,KACMiC,IAANjC,EAAAzB,KAAAZ,UACA,IAAAuE,EAAAC,eAAAnC,GACAzB,KAAAN,YAAAiE,EAAAE,KACA7D,KAAAZ,SAAAG,MAAAoE,EAAApE,OAGAuE,WAzJA,WA0JAxD,QAAAC,IAAA,OAAAP,KAAAP,aACAsE,eAAA/D,KAAAP,aAEA,IAAAuE,GACAxB,MAAA,cACAC,QAAA,KACAC,YAAA1C,KAAAP,aAEA8C,sBAAAyB,GAEAhE,KAAAmB,iBACAnB,KAAAR,sBAAA,IAGAyE,QA3LA,WA6LAjE,KAAAmB,mBCvRe+C,GADEC,OAFjB,WAA0B,IAAAC,EAAApE,KAAaqE,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,OAAA,UAAiBH,EAAA,mBAAwBI,YAAAP,EAAAQ,KAAqBC,IAAA,OAAAC,GAAA,WAAyB,OAAAV,EAAAW,GAAA,YAA0BC,OAAA,OAAeZ,EAAAW,GAAA,KAAAR,EAAA,OAAwBE,aAAaQ,QAAA,SAAAC,aAAA,WAAyCX,EAAA,aAAkBY,OAAOnD,KAAA,WAAiBoD,IAAKC,MAAA,SAAAC,GAAyBlB,EAAA5E,sBAAA,MAAkC4E,EAAAW,GAAA,YAAAX,EAAAW,GAAA,KAAAR,EAAA,YAAgDgB,YAAA,QAAAd,aAAiCe,MAAA,OAAAC,OAAA,qBAA4CN,OAAQhG,KAAAiF,EAAA1E,YAAA+F,OAAA,GAAAC,qBAAwDC,WAAA,UAAAC,MAAA,WAA0ClB,OAAA,gDAAAmB,OAAA,MAAuEtB,EAAA,mBAAwBY,OAAOnD,KAAA,QAAAwD,MAAA,KAAAM,MAAA,KAAAC,MAAA,YAA2D3B,EAAAW,GAAA,KAAAR,EAAA,mBAAoCY,OAAOa,KAAA,OAAAF,MAAA,OAAAN,MAAA,SAA4CpB,EAAAW,GAAA,KAAAR,EAAA,mBAAoCY,OAAOa,KAAA,OAAAF,MAAA,UAA8B1B,EAAAW,GAAA,KAAAR,EAAA,mBAAoCY,OAAOa,KAAA,MAAAF,MAAA,KAAAN,MAAA,GAAAO,MAAA,UAAqD3B,EAAAW,GAAA,KAAAR,EAAA,mBAAoCY,OAAOa,KAAA,OAAAF,MAAA,OAAAN,MAAA,MAAAO,MAAA,QAA0DpB,YAAAP,EAAAQ,KAAsBC,IAAA,UAAAC,GAAA,SAAAmB,GAAkC,OAAA1B,EAAA,QAAAH,EAAAW,GAAAX,EAAA8B,GAAA9B,EAAAxD,WAAAqF,EAAA3E,IAAA6E,iBAA0E/B,EAAAW,GAAA,KAAAR,EAAA,mBAAoCY,OAAOa,KAAA,GAAAF,MAAA,KAAAN,MAAA,OAAqCb,YAAAP,EAAAQ,KAAsBC,IAAA,UAAAC,GAAA,SAAAmB,GAAkC,OAAA1B,EAAA,aAAwBY,OAAOiB,KAAA,QAAApE,KAAA,QAA6BoD,IAAKC,MAAA,SAAAC,GAAyB,OAAAlB,EAAA/C,cAAA4E,EAAA3E,SAAuC8C,EAAAW,GAAA,QAAAX,EAAAW,GAAA,KAAAR,EAAA,aAA6CE,aAAamB,MAAA,WAAkBT,OAAQiB,KAAA,QAAApE,KAAA,QAA6BoD,IAAKC,MAAA,SAAAC,GAAyB,OAAAlB,EAAAb,cAAA0C,EAAA3E,SAAuC8C,EAAAW,GAAA,gBAAsB,GAAAX,EAAAW,GAAA,KAAAR,EAAA,iBAAsCE,aAAa4B,cAAA,QAAqBlB,OAAQQ,WAAA,GAAAW,cAAA,EAAAC,eAAAnC,EAAAhF,SAAAC,KAAAmH,cAAA,YAAAC,YAAArC,EAAAhF,SAAAE,SAAAoH,OAAA,yCAAAnH,MAAA6E,EAAAhF,SAAAG,OAA6M6F,IAAKuB,iBAAAvC,EAAAnD,oBAAA2F,cAAAxC,EAAAhD,oBAA6EgD,EAAAW,GAAA,KAAAR,EAAA,aAA8BY,OAAOhF,MAAA,SAAA0G,QAAAzC,EAAA5E,qBAAAgG,MAAA,OAAkEJ,IAAK0B,iBAAA,SAAAxB,GAAkClB,EAAA5E,qBAAA8F,MAAkCf,EAAA,OAAAA,EAAA,WAA0BY,OAAO4B,iBAAA,QAAAC,cAAA,QAAAZ,KAAA,UAA8D7B,EAAA,OAAYE,aAAawC,QAAA,UAAkB1C,EAAA,gBAAqBgB,YAAA,WAAAJ,OAA8BW,MAAA,UAAgBvB,EAAA,YAAiB2C,OAAOC,MAAA/C,EAAA3E,YAAA,KAAA2H,SAAA,SAAAC,GAAsDjD,EAAAkD,KAAAlD,EAAA3E,YAAA,OAAA4H,IAAuCE,WAAA,uBAAgC,OAAAnD,EAAAW,GAAA,KAAAR,EAAA,gBAAyCgB,YAAA,oBAAAJ,OAAuCW,MAAA,UAAgBvB,EAAA,YAAiBY,OAAOnD,KAAA,YAAkBkF,OAAQC,MAAA/C,EAAA3E,YAAA,KAAA2H,SAAA,SAAAC,GAAsDjD,EAAAkD,KAAAlD,EAAA3E,YAAA,OAAA4H,IAAuCE,WAAA,uBAAgC,GAAAnD,EAAAW,GAAA,KAAAR,EAAA,OAA4BE,aAAawC,QAAA,UAAkB1C,EAAA,gBAAqBgB,YAAA,WAAAJ,OAA8BW,MAAA,QAAcvB,EAAA,aAAkBY,OAAOnD,KAAA,WAAiBoD,IAAKC,MAAA,SAAAC,GAAyB,OAAAlB,EAAAtE,iBAA0BsE,EAAAW,GAAA,UAAAX,EAAAW,GAAA,KAAAR,EAAA,OAAAH,EAAAW,GAAAX,EAAA8B,GAAA9B,EAAA3E,YAAAe,SAAA,OAAA4D,EAAAW,GAAA,KAAAR,EAAA,OAA6GE,aAAawC,QAAA,UAAkB1C,EAAA,gBAAqBgB,YAAA,WAAAJ,OAA8BW,MAAA,SAAevB,EAAA,YAAiB2C,OAAOC,MAAA/C,EAAA3E,YAAA,IAAA2H,SAAA,SAAAC,GAAqDjD,EAAAkD,KAAAlD,EAAA3E,YAAA,MAAA4H,IAAsCE,WAAA,sBAA+B,eAAAnD,EAAAW,GAAA,KAAAR,EAAA,QAAyCgB,YAAA,gBAAAJ,OAAmCqC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBY,OAAOnD,KAAA,WAAiBoD,IAAKC,MAAA,SAAAC,GAAyB,OAAAlB,EAAAN,iBAA0BM,EAAAW,GAAA,SAAAX,EAAAW,GAAA,KAAAR,EAAA,aAA8CY,OAAOnD,KAAA,WAAiBoD,IAAKC,MAAA,SAAAC,GAAyBlB,EAAA5E,sBAAA,MAAmC4E,EAAAW,GAAA,oBAEpwH0C,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACE1I,EACAgF,GATF,EAVA,SAAA2D,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/255.0405a3176af5d9065ec6.js", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%;\">\r\n    <hsoft_top_title>\r\n      <template #left>文件路径设置</template>\r\n    </hsoft_top_title>\r\n    <!---->\r\n    <div style=\"padding: 10px 0;text-align: right;\">\r\n      <el-button type=\"success\" @click=\"dialogVisibleSetting = true\">添加</el-button>\r\n      <!-- <el-button type=\"primary\" @click=\"getSettingList()\">查询</el-button> -->\r\n    </div>\r\n    <el-table class=\"table\" :data=\"settingList\" border :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" style=\"width: 100%;border:1px solid #EBEEF5;\" height=\"calc(100% - 32px - 60px - 32px - 10px - 10px)\" stripe>\r\n      <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n      <el-table-column prop=\"csbs\" label=\"路径标识\" width=\"100\"></el-table-column>\r\n      <el-table-column prop=\"cssm\" label=\"路径说明\"></el-table-column>\r\n      <el-table-column prop=\"csz\" label=\"路径\" width=\"\" align=\"left\"></el-table-column>\r\n      <el-table-column prop=\"gxsj\" label=\"修改时间\" width=\"200\" align=\"left\">\r\n        <template slot-scope=\"scoped\">\r\n          <span>{{formatTime(scoped.row.gxsj)}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"\" label=\"操作\" width=\"100\">\r\n        <template slot-scope=\"scoped\">\r\n          <el-button size=\"small\" type=\"text\" @click=\"modifySetting(scoped.row)\">修改</el-button>\r\n          <el-button size=\"small\" type=\"text\" @click=\"deleteSetting(scoped.row)\" style=\"color:#F56C6C;\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-pagination background @current-change=\"handleCurrentChange\" @size-change=\"handleSizeChange\" :pager-count=\"5\" :current-page=\"pageInfo.page\" :page-sizes=\"[5, 10, 20, 30]\" :page-size=\"pageInfo.pageSize\" layout=\"total, prev, pager, sizes,next, jumper\" :total=\"pageInfo.total\" style=\"padding-top: 10px;\">\r\n    </el-pagination>\r\n    <!---->\r\n    <!-- 添加文件相关 -->\r\n    <el-dialog title=\"添加文件路径\" :visible.sync=\"dialogVisibleSetting\" width=\"35%\">\r\n      <div>\r\n        <el-form :label-position=\"'right'\" label-width=\"120px\" size=\"mini\">\r\n          <div style=\"display:flex\">\r\n            <el-form-item label=\"路径标识\" class=\"one-line\">\r\n              <el-input v-model=\"settingForm.csbs\"></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <el-form-item label=\"路径说明\" class=\"one-line-textarea\">\r\n            <el-input type=\"textarea\" v-model=\"settingForm.cssm\"></el-input>\r\n          </el-form-item>\r\n          <div style=\"display:flex\">\r\n            <el-form-item label=\"路径\" class=\"one-line\">\r\n              <el-button type=\"primary\" @click=\"choosePath()\">选择路径</el-button>\r\n              <div>{{settingForm.csz}}</div>\r\n            </el-form-item>\r\n          </div>\r\n          <div style=\"display:flex\">\r\n            <el-form-item label=\"分组号\" class=\"one-line\">\r\n              <el-input v-model=\"settingForm.fzh\"></el-input>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"addSetting()\">保 存</el-button>\r\n        <el-button type=\"warning\" @click=\"dialogVisibleSetting = false\">取 消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!---->\r\n    <!---->\r\n    <!---->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'\r\n\r\n// const { dialog } = require('electron').remote\r\n\r\nimport { getWindowLocation } from '../../../utils/windowLocation'\r\n\r\nimport { dateFormatChinese } from '../../../utils/moment'\r\n\r\n// import { writeSystemOptionsLog } from '../../../utils/logUtils'\r\n\r\n// import { moveFilesByDirectory, deleteFilesByDirectory } from '../../../utils/pathUtil'\r\n\r\n// import { decideChange } from '../../../utils'\r\n\r\n// // 系统参数设置表\r\n// import {\r\n//   // 插入文件相关表\r\n//   insertWjxgList,\r\n//   // 查询文件相关表\r\n//   selectWjxgList,\r\n//   // 删除文件相关设置\r\n//   deleteWjxgList,\r\n//   // 修改文件相关设置\r\n//   updateWjxgList\r\n// } from '../../../db/zczpSystem/zczpSysyemDb'\r\n\r\nexport default {\r\n  data () {\r\n    return {\r\n      // 分页信息\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 10,\r\n        total: 0\r\n      },\r\n      // 添加文件路径dialog\r\n      dialogVisibleSetting: false,\r\n      settingForm: {},\r\n      // 文件路径设置表格数据\r\n      settingList: []\r\n    }\r\n  },\r\n  components: {\r\n    hsoft_top_title\r\n  },\r\n  methods: {\r\n    // 选择路径\r\n    choosePath () {\r\n      const _this = this\r\n      // const { dialog } = require('electron').remote\r\n      let options = {\r\n        title: '请选择路径',\r\n        properties: ['openDirectory']\r\n      }\r\n      dialog.showOpenDialog(options, result => {\r\n        console.log('result', result)\r\n        _this.settingForm.csz = result[0]\r\n        _this.settingForm = JSON.parse(JSON.stringify(_this.settingForm))\r\n      })\r\n    },\r\n    // 格式化时间\r\n    formatTime (time) {\r\n      return dateFormatChinese(new Date(time))\r\n    },\r\n    handleCurrentChange (val) {\r\n      this.pageInfo.page = val\r\n      this.getSettingList()\r\n    },\r\n    handleSizeChange (val) {\r\n      this.pageInfo.pageSize = val\r\n      this.getSettingList()\r\n    },\r\n    // 修改(表格)\r\n    modifySetting (row) {\r\n      const _this = this\r\n      let oldRow = JSON.parse(JSON.stringify(row))\r\n      let params = JSON.parse(JSON.stringify(row))\r\n      // 校验旧路径\r\n      let oldCsz = oldRow.csz\r\n      if (!oldCsz) {\r\n        this.$message.warning('[系统异常]未检测到旧路径')\r\n        return\r\n      }\r\n      // 弹出路径选择器\r\n      let options = {\r\n        title: '请选择路径',\r\n        properties: ['openDirectory']\r\n      }\r\n      dialog.showOpenDialog(options, result => {\r\n        console.log('result', result)\r\n        let csz = result[0]\r\n        if (csz) {\r\n          // 先将该目录下的文件挪到新目录下\r\n          try {\r\n            moveFilesByDirectory(oldCsz, csz)\r\n          } catch (error) {\r\n            console.error(error)\r\n            if (!error.code) {\r\n              // 手动new出来的异常对象或无法翻译的异常对象\r\n              this.$notify({\r\n                title: '操作异常',\r\n                message: error.message,\r\n                type: 'error',\r\n                offset: 100,\r\n                duration: 0\r\n              })\r\n              return\r\n            }\r\n            let errObj = JSON.parse(error.message)\r\n            console.log('errObj', errObj)\r\n            this.$notify({\r\n              title: '系统异常',\r\n              message: '[' + errObj.mark + ']\\n',\r\n              type: 'error',\r\n              offset: 100,\r\n              duration: 0\r\n            })\r\n            return\r\n          }\r\n          params.csz = csz\r\n          // 更新数据库\r\n          updateWjxgList(params)\r\n          // 写入日志\r\n          row.cszNew = params.csz\r\n          let logParams = {\r\n            xyybs: 'yybs_wjljsz',\r\n            ymngnmc: '修改',\r\n            extraParams: row\r\n          }\r\n          writeSystemOptionsLog(logParams)\r\n          //\r\n          _this.getSettingList()\r\n          // 提示修改成功并询问是否需要清除就路径下文件\r\n          _this.$confirm('路径修改成功，路径下文件已迁移到新路径下，是否需要清理旧路径下的文件？[' + oldCsz + ']', '是否文件清理？', {\r\n            cancelButtonClass: \"btn-custom-cancel\",\r\n            confirmButtonText: '立即清理',\r\n            cancelButtonText: '稍后自行清理',\r\n            type: 'warning',\r\n            // center: true\r\n          }).then(() => {\r\n            // 确认清理\r\n            try {\r\n              deleteFilesByDirectory(oldCsz)\r\n            } catch (error) {\r\n              if (!error.code) {\r\n                // 手动new出来的异常对象或无法翻译的异常对象\r\n                this.$notify({\r\n                  title: '系统异常',\r\n                  message: error.message,\r\n                  type: 'error',\r\n                  offset: 100,\r\n                  duration: 0\r\n                })\r\n                return\r\n              }\r\n              let errObj = JSON.parse(error.message)\r\n              console.log('errObj', errObj)\r\n              this.$notify({\r\n                title: '系统异常',\r\n                message: '[' + errObj.mark + ']\\n' + errObj.solvtion,\r\n                type: 'error',\r\n                offset: 100,\r\n                duration: 0\r\n              })\r\n              return\r\n            }\r\n            this.$message.success('文件清理成功')\r\n          }).catch(() => {\r\n            // 稍后自行清理\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 删除文件相关设置\r\n    deleteSetting (row) {\r\n      let params = {\r\n        filesettingid: row.filesettingid\r\n      }\r\n      deleteWjxgList(params)\r\n      this.getSettingList()\r\n      // 写入日志\r\n      let logParams = {\r\n        xyybs: 'yybs_wjljsz',\r\n        ymngnmc: '删除',\r\n        extraParams: row\r\n      }\r\n      writeSystemOptionsLog(logParams)\r\n    },\r\n    // 获取文件路径设置集合\r\n    getSettingList () {\r\n      this.settingForm = {}\r\n      let params = {}\r\n      Object.assign(params, this.pageInfo)\r\n      let wjxgPage = selectWjxgList(params)\r\n      this.settingList = wjxgPage.list\r\n      this.pageInfo.total = wjxgPage.total\r\n    },\r\n    // 添加文件路径设置\r\n    addSetting () {\r\n      console.log('表单数据', this.settingForm)\r\n      insertWjxgList(this.settingForm)\r\n      // 写入日志\r\n      let logParams = {\r\n        xyybs: 'yybs_wjljsz',\r\n        ymngnmc: '添加',\r\n        extraParams: this.settingForm\r\n      }\r\n      writeSystemOptionsLog(logParams)\r\n      //\r\n      this.getSettingList()\r\n      this.dialogVisibleSetting = false\r\n    }\r\n  },\r\n  mounted () {\r\n    //\r\n    this.getSettingList()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.out-card {\r\n  /* margin-bottom: 10px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */\r\n}\r\n/**单位信息区域**/\r\n.out-card .out-card-div {\r\n  font-size: 13px;\r\n  padding: 5px 20px;\r\n}\r\n.out-card .out-card-div div {\r\n  padding: 10px 5px;\r\n  display: flex;\r\n}\r\n.out-card .dwxx div:hover {\r\n  background: #f4f4f5;\r\n  border-radius: 20px;\r\n}\r\n.out-card .dwxx div label {\r\n  /* background-color: red; */\r\n  width: 125px;\r\n  display: inline-block;\r\n  text-align: right;\r\n  font-weight: 600;\r\n  color: #909399;\r\n}\r\n.out-card .dwxx div span {\r\n  /* background-color: rgb(33, 92, 79); */\r\n  flex: 1;\r\n  display: inline-block;\r\n  padding-left: 20px;\r\n}\r\n</style>\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/xtsz/filePathSetting.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"height\":\"100%\"}},[_c('hsoft_top_title',{scopedSlots:_vm._u([{key:\"left\",fn:function(){return [_vm._v(\"文件路径设置\")]},proxy:true}])}),_vm._v(\" \"),_c('div',{staticStyle:{\"padding\":\"10px 0\",\"text-align\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"success\"},on:{\"click\":function($event){_vm.dialogVisibleSetting = true}}},[_vm._v(\"添加\")])],1),_vm._v(\" \"),_c('el-table',{staticClass:\"table\",staticStyle:{\"width\":\"100%\",\"border\":\"1px solid #EBEEF5\"},attrs:{\"data\":_vm.settingList,\"border\":\"\",\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"height\":\"calc(100% - 32px - 60px - 32px - 10px - 10px)\",\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"csbs\",\"label\":\"路径标识\",\"width\":\"100\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cssm\",\"label\":\"路径说明\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"csz\",\"label\":\"路径\",\"width\":\"\",\"align\":\"left\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gxsj\",\"label\":\"修改时间\",\"width\":\"200\",\"align\":\"left\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scoped){return [_c('span',[_vm._v(_vm._s(_vm.formatTime(scoped.row.gxsj)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"\",\"label\":\"操作\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scoped){return [_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.modifySetting(scoped.row)}}},[_vm._v(\"修改\")]),_vm._v(\" \"),_c('el-button',{staticStyle:{\"color\":\"#F56C6C\"},attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.deleteSetting(scoped.row)}}},[_vm._v(\"删除\")])]}}])})],1),_vm._v(\" \"),_c('el-pagination',{staticStyle:{\"padding-top\":\"10px\"},attrs:{\"background\":\"\",\"pager-count\":5,\"current-page\":_vm.pageInfo.page,\"page-sizes\":[5, 10, 20, 30],\"page-size\":_vm.pageInfo.pageSize,\"layout\":\"total, prev, pager, sizes,next, jumper\",\"total\":_vm.pageInfo.total},on:{\"current-change\":_vm.handleCurrentChange,\"size-change\":_vm.handleSizeChange}}),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"添加文件路径\",\"visible\":_vm.dialogVisibleSetting,\"width\":\"35%\"},on:{\"update:visible\":function($event){_vm.dialogVisibleSetting=$event}}},[_c('div',[_c('el-form',{attrs:{\"label-position\":'right',\"label-width\":\"120px\",\"size\":\"mini\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"路径标识\"}},[_c('el-input',{model:{value:(_vm.settingForm.csbs),callback:function ($$v) {_vm.$set(_vm.settingForm, \"csbs\", $$v)},expression:\"settingForm.csbs\"}})],1)],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"one-line-textarea\",attrs:{\"label\":\"路径说明\"}},[_c('el-input',{attrs:{\"type\":\"textarea\"},model:{value:(_vm.settingForm.cssm),callback:function ($$v) {_vm.$set(_vm.settingForm, \"cssm\", $$v)},expression:\"settingForm.cssm\"}})],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"路径\"}},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.choosePath()}}},[_vm._v(\"选择路径\")]),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(_vm.settingForm.csz))])],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"one-line\",attrs:{\"label\":\"分组号\"}},[_c('el-input',{model:{value:(_vm.settingForm.fzh),callback:function ($$v) {_vm.$set(_vm.settingForm, \"fzh\", $$v)},expression:\"settingForm.fzh\"}})],1)],1)],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.addSetting()}}},[_vm._v(\"保 存\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.dialogVisibleSetting = false}}},[_vm._v(\"取 消\")])],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-15da3680\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/xtsz/filePathSetting.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-15da3680\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./filePathSetting.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./filePathSetting.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./filePathSetting.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-15da3680\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./filePathSetting.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-15da3680\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/xtsz/filePathSetting.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}