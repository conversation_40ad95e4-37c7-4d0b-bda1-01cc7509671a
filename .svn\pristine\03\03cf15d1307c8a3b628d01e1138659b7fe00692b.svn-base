<template>
  <div>
    <hsoft_top_title>
      <template #left>修改密码</template>
    </hsoft_top_title>
    <!---->
    <div class="article">
      <div>
        <el-form ref="form" :model="form" label-width="80px" size="mini" :label-position="'right'" :rules="rules">
          <el-form-item label="姓名" prop="xm" style="position: relative;">
            <el-input v-model="form.xm" size="medium"></el-input>
          </el-form-item>
          <el-form-item label="用户名" prop="yhm">
            <el-input v-model="form.yhm" size="medium" disabled></el-input>
          </el-form-item>
          <el-form-item label="旧密码" prop="passwordOld">
            <el-input v-model="form.passwordOld" show-password size="medium" @blur="yzjmm"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="passwordNew">
            <el-input v-model="form.passwordNew" show-password size="medium"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="passwordCheck">
            <el-input v-model="form.passwordCheck" show-password size="medium"></el-input>
          </el-form-item>
        </el-form>
        <div style="display:flex;align-items: center;justify-content: flex-end;">
          <el-button type="primary" @click="updatePassword">保 存</el-button>
          <el-button type="warning" @click="$router.go(-1)">返 回</el-button>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import MD5 from 'md5'

import { getWindowLocation, removeWindowLocation } from '../../../utils/windowLocation'

import {
  getUserInfo,
  verifyPassword,
  updateXmAndMm,
} from '../../../api/dwzc'

import { dateFormatChinese } from '../../../utils/moment'

// import { writeSystemOptionsLog } from '../../../utils/logUtils'

// import { selectYhByYhm, updateYhByYhid } from '../../../db/yhdb'

export default {
  data() {

    // 旧密码验证
    const pwdOldCheck = async (rule, value, callback) => {
      console.log(value)
      // 验证旧密码与库里的密码是否相同
      if (value) {
        let params = {
          password: this.form.passwordOld
        }
        this.oldCode = await verifyPassword(params)
        let code = this.oldCode.code
        if (code === 10000) {
          callback()
        } else {
          callback(new Error('请输入正确的旧密码'))
        }
      } else {
        callback(new Error('旧密码不能为空'))
      }
    }

    // 密码验证
    const pwdCheck = async (rule, value, callback) => {
      let reg = /(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,16}/
      if (value) {
        if (value.length < 6) {
          this.changeFlag = 2;
          return callback(new Error('密码不能少于6位！'));
        } else if (value.length > 16) {
          this.changeFlag = 2;
          return callback(new Error('密码最长不能超过16位！'));
        }else if(!reg.test(value)){
          return callback(new Error('密码必须同时包含大小写字母、数字和特殊字符且至少6位'));
        } else {
          this.changeFlag = 1;
          callback()
        }
      } else {
        callback(new Error('新密码不能为空'))
      }
    }

    // 重复密码验证
    const pwdAgainCheck = async (rule, value, callback) => {
      if (value) {
        if (value.length < 1) {
          this.changeAgainFlag = 2;
          return callback(new Error('重复密码不能为空！'));
        } else if (this.form.passwordNew != this.form.passwordCheck) {
          this.changeAgainFlag = 2;
          return callback(new Error('两次输入密码不一致！'));
        } else {
          this.changeAgainFlag = 1;
          callback()
        }
      } else {
        callback(new Error('请再次输入新密码'))
      }
    }

    return {
      // 表单数据
      form: {
        xm: '',
        yhm: '',
        passwordOld: '',
        password: '',
        passwordCheck: '',
        oldCode: '',
      },
      //表单验证
      rules: {
        passwordOld: [{ required: true, validator: pwdOldCheck, trigger: 'blur' }],
        passwordNew: [{ required: true, validator: pwdCheck, trigger: 'blur' }],
        passwordCheck: [{ required: true, validator: pwdAgainCheck, trigger: 'blur' }],
      }
    }
  },
  components: {
    hsoft_top_title
  },
  mounted() {
    // 获取当前登录用户的信息
    this.getUser()
  },
  methods: {
    async getUser() {
      let datalogin = await getUserInfo()
      this.form = datalogin
      console.log("获取登陆数据:", this.form);
    },
    //验证旧密码
    async yzjmm(val) {
      let params = {
        password: this.form.passwordOld
      }
      this.oldCode = await verifyPassword(params)
      console.log('验证旧密码：', this.oldCode);
    },

    // 修改密码
    async updatePassword() {
      console.log(this.$refs)
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.yhm == 'root') {
            this.$message.warning('超级管理员账号不能修改密码')
            return
          }
          let params = {
            xm: this.form.xm,
            newPassword: this.form.passwordNew,
          }
          console.log('修改密码入参', params)
          let updateBool = updateXmAndMm(params)
          if (updateBool) {
            this.$message.success('修改密码成功')
            // 写入日志
            // let logParams = {
            // xyybs: 'yybs_xgmm',
            // ymngnmc: '保存'
            // }
            // writeSystemOptionsLog(logParams)
            // 清空缓存
            removeWindowLocation()
            // 返回登录页
            this.$router.push('/')
            return
          }
          this.$message.error('修改密码失败')
        }
      })
    }
  },

}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}

/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
}

.out-card .out-card-div div {
  padding: 10px 5px;
  display: flex;
}

.out-card .dwxx div:hover {
  background: #f4f4f5;
  border-radius: 20px;
}

.out-card .dwxx div label {
  /* background-color: red; */
  width: 125px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}

.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  flex: 1;
  display: inline-block;
  padding-left: 20px;
}

/****/
.article {
  text-align: center;
  padding: 2% 0;
  /* background: red; */
}

.article>div {
  width: 50%;
  margin: 0 auto;
}
</style>