<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">汇总情况</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="台账时间" style="font-weight: 700;">
                  <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widthw">
										</el-input> -->
                  <el-select v-model="formInline.tzsj" placeholder="台账时间">
                    <el-option v-for="item in yearSelect" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="部门" style="font-weight: 700;">
                  <!-- <el-input v-model="formInline.bmmc" clearable placeholder="部门" class="widthw">
									</el-input> -->
                  <el-cascader v-model="formInline.bmmc" :options="regionOption" :props="regionParams" filterable
                    clearable ref="cascaderArr" @change="cxbm"></el-cascader>
                </el-form-item>
                <el-form-item label="姓名" style="font-weight: 700;">
                  <el-input v-model="formInline.xm" clearable placeholder="姓名" class="widthw">
                  </el-input>
                </el-form-item>

                <!-- <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="aaa">aaa</el-button>
                </el-form-item> -->
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <!-- <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">删除
                  </el-button>
                </el-form-item> -->
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" @click="fh()">返回
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList">
                    导出
                  </el-button>
                </el-form-item>
                <!-- <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="xz" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item> -->
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="smryList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="xm" label="姓名"></el-table-column>
                  <el-table-column prop="bmmc" label="部门"></el-table-column>
                  <el-table-column prop="gwmc" label="岗位名称">
                    <template slot-scope="scoped">
                      <div>
                        {{ scoped.row.gwmc.join(',') }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="smdj" label="涉密等级" :formatter="forsmdj"></el-table-column>
                  <el-table-column prop="zw" label="职务"></el-table-column>
                  <el-table-column prop="zj" label="职级"></el-table-column>
                  <el-table-column prop="jbzc" label="职称" :formatter="forzc"></el-table-column>
                  <el-table-column prop="sfsc" label="是否审查" :formatter="forsc"></el-table-column>
                  <el-table-column prop="sgsj" label="上岗时间"></el-table-column>
                  <el-table-column prop="tznf" label="台账时间"></el-table-column>
                  <!-- <el-table-column prop="bz" label="备注"></el-table-column> -->
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button> -->
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入涉密人员汇总情况" class="scbg-dialog" :visible.sync="dialogVisible_dr"
          show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column prop="姓名" label="姓名"></el-table-column>
              <el-table-column prop="部门" label="部门"></el-table-column>
              <el-table-column prop="岗位名称" label="岗位名称"></el-table-column>
              <el-table-column prop="涉密等级" label="涉密等级"></el-table-column>
              <el-table-column prop="职务" label="职务"></el-table-column>
              <el-table-column prop="职级" label="职级"></el-table-column>
              <el-table-column prop="级别职称" label="级别职称"></el-table-column>
              <el-table-column prop="是否审查" label="是否审查"></el-table-column>
              <el-table-column prop="上岗时间" label="上岗时间"></el-table-column>
              <el-table-column prop="备注" label="备注">
              </el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>
        <!-- -----------------追加模式已存在数据展示--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入[追加模式]已存在涉密人员汇总情况" class="scbg-dialog"
          :visible.sync="dialogVisible_dr_zj" show-close>
          <div style="height: 600px;">
            <el-table :data="existDrList" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55" align="center"> </el-table-column>
              <el-table-column prop="姓名" label="姓名"></el-table-column>
              <el-table-column prop="部门" label="部门"></el-table-column>
              <el-table-column prop="岗位名称" label="岗位名称"></el-table-column>
              <el-table-column prop="涉密等级" label="涉密等级"></el-table-column>
              <el-table-column prop="职务" label="职务"></el-table-column>
              <el-table-column prop="职级" label="职级"></el-table-column>
              <el-table-column prop="级别职称" label="级别职称"></el-table-column>
              <el-table-column prop="是否审查" label="是否审查"></el-table-column>
              <el-table-column prop="上岗时间" label="上岗时间"></el-table-column>
              <el-table-column prop="备注" label="备注">
              </el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="fgDr" size="mini">覆 盖</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>

        </el-dialog>

        <!-- -----------------新增涉密人员-弹窗--------------------------- -->
        <el-dialog title="新增涉密人员" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" size="mini" label-width="152px"
            :label-position="labelPosition">
            <div style="display:flex">
              <el-form-item label="姓名" prop="xm">
                <el-input placeholder="姓名" v-model="tjlist.xm" clearable style="width: 100%"></el-input>
              </el-form-item>
              <el-form-item label="身份证号码" prop="sfzhm">
                <el-input placeholder="身份证号码" v-model="tjlist.sfzhm" clearable @blur="onInputBlur(1)" style="width:100%">
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="性别" prop="xb">
                <el-radio-group v-model="tjlist.xb">
                  <el-radio v-for="item in xb" :v-model="tjlist.xb" :label="item.id" :value="item.id" :key="item.id">
                    {{ item.xb }}</el-radio>
                  <!-- <el-radio label="女"></el-radio> -->
                </el-radio-group>
              </el-form-item>
              <el-form-item label="年龄" prop="nl">
                <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value" style="width:100%"
                  placeholder="年龄" v-model="tjlist.nl" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="联系电话">
                <el-input placeholder="联系电话" v-model="tjlist.lxdh" clearable oninput="value=value.replace(/[^\d.]/g,'')"
                  @blur="lxdh = $event.target.value">
                </el-input>
              </el-form-item>
              <el-form-item label="部门" prop="bmmc">
                <!-- <el-input v-model="tjlist.bmmc" clearable placeholder="部门"></el-input> -->
                <el-cascader v-model="tjlist.bmmc" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable ref="cascaderArr" @change="handleChange(1)"></el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="岗位名称" prop="gwmc">
                <el-select v-model="tjlist.gwmc" multiple placeholder="请选择岗位" @change="handleSelect"
                  style="height: 32px;width:100%;">
                  <el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="涉密等级" prop="smdj">
                <el-select v-model="tjlist.smdj" placeholder="请选择涉密等级" style="width: 100%;">
                  <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="岗位确定依据" prop="gwqdyj">
                <el-select v-model="tjlist.gwqdyj" placeholder="请选择岗位确定依据" style="width: 100%;">
                  <el-option v-for="item in gwqdyjxz" :label="item.mc" :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="最高学历" prop="zgxl">
                <el-select v-model="tjlist.zgxl" placeholder="请选择最高学历" style="width: 100%;">
                  <el-option v-for="item in zgxlxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="职务">
                <el-autocomplete class="inline-input" value-key="zw" v-model.trim="tjlist.zw" style="width:100%"
                  :fetch-suggestions="querySearchzw" placeholder="请输入职务名称">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="职级">
                <el-autocomplete class="inline-input" value-key="zj" v-model.trim="tjlist.zj" style="width:100%"
                  :fetch-suggestions="querySearchzj" placeholder="请输入职级名称">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="级别职称">
                <el-select v-model="tjlist.jbzc" placeholder="请选择级别职称" style="width:calc(100% - 20px)">
                  <el-option v-for="item in jbzcxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      （1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; right: 10px;top: 20px;"
                    slot="reference"></i>
                </el-popover>
              </el-form-item>
              <el-form-item label="身份类型" prop="sflx">
                <el-select v-model="tjlist.sflx" placeholder="请选择身份类型" style="width:calc(100% - 20px)">
                  <el-option v-for="item in sflxxz" :label="item.csm" :value="item.csz" :key="item.csz">
                  </el-option>
                </el-select>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; right: 10px;top: 20px;"
                    slot="reference"></i>

                </el-popover>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="用人形式" prop="yrxs">
                <el-select v-model="tjlist.yrxs" placeholder="请选择用人形式" style="width: 100%;">
                  <el-option v-for="item in yrxsxz" :label="item.csm" :value="item.csz" :key="item.csz">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否审查" prop="sfsc">
                <el-radio-group v-model="tjlist.sfsc">
                  <el-radio v-for="item in sfsc" v-model="tjlist.sfsc" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="出入境登记备案" prop="sfcrj">
                <el-radio-group v-model="tjlist.sfcrj">
                  <el-radio v-for="item in sfsc" v-model="tjlist.sfcrj" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                  <!-- <el-radio label="否"></el-radio> -->
                </el-radio-group>
              </el-form-item>
              <el-form-item label="统一保管出入境证件" prop="sfbgzj">
                <el-radio-group v-model="tjlist.sfbgzj">
                  <el-radio v-for="item in sfsc" v-model="tjlist.sfbgzj" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="邮箱" class="one-line">
                <el-input placeholder="邮箱" v-model="tjlist.yx" clearable style="width: 100%;">
                </el-input>
              </el-form-item>
              <el-form-item label="上岗时间" prop="sgsj" class="one-line">
                <el-date-picker v-model="tjlist.sgsj" style="width:100%;" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </div>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz" style="width:100%;"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="handleClose()">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改涉密人员" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%" class="xg"
          @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="152px" size="mini"
            :label-position="labelPosition">
            <div style="display:flex">
              <el-form-item label="姓名" prop="xm">
                <el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input>
              </el-form-item>
              <el-form-item label="身份证号码" prop="sfzhm">
                <el-input placeholder="身份证号码" v-model="xglist.sfzhm" clearable @blur="onInputBlur(1)" style="width:100%">
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="性别" prop="xb">
                <el-radio-group v-model="xglist.xb">
                  <el-radio v-for="item in xb" :v-model="xglist.xb" :label="item.id" :value="item.id" :key="item.id">
                    {{ item.xb }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="年龄" prop="nl">
                <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value" style="width:100%"
                  placeholder="年龄" v-model="xglist.nl" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="联系电话">
                <el-input placeholder="联系电话" v-model="xglist.lxdh" clearable oninput="value=value.replace(/[^\d.]/g,'')"
                  @blur="lxdh = $event.target.value">
                </el-input>
              </el-form-item>
              <el-form-item label="部门" prop="bmmc">
                <!-- <el-input v-model="xglist.bmmc" clearable placeholder="部门"></el-input> -->
                <el-cascader v-model="xglist.bmmc" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable ref="cascaderArr" @change="handleChange(2)"></el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="岗位名称" prop="gwmc">
                <el-select v-model="xglist.gwmc" multiple placeholder="请选择岗位" @change="handleSelect1"
                  style="height: 32px;width:100%;">
                  <el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="涉密等级" prop="smdj">
                <el-select v-model="xglist.smdj" placeholder="请选择涉密等级" style="width: 100%;">
                  <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="岗位确定依据" prop="gwqdyj">
                <el-select v-model="xglist.gwqdyj" placeholder="请选择岗位确定依据" style="width: 100%;">
                  <el-option v-for="item in gwqdyjxz" :label="item.mc" :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="最高学历" prop="zgxl">
                <el-select v-model="xglist.zgxl" placeholder="请选择最高学历" style="width: 100%;">
                  <el-option v-for="item in zgxlxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="职务">
                <el-autocomplete class="inline-input" value-key="zw" v-model.trim="xglist.zw" style="width:100%"
                  :fetch-suggestions="querySearchzw" placeholder="请输入职务名称">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="职级">
                <el-autocomplete class="inline-input" value-key="zj" v-model.trim="xglist.zj" style="width:100%"
                  :fetch-suggestions="querySearchzj" placeholder="请输入职级名称">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="级别职称">
                <el-select v-model="xglist.jbzc" placeholder="请选择级别职称" style="width:calc(100% - 20px)">
                  <el-option v-for="item in jbzcxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      （1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; right: 10px;top: 20px;"
                    slot="reference"></i>
                </el-popover>
              </el-form-item>
              <el-form-item label="身份类型" prop="sflx">
                <el-select v-model="xglist.sflx" placeholder="请选择身份类型" style="width:calc(100% - 20px)">
                  <el-option v-for="item in sflxxz" :label="item.csm" :value="item.csz" :key="item.csz">
                  </el-option>
                </el-select>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; right: 10px;top: 20px;"
                    slot="reference"></i>

                </el-popover>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="用人形式" prop="yrxs">
                <el-select v-model="xglist.yrxs" placeholder="请选择用人形式" style="width: 100%;">
                  <el-option v-for="item in yrxsxz" :label="item.csm" :value="item.csz" :key="item.csz">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否审查" prop="sfsc">
                <el-radio-group v-model="xglist.sfsc">
                  <el-radio v-for="item in sfsc" v-model="xglist.sfsc" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="出入境登记备案" prop="sfcrj">
                <el-radio-group v-model="xglist.sfcrj">
                  <el-radio v-for="item in sfsc" v-model="xglist.sfcrj" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="统一保管出入境证件" prop="sfbgzj">
                <el-radio-group v-model="xglist.sfbgzj">
                  <el-radio v-for="item in sfsc" v-model="xglist.sfbgzj" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="邮箱" class="one-line">
                <el-input placeholder="邮箱" v-model="xglist.yx" clearable style="width: 100%;">
                </el-input>
              </el-form-item>
              <el-form-item label="上岗时间" prop="sgsj" class="one-line">
                <!-- <el-input v-model="xglist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.sgsj" style="width:100%;" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd日" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </div>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="涉密人员详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%" class="xg">
          <el-form ref="form" :model="xglist" label-width="152px" size="mini" :label-position="labelPosition" disabled>
            <div style="display:flex">
              <el-form-item label="姓名" prop="xm">
                <el-input placeholder="姓名" v-model="xglist.xm" clearable></el-input>
              </el-form-item>
              <el-form-item label="身份证号码" prop="sfzhm">
                <el-input placeholder="身份证号码" v-model="xglist.sfzhm" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="性别" prop="xb">
                <el-radio-group v-model="xglist.xb">
                  <el-radio v-for="item in xb" :v-model="xglist.xb" :label="item.id" :value="item.id" :key="item.id">
                    {{ item.xb }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="年龄" prop="nl">
                <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="nl = $event.target.value" style="width:100%"
                  placeholder="年龄" v-model="xglist.nl" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="联系电话">
                <el-input placeholder="联系电话" v-model="xglist.lxdh" clearable oninput="value=value.replace(/[^\d.]/g,'')"
                  @blur="lxdh = $event.target.value">
                </el-input>
              </el-form-item>
              <el-form-item label="部门" prop="bmmc">
                <!-- <el-input v-model="xglist.bmmc" clearable placeholder="部门"></el-input> -->
                <el-cascader v-model="xglist.bmmc" :options="regionOption" :props="regionParams" style="width: 100%;"
                  filterable ref="cascaderArr" @change="handleChange(2)"></el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="岗位名称" prop="gwmc">
                <el-select v-model="xglist.gwmc" placeholder="请选择岗位" @change="handleSelect" multiple
                  style="height: 32px;width:100%;">
                  <el-option v-for="(item, label) in gwmc" :label="item.gwmc" :value="item.gwmc" :key="label">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="涉密等级" prop="smdj">
                <el-select v-model="xglist.smdj" placeholder="请选择涉密等级" style="width: 100%;">
                  <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="岗位确定依据" prop="gwqdyj">
                <el-select v-model="xglist.gwqdyj" placeholder="请选择岗位确定依据" style="width: 100%;">
                  <el-option v-for="item in gwqdyjxz" :label="item.mc" :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="最高学历" prop="zgxl">
                <el-select v-model="xglist.zgxl" placeholder="请选择最高学历" style="width: 100%;">
                  <el-option v-for="item in zgxlxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="职务">
                <el-autocomplete class="inline-input" value-key="zw" v-model.trim="xglist.zw" style="width:100%"
                  :fetch-suggestions="querySearchzw" placeholder="请输入职务名称">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="职级">
                <el-autocomplete class="inline-input" value-key="zj" v-model.trim="xglist.zj" style="width:100%"
                  :fetch-suggestions="querySearchzj" placeholder="请输入职级名称">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="级别职称">
                <el-select v-model="xglist.jbzc" placeholder="请选择级别职称" style="width:cacl(100% - 20px)">
                  <el-option v-for="item in jbzcxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      （1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; right: 10px;top: 20px;"
                    slot="reference"></i>
                </el-popover>
              </el-form-item>
              <el-form-item label="身份类型" prop="sflx">
                <el-select v-model="xglist.sflx" placeholder="请选择身份类型" style="width:calc(100% - 20px)">
                  <el-option v-for="item in sflxxz" :label="item.csm" :value="item.csz" :key="item.csz">
                  </el-option>
                </el-select>
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; right: 10px;top: 20px;"
                    slot="reference"></i>

                </el-popover>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="用人形式" prop="yrxs">
                <el-select v-model="xglist.yrxs" placeholder="请选择用人形式" style="width: 100%;">
                  <el-option v-for="item in yrxsxz" :label="item.csm" :value="item.csz" :key="item.csz">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否审查" prop="sfsc">
                <el-radio-group v-model="xglist.sfsc">
                  <el-radio v-for="item in sfsc" v-model="xglist.sfsc" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="出入境登记备案" prop="sfcrj">
                <el-radio-group v-model="xglist.sfcrj">
                  <el-radio v-for="item in sfsc" v-model="xglist.sfcrj" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="统一保管出入境证件" prop="sfbgzj">
                <el-radio-group v-model="xglist.sfbgzj">
                  <el-radio v-for="item in sfsc" v-model="xglist.sfbgzj" :label="item.sfscid" :value="item.sfscmc"
                    :key="item.id">{{ item.sfscmc }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="邮箱" class="one-line">
                <el-input placeholder="邮箱" v-model="xglist.yx" clearable style="width: 100%;">
                </el-input>
              </el-form-item>
              <el-form-item label="上岗时间" prop="sgsj" class="one-line">
                <!-- <el-input v-model="xglist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.sgsj" style="width:100%;" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </div>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getYhxxList,
  saveSmry,
  updateYhxx,
  removeSmry,
  getZzjgList, //获取全部zzjgList
  getAllYhxx,
  getLoginInfo
} from '../../../api/index'
import {
  getSmryHistoryPage
} from '../../../api/lstz'
import {
  exportLsSmryData
} from '../../../api/dcwj'
import {
  getAllSmdj,
  getAllGwqdyj,
  getAllXl,
  getAllJbzc,
  getAllYsxs,
  getAllSflx
} from '../../../api/xlxz'
import {
  getAllGwxx
} from '../../../api/qblist'
import {
  getCurYhxx
} from '../../../api/zhyl'
import {
  yhxxverify
} from '../../../api/jy'
export default {
  components: {},
  props: {},
  data() {
    var isMobileNumber = (rule, value, callback) => {
      if (!value) {
        return new Error('请输入电话号码')
      } else {
        const reg =
          /^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/
        const isPhone = reg.test(value)
        value = Number(value) //转换为数字
        if (typeof value === 'number' && !isNaN(value)) {
          //判断是否为数字
          value = value.toString() //转换成字符串
          if (value.length < 0 || value.length > 12 || !isPhone) {
            //判断是否为11位手机号
            callback(new Error('手机号格式:138xxxx8754'))
          } else {
            callback()
          }
        } else {
          callback(new Error('请输入电话号码'))
        }
      }
    }
    const isCnNewID = (rule, value, callback) => {
      var arrExp = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; //加权因子
      var arrValid = [1, 0, "X", 9, 8, 7, 6, 5, 4, 3, 2]; //校验码
      if (/^\d{17}\d|x$/i.test(value)) {
        var sum = 0,
          idx;
        for (var i = 0; i < value.length - 1; i++) {
          // 对前17位数字与权值乘积求和
          sum += parseInt(value.substr(i, 1), 10) * arrExp[i];
        }
        // 计算模（固定算法）
        idx = sum % 11;
        // 检验第18为是否与校验码相等
        if (arrValid[idx] == value.substr(17, 1).toUpperCase()) {
          callback()
          if (this.tjlist.sfzhm) {
            var org_birthday = this.tjlist.sfzhm.substring(6, 14);
            var org_gender = this.tjlist.sfzhm.substring(16, 17);
            var sex = org_gender % 2 == 1 ? 1 : 2;
            var birthday =
              org_birthday.substring(0, 4) +
              "-" +
              org_birthday.substring(4, 6) +
              "-" +
              org_birthday.substring(6, 8);
            var birthdays = new Date(birthday.replace(/-/g, "/"));
            let d = new Date();
            let age =
              d.getFullYear() -
              birthdays.getFullYear() -
              (d.getMonth() < birthdays.getMonth() ||
                (d.getMonth() == birthdays.getMonth() &&
                  d.getDate() < birthdays.getDate()) ?
                1 :
                0);
            this.tjlist.xb = sex;
            // this.form.birthday = birthdays;
            this.tjlist.nl = age;
          }
          if (this.xglist.sfzhm) {
            var org_birthday = this.xglist.sfzhm.substring(6, 14);
            var org_gender = this.xglist.sfzhm.substring(16, 17);
            var sex = org_gender % 2 == 1 ? 1 : 2;
            var birthday =
              org_birthday.substring(0, 4) +
              "-" +
              org_birthday.substring(4, 6) +
              "-" +
              org_birthday.substring(6, 8);
            var birthdays = new Date(birthday.replace(/-/g, "/"));
            let d = new Date();
            let age =
              d.getFullYear() -
              birthdays.getFullYear() -
              (d.getMonth() < birthdays.getMonth() ||
                (d.getMonth() == birthdays.getMonth() &&
                  d.getDate() < birthdays.getDate()) ?
                1 :
                0);
            this.xglist.xb = sex;
            // this.form.birthday = birthdays;
            this.xglist.nl = age;
          }
        } else {
          callback("身份证格式有误")
        }
      } else {
        callback("身份证格式有误")
      }
    }
    return {
      yearSelect: [],
      // 身份证号码已存在记录集合(追加模式)
      existDrList: [],
      dialogVisible_dr_zj: false,
      //
      sfzhm: '',
      pdmsfzhm: 0,
      smdjxz: [],
      gwqdyjxz: [],
      jbzcxz: [],
      zgxlxz: [],
      sflxxz: [

      ],
      yrxsxz: [],
      gwmc: [],
      xb: [{
        xb: '男',
        id: 1
      },
      {
        xb: '女',
        id: 2
      },
      ],
      sfsc: [{
        sfscid: 1,
        sfscmc: '是'
      },
      {
        sfscid: 0,
        sfscmc: '否'
      },
      ],
      labelPosition: 'right',
      smryList: [],

      formInline: {
        xm: undefined,
        bmmc: undefined,
        tzsj: new Date().getFullYear().toString()
      },

      tjlist: {
        xm: '',
        sfzhm: '',
        xb: '',
        nl: '',
        lxdh: '',
        bmmc: '',
        gwmc: '',
        smdj: '',
        gwqdyj: '',
        zgxl: '',
        zw: '',
        jbzc: '',
        zc: '',
        gwdyjb: '',
        sflx: '',
        yrxs: '',
        sfsc: 1,
        sfcrj: 1,
        sfbgzj: 1,
        yx: '',
        sgsj: '',
        bz: '',
      },
      xglist: {},
      bmid: '',
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        xm: [{
          required: true,
          message: '请输入姓名',
          trigger: 'blur'
        },],
        sfzhm: [{
          required: true,
          message: "身份证不能为空",
          trigger: "blur"
        },
        { //调用上面定义的方法校验格式是否正确
          validator: isCnNewID,
          trigger: "blur"
        }
        ],
        xb: [{
          required: true,
          message: '请选择性别',
          trigger: 'blur'
        },],
        nl: [{
          required: true,
          message: '请输入年龄',
          trigger: 'blur'
        },],
        bmmc: [{
          required: true,
          message: '请输入部门',
          trigger: ['blur', 'change'],
        },],
        gwmc: [{
          required: true,
          message: '请输入岗位名称',
          trigger: 'blur'
        },],
        smdj: [{
          required: true,
          message: '请选择涉密等级',
          trigger: 'blur'
        },],
        gwqdyj: [{
          required: true,
          message: '请选择岗位确定依据',
          trigger: 'blur'
        },],
        zgxl: [{
          required: true,
          message: '请选择最高学历',
          trigger: 'blur'
        },],
        zw: [{
          required: true,
          message: '请输入职务',
          trigger: 'blur'
        },],
        jbzc: [{
          required: true,
          message: '请输入职级',
          trigger: 'blur'
        },],
        zc: [{
          required: true,
          message: '请选择级别职称',
          trigger: 'blur'
        },],
        // gwdyjb: [{
        // 	required: true,
        // 	message: '请选择岗位对应级别',
        // 	trigger: 'blur'
        // },],
        sflx: [{
          required: true,
          message: '请选择身份类型',
          trigger: 'blur'
        },],
        yrxs: [{
          required: true,
          message: '请选择用人形式',
          trigger: 'blur'
        },],
        sfsc: [{
          required: true,
          message: '请选择是否审查',
          trigger: 'blur'
        },],
        sfcrj: [{
          required: true,
          message: '请选择是否出入境登记备案',
          trigger: 'blur'
        },],
        sfcrj: [{
          required: true,
          message: '请选择是否出入境登记备案',
          trigger: 'blur'
        },],
        sfbgzj: [{
          required: true,
          message: '请选择是否统一保管出入境证件',
          trigger: 'blur'
        },],
        sfbgzj: [{
          required: true,
          message: '请选择是否统一保管出入境证件',
          trigger: 'blur'
        },],
        yx: [{
          required: true,
          message: '请输入邮箱',
          trigger: 'blur'
        },],
        sgsj: [{
          required: true,
          message: '请选择上岗时间（现涉密岗位）',
          trigger: 'blur'
        },],
        lxdh: [{
          required: true,
          message: '请输入联系电话',
          trigger: 'blur'
        }, {
          validator: isMobileNumber,
          trigger: 'blur'
        }],
        // bz: [{
        // 	required: true,
        // 	message: '请输入文件名',
        // 	trigger: 'blur'
        // },],
      },
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: '',
      cxbmsj: '',
    }
  },
  computed: {},
  mounted() {
    //获取最近十年的年份
    let yearArr = []
    for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
      yearArr.push(
        {
          label: i.toString(),
          value: i.toString()
        })
    }
    yearArr.unshift({
      label: "全部",
      value: ""
    })
    this.yearSelect = yearArr
    this.zwmh()
    this.smdj()
    this.gwqdyjlx()
    this.zgxl()
    this.jbzc()
    this.yrxs()
    this.sflx()
    this.smry()
    this.zzjg()
    this.zhsj()
  },
  methods: {
    //全部组织机构List
    async zzjg() {
      let zzjgList = await getZzjgList()
      console.log(zzjgList);
      this.zzjgmc = zzjgList
      let shu = []
      console.log(this.zzjgmc);
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            // console.log(item, item1);
            childrenRegionVo.push(item1)
            // console.log(childrenRegionVo);
            item.childrenRegionVo = childrenRegionVo
          }
        });
        // console.log(item);
        shu.push(item)
      })

      console.log(shu);
      console.log(shu[0].childrenRegionVo);
      let shuList = []
      let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      console.log(shuList);
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    async zhsj() {
      let ry = await getCurYhxx()
      console.log(ry);
      if (ry != '') {
        this.tjlist = ry
      }
      this.tjlist.xm = ''
      this.tjlist.sfzhm = ''
      this.tjlist.xb = 0
      this.tjlist.nl = ''
      this.tjlist.lxdh = ''
      this.tjlist.bz = ''
      this.tjlist.yx = ''
      // this.tjlist.gwmc = this.tjlist.gwmc.split(',')
      this.tjlist.bmmc = this.tjlist.bmmc.split('/')
    },
    //获取涉密等级信息

    async smdj() {
      let data = await getAllSmdj()
      this.smdjxz = data
    },
    //获取岗位确定依据
    async gwqdyjlx() {
      let data = await getAllGwqdyj()
      console.log(data);
      this.gwqdyjxz = data
    },
    //获取最高学历
    async zgxl() {
      let data = await getAllXl()
      console.log(data);
      this.zgxlxz = data
    },
    //获取级别职称
    async jbzc() {
      let data = await getAllJbzc()
      console.log(data);
      this.jbzcxz = data
    },
    //获取用人形式
    async yrxs() {
      let data = await getAllYsxs()
      console.log(data);
      this.yrxsxz = data
    },
    //获取身份类型
    async sflx() {
      let data = await getAllSflx()
      console.log(data);
      this.sflxxz = data
    },

    // //
    // aaa() {
    // let params = {
    //   aaa: '0/1'
    // }
    // smryDataStandard(params)
    // },
    // 时间显示格式转换
    formatTime(time) { },
    Radio(val) {

    },

    mbxzgb() {

    },
    mbdc() {

    },
    morenzhi() {

    },
    xz() {
      this.dialogVisible = true
    },
    // 覆盖导入（追加模式筛选出来的重复数据覆盖添加）
    fgDr() {


    },
    //导入
    chooseFile() {

    },
    //----成员组选择
    handleSelectionChange(val) {

    },
    //---确定导入成员组
    drcy() {

    },
    //----表格导入方法
    readExcel(e) {

    },
    //查询
    onSubmit() {
      this.page = 1
      this.smry()
    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    async smry() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        xm: this.formInline.xm,
        bmmc: this.cxbmsj
      }
      // params.tznf = this.formInline.tzsj
      if(this.formInline.tzsj){
        params.tznf = this.formInline.tzsj
      }
      if (this.cxbmsj == '') {
        params.bmmc = this.formInline.bmmc
      }
      let resList = await getSmryHistoryPage(params)
      this.smryList = resList.records
      this.total = resList.total
    },
    //删除
    shanchu(id) {
      let that = this
      this.$confirm('是否继续删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let valArr = this.selectlistRow
        // console.log("....", val);
        valArr.forEach(function (item) {
          let params = {
            smryid: item.smryid,
          }
          removeSmry(params).then(() => {
            that.smry()

          })
        })
        // let params = valArr
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      }).catch(() => {
        this.$message('已取消删除')
      })
    },
    //添加
    showDialog() {
      this.dialogVisible = true
    },
    fh() {
      this.$router.go(-1)
    },
    //添加重置
    resetForm() {
      this.tjlist.xm = '',
        this.tjlist.sfzhm = '',
        this.tjlist.xb = '',
        this.tjlist.nl = '',
        this.tjlist.lxdh = '',
        this.tjlist.bmmc = '',
        this.tjlist.gwmc = '',
        this.tjlist.smdj = '',
        this.tjlist.gwqdyj = '',
        this.tjlist.zgxl = '',
        this.tjlist.zw = '',
        this.tjlist.jbzc = '',
        this.tjlist.zc = '',
        // this.tjlist.gwdyjb = '',
        this.tjlist.sflx = '',
        this.tjlist.yrxs = '',
        this.tjlist.sfsc = '是',
        this.tjlist.sfcrj = '是',
        this.tjlist.sfbgzj = '是',
        this.tjlist.yx = '',
        this.tjlist.sgsj = this.Date,
        this.tjlist.bz = ''
    },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            xm: this.tjlist.xm,
            sfzhm: this.tjlist.sfzhm,
            dwid: '901',
            bmid: this.bmid,
            xb: this.tjlist.xb,
            nl: this.tjlist.nl,
            lxdh: this.tjlist.lxdh,
            bmmc: this.tjlist.bmmc.join('/'),
            gwmc: this.tjlist.gwmc,
            smdj: this.tjlist.smdj,
            gwqdyj: this.tjlist.gwqdyj,
            zgxl: this.tjlist.zgxl,
            zw: this.tjlist.zw,
            zj: this.tjlist.zj,
            jbzc: this.tjlist.jbzc,
            // zc: this.tjlist.zc,
            // gwdyjb: this.tjlist.gwdyjb,
            sflx: this.tjlist.sflx,
            yrxs: this.tjlist.yrxs,
            sfsc: this.tjlist.sfsc,
            sfbgzj: this.tjlist.sfbgzj,
            sfcrj: this.tjlist.sfcrj,
            // yx: this.tjlist.yx,
            sgsj: this.tjlist.sgsj,
            bz: this.tjlist.bz,
            // smryid :getUuid(),
            cjrid: '901',
          }
          this.onInputBlur(1)
          if (this.pdmsfzhm.code == 10000) {
            let that = this
            saveSmry(params).then(() => {
              that.resetForm()
              that.smry()
              that.zwmh()
              that.zjmh()
              that.morenzhi()
            })
            this.dialogVisible = false
            this.$message({
              message: '添加成功',
              type: 'success'
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },

    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          // 删除旧的
          // deletesmry(this.updateItemOld)
          // 插入新的
          let that = this
          this.xglist.bmmc = this.xglist.bmmc.join('/')
          updateYhxx(this.xglist).then(() => {
            that.smry()
            that.zwmh()
            that.zjmh()
          })
          // 刷新页面表格数据

          // 关闭dialog
          this.$message.success('修改成功')
          this.xgdialogVisible = false


        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);

      this.xglist.bmmc = this.xglist.bmmc.split('/')
      // this.xqdialogVisible = true
      this.xqdialogVisible = true
    },

    async updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))
      this.xglist = JSON.parse(JSON.stringify(row))
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      let param = {
        bmmc: this.xglist.bmmc
      }
      let resList = await getAllGwxx(param)

      this.restaurants = resList;
      this.gwmc = resList;
      console.log(this.restaurants);
      this.xglist.bmmc = this.xglist.bmmc.split('/')
      this.xgdialogVisible = true
    },

    deleteTkglBtn(id) {


    },
    selectRow(val) {
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.smry()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.smry()
    },

    handleClose(done) {
      this.resetForm()
      this.dialogVisible = false

    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    async exportList() {
      if (this.formInline.bmmc != undefined) {
        var param = {
          bmmc: this.formInline.bmmc,
          gwmc: this.formInline.xm,
          tznf: this.formInline.tzsj
        }
        var returnData = await exportLsSmryData(param);
      } else {
        var returnData = await exportLsSmryData({ 'nf': new Date().getFullYear().toString() });
      }
      var date = new Date()
      var sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "在岗涉密人员信息表-" + sj + ".xls");
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) // 创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) // 指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    querySearch1(queryString, cb) {

    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.gwmc.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    smbm() {

    },
    handleSelect(item) {

      let dx = []
      let hx = []
      let zy = []
      let yb = []
      item.forEach(item => {
        this.gwmc.forEach(item1 => {
          if (item == item1.gwmc) {
            dx.push(item1)
          }
        })
      })
      console.log(dx);
      dx.forEach(item => {
        console.log(item);
        if (item.smdj == 1) {
          hx.push(item)
        } else if (item.smdj == 2) {
          zy.push(item)
        } else {
          yb.push(item)
        }
      })
      // console.log('hx',hx);
      // console.log('zy',zy);
      // console.log('yb',yb);
      if (hx.length > 0) {
        this.tjlist.smdj = hx[0].smdj
        this.tjlist.gwqdyj = hx[0].gwqdyj
      } else if (zy.length > 0) {
        this.tjlist.smdj = zy[0].smdj
        this.tjlist.gwqdyj = zy[0].gwqdyj
      } else if (yb.length > 0) {
        this.tjlist.smdj = yb[0].smdj
        this.tjlist.gwqdyj = yb[0].gwqdyj
      }
    },
    handleSelect1(item) {
      let dx = []
      let hx = []
      let zy = []
      let yb = []
      item.forEach(item => {
        this.gwmc.forEach(item1 => {
          if (item == item1.gwmc) {
            dx.push(item1)
          }
        })
      })
      console.log(dx);
      dx.forEach(item => {
        if (item.smdj == '核心') {
          hx.push(item)
        } else if (item.smdj == '重要') {
          zy.push(item)
        } else {
          yb.push(item)
        }
      })
      console.log(hx);
      console.log(zy);
      console.log(yb);
      if (hx.length > 0) {
        this.xglist.smdj = hx[0].smdj
        this.xglist.gwqdyj = hx[0].gwqdyj
      } else if (zy.length > 0) {
        this.xglist.smdj = zy[0].smdj
        this.xglist.gwqdyj = zy[0].gwqdyj
      } else if (yb.length > 0) {
        this.xglist.smdj = yb[0].smdj
        this.xglist.gwqdyj = yb[0].gwqdyj
      }
    },
    async handleChange(index) {
      let nodesObj = this.$refs['cascaderArr'].getCheckedNodes()[0].data
      console.log(nodesObj);
      this.bmid = nodesObj.bmm
      let resList
      if (index == 1) {
        let params = {
          bmmc: this.tjlist.bmmc.join('/')
        }
        resList = await getAllGwxx(params)
      } else if (index == 2) {
        let params1 = {
          bmmc: this.xglist.bmmc.join('/')
        }
        resList = await getAllGwxx(params1)
      }
      console.log(resList);
      this.restaurants = resList;
      this.gwmc = resList
      if (this.gwmc.length == 0) {
        this.$message.error('该部门没有添加岗位');
      }
      console.log(this.gwmc);
      this.tjlist.gwmc = ''
      this.tjlist.smdj = ''
      this.tjlist.gwqdyj = ''
      this.xglist.gwmc = ''
      this.xglist.smdj = ''
      this.xglist.gwqdyj = ''
    },


    //模糊匹配职务
    querySearchzw(queryString, cb) {
      var restaurants = this.restaurantszw;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterzw(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].zw === results[j].zw) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterzw(queryString) {
      return (restaurant) => {
        return (restaurant.zw.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async zwmh() {
      let resList = await getAllYhxx()
      // console.log(resList);
      this.restaurantszw = resList;
      this.restaurantszj = resList;
      // console.log("this.restaurants", this.restaurantsbm);
      // console.log(resList)
    },
    //模糊匹配职级
    querySearchzj(queryString, cb) {
      var restaurants = this.restaurantszj;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterzj(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].zj === results[j].zj) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterzj(queryString) {
      return (restaurant) => {
        return (restaurant.zj.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    zjmh() {

    },
    async onInputBlur(index) {
      if (index == 1) {
        let params = {
          sfzhm: this.tjlist.sfzhm
        }
        this.pdmsfzhm = await yhxxverify(params)
        console.log(this.pdsmzt);
        if (this.pdmsfzhm.code == 20008) {
          this.$message.error('人员已存在');
        }
      }

    },
    cz() {
      this.cxbmsj = ''
      this.formInline = {}
    },
    cxbm(item) {
      console.log(item);
      if (item != undefined) {
        this.cxbmsj = item.join('/')
      }

    },
    //列表数据回显
    forsmdj(row) {
      let hxsj
      this.smdjxz.forEach(item => {
        if (row.smdj == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    forzc(row) {
      let hxsj
      this.jbzcxz.forEach(item => {
        if (row.jbzc == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    forsc(row) {
      let hxsj
      this.sfsc.forEach(item => {
        if (row.sfsc == item.sfscid) {
          hxsj = item.sfscmc
        }
      })
      return hxsj
    },
  },
  watch: {

  }
}

</script>

<style scoped>
.bg_con {
  width: 100%;
  /* background: url(../../assets/background/table_bg.png) no-repeat center; */
  background-size: cover;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widthw {
  width: 6vw;
}

/* /deep/.el-form-item__label {
	text-align: left;
} */
/deep/.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 184px;
}

/deep/.el-radio-group {
  width: 184px;
}

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

/deep/.el-dialog {
  margin-top: 6vh !important;
}

/deep/.inline-inputgw {
  width: 105%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/deep/.el-select .el-select__tags>span {
  display: flex !important;
  flex-wrap: wrap;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */
/deep/.el-dialog__body .el-form>div .el-form-item__label {
  width: 155px !important;
}

.bz {
  height: 72px !important;
}

/deep/.el-dialog__body .el-form>div>div {
  /* width: auto; */
  max-width: 100%;
}

.el-select__tags {
  white-space: nowrap;
  overflow: hidden;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
</style>
