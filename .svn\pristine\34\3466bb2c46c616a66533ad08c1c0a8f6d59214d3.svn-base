<template>
  <div class="sec-container" v-loading="loading">
    <!-- 标题 -->
    <p class="sec-title">基本信息</p>
    <div class="sec-form-container">
      <el-form ref="formName" :model="tjlist" label-width="225px">
        <!-- 第一部分包括姓名到常住地公安start -->
        <div class="sec-header-section">
          <div class="sec-form-left">
            <el-form-item label="申请人">
              <el-input placeholder="" v-model="tjlist.xqr" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="所在部门">
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.szbm" clearable disabled></el-input>
              </template>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="使用期限">
              <el-date-picker v-model="tjlist.wcqsrq" class="riq" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" disabled>
              </el-date-picker>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="知悉范围">
              <el-input placeholder="" v-model="tjlist.zxfw" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left sec-form-left-textarea">
            <el-form-item label="用途">
              <el-input placeholder="" type="textarea" v-model="tjlist.yt" clearable disabled></el-input>
            </el-form-item>
          </div>

        </div>
        <!-- 载体详细信息start -->
        <p class="sec-title">载体详细信息</p>
        <el-table border class="sec-el-table" :data="tjlist.ztwcxddjList"
          :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="ztmc" label="载体名称"></el-table-column>
          <el-table-column prop="xmbh" label="项目编号"></el-table-column>
          <el-table-column prop="ztbh" label="载体编号"></el-table-column>
          <el-table-column prop="lx" label="载体类型"></el-table-column>
          <el-table-column prop="smmj" label="密级"></el-table-column>
          <el-table-column prop="bmqx" label="保密期限"></el-table-column>
          <el-table-column prop="ys" label="页数/大小"></el-table-column>
          <el-table-column prop="fs" label="份数"></el-table-column>
        </el-table>
        <div class="sec-form-left">
          <el-form-item label="借阅人">
            <el-input placeholder="" v-model="tjlist.jyr" clearable disabled></el-input>
          </el-form-item>
          <el-form-item label="项目经理">
            <el-input placeholder="" v-model="tjlist.xmjl" clearable disabled></el-input>
          </el-form-item>
        </div>
        <div class="sec-form-left">
          <el-form-item label="归 还 人">
            <el-input placeholder="" v-model="tjlist.ghr" clearable></el-input>
          </el-form-item>
          <el-form-item label="归还日期">
            <el-input placeholder="" v-model="tjlist.ghrq" clearable></el-input>
          </el-form-item>
        </div>
        <div class="sec-form-left">
          <el-form-item label="接收检查人">
            <el-input placeholder="" v-model="tjlist.jsjcr" clearable></el-input>
          </el-form-item>
          <el-form-item label="载体情况">
            <el-checkbox-group v-model="tjlist.ztqk" class="checkbox1">
              <el-checkbox v-for="item in ztqkList" :label="item.ztqkmc" :value="item.ztqkid"
                :key="item.ztqkid"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <!-- 载体详细信息end -->
        <!-- 底部操作按钮start -->
        <div class="sec-form-six haveBorderTop sec-footer">
          <el-button @click="returnIndex" class="fr ml10" plain>返回</el-button>
          <!-- <el-button @click="chooseApproval" class="fr" type="success">保存并提交</el-button> -->
          <el-button @click="save" class="fr" type="primary">保存</el-button>
        </div>
        <!-- 底部操作按钮end -->

      </el-form>
    </div>
    <!-- 发起申请弹框start -->
    <el-dialog title="选择审批人" :close-on-click-modal="false" :visible.sync="approvalDialogVisible" width="40%">
      <div class="dlFqsqContainer">
        <label for="">部门:</label>
        <el-cascader v-model="ryChoose.bm" :options="regionOption" :props="regionParams" filterable clearable
          ref="cascaderArr" @change="bmSelectChange"></el-cascader>
        <label for="">姓名:</label>
        <el-input class="input2" v-model="ryChoose.xm" clearable placeholder="姓名"></el-input>
        <el-button class="searchButton" type="primary" icon="el-icon-search" @click="searchRy">查询</el-button>
        <BaseTable class="baseTable" :tableHeight="'300'" :key="tableKey" :showIndex=true :tableData="ryDatas" :columns="applyColumns"
          :showSingleSelection="true" :handleColumn="handleColumnApply" :showPagination=true :currentPage="page"
          :pageSize="pageSize" :totalCount="total" @handleCurrentChange="handleCurrentChangeRy"
          @handleSizeChange="handleSizeChangeRy" @handleSelectionChange="handleSelectionChange">
        </BaseTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" class="fr ml10" @click="approvalDialogVisible = false">关 闭</el-button>
        <el-button @click="saveAndSubmit" class="fr" type="success">提交</el-button>
        <!-- <el-button @click="save" class="fr" type="primary">保存</el-button> -->
        <div style="clear:both"></div>
      </span>
    </el-dialog>
    <!-- 发起申请弹框end -->
  </div>
</template>
<script>
import {
  getLcSLid,
  updateZgfs,
  updateSlzt,
  getZzjgList,
  getSpUserList,
  getCurZgfsjl,
  getFwdyidByFwlx,
  getAllYhxx
} from '../../../api/index'
import {
  getDjgwbgInfo,
  submitDjgwbg,
  getDjgwbgInfoByLcsllid,
  updateDjgwbg
} from '../../../api/djgwbg'
import { getAllGwxx } from '../../../api/qblist'
import { getAllSmdj } from '../../../api/xlxz'
import BaseTable from '../../components/common/baseTable.vue'
import AddLineTable from "../../components/common/addLineTable.vue"; //人工纠错组件
export default {
  components: {
    AddLineTable,
    BaseTable
  },
  props: {},
  data() {
    return {
      value1: '',
      loading: false,
      // 弹框人员选择条件
      ryChoose: {
        'bm': '',
        'xm': ''
      },
      gwmclist: [],
      smdjxz: [],
      regionOption: [], // 部门下拉
      page: 1, // 审批人弹框当前页
      pageSize: 10, // 审批人弹框每页条数
      radioIdSelect: '', // 审批人弹框人员单选
      ryDatas: [], // 弹框人员选择
      total: 0, // 弹框人员总数
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true
      }, //地域信息配置参数
      // table 行样式
      headerCellStyle: {
        background: '#EEF7FF',
        color: '#4D91F8'
      },
      // form表单提交数据
      tjlist: {
        xqr: '',
        szbm: '',
        wcqsrq: '',
        ztwcxddjList: [],
        zxfw: '',
        yt: '',
        jsdw: '',
        qsdd: '',
        mddd: '',
        fhcs: [],
        jtgj: [],
        ztqk: [],
        jtlx: '',
        ghr: '',
        ghrq: '',
        jsjcr: '',
        xdmmd: '',
        jyr: '',
        xmjl: '',
      },
      // // 载体详细信息
      // ztwcxddjList: [{
      //   'ztmc': '',
      //   'xmbh': '',
      //   'ztbh': '',
      //   'lx': '',
      //   'smmj': '',
      //   'bmqx': '',
      //   'ys': '',
      //   'fs': '',
      //   'czbtn1': '增加行',
      //   'czbtn2': '',
      // }],
      ztlxList: [
        {
          lxid: '1',
          lxmc: '纸介质'
        },
        {
          lxid: '2',
          lxmc: '光盘'
        },
        {
          lxid: '3',
          lxmc: '电磁介质'
        },
      ],
      smdjList: [
        {
          smdjid: '1',
          smdjmc: '绝密'
        },
        {
          smdjid: '2',
          smdjmc: '机密'
        },
        {
          smdjid: '3',
          smdjmc: '秘密'
        },
        {
          smdjid: '4',
          smdjmc: '内部'
        },
      ],
      xdfsList: [
        {
          xdfsid: '1',
          xdfsmc: '包装密封，封口处加盖密封章'
        },
        {
          xdfsid: '2',
          xdfsmc: '指派专人传递'
        },
        {
          xdfsid: '3',
          xdfsmc: '密码箱防护'
        },
      ],
      ztqkList: [
        {
          ztqkid: '1',
          ztqkmc: '载体无异常'
        },
        {
          ztqkid: '2',
          ztqkmc: '其他情况'
        },
      ],
      jtgjList: [
        {
          jtgjid: '1',
          jtgjmc: '飞机'
        },
        {
          jtgjid: '2',
          jtgjmc: '火车'
        },
        {
          jtgjid: '3',
          jtgjmc: '专车'
        },
      ],
      ryInfo: {},
      // 政治面貌下拉选项
      sltshow: '', // 文档的缩略图显示
      routeType: '',
      pdfBase64: '',
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      approvalDialogVisible: false, // 选择申请人弹框
      fileRow: '',
      // 选择审核人table
      applyColumns: [{
        name: '姓名',
        prop: 'xm',
        scopeType: 'text',
        formatter: false
      },
      {
        name: '部门',
        prop: 'bmmc',
        scopeType: 'text',
        formatter: false
      },
      {
        name: '岗位',
        prop: 'gwmc',
        scopeType: 'text',
        formatter: false
      }
      ],
      handleColumnApply: [],
      scqk: [
        {
          sfty: '同意',
          id: 1
        },
        {
          sfty: '不同意',
          id: 0
        },
      ],
      disabled2: false,
   
    }
  },
  computed: {
  
  },
  mounted() {
    this.onfwid()
    this.getOrganization()
    this.smdj()
    this.gwxx()
    this.yhDatas = this.$route.query.datas
    this.routeType = this.$route.query.type
    this.routezt = this.$route.query.zt
    console.log(this.routezt);
    let result = {}
    let iamgeBase64 = ''
    let iamgeBase64Brcn = ''
    if (this.$route.query.type == 'add') {
      // 首次发起申请
      result = {
        ...this.tjlist,
        ...this.$route.query.datas
      }
    } else {
      // 保存 继续编辑
      result = {
        ...this.tjlist,
        ...this.$route.query.datas
      }
    }
    this.tjlist = result
    if (this.tjlist.smdj == 1) {
      this.tjlist.smdj = '核心'
    } else if (this.tjlist.smdj == 2) {
      this.tjlist.smdj = '重要'
    } else if (this.tjlist.smdj == 3) {
      this.tjlist.smdj = '一般'
    }
    if (this.tjlist.xb == 1) {
      this.tjlist.xb = '男'
    } else if (this.tjlist.xb == 2) {
      this.tjlist.xb = '女'
    }
    console.log('this.tjlist', this.tjlist);
    // this.tjlist.yjqk = result.yjqk.toString()
    // this.tjlist.qscfqk = result.qscfqk.toString()
    console.log(this.tjlist);
    if (typeof iamgeBase64 === "string") {
      // 复制某条消息
      if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
      function validDataUrl(s) {
        return validDataUrl.regex.test(s);
      }
      validDataUrl.regex =
        /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
      if (validDataUrl(iamgeBase64)) {
        let that = this;

        function previwImg(item) {
          that.tjlist.imageUrl = item;
        }
        previwImg(iamgeBase64);
      }
    }

  },
  methods: {
    chRadio() { },
    async gwxx() {
      let param = {
        bmmc: this.tjlist.bmmc
      }
      let data = await getAllGwxx(param)
      this.gwmclist = data
      console.log(data);
    },
    //获取涉密等级信息
    async smdj() {
      let data = await getAllSmdj()
      this.smdjxz = data
    },
    handleSelectBghgwmc(item, i) {
      console.log(i);
      this.gwmclist.forEach(item1 => {
        if (i == item1.gwmc) {
          console.log(item1);
          this.tjlist.bgsmdj = item1.smdj
        }

      })
    },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    handleSelectionChange(index, row) {
      this.radioIdSelect = row
    },
    // 载体详细信息增加行
    addRow(data) {
      data.push({
        'ztmc': '',
        'xmbh': '',
        'ztbh': '',
        'lx': '',
        'smmj': '',
        'bmqx': '',
        'ys': '',
        'fs': '',
        'czbtn1': '增加行',
        'czbtn2': '删除',
      })
    },
    // 载体详细信息删除行
    delRow(index, rows) {
      rows.splice(index, 1)
    },

    async onfwid() {
      let params = {
        fwlx: 3
      }
      let data = await getFwdyidByFwlx(params)
      console.log(data);
      this.fwdyid = data.data.fwdyid
    },
    // 保存
    async save() {
      let param = {
        'fwdyid': this.fwdyid,
        'lcslclzt': 3
      }
      this.ryglRyscJtcyList.forEach((e) => {
        if (e.jwjlqk == '否') {
          e.jwjlqk = 0
        } else if (e.jwjlqk == '是') {
          e.jwjlqk = 1
        }
      })

      param.smryid = this.yhDatas.smryid
      this.tjlist.dwid = this.yhDatas.dwid
      let res = await getLcSLid(param)
      if (res.code == 10000) {
        this.tjlist.lcslid = res.data.slid
        if (this.tjlist.xb == '男') {
          this.tjlist.xb = 1
        } else if (this.tjlist.xb == '女') {
          this.tjlist.xb = 2
        }
        if (this.tjlist.smdj == '核心') {
          this.tjlist.ysmdj = 1
        } else if (this.tjlist.smdj == '重要') {
          this.tjlist.ysmdj = 2
        } else if (this.tjlist.smdj == '一般') {
          this.tjlist.ysmdj = 3
        }
        console.log(this.tjlist.lcslid, 'this.tjlist.lcslid');
        let params = this.tjlist
        let resDatas = await submitDjgwbg(params)
        if (resDatas.code == 10000) {
          this.$router.push('/smztwcxd')
          this.$message({
            message: '保存成功',
            type: 'success'
          })
        }
      }
    },
    //全部组织机构List
    async getOrganization() {
      let zzjgList = await getZzjgList()
      this.zzjgmc = zzjgList
      let shu = []
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            childrenRegionVo.push(item1)
            item.childrenRegionVo = childrenRegionVo
          }
        });
        shu.push(item)
      })
      let shuList = []
      let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    handleSelectionChange1(index, row) {
      this.radioIdSelect = row
    },
    handleCurrentChangeRy(val) {
      this.page = val
      this.chooseApproval()
    },
    //列表分页--更改每页显示个数
    handleSizeChangeRy(val) {
      this.page = 1
      this.pageSize = val
      this.chooseApproval()
    },
    // 人员搜索
    searchRy() {
      this.tableKey++
      this.chooseApproval()
    },
    // 发起申请选择人员 人员下拉
    bmSelectChange(item) {
      if (item != undefined) {
        this.ryChoose.bm = item.join('/')
      }
    },
    // 选择审批人
    async chooseApproval() {
      if (this.tjlist.bgsmgw.length != 0) {
        // this.getOrganization()
        this.approvalDialogVisible = true
        let param = {
          'page': this.page,
          'pageSize': this.pageSize,
          'fwdyid': this.fwdyid,
          'bmmc': this.ryChoose.bm,
          'xm': this.ryChoose.xm
        }
        let resData = await getSpUserList(param)
        if (resData.records) {
          // this.loading = false
          this.ryDatas = resData.records
          this.total = resData.total
        } else {
          this.$message.error('数据获取失败！')
        }
      } else {
        this.$message.warning('请选择变更后岗位名称！')
      }
    },
    // 保存并提交
    async saveAndSubmit() {
      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {
        let param = {
          'fwdyid': this.fwdyid
        }
        this.ryglRyscJtcyList.forEach((e) => {
          if (e.jwjlqk == '否') {
            e.jwjlqk = 0
          } else if (e.jwjlqk == '是') {
            e.jwjlqk = 1
          }
        })
        if (this.tjlist.xb == '男') {
          this.tjlist.xb = 1
        } else if (this.tjlist.xb == '女') {
          this.tjlist.xb = 2
        }
        if (this.tjlist.smdj == '核心') {
          this.tjlist.smdj = 1
        } else if (this.tjlist.smdj == '重要') {
          this.tjlist.smdj = 2
        } else if (this.tjlist.smdj == '一般') {
          this.tjlist.smdj = 3
        }
        // this.tjlist.dwid = this.ryInfo.dwid
        // this.tjlist.lcslid = this.ryInfo.lcslid
        if (this.routeType == 'update' && this.routezt == undefined) {
          param.lcslclzt = 2
          param.smryid = this.tjlist.smryid
          param.slid = this.tjlist.lcslid
          param.clrid = this.radioIdSelect.yhid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            let params = this.tjlist
            let resDatas = await updateDjgwbg(params)
            if (resDatas.code == 10000) {
              let paramStatus = {
                'fwdyid': this.fwdyid,
                'slid': this.tjlist.lcslid
              }
              let resStatus
              resStatus = await updateSlzt(paramStatus)
              if (resStatus.code == 10000) {
                this.$router.push('/smztwcxd')
                this.$message({
                  message: '保存并提交成功',
                  type: 'success'
                })
              }
            }
          }
        } else {
          param.lcslclzt = 0
          param.clrid = this.radioIdSelect.yhid
          param.smryid = this.yhDatas.smryid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.dwid = this.yhDatas.dwid
            this.tjlist.lcslid = res.data.slid


            let params = this.tjlist
            let resDatas = await submitDjgwbg(params)
            if (resDatas.code == 10000) {
              this.$router.push('/smztwcxd')
              this.$message({
                message: '保存并提交成功',
                type: 'success'
              })
            }
          }
        }
      } else {
        this.$message({
          message: '请选择审批人',
          type: 'warning'
        })
      }
    },
    // 返回
    returnIndex() {
      this.$router.push('/smztwcxd')
    }
  },
  watch: {

  }
}

</script>

<style scoped>
.sec-container {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: overlay;
}

.sec-title {
  border-left: 5px solid #1b72d8;
  color: #1b72d8;
  font-size: 20px;
  font-weight: 700;
  text-indent: 10px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.sec-form-container {
  width: 100%;
  height: 100%;
}

.sec-form-left {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
}

.sec-form-left:not(:first-child) {
  border-top: 0;
}

.sec-form-left .el-form-item {
  float: left;
  width: 100%;
}

.sec-header-section {
  width: 100%;
  position: relative;
}

.sec-header-pic {
  width: 258px;
  position: absolute;
  right: 0px;
  top: 0;
  height: 163px;
  border: 1px solid #CDD2D9;
  border-left: 0;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sec-form-second {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
  border-top: 0;
}

.sec-form-third {
  border: 1px solid #CDD2D9;
  /* height: 40px;  */
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

/deep/.el-checkbox-group {
  display: flex;
  justify-content: center;
  background-color: #F5F7FA;
  border-right: 1px solid #CDD2D9;
}

.checkbox {
  display: inline-block !important;
  background-color: rgba(255, 255, 255, 0) !important;
  border-right: none !important;
}

.checkbox1 {
  background-color: rgba(255, 255, 255, 1) !important;
}

.sec-form-four {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-five {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  overflow: hidden;
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.yulan {
  text-align: center;
  cursor: pointer;
  color: #3874D5;
  font-weight: 600;
  float: left;
  margin-left: 10px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 2px solid #EBEBEB;
}

.sec-form-six {
  border: 1px solid #CDD2D9;
  overflow: hidden;
  background: #ffffff;
  padding: 10px;
}

.ml10 {
  margin-left: 10px;
}

.sec-footer {
  margin-top: 10px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

.sec-form-left-textarea {
  height: 54px !important;
}

.sec-form-left-textarea>>>.el-form-item__label {
  line-height: 54px !important;
}

>>>.sec-form-four .el-textarea__inner {
  border: none;
}

.sec-left-text {
  float: left;
  margin-right: 130px;
}

.haveBorderTop {
  border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
  width: 500px !important;
}

>>>.longLabel .el-form-item__content {
  margin-left: 500px !important;
  padding-left: 20px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

/* .sec-form-second:not(:first-child){
  border-top: 0;
} */
.sec-form-second .el-form-item {
  float: left;
  width: 100%;
}

.sec-el-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
  padding-left: 15px;
  background-color: #F5F7FA;
  width: calc(100% - 16px);
  border-right: 1px solid #CDD2D9;
  color: #C0C4CC;
}

>>>.sec-el-table .el-input__inner {
  border: none !important;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
  width: 200px;
  text-align: center;
  font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
  border: none;
  border-right: 1px solid #CDD2D9;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item {
  margin-bottom: 0px;
}

/* >>>.el-form > div {
  border: 1px solid #CDD2D9;;
} */
>>>.el-form-item__label {
  border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
} */
.riq {
  width: 100% !important;
}

.widthw {
  width: 6vw;
}

.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}
</style>
