<template>
  <div class="bg_con" v-loading="loading">
    <div class="container">
      <BaseHeader :columns="columns" :params="params" @handleBtn="handleBtnAll"></BaseHeader>
      <el-form :inline="true" :model="formInline" size="medium" class="fr">
        <el-form-item class="fr">
          <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
            删除
          </el-button>
        </el-form-item>
        <el-form-item class="fr">
          <el-button type="success" size="medium" @click="sendApplay" icon="el-icon-plus">
            复制申请
          </el-button>
        </el-form-item>
      </el-form>
      <!-- 查询条件以及操作按钮end -->
      <!-- 涉密人员任用审查列表start -->
      <BaseTable :showSelection=true :selectionWidth="'55'" :showIndex=true :tableData="smryList" :columns="tableColumns"
        :handleColumn="handleColumn" :handleColumnProp="handleColumnProp" :showPagination=true :currentPage="page1"
        :pageSize="pageSize1" :totalCount="total1" @operateBtn="operateBtn" @selectBtn="selectBtn"
        @handleCurrentChange="handleCurrentChange" @handleSizeChange="handleSizeChange">
      </BaseTable>
      <!-- 涉密人员任用审查列表end -->
      <!-- 发起申请弹框start -->
      <!-- <el-dialog title="选择涉密人员" :close-on-click-modal="false" :visible.sync="dialogVisible" width="40%">
        <div class="dlFqsqContainer">
          <label for="">部门:</label>
          <el-cascader v-model="ryChoose.bm" :options="regionOption" :props="regionParams" filterable clearable
            ref="cascaderArr" @change="bmSelectChange"></el-cascader>
          <label for="">姓名:</label>
          <el-input class="input2" v-model="ryChoose.xm" clearable placeholder="姓名"></el-input>
          <el-button class="searchButton" type="primary" icon="el-icon-search" @click="searchRy">查询</el-button>
          <BaseTable class="baseTable" :tableHeight="'300'" :key="tableKey" :showIndex=true :tableData="ryDatas" :columns="applyColumns"
            :showSingleSelection="true" :handleColumn="handleColumnApply" :showPagination=true :currentPage="page"
            :pageSize="pageSize" :totalCount="total" @handleCurrentChange="handleCurrentChangeRy"
            @handleSizeChange="handleSizeChangeRy" @handleSelectionChange="handleSelectionChange">
          </BaseTable>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitRy()">保 存</el-button>
          <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
        </span>
      </el-dialog> -->
      <!-- 发起申请弹框end -->
    </div>
  </div>
</template>
<script>
import {
  selectRyscPage,
  removeRysc,
  getRyscInfo,
  getZzjgList,
  getSpYhxxPage,
  getLoginInfo,
  getFwdyidByFwlx,
  getZpBySmryid,
  getZtqdListByYjlid,
} from '../../../api/index'
import {
  selectZtfzPage,
  getZtfzInfo,
  removeZtfz
} from '../../../api/ztfzsc'
import {
  getUserInfo,
} from '../../../api/dwzc'
import BaseHeader from '../../components/common/baseHeader.vue'
import BaseTable from '../../components/common/baseTable.vue'
export default {
  components: {
    BaseHeader,
    BaseTable
  },
  props: {},
  data() {
    return {
      loading: false,
      // table 行样式
      headerCellStyle: {
        background: '#EEF7FF',
        color: '#4D91F8'
      },
      formInline: {}, // 搜索条件
      dialogVisible: false, // 发起申请弹框
      ryDatas: [], // 弹框人员选择
      page: 1, // 弹框人员当前页
      pageSize: 5, // 弹框人员每页条数
      page1: 1, // 弹框人员当前页
      pageSize1: 10, // 弹框人员每页条数
      // 弹框人员选择条件
      ryChoose: {
        'bm': '',
        'xm': ''
      },
      total: 0, // 弹框人员总数
      total1: 0, // 弹框人员总数
      radioIdSelect: '', // 弹框人员单选
      smryList: [], //页面数据
      scjtlist: [ //审查状态数据
        {
          mc: "审批中",
          id: 0
        },
        {
          mc: "通过",
          id: 1
        },
        {
          mc: "已驳回",
          id: 2
        },
        {
          mc: "草稿",
          id: 3
        }
      ],
      dqztlist: [ //当前状态数据
        {
          mc: "审批中",
          id: 0
        },
        {
          mc: "已结束",
          id: 1
        },
        {
          mc: "已驳回",
          id: 2
        },
        {
          mc: "草稿",
          id: 3
        }
      ],
      rowdata: [], //列表选中的值
      regionOption: [], // 部门下拉
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true
      }, //地域信息配置参数
      // 查询条件
      params: {
        name: '',
        tmjssj: ''
      },
      // 查询条件以及功能按钮
      columns: [{
        type: 'searchInput',
        name: '姓名',
        value: 'name',
        placeholder: '姓名',
      },
      {
        type: 'dataRange',
        name: '申请时间',
        value: 'tmjssj',
        startPlaceholder: '申请起始时间',
        rangeSeparator: '至',
        endPlaceholder: '申请结束时间',
        format: 'yyyy-MM-dd'
      },
      {
        type: 'button',
        name: '查询',
        disabled: false,
        icon: 'el-icon-search',
        mold: 'primary'
      },
      {
        type: 'button',
        name: '重置',
        disabled: false,
        icon: 'el-icon-circle-close',
        mold: 'warning'
      }
      ],
      // table项
      tableColumns: [
        {
          name: '申请人',
          prop: 'xqr',
          scopeType: 'text',
          formatter: false
        },
        {
          name: '申请人部门',
          prop: 'szbm',
          scopeType: 'text',
          formatter: false
        },
        {
          name: '载体名称',
          prop: 'ztmc',
          scopeType: 'text',
          formatter: false,
          showOverflowTooltip:true
        },
        {
          name: '载体编号',
          prop: 'ztbh',
          scopeType: 'text',
          formatter: false,
          showOverflowTooltip:true
        },
        {
          name: '审查时间',
          prop: 'cjsj',
          scopeType: 'text',
          formatter: false
        },
        {
          name: '审查结果',
          prop: 'Lcfwslzt',
          scopeType: 'text',
          formatter: (row, column, cellValue, index) => {
            const options = [
              {
                mc: "审批中",
                id: 0
              },
              {
                mc: "通过",
                id: 1
              },
              {
                mc: "已驳回",
                id: 2
              },
              {
                mc: "草稿",
                id: 3
              }
            ]
            const opt = options.find(d => d.id === cellValue)
            return opt ? opt.mc : ''
          }
        },
        {
          name: '当前状态',
          prop: 'Lcfwslzt',
          scopeType: 'text',
          formatter: (row, column, cellValue, index) => {
            const options = [
              {
                mc: "审批中",
                id: 0
              },
              {
                mc: "已结束",
                id: 1
              },
              {
                mc: "已驳回",
                id: 2
              },
              {
                mc: "草稿",
                id: 3
              }
            ]
            const opt = options.find(d => d.id === cellValue)
            return opt ? opt.mc : ''
          }
        }
      ],
      // table操作按钮
      handleColumn: [
        {
          name: '编辑',
          disabled: false,
          show: true,
          formatter: (row, column) => {
            if (row.Lcfwslzt == 3) {
              return '编辑'
            } else if (row.Lcfwslzt == 0 || row.Lcfwslzt == 1 || row.Lcfwslzt == 2) {
              return '查看'
            }
          }
        }
      ],
      // 表格的操作
      handleColumnProp: {
        label: '操作',
        width: '230',
        align: 'left'
      },
      // 发起申请table
      applyColumns: [
        {
          name: '姓名',
          prop: 'xm',
          scopeType: 'text',
          formatter: false
        },
        {
          name: '部门',
          prop: 'bmmc',
          scopeType: 'text',
          formatter: false
        },
        {
          name: '岗位',
          prop: 'gwmc',
          scopeType: 'text',
          formatter: (row, column, cellValue, index) => {
            return cellValue.join('/')
          }
        }
      ],
      handleColumnApply: [],
      // 查询条件以及功能按钮
      smryColumns: [{
        type: 'cascader',
        name: '部门',
        value: 'bmmc',
        placeholder: '请选择部门',
      }, {
        type: 'searchInput',
        name: '姓名',
        value: 'name',
        placeholder: '姓名',
      },
      {
        type: 'button',
        name: '查询',
        disabled: false,
        icon: 'el-icon-search',
        mold: 'primary'
      },
      {
        type: 'button',
        name: '重置',
        disabled: false,
        icon: 'el-icon-circle-close',
        mold: 'warning'
      }
      ],
      // 当前登录人的用户名
      loginName: ''
    }
  },
  computed: {},
  mounted() {
    this.onfwid()
    this.getLoginYhm() // 获取当前登录人姓名
    this.rysclist() // 任用审查数据获取
    this.zzjg() // 获取组织机构所有部门下拉
  },
  methods: {
    // 获取当前登录人姓名
    async getLoginYhm() {
      let userInfo = await getUserInfo()
      this.loginName = userInfo.yhm
    },
    //分页
    handleSizeChange(val) {
      this.page1 = 1
      this.pageSize1 = val
      this.rysclist()
    },
    handleCurrentChange(val) {
      this.page1 = val
      this.rysclist()
    },
    // table复选集合
    selectBtn(row) {
      this.rowdata = row
      console.log(row);
    },
    //删除
    shanchu() {
      if (this.rowdata.length == 0) {
        this.$message({
          message: '未选择想要删除的数据',
          type: 'warning'
        });
      } else {
        this.$confirm('此操作将永久删除该申请, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.rowdata.forEach(async (item) => {
            let params = {
              jlid: item.jlid
            }
            let res = await removeZtfz(params)
            if (res.code == 10000) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.rysclist()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      }
    },
    // 点击公共头部按钮事件
    handleBtnAll(parameter, item) {
      if (item.name == '查询') {
        this.params = JSON.parse(JSON.stringify(parameter))
        this.page1 = 1
        this.rysclist()
      } else if (item.name == '重置') {
        this.params = {
          name: '',
          tmjssj: ''
        }
      }
    },
    //任用审查数据获取
    async rysclist(parameter) {
      let params = {
        xm: this.params.name,
        page: this.page1,
        pageSize: this.pageSize1
      }
      if (this.params.tmjssj != null) {
        params.kssj = this.params.tmjssj[0]
        params.jssj = this.params.tmjssj[1]
      }
      let data = await selectZtfzPage(params)
      if (data.records) {
        this.smryList = data.records
        this.total1 = data.total
      } else {
        this.$message.error('数据获取失败！')
      }
    },
    // 人员选择弹框保存按钮
    submit() {
      this.$router.push('/ryscTable')
    },
    // 人员搜索
    searchRy() {
      this.tableKey++
      this.page = 1
      this.sendApplay()
    },
    // 发起申请
    async sendApplay() {
      this.$router.push({
        path: '/ztfzscTable',
        query: {
          type: 'add',
        }
      })
    },
    handleCurrentChangeRy(val) {
      this.page = val
      this.sendApplay()
    },
    //列表分页--更改每页显示个数
    handleSizeChangeRy(val) {
      this.page = 1
      this.pageSize = val
      this.sendApplay()
    },
    handleSelectionChange(index, row) {
      this.radioIdSelect = row
    },
    // 选择人员提交
    async submitRy() {
      this.loading = true
      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {
        this.loading = false
        let zp = await getZpBySmryid({ smryid: this.radioIdSelect.smryid })
        this.radioIdSelect.zp = zp
        this.$router.push({
          path: '/ryscTable',
          query: {
            type: 'add',
            datas: this.radioIdSelect
          }
        })
      } else {
        this.$message.error('请选择涉密人员')
        this.loading = false
      }
    },
    //审查状态数据回想
    scjgsj(row) {
      let data;
      this.scjtlist.forEach(item => {
        if (item.id == row.Lcfwslzt) {
          data = item.mc
        }
      })
      return data
    },
    //当前状态数据回想
    dqztsj(row) {
      let data;
      this.dqztlist.forEach(item => {
        if (item.id == row.Lcfwslzt) {
          data = item.mc
        }
      })
      return data
    },
    async onfwid() {
      let params = {
        fwlx: 19
      }
      let data = await getFwdyidByFwlx(params)
      console.log(data);
      this.fwdyid = data.data.fwdyid
    },
    // 功能操作按钮
    async operateBtn(row, item) {
      // 编辑方法
      if (item == '编辑') {
        this.loading = true
        let res = await getZtfzInfo({
          'jlid': row.jlid
        })
        let zt = await getZtqdListByYjlid({
          'yjlid': row.jlid
        })
        if (res.slid) {
          this.loading = false
          this.$router.push({
            path: '/ztfzscTable',
            query: {
              type: 'update',
              datas: res,
              ztzz: zt,
              // cjrid: 
            }
          })
        } else {
          this.$message.error('任务不匹配！')
        }
      } else if (item == '查看') {  // 查看方法
        let fwdyid = this.fwdyid
        if (this.fwdyid == '' || this.fwdyid == undefined) {
          this.$message.error('请到流程管理进行配置');
        } else {
          this.$router.push({
            path: '/ztfzscblxxscb',
            query: {
              lx: '涉密载体复制审查',
              fwdyid: fwdyid,
              slid: row.slid
            }
          })
        }

      }
    },
    //全部组织机构List
    async zzjg() {
      let zzjgList = await getZzjgList()
      this.zzjgmc = zzjgList
      let shu = []
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            childrenRegionVo.push(item1)
            item.childrenRegionVo = childrenRegionVo
          }
        });
        shu.push(item)
      })
      let shuList = []
      let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    // 发起申请选择人员 人员下拉
    bmSelectChange(item) {
      console.log(item)
      if (item != undefined) {
        this.ryChoose.bm = item.join('/')
      }
    }
  },
  watch: {

  }
}

</script>

<style scoped>
.fl {
  float: left;
}

.fr {
  float: right;
}

.container {
  width: 100%;
  position: relative;
  overflow: hidden;
  height: 100%;
  /* box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13); */
  border-radius: 8px;
}

.bg_con {
  width: 100%;
  height: calc(100% - 38px);
}

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.widthw {
  width: 6vw;
}

/* 发起申请弹框 */
.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}

.spImg {
  width: 15px;
}

.baseTable {
  margin-top: 20px;
  /* height: 400px!important; */
}
</style>
