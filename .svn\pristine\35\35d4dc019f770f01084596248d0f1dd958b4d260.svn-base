webpackJsonp([118],{JCeZ:function(t,e){},"JV9+":function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=s("mvHQ"),n=s.n(i),r=s("woOf"),c=s.n(r),o=s("Xxa5"),d=s.n(o),a=s("exGp"),l=s.n(a),f=(s("1clA"),s("ye4p"),s("9WY5")),u={data:function(){return{dialogObj:{},dxdfArr:[],syxmDfArr:[],showDxList:[],spanArr:[],reverse:!0,activities:[],showMdmenu:!1,dwmc:"",nsjg:""}},computed:{syxmzf:function(){var t=0;return this.showDxList.forEach(function(e){e.sfsynr&&(t+=1*e.fz)}),t},syxmdf:function(){var t=0;return this.syxmDfArr.forEach(function(e){t+=1*e}),t},syxmbfb:function(){if(0==this.syxmzf)return 0;var t=this.syxmdf/this.syxmzf;return Math.round(1e3*t)/10}},components:{},methods:{pfss:function(){var t=this;return l()(d.a.mark(function e(){var s,i,n;return d.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s={dwid:t.dwid,rwid:t.rwid,jgid:t.jgid},i=void 0,e.next=4,Object(f.x)();case 4:if(n=e.sent,"0"!=t.djzt){e.next=11;break}return e.next=8,Object(f.w)();case 8:i=e.sent,e.next=14;break;case 11:return e.next=13,Object(f.A)(s);case 13:i=e.sent;case 14:i.data.forEach(function(t){n.data.forEach(function(e){e.zcxid==t.zcxid&&(t.dxmc=e.zcxmc)})}),t.showDxList=i.data,console.log(t.showDxList,"dasdadasdsadasd");case 17:case"end":return e.stop()}},e,t)}))()},save:function(){var t=this;return l()(d.a.mark(function e(){return d.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.log("this.showDxList",t.showDxList),t.ccbmRK(t.showDxList,1);case 2:case"end":return e.stop()}},e,t)}))()},submit:function(){this.ccbmRK(this.showDxList,2)},ccbmRK:function(t,e){var s=this;return l()(d.a.mark(function i(){var n,r,c,o;return d.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return console.log(t),n=[],r={rwid:s.rwid,jgid:s.jgid},i.next=5,Object(f.h)(r);case 5:if(1e4!=i.sent.code){i.next=12;break}return t.forEach(function(t){t.dwid=s.dwid,t.rwid=s.rwid,t.jgid=s.jgid,t.jgmc=s.nsjg,n.push(t)}),i.next=10,Object(f.c)(n);case 10:i.sent,1==e?(c={dwid:s.dwid,jgid:s.jgid,rwid:s.rwid,djzt:e},Object(f.H)(c),0==s.zt?s.$router.push({path:"/ccdnsjg",query:{rwid:s.rwid,rwmc:s.rwmc,jcjd:s.jcjd}}):s.$router.push({path:"/jczj",query:{rwid:s.rwid,rwmc:s.rwmc,jcjd:s.jcjd}}),s.$message({message:"临时保存成功",type:"success"})):2==e&&(o={dwid:s.dwid,jgid:s.jgid,rwid:s.rwid,djzt:e},Object(f.H)(o),0==s.zt?s.$router.push({path:"/ccdnsjg",query:{rwid:s.rwid,rwmc:s.rwmc,jcjd:s.jcjd}}):s.$router.push({path:"/jczj",query:{rwid:s.rwid,rwmc:s.rwmc,jcjd:s.jcjd}}),s.$message({message:"提交成功",type:"success"}));case 12:case"end":return i.stop()}},i,s)}))()},updateTable:function(){var t=this;return l()(d.a.mark(function e(){var s,i,n;return d.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(s=void 0,i={dwid:t.dwid,rwid:t.rwid,jgid:t.jgid},"0"!=t.djzt){e.next=8;break}return e.next=5,Object(f.w)();case 5:s=e.sent,e.next=11;break;case 8:return e.next=10,Object(f.A)(i);case 10:s=e.sent;case 11:n=[],console.log("bmxxzcjlList",s),s.data.forEach(function(t){t.gdkffz=t.fz,t.jsqkf=t.ykf,t.gdkffz=t.fz}),n=s.data,console.log(n),t.spanArr=t.getSpanArr(n),t.showDxList=n;case 18:case"end":return e.stop()}},e,t)}))()},returnSy:function(){this.$router.go(-1)},getRwxxDwxxCcnsjgxxByRwidCcdnsjgid:function(){var t=selectRwxxDwxxCcnsjgxxByRwidCcdnsjgid(this.dialogObj);console.log("rwxxDwxxCcnsjgxx",t),c()(this.dialogObj,t),this.dialogObj=JSON.parse(n()(this.dialogObj))},getSpanArr:function(t){console.log(t);for(var e=[],s=0;s<t.length;s++)0===s?(e.push(1),this.pos=0):t[s].zcxid==t[s-1].zcxid?(e[this.pos]+=1,e.push(0)):(e.push(1),this.pos=s);return e},objectSpanMethod:function(t){var e=t.row;t.column,t.rowIndex;if(0===t.columnIndex)return{rowspan:this.spanArr[e.scid-1],colspan:1}},handleKfjsq:function(t){t.checked=!0},handleSynrTextarea:function(t){void 0!==t.synr&&""!=t.synr?t.sfsynr=!0:t.sfsynr=!1},handleDfsmTextarea:function(t){void 0!==t.dfsm&&""!=t.dfsm?t.checked=!0:t.checked=!1},getZD:function(){var t=getBmxxzcjlZD();return console.log(t),t.forEach(function(t){t.ykf=0,t.checked=!1,t.jsqkf=0,t.gdkffz=t.fz,t.sfsynr=!0}),this.spanArr=this.getSpanArr(t),t},mouseoverMdMenu:function(){this.showMdmenu=!0},mouseoutMenu:function(){this.showMdmenu=!1}},watch:{showDxList:{handler:function(t,e){var s=this;console.log("bmcc showDxList changed...",t);var i=this;this.activities=[];var n=[];i.syxmDfArr=[];var r=[];t.forEach(function(t,e){t.mdIndex="md-"+e,-1==r.indexOf(t.zcxid)&&r.push(t.zcxid),t.checked?(3==t.kffs&&(t.ykf=t.jsqkf),2==t.kffs&&(t.ykf=t.gdkffz),console.log(t.nrid,t.fz,t.ykf,t.fz-t.ykf),t.df=t.fz-t.ykf,-1==n.indexOf(t.zcxid)&&n.push(t.zcxid),-1!=n.indexOf(t.zcxid)&&(s.activities[n.indexOf(t.zcxid)]||s.activities.push({dxmc:t.dxmc,children:[]}),s.activities[n.indexOf(t.zcxid)].children.push({href:"#"+t.mdIndex,nr:t.nr,ykf:t.ykf}))):(t.ykf=0,t.jsqkf=0,t.df=t.fz),t.sfsynr&&i.syxmDfArr.push(t.df)})},deep:!0}},mounted:function(){console.log(this.$route.query),this.dwmc=this.$route.query.dwmc,this.nsjg=this.$route.query.bmmc,this.dwid=this.$route.query.dwid,this.rwid=this.$route.query.rwid,this.jgid=this.$route.query.jgid,this.rwmc=this.$route.query.rwmc,this.djzt=this.$route.query.djzt,this.zt=this.$route.query.zt,this.jcjd=this.$route.query.jcjd,this.pfss(),this.updateTable()}},x={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticStyle:{width:"100%",height:"100%"}},[s("div",{staticClass:"mhcx"},[s("el-form",{staticClass:"demo-form-inline",staticStyle:{float:"left"},attrs:{inline:!0,size:"medium"}},[s("el-form-item",{staticStyle:{float:"left"}},[s("div",[t._v("单位："+t._s(t.dwmc))])]),t._v(" "),s("el-form-item",{staticStyle:{float:"left"}},[s("div",[t._v("抽查内设机构："+t._s(t.nsjg))])])],1),t._v(" "),s("el-form",{staticClass:"demo-form-inline",staticStyle:{float:"right"},attrs:{inline:!0,size:"medium"}},[s("el-form-item",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"primary",size:"medium",icon:"el-icon-document-add"},on:{click:t.submit}},[t._v("\n          提交\n        ")])],1),t._v(" "),s("el-form-item",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"warning",size:"medium",icon:"el-icon-document"},on:{click:t.save}},[t._v("\n          临时保存\n        ")])],1)],1),t._v(" "),s("div",{staticStyle:{clear:"both"}})],1),t._v(" "),s("div",{staticStyle:{"overflow-y":"scroll",height:"calc(100% - 58px - 64px - 108px - 16px - 20px)","margin-bottom":"5px"}},[s("el-card",{staticStyle:{"margin-bottom":"1em"}},[s("el-table",{attrs:{data:t.showDxList,"span-method":t.objectSpanMethod,"header-cell-style":{"text-align":"center"},border:""}},[s("el-table-column",{attrs:{label:"自查类",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",[s("span",{attrs:{id:t.showDxList[e.$index].mdIndex}}),t._v(t._s(t.showDxList[e.$index].dxmc)+"\n            ")])]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"检查内容"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",[s("span",{attrs:{id:t.showDxList[e.$index].mdIndex}}),t._v(t._s(t.showDxList[e.$index].scnr)+"\n            ")])]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"扣分",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",[3==t.showDxList[e.$index].kffs?s("div",[s("el-checkbox-group",{model:{value:t.showDxList[e.$index].checked,callback:function(s){t.$set(t.showDxList[e.$index],"checked",s)},expression:"showDxList[scope.$index].checked"}},[s("el-checkbox",{staticStyle:{"margin-right":"0.5em"},attrs:{label:"",name:"checkbox"}}),t._v(" "),s("el-input-number",{staticStyle:{width:"100px"},attrs:{min:t.showDxList[e.$index].zdkffz,max:t.showDxList[e.$index].zgkffz,step:t.showDxList[e.$index].kfzf,size:"mini"},on:{change:function(s){return t.handleKfjsq(t.showDxList[e.$index])}},model:{value:t.showDxList[e.$index].jsqkf,callback:function(s){t.$set(t.showDxList[e.$index],"jsqkf",s)},expression:"showDxList[scope.$index].jsqkf"}})],1)],1):t._e(),t._v(" "),2==t.showDxList[e.$index].kffs?s("div",[s("el-checkbox-group",{model:{value:t.showDxList[e.$index].checked,callback:function(s){t.$set(t.showDxList[e.$index],"checked",s)},expression:"showDxList[scope.$index].checked"}},[s("el-checkbox",{attrs:{label:"",name:"checkbox"}},[t._v(t._s(t.showDxList[e.$index].gdkffz))])],1)],1):t._e()])]}}])}),t._v(" "),s("el-table-column",{attrs:{prop:"fz",label:"分值",width:"50",align:"center"}}),t._v(" "),s("el-table-column",{attrs:{label:"实有项目",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-checkbox-group",{model:{value:t.showDxList[e.$index].sfsynr,callback:function(s){t.$set(t.showDxList[e.$index],"sfsynr",s)},expression:"showDxList[scope.$index].sfsynr"}},[s("el-checkbox",{attrs:{label:"是",name:"checkboxSynr"}})],1)]}}])}),t._v(" "),s("el-table-column",{attrs:{prop:"",label:"实有内容"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-input",{attrs:{type:"textarea",rows:3},on:{input:function(s){return t.handleSynrTextarea(t.showDxList[e.$index])}},model:{value:t.showDxList[e.$index].synr,callback:function(s){t.$set(t.showDxList[e.$index],"synr","string"==typeof s?s.trim():s)},expression:"showDxList[scope.$index].synr"}})]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"得分",width:"50",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",[t._v(t._s(t.showDxList[e.$index].df))])]}}])}),t._v(" "),s("el-table-column",{attrs:{prop:"kfbz",label:"扣分标准"}}),t._v(" "),s("el-table-column",{attrs:{label:"评分说明"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-input",{attrs:{type:"textarea",rows:3},on:{input:function(s){return t.handleDfsmTextarea(t.showDxList[e.$index])}},model:{value:t.showDxList[e.$index].dfsm,callback:function(s){t.$set(t.showDxList[e.$index],"dfsm","string"==typeof s?s.trim():s)},expression:"showDxList[scope.$index].dfsm"}})]}}])})],1)],1)],1),t._v(" "),s("el-card",{staticStyle:{"margin-bottom":"5px"},attrs:{id:"sslist"}},[s("span",{staticClass:"sslist_class"},[t._v("实有项目总分："),s("span",{staticStyle:{color:"red"}},[t._v(t._s(t.syxmzf))])]),t._v(" "),s("span",{staticClass:"sslist_class"},[t._v("实有项目得分："),s("span",{staticStyle:{color:"red"}},[t._v(t._s(t.syxmdf))])]),t._v(" "),s("span",{staticClass:"sslist_class"},[t._v("实有项目得分占实有项目总分百分比："),s("span",{staticStyle:{color:"red"}},[t._v(t._s(t.syxmbfb)+"%")])])]),t._v(" "),s("el-card",{attrs:{id:"sslist"}},[s("span",{staticStyle:{color:"red",display:"block",width:"100%","font-weight":"bold"}},[t._v("备注：")]),t._v(" "),s("ol",{staticStyle:{"margin-left":"2em","list-style":"disc"}},[s("li",{staticStyle:{"list-style":"disc"}},[t._v("\n        1.单项检查内容存在多起不符合要求行为的，每个单项的总扣分最高不超过该项的总分值。\n      ")]),t._v(" "),s("li",{staticStyle:{"list-style":"disc"}},[t._v("\n        2.实行100分评分制,得分为实有项目得分与实有项目总分比值的百分制得分。实有项目总分为实有检查内容各项分值之和，实有项目得分为实有项目总分扣除自查发现问题分值之后的得分。\n      ")])])])],1)},staticRenderFns:[]};var h=s("VU/8")(u,x,!1,function(t){s("JCeZ")},"data-v-7198e963",null);e.default=h.exports}});
//# sourceMappingURL=118.943a3ac65e09b7b7ea09.js.map