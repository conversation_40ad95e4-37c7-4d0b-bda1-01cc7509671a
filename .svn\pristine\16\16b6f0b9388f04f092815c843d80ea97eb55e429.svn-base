<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden;height: 100%;">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="培训形式" style="font-weight: 700;">
                  <el-select v-model="formInline.pxxs" clearable placeholder="请选择培训形式" class="widthw">
                    <el-option v-for="item in pxxsxz" :label="item.mc" :value="item.id" :key="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="培训日期" style="font-weight: 700;">
                  <el-date-picker v-model="formInline.pxrq" type="daterange" range-separator="至" style="width:293px;"
                    start-placeholder="培训起始时间" end-placeholder="培训结束日期" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>

                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="xz" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="pxqdList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 44px - 10px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <el-table-column prop="pxrq" label="培训日期">
                    <!-- <template slot-scope="scoped">
                      <div>
                        {{ sjgsh(scoped.row.pxrq) }}
                      </div>
                    </template> -->
                  </el-table-column>
                  <el-table-column prop="pxzt" label="培训主题"></el-table-column>
                  <el-table-column prop="pxdd" label="培训地点"></el-table-column>
                  <el-table-column prop="pxdw" label="培训单位"></el-table-column>
                  <el-table-column prop="pxnr" label="培训内容"></el-table-column>
                  <el-table-column prop="pxks" label="课时"></el-table-column>
                  <el-table-column prop="pxxs" label="培训形式" :formatter="forpxxs"></el-table-column>
                  <el-table-column prop="pxlx" label="培训类型" :formatter="forpxlx"></el-table-column>
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入培训清单汇总情况" class="scbg-dialog" :visible.sync="dialogVisible_dr"
          show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="培训日期" label="培训日期"></el-table-column>
              <el-table-column prop="部门" label="部门"></el-table-column>
              <el-table-column prop="培训单位" label="培训单位"></el-table-column>
              <el-table-column prop="培训内容" label="培训内容"></el-table-column>
              <el-table-column prop="课时" label="课时"></el-table-column>
              <el-table-column prop="培训形式" label="培训形式"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button @click="dialogVisible_dr = false" size="mini">返 回</el-button>
          </div>
        </el-dialog>

        <!-- -----------------培训清单信息-弹窗--------------------------- -->
        <el-dialog title="新增培训清单信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="47.5%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
            <div style="display:flex">
              <el-form-item label="培训主题" prop="pxzt">
                <el-autocomplete class="inline-input" value-key="pxzt" v-model.trim="tjlist.pxzt"
                  :fetch-suggestions="querySearchpxzt" placeholder="培训主题" @blur="onInputBlur(1)" style="width: 100%;">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训日期" prop="pxrq">
                <el-date-picker v-model="tjlist.pxrq" style="width:100%;" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd" @blur="onInputBlur(1)">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训地点" prop="pxdd">
                <el-autocomplete class="inline-input" value-key="pxdd" v-model.trim="tjlist.pxdd"
                  :fetch-suggestions="querySearchpxdd" placeholder="培训地点" style="width: 100%;">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训单位" prop="pxdw">
                <el-autocomplete class="inline-input" value-key="pxdw" v-model.trim="tjlist.pxdw"
                  :fetch-suggestions="querySearchpxdw" placeholder="培训单位" style="width: 100%;">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训讲师" prop="pxjs">
                <el-autocomplete class="inline-input" value-key="pxjs" v-model.trim="tjlist.pxjs"
                  :fetch-suggestions="querySearchpxjs" placeholder="培训讲师" style="width: 100%;">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训形式" prop="pxxs">
                <el-select v-model="tjlist.pxxs" placeholder="请选择培训形式" style="width: 100%;">
                  <el-option v-for="item in pxxsxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训类型" prop="pxlx" class="one-line">
                <el-select v-model="tjlist.pxlx" placeholder="请选择培训类型" style="width: 100%;">
                  <el-option v-for="item in pxlxxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="课时" prop="pxks" class="one-line">
                <el-input placeholder="课时" v-model="tjlist.pxks" clearable>
                </el-input>
              </el-form-item>
            </div>

            <el-form-item label="培训主要内容" prop="pxnr" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.pxnr" style="width: 100%;"></el-input>
            </el-form-item>
            <div class="online-titlebtn">
              <div class="title">
                <span>培训人员清单</span>
              </div>
              <div style="text-align: right;">
                <!-- <el-button type="primary" size="small" @click='rydialogVisible = true'>新 增</el-button>
								<el-button size="small" type="danger" @click="deletery1">删 除</el-button> -->
                <el-button type="primary" size="small" @click='rydialogVisible = true'>修改人员清单</el-button>
              </div>
            </div>
          </el-form>
          <div style="margin-top:10px">
            <el-table :data="tianjiaryList" border @selection-change="selectRow1"
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%;border:1px solid #EBEEF5;" stripe height="250">
              <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
              <el-table-column type="index" width="60" label="序号"></el-table-column>
              <el-table-column prop="bmmc" label="部门" width="180">
              </el-table-column>
              <el-table-column prop="xm" label="姓名" width="180">
              </el-table-column>
              <el-table-column prop="smdj" label="涉密等级" :formatter="forsmdj">
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer" style="margin-top:10px">
            <el-button type="primary" @click="submitTj('formName')" style="margin-top:10px">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false" style="margin-top:10px;">关 闭
            </el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改培训清单信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="47.5%"
          class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
            <div style="display:flex">
              <el-form-item label="培训主题" prop="pxzt">
                <el-autocomplete class="inline-input" value-key="pxzt" v-model.trim="xglist.pxzt"
                  :fetch-suggestions="querySearchpxzt" placeholder="培训主题" @blur="onInputBlur(2)">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训日期" prop="pxrq">
                <el-date-picker v-model="xglist.pxrq" style="width:100%;" clearable type="date" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd" @blur="onInputBlur(2)">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训地点" prop="pxdd">
                <el-autocomplete class="inline-input" value-key="pxdd" v-model.trim="xglist.pxdd"
                  :fetch-suggestions="querySearchpxdd" placeholder="培训地点">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训单位" prop="pxdw">
                <el-autocomplete class="inline-input" value-key="pxdw" v-model.trim="xglist.pxdw"
                  :fetch-suggestions="querySearchpxdw" placeholder="培训单位">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训讲师" prop="pxjs">
                <el-autocomplete class="inline-input" value-key="pxjs" v-model.trim="xglist.pxjs"
                  :fetch-suggestions="querySearchpxjs" placeholder="培训讲师">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="培训形式" prop="pxxs">
                <el-select v-model="xglist.pxxs" placeholder="请选择培训形式">
                  <el-option v-for="item in pxxsxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训类型" prop="pxlx" class="one-line">
                <el-select v-model="xglist.pxlx" placeholder="请选择培训类型" style="width: 100%;">
                  <el-option v-for="item in pxlxxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="课时" prop="pxks" class="one-line">
                <el-input placeholder="课时" v-model="xglist.pxks" clearable oninput="value=value.replace(/[^\d.]/g,'')"
                  @blur="pxks = $event.target.value">
                </el-input>
              </el-form-item>
            </div>

            <el-form-item label="培训主要内容" prop="pxnr" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.pxnr"></el-input>
            </el-form-item>
            <div class="online-titlebtn">
              <div class="title">
                <span>培训人员清单</span>
              </div>
              <div style="text-align: right;">
                <!-- <el-button type="primary" size="small" @click='ryxg'>保 存</el-button>
								<el-button size="small" type="danger" @click="deletery2">删 除</el-button> -->
                <el-button type="primary" size="small" @click='ryxg'>修改人员清单</el-button>
              </div>
            </div>
          </el-form>
          <div style="margin-top:10px">
            <el-table :data="xglist.rypxdjList" border @selection-change="selectRow2"
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" height="250"
              style="width: 100%;border:1px solid #EBEEF5;" stripe>
              <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
              <el-table-column type="index" width="60" label="序号"></el-table-column>
              <el-table-column prop="bmmc" label="部门" width="180">
              </el-table-column>
              <el-table-column prop="xm" label="姓名" width="180">
              </el-table-column>
              <el-table-column prop="smdj" label="涉密等级" :formatter="forsmdj">
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')" style="margin-top:10px">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false" style="margin-top:10px">关 闭
            </el-button>
          </span>
        </el-dialog>

        <el-dialog title="培训清单信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="47.5%"
          class="xg">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini" disabled>
            <div style="display:flex">
              <el-form-item label="培训主题">
                <el-input placeholder="培训主题" v-model="xglist.pxzt" clearable style="width:100%;"></el-input>
              </el-form-item>
              <el-form-item label="培训日期">
                <el-date-picker v-model="xglist.pxrq" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" style="width:100%;">
                </el-date-picker>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训地点">
                <el-input placeholder="培训地点" v-model="xglist.pxdd" clearable style="width:100%;"></el-input>
              </el-form-item>
              <el-form-item label="培训单位">
                <el-input placeholder="培训单位" v-model="xglist.pxdw" clearable style="width:100%;"></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训讲师">
                <el-input placeholder="培训讲师" v-model="xglist.pxjs" clearable style="width:100%;"></el-input>
              </el-form-item>
              <el-form-item label="培训形式">
                <el-select v-model="xglist.pxxs" placeholder="请选择培训形式" style="width:100%;">
                  <el-option v-for="item in pxxsxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="培训类型" class="one-line">
                <el-select v-model="xglist.pxlx" placeholder="请选择培训类型" style="width: 100%;">
                  <el-option v-for="item in pxlxxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="课时" prop="pxks" class="one-line">
                <el-input placeholder="课时" v-model="xglist.pxks" clearable></el-input>
              </el-form-item>
            </div>
            <!-- <el-form-item label="培训主要内容">
							<el-input type="textarea" v-model="xglist.pxnr"></el-input>
						</el-form-item> -->
            <div class="online-titlebtn">
              <div class="title">
                <span>培训人员清单</span>
              </div>
            </div>
          </el-form>
          <div style="margin-top:10px">
            <el-table :data="xglist.rypxdjList" border @selection-change="selectRow"
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" height="250"
              style="width: 100%;border:1px solid #EBEEF5;" stripe>
              <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
              <el-table-column type="index" width="60" label="序号"></el-table-column>
              <el-table-column prop="bmmc" label="部门" width="180">
              </el-table-column>
              <el-table-column prop="xm" label="姓名" width="180">
              </el-table-column>
              <el-table-column prop="smdj" label="涉密等级" :formatter='forsmdj'>
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <el-dialog title="培训人员清单" :close-on-click-modal="false" :visible.sync="rydialogVisible" width="54%" class="xg"
          style="margin-top:4vh">
          <el-row type="flex">
            <el-col :span="12" style="height:500px">
              <div style="height:96%;border: 1px solid #dee5e7;">
                <div style="padding-top: 10px;padding-left: 10px;width: 97%;height: 68px;background: #fafafa;">
                  <el-row>待选人员列表</el-row>
                  <el-form :inline="true" :model="formInlinery" size="medium" class="demo-form-inline"
                    style="display:flex;margin-bottom: -3%;">
                    <div class="dialog-select-div">
                      <span class="title">部门</span>
                      <el-cascader v-model="formInlinery.bm" :options="regionOption" clearable class="widths"
                        style="width:14vw" :props="regionParams" filterable ref="cascaderArr" @change="handleChange">
                      </el-cascader>
                      <el-button type="primary" icon="el-icon-search" @click="onSubmitry">查询
                      </el-button>
                    </div>
                    <!-- <el-form-item label="部门" class="dialog-select-div">
											<el-cascader v-model="formInlinery.bm" :options="regionOption" clearable class="widths"
												style="width:14vw" :props="regionParams" filterable ref="cascaderArr">
											</el-cascader>
										</el-form-item>
										<el-form-item>
											<el-button type="primary" icon="el-icon-search" @click="onSubmitry">查询
											</el-button>
										</el-form-item> -->
                  </el-form>
                </div>
                <el-table :data="table1Data" style="width: 100%;margin-top:1%;" height="400" ref="table1"
                  @selection-change="onTable1Select" @row-click="handleRowClick">
                  <el-table-column type="selection" width="55">
                  </el-table-column>
                  <el-table-column prop="xm" label="姓名">
                  </el-table-column>
                </el-table>
              </div>
              <!-- 分页区 -->
              <!-- <div style="border: 1px solid #ebeef5;">
								<el-pagination background @current-change="handleCurrentChange1" @size-change="handleSizeChange1"
									:pager-count="5" :current-page="page1" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize1"
									layout="total, prev, pager, sizes,next, jumper" :total="total1">
								</el-pagination>
							</div> -->
            </el-col>
            <!-- <el-col :span="4" style="margin-left: 18px;
    margin-top: 70px;display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;">
							<el-button type="primary" @click="onAdd">添 加</el-button>
							<el-button type="danger" @click="onDelete" style="margin-top: 50px;
    margin-left: 0;">删 除</el-button>
						</el-col> -->
            <el-col :span="12" style="margin-left:10px;height:500px">
              <div style="height:96%;
    										border: 1px solid #dee5e7;
    										">
                <div style="padding-top: 10px;
    										padding-left: 10px;
    										width: 97%;
    										height: 68px;
    										background: #fafafa;">
                  <el-row>已选人员列表</el-row>
                  <div style="float:right;">
                    <el-button type="primary" @click="addpxry">保 存</el-button>
                    <el-button type="warning" @click="rydialogVisible = false">关 闭</el-button>
                  </div>

                </div>
                <el-table :data="table2Data" style="width: 100%;" height="404" ref="table2">
                  <el-table-column prop="xm" label="姓名">
                    <template slot-scope="scope">
                      <div style="display:flex;justify-content: space-between;
    														align-items: center;">
                        <div>
                          {{ scope.row.xm }}
                        </div>
                        <i class="el-icon-circle-close btn" @click="onTable2Select(scope.row)"></i>
                      </div>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column type="selection" width="55">
										</el-table-column> -->

                </el-table>
              </div>

            </el-col>
          </el-row>
        </el-dialog>
      </div>
    </div>
  </div>

</template>
<script>
  import {
    getAllYhxx,
    savePxdj,
    removePxdj,
    updatePxdj,
    getPxdjxxList,
    saveRypxdj,
    getPxdjById,
    getAllPxdj,
    getZzjgList
  } from '../../../api/index'
  import {
    getAllSmdj,
    getJypxlx,
    getJypxxs,
    // getAllGwqdyj,
  } from '../../../api/xlxz'
  export default {
    components: {},
    props: {},
    data() {

      var checkKsValidator = (rule, value, callback, form) => {
        // console.log('pxks value', value, value.length, form)
        // 校验是否存在非数字字符串
        let notNum = value.match(/[^\d]/)
        // console.log('notNum', notNum)
        if (notNum) {
          form.pxks = value.replace(/[^\d.]/g, '')
          // callback(new Error('课时只能输入数字'))
          return
        }
        if (value.length <= 0) {
          callback(new Error('请输入课时，课时只能为数字'))
        }
        callback()
      }

      return {
        pxrq: '',
        pxzt: '',
        pdpxzt: 0,
        pxxsxz: [],
        pxlxxz: [],
        table1Data: [],
        table2Data: [],
        selectedTable1Data: [], // table1已选数据
        selectedTable2Data: [], // table2已选数据
        selectedTableData: [], //备份数据
        // data: generateData(),
        value: [],
        filterMethod(query, item) {
          return item.pinyin.indexOf(query) > -1;
        },
        tianjiaryList: [],
        xgtianjiaryList: [],
        pxqdList: [],
        xglist: {},
        updateItemOld: {},
        xgdialogVisible: false,
        xqdialogVisible: false,
        rydialogVisible: false,
        formInline: {},
        formInlinery: {
          bm: ''
        },
        tjlist: {
          pxzt: '',
          pxrq: '',
          pxdd: '',
          pxdw: '',
          pxjs: '',
          pxlx: '',
          pxxs: '',
          pxks: '',
          pxnr: '',
        },
        page: 1,
        pageSize: 10,
        total: 0,
        page1: 1,
        pageSize1: 10,
        total1: 0,
        selectlistRow: [], //列表的值
        selectlistRow1: [], //列表的值
        selectlistRow2: [], //列表的值
        dialogVisible: false, //添加弹窗状态
        //表单验证
        rules: {
          pxzt: [{
            required: true,
            message: '请输入培训主题',
            trigger: ['blur', 'change'],
          }, ],
          pxrq: [{
            required: true,
            message: '请选择培训日期',
            trigger: 'blur'
          }, ],
          pxdd: [{
            required: true,
            message: '请输入培训地点',
            trigger: ['blur', 'change'],
          }, ],
          pxdw: [{
            required: true,
            message: '请输入培训单位',
            trigger: ['blur', 'change'],
          }, ],
          pxjs: [{
            required: true,
            message: '请输入培训讲师',
            trigger: ['blur', 'change'],
          }, ],
          pxxs: [{
            required: true,
            message: '请选择培训形式',
            trigger: 'blur'
          }, ],
          pxlx: [{
            required: true,
            message: '请选择培训类型',
            trigger: 'blur'
          }, ],
          // pxks: [{
          // 	required: true,
          // 	message: '请输入课时',
          // 	trigger: 'blur'
          // },],
          pxks: [{
            validator: (rule, value, callback) => {
              checkKsValidator(rule, value, callback, this.tjlist)
            },
            trigger: ['blur', 'change']
          }],
          pxnr: [{
            required: true,
            message: '请输入培训主要内容',
            trigger: 'blur'
          }, ],
        },
        //导入
        dialogVisible_dr: false, //导入成员组弹窗状态
        dr_cyz_list: [], //待选择导入成员组列表
        multipleTable: [], //已选择导入成员组列表
        regionOption: [], //地域信息
        regionParams: {
          label: 'label', //这里可以配置你们后端返回的属性
          value: 'label',
          children: 'childrenRegionVo',
          expandTrigger: 'click',
          checkStrictly: true,
        }, //地域信息配置参数
        dwmc: '',
        dwdm: '',
        dwlxr: '',
        dwlxdh: '',
        year: '',
        yue: '',
        ri: '',
        Date: '',
        xh: [],
        dclist: [],
        pxtjlist: [],
        sxry: '',
        dr_dialog: false,
        //数据导入方式
        sjdrfs: '',
        pxzxs: '',
        bmm:undefined,
      }
    },
    computed: {},
    mounted() {
      this.pxqd()
      this.smdjxz()
      this.pxxsxzlist()
      this.pxlxxzlist()
      this.pxztlist()
      this.zzjg()
    },
    methods: {
      //全部组织机构List
      async zzjg() {
        let zzjgList = await getZzjgList()
        console.log(zzjgList);
        this.zzjgmc = zzjgList
        let shu = []
        console.log(this.zzjgmc);
        this.zzjgmc.forEach(item => {
          let childrenRegionVo = []
          this.zzjgmc.forEach(item1 => {
            if (item.bmm == item1.fbmm) {
              // console.log(item, item1);
              childrenRegionVo.push(item1)
              // console.log(childrenRegionVo);
              item.childrenRegionVo = childrenRegionVo
            }
          });
          // console.log(item);
          shu.push(item)
        })

        console.log(shu);
        console.log(shu[0].childrenRegionVo);
        let shuList = []
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
        console.log(shuList);
        shuList[0].childrenRegionVo.forEach(item => {
          this.regionOption.push(item)
        })
      },
      //涉密等级获取
      async smdjxz() {
        this.smdj = await getAllSmdj()

      },
      //涉密等级获取
      async pxxsxzlist() {
        this.pxxsxz = await getJypxxs()
      },
      //涉密等级获取
      async pxlxxzlist() {
        this.pxlxxz = await getJypxlx()
      },
      //
      handKsInput(value) {

      },
      //
      Radio(val) {

      },
      mbxzgb() {

      },
      cz() {
        this.formInline = {}
      },
      mbdc() {

      },

      sjgsh(sj) {

      },
      xz() {
        this.tianjiaryList = []
        this.table2Data = []
        this.formInlinery.bm = ''
        this.ry()
        this.dialogVisible = true
      },
      async ry() {
        let param = {
          bmid:this.bmm
        }
        let list = await getAllYhxx(param)
        console.log(list);
        this.table1Data = list
      },
      handleChange(){
        let nodesObj = this.$refs['cascaderArr'].getCheckedNodes()[0]
        console.log(nodesObj);
        if (nodesObj != undefined) {
          // console.log(nodesObj);
        this.bmm = nodesObj.data.bmm
        }else{
          this.bmm = undefined
        }
        
      },
      onSubmitry() {
        this.ry()
      },
      /**
       * table1选择事件处理函数
       * @param {array} rows 已勾选的数据
       */
      onTable1Select(rows) {
        console.log(rows);
        this.table2Data = rows
        this.selectlistRow = rows
        // this.selectedTable1Data = [...rows];
        // this.filterAdd(this.selectedTable1Data, this.table2Data, 'sfzhm');
        // this.selectedTable1Data = [];
        // this.$refs.table1.clearSelection();
      },

      /**
       * table2选择事件处理函数
       * @param {array} rows 已勾选的数据
       */
      onTable2Select(rows) {
        console.log(rows);
        console.log(this.table2Data);
        console.log(this.$refs.table1.selection);
        this.$refs.table1.selection.forEach((item, label) => {
          if (item == rows) {
            this.$refs.table1.selection.splice(label, 1)
          }
        })
        this.table2Data.forEach((item, label) => {
          if (item == rows) {
            console.log(label);
            this.table2Data.splice(label, 1)
          }
        })
        // this.selectedTable2Data = [...rows];
        // this.table2Data = this.filterDelete(this.selectedTable2Data, this.table2Data, 'sfzhm');
        // this.selectedTable2Data = [];
      },

      /**
       * 添加按钮事件处理函数
       */
      onAdd() {

        // this.tianjiaryList = []
      },

      /**
       * 删除按钮事件处理函数
       */
      onDelete() {

      },

      /**
       * 根据选中项去重添加到array中
       * @param {array} records   待添加数据
       * @param {array} targetRecords   目标数据
       * @param {string} compareProperty  对比的重复属性
       * @param {boolean} isEnd   往尾部添加？默认往头部添加
       */
      filterAdd(records = [], targetRecords = [], compareProperty, isEnd = false) {
        const o = new Set();
        targetRecords.forEach(record => {
          o.add(record[compareProperty]);
        })
        records.forEach(record => {
          if (!o.has(record[compareProperty])) {
            if (isEnd) {
              targetRecords.push(record);
            } else {
              targetRecords.unshift(record);
            }
          }
        })
      },

      /**
       * 删除数组中数据
       * @param {array} records   待删除数据
       * @param {array} targetRecords   目标数据
       * @param {string} compareProperty  对比的重复属性
       * @return {array} 删除待删除数据后的目标数据
       */
      filterDelete(records = [], targetRecords = [], compareProperty) {
        const o = new Set();
        records.forEach(record => {
          o.add(record[compareProperty]);
        })

        return targetRecords.filter((item) => !o.has(item[compareProperty]))
      },
      addpxry() {
        this.tianjiaryList = this.table2Data
        this.xglist.rypxdjList = this.table2Data
        this.rydialogVisible = false
      },
      //导入
      chooseFile() {

      },
      //----成员组选择
      handleSelectionChange(val) {

      },
      //---确定导入成员组
      drcy() {

      },
      //----表格导入方法
      readExcel(e) {

      },
      //导出
      exportList() {

      },
      //修改
      updataDialog(form) {
        this.$refs[form].validate((valid) => {
          if (valid) {
            // 删除旧的
            // deletePxqd(this.updateItemOld)
            // 插入新的
            let that = this
            updatePxdj(this.xglist).then(() => {
              // 刷新页面表格数据
              that.pxqd()
            })

            // 关闭dialog
            this.$message.success('修改成功')
            this.xgdialogVisible = false

          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      async xqyl(row) {
        this.updateItemOld = JSON.parse(JSON.stringify(row))

        this.xglist = JSON.parse(JSON.stringify(row))
        this.pxrq = this.xglist.pxrq
        this.pxzt = this.xglist.pxzt
        let ry = await getPxdjById({
          pxid: this.xglist.pxid
        })
        // this.table2Data = ry.rypxdjList
        this.xglist.rypxdjList = ry.rypxdjList
        // this.form1.ywlx = row.ywlx
        console.log('old', row)
        console.log("this.xglist.ywlx", this.xglist);
        this.xqdialogVisible = true
      },

      async updateItem(row) {
        this.ry()
        this.updateItemOld = JSON.parse(JSON.stringify(row))
        this.xglist = JSON.parse(JSON.stringify(row))
        let ry = await getPxdjById({
          pxid: this.xglist.pxid
        })
        console.log(ry)
        this.xglist.rypxdjList = ry.rypxdjList
        this.table2Data = ry.rypxdjList
        // this.form1.ywlx = row.ywlx
        console.log('old', row)
        console.log("this.xglist.ywlx", this.xglist);
        this.xgtianjiaryList = ry.rypxdjList
        this.xgdialogVisible = true
      },
      ryxg() {
        this.rydialogVisible = true
      },
      //查询
      onSubmit() {
        this.page = 1
        this.pxqd()
      },

      returnSy() {
        this.$router.push("/tzglsy");
      },
      async pxqd() {
        let params = {
          page: this.page,
          pageSize: this.pageSize,
          pxxs: this.formInline.pxxs,
        }
        if (this.formInline.pxrq != null) {
          params.kssj = this.formInline.pxrq[0]
          params.jssj = this.formInline.pxrq[1]
        }
        // Object.assign(params, this.formInline)
        let resList = await getPxdjxxList(params)
        console.log("params", params);

        this.pxqdList = resList.records
        // this.dclist = resList.list_total
        // this.dclist.forEach((item, label) => {
        // this.xh.push(label + 1)
        // })
        this.total = resList.total
      },
      //删除
      shanchu(id) {
        let that = this
        if (this.selectlistRow != '') {
          this.$confirm('是否继续删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let valArr = this.selectlistRow
            // console.log("....", val);
            valArr.forEach(function (item) {
              let param = {
                pxid: item.pxid,
                dwid: item.dwid,
              }

              removePxdj(param).then(() => {
                that.pxqd()

              })
              console.log("删除：", item);
              console.log("删除：", item);
            })
            // let params = valArr
            this.$message({
              message: '删除成功',
              type: 'success'
            });
          }).catch(() => {
            this.$message('已取消删除')
          })
        } else {
          this.$message({
            message: '未选择删除记录，请选择下列列表',
            type: 'warning'
          });
        }
      },
      //添加
      showDialog() {

        this.dialogVisible = true
      },
      //确定添加成员组
      submitTj(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let params = {
              dwid: '111',
              pxzt: this.tjlist.pxzt,
              pxjs: this.tjlist.pxjs,
              pxrq: this.tjlist.pxrq,
              pxdd: this.tjlist.pxdd,
              pxdw: this.tjlist.pxdw,
              pxnr: this.tjlist.pxnr,
              pxks: this.tjlist.pxks,
              pxxs: this.tjlist.pxxs,
              pxlx: this.tjlist.pxlx,
              rypxdjList: this.tianjiaryList,
              cjrid: '111',
              // pxqdid: getUuid()
            }
            let that = this
            savePxdj(params).then(() => {
              that.resetForm()
              that.pxqd()
              that.pxztlist()
            })
            this.dialogVisible = false

            this.$message({
              message: '添加成功',
              type: 'success'
            });



          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      deleteTkglBtn() {

      },
      tianjiary() {},
      selectRow(val) {
        console.log(val);
        this.selectlistRow = val;
      },
      selectRow1(val) {
        console.log(val);
        this.selectlistRow1 = val;
      },
      selectRow2(val) {
        console.log(val);
        this.selectlistRow2 = val;
      },
      deletery1() {
        this.selectlistRow1.forEach(item => {
          console.log(item);
          this.tianjiaryList.forEach((item1, index) => {
            if (item.sfzhm == item1.sfzhm) {
              console.log(index);
              this.tianjiaryList.splice(index, 1)
            }
          })
        })
      },
      deletery2() {
        this.selectlistRow2.forEach(item => {
          console.log(item);
          this.xglist.rypxdjList.forEach((item1, index) => {
            if (item.sfzhm == item1.sfzhm) {
              console.log(index);
              this.xglist.rypxdjList.splice(index, 1)
            }
          })
        })
      },
      //列表分页--跳转页数
      handleCurrentChange(val) {
        this.page = val
        this.pxqd()
      },
      //列表分页--更改每页显示个数
      handleSizeChange(val) {
        this.page = 1
        this.pageSize = val
        this.pxqd()
      },
      handleCurrentChange1(val) {
        this.page1 = val
        this.ry()
      },
      //列表分页--更改每页显示个数
      handleSizeChange1(val) {
        this.page1 = 1
        this.pageSize1 = val
        this.ry()
      },
      //添加重置
      resetForm() {
        this.tjlist.pxzt = ''
        this.tjlist.pxjs = ''
        this.tjlist.pxrq = ''
        this.tjlist.pxdd = ''
        this.tjlist.pxdw = ''
        this.tjlist.pxnr = ''
        this.tjlist.pxks = ''
        this.tjlist.pxxs = ''
        this.tjlist.pxlx = ''
        this.formInlinery.bm = ''
      },
      handleClose(done) {

        this.resetForm()
        this.dialogVisible = false
      },
      // 弹框关闭触发
      close(formName) {
        // 清空表单校验，避免再次进来会出现上次校验的记录
        this.$refs[formName].resetFields();
      },
      close1(form) {
        // 清空表单校验，避免再次进来会出现上次校验的记录
        this.$refs[form].resetFields();
        this.formInlinery.bm = ''
      },
      onInputBlur(index) {


      },
      //模糊查询培训主题
      querySearchpxzt(queryString, cb) {
        var restaurants = this.restaurantspxzt;
        console.log("restaurants", restaurants);
        var results = queryString ? restaurants.filter(this.createFilterpxzt(queryString)) : restaurants;
        console.log("results", results);
        // 调用 callback 返回建议列表的数据
        for (var i = 0; i < results.length; i++) {
          for (var j = i + 1; j < results.length; j++) {
            if (results[i].pxzt === results[j].pxzt) {
              results.splice(j, 1);
              j--;
            }
          }
        }
        cb(results);
        console.log("cb(results.zw)", results);
      },
      createFilterpxzt(queryString) {
        return (restaurant) => {
          return (restaurant.pxzt.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        };
      },
      //模糊查询培训地点
      querySearchpxdd(queryString, cb) {
        var restaurants = this.restaurantspxzt;
        console.log("restaurants", restaurants);
        var results = queryString ? restaurants.filter(this.createFilterpxdd(queryString)) : restaurants;
        console.log("results", results);
        // 调用 callback 返回建议列表的数据
        for (var i = 0; i < results.length; i++) {
          for (var j = i + 1; j < results.length; j++) {
            if (results[i].pxdd === results[j].pxdd) {
              results.splice(j, 1);
              j--;
            }
          }
        }
        cb(results);
        console.log("cb(results.zw)", results);
      },
      createFilterpxdd(queryString) {
        return (restaurant) => {
          return (restaurant.pxdd.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        };
      },
      //模糊查询培训单位
      querySearchpxdw(queryString, cb) {
        var restaurants = this.restaurantspxzt;
        console.log("restaurants", restaurants);
        var results = queryString ? restaurants.filter(this.createFilterpxdw(queryString)) : restaurants;
        console.log("results", results);
        // 调用 callback 返回建议列表的数据
        for (var i = 0; i < results.length; i++) {
          for (var j = i + 1; j < results.length; j++) {
            if (results[i].pxdw === results[j].pxdw) {
              results.splice(j, 1);
              j--;
            }
          }
        }
        cb(results);
        console.log("cb(results.zw)", results);
      },
      createFilterpxdw(queryString) {
        return (restaurant) => {
          return (restaurant.pxdw.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        };
      },
      //模糊查询培训讲师
      querySearchpxjs(queryString, cb) {
        var restaurants = this.restaurantspxzt;
        console.log("restaurants", restaurants);
        var results = queryString ? restaurants.filter(this.createFilterpxjs(queryString)) : restaurants;
        console.log("results", results);
        // 调用 callback 返回建议列表的数据
        for (var i = 0; i < results.length; i++) {
          for (var j = i + 1; j < results.length; j++) {
            if (results[i].pxjs === results[j].pxjs) {
              results.splice(j, 1);
              j--;
            }
          }
        }
        cb(results);
        console.log("cb(results.zw)", results);
      },
      createFilterpxjs(queryString) {
        return (restaurant) => {
          return (restaurant.pxjs.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        };
      },
      createFilterpxdw(queryString) {
        return (restaurant) => {
          return (restaurant.pxdw.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        };
      },
      //模糊查询培训讲师
      querySearchpxjs(queryString, cb) {
        var restaurants = this.restaurantspxzt;
        console.log("restaurants", restaurants);
        var results = queryString ? restaurants.filter(this.createFilterpxjs(queryString)) : restaurants;
        console.log("results", results);
        // 调用 callback 返回建议列表的数据
        for (var i = 0; i < results.length; i++) {
          for (var j = i + 1; j < results.length; j++) {
            if (results[i].pxjs === results[j].pxjs) {
              results.splice(j, 1);
              j--;
            }
          }
        }
        cb(results);
        console.log("cb(results.zw)", results);
      },
      createFilterpxjs(queryString) {
        return (restaurant) => {
          return (restaurant.pxjs.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        };
      },
      async pxztlist() {
        let resList = await getAllPxdj()
        this.restaurantspxzt = resList;
      },
      handleRowClick(row, column, event) {
        this.$refs.table1.toggleRowSelection(row);
      },
      forsmdj(row) {
        let hxsj
        this.smdj.forEach(item => {
          if (row.smdj == item.id) {
            hxsj = item.mc
          }
        })
        return hxsj
      },
      forpxlx(row) {
        let hxsj
        this.pxlxxz.forEach(item => {
          if (row.pxlx == item.id) {
            hxsj = item.mc
          }
        })
        return hxsj
      },
      forpxxs(row) {
        let hxsj
        this.pxxsxz.forEach(item => {
          if (row.pxxs == item.id) {
            hxsj = item.mc
          }
        })
        return hxsj
      }
    },
    watch: {

    }
  }

</script>

<style scoped>
  .bg_con {
    width: 100%;
  }

  .daochu {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  /* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

  .dabg {
    /* margin-top: 10px; */
    box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
    border-radius: 8px;
    /* padding: 20px 20px; */
    width: 100%;
  }

  /deep/.mhcx .el-form-item {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .xmlb-title {
    line-height: 60px;
    width: 100%;
    padding-left: 10px;
    height: 60px;
    background: url(../../assets/background/bg-02.png) no-repeat left;
    background-size: 100% 100%;
    text-indent: 10px;
    /* margin: 0 20px; */
    color: #0646bf;
    font-weight: 700;
  }

  .fhsy {
    display: inline-block;
    width: 120px;
    margin-top: 10px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 30px;
    padding-top: 4px;
    float: right;
    background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
    background-size: 100% 100%;
  }

  .item_button {
    height: 100%;
    float: left;
    padding-left: 10px;
    line-height: 50px;
  }

  .select_wrap {
    /* //padding: 5px; */

    .select_wrap_content {
      float: left;
      width: 100%;
      line-height: 50px;
      /* // padding-left: 20px; */
      /* // padding-right: 20px; */
      height: 100%;
      background: rgba(255, 255, 255, 0.7);

      .item_label {
        padding-left: 10px;
        height: 100%;
        float: left;
        line-height: 50px;
        font-size: 1em;
      }
    }
  }

  .mhcx1 {
    margin-top: 0px;
  }

  .widthw {
    width: 8vw;
  }

  .clearfix:after {
    /*伪元素是行内元素 正常浏览器清除浮动方法*/
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }

  /* .cd {
		width: 184px;
	} */

  .pxryqd::before {
    content: "";
    position: absolute;
    left: 8px;
    top: 314px;
    width: 5px;
    height: 20px;
    border-radius: 2px;
    background: #409eef;
  }

  .btn:hover {
    cursor: pointer;
  }

  /deep/.el-transfer-panel .el-transfer-panel__footer {
    position: relative;
  }

  /deep/.el-dialog {
    margin-top: 6vh !important;
  }

</style>
