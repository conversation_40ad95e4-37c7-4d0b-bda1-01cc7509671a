<template>
  <div class="sec-container" v-loading="loading">
    <!-- 标题 -->
    <p class="sec-title">基本信息</p>
    <div class="sec-form-container">
      <el-form ref="formName" :model="tjlist" label-width="225px">
        <!-- 第一部分包括姓名到常住地公安start -->
        <div class="sec-header-section">
          <div class="sec-form-left">
            <el-form-item label="所在部门">
              <template slot-scope="scope">
                <el-cascader v-model="tjlist.szbm" style="width: 100%;" :options="regionOption" :props="regionParams"
                  filterable clearable ref="cascaderArr" @change="handleChange(1)"></el-cascader>
              </template>
            </el-form-item>
            <el-form-item label="申请人">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.xqr"
                :fetch-suggestions="querySearch" placeholder="请输入申请人" style="width:100%">
              </el-autocomplete>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="外发传递期限">
              <el-date-picker v-model="tjlist.wfcdqx" class="riq" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </div>
          <div class="sec-form-left sec-form-left-textarea">
            <el-form-item label="用途">
              <el-input placeholder="" type="textarea" v-model="tjlist.yt" clearable></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="接收单位">
              <el-input placeholder="" v-model="tjlist.jsdw" clearable></el-input>
              <!-- <el-cascader v-model="tjlist.jsdw" style="width: 100%;" :options="regionOption" :props="regionParams"
                filterable clearable ref="cascaderArr"></el-cascader> -->
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="传递起始地点">
              <el-input placeholder="" v-model="tjlist.qsdd" clearable></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="传递目的地点">
              <el-input placeholder="" v-model="tjlist.mddd" clearable></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="移交部门">
              <template slot-scope="scope">
                <el-cascader v-model="tjlist.yjrszbm" style="width: 100%;" :options="regionOption" :props="regionParams"
                  filterable clearable ref="cascaderArr" @change="handleChange(2)"></el-cascader>
              </template>
            </el-form-item>
            <el-form-item label="移交人">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.yjr"
                :fetch-suggestions="querySearch" placeholder="请输入移交人" style="width:100%">
              </el-autocomplete>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="传递部门">
              <template slot-scope="scope">
                <el-cascader v-model="tjlist.cdrszbm" style="width: 100%;" :options="regionOption" :props="regionParams"
                  filterable clearable ref="cascaderArr" @change="handleChange(3)"></el-cascader>
              </template>
            </el-form-item>
            <el-form-item label="传递人">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.cdr"
                :fetch-suggestions="querySearch" placeholder="请输入传递人" style="width:100%">
              </el-autocomplete>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="项目经理部门">
              <template slot-scope="scope">
                <el-cascader v-model="tjlist.xmjlszbm" style="width: 100%;" :options="regionOption" :props="regionParams"
                  filterable clearable ref="cascaderArr" @change="handleChange(4)"></el-cascader>
              </template>
            </el-form-item>
            <el-form-item label="项目经理">
              <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.xmjl"
                :fetch-suggestions="querySearch" placeholder="请输入项目经理" style="width:100%">
              </el-autocomplete>
            </el-form-item>
          </div>
          <p class="sec-title">采取防护措施</p>
          <div class="sec-form-third haveBorderTop">
            <div class="sec-left-text">
              <div>
                防护措施：<el-checkbox-group v-model="tjlist.fhcs" class="checkbox">
                  <el-checkbox v-for="item in xdfsList" :label="item.xdfsmc" :value="item.xdfsid"
                    :key="item.xdfsid"></el-checkbox>
                </el-checkbox-group>
              </div>
              <div style="margin-top: 10px;">交通工具： <el-checkbox-group v-model="tjlist.jtgj" class="checkbox">
                  <el-checkbox v-for="item in jtgjList" :label="item.jtgjmc" :value="item.jtgjid"
                    :key="item.jtgjid"></el-checkbox>
                </el-checkbox-group>
              </div>
              <div style="display: flex;align-items: center;" class="brno">交通路线：<el-input v-model="tjlist.jtxl"
                  style="width: 300px;border-right: none;"></el-input> </div>
              <p>注：传递绝密级文件，实行二人护送制。</p>
            </div>
          </div>
        </div>
        <!-- 载体详细信息start -->
        <p class="sec-title">载体详细信息</p>
        <el-table border class="sec-el-table" :data="tjlist.ztwfWfscScjlList"
          :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="ztmc" label="载体名称"></el-table-column>
          <el-table-column prop="xmbh" label="项目编号"></el-table-column>
          <el-table-column prop="ztbh" label="载体编号"></el-table-column>
          <el-table-column prop="lx" label="载体类型"></el-table-column>
          <el-table-column prop="smmj" label="密级"></el-table-column>
          <el-table-column prop="bmqx" label="保密期限"></el-table-column>
          <el-table-column prop="ys" label="页数/大小"></el-table-column>
          <el-table-column prop="fs" label="份数"></el-table-column>
        </el-table>
        <!-- 载体详细信息end -->
        <!-- 底部操作按钮start -->
        <div class="sec-form-six haveBorderTop sec-footer">
          <el-button @click="returnIndex" class="fr ml10" plain>返回</el-button>
          <el-button @click="chooseApproval" class="fr" type="success">保存并提交</el-button>
          <el-button @click="save" class="fr" type="primary">临时保存</el-button>
        </div>
        <!-- 底部操作按钮end -->

      </el-form>
    </div>
    <!-- 发起申请弹框start -->
    <el-dialog title="选择审批人" :close-on-click-modal="false" :visible.sync="approvalDialogVisible" width="40%" :destroy-on-close="true">
      <div class="dlFqsqContainer">
        <label for="">部门:</label>
        <el-cascader v-model="ryChoose.bm" :options="regionOption" :props="regionParams" filterable clearable
          ref="cascaderArr" @change="bmSelectChange"></el-cascader>
        <label for="">姓名:</label>
        <el-input class="input2" v-model="ryChoose.xm" clearable placeholder="姓名"></el-input>
        <el-button class="searchButton" type="primary" icon="el-icon-search" @click="searchRy">查询</el-button>
        <BaseTable class="baseTable" :tableHeight="'300'" :key="tableKey" :showIndex=true :tableData="ryDatas" :columns="applyColumns"
          :showSingleSelection="true" :handleColumn="handleColumnApply" :showPagination=true :currentPage="page"
          :pageSize="pageSize" :totalCount="total" @handleCurrentChange="handleCurrentChangeRy"
          @handleSizeChange="handleSizeChangeRy" @handleSelectionChange="handleSelectionChange">
        </BaseTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" class="fr ml10" @click="approvalDialogVisible = false">关 闭</el-button>
        <el-button @click="saveAndSubmit" class="fr" type="success">提交</el-button>
        <!-- <el-button @click="save" class="fr" type="primary">保存</el-button> -->
        <div style="clear:both"></div>
      </span>
    </el-dialog>
    <!-- 发起申请弹框end -->
  </div>
</template>
<script>
import {
  getLcSLid,
  getZzjgList,
  getSpUserList,
  getLoginInfo,
  getFwdyidByFwlx,
  savaZtqdBatch,
  getAllYhxx,
  deleteSlxxBySlid,
} from '../../../api/index'
import {
  addZtglWfcd,
  updateZtglWfcd
} from '../../../api/ztwf'
import {
  deleteZtqdByYjlid
} from '../../../api/ztwcxd'
import { getUserInfo } from '../../../api/dwzc'
import { getAllGwxx } from '../../../api/qblist'
import { getAllSmdj } from '../../../api/xlxz'
import BaseTable from '../../components/common/baseTable.vue'
import AddLineTable from "../../components/common/addLineTable.vue"; //人工纠错组件
export default {
  components: {
    AddLineTable,
    BaseTable
  },
  props: {},
  data() {
    return {
      tableKey:1,
      value1: '',
      loading: false,
      // 弹框人员选择条件
      ryChoose: {
        'bm': '',
        'xm': ''
      },
      gwmclist: [],
      smdjxz: [],
      regionOption: [], // 部门下拉
      page: 1, // 审批人弹框当前页
      pageSize: 10, // 审批人弹框每页条数
      radioIdSelect: '', // 审批人弹框人员单选
      ryDatas: [], // 弹框人员选择
      total: 0, // 弹框人员总数
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true
      }, //地域信息配置参数
      // table 行样式
      headerCellStyle: {
        background: '#EEF7FF',
        color: '#4D91F8'
      },
      // form表单提交数据
      tjlist: {
        xqr: '',
        szbm: [],
        yjrszbm: [],
        cdrszbm: [],
        xmjlszbm: [],
        wfcdqx: [],
        ztwfWfscScjlList: [],
        yt: '',
        jsdw: '',
        qsdd: '',
        mddd: '',
        fhcs: [],
        jtgj: [],
        jtxl: '',
      },
      // // 载体详细信息
      // ztwfWfscScjlList: [{
      //   'ztmc': '',
      //   'xmbh': '',
      //   'ztbh': '',
      //   'lx': '',
      //   'smmj': '',
      //   'bmqx': '',
      //   'ys': '',
      //   'fs': '',
      //   'czbtn1': '增加行',
      //   'czbtn2': '',
      // }],
      ztlxList: [
        {
          lxid: '1',
          lxmc: '纸介质'
        },
        {
          lxid: '2',
          lxmc: '光盘'
        },
        {
          lxid: '3',
          lxmc: '电磁介质'
        },
      ],
      smdjList: [
        {
          smdjid: '1',
          smdjmc: '绝密'
        },
        {
          smdjid: '2',
          smdjmc: '机密'
        },
        {
          smdjid: '3',
          smdjmc: '秘密'
        },
        {
          smdjid: '4',
          smdjmc: '内部'
        },
      ],
      xdfsList: [
        {
          xdfsid: '1',
          xdfsmc: '包装密封，封口处加盖密封章'
        },
        {
          xdfsid: '2',
          xdfsmc: '指派专人传递'
        },
        {
          xdfsid: '3',
          xdfsmc: '密码箱防护'
        },
      ],
      jtgjList: [
        {
          jtgjid: '1',
          jtgjmc: '飞机'
        },
        {
          jtgjid: '2',
          jtgjmc: '火车'
        },
        {
          jtgjid: '3',
          jtgjmc: '专车'
        },
      ],
      ryInfo: {},
      // 政治面貌下拉选项
      sltshow: '', // 文档的缩略图显示
      routeType: '',
      pdfBase64: '',
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      approvalDialogVisible: false, // 选择申请人弹框
      fileRow: '',
      // 选择审核人table
      applyColumns: [{
        name: '姓名',
        prop: 'xm',
        scopeType: 'text',
        formatter: false
      },
      {
        name: '部门',
        prop: 'bmmc',
        scopeType: 'text',
        formatter: false
      },
      {
        name: '岗位',
        prop: 'gwmc',
        scopeType: 'text',
        formatter: false
      }
      ],
      handleColumnApply: [],
      scqk: [
        {
          sfty: '同意',
          id: 1
        },
        {
          sfty: '不同意',
          id: 0
        },
      ],
      2: false,
      ztidList: [],
    }
  },
  computed: {

  },
  mounted() {
    this.onfwid()
    this.smdj()
    this.gwxx()
    this.smry()
    this.getOrganization()
    this.yhDatas = this.$route.query.datas
    this.routeType = this.$route.query.type
    if (this.$route.query.type == 'update') {
      this.tjlist = this.$route.query.datas
      console.log(this.tjlist);
      this.tjlist.ztwfWfscScjlList = this.$route.query.ztqs
      let Array = []
      Array.push(this.tjlist.wfqsrq, this.tjlist.wfjzrq)
      console.log(Array);
      this.tjlist.wfcdqx = Array
      this.tjlist.szbm = this.tjlist.szbm.split('/')
      this.tjlist.cdrszbm = this.tjlist.cdrszbm.split('/')
      this.tjlist.yjrszbm = this.tjlist.yjrszbm.split('/')
      this.tjlist.xmjlszbm = this.tjlist.xmjlszbm.split('/')
    } else {
      this.dqlogin()
      this.tjlist.ztwfWfscScjlList = this.$route.query.datas
      this.ztidList = JSON.parse(JSON.stringify(this.tjlist.ztwfWfscScjlList))
      console.log(this.tjlist.ztwfWfscScjlList);
      console.log(this.ztidList);
    }
    this.tjlist.ztwfWfscScjlList.forEach((item) => {
      if (item.lx == 1) {
        item.lx = '纸介质'
      } else if (item.lx == 2) {
        item.lx = '光盘'
      } else if (item.lx == 3) {
        item.lx = '电磁介质'
      }
      if (item.smmj == 1) {
        item.smmj = '绝密'
      } else if (item.smmj == 2) {
        item.smmj = '机密'
      } else if (item.smmj == 3) {
        item.smmj = '秘密'
      } else if (item.smmj == 4) {
        item.smmj = '内部'
      }
    })
  },
  methods: {
    async dqlogin() {
      let data = await getUserInfo()
      this.tjlist.szbm = data.bmmc.split('/')
      this.tjlist.xqr = data.xm
      this.tjlist.yjrszbm = data.bmmc.split('/')
      // this.tjlist.yjr = data.xm
      this.tjlist.cdrszbm = data.bmmc.split('/')
      // this.tjlist.cdr = data.xm
      this.tjlist.xmjlszbm = data.bmmc.split('/')
      // this.tjlist.xmjl = data.xm
    },
    async handleChange(index) {
      let resList
      let params
      if (index == 1) {
        this.tjlist.yjrszbm = this.tjlist.szbm
        this.tjlist.cdrszbm = this.tjlist.szbm
        this.tjlist.xmjlszbm = this.tjlist.szbm
        params = {
          bmmc: this.tjlist.szbm.join('/')
        }
        resList = await getAllYhxx(params)
        this.tjlist.xqr = "";
      } else if (index == 2) {
        params = {
          bmmc: this.tjlist.yjrszbm.join('/')
        }
        resList = await getAllYhxx(params)
        this.tjlist.yjr = "";

      } else if (index == 3) {
        params = {
          bmmc: this.tjlist.jsrszbm.join('/')
        }
        resList = await getAllYhxx(params)
        this.tjlist.jsr = "";
      } else if (index == 4) {
        params = {
          bmmc: this.tjlist.xmjlszbm.join('/')
        }
        resList = await getAllYhxx(params)
        this.tjlist.xmjl = "";
      }
      this.restaurants = resList;
    },
    //人员获取
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async smry() {
      this.restaurants = await getAllYhxx()
    },
    chRadio() { },
    async gwxx() {
      let param = {
        bmmc: this.tjlist.bmmc
      }
      let data = await getAllGwxx(param)
      this.gwmclist = data
      console.log(data);
    },
    //获取涉密等级信息
    async smdj() {
      let data = await getAllSmdj()
      this.smdjxz = data
    },
    handleSelectBghgwmc(item, i) {
      console.log(i);
      this.gwmclist.forEach(item1 => {
        if (i == item1.gwmc) {
          console.log(item1);
          this.tjlist.bgsmdj = item1.smdj
        }

      })
    },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    handleSelectionChange(index, row) {
      this.radioIdSelect = row
    },
    // 载体详细信息增加行
    addRow(data) {
      data.push({
        'ztmc': '',
        'xmbh': '',
        'ztbh': '',
        'lx': '',
        'smmj': '',
        'bmqx': '',
        'ys': '',
        'fs': '',
        'czbtn1': '增加行',
        'czbtn2': '删除',
      })
    },
    // 载体详细信息删除行
    delRow(index, rows) {
      rows.splice(index, 1)
    },
    jyxx() {
      if (this.tjlist.xqr == '' || this.tjlist.xqr == undefined) {
        this.$message.error('请输入申请人')
        return true
      }
      if (this.tjlist.szbm.length == 0 || this.tjlist.szbm == undefined) {
        this.$message.error('请选择所在部门')
        return true
      }
      if (this.tjlist.wfcdqx.length == 0 || this.tjlist.wfcdqx == undefined) {
        this.$message.error('请选择外发传递期限')
        return true
      }
      if (this.tjlist.yt == '' || this.tjlist.yt == undefined) {
        this.$message.error('请输入用途')
        return true
      }
      if (this.tjlist.jsdw == '' || this.tjlist.jsdw == undefined) {
        this.$message.error('请输入接收单位')
        return true
      }
      if (this.tjlist.qsdd == '' || this.tjlist.qsdd == undefined) {
        this.$message.error('请输入传递起始地点')
        return true
      }
      if (this.tjlist.mddd == '' || this.tjlist.mddd == undefined) {
        this.$message.error('请输入传递目的地点')
        return true
      }
      if (this.tjlist.yjrszbm.length == 0 || this.tjlist.yjrszbm == undefined) {
        this.$message.error('请选择移交部门')
        return true
      }
      if (this.tjlist.yjr == '' || this.tjlist.yjr == undefined) {
        this.$message.error('请输入移交人')
        return true
      }
      if (this.tjlist.cdrszbm.length == 0 || this.tjlist.cdrszbm == undefined) {
        this.$message.error('请选择传递部门')
        return true
      }
      if (this.tjlist.cdr == '' || this.tjlist.cdr == undefined) {
        this.$message.error('请输入传递人')
        return true
      }
      if (this.tjlist.xmjlszbm.length == 0 || this.tjlist.xmjlszbm == undefined) {
        this.$message.error('请选择项目经理部门')
        return true
      }
      if (this.tjlist.xmjl == '' || this.tjlist.xmjl == undefined) {
        this.$message.error('请输入项目经理')
        return true
      }
      if (this.tjlist.fhcs.length == 0 || this.tjlist.fhcs == undefined) {
        this.$message.error('请选择防护措施')
        return true
      }
      if (this.tjlist.jtgj.length == 0 || this.tjlist.jtgj == undefined) {
        this.$message.error('请选择交通工具')
        return true
      }
      if (this.tjlist.jtxl == '' || this.tjlist.jtxl == undefined) {
        this.$message.error('请输入交通路线')
        return true
      }
    },
    async onfwid() {
      let params = {
        fwlx: 21
      }
      let data = await getFwdyidByFwlx(params)
      console.log(data);
      this.fwdyid = data.data.fwdyid
    },
    // 保存
    async save() {
      if (this.jyxx()) {
        return
      }
      let param = {
        'fwdyid': this.fwdyid,
        'lcslclzt': 3
      }
      // param.smryid = ''
      let ztid = []
      this.ztidList.forEach((item) => {
        console.log(item);
        ztid.push(item.ztid)
      })
      console.log(ztid);
      param.smryid = ztid.join(',')
      console.log(param.smryid);
      let res = await getLcSLid(param)
      if (res.code == 10000) {
        let szbmArr = JSON.parse(JSON.stringify(this.tjlist.szbm))
        let yjrszbmArr = JSON.parse(JSON.stringify(this.tjlist.yjrszbm))
        let cdrszbmArr = JSON.parse(JSON.stringify(this.tjlist.cdrszbm))
        let xmjlszbmArr = JSON.parse(JSON.stringify(this.tjlist.xmjlszbm))
        this.tjlist.szbm = szbmArr.join('/')
        this.tjlist.yjrszbm = yjrszbmArr.join('/')
        this.tjlist.cdrszbm = cdrszbmArr.join('/')
        this.tjlist.xmjlszbm = xmjlszbmArr.join('/')
        this.tjlist.slid = res.data.slid
        this.tjlist.lcslid = res.data.slid
        this.tjlist.wfqsrq = this.tjlist.wfcdqx[0]
        this.tjlist.wfjzrq = this.tjlist.wfcdqx[1]
        this.tjlist.ztwfWfscScjlList.forEach((item) => {
          if (item.lx == '纸介质') {
            item.lx = 1
          } else if (item.lx == '光盘') {
            item.lx = 2
          } else if (item.lx == '电磁介质') {
            item.lx = 3
          }
          if (item.smmj == '绝密') {
            item.smmj = 1
          } else if (item.smmj == '机密') {
            item.smmj = 2
          } else if (item.smmj == '秘密') {
            item.smmj = 3
          } else if (item.smmj == '内部') {
            item.smmj = 4
          }
        })
        let params = this.tjlist
        if (this.routeType == 'update') {
          let resDatas = await updateZtglWfcd(params)
          if (resDatas.code == 10000) {
            this.tjlist.ztwfWfscScjlList.forEach((item) => {
              item.splx = 4
              item.yjlid = resDatas.data
              item.jsdw = this.tjlist.jsdw
            })
            let del = await deleteZtqdByYjlid({ 'yjlid': this.tjlist.jlid })
            if (del.code == 10000) {
              let data = await savaZtqdBatch(this.tjlist.ztwfWfscScjlList)
              if (data.code == 10000) {
                this.$router.push('/ztwfsc')
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
              }
            }
          }
        } else {
          let resDatas = await addZtglWfcd(params)
          if (resDatas.code == 10000) {
            this.tjlist.ztwfWfscScjlList.forEach((item) => {
              item.splx = 4
              item.yjlid = resDatas.data
              item.jsdw = this.tjlist.jsdw
            })
            let data = await savaZtqdBatch(this.tjlist.ztwfWfscScjlList)
            if (data.code == 10000) {
              this.$router.push('/ztwfsc')
              this.$message({
                message: '保存成功',
                type: 'success'
              })
            } else {
              deleteSlxxBySlid({ slid: res.data.slid })
            }
          }
        }
      }
    },
    //全部组织机构List
    async getOrganization() {
      let zzjgList = await getZzjgList()
      this.zzjgmc = zzjgList
      let shu = []
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            childrenRegionVo.push(item1)
            item.childrenRegionVo = childrenRegionVo
          }
        });
        shu.push(item)
      })
      let shuList = []
      let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    handleSelectionChange1(index, row) {
      this.radioIdSelect = row
    },
    handleCurrentChangeRy(val) {
      this.page = val
      this.chooseApproval()
    },
    //列表分页--更改每页显示个数
    handleSizeChangeRy(val) {
      this.page = 1
      this.pageSize = val
      this.chooseApproval()
    },
    // 人员搜索
    searchRy() {
      this.tableKey++
      this.chooseApproval()
    },
    // 发起申请选择人员 人员下拉
    bmSelectChange(item) {
      if (item != undefined) {
        this.ryChoose.bm = item.join('/')
      }
    },
    // 选择审批人
    async chooseApproval() {
     // this.getOrganization()
      this.approvalDialogVisible = true
      let param = {
        'page': this.page,
        'pageSize': this.pageSize,
        'fwdyid': this.fwdyid,
        'bmmc': this.ryChoose.bm,
        'xm': this.ryChoose.xm
      }
      let resData = await getSpUserList(param)
      if (resData.records) {
        // this.loading = false
        this.ryDatas = resData.records
        this.total = resData.total
      } else {
        this.$message.error('数据获取失败！')
      }

    },
    // 保存并提交
    async saveAndSubmit() {
      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {
        if (this.jyxx()) {
          return
        }
        let param = {
          'fwdyid': this.fwdyid
        }
        let ztid = []
        this.ztidList.forEach((item) => {
          console.log(item);
          ztid.push(item.ztid)
        })
        console.log(ztid);
        param.smryid = ztid.join(',')
        console.log(param.smryid);
        this.tjlist.ztwfWfscScjlList.forEach((item) => {
          if (item.lx == '纸介质') {
            item.lx = 1
          } else if (item.lx == '光盘') {
            item.lx = 2
          } else if (item.lx == '电磁介质') {
            item.lx = 3
          }
          if (item.smmj == '绝密') {
            item.smmj = 1
          } else if (item.smmj == '机密') {
            item.smmj = 2
          } else if (item.smmj == '秘密') {
            item.smmj = 3
          } else if (item.smmj == '内部') {
            item.smmj = 4
          }
        })
        let szbmArr = JSON.parse(JSON.stringify(this.tjlist.szbm))
        let yjrszbmArr = JSON.parse(JSON.stringify(this.tjlist.yjrszbm))
        let cdrszbmArr = JSON.parse(JSON.stringify(this.tjlist.cdrszbm))
        let xmjlszbmArr = JSON.parse(JSON.stringify(this.tjlist.xmjlszbm))
        this.tjlist.szbm = szbmArr.join('/')
        this.tjlist.yjrszbm = yjrszbmArr.join('/')
        this.tjlist.cdrszbm = cdrszbmArr.join('/')
        this.tjlist.xmjlszbm = xmjlszbmArr.join('/')
        if (this.routeType == 'update') {
          param.lcslclzt = 2
          param.slid = this.tjlist.slid
          param.clrid = this.radioIdSelect.yhid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.slid = res.data.slid
            this.tjlist.wfqsrq = this.tjlist.wfcdqx[0]
            this.tjlist.wfjzrq = this.tjlist.wfcdqx[1]
            let params = this.tjlist
            console.log(this.tjlist);

            let resDatas = await updateZtglWfcd(params)
            if (resDatas.code == 10000) {
              this.tjlist.ztwfWfscScjlList.forEach((item) => {
                item.splx = 4
                item.yjlid = resDatas.data
                item.jsdw = this.tjlist.jsdw
              })
              let del = await deleteZtqdByYjlid({ 'yjlid': this.tjlist.jlid })
              if (del.code == 10000) {
                let data = await savaZtqdBatch(this.tjlist.ztwfWfscScjlList)
                if (data.code == 10000) {
                  this.$router.push('/ztwfsc')
                  this.$message({
                    message: '保存成功',
                    type: 'success'
                  })
                }
              }
            }
          }
        } else {
          param.lcslclzt = 0
          param.clrid = this.radioIdSelect.yhid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.slid = res.data.slid
            this.tjlist.wfqsrq = this.tjlist.wfcdqx[0]
            this.tjlist.wfjzrq = this.tjlist.wfcdqx[1]
            let params = this.tjlist
            console.log(this.tjlist);
            let resDatas = await addZtglWfcd(params)
            if (resDatas.code == 10000) {
              this.tjlist.ztwfWfscScjlList.forEach((item) => {
                item.splx = 4
                item.yjlid = resDatas.data
                item.jsdw = this.tjlist.jsdw
              })
              let data = await savaZtqdBatch(this.tjlist.ztwfWfscScjlList)
              if (data.code == 10000) {
                this.$router.push('/ztwfsc')
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
              } else {
                deleteSlxxBySlid({ slid: res.data.slid })
              }
            }
          }
        }
      } else {
        this.$message({
          message: '请选择审批人',
          type: 'warning'
        })
      }
    },
    // 返回
    returnIndex() {
      this.$router.push('/ztwfsc')
    }
  },
  watch: {

  }
}

</script>

<style scoped>
.sec-container {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: overlay;
}

.sec-title {
  border-left: 5px solid #1b72d8;
  color: #1b72d8;
  font-size: 20px;
  font-weight: 700;
  text-indent: 10px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.sec-form-container {
  width: 100%;
  height: 100%;
}

.sec-form-left {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
}

.sec-form-left:not(:first-child) {
  border-top: 0;
}

.sec-form-left .el-form-item {
  float: left;
  width: 100%;
}

.sec-header-section {
  width: 100%;
  position: relative;
}

.sec-header-pic {
  width: 258px;
  position: absolute;
  right: 0px;
  top: 0;
  height: 163px;
  border: 1px solid #CDD2D9;
  border-left: 0;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sec-form-second {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
  border-top: 0;
}

.sec-form-third {
  border: 1px solid #CDD2D9;
  /* height: 40px;  */
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

/deep/.el-checkbox-group {
  display: flex;
  justify-content: center;
  background-color: #F5F7FA;
  border-right: 1px solid #CDD2D9;
}

.checkbox {
  display: inline-block !important;
  background-color: rgba(255, 255, 255, 0) !important;
  border-right: none !important;
}

.sec-form-four {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-five {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  overflow: hidden;
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.yulan {
  text-align: center;
  cursor: pointer;
  color: #3874D5;
  font-weight: 600;
  float: left;
  margin-left: 10px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 2px solid #EBEBEB;
}

.sec-form-six {
  border: 1px solid #CDD2D9;
  overflow: hidden;
  background: #ffffff;
  padding: 10px;
}

.ml10 {
  margin-left: 10px;
}

.sec-footer {
  margin-top: 10px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

.sec-form-left-textarea {
  height: 54px !important;
}

.sec-form-left-textarea>>>.el-form-item__label {
  line-height: 54px !important;
}

>>>.sec-form-four .el-textarea__inner {
  border: none;
}

.sec-left-text {
  float: left;
  margin-right: 130px;
}

.haveBorderTop {
  border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
  width: 500px !important;
}

>>>.longLabel .el-form-item__content {
  margin-left: 500px !important;
  padding-left: 20px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

/* .sec-form-second:not(:first-child){
  border-top: 0;
} */
.sec-form-second .el-form-item {
  float: left;
  width: 100%;
}

.sec-el-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
  padding-left: 15px;
  background-color: #F5F7FA;
  width: calc(100% - 16px);
  border-right: 1px solid #CDD2D9;
  color: #C0C4CC;
}

>>>.sec-el-table .el-input__inner {
  border: none !important;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
  width: 200px;
  text-align: center;
  font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
  border: none;
  border-right: 1px solid #CDD2D9;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item {
  margin-bottom: 0px;
}

/* >>>.el-form > div {
  border: 1px solid #CDD2D9;;
} */
>>>.el-form-item__label {
  border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
} */
.riq {
  width: 100% !important;
}

.widthw {
  width: 6vw;
}

.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}
</style>
