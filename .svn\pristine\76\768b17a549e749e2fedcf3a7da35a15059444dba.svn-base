{"version": 3, "sources": ["webpack:///./src/api/bmAi.js", "webpack:///src/renderer/view/bmai/bmai.vue", "webpack:///./src/renderer/view/bmai/bmai.vue?4e5f", "webpack:///./src/renderer/view/bmai/bmai.vue"], "names": ["quest_api", "data", "createAPI", "BASE_URL", "bmai", "components", "props", "textarea", "inputxx", "computed", "watch", "methods", "buttonClicked", "_this", "this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "parmas", "wrap", "_context", "prev", "next", "JSON", "parse", "stringify_default", "setTimeout", "$refs", "hgroups", "innerHTML", "question", "bmAi_quest_api", "sent", "scrollTop", "scrollHeight", "answer", "$message", "error", "stop", "handleEnter", "event", "console", "log", "key", "shift<PERSON>ey", "preventDefault", "mounted", "created", "beforeCreated", "beforeMount", "beforUpdate", "updated", "beforeDestory", "destoryed", "activated", "bmai_bmai", "render", "$createElement", "_self", "_c", "_m", "staticRenderFns", "_h", "staticClass", "staticStyle", "width", "attrs", "src", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "2MAKaA,EAAY,SAAAC,GAAA,OAAQC,YAAUC,kBAAuB,OAAOF,ICkBzEG,GAEAC,cAEAC,SACAL,KALA,WAOA,OACAM,SAAA,GACAC,QAAA,KAIAC,YAEAC,SAEAC,SACAC,cADA,WACA,IAAAC,EAAAC,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAnB,EAAA,OAAAe,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,UACA,IAAAX,EAAAN,WACAM,EAAAL,QAAAiB,KAAAC,MAAAC,IAAAd,EAAAN,YAEAqB,WAAA,WACAf,EAAAN,SAAA,IACA,KACAM,EAAAN,SAPA,CAAAe,EAAAE,KAAA,gBAQAX,EAAAgB,MAAAC,QAAAC,WAAA,6KAKAlB,EAAAN,SALA,qCAQAa,GACAY,SAAAnB,EAAAL,SAjBAc,EAAAE,KAAA,EAmBAS,EAAAb,GAnBA,OAmBAnB,EAnBAqB,EAAAY,KAoBArB,EAAAgB,MAAAC,QAAAK,UAAAtB,EAAAgB,MAAAC,QAAAM,aAEAR,WAAA,WACAf,EAAAgB,MAAAC,QAAAC,WAAA,gMAKA9B,EAAAoC,OALA,2BAOAxB,EAAAgB,MAAAC,QAAAK,UAAAtB,EAAAgB,MAAAC,QAAAM,cACA,KA/BAd,EAAAE,KAAA,iBAiCAX,EAAAyB,SAAAC,MAAA,UAjCA,yBAAAjB,EAAAkB,SAAArB,EAAAN,KAAAE,IAoCA0B,YArCA,SAqCAC,GACAC,QAAAC,IAAAF,GACA,SAAAA,EAAAG,KAAAH,EAAAI,SAWAhC,KAAAP,UAAA,MAVAmC,EAAAK,iBACAjC,KAAAF,mBAwBAoC,QAlFA,aAqFAC,QArFA,aAyFAC,cAzFA,aA2FAC,YA3FA,aA6FAC,YA7FA,aA+FAC,QA/FA,aAiGAC,cAjGA,aAmGAC,UAnGA,aAqGAC,UArGA,cCpBeC,GADEC,OAFjB,WAA0B5C,KAAa6C,eAAb7C,KAAuC8C,MAAAC,GAAwB,OAA/D/C,KAA+DgD,GAAA,IAExEC,iBADjB,WAAoC,IAAaC,EAAblD,KAAa6C,eAA0BE,EAAvC/C,KAAuC8C,MAAAC,IAAAG,EAAwB,OAAAH,EAAA,OAAiBI,YAAA,kBAA4BJ,EAAA,UAAeI,YAAA,eAAAC,aAAwCC,MAAA,QAAeC,OAAQC,IAAA,uCCE9N,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEpE,EACAqD,GATF,EAVA,SAAAgB,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/188.c2748faefa106c1fb4bf.js", "sourcesContent": ["import {createAPI, createFileAPI, createUploadAPI,createDownloadAPI} from './request'\r\n// var BASE_URL = '/api'\r\nvar BASE_URL = '/bmai'\r\n//获取uuid\r\n\r\nexport const quest_api = data => createAPI(BASE_URL+\"/quest_api\", 'post',data)\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/api/bmAi.js", "<template>\r\n  <!-- <div class=\"bmaiContainer\" v-loading=\"pageLoading\"> -->\r\n  <div class=\"bmaiContainer\">\r\n    <iframe class=\"bmaiAiIframe\" style=\"width: 100%;\" src=\"http://221.212.111.71:28501/\">\r\n    </iframe>\r\n    <!-- <div class=\"bmaiCon\">\r\n      <div class=\"bmaiCenter\">\r\n        <div class=\"bmaiNr\" ref=\"hgroups\">\r\n        </div>\r\n        <div class=\"inputKK\">\r\n          <el-input placeholder=\"请输入对话内容，换行请使用Shift+Enter。输入/help查看自定义命令\" type=\"textarea\" v-model.trim=\"textarea\"\r\n            @keyup.enter.native=\"handleEnter($event)\" class=\"input-with-select\">\r\n          </el-input>\r\n          <el-button ref=\"myButton\" slot=\"append\" type=\"primary\" @click=\"buttonClicked\" plain>\r\n            <i class=\"el-icon-my-message\"></i></el-button>\r\n        </div>\r\n      </div>\r\n    </div> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { quest_api } from '../../../api/bmAi'\r\nexport default {\r\n  //import引入的组件需要注入到对象中才能使用\r\n  components: {\r\n  },\r\n  props: {},\r\n  data() {\r\n    //这里存放数据\r\n    return {\r\n      textarea: '',\r\n      inputxx: ''\r\n    }\r\n  },\r\n  //计算属性 类似于data概念\r\n  computed: {},\r\n  //监控data中数据变化\r\n  watch: {},\r\n  //方法集合\r\n  methods: {\r\n    async buttonClicked() {\r\n      if (this.textarea != '') {\r\n        this.inputxx = JSON.parse(JSON.stringify(this.textarea))\r\n      }\r\n      setTimeout(() => { //模拟系统延时1秒回答\r\n        this.textarea = \"\";\r\n      }, 50);\r\n      if (this.textarea) { //判断能容不能为空或空格 输入时也加了 .trim (意思是过滤输入时前后的空格)\r\n        this.$refs.hgroups.innerHTML += `\r\n          <div class=\"bmaiWdtw\" >\r\n            <div class=\"bmaiRwtb\">\r\n              <div class=\"bmaiWdImg\"></div>\r\n            </div>\r\n            <div class=\"bmaiWz\">${this.textarea}</div>\r\n          </div>\r\n        `; //用户输入时每次增加标签\r\n        let parmas = {\r\n          question: this.inputxx\r\n        }\r\n        let data = await quest_api(parmas)\r\n        this.$refs.hgroups.scrollTop = this.$refs.hgroups.scrollHeight; //用户说话后滚动条自动滚动\r\n\r\n        setTimeout(() => { //模拟系统延时1秒回答\r\n          this.$refs.hgroups.innerHTML += `\r\n          <div class=\"bmaiWdtw bmaiWdtw1\">\r\n            <div class=\"bmaiRwtb bmaiRwtb1\">\r\n              <div class=\"bmaiFyImg\"></div>\r\n            </div>\r\n            <div class=\"bmaiWz\">${data.answer}</div>\r\n          </div>`; //系统回答时每次增加的标签 注意和上边用户的class不同\r\n          this.$refs.hgroups.scrollTop = this.$refs.hgroups.scrollHeight; //系统说话后滚动条自动滚动\r\n        }, 1000);\r\n      } else {\r\n        this.$message.error(\"消息不可为空\"); //验证输入内容为空时 消息提示\r\n      }\r\n    },\r\n    handleEnter(event) {\r\n      console.log(event);\r\n      if (event.key == \"Enter\" && !event.shiftKey) {\r\n        event.preventDefault(); // 阻止浏览器默认换行操作\r\n        this.buttonClicked()\r\n      } else {\r\n        // // if (!e.shiftKey && e.keyCode == 13) {\r\n        // event.cancelBubble = true; //ie阻止冒泡行为\r\n        // event.stopPropagation();//Firefox阻止冒泡行为\r\n        // event.preventDefault(); //取消事件的默认动作*换行\r\n        //   //以下处理发送消息代码\r\n        //   console.log(this.textarea);\r\n        //     }\r\n        this.textarea += '\\n'\r\n        // const textarea = event.target;\r\n        // const cursorPosition = textarea.selectionStart;\r\n        // const textBeforeCursor = this.textarea.substring(0, cursorPosition);\r\n        // const textAfterCursor = this.textarea.substring(cursorPosition);\r\n        // this.textarea = `${textBeforeCursor}\\n${textAfterCursor}`;\r\n        // textarea.setSelectionRange(cursorPosition + 1, cursorPosition + 1);\r\n\r\n\r\n\r\n      }\r\n    },\r\n\r\n  },\r\n  //生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {\r\n\r\n  },\r\n  created() {\r\n\r\n  },\r\n  //生命周期-创建之前\r\n  beforeCreated() { },\r\n  //生命周期-挂载之前\r\n  beforeMount() { },\r\n  //生命周期-更新之前\r\n  beforUpdate() { },\r\n  //生命周期-更新之后\r\n  updated() { },\r\n  //生命周期-销毁之前\r\n  beforeDestory() { },\r\n  //生命周期-销毁完成\r\n  destoryed() { },\r\n  //如果页面有keep-alive缓存功能，这个函数会触发\r\n  activated() { }\r\n}\r\n</script>\r\n<style scoped>\r\n.bmaiContainer {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.bmaiAiIframe {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.bmaiCon {\r\n  height: 100%;\r\n  width: 100%;\r\n  background: #fff;\r\n}\r\n\r\n.bmaiCenter {\r\n  width: 702px;\r\n  height: auto;\r\n  padding: 96px 16px 100px 16px;\r\n  margin: 0 auto;\r\n  position: relative;\r\n}\r\n\r\n.bmaiNr {\r\n  width: 100%;\r\n  height: 548px;\r\n  margin-bottom: 20px;\r\n  overflow-y: scroll;\r\n}\r\n\r\n.el-icon-my-message {\r\n  background: url('./img/icon.png') center no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 28px;\r\n  height: 30px;\r\n}\r\n\r\n.inputKK {\r\n  width: 100%;\r\n  height: auto;\r\n  position: relative;\r\n}\r\n\r\n/deep/.el-textarea__inner {\r\n  height: 44px;\r\n}\r\n\r\n/deep/.el-button {\r\n  padding: 4px 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  border-radius: 0px 4px 4px 0px;\r\n  position: absolute;\r\n  right: 1px;\r\n  bottom: 1px;\r\n}\r\n\r\n/deep/.bmaiWdtw {\r\n  width: calc(100% - 32px);\r\n  height: auto;\r\n  padding: 16px;\r\n  background: #F7F8FA;\r\n  border-radius: 0.5rem;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  justify-content: space-between;\r\n}\r\n\r\n/deep/.bmaiWdtw1 {\r\n  background: transparent;\r\n}\r\n\r\n/deep/.bmaiRwtb {\r\n  width: 32px;\r\n  height: 32px;\r\n  background-color: rgb(255, 108, 108);\r\n  border-radius: 0.5rem;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/deep/.bmaiRwtb1 {\r\n  background-color: #165DFF;\r\n}\r\n\r\n/deep/.bmaiWz {\r\n  width: 632px;\r\n  height: auto;\r\n  line-height: 32px;\r\n  word-wrap: break-word;\r\n  /* 允许单词内断行 */\r\n  white-space: normal;\r\n  /* 处理空白符，自动换行 */\r\n}\r\n\r\n/deep/.bmaiWdImg {\r\n  width: 20px;\r\n  height: 20px;\r\n  background: url(./img/wd.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n}\r\n\r\n/deep/.bmaiFyImg {\r\n  width: 20px;\r\n  height: 20px;\r\n  background: url(./img/fy.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n}\r\n</style>\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/bmai/bmai.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _vm._m(0)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"bmaiContainer\"},[_c('iframe',{staticClass:\"bmaiAiIframe\",staticStyle:{\"width\":\"100%\"},attrs:{\"src\":\"http://221.212.111.71:28501/\"}})])}]\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-3e633fa9\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/bmai/bmai.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-3e633fa9\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./bmai.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./bmai.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./bmai.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-3e633fa9\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./bmai.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-3e633fa9\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/bmai/bmai.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}