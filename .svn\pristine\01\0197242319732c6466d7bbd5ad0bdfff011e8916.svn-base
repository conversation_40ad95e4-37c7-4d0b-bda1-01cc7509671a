{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/rycs/fmzdryscdjTable.vue", "webpack:///./src/renderer/view/rcgz/rycs/fmzdryscdjTable.vue?ad16", "webpack:///./src/renderer/view/rcgz/rycs/fmzdryscdjTable.vue"], "names": ["fmzdryscdjTable", "components", "AddLineTable", "addLineTable", "BaseTable", "baseTable", "props", "data", "table<PERSON><PERSON>", "xb", "id", "sfsc", "zgxl", "loading", "ry<PERSON><PERSON>ose", "bm", "xm", "regionOption", "page", "pageSize", "radioIdSelect", "ryDatas", "total", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "headerCellStyle", "background", "color", "tjlist", "sm<PERSON><PERSON>", "gj", "dwzwzc", "yrsmgw", "cym", "mz", "hyzk", "zzmm", "lxdh", "sfzhm", "hjdz", "hjdgajg", "czdz", "czgajg", "imageUrl", "yjqk", "qscfqk", "qtqk", "brcn", "zztd", "sszk", "zztdlist", "mc", "sszklist", "ryglRyscScjlList", "qssj", "zzsj", "szdw", "zw", "zmr", "czbtn1", "czbtn2", "ryglRyscJtcyList", "gxms", "jwjlqk", "cgszd", "ryglRyscYccgList", "cggj", "sy", "ryglRyscJwzzqkList", "jgmc", "zznr", "ryglRyscCfjlList", "cfdw", "cfsj", "cfjg", "cfyy", "ryglRyscSwzjList", "zjmc", "fjlb", "cyqk", "zjhm", "yxq", "ryInfo", "zzmmoptions", "ynoptions", "sltshow", "routeType", "dialogImageUrl", "dialogVisible", "approvalDialogVisible", "fileRow", "displayxq", "applyColumns", "name", "prop", "scopeType", "formatter", "handleColumnApply", "computed", "mounted", "this", "$route", "query", "type", "onfwid", "defaultym", "getOrganization", "methods", "sfzhmr", "arrExp", "test", "$message", "error", "sum", "i", "length", "parseInt", "substr", "toUpperCase", "org_birthday", "substring", "sex", "birthday", "birthdays", "Date", "replace", "d", "age", "getFullYear", "getMonth", "getDate", "nl", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "rwid", "ryxx", "jtcylist", "wrap", "_context", "prev", "next", "xqpd", "Object", "fmzdrydj", "sent", "map", "index", "zp", "undefined", "zpcl", "stop", "httpRequestimg", "_this2", "URL", "createObjectURL", "file", "fileimg", "blobToBase64", "dataurl", "split", "beforeAvatarUpload", "isJPG", "isPNG", "blob", "callback", "fileReader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "handleSelectionChange", "row", "addRow", "push", "delRow", "rows", "splice", "cyjshgxAddRow", "ybrgx", "sfywjjwjlqcqjlxk", "dw", "cyjshgxDelRow", "yscgqkAddRow", "qsrq", "zzrq", "jsnsdgjhdq", "yscgqkDelRow", "jsjwzzqkAddRow", "gjdq", "jsjwzzqkDelRow", "clhwffzqkAddRow", "_data$push", "cljg", "clyy", "defineProperty_default", "wdzlxz", "_this3", "_callee2", "returnData", "date", "sj", "_context2", "api", "dom_download", "content", "fileName", "Blob", "url", "window", "dom", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "clhwffzqkDelRow", "httpRequest", "_this4", "yulan", "console", "log", "shanchu", "_this5", "_callee3", "params", "_context3", "fwlx", "fwdyid", "jyxx", "gzdd", "gwmc", "sxzk", "save", "_this6", "_callee4", "_fmzdrydata", "_context4", "abrupt", "code", "for<PERSON>ach", "item", "$router", "message", "_this7", "_callee5", "zzjgList", "shu", "shuList", "list", "_context5", "zzjgmc", "childrenRegionVo", "item1", "bmm", "fbmm", "handleSelectionChange1", "handleCurrentChangeRy", "val", "chooseApproval", "handleSizeChangeRy", "searchRy", "bmSelectChange", "join", "_this8", "_callee6", "param", "resData", "_context6", "bmmc", "records", "saveAndSubmit", "_this9", "_callee7", "paramStatus", "_res", "_params", "_context7", "keys_default", "lcslclzt", "slid", "lcslid", "clrid", "yhid", "dwid", "rysc", "yhDatas", "returnIndex", "watch", "rycs_fmzdryscdjTable", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "_v", "ref", "attrs", "model", "label-width", "placeholder", "clearable", "disabled", "$$v", "$set", "scopedSlots", "_u", "key", "fn", "scope", "on", "blur", "_l", "v-model", "_s", "oninput", "action", "show-file-list", "before-upload", "http-request", "src", "staticStyle", "width", "_e", "size", "margin-top", "border", "header-cell-style", "stripe", "align", "$event", "$index", "position", "visible", "update:visible", "alt", "slot", "plain", "title", "close-on-click-modal", "for", "options", "filterable", "change", "icon", "tableHeight", "showIndex", "tableData", "columns", "showSingleSelection", "handleColumn", "showPagination", "currentPage", "totalCount", "handleCurrentChange", "handleSizeChange", "clear", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "+QAsQAA,GACAC,YACAC,aAAAC,EAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAOA,OACAC,SAAA,EACAC,KACAA,GAAA,IACAC,GAAA,IAGAD,GAAA,IACAC,GAAA,IAGAC,OACAD,GAAA,EACAC,KAAA,MAGAD,GAAA,EACAC,KAAA,MAGAC,OAEAF,GAAA,EACAE,KAAA,QAGAF,GAAA,EACAE,KAAA,SAGAF,GAAA,EACAE,KAAA,YAGAC,SAAA,EAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,gBACAC,KAAA,EACAC,SAAA,GACAC,cAAA,GACAC,WACAC,MAAA,EACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,iBACAC,WAAA,UACAC,MAAA,WAGAC,QACAC,OAAA,GACAjB,GAAA,GACAP,GAAA,GACAyB,GAAA,KACAC,OAAA,GACAC,OAAA,GACAC,IAAA,GACAC,GAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,QAAA,GACAC,KAAA,GACAC,OAAA,GACAC,SAAA,GACAC,KAAA,IACAC,OAAA,IACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,IAEAN,SAAA,GACAO,WAEA5C,GAAA,EACA6C,GAAA,OAGA7C,GAAA,EACA6C,GAAA,QAGAC,WAEA9C,GAAA,EACA6C,GAAA,OAGA7C,GAAA,EACA6C,GAAA,SAGA7C,GAAA,EACA6C,GAAA,QAIAE,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,KAGAC,mBACAC,KAAA,GACA1B,KAAA,GACA2B,OAAA,GACAnD,GAAA,GACAoD,MAAA,GACAL,OAAA,MACAC,OAAA,KAGAK,mBACAC,KAAA,GACAC,GAAA,GACAZ,KAAA,GACAD,KAAA,GAEAK,OAAA,MACAC,OAAA,KAGAQ,qBACAb,KAAA,GACAc,KAAA,GAEAC,KAAA,GACAxC,GAAA,GACA6B,OAAA,MACAC,OAAA,KAGAW,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAhB,OAAA,MACAC,OAAA,KAGAgB,mBACAC,KAAA,KACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,QACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,QACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,KACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,QACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,QACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAC,UAEAC,cACA9D,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAEAC,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAGAgE,YACA/D,MAAA,EACAD,MAAA,MAEAC,MAAA,EACAD,MAAA,MAEAiE,QAAA,GACAC,UAAA,GACAC,eAAA,GACAC,eAAA,EACAC,uBAAA,EACAC,QAAA,GACAC,WAAA,EAEAC,eAEAC,KAAA,KACAC,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAC,uBAGAC,YAEAC,QAnQA,WAoQAC,KAAAd,UAAAc,KAAAC,OAAAC,MAAAC,KACAH,KAAAI,SACAJ,KAAAK,YACAL,KAAAM,mBAEAC,SACAC,OADA,WAEA,IAAAC,GAAA,qCAEA,oBAAAC,KAAAV,KAAAxE,OAAAU,OAyCA,OADA8D,KAAAW,SAAAC,MAAA,YACA,EAtCA,IAFA,IAAAC,EAAA,EAEAC,EAAA,EAAAA,EAAAd,KAAAxE,OAAAU,MAAA6E,OAAA,EAAAD,IAEAD,GAAAG,SAAAhB,KAAAxE,OAAAU,MAAA+E,OAAAH,EAAA,OAAAL,EAAAK,GAKA,IAXA,yBASAD,EAAA,KAEAb,KAAAxE,OAAAU,MAAA+E,OAAA,MAAAC,cA2BA,OADAlB,KAAAW,SAAAC,MAAA,YACA,EA1BA,GAAAZ,KAAAxE,OAAAU,MAAA,CACA,IAAAiF,EAAAnB,KAAAxE,OAAAU,MAAAkF,UAAA,MAEAC,EADArB,KAAAxE,OAAAU,MAAAkF,UAAA,OACA,SACAE,EACAH,EAAAC,UAAA,KACA,IACAD,EAAAC,UAAA,KACA,IACAD,EAAAC,UAAA,KACAG,EAAA,IAAAC,KAAAF,EAAAG,QAAA,WACAC,EAAA,IAAAF,KACAG,EACAD,EAAAE,cACAL,EAAAK,eACAF,EAAAG,WAAAN,EAAAM,YACAH,EAAAG,YAAAN,EAAAM,YACAH,EAAAI,UAAAP,EAAAO,UACA,EACA,GACA9B,KAAAxE,OAAAvB,GAAAoH,EAEArB,KAAAxE,OAAAuG,GAAAJ,IAWAtB,UAhDA,WAgDA,IAAA2B,EAAAhC,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAN,EAAAC,EAAAM,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,UACA,OAAAZ,EAAA/B,OAAAC,MAAAC,KADA,CAAAuC,EAAAE,KAAA,QAAAF,EAAAE,KAAA,uBAIA,GAAAZ,EAAA/B,OAAAC,MAAA2C,OACAb,EAAAzC,WAAA,GAEA+C,EAAAN,EAAA/B,OAAAC,MAAAoC,KAPAI,EAAAE,KAAA,EAQAE,OAAAC,EAAA,EAAAD,EAAAR,SARA,cAQAC,EARAG,EAAAM,KASAhB,EAAAxG,OAAA+G,EATAG,EAAAE,KAAA,GAUAE,OAAAC,EAAA,EAAAD,EAAAR,SAVA,QAWA,IADAE,EAVAE,EAAAM,MAWAjC,OACAiB,EAAA/E,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,KAGAwE,EAAAvE,iBAAA+E,EAAAS,IAAA,SAAAlJ,EAAAmJ,GAQA,OAPA,GAAAA,GACAnJ,EAAAwD,OAAA,MACAxD,EAAAyD,OAAA,KAEAzD,EAAAwD,OAAA,MACAxD,EAAAyD,OAAA,MAEAzD,IAGA,IAAAwI,EAAAY,SAAAC,GAAAb,EAAAY,KACAnB,EAAAzF,SAAAuG,OAAAO,EAAA,EAAAP,CAAAP,EAAAY,KAEA,IAAAZ,EAAA5F,WAAAyG,GAAAb,EAAA5F,OACAqF,EAAA/C,QAAA6D,OAAAO,EAAA,EAAAP,CAAAP,EAAA5F,OArCA,yBAAA+F,EAAAY,SAAAjB,EAAAL,KAAAC,IA0CAsB,eA1FA,SA0FAxJ,GAAA,IAAAyJ,EAAAxD,KACAA,KAAAzD,SAAAkH,IAAAC,gBAAA3J,EAAA4J,MACA3D,KAAA4D,QAAA7J,EAAA4J,KACA3D,KAAA6D,aAAA9J,EAAA4J,KAAA,SAAAG,GACAN,EAAAhI,OAAA2H,GAAAW,EAAAC,MAAA,WAGAC,mBAjGA,SAiGAL,GACA,IAAAM,EAAA,eAAAN,EAAAxD,KACA+D,EAAA,cAAAP,EAAAxD,KAIA,OAHA8D,GAAAC,GACAlE,KAAAW,SAAAC,MAAA,wBAEAqD,GAAAC,GAGAL,aA1GA,SA0GAM,EAAAC,GACA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAA,SAAAC,GACAJ,EAAAI,EAAAC,OAAAC,SAEAL,EAAAM,cAAAR,IAEAS,sBAjHA,SAiHA1B,EAAA2B,GACA7E,KAAApF,cAAAiK,GAGAC,OArHA,SAqHA/K,GACAA,EAAAgL,MACA7H,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,QAIAwH,OAjIA,SAiIA9B,EAAA+B,GACAA,EAAAC,OAAAhC,EAAA,IAGAiC,cArIA,SAqIApL,GACAA,EAAAgL,MACAK,MAAA,GACA5K,GAAA,GACA6K,iBAAA,GACAC,GAAA,GACAjI,GAAA,GACArB,KAAA,GACAuB,OAAA,MACAC,OAAA,QAIA+H,cAlJA,SAkJArC,EAAA+B,GACAA,EAAAC,OAAAhC,EAAA,IAGAsC,aAtJA,SAsJAzL,GACAA,EAAAgL,MACAU,KAAA,GACAC,KAAA,GACAC,WAAA,GACA5H,GAAA,GACAR,OAAA,MACAC,OAAA,QAIAoI,aAjKA,SAiKA1C,EAAA+B,GACAA,EAAAC,OAAAhC,EAAA,IAGA2C,eArKA,SAqKA9L,GACAA,EAAAgL,MACAU,KAAA,GACAK,KAAA,GACA7H,KAAA,GACAC,KAAA,GACAX,OAAA,MACAC,OAAA,QAIAuI,eAhLA,SAgLA7C,EAAA+B,GACAA,EAAAC,OAAAhC,EAAA,IAGA8C,gBApLA,SAoLAjM,GAAA,IAAAkM,EACAlM,EAAAgL,MAAAkB,GACAR,KAAA,GACAS,KAAA,GACAC,KAAA,IAHAC,IAAAH,EAAA,OAIA,IAJAG,IAAAH,EAKA,gBALAG,IAAAH,EAMA,eANAA,KASAI,OA9LA,WA8LA,IAAAC,EAAAtG,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAmE,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAxE,EAAAC,EAAAM,KAAA,SAAAkE,GAAA,cAAAA,EAAAhE,KAAAgE,EAAA/D,MAAA,cAAA+D,EAAA/D,KAAA,EACAE,OAAA8D,EAAA,EAAA9D,GADA,OACA0D,EADAG,EAAA3D,KAEAyD,EAAA,IAAAjF,KACAkF,EAAAD,EAAA7E,cAAA,IAAA6E,EAAA5E,WAAA,GAAA4E,EAAA3E,UACAwE,EAAAO,aAAAL,EAAA,UAAAE,EAAA,QAJA,wBAAAC,EAAArD,SAAAiD,EAAAD,KAAArE,IAOA4E,aArMA,SAqMAC,EAAAC,GACA,IAAA5C,EAAA,IAAA6C,MAAAF,IACAG,EAAAC,OAAAzD,IAAAC,gBAAAS,GACAgD,EAAAC,SAAAC,cAAA,KACAF,EAAAG,MAAAC,QAAA,OACAJ,EAAAK,KAAAP,EACAE,EAAAM,aAAA,WAAAV,GACAK,SAAAM,KAAAC,YAAAR,GACAA,EAAAS,SAGAC,gBAhNA,SAgNA3E,EAAA+B,GACAA,EAAAC,OAAAhC,EAAA,IAGA4E,YApNA,SAoNA/N,GAAA,IAAAgO,EAAA/H,KACAA,KAAAf,QAAAwE,IAAAC,gBAAA3J,EAAA4J,MACA3D,KAAAV,QAAAvF,EAAA4J,KAEA3D,KAAA6D,aAAA9J,EAAA4J,KAAA,SAAAG,GACAiE,EAAAvM,OAAAmB,KAAAmH,EAAAC,MAAA,WAIAiE,MA7NA,WA8NAC,QAAAC,IAAAlI,KAAAd,WACA,OAAAc,KAAAd,UACAc,KAAAb,eAAAsE,IAAAC,gBAAA1D,KAAAV,SAEAU,KAAAb,eAAAa,KAAAf,QAEAe,KAAAZ,eAAA,GAGA+I,QAvOA,WAwOAnI,KAAAxE,OAAAmB,KAAA,GACAqD,KAAAf,QAAA,IAEAmB,OA3OA,WA2OA,IAAAgI,EAAApI,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAiG,IAAA,IAAAC,EAAAvO,EAAA,OAAAmI,EAAAC,EAAAM,KAAA,SAAA8F,GAAA,cAAAA,EAAA5F,KAAA4F,EAAA3F,MAAA,cACA0F,GACAE,KAAA,GAFAD,EAAA3F,KAAA,EAIAE,OAAA8D,EAAA,EAAA9D,CAAAwF,GAJA,OAIAvO,EAJAwO,EAAAvF,KAKAiF,QAAAC,IAAAnO,GACAqO,EAAAK,OAAA1O,OAAA0O,OANA,wBAAAF,EAAAjF,SAAA+E,EAAAD,KAAAnG,IAQAyG,KAnPA,WAoPA,UAAA1I,KAAAxE,OAAAhB,SAAA4I,GAAApD,KAAAxE,OAAAhB,IACAwF,KAAAW,SAAAC,MAAA,UACA,GAEA,IAAAZ,KAAAxE,OAAAU,YAAAkH,GAAApD,KAAAxE,OAAAU,OACA8D,KAAAW,SAAAC,MAAA,YACA,GAEA,IAAAZ,KAAAxE,OAAAvB,SAAAmJ,GAAApD,KAAAxE,OAAAvB,IACA+F,KAAAW,SAAAC,MAAA,UACA,GAEA,IAAAZ,KAAAxE,OAAAuG,SAAAqB,GAAApD,KAAAxE,OAAAuG,IACA/B,KAAAW,SAAAC,MAAA,UACA,GAEA,IAAAZ,KAAAxE,OAAApB,WAAAgJ,GAAApD,KAAAxE,OAAApB,MACA4F,KAAAW,SAAAC,MAAA,YACA,GAEA,GAAAZ,KAAAxE,OAAAmN,WAAAvF,GAAApD,KAAAxE,OAAAmN,MACA3I,KAAAW,SAAAC,MAAA,YACA,GAEA,IAAAZ,KAAAxE,OAAAoN,WAAAxF,GAAApD,KAAAxE,OAAAoN,MACA5I,KAAAW,SAAAC,MAAA,YACA,GAEA,GAAAZ,KAAAxE,OAAAoB,WAAAwG,GAAApD,KAAAxE,OAAAoB,MACAoD,KAAAW,SAAAC,MAAA,YACA,GAEA,IAAAZ,KAAAxE,OAAAqN,WAAAzF,GAAApD,KAAAxE,OAAAqN,MACA7I,KAAAW,SAAAC,MAAA,YACA,QAFA,GAOAkI,KA3RA,WA2RA,IAAAC,EAAA/I,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAA4G,IAAA,IAAAC,EAAA,OAAA/G,EAAAC,EAAAM,KAAA,SAAAyG,GAAA,cAAAA,EAAAvG,KAAAuG,EAAAtG,MAAA,WACAmG,EAAAvI,SADA,CAAA0I,EAAAtG,KAAA,eAAAsG,EAAAC,OAAA,qBAIAJ,EAAAL,OAJA,CAAAQ,EAAAtG,KAAA,eAAAsG,EAAAC,OAAA,oBAOA,UAAAJ,EAAA7J,UAPA,CAAAgK,EAAAtG,KAAA,gBAAAsG,EAAAtG,KAAA,EAQAE,OAAAC,EAAA,EAAAD,CAAAiG,EAAAvN,QARA,UASA,KATA0N,EAAAlG,KASAoG,KATA,CAAAF,EAAAtG,KAAA,gBAUAE,OAAAC,EAAA,EAAAD,EAAAR,KAAAyG,EAAAvN,OAAA8G,OACAyG,EAAAtL,iBAAA4L,QAAA,SAAAC,GACAA,EAAAhH,KAAAyG,EAAAvN,OAAA8G,OAZA4G,EAAAtG,KAAA,GAcAE,OAAAC,EAAA,EAAAD,CAAAiG,EAAAtL,kBAdA,QAAAyL,EAAAlG,KAgBA+F,EAAAQ,QAAAxE,KAAA,WACAgE,EAAApI,UACA6I,QAAA,OACArJ,KAAA,YAnBA+I,EAAAtG,KAAA,iBA0BAmG,EAAApI,SAAAC,MAAA,UA1BA,QA8BAmI,EAAAQ,QAAAxE,KAAA,WACAgE,EAAApI,UACA6I,QAAA,OACArJ,KAAA,YAjCA+I,EAAAtG,KAAA,wBAAAsG,EAAAtG,KAAA,GAqCAE,OAAAC,EAAA,EAAAD,CAAAiG,EAAAvN,QArCA,WAsCA,MADAyN,EArCAC,EAAAlG,MAsCAoG,KAtCA,CAAAF,EAAAtG,KAAA,gBAuCAmG,EAAAtL,iBAAA4L,QAAA,SAAAC,GACAA,EAAAhH,KAAA2G,EAAAlP,OAxCAmP,EAAAtG,KAAA,GA0CAE,OAAAC,EAAA,EAAAD,CAAAiG,EAAAtL,kBA1CA,QAAAyL,EAAAlG,KA4CA+F,EAAAQ,QAAAxE,KAAA,WACAgE,EAAApI,UACA6I,QAAA,OACArJ,KAAA,YA/CA+I,EAAAtG,KAAA,iBAsDAmG,EAAApI,SAAAC,MAAA,UAtDA,QAwDAqH,QAAAC,IAAAa,EAAAvN,QAxDA,yBAAA0N,EAAA5F,SAAA0F,EAAAD,KAAA9G,IA6DA3B,gBAxVA,WAwVA,IAAAmJ,EAAAzJ,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAsH,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAA5H,EAAAC,EAAAM,KAAA,SAAAsH,GAAA,cAAAA,EAAApH,KAAAoH,EAAAnH,MAAA,cAAAmH,EAAAnH,KAAA,EACAE,OAAA8D,EAAA,IAAA9D,GADA,cACA6G,EADAI,EAAA/G,KAEAyG,EAAAO,OAAAL,EACAC,KACAH,EAAAO,OAAAX,QAAA,SAAAC,GACA,IAAAW,KACAR,EAAAO,OAAAX,QAAA,SAAAa,GACAZ,EAAAa,KAAAD,EAAAE,OACAH,EAAAlF,KAAAmF,GACAZ,EAAAW,sBAGAL,EAAA7E,KAAAuE,KAEAO,KAdAE,EAAAnH,KAAA,EAeAE,OAAA8D,EAAA,EAAA9D,GAfA,OAgBA,KADAgH,EAfAC,EAAA/G,MAgBAoH,MACAR,EAAAP,QAAA,SAAAC,GACA,IAAAA,EAAAc,MACAP,EAAA9E,KAAAuE,KAIA,IAAAQ,EAAAM,MACAR,EAAAP,QAAA,SAAAC,GACArB,QAAAC,IAAAoB,GACAA,EAAAc,MAAAN,EAAAM,MACAP,EAAA9E,KAAAuE,KAIAO,EAAA,GAAAI,iBAAAZ,QAAA,SAAAC,GACAG,EAAAhP,aAAAsK,KAAAuE,KAhCA,yBAAAS,EAAAzG,SAAAoG,EAAAD,KAAAxH,IAmCAoI,uBA3XA,SA2XAnH,EAAA2B,GACA7E,KAAApF,cAAAiK,GAEAyF,sBA9XA,SA8XAC,GACAvK,KAAAtF,KAAA6P,EACAvK,KAAAwK,kBAGAC,mBAnYA,SAmYAF,GACAvK,KAAAtF,KAAA,EACAsF,KAAArF,SAAA4P,EACAvK,KAAAwK,kBAGAE,SAzYA,WA0YA1K,KAAAhG,WACAgG,KAAAwK,kBAGAG,eA9YA,SA8YArB,QACAlG,GAAAkG,IACAtJ,KAAA1F,SAAAC,GAAA+O,EAAAsB,KAAA,OAIAJ,eApZA,WAoZA,IAAAK,EAAA7K,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAA0I,IAAA,IAAAC,EAAAC,EAAA,OAAA9I,EAAAC,EAAAM,KAAA,SAAAwI,GAAA,cAAAA,EAAAtI,KAAAsI,EAAArI,MAAA,cAEAiI,EAAAxL,uBAAA,EACA0L,GACArQ,KAAAmQ,EAAAnQ,KACAC,SAAAkQ,EAAAlQ,SACA8N,OAAAoC,EAAApC,OACAyC,KAAAL,EAAAvQ,SAAAC,GACAC,GAAAqQ,EAAAvQ,SAAAE,IARAyQ,EAAArI,KAAA,EAUAE,OAAA8D,EAAA,GAAA9D,CAAAiI,GAVA,QAUAC,EAVAC,EAAAjI,MAWAmI,SAEAN,EAAAhQ,QAAAmQ,EAAAG,QACAN,EAAA/P,MAAAkQ,EAAAlQ,OAEA+P,EAAAlK,SAAAC,MAAA,WAhBA,wBAAAqK,EAAA3H,SAAAwH,EAAAD,KAAA5I,IAoBAmJ,cAxaA,WAwaA,IAAAC,EAAArL,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAkJ,IAAA,IAAAP,EAAAzC,EAAAiD,EAAAC,EAAAC,EAAA,OAAAvJ,EAAAC,EAAAM,KAAA,SAAAiJ,GAAA,cAAAA,EAAA/I,KAAA+I,EAAA9I,MAAA,YACA,IAAAyI,EAAAzQ,eAAA+Q,IAAAN,EAAAzQ,eAAAmG,OAAA,GADA,CAAA2K,EAAA9I,KAAA,YAEAmI,GACAtC,OAAA4C,EAAA5C,QAEA4C,EAAA5N,iBAAA4L,QAAA,SAAA7E,GACA,KAAAA,EAAA7G,OACA6G,EAAA7G,OAAA,EACA,KAAA6G,EAAA7G,SACA6G,EAAA7G,OAAA,KAGA,UAAA0N,EAAAnM,UAZA,CAAAwM,EAAA9I,KAAA,gBAaAmI,EAAAa,SAAA,EACAb,EAAAtP,OAAA4P,EAAAvM,OAAArD,OACAsP,EAAAc,KAAAR,EAAAvM,OAAAgN,OACAf,EAAAgB,MAAAV,EAAAzQ,cAAAoR,KAhBAN,EAAA9I,KAAA,GAiBAE,OAAA8D,EAAA,EAAA9D,CAAAiI,GAjBA,WAkBA,KAlBAW,EAAA1I,KAkBAoG,KAlBA,CAAAsC,EAAA9I,KAAA,gBAmBAyI,EAAA7P,OAAAyQ,KAAAZ,EAAAvM,OAAAmN,KACAZ,EAAA7P,OAAAsQ,OAAAT,EAAAvM,OAAAgN,OACAxD,GACA4D,KAAAb,EAAA7P,OACAyB,iBAAAoO,EAAApO,iBACAQ,iBAAA4N,EAAA5N,iBACAI,iBAAAwN,EAAAxN,iBACAG,mBAAAqN,EAAArN,mBACAG,iBAAAkN,EAAAlN,iBACAK,iBAAA6M,EAAA7M,kBA5BAkN,EAAA9I,KAAA,GA8BAE,OAAA8D,EAAA,IAAA9D,CAAAwF,GA9BA,WA+BA,KA/BAoD,EAAA1I,KA+BAoG,KA/BA,CAAAsC,EAAA9I,KAAA,gBAgCA2I,GACA9C,OAAA4C,EAAA5C,OACAoD,KAAAR,EAAA7P,OAAAsQ,QAlCAJ,EAAA9I,KAAA,GAoCAE,OAAA8D,EAAA,IAAA9D,CAAAyI,GApCA,QAqCA,KArCAG,EAAA1I,KAqCAoG,OACAiC,EAAA9B,QAAAxE,KAAA,SACAsG,EAAA1K,UACA6I,QAAA,UACArJ,KAAA,aAzCA,QAAAuL,EAAA9I,KAAA,wBA+CAmI,EAAAa,SAAA,EACAb,EAAAgB,MAAAV,EAAAzQ,cAAAoR,KACAjB,EAAAtP,OAAA4P,EAAAc,QAAA1Q,OAjDAiQ,EAAA9I,KAAA,GAkDAE,OAAA8D,EAAA,EAAA9D,CAAAiI,GAlDA,WAmDA,MADAS,EAlDAE,EAAA1I,MAmDAoG,KAnDA,CAAAsC,EAAA9I,KAAA,gBAoDAyI,EAAA7P,OAAAyQ,KAAAZ,EAAAc,QAAAF,KACAZ,EAAA7P,OAAAsQ,OAAAN,EAAAzR,KAAA8R,KACAJ,GACAS,KAAAb,EAAA7P,OACAyB,iBAAAoO,EAAApO,iBACAQ,iBAAA4N,EAAA5N,iBACAI,iBAAAwN,EAAAxN,iBACAG,mBAAAqN,EAAArN,mBACAG,iBAAAkN,EAAAlN,iBACAK,iBAAA6M,EAAA7M,kBA7DAkN,EAAA9I,KAAA,GA+DAE,OAAA8D,EAAA,IAAA9D,CAAA2I,GA/DA,QAgEA,KAhEAC,EAAA1I,KAgEAoG,MACAiC,EAAA9B,QAAAxE,KAAA,SACAsG,EAAA1K,UACA6I,QAAA,UACArJ,KAAA,aAGA2C,OAAA8D,EAAA,EAAA9D,EAAA+I,KAAAL,EAAAzR,KAAA8R,OAvEA,QAAAH,EAAA9I,KAAA,iBA4EAyI,EAAA1K,UACA6I,QAAA,SACArJ,KAAA,YA9EA,yBAAAuL,EAAApI,SAAAgI,EAAAD,KAAApJ,IAmFAmK,YA3fA,WA4fApM,KAAAuJ,QAAAxE,KAAA,aAGAsH,UC3gCeC,GADEC,OAFjB,WAA0B,IAAAC,EAAAxM,KAAayM,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAapN,KAAA,UAAAqN,QAAA,YAAA7R,MAAAuR,EAAA,QAAAO,WAAA,YAA4EC,YAAA,kBAA8BL,EAAA,KAAUK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,uBAAiCL,EAAA,WAAgBO,IAAA,WAAAC,OAAsBC,MAAAZ,EAAAhR,OAAA6R,cAAA,WAA0CV,EAAA,OAAYK,YAAA,uBAAiCL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnS,MAAA,QAAc2R,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyD6N,OAAQnS,MAAAuR,EAAAhR,OAAA,GAAA4I,SAAA,SAAAqJ,GAA+CjB,EAAAkB,KAAAlB,EAAAhR,OAAA,KAAAiS,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnS,MAAA,OAAc2S,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyD6N,OAAQnS,MAAAuR,EAAAhR,OAAA,IAAA4I,SAAA,SAAAqJ,GAAgDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,MAAAiS,IAAiCV,WAAA,uBAAiCP,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAOnS,MAAA,QAAe2S,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyDyO,IAAKC,KAAAzB,EAAAhM,QAAkB4M,OAAQnS,MAAAuR,EAAAhR,OAAA,MAAA4I,SAAA,SAAAqJ,GAAkDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,QAAAiS,IAAmCV,WAAA,0BAAmC,GAAAP,EAAAS,GAAA,KAAAN,EAAA,OAA4BK,YAAA,kBAA4BL,EAAA,gBAAqBK,YAAA,YAAAG,OAA+BnS,MAAA,QAAc2R,EAAA,kBAAuBQ,OAAOK,SAAA,IAAcJ,OAAQnS,MAAAuR,EAAAhR,OAAA,GAAA4I,SAAA,SAAAqJ,GAA+CjB,EAAAkB,KAAAlB,EAAAhR,OAAA,KAAAiS,IAAgCV,WAAA,cAAyBP,EAAA0B,GAAA1B,EAAA,YAAAlD,GAAgC,OAAAqD,EAAA,YAAsBkB,IAAAvE,EAAApP,GAAAiT,OAAmBgB,UAAA3B,EAAAhR,OAAAvB,GAAAe,MAAAsO,EAAApP,GAAAe,MAAAqO,EAAApP,MAAyDsS,EAAAS,GAAA,qBAAAT,EAAA4B,GAAA9E,EAAArP,SAAiD,OAAAuS,EAAAS,GAAA,KAAAN,EAAA,gBAAwCQ,OAAOnS,MAAA,QAAc2R,EAAA,YAAiBQ,OAAOhN,KAAA,SAAAmN,YAAA,GAAAC,UAAA,GAAAc,QAAA,uCAAAb,SAAA,IAA+GJ,OAAQnS,MAAAuR,EAAAhR,OAAA,GAAA4I,SAAA,SAAAqJ,GAA+CjB,EAAAkB,KAAAlB,EAAAhR,OAAA,KAAAiS,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnS,MAAA,MAAa2S,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyD6N,OAAQnS,MAAAuR,EAAAhR,OAAA,GAAA4I,SAAA,SAAAqJ,GAA+CjB,EAAAkB,KAAAlB,EAAAhR,OAAA,KAAAiS,IAAgCV,WAAA,uBAAgC,GAAAP,EAAAS,GAAA,KAAAN,EAAA,OAA4BK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnS,MAAA,QAAc2R,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyD6N,OAAQnS,MAAAuR,EAAAhR,OAAA,GAAA4I,SAAA,SAAAqJ,GAA+CjB,EAAAkB,KAAAlB,EAAAhR,OAAA,KAAAiS,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnS,MAAA,UAAgB2R,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyD6N,OAAQnS,MAAAuR,EAAAhR,OAAA,KAAA4I,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,OAAAiS,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnS,MAAA,YAAkB2R,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyD6N,OAAQnS,MAAAuR,EAAAhR,OAAA,OAAA4I,SAAA,SAAAqJ,GAAmDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,SAAAiS,IAAoCV,WAAA,oBAA6B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCK,YAAA,YAAAG,OAA+BnS,MAAA,UAAgB2R,EAAA,kBAAuBQ,OAAOK,SAAAhB,EAAAjN,WAAyB6N,OAAQnS,MAAAuR,EAAAhR,OAAA,KAAA4I,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,OAAAiS,IAAkCV,WAAA,gBAA2BP,EAAA0B,GAAA1B,EAAA,cAAAlD,GAAkC,OAAAqD,EAAA,YAAsBkB,IAAAvE,EAAApP,GAAAiT,OAAmBgB,UAAA3B,EAAAhR,OAAApB,KAAAY,MAAAsO,EAAApP,GAAAe,MAAAqO,EAAApP,MAA2DsS,EAAAS,GAAA,qBAAAT,EAAA4B,GAAA9E,EAAAlP,WAAmD,WAAAoS,EAAAS,GAAA,KAAAN,EAAA,OAAmCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnS,MAAA,UAAgB2R,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyD6N,OAAQnS,MAAAuR,EAAAhR,OAAA,KAAA4I,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,OAAAiS,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnS,MAAA,UAAgB2R,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAAyD6N,OAAQnS,MAAAuR,EAAAhR,OAAA,KAAA4I,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,OAAAiS,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnS,MAAA,UAAgB2R,EAAA,YAAiBQ,OAAOG,YAAA,OAAAC,UAAA,GAAAC,SAAAhB,EAAAjN,WAA6D6N,OAAQnS,MAAAuR,EAAAhR,OAAA,KAAA4I,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,OAAAiS,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCK,YAAA,YAAAG,OAA+BnS,MAAA,UAAgB2R,EAAA,kBAAuBQ,OAAOK,SAAAhB,EAAAjN,WAAyB6N,OAAQnS,MAAAuR,EAAAhR,OAAA,KAAA4I,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,OAAAiS,IAAkCV,WAAA,gBAA2BP,EAAA0B,GAAA1B,EAAA,cAAAlD,GAAkC,OAAAqD,EAAA,YAAsBkB,IAAAvE,EAAApP,GAAAiT,OAAmBgB,UAAA3B,EAAAhR,OAAArB,KAAAa,MAAAsO,EAAApP,GAAAe,MAAAqO,EAAApP,MAA2DsS,EAAAS,GAAA,qBAAAT,EAAA4B,GAAA9E,EAAAnP,WAAmD,WAAAqS,EAAAS,GAAA,KAAAN,EAAA,OAAmCK,YAAA,mBAA6BL,EAAA,OAAAA,EAAA,aAA4BK,YAAA,kBAAAG,OAAqCmB,OAAA,IAAAC,kBAAA,EAAAC,gBAAAhC,EAAAxI,mBAAAyK,eAAAjC,EAAAjJ,eAAAiK,SAAAhB,EAAAjN,aAAuIiN,EAAA,SAAAG,EAAA,OAA2BK,YAAA,YAAAG,OAA+BuB,IAAAlC,EAAAjQ,YAAoBoQ,EAAA,KAAUK,YAAA,oCAAA2B,aAA6DC,MAAA,WAAiBpC,EAAAS,GAAA,KAAAT,EAAAjN,UAAuFiN,EAAAqC,KAAvFlC,EAAA,aAA+CQ,OAAOhN,KAAA,UAAA2O,KAAA,WAAiCtC,EAAAS,GAAA,sBAAAT,EAAAS,GAAA,KAAAN,EAAA,KAA4DK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,iCAA2CL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,OAAAH,EAAAS,GAAA,2DAAAN,EAAA,kBAAmGQ,OAAOK,SAAAhB,EAAAjN,WAAyB6N,OAAQnS,MAAAuR,EAAAhR,OAAA,KAAA4I,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,OAAAiS,IAAkCV,WAAA,gBAA2BP,EAAA0B,GAAA1B,EAAA,kBAAAlD,GAAsC,OAAAqD,EAAA,YAAsBkB,IAAAvE,EAAApP,GAAAiT,OAAmBgB,UAAA3B,EAAAhR,OAAAoB,KAAA5B,MAAAsO,EAAApP,GAAAe,MAAAqO,EAAApP,MAA2DsS,EAAAS,GAAAT,EAAA4B,GAAA9E,EAAAvM,SAA4B,OAAAyP,EAAAS,GAAA,KAAAN,EAAA,OAA+BgC,aAAaI,aAAA,UAAqBvC,EAAAS,GAAA,uBAAAN,EAAA,kBAAqDQ,OAAOK,SAAAhB,EAAAjN,WAAyB6N,OAAQnS,MAAAuR,EAAAhR,OAAA,KAAA4I,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhR,OAAA,OAAAiS,IAAkCV,WAAA,gBAA2BP,EAAA0B,GAAA1B,EAAA,kBAAAlD,GAAsC,OAAAqD,EAAA,YAAsBkB,IAAAvE,EAAApP,GAAAiT,OAAmBgB,UAAA3B,EAAAhR,OAAAqN,KAAA7N,MAAAsO,EAAApP,GAAAe,MAAAqO,EAAApP,MAA2DsS,EAAAS,GAAAT,EAAA4B,GAAA9E,EAAAvM,SAA4B,WAAAyP,EAAAS,GAAA,KAAAN,EAAA,KAAiCK,YAAA,cAAwBR,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAuDK,YAAA,eAAAG,OAAkC6B,OAAA,GAAAjV,KAAAyS,EAAA/O,iBAAAwR,qBAA6D3T,WAAA,UAAAC,MAAA,WAA0C2T,OAAA,MAAcvC,EAAA,mBAAwBQ,OAAOhN,KAAA,QAAAyO,MAAA,KAAA5T,MAAA,KAAAmU,MAAA,YAA2D3C,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOzN,KAAA,OAAA1E,MAAA,SAA8B2S,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAE,SAAAhB,EAAAjN,WAA0C6N,OAAQnS,MAAA8S,EAAAlJ,IAAA,KAAAT,SAAA,SAAAqJ,GAAgDjB,EAAAkB,KAAAK,EAAAlJ,IAAA,OAAA4I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOzN,KAAA,KAAA1E,MAAA,MAAyB2S,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAE,SAAAhB,EAAAjN,WAA0C6N,OAAQnS,MAAA8S,EAAAlJ,IAAA,GAAAT,SAAA,SAAAqJ,GAA8CjB,EAAAkB,KAAAK,EAAAlJ,IAAA,KAAA4I,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOzN,KAAA,SAAA1E,MAAA,sBAA6C2S,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,aAAwBQ,OAAOG,YAAA,MAAAE,SAAAhB,EAAAjN,WAA6C6N,OAAQnS,MAAA8S,EAAAlJ,IAAA,OAAAT,SAAA,SAAAqJ,GAAkDjB,EAAAkB,KAAAK,EAAAlJ,IAAA,SAAA4I,IAAmCV,WAAA,qBAAgCP,EAAA0B,GAAA1B,EAAA,mBAAAlD,GAAuC,OAAAqD,EAAA,aAAuBkB,IAAAvE,EAAArO,MAAAkS,OAAsBnS,MAAAsO,EAAAtO,MAAAC,MAAAqO,EAAArO,WAAyC,UAAUuR,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOzN,KAAA,QAAA1E,MAAA,MAA4B2S,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAE,SAAAhB,EAAAjN,WAA0C6N,OAAQnS,MAAA8S,EAAAlJ,IAAA,MAAAT,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAK,EAAAlJ,IAAA,QAAA4I,IAAkCV,WAAA,4BAAsCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOzN,KAAA,OAAA1E,MAAA,QAA6B2S,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,aAAwBQ,OAAOG,YAAA,MAAAE,SAAAhB,EAAAjN,WAA6C6N,OAAQnS,MAAA8S,EAAAlJ,IAAA,KAAAT,SAAA,SAAAqJ,GAAgDjB,EAAAkB,KAAAK,EAAAlJ,IAAA,OAAA4I,IAAiCV,WAAA,mBAA8BP,EAAA0B,GAAA1B,EAAA,qBAAAlD,GAAyC,OAAAqD,EAAA,aAAuBkB,IAAAvE,EAAArO,MAAAkS,OAAsBnS,MAAAsO,EAAAtO,MAAAC,MAAAqO,EAAArO,WAAyC,UAAUuR,EAAAS,GAAA,KAAAT,EAAAjN,UAAmoBiN,EAAAqC,KAAnoBlC,EAAA,mBAAqDQ,OAAOnS,MAAA,KAAA4T,MAAA,OAA2BjB,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,WAAAA,EAAAlJ,IAAAtH,OAAAoP,EAAA,aAAiDQ,OAAO2B,KAAA,SAAA3O,KAAA,QAA8B6N,IAAKpG,MAAA,SAAAwH,GAAyB,OAAA5C,EAAArH,cAAAqH,EAAA/O,sBAAiD+O,EAAAS,GAAAT,EAAA4B,GAAAL,EAAAlJ,IAAAtH,QAAA,oBAAAiP,EAAAqC,KAAArC,EAAAS,GAAA,SAAAc,EAAAlJ,IAAArH,OAAAmP,EAAA,aAAoHQ,OAAO2B,KAAA,SAAA3O,KAAA,QAA8B6N,IAAKpG,MAAA,SAAAwH,GAAyB,OAAA5C,EAAAjH,cAAAwI,EAAAsB,OAAA7C,EAAA/O,sBAA+D+O,EAAAS,GAAAT,EAAA4B,GAAAL,EAAAlJ,IAAArH,QAAA,oBAAAgP,EAAAqC,SAAiE,uBAAyB,GAAArC,EAAAS,GAAA,KAAAN,EAAA,KAAmCK,YAAA,cAAwBR,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCK,YAAA,iCAA2CL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,KAAAH,EAAAS,GAAA,qBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAkEQ,OAAO2B,KAAA,QAAA3O,KAAA,WAAgC6N,IAAKpG,MAAA4E,EAAAnG,UAAoBmG,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAyCK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,8BAAA2B,aAAuDW,SAAA,cAAuB3C,EAAA,aAAkBK,YAAA,cAAAG,OAAiCmB,OAAA,IAAAG,eAAAjC,EAAA1E,YAAAyG,kBAAA,EAAAf,SAAAhB,EAAAjN,aAA6FiN,EAAA,QAAAG,EAAA,OAA0BK,YAAA,SAAAG,OAA4BuB,IAAAlC,EAAAvN,WAAmB0N,EAAA,KAAUK,YAAA,wCAAgDR,EAAAS,GAAA,KAAAT,EAAA,QAAAG,EAAA,KAAsCK,YAAA,QAAAgB,IAAwBpG,MAAA4E,EAAAxE,SAAmBwE,EAAAS,GAAA,QAAAT,EAAAqC,KAAArC,EAAAS,GAAA,KAAAT,EAAAvN,UAAAuN,EAAAjN,UAAAoN,EAAA,KAA8EK,YAAA,QAAAgB,IAAwBpG,MAAA4E,EAAArE,WAAqBqE,EAAAS,GAAA,QAAAT,EAAAqC,KAAArC,EAAAS,GAAA,KAAAN,EAAA,aAAsDQ,OAAOoC,QAAA/C,EAAApN,eAA4B4O,IAAKwB,iBAAA,SAAAJ,GAAkC5C,EAAApN,cAAAgQ,MAA2BzC,EAAA,OAAYgC,aAAaC,MAAA,QAAezB,OAAQuB,IAAAlC,EAAArN,eAAAsQ,IAAA,MAAmCjD,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCuC,KAAA,UAAgBA,KAAA,WAAe/C,EAAA,aAAkBQ,OAAO2B,KAAA,SAAed,IAAKpG,MAAA,SAAAwH,GAAyB5C,EAAApN,eAAA,MAA4BoN,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkDK,YAAA,0CAAoDL,EAAA,aAAkBK,YAAA,UAAAG,OAA6BwC,MAAA,IAAW3B,IAAKpG,MAAA4E,EAAAJ,eAAyBI,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAT,EAAAjN,UAA8HiN,EAAAqC,KAA9HlC,EAAA,aAA8DK,YAAA,KAAAG,OAAwBhN,KAAA,WAAiB6N,IAAKpG,MAAA4E,EAAA1D,QAAkB0D,EAAAS,GAAA,uBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAqEQ,OAAOyC,MAAA,QAAAC,wBAAA,EAAAN,QAAA/C,EAAAnN,sBAAAuP,MAAA,OAA+FZ,IAAKwB,iBAAA,SAAAJ,GAAkC5C,EAAAnN,sBAAA+P,MAAmCzC,EAAA,OAAYK,YAAA,oBAA8BL,EAAA,SAAcQ,OAAO2C,IAAA,MAAUtD,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,eAAgDO,IAAA,cAAAC,OAAyB4C,QAAAvD,EAAA/R,aAAAX,MAAA0S,EAAAzR,aAAAiV,WAAA,GAAAzC,UAAA,IAAmFS,IAAKiC,OAAAzD,EAAA7B,gBAA4ByC,OAAQnS,MAAAuR,EAAAlS,SAAA,GAAA8J,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAlS,SAAA,KAAAmT,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,SAA0BQ,OAAO2C,IAAA,MAAUtD,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA6CK,YAAA,SAAAG,OAA4BI,UAAA,GAAAD,YAAA,MAAkCF,OAAQnS,MAAAuR,EAAAlS,SAAA,GAAA8J,SAAA,SAAAqJ,GAAiDjB,EAAAkB,KAAAlB,EAAAlS,SAAA,KAAAmT,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,aAA8BK,YAAA,eAAAG,OAAkChN,KAAA,UAAA+P,KAAA,kBAAyClC,IAAKpG,MAAA4E,EAAA9B,YAAsB8B,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CkB,IAAArB,EAAAxS,SAAAgT,YAAA,YAAAG,OAAgDgD,YAAA,MAAAC,WAAA,EAAAC,UAAA7D,EAAA3R,QAAAyV,QAAA9D,EAAAhN,aAAA+Q,qBAAA,EAAAC,aAAAhE,EAAA3M,kBAAA4Q,gBAAA,EAAAC,YAAAlE,EAAA9R,KAAAC,SAAA6R,EAAA7R,SAAAgW,WAAAnE,EAAA1R,OAAoPkT,IAAK4C,oBAAApE,EAAAlC,sBAAAuG,iBAAArE,EAAA/B,mBAAA7F,sBAAA4H,EAAA5H,0BAA6I,GAAA4H,EAAAS,GAAA,KAAAN,EAAA,QAA6BK,YAAA,gBAAAG,OAAmCuC,KAAA,UAAgBA,KAAA,WAAe/C,EAAA,aAAkBK,YAAA,UAAAG,OAA6BhN,KAAA,WAAiB6N,IAAKpG,MAAA,SAAAwH,GAAyB5C,EAAAnN,uBAAA,MAAoCmN,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA8CK,YAAA,KAAAG,OAAwBhN,KAAA,WAAiB6N,IAAKpG,MAAA4E,EAAApB,iBAA2BoB,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCgC,aAAamC,MAAA,WAAgB,UAE94ZC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACE1X,EACA8S,GATF,EAVA,SAAA6E,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/86.61bc75207e0d7ce2f72e.js", "sourcesContent": ["<template>\r\n  <div class=\"sec-container\" v-loading=\"loading\">\r\n    <!-- 标题 -->\r\n    <p class=\"sec-title\">基本信息</p>\r\n    <div class=\"sec-form-container\">\r\n      <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n        <!-- 第一部分包括姓名到常住地公安start -->\r\n        <div class=\"sec-header-section\">\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"姓名\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.xm\" clearable :disabled=\"displayxq\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"曾用名\">\r\n              <!-- <el-input placeholder=\"\" v-model=\"tjlist.xb\" clearable disabled></el-input> -->\r\n              <template slot-scope=\"scope\">\r\n                <el-input placeholder=\"\" v-model=\"tjlist.cym\" clearable :disabled=\"displayxq\"></el-input>\r\n              </template>\r\n            </el-form-item>\r\n            <el-form-item label=\"身份证号\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input placeholder=\"\" v-model=\"tjlist.sfzhm\" clearable @blur=\"sfzhmr\" :disabled=\"displayxq\"></el-input>\r\n              </template>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"性别\" class=\"longLabel\">\r\n              <el-radio-group v-model=\"tjlist.xb\"  disabled>\r\n                <el-radio v-for=\"item in xb\" :v-model=\"tjlist.xb\" :label=\"item.id\" :value=\"item.id\" :key=\"item.id\">\r\n                  {{ item.xb }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"年龄\">\r\n              <el-input type=\"number\" placeholder=\"\" v-model=\"tjlist.nl\" clearable\r\n                oninput=\"value = value.replace(/[^0-9]/g,'' )\" disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"民族\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input placeholder=\"\" v-model=\"tjlist.mz\" clearable :disabled=\"displayxq\"></el-input>\r\n              </template>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"籍贯\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.jg\" clearable :disabled=\"displayxq\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"家庭住址\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.jtdz\" clearable :disabled=\"displayxq\"></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"所在地派出所\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.szdpcs\" clearable :disabled=\"displayxq\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"最高学历\" class=\"longLabel\">\r\n              <el-radio-group v-model=\"tjlist.zgxl\" :disabled=\"displayxq\">\r\n                <el-radio v-for=\"item in zgxl\" :v-model=\"tjlist.zgxl\" :label=\"item.id\" :value=\"item.id\" :key=\"item.id\">\r\n                  {{ item.zgxl }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"工作地点\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.gzdd\" clearable :disabled=\"displayxq\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"工作岗位\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.gwmc\" clearable :disabled=\"displayxq\"></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"联系方式\">\r\n              <el-input placeholder=\"（详细）\" v-model=\"tjlist.lxdh\" clearable :disabled=\"displayxq\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"是否审查\" class=\"longLabel\">\r\n              <el-radio-group v-model=\"tjlist.sfsc\" :disabled=\"displayxq\">\r\n                <el-radio v-for=\"item in sfsc\" :v-model=\"tjlist.sfsc\" :label=\"item.id\" :value=\"item.id\" :key=\"item.id\">\r\n                  {{ item.sfsc }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </div>\r\n          <!-- 电子照片 -->\r\n          <div class=\"sec-header-pic\">\r\n            <div>\r\n              <el-upload class=\"avatar-uploader\" action=\"#\" :show-file-list=\"false\" :before-upload=\"beforeAvatarUpload\"\r\n                :http-request=\"httpRequestimg\" :disabled=\"displayxq\">\r\n                <img v-if=\"imageUrl\" :src=\"imageUrl\" class=\"avatarimg\" style=\"\">\r\n                <i v-else class=\"el-icon-plus avatar-uploader-icon\" style=\"width: 146px;\"></i>\r\n                <el-button type=\"primary\" size=\"small\" v-if=\"!displayxq\">上传头像</el-button>\r\n              </el-upload>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 第一部分包括姓名到常住地公安end -->\r\n        <p class=\"sec-title\">现实表现</p>\r\n        <div class=\"sec-form-third haveBorderTop\">\r\n          <div class=\"sec-left-text\">\r\n            <div>\r\n              政治态度：热爱祖国，热爱社会主义，拥护党的方针、政策:\r\n              <el-radio-group v-model=\"tjlist.zztd\" :disabled=\"displayxq\">\r\n                <el-radio v-for=\"item in zztdlist\" :v-model=\"tjlist.zztd\" :label=\"item.id\" :value=\"item.id\"\r\n                  :key=\"item.id\">{{ item.mc }}</el-radio>\r\n              </el-radio-group>\r\n            </div>\r\n            <div style=\"margin-top: 10px;\">思想状况：\r\n              <el-radio-group v-model=\"tjlist.sxzk\" :disabled=\"displayxq\">\r\n                <el-radio v-for=\"item in sszklist\" :v-model=\"tjlist.sxzk\" :label=\"item.id\" :value=\"item.id\"\r\n                  :key=\"item.id\">{{ item.mc }}</el-radio>\r\n              </el-radio-group>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 家庭成员及主要社会关系情况start -->\r\n        <p class=\"sec-title\">家庭成员及主要社会关系情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscJtcyList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"gxms\" label=\"与本人关系\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.gxms\" placeholder=\"\" :disabled=\"displayxq\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"xm\" label=\"姓名\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.xm\" placeholder=\"\" :disabled=\"displayxq\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"jwjlqk\" label=\"是否有外籍、境外居留权、长期居留许可\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.jwjlqk\" placeholder=\"\"></el-input> -->\r\n              <el-select v-model=\"scope.row.jwjlqk\" placeholder=\"请选择\" :disabled=\"displayxq\">\r\n                <el-option v-for=\"item in ynoptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"cgszd\" label=\"单位\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.cgszd\" placeholder=\"\" :disabled=\"displayxq\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"zw\" label=\"职务\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.zw\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"zzmm\" label=\"政治面貌\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.zzmm\" placeholder=\"请选择\" :disabled=\"displayxq\">\r\n                <el-option v-for=\"item in zzmmoptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"140\" v-if=\"!displayxq\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.czbtn1 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"cyjshgxAddRow(ryglRyscJtcyList)\">{{ scope.row.czbtn1 }}\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.czbtn2 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"cyjshgxDelRow(scope.$index, ryglRyscJtcyList)\">{{ scope.row.czbtn2 }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 家庭成员及主要社会关系情况end -->\r\n        <!-- 下载start -->\r\n        <p class=\"sec-title\">下载</p>\r\n        <div class=\"sec-form-third haveBorderTop\">\r\n          <div class=\"sec-left-text\">\r\n            <p>1.非密重点人员保密承诺书</p>\r\n          </div>\r\n          <el-button size=\"small\" type=\"primary\" @click=\"wdzlxz\">下载</el-button>\r\n        </div>\r\n        <!-- 本人承诺start -->\r\n        <p class=\"sec-title\">本人承诺</p>\r\n        <div class=\"sec-form-five haveBorderTop\" style=\"position: relative;\">\r\n          <el-upload class=\"upload-demo\" action=\"#\" :http-request=\"httpRequest\" :show-file-list=\"false\"\r\n            :disabled=\"displayxq\">\r\n            <img v-if=\"sltshow\" :src=\"sltshow\" class=\"avatar\">\r\n            <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n          </el-upload>\r\n          <p v-if=\"sltshow\" class=\"yulan\" @click=\"yulan\">预览</p>\r\n          <p v-if=\"sltshow && !displayxq\" class=\"yulan\" @click=\"shanchu\">删除</p>\r\n          <!-- 预览本人承诺扫描件 -->\r\n          <el-dialog :visible.sync=\"dialogVisible\">\r\n            <img :src=\"dialogImageUrl\" alt=\"\" style=\"width: 100%\">\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n        <!-- 本人承诺end -->\r\n        <!-- 下载start -->\r\n        <!-- <p class=\"sec-title\">下载</p>\r\n        <div class=\"sec-form-third haveBorderTop\">\r\n          <div class=\"sec-left-text\">\r\n            <p>1.涉密人员保密审查表</p>\r\n            <p>2.保密承诺书</p>\r\n            <p>3.保密协议书</p>\r\n          </div>\r\n          <el-button size=\"small\" type=\"primary\" @click=\"wdzlxz\">下载</el-button>\r\n        </div> -->\r\n        <!-- 下载end -->\r\n        <!-- 底部操作按钮start -->\r\n        <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n          <el-button @click=\"returnIndex\" class=\"fr ml10\" plain>返回</el-button>\r\n          <el-button @click=\"save\" class=\"fr\" type=\"success\" v-if=\"!displayxq\">保存并提交</el-button>\r\n        </div>\r\n        <!-- 底部操作按钮end -->\r\n\r\n      </el-form>\r\n    </div>\r\n    <!-- 发起申请弹框start -->\r\n    <el-dialog title=\"选择审批人\" :close-on-click-modal=\"false\" :visible.sync=\"approvalDialogVisible\" width=\"40%\">\r\n      <div class=\"dlFqsqContainer\">\r\n        <label for=\"\">部门:</label>\r\n        <el-cascader v-model=\"ryChoose.bm\" :options=\"regionOption\" :props=\"regionParams\" filterable clearable\r\n          ref=\"cascaderArr\" @change=\"bmSelectChange\"></el-cascader>\r\n        <label for=\"\">姓名:</label>\r\n        <el-input class=\"input2\" v-model=\"ryChoose.xm\" clearable placeholder=\"姓名\"></el-input>\r\n        <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\" @click=\"searchRy\">查询</el-button>\r\n        <BaseTable class=\"baseTable\" :tableHeight=\"'300'\" :key=\"tableKey\" :showIndex=true :tableData=\"ryDatas\" :columns=\"applyColumns\"\r\n          :showSingleSelection=\"true\" :handleColumn=\"handleColumnApply\" :showPagination=true :currentPage=\"page\"\r\n          :pageSize=\"pageSize\" :totalCount=\"total\" @handleCurrentChange=\"handleCurrentChangeRy\"\r\n          @handleSizeChange=\"handleSizeChangeRy\" @handleSelectionChange=\"handleSelectionChange\">\r\n        </BaseTable>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"warning\" class=\"fr ml10\" @click=\"approvalDialogVisible = false\">关 闭</el-button>\r\n        <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">提交</el-button>\r\n        <div style=\"clear:both\"></div>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 发起申请弹框end -->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  submitRyrysc,\r\n  getLcSLid,\r\n  updateRysc,\r\n  updateSlzt,\r\n  getZzjgList,\r\n  getSpUserList,\r\n  getFwdyidByFwlx,\r\n  downloadRyscwdZip,\r\n  deleteSlxxBySlid,\r\n  getLoginInfo,\r\n  downloadFmzdryCns\r\n} from '../../../../api/index'\r\nimport {\r\n  addZdryJtcy,\r\n  addZdry,\r\n  selectZdryJtcy,\r\n  selectZdryByRwid,\r\n  updateZdry,\r\n  deleteZdryJtcy\r\n} from '../../../../api/fmzdrydj'\r\nimport BaseTable from '../../../components/common/baseTable.vue'\r\nimport AddLineTable from \"../../../components/common/addLineTable.vue\";   //人工纠错组件\r\nimport {\r\n  zp\r\n} from '../../../../utils/zpcl'\r\nexport default {\r\n  components: {\r\n    AddLineTable,\r\n    BaseTable\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      tableKey:1,\r\n      xb: [{\r\n        xb: '男',\r\n        id: 1\r\n      },\r\n      {\r\n        xb: '女',\r\n        id: 2\r\n      },\r\n      ],\r\n      sfsc: [{\r\n        id: 1,\r\n        sfsc: '是'\r\n      },\r\n      {\r\n        id: 0,\r\n        sfsc: '否'\r\n      },\r\n      ],\r\n      zgxl: [\r\n        {\r\n          id: 1,\r\n          zgxl: '研究生'\r\n        },\r\n        {\r\n          id: 2,\r\n          zgxl: '大学本科'\r\n        },\r\n        {\r\n          id: 3,\r\n          zgxl: '大学专科及以下'\r\n        },\r\n      ],\r\n      loading: false,\r\n      // 弹框人员选择条件\r\n      ryChoose: {\r\n        'bm': '',\r\n        'xm': ''\r\n      },\r\n      regionOption: [], // 部门下拉\r\n      page: 1, // 审批人弹框当前页\r\n      pageSize: 10, // 审批人弹框每页条数\r\n      radioIdSelect: '', // 审批人弹框人员单选\r\n      ryDatas: [], // 弹框人员选择\r\n      total: 0, // 弹框人员总数\r\n      regionParams: {\r\n        label: 'label', //这里可以配置你们后端返回的属性\r\n        value: 'label',\r\n        children: 'childrenRegionVo',\r\n        expandTrigger: 'click',\r\n        checkStrictly: true\r\n      }, //地域信息配置参数\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: '#EEF7FF',\r\n        color: '#4D91F8'\r\n      },\r\n      // form表单提交数据\r\n      tjlist: {\r\n        smryid: '',\r\n        xm: '',\r\n        xb: '',\r\n        gj: '中国',\r\n        dwzwzc: '',\r\n        yrsmgw: '',\r\n        cym: '',\r\n        mz: '',\r\n        hyzk: '',\r\n        zzmm: '',\r\n        lxdh: '',\r\n        sfzhm: '',\r\n        hjdz: '',\r\n        hjdgajg: '',\r\n        czdz: '',\r\n        czgajg: '',\r\n        imageUrl: '',\r\n        yjqk: '0', // 拥有外籍、境外永久居留权或者长期居留许可情况\r\n        qscfqk: '0', // 配偶子女有关情况\r\n        qtqk: '', // 其他需要说明的情况\r\n        brcn: '',\r\n        zztd: '',\r\n        sszk: '',\r\n      },\r\n      imageUrl: '',\r\n      zztdlist: [\r\n        {\r\n          id: 1,\r\n          mc: '端正'\r\n        },\r\n        {\r\n          id: 2,\r\n          mc: '不端正'\r\n        },\r\n      ],\r\n      sszklist: [\r\n        {\r\n          id: 1,\r\n          mc: '稳定'\r\n        },\r\n        {\r\n          id: 2,\r\n          mc: '基本稳定'\r\n        },\r\n        {\r\n          id: 3,\r\n          mc: '不稳定'\r\n        },\r\n      ],\r\n      // 主要学习及工作经历\r\n      ryglRyscScjlList: [{\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 家庭成员及社会关系\r\n      ryglRyscJtcyList: [{\r\n        \"gxms\": \"\",//关系描述\r\n        \"zzmm\": \"\",//政治面貌\r\n        \"jwjlqk\": '',//是否有外籍、境外居留权、长期居留许可\r\n        \"xm\": \"\",//姓名\r\n        \"cgszd\": \"\",//工作(学习)单位\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 因私出国(境)情况\r\n      ryglRyscYccgList: [{\r\n        \"cggj\": \"\",//出国国家\r\n        \"sy\": \"\",//事由\r\n        \"zzsj\": \"\",//终止时间\r\n        \"qssj\": \"\",//起始时间\r\n        // \"bz\": \"\",//备注\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 接受境外资助情况\r\n      ryglRyscJwzzqkList: [{\r\n        \"zzsj\": \"\",//时间\r\n        \"jgmc\": \"\",//机构名称\r\n        // \"bz\": \"\",//备注\r\n        \"zznr\": \"\",//资助内容\r\n        \"gj\": \"\",//国家\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 处分或者违法犯罪情况\r\n      ryglRyscCfjlList: [{\r\n        \"cfdw\": \"\",//处罚单位\r\n        \"cfsj\": \"\",//处罚时间\r\n        \"cfjg\": \"\",//处罚结果\r\n        \"cfyy\": \"\",//处罚原因\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 持有因公出入境证件情况\r\n      ryglRyscSwzjList: [{\r\n        'zjmc': '护照',\r\n        'fjlb': 1,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '港澳通行证',\r\n        'fjlb': 2,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '台湾通行证',\r\n        'fjlb': 3,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '护照',\r\n        'fjlb': 4,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '港澳通行证',\r\n        'fjlb': 5,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '台湾通行证',\r\n        'fjlb': 6,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }],\r\n      ryInfo: {}, // 当前任用审查的人员信息\r\n      // 政治面貌下拉选项\r\n      zzmmoptions: [{\r\n        value: '中央党员',\r\n        label: '中央党员'\r\n      }, {\r\n        value: '团员',\r\n        label: '团员'\r\n      }, {\r\n        value: '民主党派',\r\n        label: '民主党派'\r\n      }, {\r\n        value: '群众',\r\n        label: '群众'\r\n      }],\r\n      // 是否有外籍、境外居留权、长期居留许可\r\n      ynoptions: [{\r\n        value: 1,\r\n        label: '是'\r\n      }, {\r\n        value: 0,\r\n        label: '否'\r\n      }],\r\n      sltshow: '', // 文档的缩略图显示\r\n      routeType: '', // 当前的类型（编辑或者首次新增）\r\n      dialogImageUrl: '', // 预览本人承诺扫描件展示\r\n      dialogVisible: false, // 预览本人承诺扫描件弹框显隐\r\n      approvalDialogVisible: false, // 选择申请人弹框弹框显隐\r\n      fileRow: '', // 本人承诺凭证的file数据\r\n      displayxq: false,\r\n      // 选择审核人table\r\n      applyColumns: [\r\n        {\r\n          name: '姓名',\r\n          prop: 'xm',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '部门',\r\n          prop: 'bmmc',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '岗位',\r\n          prop: 'gwmc',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        }\r\n      ],\r\n      handleColumnApply: [],\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted() {\r\n    this.routeType = this.$route.query.type\r\n    this.onfwid()\r\n    this.defaultym()\r\n    this.getOrganization()\r\n  },\r\n  methods: {\r\n    sfzhmr() {\r\n      var arrExp = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; //加权因子\r\n      var arrValid = [1, 0, \"X\", 9, 8, 7, 6, 5, 4, 3, 2]; //校验码\r\n      if (/^\\d{17}\\d|x$/i.test(this.tjlist.sfzhm)) {\r\n        var sum = 0,\r\n          idx;\r\n        for (var i = 0; i < this.tjlist.sfzhm.length - 1; i++) {\r\n          // 对前17位数字与权值乘积求和\r\n          sum += parseInt(this.tjlist.sfzhm.substr(i, 1), 10) * arrExp[i];\r\n        }\r\n        // 计算模（固定算法）\r\n        idx = sum % 11;\r\n        // 检验第18为是否与校验码相等\r\n        if (arrValid[idx] == this.tjlist.sfzhm.substr(17, 1).toUpperCase()) {\r\n          if (this.tjlist.sfzhm) {\r\n            var org_birthday = this.tjlist.sfzhm.substring(6, 14);\r\n            var org_gender = this.tjlist.sfzhm.substring(16, 17);\r\n            var sex = org_gender % 2 == 1 ? 1 : 2;\r\n            var birthday =\r\n              org_birthday.substring(0, 4) +\r\n              \"-\" +\r\n              org_birthday.substring(4, 6) +\r\n              \"-\" +\r\n              org_birthday.substring(6, 8);\r\n            var birthdays = new Date(birthday.replace(/-/g, \"/\"));\r\n            let d = new Date();\r\n            let age =\r\n              d.getFullYear() -\r\n              birthdays.getFullYear() -\r\n              (d.getMonth() < birthdays.getMonth() ||\r\n                (d.getMonth() == birthdays.getMonth() &&\r\n                  d.getDate() < birthdays.getDate()) ?\r\n                1 :\r\n                0);\r\n            this.tjlist.xb = sex;\r\n            // this.form.birthday = birthdays;\r\n            this.tjlist.nl = age;\r\n          }\r\n        } else {\r\n          this.$message.error(\"身份证格式有误\");\r\n          return true\r\n        }\r\n      } else {\r\n        this.$message.error(\"身份证格式有误\");\r\n        return true\r\n      }\r\n    },\r\n    async defaultym() {\r\n      if (this.$route.query.type == 'add') {\r\n\r\n      } else {\r\n        if (this.$route.query.xqpd == 1) {\r\n          this.displayxq = true\r\n        }\r\n        let rwid = this.$route.query.rwid\r\n        let ryxx = await selectZdryByRwid({ rwid: rwid })\r\n        this.tjlist = ryxx\r\n        let jtcylist = await selectZdryJtcy({ rwid: rwid })\r\n        if (jtcylist.length == 0) {\r\n          this.ryglRyscScjlList = [{\r\n            'qssj': '',\r\n            'zzsj': '',\r\n            'szdw': '',\r\n            'zw': '',\r\n            'zmr': '',\r\n            'czbtn1': '增加行',\r\n            'czbtn2': ''\r\n          }]\r\n        } else {\r\n          this.ryglRyscJtcyList = jtcylist.map((data, index) => {\r\n            if (index == 0) {\r\n              data.czbtn1 = '增加行'\r\n              data.czbtn2 = ''\r\n            } else {\r\n              data.czbtn1 = '增加行'\r\n              data.czbtn2 = '删除'\r\n            }\r\n            return data\r\n          })\r\n        }\r\n        if (ryxx.zp != '' && ryxx.zp != undefined) {\r\n          this.imageUrl = zp(ryxx.zp)\r\n        }\r\n        if (ryxx.brcn != '' && ryxx.brcn != undefined) {\r\n          this.sltshow = zp(ryxx.brcn)\r\n        }\r\n      }\r\n    },\r\n    // 不用action\r\n    httpRequestimg(data) {\r\n      this.imageUrl = URL.createObjectURL(data.file);\r\n      this.fileimg = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.zp = dataurl.split(',')[1]\r\n      });\r\n    },\r\n    beforeAvatarUpload(file) {\r\n      const isJPG = file.type === 'image/jpeg';\r\n      const isPNG = file.type === 'image/png';\r\n      if (!isJPG && !isPNG) {\r\n        this.$message.error('上传缩略图只能是 JPG/PNG 格式!');\r\n      }\r\n      return isJPG || isPNG;\r\n    },\r\n    // blob格式转base64\r\n    blobToBase64(blob, callback) {\r\n      const fileReader = new FileReader();\r\n      fileReader.onload = (e) => {\r\n        callback(e.target.result);\r\n      };\r\n      fileReader.readAsDataURL(blob);\r\n    },\r\n    handleSelectionChange(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    // 主要学习及工作经历增加行\r\n    addRow(data) {\r\n      data.push({\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除',\r\n      })\r\n    },\r\n    // 主要学习及工作经历删除行\r\n    delRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 家庭成员及主要社会关系情况 添加行\r\n    cyjshgxAddRow(data) {\r\n      data.push({\r\n        'ybrgx': '',\r\n        'xm': '',\r\n        'sfywjjwjlqcqjlxk': '',\r\n        'dw': '',\r\n        'zw': '',\r\n        'zzmm': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 家庭成员及主要社会关系情况 删除行\r\n    cyjshgxDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 因私出国(境)情况 添加行\r\n    yscgqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'zzrq': '',\r\n        'jsnsdgjhdq': '',\r\n        'sy': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 因私出国(境)情况 删除行\r\n    yscgqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 接受境外资助情况 添加行\r\n    jsjwzzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'gjdq': '',\r\n        'jgmc': '',\r\n        'zznr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 接受境外资助情况 删除行\r\n    jsjwzzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 处分或者违法犯罪情况 添加行\r\n    clhwffzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'cljg': '',\r\n        'clyy': '',\r\n        'cljg': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    async wdzlxz() {\r\n      var returnData = await downloadFmzdryCns();\r\n      var date = new Date()\r\n      var sj = date.getFullYear() + \"\" + (date.getMonth() + 1) + \"\" + date.getDate()\r\n      this.dom_download(returnData, '非密重点人员' + '-' + sj + \".doc\");\r\n    },\r\n    //处理下载流\r\n    dom_download(content, fileName) {\r\n      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象\r\n      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象\r\n      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download\r\n      dom.style.display = 'none'\r\n      dom.href = url\r\n      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件\r\n      document.body.appendChild(dom)\r\n      dom.click()\r\n    },\r\n    // 处分或者违法犯罪情况 删除行\r\n    clhwffzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 上传本人承诺凭证\r\n    httpRequest(data) {\r\n      this.sltshow = URL.createObjectURL(data.file);\r\n      this.fileRow = data.file\r\n      // this.tjlist.brcn = URL.createObjectURL(this.fileRow)\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.brcn = dataurl.split(',')[1]\r\n      });\r\n    },\r\n    // 预览\r\n    yulan() {\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogImageUrl = URL.createObjectURL(this.fileRow)\r\n      } else {\r\n        this.dialogImageUrl = this.sltshow\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除\r\n    shanchu() {\r\n      this.tjlist.brcn = ''\r\n      this.sltshow = ''\r\n    },\r\n    async onfwid() {\r\n      let params = {\r\n        fwlx: 1\r\n      }\r\n      let data = await getFwdyidByFwlx(params)\r\n      console.log(data);\r\n      this.fwdyid = data.data.fwdyid\r\n    },\r\n    jyxx() {\r\n      if (this.tjlist.xm == '' || this.tjlist.xm == undefined) {\r\n        this.$message.error('请输入姓名')\r\n        return true\r\n      }\r\n      if (this.tjlist.sfzhm == '' || this.tjlist.sfzhm == undefined) {\r\n        this.$message.error('请输入身份证号')\r\n        return true\r\n      }\r\n      if (this.tjlist.xb == '' || this.tjlist.xb == undefined) {\r\n        this.$message.error('请选择性别')\r\n        return true\r\n      }\r\n      if (this.tjlist.nl == '' || this.tjlist.nl == undefined) {\r\n        this.$message.error('请输入年龄')\r\n        return true\r\n      }\r\n      if (this.tjlist.zgxl == '' || this.tjlist.zgxl == undefined) {\r\n        this.$message.error('请选择最高学历')\r\n        return true\r\n      }\r\n      if (this.tjlist.gzdd == 0 || this.tjlist.gzdd == undefined) {\r\n        this.$message.error('请输入工作地点')\r\n        return true\r\n      }\r\n      if (this.tjlist.gwmc == '' || this.tjlist.gwmc == undefined) {\r\n        this.$message.error('请输入工作岗位')\r\n        return true\r\n      }\r\n      if (this.tjlist.zztd == 0 || this.tjlist.zztd == undefined) {\r\n        this.$message.error('请选择政治态度')\r\n        return true\r\n      }\r\n      if (this.tjlist.sxzk == '' || this.tjlist.sxzk == undefined) {\r\n        this.$message.error('请输入思想状况')\r\n        return true\r\n      }\r\n\r\n    },\r\n    // 保存\r\n    async save() {\r\n      if (this.sfzhmr()) {\r\n        return\r\n      }\r\n      if (this.jyxx()) {\r\n        return\r\n      }\r\n      if (this.routeType == 'update') {\r\n        let fmzdrydata = await updateZdry(this.tjlist)\r\n        if (fmzdrydata.code == 10000) {\r\n          deleteZdryJtcy({ rwid: this.tjlist.rwid })\r\n          this.ryglRyscJtcyList.forEach(item => {\r\n            item.rwid = this.tjlist.rwid\r\n          })\r\n          let zrcyzlist = await addZdryJtcy(this.ryglRyscJtcyList)\r\n          // if (zrcyzlist.code == 10000) {\r\n          this.$router.push('/fmzdry')\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          // } else {\r\n          //   this.$message.error('数据添加错误')\r\n          // }\r\n\r\n        } else {\r\n          this.$message.error('数据添加错误')\r\n        }\r\n\r\n\r\n        this.$router.push('/fmzdry')\r\n        this.$message({\r\n          message: '保存成功',\r\n          type: 'success'\r\n        })\r\n\r\n      } else {\r\n        let fmzdrydata = await addZdry(this.tjlist)\r\n        if (fmzdrydata.code == 10000) {\r\n          this.ryglRyscJtcyList.forEach(item => {\r\n            item.rwid = fmzdrydata.data\r\n          })\r\n          let zrcyzlist = await addZdryJtcy(this.ryglRyscJtcyList)\r\n          // if (zrcyzlist.code == 10000) {\r\n          this.$router.push('/fmzdry')\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          // } else {\r\n          //   this.$message.error('数据添加错误')\r\n          // }\r\n\r\n        } else {\r\n          this.$message.error('数据添加错误')\r\n        }\r\n        console.log(this.tjlist);\r\n\r\n      }\r\n    },\r\n    //全部组织机构List\r\n    async getOrganization() {\r\n      let zzjgList = await getZzjgList()\r\n      this.zzjgmc = zzjgList\r\n      let shu = []\r\n      this.zzjgmc.forEach(item => {\r\n        let childrenRegionVo = []\r\n        this.zzjgmc.forEach(item1 => {\r\n          if (item.bmm == item1.fbmm) {\r\n            childrenRegionVo.push(item1)\r\n            item.childrenRegionVo = childrenRegionVo\r\n          }\r\n        });\r\n        shu.push(item)\r\n      })\r\n      let shuList = []\r\n      let list = await getLoginInfo()\r\n      if (list.fbmm == '') {\r\n        shu.forEach(item => {\r\n          if (item.fbmm == '') {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      if (list.fbmm != '') {\r\n        shu.forEach(item => {\r\n          console.log(item);\r\n          if (item.fbmm == list.fbmm) {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      shuList[0].childrenRegionVo.forEach(item => {\r\n        this.regionOption.push(item)\r\n      })\r\n    },\r\n    handleSelectionChange1(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    handleCurrentChangeRy(val) {\r\n      this.page = val\r\n      this.chooseApproval()\r\n    },\r\n    //列表分页--更改每页显示个数\r\n    handleSizeChangeRy(val) {\r\n      this.page = 1\r\n      this.pageSize = val\r\n      this.chooseApproval()\r\n    },\r\n    // 人员搜索\r\n    searchRy() {\r\n      this.tableKey++\r\n      this.chooseApproval()\r\n    },\r\n    // 发起申请选择人员 人员下拉\r\n    bmSelectChange(item) {\r\n      if (item != undefined) {\r\n        this.ryChoose.bm = item.join('/')\r\n      }\r\n    },\r\n    // 选择审批人\r\n    async chooseApproval() {\r\n     // this.getOrganization()\r\n      this.approvalDialogVisible = true\r\n      let param = {\r\n        'page': this.page,\r\n        'pageSize': this.pageSize,\r\n        'fwdyid': this.fwdyid,\r\n        'bmmc': this.ryChoose.bm,\r\n        'xm': this.ryChoose.xm\r\n      }\r\n      let resData = await getSpUserList(param)\r\n      if (resData.records) {\r\n        // this.loading = false\r\n        this.ryDatas = resData.records\r\n        this.total = resData.total\r\n      } else {\r\n        this.$message.error('数据获取失败！')\r\n      }\r\n    },\r\n    // 保存并提交\r\n    async saveAndSubmit() {\r\n      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {\r\n        let param = {\r\n          'fwdyid': this.fwdyid\r\n        }\r\n        this.ryglRyscJtcyList.forEach((e) => {\r\n          if (e.jwjlqk == '否') {\r\n            e.jwjlqk = 0\r\n          } else if (e.jwjlqk == '是') {\r\n            e.jwjlqk = 1\r\n          }\r\n        })\r\n        if (this.routeType == 'update') {\r\n          param.lcslclzt = 2\r\n          param.smryid = this.ryInfo.smryid\r\n          param.slid = this.ryInfo.lcslid\r\n          param.clrid = this.radioIdSelect.yhid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = this.ryInfo.dwid\r\n            this.tjlist.lcslid = this.ryInfo.lcslid\r\n            let params = {\r\n              'rysc': this.tjlist,\r\n              'ryglRyscScjlList': this.ryglRyscScjlList,\r\n              'ryglRyscJtcyList': this.ryglRyscJtcyList,\r\n              'ryglRyscYccgList': this.ryglRyscYccgList,\r\n              'ryglRyscJwzzqkList': this.ryglRyscJwzzqkList,\r\n              'ryglRyscCfjlList': this.ryglRyscCfjlList,\r\n              'ryglRyscSwzjList': this.ryglRyscSwzjList\r\n            }\r\n            let resDatas = await updateRysc(params)\r\n            if (resDatas.code == 10000) {\r\n              let paramStatus = {\r\n                'fwdyid': this.fwdyid,\r\n                'slid': this.tjlist.lcslid\r\n              }\r\n              let resStatus = await updateSlzt(paramStatus)\r\n              if (resStatus.code == 10000) {\r\n                this.$router.push('/rysc')\r\n                this.$message({\r\n                  message: '保存并提交成功',\r\n                  type: 'success'\r\n                })\r\n              }\r\n            }\r\n          }\r\n        } else {\r\n          param.lcslclzt = 0\r\n          param.clrid = this.radioIdSelect.yhid\r\n          param.smryid = this.yhDatas.smryid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = this.yhDatas.dwid\r\n            this.tjlist.lcslid = res.data.slid\r\n            let params = {\r\n              'rysc': this.tjlist,\r\n              'ryglRyscScjlList': this.ryglRyscScjlList,\r\n              'ryglRyscJtcyList': this.ryglRyscJtcyList,\r\n              'ryglRyscYccgList': this.ryglRyscYccgList,\r\n              'ryglRyscJwzzqkList': this.ryglRyscJwzzqkList,\r\n              'ryglRyscCfjlList': this.ryglRyscCfjlList,\r\n              'ryglRyscSwzjList': this.ryglRyscSwzjList\r\n            }\r\n            let resDatas = await submitRyrysc(params)\r\n            if (resDatas.code == 10000) {\r\n              this.$router.push('/rysc')\r\n              this.$message({\r\n                message: '保存并提交成功',\r\n                type: 'success'\r\n              })\r\n            } else {\r\n              deleteSlxxBySlid({ slid: res.data.slid })\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: '请选择审批人',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 返回\r\n    returnIndex() {\r\n      this.$router.push('/fmzdry')\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.sec-container {\r\n  width: 100%;\r\n  height: calc(100% - 50px);\r\n  overflow-y: overlay;\r\n}\r\n\r\n.sec-container>>>.el-input.is-disabled .el-input__inner {\r\n  color: #000000;\r\n}\r\n\r\n.sec-title {\r\n  border-left: 5px solid #1b72d8;\r\n  color: #1b72d8;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  text-indent: 10px;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n  width: calc(100% - 260px);\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n  width: 258px;\r\n  position: absolute;\r\n  right: 0px;\r\n  top: 0;\r\n  height: 245px;\r\n  border: 1px solid #CDD2D9;\r\n  border-left: 0;\r\n  background: #ffffff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.sec-form-second {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-third {\r\n  border: 1px solid #CDD2D9;\r\n  /* height: 40px;  */\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-four {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  overflow: hidden;\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.yulan {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  color: #3874D5;\r\n  font-weight: 600;\r\n  float: left;\r\n  margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n  width: 100px;\r\n  height: 100px;\r\n  display: block;\r\n}\r\n\r\n.avatarimg {\r\n  width: 150px;\r\n  height: 180px;\r\n  display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 100px;\r\n  height: 100px;\r\n  line-height: 100px;\r\n  text-align: center;\r\n  border: 2px solid #EBEBEB;\r\n}\r\n\r\n>>>.avatar-uploader .el-upload {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 150px;\r\n  height: 180px;\r\n}\r\n\r\n\r\n.sec-form-six {\r\n  border: 1px solid #CDD2D9;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.ml10 {\r\n  margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n  margin-top: 10px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n  border: none;\r\n}\r\n\r\n.sec-left-text {\r\n  float: left;\r\n  margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n  border-top: 1px solid #CDD2D9;\r\n}\r\n\r\n>>>.longLabel .el-form-item__label {\r\n  width: 225px !important;\r\n}\r\n\r\n>>>.longLabel .el-form-item__content {\r\n  margin-left: 225px !important;\r\n  padding-left: 20px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n  border-top: 0;\r\n} */\r\n.sec-form-second .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n  width: 100%;\r\n  border: 1px solid #EBEEF5;\r\n  height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n.hyzk {\r\n  padding-left: 15px;\r\n  background-color: #F5F7FA;\r\n  width: calc(100% - 16px);\r\n  border-right: 1px solid #CDD2D9;\r\n  color: #000000;\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n  border: none !important;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n  width: 200px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n  border: none;\r\n  border-right: 1px solid #CDD2D9;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n  border: 1px solid #CDD2D9;;\r\n} */\r\n>>>.el-form-item__label {\r\n  border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n/* /deep/.sec-form-container .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n} */\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/rycs/fmzdryscdjTable.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"sec-container\"},[_c('p',{staticClass:\"sec-title\"},[_vm._v(\"基本信息\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"姓名\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.xm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xm\", $$v)},expression:\"tjlist.xm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"曾用名\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.cym),callback:function ($$v) {_vm.$set(_vm.tjlist, \"cym\", $$v)},expression:\"tjlist.cym\"}})]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"身份证号\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},on:{\"blur\":_vm.sfzhmr},model:{value:(_vm.tjlist.sfzhm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sfzhm\", $$v)},expression:\"tjlist.sfzhm\"}})]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{staticClass:\"longLabel\",attrs:{\"label\":\"性别\"}},[_c('el-radio-group',{attrs:{\"disabled\":\"\"},model:{value:(_vm.tjlist.xb),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xb\", $$v)},expression:\"tjlist.xb\"}},_vm._l((_vm.xb),function(item){return _c('el-radio',{key:item.id,attrs:{\"v-model\":_vm.tjlist.xb,\"label\":item.id,\"value\":item.id}},[_vm._v(\"\\n                \"+_vm._s(item.xb))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"年龄\"}},[_c('el-input',{attrs:{\"type\":\"number\",\"placeholder\":\"\",\"clearable\":\"\",\"oninput\":\"value = value.replace(/[^0-9]/g,'' )\",\"disabled\":\"\"},model:{value:(_vm.tjlist.nl),callback:function ($$v) {_vm.$set(_vm.tjlist, \"nl\", $$v)},expression:\"tjlist.nl\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"民族\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.mz),callback:function ($$v) {_vm.$set(_vm.tjlist, \"mz\", $$v)},expression:\"tjlist.mz\"}})]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"籍贯\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.jg),callback:function ($$v) {_vm.$set(_vm.tjlist, \"jg\", $$v)},expression:\"tjlist.jg\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"家庭住址\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.jtdz),callback:function ($$v) {_vm.$set(_vm.tjlist, \"jtdz\", $$v)},expression:\"tjlist.jtdz\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"所在地派出所\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.szdpcs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"szdpcs\", $$v)},expression:\"tjlist.szdpcs\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"longLabel\",attrs:{\"label\":\"最高学历\"}},[_c('el-radio-group',{attrs:{\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.zgxl),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zgxl\", $$v)},expression:\"tjlist.zgxl\"}},_vm._l((_vm.zgxl),function(item){return _c('el-radio',{key:item.id,attrs:{\"v-model\":_vm.tjlist.zgxl,\"label\":item.id,\"value\":item.id}},[_vm._v(\"\\n                \"+_vm._s(item.zgxl))])}),1)],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"工作地点\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.gzdd),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gzdd\", $$v)},expression:\"tjlist.gzdd\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"工作岗位\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.gwmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gwmc\", $$v)},expression:\"tjlist.gwmc\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"联系方式\"}},[_c('el-input',{attrs:{\"placeholder\":\"（详细）\",\"clearable\":\"\",\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.lxdh),callback:function ($$v) {_vm.$set(_vm.tjlist, \"lxdh\", $$v)},expression:\"tjlist.lxdh\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"longLabel\",attrs:{\"label\":\"是否审查\"}},[_c('el-radio-group',{attrs:{\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.sfsc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sfsc\", $$v)},expression:\"tjlist.sfsc\"}},_vm._l((_vm.sfsc),function(item){return _c('el-radio',{key:item.id,attrs:{\"v-model\":_vm.tjlist.sfsc,\"label\":item.id,\"value\":item.id}},[_vm._v(\"\\n                \"+_vm._s(item.sfsc))])}),1)],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-header-pic\"},[_c('div',[_c('el-upload',{staticClass:\"avatar-uploader\",attrs:{\"action\":\"#\",\"show-file-list\":false,\"before-upload\":_vm.beforeAvatarUpload,\"http-request\":_vm.httpRequestimg,\"disabled\":_vm.displayxq}},[(_vm.imageUrl)?_c('img',{staticClass:\"avatarimg\",attrs:{\"src\":_vm.imageUrl}}):_c('i',{staticClass:\"el-icon-plus avatar-uploader-icon\",staticStyle:{\"width\":\"146px\"}}),_vm._v(\" \"),(!_vm.displayxq)?_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(\"上传头像\")]):_vm._e()],1)],1)])]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"现实表现\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-third haveBorderTop\"},[_c('div',{staticClass:\"sec-left-text\"},[_c('div',[_vm._v(\"\\n            政治态度：热爱祖国，热爱社会主义，拥护党的方针、政策:\\n            \"),_c('el-radio-group',{attrs:{\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.zztd),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zztd\", $$v)},expression:\"tjlist.zztd\"}},_vm._l((_vm.zztdlist),function(item){return _c('el-radio',{key:item.id,attrs:{\"v-model\":_vm.tjlist.zztd,\"label\":item.id,\"value\":item.id}},[_vm._v(_vm._s(item.mc))])}),1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"margin-top\":\"10px\"}},[_vm._v(\"思想状况：\\n            \"),_c('el-radio-group',{attrs:{\"disabled\":_vm.displayxq},model:{value:(_vm.tjlist.sxzk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sxzk\", $$v)},expression:\"tjlist.sxzk\"}},_vm._l((_vm.sszklist),function(item){return _c('el-radio',{key:item.id,attrs:{\"v-model\":_vm.tjlist.sxzk,\"label\":item.id,\"value\":item.id}},[_vm._v(_vm._s(item.mc))])}),1)],1)])]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"家庭成员及主要社会关系情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscJtcyList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gxms\",\"label\":\"与本人关系\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":_vm.displayxq},model:{value:(scope.row.gxms),callback:function ($$v) {_vm.$set(scope.row, \"gxms\", $$v)},expression:\"scope.row.gxms\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xm\",\"label\":\"姓名\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":_vm.displayxq},model:{value:(scope.row.xm),callback:function ($$v) {_vm.$set(scope.row, \"xm\", $$v)},expression:\"scope.row.xm\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"jwjlqk\",\"label\":\"是否有外籍、境外居留权、长期居留许可\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"disabled\":_vm.displayxq},model:{value:(scope.row.jwjlqk),callback:function ($$v) {_vm.$set(scope.row, \"jwjlqk\", $$v)},expression:\"scope.row.jwjlqk\"}},_vm._l((_vm.ynoptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cgszd\",\"label\":\"单位\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":_vm.displayxq},model:{value:(scope.row.cgszd),callback:function ($$v) {_vm.$set(scope.row, \"cgszd\", $$v)},expression:\"scope.row.cgszd\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzmm\",\"label\":\"政治面貌\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-select',{attrs:{\"placeholder\":\"请选择\",\"disabled\":_vm.displayxq},model:{value:(scope.row.zzmm),callback:function ($$v) {_vm.$set(scope.row, \"zzmm\", $$v)},expression:\"scope.row.zzmm\"}},_vm._l((_vm.zzmmoptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)]}}])}),_vm._v(\" \"),(!_vm.displayxq)?_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"140\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.czbtn1 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.cyjshgxAddRow(_vm.ryglRyscJtcyList)}}},[_vm._v(_vm._s(scope.row.czbtn1)+\"\\n            \")]):_vm._e(),_vm._v(\" \"),(scope.row.czbtn2 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.cyjshgxDelRow(scope.$index, _vm.ryglRyscJtcyList)}}},[_vm._v(_vm._s(scope.row.czbtn2)+\"\\n            \")]):_vm._e()]}}],null,false,2177127118)}):_vm._e()],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"下载\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-third haveBorderTop\"},[_c('div',{staticClass:\"sec-left-text\"},[_c('p',[_vm._v(\"1.非密重点人员保密承诺书\")])]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":_vm.wdzlxz}},[_vm._v(\"下载\")])],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"本人承诺\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-five haveBorderTop\",staticStyle:{\"position\":\"relative\"}},[_c('el-upload',{staticClass:\"upload-demo\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpRequest,\"show-file-list\":false,\"disabled\":_vm.displayxq}},[(_vm.sltshow)?_c('img',{staticClass:\"avatar\",attrs:{\"src\":_vm.sltshow}}):_c('i',{staticClass:\"el-icon-plus avatar-uploader-icon\"})]),_vm._v(\" \"),(_vm.sltshow)?_c('p',{staticClass:\"yulan\",on:{\"click\":_vm.yulan}},[_vm._v(\"预览\")]):_vm._e(),_vm._v(\" \"),(_vm.sltshow && !_vm.displayxq)?_c('p',{staticClass:\"yulan\",on:{\"click\":_vm.shanchu}},[_vm._v(\"删除\")]):_vm._e(),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogVisible},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"plain\":\"\"},on:{\"click\":_vm.returnIndex}},[_vm._v(\"返回\")]),_vm._v(\" \"),(!_vm.displayxq)?_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.save}},[_vm._v(\"保存并提交\")]):_vm._e()],1)],1)],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"选择审批人\",\"close-on-click-modal\":false,\"visible\":_vm.approvalDialogVisible,\"width\":\"40%\"},on:{\"update:visible\":function($event){_vm.approvalDialogVisible=$event}}},[_c('div',{staticClass:\"dlFqsqContainer\"},[_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"部门:\")]),_vm._v(\" \"),_c('el-cascader',{ref:\"cascaderArr\",attrs:{\"options\":_vm.regionOption,\"props\":_vm.regionParams,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.bmSelectChange},model:{value:(_vm.ryChoose.bm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"bm\", $$v)},expression:\"ryChoose.bm\"}}),_vm._v(\" \"),_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"姓名:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"姓名\"},model:{value:(_vm.ryChoose.xm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"xm\", $$v)},expression:\"ryChoose.xm\"}}),_vm._v(\" \"),_c('el-button',{staticClass:\"searchButton\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchRy}},[_vm._v(\"查询\")]),_vm._v(\" \"),_c('BaseTable',{key:_vm.tableKey,staticClass:\"baseTable\",attrs:{\"tableHeight\":'300',\"showIndex\":true,\"tableData\":_vm.ryDatas,\"columns\":_vm.applyColumns,\"showSingleSelection\":true,\"handleColumn\":_vm.handleColumnApply,\"showPagination\":true,\"currentPage\":_vm.page,\"pageSize\":_vm.pageSize,\"totalCount\":_vm.total},on:{\"handleCurrentChange\":_vm.handleCurrentChangeRy,\"handleSizeChange\":_vm.handleSizeChangeRy,\"handleSelectionChange\":_vm.handleSelectionChange}})],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.approvalDialogVisible = false}}},[_vm._v(\"关 闭\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.saveAndSubmit}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-92fc80a0\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/rycs/fmzdryscdjTable.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-92fc80a0\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./fmzdryscdjTable.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./fmzdryscdjTable.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./fmzdryscdjTable.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-92fc80a0\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./fmzdryscdjTable.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-92fc80a0\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/rycs/fmzdryscdjTable.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}