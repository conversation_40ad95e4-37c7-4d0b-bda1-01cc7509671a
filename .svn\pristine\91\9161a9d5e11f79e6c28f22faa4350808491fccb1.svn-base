<template>
  <div>
    <div class="bg_con" style="height: calc(100% - 38px)">
      <div
        style="width: 100%; position: relative; overflow: hidden; height: 100%"
      >
        <div class="dabg" style="height: 100%">
          <div class="content" style="height: 100%">
            <div class="table" style="height: 100%">
              <!-- 使用vue-barcode组件生成条形码 -->
              <vue-barcode
                :value="barcodeValue"
                :options="barcodeOptions"
              ></vue-barcode>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VueBarcode from "vue-barcode";

export default {
  components: {
    VueBarcode,
  },
  data() {
    return {
      barcodeValue: "123456789102", // 条形码的值
      barcodeOptions: {
        format: "CODE128", // 条形码的格式
        displayValue: true, // 是否显示条形码的值
        textAlign: "center", // 文本对齐方式
        fontSize: 20, // 文本字体大小
        width: 2, // 条形码线条宽度
        height: 100, // 条形码高度
      },
    };
  },
};
</script>

<style scoped>
/* 添加一些样式以美化条形码 */
div {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
