<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; height: 100%; position: relative; overflow: hidden; ">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="select_wrap">
              <div class="select_wrap_content">

                <div class="item_button" style="float:right">
                  <el-button type="danger" icon="el-icon-delete-solid" size="medium" @click="shanchu()">删除
                  </el-button>
                </div>

                <div class="item_button" style="float:right">
                  <el-button type="success" icon="el-icon-plus" size="medium" @click="showDialog()">添加
                  </el-button>
                </div>
              </div>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="bmzdList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 38px - 50px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" align="center" label="序号"></el-table-column>
                  <el-table-column prop="sbnf" width="70" align="center" label="年度"></el-table-column>
                  <el-table-column label="文件名">
                    <template slot-scope="scoped">
                      <div>
                        <p @click="downloadFj(scoped.row)" style="color: #409EFF;cursor: pointer;">{{ scoped.row.wjm }}
                        </p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="wh" label="文号"></el-table-column>
                  <el-table-column prop="zdysx" width="120" align="center" label="颁发日期"></el-table-column>
                  <el-table-column prop="ssrq" width="120" align="center" label="实施日期"></el-table-column>
                  <el-table-column prop="bz" label="备注"></el-table-column>
                  <el-table-column prop="" width="100" align="center" label="操作">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- -----------------新增保密制度-弹窗--------------------------- -->

        <el-dialog title="新增保密制度" :close-on-click-modal="false" :visible.sync="dialogVisible" width="35%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="年度" prop="sbnf" class="one-line">
              <el-date-picker v-model="tjlist.sbnf" format="yyyy年" value-format="yyyy年" type="year" placeholder="选择年"
                style="width:100%;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="保密制度">
              <!-- <template> -->
              <!-- <el-button size="mini" type="primary" @click="uploadSC()">
                  上 传
                </el-button> -->
              <el-upload style="height: 40px;padding: 0px;" class="el-button el-button--text el-button--mini"
                action="/posts" :show-file-list="false" :data="{}" :http-request="scwj">
                <el-button slot="trigger" type="primary" size="mini">上 传</el-button>
              </el-upload>
              <el-popover placement="right" width="200" trigger="hover">
                <div>
                  <div style="display:flex;margin-bottom:10px">
                    <i class="el-icon-info" style="color:#409eef;    position: relative; top: 2px;"></i>
                    <div class="tszt">提示</div>
                  </div>
                  <div class="smzt">
                    请上传本单位保密管理工作的相关制度文件，为符合国家保密法律法规和要求规定，保密管理制度需要包含：保密组织机构及职责、保密教育培训管理、涉密人员管理、涉密载体管理、涉密场所管理、计算机和信息系统管理、通信及办公自动化设备管理、宣传报道管理、涉密会议和涉外活动保密管理、保密工作经费管理、保密监督检查管理、保密工作考核与奖惩管理、泄密事件报告与查处管理、保密风险评估管理、保密工作档案管理、持续改进管理等管理规定和要求。
                  </div>
                </div>
                <i class="el-icon-info" style="color:#409eef;position: absolute;left: 75px; top: 20px;"
                  slot="reference"></i>

              </el-popover>
              <!-- <div>{{ tjlist.fj }}</div> -->
              <!-- </template> -->
            </el-form-item>
            <el-form-item label="文件名" prop="wjm" class="one-line">
              <el-input v-model="tjlist.wjm" clearable style="width:100%;"></el-input>
            </el-form-item>
            <el-form-item label="文号" prop="wh" class="one-line">
              <el-input v-model="tjlist.wh" clearable style="width:100%;"></el-input>
            </el-form-item>
            <el-form-item label="颁发日期" prop="bfrq" class="one-line">
              <el-date-picker v-model="tjlist.bfrq" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="实施日期" prop="ssrq" class="one-line">
              <el-date-picker v-model="tjlist.ssrq" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="bz one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz" style="width:100%;"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="qkbdgb">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改保密制度" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="35%" class="xg"
          @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="年度" prop="sbnf" class="one-line">
              <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="sbnf = $event.target.value" placeholder="年度"
                v-model="xglist.sbnf" clearable></el-input>
            </el-form-item>
            <el-form-item label="文件名" prop="wjm" class="one-line">
              <el-input v-model="xglist.wjm" clearable></el-input>
            </el-form-item>
            <el-form-item label="文号" prop="wh" class="one-line">
              <el-input v-model="xglist.wh" clearable></el-input>
            </el-form-item>
            <el-form-item label="颁发日期" prop="zdysx" class="one-line">
              <el-date-picker v-model="xglist.zdysx" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="实施日期" prop="ssrq" class="one-line">
              <el-date-picker v-model="xglist.ssrq" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="bz one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="保密制度详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="35%" class="xg">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini" disabled>
            <el-form-item label="年度" prop="sbnf" class="one-line">
              <el-input oninput="value=value.replace(/[^\d.]/g,'')" @blur="sbnf = $event.target.value" placeholder="年度"
                v-model="xglist.sbnf" clearable></el-input>
            </el-form-item>
            <el-form-item label="文件名" prop="wjm" class="one-line">
              <el-input v-model="xglist.wjm" clearable></el-input>
            </el-form-item>
            <el-form-item label="文号" prop="wh" class="one-line">
              <el-input v-model="xglist.wh" clearable></el-input>
            </el-form-item>
            <el-form-item label="颁发日期" prop="bfrq" class="one-line">
              <el-date-picker v-model="xglist.bfrq" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="实施日期" prop="ssrq" class="one-line">
              <el-date-picker v-model="xglist.ssrq" class="cd" clearable type="date" placeholder="选择日期"
                style="width:100%;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="备注" prop="bz" class="bz one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import { getBmzdList, saveBmzd, removeBatch, removeBmzd, updateBmzd, } from '../../../api/index'
import { downloadDmzdFile, } from '../../../api/scwj'
import { dom_download } from '@/utils/common.js'
import {
  getUuid
} from "../../../utils/getUuid"; //获取uuid
//   import {
//   getFileSavePath,
//   getFileNameByDirectory
// } from "../../../utils/pathUtil"
export default {
  components: {},
  props: {},
  data() {
    return {
      fileList: [],
      bmzdList: [],
      tjlist: {
        sbnf: '',
        wjm: '',
        wh: '',
        bfrq: '',
        ssrq: '',
        bz: '',
        fj: '',
        fromFjPath: ''
      },
      xglist: {},
      updateItemOld: {},
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      xgdialogVisible: false,
      xqdialogVisible: false,
      //表单验证
      rules: {
        sbnf: [{
          required: true,
          message: '请输入年度',
          trigger: 'blur'
        },
          // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        wjm: [{
          required: true,
          message: '请输入文件名',
          trigger: 'blur'
        },],
        wh: [{
          required: true,
          message: '请输入文号',
          trigger: 'blur'
        },],
        bfrq: [{
          required: true,
          message: '请输入颁发日期',
          trigger: 'blur'
        },],
        ssrq: [{
          required: true,
          message: '请输入实施日期',
          trigger: 'blur'
        },],
      },
      file: {},
      filename: '',
    }
  },
  computed: {},
  mounted() {
    this.bmzd()
  },
  methods: {
    /**
     * 下载附件
    */
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom",dom);
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    //导出文件
    async downloadFj(row) {
      console.log(row)
      // console.log('type',type);
      // console.log('filename',filename);
      //window.location.href  = "/hyhd/rw/downloadHyFile?type="+type+"&rwid="+this.rwid+"&filename="+filename
      var param = {
        // type: type,
        zdid: row.zdid,
        // filename: filename
      }
      var returnData = await downloadDmzdFile(param)
      console.log(returnData)
      this.dom_download(returnData, row.wjm)
    },
    // downloadFj(row) {

    // },

    uploadSC() {

    },
    // 文件保存
    saveFj(fromPath, saveToPath) {

    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    async bmzd() {
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      let data = await getBmzdList(params)
      console.log(data);
      this.bmzdList = data.records
      this.total = data.total
    },
    //删除
    shanchu(id) {
      let that = this
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            let params = {
              zdid: item.zdid,
              dwid: item.dwid
            }
            removeBmzd(params).then(() => {
              that.bmzd()
            })
            console.log("删除：", item);
            console.log("删除：", item);
          })
          // let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });

        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {
      this.dialogVisible = true
    },
    //-----------------------------------------上传审查相关附件-------------------------
    //上传文件
    async scwj(item) {
      this.file = item.file
      this.filename = item.file.name
      console.log("上传文件", "this.file", this.file, "this.filename", this.filename);
      this.tjlist.wjm = this.filename
      // let fd = new FormData()
      // fd.append('bmzdfile', this.file)
      // fd.append('dwid', 1);
      // fd.append('dwmc', 1);
      // fd.append('zdysx', this.tjlist.bfrq);
      // fd.append('ssrq', this.tjlist.ssrq);
      // fd.append('sbnf', this.tjlist.sbnf);
      // fd.append('cjrid', 1);
      // let data = await saveBmzd(fd)
      // if (data.code == 10000) {
      //   this.$message.success('上传报告成功')
      //   this.gjleftsx()
      // } else {
      //   this.$message.error(data.message)
      // }
    },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {

          let params = new FormData();
          params.append('dwmc', 'gongdarenjian');
          params.append('bmzdfile', this.file)
          params.append('sbnf', this.tjlist.sbnf);
          params.append('wjm', this.tjlist.wjm);
          params.append('wh', this.tjlist.wh);
          params.append('zdysx', this.tjlist.bfrq);
          params.append('ssrq', this.tjlist.ssrq);
          params.append('bz', this.tjlist.bz);
          params.append('wjfj', '1');
          params.append('zdid', '123');
          params.append('dwid', '111');
          params.append('cjrid', '222');

          // 格式化上报年份
          // let date = new Date(params.sbnf)
          // if (date != 'Invalid Date') {
          //   params.sbnf = date.getFullYear() + '年'
          // }
          let that = this
          saveBmzd(params).then(() => {
            that.resetForm()
            that.bmzd()
          })
          this.dialogVisible = false
          this.$message({
            message: '添加成功',
            type: 'success'
          });

          // this.saveFj(this.tjlist.fromFjPath, this.tjlist.saveToPath)
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    //上传
    sc() {
      //上传功能
    },
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          // // 删除旧的
          // deleteBmzd(this.updateItemOld)
          // // 插入新的
          // addBmzd(this.xglist)
          // let params
          // console.log(this.xglist);
          let that = this
          updateBmzd(this.xglist).then(() => {
            that.bmzd()
          })
          // 刷新页面表格数据

          // 关闭dialog
          this.$message.success('修改成功')
          this.xgdialogVisible = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))
      this.xglist = JSON.parse(JSON.stringify(row))
      this.xqdialogVisible = true
    },

    updateItem(row) {
      console.log(row);
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      console.log(this.xglist);
      this.xgdialogVisible = true
    },

    selectRow(val) {
      // console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.bmzd()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.bmzd()
    },
    //添加重置
    resetForm() {
      this.tjlist.sbnf = ""
      this.tjlist.fj = ""
      this.tjlist.wjm = ""
      this.tjlist.wh = ""
      this.tjlist.bfrq = ""
      this.tjlist.ssrq = ""
      this.tjlist.bz = ""
    },
    qkbdgb() {
      this.dialogVisible = false
      this.resetForm()
    },
    handleClose(done) {
      this.resetForm()
      this.dialogVisible = false
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },

  },
  watch: {

  }
}
</script>

<style scoped>
.bg_con {
  width: 100%;
  background-size: cover;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.tszt {
  font-family: KaiTi;
  font-weight: 700;
}

/deep/.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.bz {
  height: 72px !important;
  line-height: 72px !important;
  width: 100%;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

/* /deep/.el-form-item__error{
	top: 53%;
} */
.smzt {
  font-size: 12px;
}
</style>