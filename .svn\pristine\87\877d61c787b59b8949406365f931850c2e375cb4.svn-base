<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div class="bg_con_top">
      <img src="./img/title.png" alt="" /><span style="margin-left: 10px"
        >长传链路信息</span
      >
    </div>
    <div>
      <el-form ref="form" :model="form" size="mini" label-width="120px">
        <div class="flex">
          <el-form-item label="长传链路编号">
            <el-input v-model="form.equipmentCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="长传链路名称">
            <el-input
              v-model="form.equipmentName"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="负责人">
            <el-input
              v-model="form.createByName"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="使用状态">
            <el-select
              :disabled="disabled"
              v-model="form.useStatus"
              clearable
              placeholder="请选择使用状态"
              class="widthx"
            >
              <el-option
                v-for="item in syztList"
                :label="item.mc"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="维修状态">
            <el-select
              :disabled="disabled"
              v-model="form.maintenanceStatus"
              clearable
              placeholder="请选择维修状态"
              class="widthx"
            >
              <el-option
                v-for="item in wxztList"
                :label="item.mc"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="审批状态">
            <el-input v-model="form.approveStatus" 
              :disabled="disabled"
            ></el-input>
          </el-form-item> -->
        </div>
        <div class="flex">
          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="form.remark"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div style="width: 960px">
          <el-form-item style="float: right">
            <el-button type="success" @click="add" v-if="tjType == '1'"
              >提交</el-button
            >
            <el-button type="primary" @click="update" v-if="tjType == '2'"
              >修改</el-button
            >
            <el-button type="primary" @click="ccllgzclClick" v-if="tjType == '2' && form.sfxs != 3"
              >长传链路故障处理</el-button
            >
            <el-button type="primary" @click="fh">返回</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getMaintenanceStatus,
  insertLinkFailure,
  updateLinkFailure,
  ccllGetEquipmentCode,
  selectlinkFailureById,
} from "../../../../api/shma";
export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        equipmentCode: "",
        equipmentName: "",
        createByName: "",
        useStatus: "",
        maintenanceStatus: "",
        approveStatus: "",
      },
      tjType: "",
      id: "",
      disabled: false,
      wxztList: [],
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" },
      ],
    };
  },
  computed: {},
  mounted() {
    this.tjType = this.$route.query.type;
    this.id = this.$route.query.id;
    console.log(this.id, "this.id");

    this.getMaintenanceStatus();
    if (this.tjType == "1") {
      this.ccllGetEquipmentCode();
    } else if (this.tjType == "2") {
      this.selectlinkFailureById();
    } else if (this.tjType == "3") {
      this.disabled = true;
      this.selectlinkFailureById();
    }
  },
  methods: {
    async selectlinkFailureById() {
      let params = { id: this.id };
      let data = await selectlinkFailureById(params);
      this.form = data.data;
    },
    async ccllGetEquipmentCode() {
      let data = await ccllGetEquipmentCode();
      console.log(data, "data147");

      this.form.equipmentCode = data.data;
    },
    async add() {
      let params = this.form;
      let data = await insertLinkFailure(params);
      console.log(data);
      if (data.code == 10000) {
        this.$message.success("提交成功");
        this.$router.push({ path: "/ccllgl" });
      } else {
        this.$message.error("提交失败");
      }
    },
    async update() {
      let params = this.form;
      let data = await updateLinkFailure(params);
      console.log(data);
      if (data.code == 10000) {
        this.$message.success("提交成功");
        this.$router.push({ path: "/ccllgl" });
      } else {
        this.$message.error("提交失败");
      }
    },
    fh() {
      this.$router.push({ path: "/ccllgl" });
    },
    async getMaintenanceStatus() {
      let data = await getMaintenanceStatus();
      data.data.forEach((item) => {
        if (item.equipment_main_type == 1) {
          this.wxztList.push(item);
        }
      });
      console.log(this.wxztList);
    },
    ccllgzclClick() {
      this.$router.push({
        path: "/ccllgzcl",
        query: { equipmentCode: this.form.equipmentCode, id: this.id },
      });
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  padding: 10px;
}
.bg_con_top {
  width: 100%;
  height: 35px;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 20px;
}
/deep/.el-form-item .el-form-item__label {
  font-family: SourceHanSansSC-Regular !important;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
  text-align: left;
}
.flex {
  width: 960px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/deep/.el-input .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-select .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-textarea .el-textarea__inner {
  width: 840px !important;
  height: 129px !important;
  border-radius: 2px;
}
.ml-10 {
  background: #20bdd1;
  border: 1px solid #20bdd1;
  color: #ffffff;
}
</style>
