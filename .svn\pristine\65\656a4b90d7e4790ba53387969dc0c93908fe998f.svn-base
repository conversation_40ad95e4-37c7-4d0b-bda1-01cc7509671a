<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden;height: 100%; ">
      <!-- <div class="xmlb-title" style=" cursor: pointer;">
				<span style="font-size: 24px; cursor: pointer;">涉密载体台账</span>
				<span style="" @click="returnSy" class="fhsy">返回</span>
			</div> -->

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.ztbh" clearable placeholder="编号" class="widths">
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.zrr" clearable placeholder="责任人" class="widths">
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-cascader v-model="formInline.scbm" :options="regionOption" clearable class="widths"
                    :props="regionParams" filterable ref="cascaderArr" placeholder="部门" @change="cxbm"></el-cascader>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-select v-model="formInline.lx" clearable placeholder="类型" class="widthx">
                    <el-option v-for="item in sblxxz" :label="item.mc" :value="item.id" :key="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-select v-model="formInline.smmj" clearable placeholder="密级" class="widthx">
                    <el-option v-for="item in sbmjxz" :label="item.mc" :value="item.id" :key="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <!-- <el-date-picker v-model="formInline.bmqx" type="daterange" range-separator="至"
										start-placeholder="查询起始时间" end-placeholder="查询结束日期" format="yyyy-MM-dd"
										value-format="yyyy-MM-dd">
									</el-date-picker> -->
                  <el-input v-model="formInline.bmqx" clearable placeholder="保密期限" class="widths">
                  </el-input>
                </el-form-item>
                <!-- <el-form-item label="生成原因" style="font-weight: 700;">
                  <el-select v-model="formInline.scyy" clearable placeholder="请选择原因" class="widthx">
                    <el-option label="制作" value="制作"></el-option>
                    <el-option label="复制" value="复制"></el-option>
                    <el-option label="接收" value="接收"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item style="font-weight: 700;">
                  <el-select v-model="formInline.zt" clearable placeholder="状态" class="widthx">
                    <el-option v-for="item in sbsyqkxz" :label="item.mc" :value="item.id" :key="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>
                <div style="float:right">
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button v-if="this.dwjy" type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                      删除
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" @click="ckls">
                    查看历史
                  </el-button>
                </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <input type="file" ref="upload"
                      style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                      accept=".xls,.xlsx">
                    <el-button v-if="this.dwjy" type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                      导入
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button v-if="this.dwjy" type="danger" size="medium" icon="el-icon-delete" @click="updateSbZtBtn(4)">销毁
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button v-if="this.dwjy" type="primary" size="medium" icon="el-icon-position" @click="updateSbZtBtn(3)">外发
                    </el-button>
                  </el-form-item>
                  <!-- <el-form-item style="float: right;margin-left: 5px;">
                    <el-button type="danger" size="medium" icon="el-icon-circle-close" @click="bfsb">外发
                    </el-button>
                  </el-form-item> -->
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button v-if="this.dwjy" type="warning" size="medium" icon="el-icon-remove-outline" @click="updateSbZtBtn(2)">
                      借出
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;margin-left: 5px;">
                    <el-button v-if="this.dwjy" type="success" size="medium" icon="el-icon-circle-check" @click="updateSbZtBtn(1)">在管
                    </el-button>
                  </el-form-item>
                  <el-form-item style="float: right;">
                    <el-button v-if="this.dwjy" type="success" size="medium" @click="xzsmsb()" icon="el-icon-plus">
                      新增
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>

            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="smzttzList" ref="tableDiv" border @selection-change="selectRow"
                  @mousedown.native="mouseDownTableHandler" @mouseup.native="mouseUpTableHandler"
                  @mousemove.native="mouseMoveTableHandler"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width:100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 85px)" class="table" stripe 
                  >
                  <el-table-column type="selection" width="55" align="center" fixed> </el-table-column>
                  <el-table-column prop="ztmc" width="200" label="载体名称" :filters="mc_filters" :filter-method="filterMc"
                    fixed>
                  </el-table-column>
                  <el-table-column prop="ztbh" label="载体编号" width="150" fixed></el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>

                  <el-table-column prop="xmbh" label="项目编号" width="150" :filters="xmbh_filters"
                    :filter-method="filterXmbh"></el-table-column>
                  <el-table-column prop="lx" label="载体类型" width="90" :filters="lx_filters" :filter-method="filterLx"
                    :formatter="forztlx">
                  </el-table-column>
                  <el-table-column prop="scyy" label="生成原因" width="90" :filters="scyy_filters" :filter-method="filterScyy"
                    :formatter="forscyy"></el-table-column>
                  <el-table-column prop="smmj" label="密级" width="140" :filters="mj_filters" :filter-method="filterMj"
                    :formatter="formj">
                  </el-table-column>
                  <el-table-column prop="bmqx" label="保密期限" width="90" :filters="bmqx_filters"
                    :filter-method="filterBmqx"></el-table-column>
                  <el-table-column prop="fs" label="份数" width="70"></el-table-column>
                  <el-table-column prop="ys" label="页数" width="70"></el-table-column>
                  <el-table-column prop="zxfw" label="知悉范围" width="180" :filters="zxfw_filters"
                    :filter-method="filterZxfw"></el-table-column>
                  <el-table-column prop="scrq" label="生成日期" width="120" :filters="scrq_filters"
                    :filter-method="filterScrq"></el-table-column>
                  <el-table-column prop="scbm" label="生成部门" width="200" :filters="scbm_filters"
                    :filter-method="filterScbm"></el-table-column>
                  <el-table-column prop="zrr" label="责任人" width="80" :filters="zrr_filters" :filter-method="filterZrr">
                  </el-table-column>
                  <el-table-column prop="bgwz" label="保管位置" width="140" :filters="bmwz_filters"
                    :filter-method="filterBmwz"></el-table-column>
                  <el-table-column prop="zt" label="使用状态" width="90" :filters="zt_filters" :filter-method="filterZt"
                    :formatter="forsyzt">
                  </el-table-column>
                  <el-table-column prop="ztbgsj" label="状态变化时间" width="120" :filters="ztbhsj_filters"
                    :filter-method="filterZtbhsj"></el-table-column>
                  <el-table-column prop="" label="操作" width="160" fixed="right">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="getTrajectory(scoped.row)">轨迹
                      </el-button>
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button v-if="dwjy" size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <!-- <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination> -->
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu" v-if="uploadShow">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-upload :disabled="false" :http-request="uploadFile" action="/" :data="{}" class="upload-button"
                :show-file-list="false" :accept='accept' style="display: inline-block;margin-left: 20px;">
                <el-button size="small" type="primary">上传导入</el-button>
              </el-upload>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1800px" height="800px" title="导入涉密载体台账" class="scbg-dialog" :visible.sync="dialogVisible_dr"
          show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55" align="center" fixed> </el-table-column>
              <el-table-column prop="ztmc" label="载体名称" :filters="mc_filters" :filter-method="filterMc" fixed>
              </el-table-column>
              <el-table-column prop="ztbh" label="载体编号" fixed></el-table-column>
              <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
              <el-table-column prop="xmbh" label="项目编号" :filters="xmbh_filters"
                :filter-method="filterXmbh"></el-table-column>
              <el-table-column prop="lx" label="载体类型" :filters="lx_filters" :filter-method="filterLx"
                :formatter="forztlx">
              </el-table-column>
              <el-table-column prop="scyy" label="生成原因" :filters="scyy_filters" :filter-method="filterScyy"
                :formatter="forscyy"></el-table-column>
              <el-table-column prop="smmj" label="密级" :filters="mj_filters" :filter-method="filterMj" :formatter="formj">
              </el-table-column>
              <el-table-column prop="bmqx" label="保密期限" :filters="bmqx_filters"
                :filter-method="filterBmqx"></el-table-column>
              <el-table-column prop="fs" label="份数"></el-table-column>
              <el-table-column prop="ys" label="页数"></el-table-column>
              <el-table-column prop="zxfw" label="知悉范围" :filters="zxfw_filters"
                :filter-method="filterZxfw"></el-table-column>
              <el-table-column prop="scrq" label="生成日期" :filters="scrq_filters"
                :filter-method="filterScrq"></el-table-column>
              <el-table-column prop="scbm" label="生成部门" :filters="scbm_filters"
                :filter-method="filterScbm"></el-table-column>
              <el-table-column prop="zrr" label="责任人" :filters="zrr_filters" :filter-method="filterZrr">
              </el-table-column>
              <el-table-column prop="bgwz" label="保管位置" :filters="bmwz_filters"
                :filter-method="filterBmwz"></el-table-column>
              <el-table-column prop="zt" label="使用状态" :filters="zt_filters" :filter-method="filterZt"
                :formatter="forsyzt">
              </el-table-column>
              <el-table-column prop="ztbgsj" label="状态变化时间" :filters="ztbhsj_filters"
                :filter-method="filterZtbhsj"></el-table-column>
            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>
        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->

        <el-dialog title="涉密载体详细信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="47%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">

            <el-form-item label="涉密载体名称" prop="ztmc" class="one-line ztgl">
              <el-input placeholder="涉密载体名称" v-model="tjlist.ztmc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="载体编号" prop="ztbh">
                <el-input placeholder="载体编号" v-model="tjlist.ztbh" clearable @blur="onInputBlur(1)">
                </el-input>
              </el-form-item>
              <el-form-item label="项目编号">
                <el-input placeholder="项目编号" v-model="tjlist.xmbh" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成原因" prop='scyy'>
                <el-select v-model="tjlist.scyy" clearable placeholder="请选择生成原因" style="width: 100%;">
                  <el-option v-for="item in ztscyyxz" :label="item.mc" :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="保密期限" prop="bmqx">
                <el-input v-model="tjlist.bmqx" clearable placeholder="保密期限" @blur="bmqx = $event.target.value"
                  type="number"></el-input>
                <!-- <el-date-picker v-model="tjlist.bmqx" class="cd" clearable type="date"
									placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
								</el-date-picker> -->
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="smmj" class="one-line ztgl">
              <el-radio-group v-model="tjlist.smmj">
                <el-radio v-for="item in sbmjxz" :v-model="tjlist.smmj" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <div style="display:flex">
              <el-form-item label="类型" prop="lx">
                <el-select v-model="tjlist.lx" clearable placeholder="请选择涉密机类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="份数" prop="fs">
                <el-input placeholder="份数" v-model="tjlist.fs" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="页数" prop="ys">
                <el-input placeholder="页数" v-model="tjlist.ys" clearable></el-input>
              </el-form-item>
              <el-form-item label="知悉范围" prop="zxfw">
                <!-- <el-autocomplete class="inline-input" value-key="zxfw" v-model.trim="tjlist.zxfw" style="width: 100%;"
									:fetch-suggestions="querySearchzxfw" placeholder="知悉范围">
								</el-autocomplete> -->
                <el-input type="textarea" :rows="1" placeholder="知悉范围" v-model="tjlist.zxfw" clearable
                  style="width:65%"></el-input>
                <el-button type="success" @click="zxfw()" size="mini" style="float:right;">
                  添 加
                </el-button>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成日期" prop="scrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.scrq" clearable type="date" style="width: 100%;" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="生成部门" prop="scbm">
                <el-cascader v-model="tjlist.scbm" :options="regionOption" style="width: 100%;" :props="regionParams"
                  filterable ref="cascader" @change="handleChange(1)">
                </el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.zrr" style="width: 100%;"
                  :fetch-suggestions="querySearch" placeholder="请输入责任人">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="保管位置" prop="bgwz">
                <el-autocomplete class="inline-input" value-key="bgwz" v-model.trim="tjlist.bgwz" style="width: 100%;"
                  :fetch-suggestions="querySearchbmwz" placeholder="保管位置">
                </el-autocomplete>
              </el-form-item>
            </div>
            <el-form-item label="状态" prop="zt" class="one-line ztgl">
              <el-radio-group v-model="tjlist.zt">
                <el-radio v-for="item in sbsyqkxz" :v-model="tjlist.zt" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态变化时间" class="one-line">
              <el-date-picker v-model="tjlist.ztbgsj" clearable type="date" style="width: 100%;" placeholder="选择日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="handleClose()">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改涉密载体详细信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="47%" class="xg"
          @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">

            <el-form-item label="涉密载体名称" prop="ztmc" class="one-line ztgl">
              <el-input placeholder="涉密载体名称" v-model="xglist.ztmc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="载体编号" prop="ztbh">
                <el-input placeholder="载体编号" v-model="xglist.ztbh" clearable @blur="onInputBlur(2)" disabled>
                </el-input>
              </el-form-item>
              <el-form-item label="项目编号">
                <el-input placeholder="项目编号" v-model="xglist.xmbh" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成原因" prop='scyy'>
                <el-select v-model="xglist.scyy" clearable placeholder="请选择生成原因" style="width: 100%;">
                  <el-option v-for="item in ztscyyxz" :label="item.mc" :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="保密期限" prop="bmqx">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <!-- <el-date-picker v-model="xglist.bmqx" class="cd" clearable type="date"
									placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
								</el-date-picker> -->
                <el-input v-model="xglist.bmqx" clearable placeholder="保密期限" @blur="bmqx = $event.target.value"
                  type="number"></el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="smmj" class="one-line ztgl">
              <el-radio-group v-model="xglist.smmj">
                <el-radio v-for="item in sbmjxz" :v-model="xglist.smmj" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <div style="display:flex">
              <el-form-item label="类型" prop="lx">
                <el-select v-model="xglist.lx" clearable placeholder="请选择涉密机类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="份数" prop="fs">
                <el-input placeholder="份数" v-model="xglist.fs" clearable disabled></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="页数" prop="ys">
                <el-input placeholder="页数" v-model="xglist.ys" clearable></el-input>
              </el-form-item>
              <el-form-item label="知悉范围" prop="zxfw">
                <!-- <el-autocomplete class="inline-input" value-key="zxfw" v-model.trim="xglist.zxfw" style="width: 100%;"
									:fetch-suggestions="querySearchzxfw" placeholder="知悉范围">
								</el-autocomplete> -->
                <el-input type="textarea" :rows="1" placeholder="知悉范围" v-model="xglist.zxfw" clearable
                  style="width:65%"></el-input>
                <el-button type="success" @click="zxfw()" size="mini" style="float:right;">
                  保 存
                </el-button>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成日期" prop="scrq">
                <el-date-picker v-model="xglist.scrq" clearable type="date" style="width: 100%;" placeholder="选择日期"
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="生成部门" prop="scbm">
                <el-cascader v-model="xglist.scbm" :options="regionOption" style="width: 100%;" :props="regionParams"
                  filterable ref="cascader" @change="handleChange(2)">
                </el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.zrr" style="width: 100%;"
                  :fetch-suggestions="querySearch" placeholder="请输入责任人">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="保管位置" prop="bgwz">
                <el-autocomplete class="inline-input" value-key="bgwz" v-model.trim="xglist.bgwz" style="width: 100%;"
                  :fetch-suggestions="querySearchbmwz" placeholder="保管位置">
                </el-autocomplete>
              </el-form-item>
            </div>
            <el-form-item label="状态" prop="zt" class="one-line ztgl">
              <el-radio-group v-model="xglist.zt">
                <el-radio v-for="item in sbsyqkxz" :v-model="xglist.zt" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态变化时间" class="one-line">
              <el-date-picker v-model="xglist.ztbgsj" clearable type="date" style="width: 100%;" placeholder="选择日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="涉密载体详细信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="47%"
          class="xg">
          <el-form ref="form" :model="xglist" label-width="120px" size="mini" disabled>

            <el-form-item label="涉密载体名称" prop="ztmc" class="one-line">
              <el-input placeholder="涉密载体名称" v-model="xglist.ztmc" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="载体编号" prop="ztbh">
                <el-input placeholder="载体编号" v-model="xglist.ztbh" clearable></el-input>
              </el-form-item>
              <el-form-item label="项目编号">
                <el-input placeholder="项目编号" v-model="xglist.xmbh" clearable>
                </el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成原因" prop='scyy'>
                <el-select v-model="xglist.scyy" clearable placeholder="请选择生成原因" style="width: 100%;">
                  <el-option v-for="item in ztscyyxz" :label="item.mc" :value="item.id" :key="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="保密期限" prop="bmqx">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <!-- <el-date-picker v-model="xglist.bmqx" class="cd" clearable type="date"
									placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
								</el-date-picker> -->
                <el-input v-model="xglist.bmqx" clearable placeholder="保密期限"
                  oninput="value=value.replace(/[^\d.]/g,'')"></el-input>
              </el-form-item>
            </div>
            <el-form-item label="密级" prop="smmj" class="one-line">
              <el-radio-group v-model="xglist.smmj">
                <el-radio v-for="item in sbmjxz" :v-model="xglist.smmj" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <div style="display:flex">
              <el-form-item label="类型" prop="lx">
                <el-select v-model="xglist.lx" clearable placeholder="请选择涉密机类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.mc" :value="item.id" :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="份数" prop="fs">
                <el-input placeholder="份数" v-model="xglist.fs" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="页数" prop="ys">
                <el-input placeholder="页数" v-model="xglist.ys" clearable></el-input>
              </el-form-item>
              <el-form-item label="知悉范围" prop="zxfw">
                <el-input placeholder="知悉范围" v-model="xglist.zxfw" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="生成日期" prop="scrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.scrq" class="cd" clearable type="date" style="width: 100%;"
                  placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="生成部门" prop="scbm">
                <el-cascader v-model="xglist.scbm" :options="regionOption" style="width: 100%;" :props="regionParams"
                  filterable ref="cascader" @change="handleChange(2)">
                </el-cascader>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="责任人" prop="zrr">
                <el-autocomplete class="inline-input" value-key="xm" v-model.trim="xglist.zrr" style="width: 100%;"
                  :fetch-suggestions="querySearch" placeholder="请输入责任人">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="保管位置" prop="bgwz">
                <el-input placeholder="保管位置" v-model="xglist.bgwz" clearable></el-input>
              </el-form-item>
            </div>
            <el-form-item label="状态" prop="zt" class="one-line">
              <el-radio-group v-model="xglist.zt">
                <el-radio v-for="item in sbsyqkxz" :v-model="xglist.zt" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">

            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <el-dialog title="培训人员清单" :close-on-click-modal="false" :visible.sync="rydialogVisible" width="54%" class="xg"
          style="margin-top:4vh">
          <el-row type="flex">
            <el-col :span="12" style="height:500px">
              <div style="height:96%;border: 1px solid #dee5e7;">
                <div style="padding-top: 10px;padding-left: 10px;width: 97%;height: 68px;background: #fafafa;">
                  <el-row>待选人员列表</el-row>
                  <el-form :inline="true" :model="formInlinery" size="medium" class="demo-form-inline"
                    style="display:flex;margin-bottom: -3%;">
                    <div class="dialog-select-div">
                      <span class="title">部门</span>
                      <el-cascader v-model="formInlinery.bm" :options="regionOption" clearable class="widths"
                        style="width:14vw" :props="regionParams" filterable ref="cascaderArr" @change="bmrycx">
                      </el-cascader>
                      <el-button type="primary" icon="el-icon-search" @click="onSubmitry">查询
                      </el-button>
                    </div>
                  </el-form>
                </div>
                <el-table :data="table1Data" style="width: 100%;margin-top:1%;" height="400" ref="table1"
                  @selection-change="onTable1Select" @row-click="handleRowClick">
                  <el-table-column type="selection" width="55">
                  </el-table-column>
                  <el-table-column prop="xm" label="姓名">
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
            <el-col :span="12" style="margin-left:10px;height:500px">
              <div style="height:96%;
          										border: 1px solid #dee5e7;
          										">
                <div style="padding-top: 10px;
          										padding-left: 10px;
          										width: 97%;
          										height: 68px;
          										background: #fafafa;">
                  <el-row>已选人员列表</el-row>
                  <div style="float:right;">
                    <el-button type="primary" @click="addpxry">保 存</el-button>
                    <el-button type="warning" @click="pxrygb">关 闭</el-button>
                  </div>

                </div>
                <el-table :data="table2Data" style="width: 100%;" height="404" ref="table2">
                  <el-table-column prop="xm" label="姓名">
                    <template slot-scope="scope">
                      <div style="display:flex;justify-content: space-between;
          														align-items: center;">
                        <div>
                          {{ scope.row.xm }}
                        </div>
                        <i class="el-icon-circle-close btn" @click="onTable2Select(scope.row)"></i>
                      </div>
                    </template>
                  </el-table-column>

                </el-table>
              </div>

            </el-col>
          </el-row>
        </el-dialog>
        <!-- 历史轨迹 dialog -->
        <el-dialog title="历史轨迹" :close-on-click-modal="false" :visible.sync="lsgjDialogVisible" width="46%" class="xg">
          <div
            style="padding-left: 10px;background: #EBEEF5;height: 40px;line-height: 40px;font-size: 16px;border-radius: 5px;margin-bottom: 5px;">
            <span>载体编号：<span style="font-size: 14px;">{{ lsgjDialogData.ztbh }}</span></span>
          </div>
          <div style="max-height: 400px;overflow-y: scroll;padding: 10px;">
            <el-timeline>
              <el-timeline-item v-for="(activity, index) in lsgjDialogData.timelineList" :key="index"
                :icon="activity.icon" :color="activity.color" :size="'large'" :timestamp="'操作时间：' + activity.czsj">
                <div>
                  <p> {{ activity.syqk }}</p>
                  <p>操作人：{{ activity.czrxm }}</p>
                  <p>状态变化时间:{{ activity.czrxm }}</p>
                  <p>责任人：{{ activity.zrr }}</p>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="lsgjDialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 状态变化时间确认 dialog -->
        <el-dialog title="状态变更确认(变更后)" :close-on-click-modal="false" :visible.sync="dialogVisibleZtbhsjQr" width="70%"
          class="xg">
          <el-table :data="ztbhsjQrList" ref="tableDiv" border @selection-change="selectRow"
            @mousedown.native="mouseDownTableHandler" @mouseup.native="mouseUpTableHandler"
            @mousemove.native="mouseMoveTableHandler" :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
            style="width: 100%;border:1px solid #EBEEF5;" max-height="400px;" stripe>
            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
            <el-table-column prop="ztmc" width="" label="载体名称">
            </el-table-column>
            <el-table-column prop="ztbh" label="载体编号" width="150"></el-table-column>
            <el-table-column prop="xmbh" label="项目编号" width="150"></el-table-column>
            <el-table-column prop="zt" label="使用状态" width="90" align="center" :formatter="forsyzt">
            </el-table-column>
            <el-table-column prop="ztbgsj" label="状态变化时间" width="120" align="center"></el-table-column>
            <el-table-column prop="" label="操作" width="80" align="center" fixed="right">
              <template slot-scope="scoped">
                <el-button size="medium" type="text" @click="removeDialogTableZtbhqrQrBtn(scoped.row)"
                  style="color:#F56C6C;">移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="width: 100%;height:10px;"></div>
          <el-form ref="formZtbhsjQr" :model="dialogObjZtbhsjQr" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="状态" prop="zt" class="one-line ztgl">
              <el-radio-group v-model="dialogObjZtbhsjQr.zt">
                <el-radio v-for="item in sbsyqkxz" :v-model="dialogObjZtbhsjQr.zt" :label="item.id" :value="item.id"
                  :key="item.id">
                  {{ item.mc }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态变化时间" prop="ztbhsj" class="one-line">
              <el-date-picker v-model="dialogObjZtbhsjQr.ztbhsj" clearable type="date" style="width: 100%;"
                placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="ztbhsjQrBtn">确 认</el-button>
            <el-button type="warning" @click="dialogVisibleZtbhsjQr = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import {
  saveZtgl,
  removeZtgl,
  updateZtgl,
  getZtglList,
  getZzjgList,
  getAllYhxx,
  getAllZt,
  getGjxx,
  getLoginInfo
} from '../../../../api/index'
//导入
import {
  //涉密载体导入模板
  downloadImportTemplateSmzt,
  //涉密载体模板上传解析
  uploadFileSmzt,
  //上传解析失败时 下载错误批注文件
  downloadSmztError,
  //删除全部涉密载体
  deleteAllZt
} from '../../../../api/drwj'
import {
  setTrajectoryIcons
} from '../../../../utils/logUtils'
import {
  getAllSmztYy, //原因
  getSmztZt, //状态
  getSmztlx, //类型
  getAllSmsbmj //密级
} from '../../../../api/xlxz'
import {
  decideChange,
  getDateTime
} from '../../../../utils/utils';
import {
  getCurZt
} from '../../../../api/zhyl'
import {
  ztverify
} from '../../../../api/jy'
import {
  exportZtData
} from '../../../../api/dcwj'
import {
  // 获取注册信息
  getDwxx,
} from '../../../../api/dwzc'
export default {
  components: {},
  props: {},
  data() {
    return {
      // 状态变化时间确认dialog显隐
      dialogVisibleZtbhsjQr: false,
      dialogObjZtbhsjQr: {},
      // 状态变化时间确认dialog表格数据
      ztbhsjQrList: [],
      // 标记鼠标是否是按下状态
      isTableMouseDown: false,
      // 表格鼠标按下移动偏移计算基准点
      tableMouseOffset: 0,
      // 历史轨迹dialog显隐
      lsgjDialogVisible: false,
      // 历史轨迹dialog数据
      lsgjDialogData: {
        ztbh: '',
        xmbh: '',
        // 历史轨迹时间线数据
        timelineList: [],
      },
      //数据筛选
      mc_filters: [],
      xmbh_filters: [],
      lx_filters: [],
      scyy_filters: [],
      mj_filters: [],
      bmqx_filters: [],
      zxfw_filters: [],
      scrq_filters: [],
      scbm_filters: [],
      zrr_filters: [],
      bmwz_filters: [],
      zt_filters: [],
      ztbhsj_filters: [],
      //
      rydialogVisible: false,
      ztbh: '',
      pdsmzt: 0,
      sbmjxz: [], //密级
      ztscyyxz: [], //生产原因
      sblxxz: [],
      sbsyqkxz: [],
      smzttzList: [],
      xglist: {},
      tableDataCopy: [],
      updateItemOld: {},
      xgdialogVisible: false,
      xqdialogVisible: false,
      formInline: {

      },
      formInlinery: {
        bm: ''
      },
      table1Data: [],
      table2Data: [],
      selectedTable1Data: [], // table1已选数据
      selectedTable2Data: [], // table2已选数据
      selectedTableData: [], //备份数据
      tjlist: {
        ztmc: '',
        ztbh: '',
        xmbh: '',
        scyy: '',
        smmj: '',
        bmqx: '',
        lx: '',
        fs: '',
        ys: '',
        zxfw: '',
        scrq: '',
        scbm: '',
        zrr: '',
        bgwz: '',
        zt: '',
        ztbgsj: ''
      },
      page: 1,
      pageSize: 10,
      total: 0,
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //表单验证
      rules: {
        ztmc: [{
          required: true,
          message: '请输入涉密载体名称',
          trigger: 'blur'
        },],
        ztbh: [{
          required: true,
          message: '请输入载体编号',
          trigger: 'blur'
        },],
        scyy: [{
          required: true,
          message: '请选择生成原因',
          trigger: 'blur'
        },],
        smmj: [{
          required: true,
          message: '请选择密级',
          trigger: 'blur'
        },],
        bmqx: [{
          required: true,
          message: '请选择保密期限',
          trigger: 'blur'
        },],
        lx: [{
          required: true,
          message: '请选择类型',
          trigger: 'blur'
        },],
        fs: [{
          required: true,
          message: '请输入份数',
          trigger: 'blur'
        },],
        zxfw: [{
          required: true,
          message: '请输入知悉范围',
          trigger: ['blur', 'change'],
        },],
        scrq: [{
          required: true,
          message: '请选择生成日期',
          trigger: 'blur'
        },],
        scbm: [{
          required: true,
          message: '请输入生成部门',
          trigger: ['blur', 'change'],
        },],
        zrr: [{
          required: true,
          message: '请输入责任人',
          trigger: ['blur', 'change'],
        },],
        bgwz: [{
          required: true,
          message: '请输入保管位置',
          trigger: ['blur', 'change'],
        },],
        zt: [{
          required: true,
          message: '请选择状态',
          trigger: 'blur'
        },],
        ztbgsj: [{
          required: true,
          message: '请选择状态变化时间',
          trigger: 'blur'
        },],
        ztbhsj: [{
          required: true,
          message: '请选择状态变化时间',
          trigger: 'blur'
        },],
      },
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      regionOption: [], //地域信息
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true,
      }, //地域信息配置参数
      dwmc: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dclist: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: '',
      scbmid: "",
      cxbmsj: '',
      bmm: undefined,
      //获取单位信息数据
      dwxxList: {},
      //导入
      filename: '',
      form: {
        file: {},
      },
      accept: '',//接受文件格式
      dwjy: true,
      uploadShow: false // 上传按钮显隐
    }
  },
  computed: {},
  mounted() {
    this.getLogin()
    this.ztyy()
    this.ztmj()
    this.ztlx()
    this.ztzt()
    this.zzjg()
    this.smry()
    this.smzttz()
    this.zhsj()
    this.rydata()
    let anpd = localStorage.getItem('dwjy');
    console.log(anpd);
    if (anpd == 1) {
      this.dwjy = false
    }
    else {
      this.dwjy = true
    }
  },
  methods: {
    ckls() {
      this.$router.push({
        path: '/lsSmzttz'
      })
    },
    //获取登录信息
    async getLogin() {
      this.dwxxList = await getDwxx()
    },
    //全部组织机构List
    async zzjg() {
      let zzjgList = await getZzjgList()
      console.log(zzjgList);
      this.zzjgmc = zzjgList
      let shu = []
      console.log(this.zzjgmc);
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            // console.log(item, item1);
            childrenRegionVo.push(item1)
            // console.log(childrenRegionVo);
            item.childrenRegionVo = childrenRegionVo
          }
        });
        // console.log(item);
        shu.push(item)
      })

      console.log(shu);
      console.log(shu[0].childrenRegionVo);
      let shuList = []
       let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      console.log(shuList);
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    async zhsj() {
      let sj = await getCurZt()
      if (sj != '') {
        this.tjlist = sj
        this.tjlist.scbm = this.tjlist.scbm.split('/')
      }

    },
    async ztyy() {
      this.ztscyyxz = await getAllSmztYy()
    },
    async ztmj() {
      this.sbmjxz = await getAllSmsbmj()
    },
    async ztlx() {
      this.sblxxz = await getSmztlx()
    },
    async ztzt() {
      this.sbsyqkxz = await getSmztZt()
    },
    // 状态变化时间dialog移除表格某行
    removeDialogTableZtbhqrQrBtn(row) {
      let rowIndex = this.ztbhsjQrList.indexOf(row)
      console.log('rowIndex', rowIndex)
      this.ztbhsjQrList.splice(rowIndex, 1)
    },
    // 状态变化时间dialog确认按钮点击事件
    ztbhsjQrBtn() {
      // console.log('this.dialogObjZtbhsjQr', this.dialogObjZtbhsjQr)
      // 数据加工
      this.$refs['formZtbhsjQr'].validate((valid) => {
        if (valid) {
          // 更新数据
          let that = this
          this.ztbhsjQrList.forEach((item) => {
            console.log(item);
            item.zt = this.dialogObjZtbhsjQr.zt;
            item.ztbgsj = this.dialogObjZtbhsjQr.ztbhsj;
            updateZtgl(item).then(() => {
              that.smzttz()
            })
          })
          // updateZtgl(this.ztbhsjQrList, this.dialogObjZtbhsjQr)
          this.dialogVisibleZtbhsjQr = false

        }
      })
    },
    // 表格鼠标按下事件
    mouseDownTableHandler(e) {
      this.tableMouseOffset = e.clientX
      this.isTableMouseDown = true
    },
    // 表格鼠标按下移动事件
    mouseMoveTableHandler(e) {
      // console.log(this.$refs['tableDiv'])
      let tableDiv = this.$refs['tableDiv'].bodyWrapper
      // console.log(tableDiv)
      if (this.isTableMouseDown) {
        tableDiv.scrollLeft -= (-this.tableMouseOffset + (this.tableMouseOffset = e.clientX))
      }
    },
    // 表格鼠标左键抬起事件
    mouseUpTableHandler(e) {
      this.isTableMouseDown = false
    },
    //数据筛选
    filterMc(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterXmbh(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterLx(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterScyy(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterMj(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterBmqx(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterBmqx(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterZxfw(value, row, column) {

    },
    filterScrq(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterScbm(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterZrr(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterBmwz(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterZt(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    filterZtbhsj(value, row, column) {
      const property = column['property'];
      return row[property] === value;
    },
    // 获取轨迹日志
    async getTrajectory(row) {
      console.log(row)
      let params = {
        gdzcbh: row.ztbh,
        sssb: 'smzt',
      }
      let data = await getGjxx(params)
      if (data.code == 10000) {
        console.log("data", data.data);
        if (data.data.length <= 0) {
          this.$message.warning('暂无轨迹')
          return
        }
        //
        this.lsgjDialogData.bmbh = row.bmbh
        this.lsgjDialogData.ztbh = row.ztbh
        this.lsgjDialogData.timelineList = data.data
        this.lsgjDialogData.timelineList.forEach((item) => {
          this.sbsyqkxz.forEach((item1) => {
            if (item.syqk == item1.id) {
              item.syqk = item1.mc
            }
          })
        })
        // icon图标处理
        setTrajectoryIcons(this.lsgjDialogData.timelineList)
        //
        this.lsgjDialogVisible = true
      }
    },
    xzsmsb() {
      this.dialogVisible = true
    },
    Radio(val) {
      this.sjdrfs = val
      console.log("当前选中的数据导入方式", val)
      if (this.sjdrfs != '') {
        this.uploadShow = true
      }
    },
    mbxzgb() { this.sjdrfs = '' },
    async mbdc() {
      var returnData = await downloadImportTemplateSmzt();
      var date = new Date()
      var sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "涉密载体信息模板表-" + sj + ".xls");
    },
    //导入
    chooseFile() {

    },
    uploadFile(item) {
      this.form.file = item.file
      console.log(this.form.file, "this.form.file");
      this.filename = item.file.name
      console.log(this.filename, "this.filename");
      this.uploadZip()
    },

    async uploadZip() {
      let fd = new FormData()
      fd.append("file", this.form.file)
      let resData = await uploadFileSmzt(fd)
      console.log(resData)
      if (resData.code == 10000) {
        this.dr_cyz_list = resData.data
        this.dialogVisible_dr = true
        this.hide()
        //刷新表格数据
        // this.smzttz()
        this.$message({
          title: "提示",
          message: "上传成功",
          type: "success"
        });
      } else if (resData.code == 10001) {
        this.$message({
          title: "提示",
          message: resData.message,
          type: "error"
        });
        this.$confirm("[" + this.filename + "]中存在问题，是否下载错误批注文件？", "提示", {
          confirmButtonText: "下载",
          cancelButtonText: "取消",
          type: "warning"
        }).then(async () => {
          let returnData = await downloadSmztError()
          this.dom_download(returnData, "涉密载体错误批注.xls");
        }).catch()
      } else if (resData.code == 10002) {
        this.$message({
          title: "提示",
          message: resData.message,
          type: "error"
        });
      }
    },
    //----成员组选择
    handleSelectionChange(val) {
      this.multipleTable = val
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    async drcy() {
      if (this.sjdrfs == 1) {
        this.multipleTable.forEach(async (item) => {
          let data = await saveZtgl(item)
          this.smzttz()
          console.log("data", data);
          if (data.code == 40006) {
            this.$message({
              title: "提示",
              message: data.message,
              type: "warning"
            });
          }
        })
        this.dialogVisible_dr = false
      } else if (this.sjdrfs == 2) {
        this.dclist = await getAllZt()
        deleteAllZt(this.dclist)
        setTimeout(() => {
          this.multipleTable.forEach(async (item) => {
            let data = await saveZtgl(item)
            this.smzttz()
            console.log("data", data);
          })
        }, 500);
        this.dialogVisible_dr = false
      }
      this.uploadShow = false
      this.dr_dialog = false
    },
    //隐藏
    hide() {
      this.filename = null
      this.form.file = {}
    },
    //----表格导入方法
    readExcel(e) {

    },
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          let that = this
          this.xglist.scbm = this.xglist.scbm.join('/')
          updateZtgl(this.xglist).then(() => {
            // 刷新页面表格数据
            that.smzttz()
            that.zxfwlist()
          })
          // if (changedFlag) {
          //   // 加入状态变化时间及责任人到轨迹日志额外参数中
          //   let paramsLogs = {
          //     xyybs: 'mk_smzttz',
          //     id: this.xglist.smztid,
          //     ymngnmc: this.xglist.zt,
          //     extraParams: {
          //       ztbhsj: this.xglist.ztbhsj,
          //       zrr: this.xglist.zrr
          //     }
          //   }
          //   writeTrajectoryLog(paramsLogs)
          // }

          // 关闭dialog
          this.$message.success('修改成功')
          this.xgdialogVisible = false
          // } else if (this.pdsmzt == 2) {
          //   this.$message.error('载体编号已存在');
          // }

        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    xqyl(row) {
      // this.updateItemOld = JSON.parse(JSON.stringify(row))

      // this.xglist = JSON.parse(JSON.stringify(row))
      // // this.form1.ywlx = row.ywlx
      // console.log('old', row)
      // console.log("this.xglist.ywlx", this.xglist);
      // this.xglist.scbm = this.xglist.scbm.split('/')
      // this.xqdialogVisible = true
      this.$router.push({
        path: '/ztglxqy',
        query: {
          row: row
        }
      })
    },

    updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row))

      this.xglist = JSON.parse(JSON.stringify(row))
      this.ztbh = this.xglist.ztbh
      // this.form1.ywlx = row.ywlx
      console.log('old', row)
      console.log("this.xglist.ywlx", this.xglist);
      this.xglist.scbm = this.xglist.scbm.split('/')
      this.xgdialogVisible = true
    },
    //查询
    onSubmit() {
      this.page = 1
      this.smzttz()
    },
    filterFunc(val, target, filterArr) {
      // 参数不存在或为空时，就相当于查询全部

    },
    cxbm(item) {
      if (item != undefined) {
        this.cxbmsj = item.join('/')
      }

    },
    returnSy() {
      this.$router.push("/tzglsy");
    },
    async smzttz() {
      let params = {
        ztbh: this.formInline.ztbh,
        zrr: this.formInline.zrr,
        lx: this.formInline.lx,
        scbm: this.cxbmsj,
        smmj: this.formInline.smmj,
        bmqx: this.formInline.bmqx,
        zt: this.formInline.zt,
      }
      if (this.cxbmsj == '') {
        params.scbm = this.formInline.scbm
      }
      let resList = await getZtglList(params)
      console.log("params", resList);

      this.smzttzList = resList
      this.dclist = resList
      this.restaurantszxfw = resList;
      // if (resList.list_total.length != 0) {
      //   this.tjlist = resList.list_total[resList.list_total.length - 1]
      // }
      // this.dclist.forEach((item, label) => {
      //   this.xh.push(label + 1)
      // })
      this.tableDataCopy = resList
      this.total = this.smzttzList.length
      this.dclist.forEach(item => {
        console.log(item);

        this.mc_filters.push({
          text: item.ztmc,
          value: item.ztmc
        })
        this.xmbh_filters.push({
          text: item.xmbh,
          value: item.xmbh
        })
        let lx
        this.sblxxz.forEach(item1 => {
          if (item.lx == item1.id) {
            lx = item1.mc
          }
        })
        this.lx_filters.push({
          text: lx,
          value: item.lx,
        })
        let scyy
        this.ztscyyxz.forEach(item1 => {
          if (item.scyy == item1.id) {
            scyy = item1.mc
          }
        })
        this.scyy_filters.push({
          text: scyy,
          value: item.scyy
        })
        let smmj
        this.sbmjxz.forEach(item1 => {
          if (item.smmj == item1.id) {
            smmj = item1.mc
          }
        })
        this.mj_filters.push({
          text: smmj,
          value: item.smmj
        })
        this.bmqx_filters.push({
          text: item.bmqx,
          value: item.bmqx
        })
        this.zxfw_filters.push({
          text: item.zxfw,
          value: item.zxfw
        })
        this.scrq_filters.push({
          text: item.scrq,
          value: item.scrq
        })
        this.scbm_filters.push({
          text: item.scbm,
          value: item.scbm
        })
        this.zrr_filters.push({
          text: item.zrr,
          value: item.zrr
        })
        this.bmwz_filters.push({
          text: item.bgwz,
          value: item.bgwz
        })
        let zt
        this.sbsyqkxz.forEach(item1 => {
          if (item.zt == item1.id) {
            zt = item1.mc
          }
        })
        this.zt_filters.push({
          text: zt,
          value: item.zt
        })
        this.ztbhsj_filters.push({
          text: item.ztbgsj,
          value: item.ztbgsj
        })

      })
      console.log(this.mc_filters);
      var mc = {};
      this.mc_filters = this.mc_filters.reduce(function (item, next) {
        mc[next.text] ? '' : mc[next.text] = true && item.push(next);
        return item;
      }, []);
      var xmbh = {};
      this.xmbh_filters = this.xmbh_filters.reduce(function (item, next) {
        xmbh[next.text] ? '' : xmbh[next.text] = true && item.push(next);
        return item;
      }, []);
      var lx = {};
      this.lx_filters = this.lx_filters.reduce(function (item, next) {
        lx[next.text] ? '' : lx[next.text] = true && item.push(next);
        return item;
      }, []);
      var scyy = {};
      this.scyy_filters = this.scyy_filters.reduce(function (item, next) {
        scyy[next.text] ? '' : scyy[next.text] = true && item.push(next);
        return item;
      }, []);
      var mj = {};
      this.mj_filters = this.mj_filters.reduce(function (item, next) {
        mj[next.text] ? '' : mj[next.text] = true && item.push(next);
        return item;
      }, []);
      var bmqx = {};
      this.bmqx_filters = this.bmqx_filters.reduce(function (item, next) {
        bmqx[next.text] ? '' : bmqx[next.text] = true && item.push(next);
        return item;
      }, []);
      var zxfw = {};
      this.zxfw_filters = this.zxfw_filters.reduce(function (item, next) {
        zxfw[next.text] ? '' : zxfw[next.text] = true && item.push(next);
        return item;
      }, []);
      var scrq = {};
      this.scrq_filters = this.scrq_filters.reduce(function (item, next) {
        scrq[next.text] ? '' : scrq[next.text] = true && item.push(next);
        return item;
      }, []);
      var scbm = {};
      this.scbm_filters = this.scbm_filters.reduce(function (item, next) {
        scbm[next.text] ? '' : scbm[next.text] = true && item.push(next);
        return item;
      }, []);
      var zrr = {};
      this.zrr_filters = this.zrr_filters.reduce(function (item, next) {
        zrr[next.text] ? '' : zrr[next.text] = true && item.push(next);
        return item;
      }, []);
      var bmwz = {};
      this.bmwz_filters = this.bmwz_filters.reduce(function (item, next) {
        bmwz[next.text] ? '' : bmwz[next.text] = true && item.push(next);
        return item;
      }, []);
      var zt = {};
      this.zt_filters = this.zt_filters.reduce(function (item, next) {
        zt[next.text] ? '' : zt[next.text] = true && item.push(next);
        return item;
      }, []);
      var ztbhsj = {};
      this.ztbhsj_filters = this.ztbhsj_filters.reduce(function (item, next) {
        ztbhsj[next.text] ? '' : ztbhsj[next.text] = true && item.push(next);
        return item;
      }, []);
    },
    //删除
    shanchu(id) {
      let that = this
      if (this.selectlistRow != '') {
        this.$confirm('是否继续删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let valArr = this.selectlistRow
          // console.log("....", val);
          valArr.forEach(function (item) {
            let params = {
              ztid: item.ztid,
              dwid: item.dwid,
            }
            removeZtgl(item).then(() => {
              that.smzttz()
              that.zxfwlist()
            })
            console.log("删除：", item);
            console.log("删除：", item);
          })
          let params = valArr
          this.$message({
            message: '删除成功',
            type: 'success'
          });

        }).catch(() => {
          this.$message('已取消删除')
        })
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {

      this.dialogVisible = true
    },

    //导出
    async exportList() {
      var param = {
        ztbh: this.formInline.ztbh,
        zrr: this.formInline.zrr,
        lx: this.formInline.lx,
        smmj: this.formInline.smmj,
        bmqx: this.formInline.bmqx,
        zt: this.formInline.zt,
      }

      if (this.formInline.scbm != undefined) {
        param.scbm = this.formInline.scbm.join('/')
      }
      var returnData = await exportZtData(param);
      var date = new Date()
      var sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "涉密载体信息表-" + sj + ".xls");
    },

    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    //   //编号自增
    //   setInterval(num,len) {
    //   var len = len //显示的长度，如果以0001则长度为4
    //   num = parseInt(num, 10) + 1//转数据类型，以十进制自增
    //   num = num.toString()//转为字符串
    //   while (num.length < len) {//当字符串长度小于设定长度时，在前面加0
    //     num = "0" + num
    //   }
    //   //如果字符串长度超过设定长度只做自增处理。
    //   return num
    // },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let ztbh = this.tjlist.ztbh
          // let exp = /^[+-]?\d*(\.\d*)?(e[+-]?\d+)?$/;
          let exp = /[0-9]+$/;
          let matchArr = ztbh.match(exp)
          let mwslen
          let prefix
          if (matchArr != null) {
            mwslen = matchArr[0].length;
            prefix = ztbh.substring(0, ztbh.lastIndexOf(matchArr[0]))
          }
          let mws = 0;
          let bl;
          for (let i = 1; i <= this.tjlist.fs; i++) {
            mws = 0
            // let uuid = getUuid()
            let params = {
              dwid: '111',
              ztmc: this.tjlist.ztmc,
              ztbh: ztbh,
              xmbh: this.tjlist.xmbh,
              scyy: this.tjlist.scyy,
              smmj: this.tjlist.smmj,
              bmqx: this.tjlist.bmqx,
              lx: this.tjlist.lx,
              fs: 1,
              ys: this.tjlist.ys,
              zxfw: this.tjlist.zxfw,
              scrq: this.tjlist.scrq,
              scbm: this.tjlist.scbm.join('/'),
              scbmid: this.scbmid,
              zrr: this.tjlist.zrr,
              bgwz: this.tjlist.bgwz,
              zt: this.tjlist.zt,
              ztbgsj: this.tjlist.ztbgsj,
              cjrid: '111'
            }
            console.log(params);
            this.onInputBlur(2, ztbh)
            if (this.pdsmzt.code == 10000) {
              let that = this
              saveZtgl(params).then(() => {
                // that.resetForm()
                that.smzttz()
                that.zxfwlist()
              })
              this.dialogVisible = false
              this.$message({
                message: '添加成功',
                type: 'success'
              });
            }


            console.log(ztbh.match(exp));
            if (ztbh.match(exp) != null) {
              if (ztbh.match(exp)[0] != '') {
                mws = ztbh.match(exp)[0] * 1
                mws++
                bl = mwslen - mws.toString().length
                mws = (mws.toString()).padStart(mwslen, '0')
                ztbh = prefix + mws
              }
            } else {
              mws++
              ztbh = ztbh + mws
            }

          }
        } else {
          console.log('error submit!!');
          return false;
        }

      });

    },
    deleteTkglBtn() {

    },

    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.smzttz()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.smzttz()
    },
    //添加重置
    resetForm() {
      this.tjlist.ztmc = ''
      this.tjlist.scyy = ''
      this.tjlist.smmj = '秘密'
      this.tjlist.bmqx = this.Date
      this.tjlist.lx = '纸介质'
      this.tjlist.fs = ''
      this.tjlist.ys = ''
      this.tjlist.zxfw = ''
      this.tjlist.scrq = this.Date
      this.tjlist.scbm = ''
      this.tjlist.zrr = ''
      this.tjlist.bgwz = ''
      this.tjlist.zt = '在管'
      this.tjlist.ztbgsj = ''
    },
    handleClose(done) {
      // this.resetForm()
      this.dialogVisible = false
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].clearValidate();
    },
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].clearValidate();
    },
    // 设备在管状态更新
    updateSbZtBtn(zt) {
      if (!this.selectlistRow) {
        this.$message({
          message: '请选择需要操作的数据',
          type: 'warning'
        })
        return
      }
      if (this.selectlistRow.length <= 0) {
        this.$message({
          message: '尚未选择需要操作的数据',
          type: 'warning'
        })
        return
      }
      // 弹出状态变化时间选择dialog
      console.log('弹出状态变化时间选择dialog')
      this.ztbhsjQrList = JSON.parse(JSON.stringify(this.selectlistRow))
      this.dialogObjZtbhsjQr = {
        zt: zt,
        ztbhsj: null
      }
      console.log(this.ztbhsjQrList);
      this.dialogVisibleZtbhsjQr = true
      return
      let params = this.selectlistRow
      updateSbZt(params, zt)
      this.smzttz()
      this.$message({
        message: '操作成功',
        type: 'success'
      })
    },
    zysb() {

    },
    tysb() {

    },

    bfsb() {

    },
    jcsb() {

    },
    xhsb() {

    },
    async onInputBlur(index, param) {
      if (index == 1) {
        let params = {
          ztbh: this.tjlist.ztbh
        }
        this.pdsmzt = await ztverify(params)
        console.log(this.pdsmzt);
        if (this.pdsmzt.code == 40006) {
          this.$message.error('载体编号已存在');
        }
      } else if (index == 2) {
        let params = {
          ztbh: param
        }
        this.pdsmzt = await ztverify(params)
        console.log(this.pdsmzt);
        if (this.pdsmzt.code == 40006) {
          this.$message.error('载体编号已存在');
        }
      }
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log("cb(results.dwmc)", results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    async smry() {
      let list = await getAllYhxx()
      this.restaurants = list
    },
    async rydata() {
      let param = {
        bmid: this.bmm
      }
      let list = await getAllYhxx(param)
      this.table1Data = list
    },
    bmrycx() {
      let nodesObj = this.$refs['cascaderArr'].getCheckedNodes()[0]
      if (nodesObj != undefined) {
        // console.log(nodesObj);
        this.bmm = nodesObj.data.bmm
      } else {
        this.bmm = undefined
      }
    },
    async handleChange(index) {
      let nodesObj = this.$refs['cascader'].getCheckedNodes()[0].data
      this.scbmid = nodesObj.bmm
      console.log(nodesObj);
      let resList
      let params
      if (index == 1) {
        params = {
          bmmc: this.tjlist.scbm.join('/')
        }
        resList = await getAllYhxx(params)
      } else if (index == 2) {
        this.xglist.scbmid = nodesObj.bmm
        params = {
          bmmc: this.xglist.scbm.join('/')
        }
        resList = await getAllYhxx(params)
      }
      this.restaurants = resList;
      this.tjlist.zrr = "";
      this.xglist.zrr = "";

    },
    //模糊查询品牌型号
    querySearchzxfw(queryString, cb) {

    },
    createFilterzxfw(queryString) {

    },
    //模糊查询操作系统
    querySearchbmwz(queryString, cb) {
      var restaurants = this.restaurantszxfw;
      console.log("restaurants", restaurants);
      var results = queryString ? restaurants.filter(this.createFilterbmwz(queryString)) : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].bmwz === results[j].bmwz) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterbmwz(queryString) {
      return (restaurant) => {
        return (restaurant.bmwz.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    zxfwlist() {

    },
    cz() {
      this.cxbmsj = ''
      this.formInline = {}
    },
    zxfw() {
      this.rydialogVisible = true
    },
    onSubmitry() {
      this.rydata()
    },

    onTable1Select(rows) {
      console.log(rows);
      this.table2Data = rows
      this.selectlistRow = rows
      // this.selectedTable1Data = [...rows];
      // this.filterAdd(this.selectedTable1Data, this.table2Data, 'sfzhm');
      // this.selectedTable1Data = [];
      // this.$refs.table1.clearSelection();
    },

    /**
     * table2选择事件处理函数
     * @param {array} rows 已勾选的数据
     */
    onTable2Select(rows) {
      this.$refs.table1.selection.forEach((item, label) => {
        if (item == rows) {
          this.$refs.table1.selection.splice(label, 1)
        }
      })
      this.table2Data.forEach((item, label) => {
        if (item == rows) {
          console.log(label);
          this.table2Data.splice(label, 1)
        }
      })
      // this.selectedTable2Data = [...rows];
      // this.table2Data = this.filterDelete(this.selectedTable2Data, this.table2Data, 'sfzhm');
      // this.selectedTable2Data = [];
    },
    handleRowClick(row, column, event) {
      this.$refs.table1.toggleRowSelection(row);
    },
    addpxry() {
      // this.tianjiaryList = this.table2Data
      // this.xglist.ry = this.table2Data
      // this.rydialogVisible = false
      let ry = []
      this.table2Data.forEach(item => {
        ry.push(item.xm)
        // console.log(item);
      })
      console.log(ry);
      this.tjlist.zxfw = ry.join(',')
      this.rydialogVisible = false
      this.$refs.table1.clearSelection()
      this.table2Data = []
    },
    pxrygb() {
      this.rydialogVisible = false
      this.$refs.table1.clearSelection()
      this.table2Data = []
    },
    forsyzt(row) {
      let hxsj
      this.sbsyqkxz.forEach(item => {
        if (row.zt == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    formj(row) {
      let hxsj
      this.sbmjxz.forEach(item => {
        if (row.smmj == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    forscyy(row) {
      let hxsj
      this.ztscyyxz.forEach(item => {
        if (row.scyy == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
    forztlx(row) {
      let hxsj
      this.sblxxz.forEach(item => {
        if (row.lx == item.id) {
          hxsj = item.mc
        }
      })
      return hxsj
    },
  },
  watch: {

  }
}

</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.widthx {
  width: 8vw;
}

.cd {
  width: 191px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
.table ::-webkit-scrollbar {
  display: block !important;
  width: 8px;
  /*滚动条宽度*/
  height: 8px;
  /*滚动条高度*/
}

.table ::-webkit-scrollbar-track {
  border-radius: 10px;
  /*滚动条的背景区域的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(238, 238, 238, 0.3);
  background-color: #eeeeee;
  /*滚动条的背景颜色*/
}

.table ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /*滚动条的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.3);
  background-color: rgb(145, 143, 143);
  /*滚动条的背景颜色*/
}
/deep/ .el-table__row {
    background-color: #fff !important;
}
</style>
