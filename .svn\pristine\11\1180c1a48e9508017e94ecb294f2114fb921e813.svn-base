{"version": 3, "sources": ["webpack:///./src/renderer/assets/mapJson/231100.js", "webpack:///src/renderer/view/homePage/components/dt1.vue", "webpack:///./src/renderer/view/homePage/components/dt1.vue?77c2", "webpack:///./src/renderer/view/homePage/components/dt1.vue", "webpack:///src/renderer/view/homePage/components/bing.vue", "webpack:///./src/renderer/view/homePage/components/bing.vue?97dd", "webpack:///./src/renderer/view/homePage/components/bing.vue", "webpack:///src/renderer/view/homePage/index.vue", "webpack:///./src/renderer/view/homePage/index.vue?1f4a", "webpack:///./src/renderer/view/homePage/index.vue"], "names": ["hhs", "type", "features", "properties", "adcode", "name", "center", "centroid", "childrenNum", "level", "parent", "subFeatureIndex", "acroutes", "geometry", "coordinates", "dt1", "data", "dtList", "created", "mounted", "this", "getQxMap", "methods", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "params", "wrap", "_context", "prev", "next", "citycode", "Object", "dpzs", "sent", "map", "item", "value", "count", "console", "log", "$nextTick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stop", "charts", "echarts", "$refs", "_31100", "option", "tooltip", "show", "trigger", "formatter", "visualMap", "min", "max", "length", "right", "text", "textStyle", "color", "realtime", "calculable", "inRange", "series", "roam", "zoom", "label", "normal", "fontSize", "emphasis", "itemStyle", "areaColor", "borderWidth", "borderColor", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "setOption", "components_dt1", "render", "_h", "$createElement", "_c", "_self", "staticClass", "ref", "staticStyle", "width", "height", "staticRenderFns", "homePage_components_dt1", "__webpack_require__", "normalizeComponent", "bing", "btList", "props", "canClick", "Boolean", "default", "getBt", "_this2", "chartDom", "document", "getElementById", "myChart", "trafficWay", "sum", "reduce", "cur", "pre", "legendData", "i", "push", "borderRadius", "labelLine", "title", "padding", "fontFamily", "x", "y", "legend", "icon", "itemWidth", "itemHeight", "orient", "top", "align", "itemGap", "toolbox", "clockwise", "radius", "scale", "zlevel", "off", "on", "$emit", "components_bing", "attrs", "id", "_v", "homePage_components_bing", "bing_normalizeComponent", "ssrContext", "homePage", "dwpmqk", "dwData", "xqData", "pfData", "bmxzgldw", "jgdw", "smryObj", "smcsObj", "smsbObj", "smztObj", "currentTime", "mc<PERSON><PERSON>j", "components", "computed", "setInterval", "updateTime", "getLoginInfo", "getLeftNum", "getBtnTable", "getPfPhb", "res", "api", "handleValueChanged", "_this3", "_callee2", "values", "_context2", "selected", "values_default", "gybw", "lsbw", "lsyx", "fh", "publish", "$router", "rClick", "val", "_this4", "_callee3", "parmas", "dataLogin", "PubSub", "_context3", "dwid", "bmid", "code", "store", "commit", "dwzc", "path", "query", "dwmc", "fs", "localStorage", "setItem", "search", "now", "Date", "moment", "_this5", "_callee4", "_context4", "smry", "hx", "concat", "toConsumableArray_default", "String", "Number", "zy", "yb", "smcs", "smsb", "smzt", "_this6", "_callee5", "_context5", "_this7", "_callee6", "_context6", "_this8", "_callee7", "_context7", "watch", "view_homePage", "_vm", "_s", "click", "total", "_e", "_l", "index", "key", "placeholder", "model", "callback", "$$v", "expression", "_m", "$event", "zzjg", "smgw", "jypx", "valueChanged", "text-inside", "percentage", "homePage_Component", "homePage_normalizeComponent", "__webpack_exports__"], "mappings": "6OAstFeA,cArtFXC,KAAQ,oBACRC,WACID,KAAQ,UACRE,YAAgBC,OAAU,OAAQC,KAAQ,MAAOC,QAAW,WAAY,WAAYC,UAAa,WAAY,WAAYC,YAAe,EAAGC,MAAS,WAAYC,QAAYN,OAAU,QAAUO,gBAAmB,EAAGC,UAAa,IAAQ,KAAQ,SACnPC,UACIZ,KAAQ,eACRa,gBAGa,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAA<PERSON>,Y<PERSON><PERSON>,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,UACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,gBAM7Bb,KAAQ,UACRE,YAAgBC,OAAU,OAAQC,KAAQ,MAAOC,QAAW,WAAY,WAAYC,UAAa,WAAY,WAAYC,YAAe,EAAGC,MAAS,WAAYC,QAAYN,OAAU,QAAUO,gBAAmB,EAAGC,UAAa,IAAQ,KAAQ,SACnPC,UACIZ,KAAQ,eACRa,gBAGa,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,UACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,iBAM7Bb,KAAQ,UACRE,YAAgBC,OAAU,OAAQC,KAAQ,MAAOC,QAAW,WAAY,WAAYC,UAAa,WAAY,WAAYC,YAAe,EAAGC,MAAS,WAAYC,QAAYN,OAAU,QAAUO,gBAAmB,EAAGC,UAAa,IAAQ,KAAQ,SACnPC,UACIZ,KAAQ,eACRa,gBAGa,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,UAAW,YACX,WAAY,WACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,SAAU,UACV,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,WACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,UACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,iBAM7Bb,KAAQ,UACRE,YAAgBC,OAAU,OAAQC,KAAQ,MAAOC,QAAW,WAAY,WAAYC,UAAa,WAAY,WAAYC,YAAe,EAAGC,MAAS,WAAYC,QAAYN,OAAU,QAAUO,gBAAmB,EAAGC,UAAa,IAAQ,KAAQ,SACnPC,UACIZ,KAAQ,eACRa,gBAGa,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,UACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,QAAS,YACT,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,SAAU,YACV,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,SACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,iBAM7Bb,KAAQ,UACRE,YAAgBC,OAAU,OAAQC,KAAQ,QAASC,QAAW,WAAY,WAAYC,UAAa,WAAY,WAAYC,YAAe,EAAGC,MAAS,WAAYC,QAAYN,OAAU,QAAUO,gBAAmB,EAAGC,UAAa,IAAQ,KAAQ,SACrPC,UACIZ,KAAQ,eACRa,gBAGa,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,SACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,SAAU,YACV,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,WACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,WACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,SAAU,YACV,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,iBAM7Bb,KAAQ,UACRE,YAAgBC,OAAU,OAAQC,KAAQ,MAAOC,QAAW,WAAY,WAAYC,UAAa,UAAW,WAAYC,YAAe,EAAGC,MAAS,WAAYC,QAAYN,OAAU,QAAUO,gBAAmB,EAAGC,UAAa,IAAQ,KAAQ,SAClPC,UACIZ,KAAQ,eACRa,gBAGa,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,SACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,SAAU,YACV,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,SAAU,UACV,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,SAAU,YACV,WAAY,YACZ,WAAY,YACZ,UAAW,WACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,UAAW,YACX,UAAW,YACX,UAAW,YACX,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,UACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,WACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,YACZ,WAAY,mBCnsFrCC,GACAC,KADA,WAEA,OACAC,YAGAC,QANA,aAOAC,QAPA,WAQAC,KAAAC,YAEAC,SACAD,SADA,WACA,IAAAE,EAAAH,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAb,EAAA,OAAAS,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACAJ,GACAK,SAAA,UAFAH,EAAAE,KAAA,EAIAE,OAAAC,EAAA,EAAAD,CAAAN,GAJA,OAIAb,EAJAe,EAAAM,KAKAd,EAAAN,OAAAD,EAAAsB,IAAA,SAAAC,GAEA,OADAA,EAAAC,MAAAD,EAAAE,MACAF,IAEAG,QAAAC,IAAApB,EAAAN,QACAM,EAAAqB,UAAA,WACArB,EAAAsB,eAXA,wBAAAd,EAAAe,SAAAlB,EAAAL,KAAAC,IAcAqB,WAfA,WAgBA,IAAAE,EAAAC,EAAA,KAAA5B,KAAA6B,MAAA,QAGMD,EAAA,YAAN,kBAAAE,GAEA,IAAAC,GAGAC,SACAC,MAAA,EACAC,QAAA,OACAC,UAAA,SAAA1B,GACA,OAAAA,EAAAxB,KAAA,MAAAwB,EAAAW,QAGAgB,WACAC,IAAArC,KAAAH,OAAA,GAAAuB,MACAkB,IAAAtC,KAAAH,OAAAG,KAAAH,OAAA0C,OAAA,GAAAnB,MAEAoB,MAAA,MACAC,MAAA,SACAC,WACAC,MAAA,QAEAC,UAAA,EACAC,YAAA,EACAC,SACAH,OACA,UACA,UACA,UACA,UACA,UACA,aAIAI,SAEA9D,KAAA,SACAJ,KAAA,MACAqC,IAAA,kBACA8B,MAAA,EACAC,KAAA,KACAC,OACAC,QACAlB,MAAA,EACAU,MAAA,OACAS,SAAA,IAEAC,UACApB,MAAA,EACAU,MAAA,OACAS,SAAA,KAGAC,UACAC,WACAC,UAAA,UACAC,YAAA,GAEAN,OACAE,SAAA,GACAT,MAAA,SAGAW,WACAH,QACAI,UAAA,UACAE,YAAA,UACAD,YAAA,EACAE,YAAA,0BACAC,WAAA,IAEAN,UAEAE,UAAA,UACAZ,MAAA,OACAO,OACAjB,MAAA,KAgBArC,KAAAI,KAAAH,UAOA8B,EAAAiC,UAAA7B,MCzIe8B,GADEC,OAFjB,WAA0B,IAAaC,EAAb/D,KAAagE,eAA0BC,EAAvCjE,KAAuCkE,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,YAAsBF,EAAA,OAAYG,IAAA,SAAAC,aAA0BC,MAAA,sBAAAC,OAAA,2BAErJC,oBCqBFC,EAvBUC,EAAQ,OAcjCC,CACEhF,EACAkE,GATF,EAEA,KAEA,KAEA,MAUgC,QCAhCe,GACAhF,KADA,WAEA,OACAiF,YAGA/E,QANA,aAWAgF,OACAC,UACAlG,KAAAmG,QACAC,SAAA,IAGAlF,QAjBA,WAkBAC,KAAAkF,SAEAhF,SACAgF,MADA,WACA,IAAA/E,EAAAH,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAb,EAAA,OAAAS,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACAJ,GACAK,SAAA,UAFAH,EAAAE,KAAA,EAIAE,OAAAC,EAAA,EAAAD,CAAAN,GAJA,OAIAb,EAJAe,EAAAM,KAKAd,EAAA0E,OAAAjF,EACAO,EAAAqB,UAAA,WACArB,EAAAsB,eAPA,wBAAAd,EAAAe,SAAAlB,EAAAL,KAAAC,IAUAqB,WAXA,WAoCA,IAzBA,IAGAM,EAHAoD,EAAAnF,KACAoF,EAAAC,SAAAC,eAAA,QACAC,EAAA3D,EAAA,KAAAwD,GAEAI,EAAAxF,KAAA6E,OAeAY,EAAAD,EAAAE,OAAA,SAAAC,EAAAC,GACA,OAAAD,EAAAC,EAAAxE,OACA,GACAxB,KACAiG,KACAlD,GAAA,+BACAmD,EAAA,EAAAA,EAAAN,EAAAjD,OAAAuD,IAAA,CACA,IAAA7G,EAAAuG,EAAAM,GAAA7G,KACA4G,EAAAE,KAAA9G,GACAW,EAAAmG,MAEA3E,MAAAoE,EAAAM,GAAA1E,MACAnC,OACAqE,WACAE,YAAA,EACAwC,aAAA,EACArC,WAAA,EACAF,YAAAd,EAAAmD,GACApC,YAAAf,EAAAmD,MAIA1E,MAAAqE,EAAA,IACAxG,KAAA,GACAqE,WACAJ,OACAjB,MAAA,GAEAgE,WACAhE,MAAA,GAEAU,MAAA,mBACAc,YAAA,mBACAD,YAAA,MAsBAzB,GACAC,SACAC,MAAA,EACAC,QAAA,OACAC,UAAA,SAAA1B,GACA,OAAAA,EAAAxB,KAAA,MAAAwB,EAAAW,QAGA8E,OACAzD,KAAA,OAEAC,WACAC,MAAA,OACAS,SAAA,GACA+C,SAAA,UACAC,WAAA,mBAOAC,EAAA,QACAC,EAAA,OAEA3D,QACA4D,QACAC,KAAA,OACAC,UAAA,EACAC,WAAA,EACApD,WACAE,YAAA,GAEAmD,OAAA,WACA/G,KAAAiG,EACArD,MAAA,MACAoE,IAAA,MACAC,MAAA,OACAnE,WACAC,MAAA,OACAS,SAAA,GACAgD,WAAA,0BACAD,SAAA,WAEAW,QAAA,IAEAC,SACA9E,MAAA,GAEAc,SAhEA9D,KAAA,GACAJ,KAAA,MACAmI,WAAA,EACAC,QAAA,aACA/H,QAAA,eACAmE,UACA6D,OAAA,GAEAC,OAAA,EACAjE,OACAjB,MAAA,GAEArC,YAuDA2F,EAAA3B,UAAA7B,GAcAwD,EAAA6B,IAAA,uBACA7B,EAAA8B,GAAA,+BAAA5G,GAKA0E,EAAAmC,MAAA,eAAA7G,QCrMe8G,GADEzD,OAFP,WAAgB,IAAaC,EAAb/D,KAAagE,eAA0BC,EAAvCjE,KAAuCkE,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,YAAsBF,EAAA,OAAYE,YAAA,SAAmBF,EAAA,OAAYG,IAAA,SAAAD,YAAA,KAAAE,aAA2CC,MAAA,qBAAAC,OAAA,uBAA4DiD,OAAQC,GAAA,UAAhQzH,KAA6Q0H,GAAA,KAAAzD,EAAA,OAAwBE,YAAA,SAArSnE,KAAwT0H,GAAA,KAAAzD,EAAA,OAAwBE,YAAA,QAAhVnE,KAAkW0H,GAAA,KAAAzD,EAAA,OAAwBE,YAAA,YAA1XnE,KAAgZ0H,GAAA,KAAAzD,EAAA,OAAwBE,YAAA,iBAElaK,oBCChC,IAuBemD,EAvBUjD,EAAQ,OAcjBkD,CACdhD,EACA2C,GAT6B,EAV/B,SAAAM,GACEnD,EAAQ,SAaS,kBAEU,MAUG,wDC2UhCoD,GACAlI,KADA,WAEA,OACAmI,OAAA,GACAC,UACAC,UACAC,UACAC,SAAA,GACAC,KAAA,GACAC,WACAC,WACAC,WACAC,WACAC,YAAA,GACAC,WAGAC,YACAhJ,IAAA8E,EACAG,KAAA+C,GAEAiB,YACA9I,QAtBA,aAuBAC,QAvBA,WAuBA,IAAAI,EAAAH,KAIA6I,YAAA,WACA1I,EAAA2I,cACA,KACA9I,KAAA+I,eACA/I,KAAAgJ,aACAhJ,KAAAC,WACAD,KAAAiJ,cACAjJ,KAAAkJ,YAEAhJ,SACA6I,aADA,WACA,IAAA5D,EAAAnF,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAA2I,EAAA,OAAA9I,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAE,OAAAqI,EAAA,EAAArI,GADA,OACAoI,EADAxI,EAAAM,KAEAK,QAAAC,IAAA4H,GACAhE,EAAAuD,MAAAS,EAHA,wBAAAxI,EAAAe,SAAAlB,EAAA2E,KAAA/E,IAKAiJ,mBANA,SAMAzJ,GAAA,IAAA0J,EAAAtJ,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAAgJ,IAAA,IAAAC,EAAA/I,EAAA0I,EAAA,OAAA9I,EAAAC,EAAAI,KAAA,SAAA+I,GAAA,cAAAA,EAAA7I,KAAA6I,EAAA5I,MAAA,cACAS,QAAAC,IAAA,SAAA3B,EAAA8J,UACAF,EAAAG,IAAA/J,EAAA8J,UACApI,QAAAC,IAAAiI,GACA/I,GACAK,SAAA,SACAI,IAAAtB,EAAAX,KACA2K,KAAAJ,EAAA,GACAK,KAAAL,EAAA,GACAM,KAAAN,EAAA,IATAC,EAAA5I,KAAA,EAYAE,OAAAC,EAAA,EAAAD,CAAAN,GAZA,OAYA0I,EAZAM,EAAAxI,KAaAqI,EAAApB,OAAAiB,EAbA,wBAAAM,EAAA/H,SAAA6H,EAAAD,KAAAlJ,IAeA2J,GArBA,WAsBArF,EAAA,QACAsF,QAAA,eACAhK,KAAAiK,QAAAlE,KAAA,YAEAmE,OA1BA,SA0BAC,GAAA,IAAAC,EAAApK,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAA8J,IAAA,IAAAC,EAAA1K,EAAA2K,EAAAC,EAAA,OAAAnK,EAAAC,EAAAI,KAAA,SAAA+J,GAAA,cAAAA,EAAA7J,KAAA6J,EAAA5J,MAAA,cACAS,QAAAC,IAAA4I,GACAG,GACAI,KAAAP,EAAAQ,MAHAF,EAAA5J,KAAA,EAKAE,OAAAC,EAAA,EAAAD,CAAAuJ,GALA,UAMA,MADA1K,EALA6K,EAAAxJ,MAMA2J,KANA,CAAAH,EAAA5J,KAAA,gBASAgK,EAAA,QAAAC,OAAA,cAAAlL,QATA6K,EAAA5J,KAAA,EAYAE,OAAAgK,EAAA,EAAAhK,GAZA,OAYAwJ,EAZAE,EAAAxJ,KAaAK,QAAAC,IAAAgJ,IACAC,EAAA9F,EAAA,SACAsF,QAAA,OAAAO,GACAC,EAAAR,QAAA,mBACAI,EAAAH,QAAAlE,MACAiF,KAAA,UACAC,OACAC,KAAAf,EAAAe,KACAC,GAAAhB,EAAAgB,MAGAC,aAAAC,QAAA,OAAAlB,EAAAe,MACAE,aAAAC,QAAA,UACAR,EAAA,QAAAC,OAAA,cAAAX,EAAAgB,IA1BA,yBAAAV,EAAA/I,SAAA2I,EAAAD,KAAAhK,IA0CAkL,OApEA,WAqEAtL,KAAAiJ,eAEAH,WAvEA,WAwEA,IAAAyC,EAAA,IAAAC,KACAxL,KAAAyI,YAAA1H,OAAA0K,EAAA,EAAA1K,CAAAwK,IAEAvC,WA3EA,WA2EA,IAAA0C,EAAA1L,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAAoL,IAAA,IAAAlL,EAAAb,EAAA,OAAAS,EAAAC,EAAAI,KAAA,SAAAkL,GAAA,cAAAA,EAAAhL,KAAAgL,EAAA/K,MAAA,cACAJ,GACAK,SAAA,UAFA8K,EAAA/K,KAAA,EAIAE,OAAAC,EAAA,EAAAD,CAAAN,GAJA,OAIAb,EAJAgM,EAAA3K,KAKAyK,EAAAtD,KAAAxI,EAAAwI,KACAsD,EAAAvD,SAAAvI,EAAAuI,SACAuD,EAAArD,QAAAzI,EAAAiM,KACAH,EAAArD,QAAAyD,MAAAC,OAAAC,IAAAC,OAAAP,EAAArD,QAAAyD,MAAA5K,IAAAgL,QACAR,EAAArD,QAAA8D,MAAAJ,OAAAC,IAAAC,OAAAP,EAAArD,QAAA8D,MAAAjL,IAAAgL,QACAR,EAAArD,QAAA+D,MAAAL,OAAAC,IAAAC,OAAAP,EAAArD,QAAA+D,MAAAlL,IAAAgL,QACAR,EAAApD,QAAA1I,EAAAyM,KACAX,EAAApD,QAAAwD,MAAAC,OAAAC,IAAAC,OAAAP,EAAApD,QAAAwD,MAAA5K,IAAAgL,QACAR,EAAApD,QAAA6D,MAAAJ,OAAAC,IAAAC,OAAAP,EAAApD,QAAA6D,MAAAjL,IAAAgL,QACAR,EAAApD,QAAA8D,MAAAL,OAAAC,IAAAC,OAAAP,EAAApD,QAAA8D,MAAAlL,IAAAgL,QACAR,EAAAnD,QAAA3I,EAAA0M,KACAZ,EAAAnD,QAAAuD,MAAAC,OAAAC,IAAAC,OAAAP,EAAAnD,QAAAuD,MAAA5K,IAAAgL,QACAR,EAAAnD,QAAA4D,MAAAJ,OAAAC,IAAAC,OAAAP,EAAAnD,QAAA4D,MAAAjL,IAAAgL,QACAR,EAAAnD,QAAA6D,MAAAL,OAAAC,IAAAC,OAAAP,EAAAnD,QAAA6D,MAAAlL,IAAAgL,QACAR,EAAAlD,QAAA5I,EAAA2M,KACAb,EAAAlD,QAAAsD,MAAAC,OAAAC,IAAAC,OAAAP,EAAAlD,QAAAsD,MAAA5K,IAAAgL,QACAR,EAAAlD,QAAA2D,MAAAJ,OAAAC,IAAAC,OAAAP,EAAAlD,QAAA2D,MAAAjL,IAAAgL,QACAR,EAAAlD,QAAA4D,MAAAL,OAAAC,IAAAC,OAAAP,EAAAlD,QAAA4D,MAAAlL,IAAAgL,QAtBA,yBAAAN,EAAAlK,SAAAiK,EAAAD,KAAAtL,IAwBAH,SAnGA,WAmGA,IAAAuM,EAAAxM,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAAkM,IAAA,IAAAhM,EAAAb,EAAA,OAAAS,EAAAC,EAAAI,KAAA,SAAAgM,GAAA,cAAAA,EAAA9L,KAAA8L,EAAA7L,MAAA,cACAJ,GACAK,SAAA,UAFA4L,EAAA7L,KAAA,EAIAE,OAAAC,EAAA,EAAAD,CAAAN,GAJA,OAIAb,EAJA8M,EAAAzL,KAKAuL,EAAAvE,OAAArI,EALA,wBAAA8M,EAAAhL,SAAA+K,EAAAD,KAAApM,IAOA6I,YA1GA,WA0GA,IAAA0D,EAAA3M,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAAqM,IAAA,IAAAnM,EAAAb,EAAA,OAAAS,EAAAC,EAAAI,KAAA,SAAAmM,GAAA,cAAAA,EAAAjM,KAAAiM,EAAAhM,MAAA,cACAJ,GACAyK,KAAAyB,EAAA5E,OACAjH,SAAA,UAHA+L,EAAAhM,KAAA,EAKAE,OAAAC,EAAA,EAAAD,CAAAN,GALA,OAKAb,EALAiN,EAAA5L,KAMA0L,EAAA3E,OAAApI,EANA,wBAAAiN,EAAAnL,SAAAkL,EAAAD,KAAAvM,IAQA8I,SAlHA,WAkHA,IAAA4D,EAAA9M,KAAA,OAAAI,IAAAC,EAAAC,EAAAC,KAAA,SAAAwM,IAAA,IAAAtM,EAAAb,EAAA,OAAAS,EAAAC,EAAAI,KAAA,SAAAsM,GAAA,cAAAA,EAAApM,KAAAoM,EAAAnM,MAAA,cACAJ,GACAK,SAAA,UAFAkM,EAAAnM,KAAA,EAIAE,OAAAC,EAAA,EAAAD,CAAAN,GAJA,OAIAb,EAJAoN,EAAA/L,KAKA6L,EAAA5E,OAAAtI,EALA,wBAAAoN,EAAAtL,SAAAqL,EAAAD,KAAA1M,KAQA6M,UChgBeC,GADEpJ,OAFP,WAAgB,IAAAqJ,EAAAnN,KAAa+D,EAAAoJ,EAAAnJ,eAA0BC,EAAAkJ,EAAAjJ,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,QAAAqD,OAA2BC,GAAA,eAAkBxD,EAAA,OAAYE,YAAA,QAAkBF,EAAA,OAAYE,YAAA,YAAsBF,EAAA,OAAYE,YAAA,gBAA0BgJ,EAAAzF,GAAAyF,EAAAC,GAAAD,EAAA1E,gBAAA0E,EAAAzF,GAAA,KAAAzD,EAAA,OAA0DE,YAAA,gBAA0BgJ,EAAAzF,GAAA,KAAAzD,EAAA,OAAwBE,YAAA,gBAA0BgJ,EAAAzF,GAAA,iBAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAgDE,YAAA,gBAA0BgJ,EAAAzF,GAAA,cAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAA6CE,YAAA,YAAAqD,OAA+BtB,MAAAiH,EAAAzE,MAAAxF,OAAwBmE,IAAKgG,MAAAF,EAAApD,MAAgBoD,EAAAzF,GAAAyF,EAAAC,GAAAD,EAAAzE,MAAAxF,YAAAiK,EAAAzF,GAAA,KAAAzD,EAAA,OAA4DE,YAAA,WAAqBF,EAAA,OAAYE,YAAA,cAAwBF,EAAA,OAAYE,YAAA,WAAqBF,EAAA,OAAYE,YAAA,aAAuBgJ,EAAAzF,GAAAyF,EAAAC,GAAAD,EAAA/E,SAAA+E,EAAAzF,GAAA,KAAAzD,EAAA,OAAmDE,YAAA,aAAuBgJ,EAAAzF,GAAA,SAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAwCE,YAAA,WAAqBF,EAAA,OAAYE,YAAA,aAAuBgJ,EAAAzF,GAAAyF,EAAAC,GAAAD,EAAAhF,aAAAgF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuDE,YAAA,aAAuBgJ,EAAAzF,GAAA,WAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAA0CE,YAAA,aAAuBF,EAAA,OAAYE,YAAA,WAAqBgJ,EAAAzF,GAAAyF,EAAAC,GAAAD,EAAA9E,QAAAiF,UAAAH,EAAAzF,GAAA,KAAAzD,EAAA,OAA4DE,YAAA,UAAoBgJ,EAAAzF,GAAA,SAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAwCE,YAAA,YAAsB,GAAAnE,KAAAqI,QAAAyD,GAAAvJ,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAqI,QAAA,YAAAlH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,UAAoBgJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuCE,YAAA,oBAA8B,GAAAnE,KAAAqI,QAAA8D,GAAA5J,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAqI,QAAA,YAAAlH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,gBAA0BgJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuCE,YAAA,oBAA8B,GAAAnE,KAAAqI,QAAA+D,GAAA7J,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAqI,QAAA,YAAAlH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,gBAA0BgJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAyCE,YAAA,wBAAkCF,EAAA,OAAYE,YAAA,WAAqBgJ,EAAAzF,GAAAyF,EAAAC,GAAAD,EAAA7E,QAAAgF,UAAAH,EAAAzF,GAAA,KAAAzD,EAAA,OAA4DE,YAAA,UAAoBgJ,EAAAzF,GAAA,SAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAwCE,YAAA,YAAsB,GAAAnE,KAAAsI,QAAAwD,GAAAvJ,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAsI,QAAA,YAAAnH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,UAAoBgJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuCE,YAAA,oBAA8B,GAAAnE,KAAAsI,QAAA6D,GAAA5J,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAsI,QAAA,YAAAnH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,gBAA0BgJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuCE,YAAA,oBAA8B,GAAAnE,KAAAsI,QAAA8D,GAAA7J,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAsI,QAAA,YAAAnH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,gBAA0BgJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAyCE,YAAA,wBAAkCF,EAAA,OAAYE,YAAA,WAAqBgJ,EAAAzF,GAAAyF,EAAAC,GAAAD,EAAA5E,QAAA+E,UAAAH,EAAAzF,GAAA,KAAAzD,EAAA,OAA4DE,YAAA,UAAoBgJ,EAAAzF,GAAA,SAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAwCE,YAAA,YAAsB,GAAAnE,KAAAuI,QAAAuD,GAAAvJ,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAuI,QAAA,YAAApH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,UAAoBgJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuCE,YAAA,oBAA8B,GAAAnE,KAAAuI,QAAA4D,GAAA5J,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAuI,QAAA,YAAApH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,gBAA0BgJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuCE,YAAA,oBAA8B,GAAAnE,KAAAuI,QAAA6D,GAAA7J,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAuI,QAAA,YAAApH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,gBAA0BgJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAyCE,YAAA,wBAAkCF,EAAA,OAAYE,YAAA,WAAqBgJ,EAAAzF,GAAAyF,EAAAC,GAAAD,EAAA3E,QAAA8E,UAAAH,EAAAzF,GAAA,KAAAzD,EAAA,OAA4DE,YAAA,UAAoBgJ,EAAAzF,GAAA,SAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAwCE,YAAA,YAAsB,GAAAnE,KAAAwI,QAAAsD,GAAAvJ,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAwI,QAAA,YAAArH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,UAAoBgJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuCE,YAAA,oBAA8B,GAAAnE,KAAAwI,QAAA2D,GAAA5J,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAwI,QAAA,YAAArH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,gBAA0BgJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAAuCE,YAAA,oBAA8B,GAAAnE,KAAAwI,QAAA4D,GAAA7J,OAAA0B,EAAA,OAA0CE,YAAA,eAAyBgJ,EAAAzF,GAAA,OAAAyF,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAyF,EAAAK,GAAAxN,KAAAwI,QAAA,YAAArH,EAAAsM,GAAmF,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,eAAmCgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,GAAA,qBAAyD,GAAAgM,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,gBAA0BgJ,EAAAzF,GAAA,YAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAA2CE,YAAA,UAAoBF,EAAA,OAAYG,IAAA,QAAS,GAAA+I,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,YAAsBF,EAAA,YAAiBE,YAAA,WAAAqD,OAA8BmG,YAAA,UAAuBC,OAAQxM,MAAA+L,EAAA,OAAAU,SAAA,SAAAC,GAA4CX,EAAApF,OAAA+F,GAAeC,WAAA,YAAsBZ,EAAAzF,GAAA,KAAAzD,EAAA,OAAwBE,YAAA,SAAAkD,IAAyBgG,MAAAF,EAAA7B,UAAoB6B,EAAAzF,GAAA,YAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,OAA2CE,YAAA,eAAyBgJ,EAAAa,GAAA,GAAAb,EAAAzF,GAAA,QAAA1H,KAAAgI,OAAAzF,OAAA0B,EAAA,OAA4DE,YAAA,cAAyBgJ,EAAAK,GAAAL,EAAA,gBAAAhM,EAAAsM,GAA0C,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,iBAAqCF,EAAA,QAAaE,YAAA,eAAyBgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAK,EAAA,QAAAN,EAAAC,GAAAK,EAAA,qBAAAN,EAAAzF,GAAA,KAAAzD,EAAA,KAA6GE,YAAA,gBAAAqD,OAAmCtB,MAAA/E,EAAA+J,MAAkB7D,IAAKgG,MAAA,SAAAY,GAAyB,OAAAd,EAAAjD,OAAA/I,OAA0BgM,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAjM,EAAA+J,MAAA,kBAAAiC,EAAAzF,GAAA,KAAAzD,EAAA,KAAkFE,YAAA,UAAAqD,OAA6BtB,MAAA/E,EAAAgK,MAAiBgC,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAAgK,OAAAgC,EAAAzF,GAAA,KAAAzD,EAAA,KAAgDE,YAAA,UAAAqD,OAA6BtB,MAAA/E,EAAA+M,QAAmBf,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAA+M,SAAAf,EAAAzF,GAAA,KAAAzD,EAAA,KAAkDE,YAAA,UAAAqD,OAA6BtB,MAAA/E,EAAAgN,QAAmBhB,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAAgN,SAAAhB,EAAAzF,GAAA,KAAAzD,EAAA,KAAkDE,YAAA,UAAAqD,OAA6BtB,MAAA/E,EAAA0K,QAAmBsB,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAA0K,SAAAsB,EAAAzF,GAAA,KAAAzD,EAAA,KAAkDE,YAAA,UAAAqD,OAA6BtB,MAAA/E,EAAAkL,QAAmBc,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAAkL,SAAAc,EAAAzF,GAAA,KAAAzD,EAAA,KAAkDE,YAAA,UAAAqD,OAA6BtB,MAAA/E,EAAAmL,QAAmBa,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAAmL,SAAAa,EAAAzF,GAAA,KAAAzD,EAAA,KAAkDE,YAAA,UAAAqD,OAA6BtB,MAAA/E,EAAAoL,QAAmBY,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAAoL,SAAAY,EAAAzF,GAAA,KAAAzD,EAAA,KAAkDE,YAAA,UAAAqD,OAA6BtB,MAAA/E,EAAAiN,QAAmBjB,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAAiN,aAAgC,GAAAjB,EAAAI,KAAAJ,EAAAzF,GAAA,QAAA1H,KAAAgI,OAAAzF,OAAA0B,EAAA,OAA8DE,YAAA,mBAA6BgJ,EAAAzF,GAAA,4BAAAyF,EAAAI,OAAAJ,EAAAzF,GAAA,KAAAzD,EAAA,OAAsEE,YAAA,iBAA2BgJ,EAAAa,GAAA,GAAAb,EAAAzF,GAAA,QAAA1H,KAAAiI,OAAA1F,OAAA0B,EAAA,OAA4DE,YAAA,mBAA8BgJ,EAAAK,GAAAL,EAAA,gBAAAhM,EAAAsM,GAA0C,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,sBAA0CF,EAAA,QAAaE,YAAA,gBAA0BgJ,EAAAzF,GAAA,iBAAAyF,EAAAC,GAAAK,EAAA,QAAAN,EAAAC,GAAAK,EAAA,qBAAAN,EAAAzF,GAAA,KAAAzD,EAAA,KAA6GE,YAAA,WAAAqD,OAA8BtB,MAAA/E,EAAAlC,QAAmBkO,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAAlC,SAAAkO,EAAAzF,GAAA,KAAAzD,EAAA,KAAkDE,YAAA,WAAAqD,OAA8BtB,MAAA/E,EAAAE,SAAoB8L,EAAAzF,GAAAyF,EAAAC,GAAAjM,EAAAE,cAAiC,GAAA8L,EAAAI,KAAAJ,EAAAzF,GAAA,QAAA1H,KAAAiI,OAAA1F,OAAA0B,EAAA,OAA8DE,YAAA,wBAAkCgJ,EAAAzF,GAAA,4BAAAyF,EAAAI,OAAAJ,EAAAzF,GAAA,KAAAzD,EAAA,OAAsEE,YAAA,aAAuBF,EAAA,QAAaoD,IAAIgH,aAAAlB,EAAA9D,uBAAuC,GAAA8D,EAAAzF,GAAA,KAAAzD,EAAA,OAA4BE,YAAA,WAAqBF,EAAA,OAAYE,YAAA,aAAwBgJ,EAAAK,GAAAL,EAAA,gBAAAhM,EAAAsM,GAA0C,OAAAxJ,EAAA,OAAiByJ,IAAAD,EAAAtJ,YAAA,YAAgCF,EAAA,OAAYE,YAAA,eAAyBF,EAAA,OAAYE,YAAA,iBAAAqD,OAAoCtB,MAAA/E,EAAA+J,QAAmBiC,EAAAzF,GAAA,mBAAAyF,EAAAC,GAAAjM,EAAA+J,MAAA,oBAAAiC,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,GAAAlH,EAAA,OAAuGE,YAAA,qCAA+CgJ,EAAAzF,GAAA,mBAAAyF,EAAAC,GAAAjM,EAAAgK,IAAA,qBAAAgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,IAAAhK,EAAAgK,IAAA,GAAAlH,EAAA,OAAgIE,YAAA,qCAA+CgJ,EAAAzF,GAAA,mBAAAyF,EAAAC,GAAAjM,EAAAgK,IAAA,qBAAAgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,IAAAhK,EAAAgK,IAAA,GAAAlH,EAAA,OAAgIE,YAAA,qCAA+CgJ,EAAAzF,GAAA,mBAAAyF,EAAAC,GAAAjM,EAAAgK,IAAA,qBAAAgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,IAAAhK,EAAAgK,IAAA,GAAAlH,EAAA,OAAgIE,YAAA,qCAA+CgJ,EAAAzF,GAAA,mBAAAyF,EAAAC,GAAAjM,EAAAgK,IAAA,qBAAAgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,IAAAhK,EAAAgK,IAAA,GAAAlH,EAAA,OAAgIE,YAAA,qCAA+CgJ,EAAAzF,GAAA,mBAAAyF,EAAAC,GAAAjM,EAAAgK,IAAA,qBAAAgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,IAAA,GAAAlH,EAAA,OAAgHE,YAAA,oBAA8BgJ,EAAAzF,GAAA,mBAAAyF,EAAAC,GAAAjM,EAAAgK,IAAA,qBAAAgC,EAAAI,OAAAJ,EAAAzF,GAAA,KAAAzD,EAAA,OAAkGE,YAAA,YAAsBhD,EAAAgK,GAAA,GAAAlH,EAAA,eAAmCE,YAAA,mCAAAqD,OAAsD8G,eAAA,EAAAC,WAAApN,EAAAgK,MAAyCgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,IAAAhK,EAAAgK,IAAA,GAAAlH,EAAA,eAAyEE,YAAA,mCAAAqD,OAAsD8G,eAAA,EAAAC,WAAApN,EAAAgK,MAAyCgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,IAAAhK,EAAAgK,IAAA,GAAAlH,EAAA,eAAyEE,YAAA,mCAAAqD,OAAsD8G,eAAA,EAAAC,WAAApN,EAAAgK,MAAyCgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,IAAAhK,EAAAgK,IAAA,GAAAlH,EAAA,eAAyEE,YAAA,mCAAAqD,OAAsD8G,eAAA,EAAAC,WAAApN,EAAAgK,MAAyCgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,GAAA,IAAAhK,EAAAgK,IAAA,GAAAlH,EAAA,eAAyEE,YAAA,mCAAAqD,OAAsD8G,eAAA,EAAAC,WAAApN,EAAAgK,MAAyCgC,EAAAI,KAAAJ,EAAAzF,GAAA,KAAAvG,EAAAgK,IAAA,GAAAlH,EAAA,eAAyDE,YAAA,kBAAAqD,OAAqC8G,eAAA,EAAAC,WAAApN,EAAAgK,MAAyCgC,EAAAI,MAAA,OAAiB,UAEpgW/I,iBADb,WAAiB,IAAA2I,EAAAnN,KAAa+D,EAAAoJ,EAAAnJ,eAA0BC,EAAAkJ,EAAAjJ,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,iBAA2BF,EAAA,MAAAA,EAAA,MAAAkJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,QAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,UAAAyF,EAAAzF,GAAA,KAAAzD,EAAA,MAAAkJ,EAAAzF,GAAA,eAA2X,WAAc,IAAa3D,EAAb/D,KAAagE,eAA0BC,EAAvCjE,KAAuCkE,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,sBAAgCF,EAAA,MAAAA,EAAA,MAAhHjE,KAAgH0H,GAAA,QAAhH1H,KAAgH0H,GAAA,KAAAzD,EAAA,MAAhHjE,KAAgH0H,GAAA,QAAhH1H,KAAgH0H,GAAA,KAAAzD,EAAA,MAAhHjE,KAAgH0H,GAAA,iBCExoB,IAcI8G,EAdqB9J,EAAQ,OAcjB+J,CACd3G,EACAoF,GAT6B,EAV/B,SAAoBrF,GAClBnD,EAAQ,SAaS,kBAEU,MAUdgK,EAAA,QAAAF,EAAiB", "file": "js/21.59da3077f2c31bad03ee.js", "sourcesContent": ["const hhs = {\n    \"type\": \"FeatureCollection\",\n    \"features\": [{\n        \"type\": \"Feature\",\n        \"properties\": { \"adcode\": 231102, \"name\": \"爱辉区\", \"center\": [127.497639, 50.249027], \"centroid\": [126.733255, 50.225369], \"childrenNum\": 0, \"level\": \"district\", \"parent\": { \"adcode\": 231100 }, \"subFeatureIndex\": 0, \"acroutes\": [100000, 230000, 231100] },\n        \"geometry\": {\n            \"type\": \"MultiPolygon\",\n            \"coordinates\": [\n                [\n                    [\n                        [126.666358, 49.43616],\n                        [126.678593, 49.434591],\n                        [126.694125, 49.424463],\n                        [126.732215, 49.416073],\n                        [126.739002, 49.417028],\n                        [126.750328, 49.427839],\n                        [126.767486, 49.437183],\n                        [126.774559, 49.449694],\n                        [126.790951, 49.459749],\n                        [126.815946, 49.464383],\n                        [126.822733, 49.467961],\n                        [126.828038, 49.483871],\n                        [126.827177, 49.493476],\n                        [126.847776, 49.500594],\n                        [126.839794, 49.500662],\n                        [126.831957, 49.507506],\n                        [126.845577, 49.514995],\n                        [126.856617, 49.528338],\n                        [126.859102, 49.53756],\n                        [126.852937, 49.546678],\n                        [126.859628, 49.552427],\n                        [126.873918, 49.552801],\n                        [126.902879, 49.556882],\n                        [126.905173, 49.565079],\n                        [126.927683, 49.575994],\n                        [126.935043, 49.573376],\n                        [126.931554, 49.565929],\n                        [126.940922, 49.567085],\n                        [126.965439, 49.577966],\n                        [126.975188, 49.574396],\n                        [126.997411, 49.577252],\n                        [127.005058, 49.573784],\n                        [126.998989, 49.571098],\n                        [127.009694, 49.569295],\n                        [127.014425, 49.573138],\n                        [127.016719, 49.585716],\n                        [127.025848, 49.592786],\n                        [127.039946, 49.593942],\n                        [127.067044, 49.598972],\n                        [127.087929, 49.598972],\n                        [127.09873, 49.60485],\n                        [127.09266, 49.609845],\n                        [127.103509, 49.615722],\n                        [127.117464, 49.619459],\n                        [127.121574, 49.626354],\n                        [127.129794, 49.624588],\n                        [127.141981, 49.614262],\n                        [127.16105, 49.613005],\n                        [127.168936, 49.608622],\n                        [127.191733, 49.608588],\n                        [127.202294, 49.612597],\n                        [127.22447, 49.611374],\n                        [127.236227, 49.620546],\n                        [127.250612, 49.62795],\n                        [127.256204, 49.634811],\n                        [127.262942, 49.63498],\n                        [127.272357, 49.641092],\n                        [127.270923, 49.65827],\n                        [127.278331, 49.663972],\n                        [127.293577, 49.666449],\n                        [127.277375, 49.686501],\n                        [127.290088, 49.704273],\n                        [127.294867, 49.714479],\n                        [127.323016, 49.738035],\n                        [127.34156, 49.746879],\n                        [127.344331, 49.757109],\n                        [127.34089, 49.761987],\n                        [127.34978, 49.767304],\n                        [127.365599, 49.77174],\n                        [127.373819, 49.776616],\n                        [127.375205, 49.797435],\n                        [127.379315, 49.803222],\n                        [127.389638, 49.804542],\n                        [127.471362, 49.804102],\n                        [127.483692, 49.806301],\n                        [127.491625, 49.812527],\n                        [127.514948, 49.81608],\n                        [127.534303, 49.82156],\n                        [127.529046, 49.844625],\n                        [127.529381, 49.864299],\n                        [127.547398, 49.92881],\n                        [127.543527, 49.9443],\n                        [127.535833, 49.953477],\n                        [127.517051, 49.967003],\n                        [127.500562, 49.983795],\n                        [127.495831, 49.994515],\n                        [127.496548, 50.041075],\n                        [127.501805, 50.056731],\n                        [127.542715, 50.099128],\n                        [127.563743, 50.116177],\n                        [127.587161, 50.13779],\n                        [127.595811, 50.1515],\n                        [127.606899, 50.178806],\n                        [127.608476, 50.229749],\n                        [127.603219, 50.239441],\n                        [127.488089, 50.26234],\n                        [127.469737, 50.267233],\n                        [127.446223, 50.270786],\n                        [127.393748, 50.284792],\n                        [127.371859, 50.296516],\n                        [127.340938, 50.32789],\n                        [127.332288, 50.340575],\n                        [127.338931, 50.363391],\n                        [127.369852, 50.403845],\n                        [127.366698, 50.412901],\n                        [127.36665, 50.432343],\n                        [127.364643, 50.438388],\n                        [127.349732, 50.446569],\n                        [127.322873, 50.449039],\n                        [127.305094, 50.454381],\n                        [127.293816, 50.465796],\n                        [127.295918, 50.476008],\n                        [127.30194, 50.48158],\n                        [127.310495, 50.496792],\n                        [127.322969, 50.525667],\n                        [127.328704, 50.531066],\n                        [127.35389, 50.541431],\n                        [127.361154, 50.547561],\n                        [127.370712, 50.581233],\n                        [127.364691, 50.591819],\n                        [127.346195, 50.609225],\n                        [127.336207, 50.625925],\n                        [127.325549, 50.630781],\n                        [127.294532, 50.66353],\n                        [127.289706, 50.679048],\n                        [127.288511, 50.699377],\n                        [127.292573, 50.715879],\n                        [127.304282, 50.728327],\n                        [127.302848, 50.748303],\n                        [127.295154, 50.755037],\n                        [127.266335, 50.76313],\n                        [127.245976, 50.773078],\n                        [127.236131, 50.7814],\n                        [127.173619, 50.86735],\n                        [127.161337, 50.886936],\n                        [127.14418, 50.910216],\n                        [127.113975, 50.937681],\n                        [127.099112, 50.944949],\n                        [127.051894, 50.963017],\n                        [127.030961, 50.981242],\n                        [127.018535, 50.977775],\n                        [127.010937, 50.967838],\n                        [127.013326, 50.961266],\n                        [127.001522, 50.950697],\n                        [127.001091, 50.941381],\n                        [127.005584, 50.930841],\n                        [126.994687, 50.926743],\n                        [126.976049, 50.93213],\n                        [126.958844, 50.924628],\n                        [126.916452, 50.923339],\n                        [126.867705, 50.915208],\n                        [126.785264, 50.915571],\n                        [126.776901, 50.91329],\n                        [126.743255, 50.913026],\n                        [126.711283, 50.906149],\n                        [126.687769, 50.904132],\n                        [126.670899, 50.914348],\n                        [126.639499, 50.902744],\n                        [126.610203, 50.908398],\n                        [126.569102, 50.904661],\n                        [126.518634, 50.903339],\n                        [126.506543, 50.903934],\n                        [126.476339, 50.902115],\n                        [126.470078, 50.903835],\n                        [126.451726, 50.92086],\n                        [126.452777, 50.926578],\n                        [126.443219, 50.931072],\n                        [126.420518, 50.929486],\n                        [126.409143, 50.936293],\n                        [126.371053, 50.964073],\n                        [126.334015, 50.982991],\n                        [126.344768, 50.964932],\n                        [126.335879, 50.953835],\n                        [126.333776, 50.929784],\n                        [126.342187, 50.922314],\n                        [126.335353, 50.910679],\n                        [126.344625, 50.89484],\n                        [126.327324, 50.872214],\n                        [126.314086, 50.867449],\n                        [126.306487, 50.858315],\n                        [126.290285, 50.853781],\n                        [126.283308, 50.841598],\n                        [126.272077, 50.841267],\n                        [126.268492, 50.831565],\n                        [126.253486, 50.8209],\n                        [126.235229, 50.826134],\n                        [126.232218, 50.817687],\n                        [126.233461, 50.801088],\n                        [126.224046, 50.790847],\n                        [126.220701, 50.781997],\n                        [126.226961, 50.775499],\n                        [126.222517, 50.768071],\n                        [126.226053, 50.758088],\n                        [126.219554, 50.753411],\n                        [126.223855, 50.737685],\n                        [126.208275, 50.728194],\n                        [126.211429, 50.71724],\n                        [126.201058, 50.703561],\n                        [126.204117, 50.690907],\n                        [126.187677, 50.685526],\n                        [126.177115, 50.684961],\n                        [126.165979, 50.677353],\n                        [126.161726, 50.669146],\n                        [126.165453, 50.6621],\n                        [126.177545, 50.659375],\n                        [126.157472, 50.642753],\n                        [126.154748, 50.636102],\n                        [126.163303, 50.545762],\n                        [126.161774, 50.539298],\n                        [126.14734, 50.504396],\n                        [126.14433, 50.492356],\n                        [126.14734, 50.486618],\n                        [126.134007, 50.480546],\n                        [126.113313, 50.458787],\n                        [126.106287, 50.458887],\n                        [126.089369, 50.448605],\n                        [126.090421, 50.440124],\n                        [126.074458, 50.434447],\n                        [126.066572, 50.425596],\n                        [126.054863, 50.42152],\n                        [126.042055, 50.42152],\n                        [126.032162, 50.425195],\n                        [126.027335, 50.419917],\n                        [126.008506, 50.414772],\n                        [125.99369, 50.41407],\n                        [125.976055, 50.402876],\n                        [125.959232, 50.384057],\n                        [125.9437, 50.382051],\n                        [125.918753, 50.366836],\n                        [125.907474, 50.363491],\n                        [125.896099, 50.351583],\n                        [125.89696, 50.340508],\n                        [125.891464, 50.316307],\n                        [125.893949, 50.31172],\n                        [125.895144, 50.292798],\n                        [125.881475, 50.280135],\n                        [125.874593, 50.267836],\n                        [125.852466, 50.250976],\n                        [125.850745, 50.243398],\n                        [125.831342, 50.200257],\n                        [125.834496, 50.192637],\n                        [125.833158, 50.175548],\n                        [125.834496, 50.160503],\n                        [125.842095, 50.144309],\n                        [125.885107, 50.143839],\n                        [125.87727, 50.129892],\n                        [125.877174, 50.112882],\n                        [125.889122, 50.102558],\n                        [125.899588, 50.101717],\n                        [125.909816, 50.096975],\n                        [125.931943, 50.095394],\n                        [125.937009, 50.086278],\n                        [125.922242, 50.068343],\n                        [125.910724, 50.068613],\n                        [125.901165, 50.074805],\n                        [125.895526, 50.073728],\n                        [125.889074, 50.065617],\n                        [125.89543, 50.059458],\n                        [125.909433, 50.05461],\n                        [125.887975, 50.046833],\n                        [125.872968, 50.048012],\n                        [125.879564, 50.042153],\n                        [125.875931, 50.037674],\n                        [125.881332, 50.032252],\n                        [125.911154, 50.021507],\n                        [125.919804, 50.024404],\n                        [125.931752, 50.018946],\n                        [125.95149, 50.016689],\n                        [125.961765, 50.01086],\n                        [125.966544, 50.004188],\n                        [125.983654, 49.990807],\n                        [125.986282, 49.982143],\n                        [125.981694, 49.968723],\n                        [125.984419, 49.962483],\n                        [126.001862, 49.950778],\n                        [126.019593, 49.948079],\n                        [126.02595, 49.929316],\n                        [126.020645, 49.918987],\n                        [126.029247, 49.906123],\n                        [126.032401, 49.887074],\n                        [126.037754, 49.869876],\n                        [126.045353, 49.864975],\n                        [126.081006, 49.830591],\n                        [126.074984, 49.815505],\n                        [126.087314, 49.801733],\n                        [126.086693, 49.782575],\n                        [126.073216, 49.77218],\n                        [126.065091, 49.762224],\n                        [126.063084, 49.753248],\n                        [126.068006, 49.737629],\n                        [126.060455, 49.727021],\n                        [126.058543, 49.713157],\n                        [126.061076, 49.70841],\n                        [126.078234, 49.692878],\n                        [126.085546, 49.689045],\n                        [126.085641, 49.695083],\n                        [126.093718, 49.692437],\n                        [126.102416, 49.697457],\n                        [126.110684, 49.69298],\n                        [126.122059, 49.695252],\n                        [126.129084, 49.687858],\n                        [126.153075, 49.688774],\n                        [126.154461, 49.679207],\n                        [126.173244, 49.6769],\n                        [126.178883, 49.680904],\n                        [126.174247, 49.689147],\n                        [126.177115, 49.694303],\n                        [126.192073, 49.700509],\n                        [126.208084, 49.693997],\n                        [126.223807, 49.694404],\n                        [126.231788, 49.698745],\n                        [126.230355, 49.708613],\n                        [126.21444, 49.712885],\n                        [126.22763, 49.723665],\n                        [126.237237, 49.742271],\n                        [126.245648, 49.738272],\n                        [126.261419, 49.737493],\n                        [126.277047, 49.743728],\n                        [126.284025, 49.736205],\n                        [126.295638, 49.742915],\n                        [126.312413, 49.741932],\n                        [126.322593, 49.748674],\n                        [126.336691, 49.751283],\n                        [126.343669, 49.7565],\n                        [126.361925, 49.755924],\n                        [126.375976, 49.761377],\n                        [126.376502, 49.764832],\n                        [126.391747, 49.769133],\n                        [126.411772, 49.767169],\n                        [126.420948, 49.761682],\n                        [126.429742, 49.760801],\n                        [126.456601, 49.749115],\n                        [126.465107, 49.747827],\n                        [126.490437, 49.750267],\n                        [126.504536, 49.742339],\n                        [126.499422, 49.728648],\n                        [126.500234, 49.721903],\n                        [126.484415, 49.719937],\n                        [126.484845, 49.706172],\n                        [126.478776, 49.696236],\n                        [126.485562, 49.683652],\n                        [126.511131, 49.676799],\n                        [126.536938, 49.658983],\n                        [126.536508, 49.654265],\n                        [126.527762, 49.653552],\n                        [126.525229, 49.642722],\n                        [126.517439, 49.635625],\n                        [126.525851, 49.621361],\n                        [126.536795, 49.612257],\n                        [126.55161, 49.603627],\n                        [126.552901, 49.596491],\n                        [126.547883, 49.587076],\n                        [126.541192, 49.583099],\n                        [126.537225, 49.574838],\n                        [126.545589, 49.569431],\n                        [126.549269, 49.5561],\n                        [126.528623, 49.549671],\n                        [126.519255, 49.543582],\n                        [126.543629, 49.521939],\n                        [126.539041, 49.511353],\n                        [126.548217, 49.506961],\n                        [126.540571, 49.493408],\n                        [126.54664, 49.48755],\n                        [126.556963, 49.485166],\n                        [126.571253, 49.476173],\n                        [126.57823, 49.466871],\n                        [126.589175, 49.461589],\n                        [126.609391, 49.459544],\n                        [126.613214, 49.455966],\n                        [126.631853, 49.453069],\n                        [126.646334, 49.446865],\n                        [126.64901, 49.441683],\n                        [126.661436, 49.440763],\n                        [126.666358, 49.43616]\n                    ]\n                ]\n            ]\n        }\n    }, {\n        \"type\": \"Feature\",\n        \"properties\": { \"adcode\": 231123, \"name\": \"逊克县\", \"center\": [128.476152, 49.582974], \"centroid\": [128.612059, 49.036567], \"childrenNum\": 0, \"level\": \"district\", \"parent\": { \"adcode\": 231100 }, \"subFeatureIndex\": 1, \"acroutes\": [100000, 230000, 231100] },\n        \"geometry\": {\n            \"type\": \"MultiPolygon\",\n            \"coordinates\": [\n                [\n                    [\n                        [128.431879, 48.330198],\n                        [128.437997, 48.333405],\n                        [128.447746, 48.347973],\n                        [128.451139, 48.359401],\n                        [128.458977, 48.363686],\n                        [128.487843, 48.370723],\n                        [128.50003, 48.377793],\n                        [128.525073, 48.373858],\n                        [128.53855, 48.385036],\n                        [128.559961, 48.380579],\n                        [128.578409, 48.387927],\n                        [128.586677, 48.383156],\n                        [128.597334, 48.390747],\n                        [128.611624, 48.389911],\n                        [128.614587, 48.396874],\n                        [128.63681, 48.404637],\n                        [128.647802, 48.403175],\n                        [128.665915, 48.407004],\n                        [128.675856, 48.413443],\n                        [128.692679, 48.40951],\n                        [128.701042, 48.411633],\n                        [128.722405, 48.404254],\n                        [128.727758, 48.408222],\n                        [128.741713, 48.409649],\n                        [128.755812, 48.396839],\n                        [128.757245, 48.381067],\n                        [128.765035, 48.371594],\n                        [128.773208, 48.368912],\n                        [128.765657, 48.361318],\n                        [128.774259, 48.355778],\n                        [128.774689, 48.341108],\n                        [128.769384, 48.335984],\n                        [128.778082, 48.327305],\n                        [128.782049, 48.307081],\n                        [128.787736, 48.293444],\n                        [128.796578, 48.289851],\n                        [128.806088, 48.271986],\n                        [128.805419, 48.265634],\n                        [128.815073, 48.265459],\n                        [128.815838, 48.258199],\n                        [128.829602, 48.253067],\n                        [128.849197, 48.250623],\n                        [128.857178, 48.258967],\n                        [128.855648, 48.263888],\n                        [128.863343, 48.273102],\n                        [128.877107, 48.277255],\n                        [128.891492, 48.276138],\n                        [128.897132, 48.281303],\n                        [128.904444, 48.278127],\n                        [128.912712, 48.280081],\n                        [128.91209, 48.296165],\n                        [128.914623, 48.302478],\n                        [128.904539, 48.307047],\n                        [128.904444, 48.32354],\n                        [128.92638, 48.325108],\n                        [128.934218, 48.332464],\n                        [128.921362, 48.342641],\n                        [128.924707, 48.35543],\n                        [128.921219, 48.363164],\n                        [128.92877, 48.369539],\n                        [128.92356, 48.375669],\n                        [128.927957, 48.382634],\n                        [128.939762, 48.385802],\n                        [128.950037, 48.396596],\n                        [128.960551, 48.400704],\n                        [128.965522, 48.4077],\n                        [128.977087, 48.408153],\n                        [128.97379, 48.416818],\n                        [128.955007, 48.421377],\n                        [128.954386, 48.425622],\n                        [128.968389, 48.43011],\n                        [128.97919, 48.445416],\n                        [128.988462, 48.452788],\n                        [128.995487, 48.483312],\n                        [128.987649, 48.49676],\n                        [128.995917, 48.503534],\n                        [128.994149, 48.508259],\n                        [129.004281, 48.511767],\n                        [128.99888, 48.52809],\n                        [129.009586, 48.533958],\n                        [129.017997, 48.544268],\n                        [129.017328, 48.553848],\n                        [129.032478, 48.556867],\n                        [129.034437, 48.571337],\n                        [129.047628, 48.576436],\n                        [129.066601, 48.57883],\n                        [129.079505, 48.590414],\n                        [129.086912, 48.605429],\n                        [129.094416, 48.606712],\n                        [129.107511, 48.614477],\n                        [129.134895, 48.607162],\n                        [129.141252, 48.614234],\n                        [129.157262, 48.614096],\n                        [129.165434, 48.617839],\n                        [129.184121, 48.618706],\n                        [129.198984, 48.622588],\n                        [129.200848, 48.641716],\n                        [129.192293, 48.650307],\n                        [129.205579, 48.658412],\n                        [129.210263, 48.669321],\n                        [129.229523, 48.674203],\n                        [129.231291, 48.680642],\n                        [129.211649, 48.698087],\n                        [129.207587, 48.704904],\n                        [129.194014, 48.714384],\n                        [129.165482, 48.737938],\n                        [129.168015, 48.749659],\n                        [129.180823, 48.750177],\n                        [129.18522, 48.753531],\n                        [129.173463, 48.763555],\n                        [129.182448, 48.76663],\n                        [129.193536, 48.761861],\n                        [129.204241, 48.760893],\n                        [129.221589, 48.751318],\n                        [129.239368, 48.747031],\n                        [129.252989, 48.747377],\n                        [129.267231, 48.754499],\n                        [129.259536, 48.766319],\n                        [129.260014, 48.774095],\n                        [129.269238, 48.779519],\n                        [129.269955, 48.78522],\n                        [129.281473, 48.793821],\n                        [129.295619, 48.794478],\n                        [129.302549, 48.79845],\n                        [129.311725, 48.794719],\n                        [129.311486, 48.787535],\n                        [129.322908, 48.792094],\n                        [129.324485, 48.801385],\n                        [129.340591, 48.805011],\n                        [129.36052, 48.802076],\n                        [129.365538, 48.809259],\n                        [129.373424, 48.81247],\n                        [129.386805, 48.808982],\n                        [129.394978, 48.815681],\n                        [129.407738, 48.809466],\n                        [129.4118, 48.812435],\n                        [129.433355, 48.804666],\n                        [129.441766, 48.807118],\n                        [129.438038, 48.814852],\n                        [129.455721, 48.812297],\n                        [129.450655, 48.819341],\n                        [129.455912, 48.831043],\n                        [129.463129, 48.83936],\n                        [129.484205, 48.841879],\n                        [129.495627, 48.850919],\n                        [129.497109, 48.858371],\n                        [129.507097, 48.863373],\n                        [129.509678, 48.872685],\n                        [129.513836, 48.866063],\n                        [129.524159, 48.867374],\n                        [129.524541, 48.874375],\n                        [129.511398, 48.877513],\n                        [129.511255, 48.885616],\n                        [129.522104, 48.892511],\n                        [129.510156, 48.89844],\n                        [129.50055, 48.897199],\n                        [129.500789, 48.902989],\n                        [129.526023, 48.909193],\n                        [129.525879, 48.9135],\n                        [129.506428, 48.923802],\n                        [129.513453, 48.930692],\n                        [129.526453, 48.935273],\n                        [129.517802, 48.935342],\n                        [129.486164, 48.944916],\n                        [129.481003, 48.949978],\n                        [129.465136, 48.952664],\n                        [129.459114, 48.95015],\n                        [129.452137, 48.96072],\n                        [129.459592, 48.965333],\n                        [129.448552, 48.977724],\n                        [129.444347, 48.989251],\n                        [129.437656, 48.992588],\n                        [129.443678, 49.00707],\n                        [129.443295, 49.015565],\n                        [129.449747, 49.016115],\n                        [129.444633, 49.036949],\n                        [129.434071, 49.04537],\n                        [129.41008, 49.052105],\n                        [129.396364, 49.047088],\n                        [129.369983, 49.048497],\n                        [129.342694, 49.045164],\n                        [129.316791, 49.051212],\n                        [129.294759, 49.052174],\n                        [129.281329, 49.061073],\n                        [129.266609, 49.064646],\n                        [129.26183, 49.072719],\n                        [129.245533, 49.089444],\n                        [129.222689, 49.086662],\n                        [129.226608, 49.096413],\n                        [129.234159, 49.101254],\n                        [129.23153, 49.121778],\n                        [129.235067, 49.128057],\n                        [129.235306, 49.147713],\n                        [129.232247, 49.15807],\n                        [129.241136, 49.172058],\n                        [129.241089, 49.177748],\n                        [129.225174, 49.189434],\n                        [129.2257, 49.193375],\n                        [129.215233, 49.210539],\n                        [129.207634, 49.21667],\n                        [129.203763, 49.237386],\n                        [129.193631, 49.243171],\n                        [129.18393, 49.261002],\n                        [129.185268, 49.269077],\n                        [129.179389, 49.271027],\n                        [129.171265, 49.284128],\n                        [129.17246, 49.296473],\n                        [129.177334, 49.299414],\n                        [129.180632, 49.309909],\n                        [129.178959, 49.320642],\n                        [129.194205, 49.325563],\n                        [129.18565, 49.333559],\n                        [129.19626, 49.341553],\n                        [129.198219, 49.347532],\n                        [129.221733, 49.358256],\n                        [129.227707, 49.356651],\n                        [129.225508, 49.365768],\n                        [129.246728, 49.377001],\n                        [129.262308, 49.375704],\n                        [129.264602, 49.372324],\n                        [129.279131, 49.368432],\n                        [129.293229, 49.360578],\n                        [129.289215, 49.369183],\n                        [129.301449, 49.370549],\n                        [129.266514, 49.39608],\n                        [129.248831, 49.399527],\n                        [129.215233, 49.399083],\n                        [129.197216, 49.394442],\n                        [129.181014, 49.38649],\n                        [129.144024, 49.357436],\n                        [129.122565, 49.354499],\n                        [129.085001, 49.359895],\n                        [129.07224, 49.366383],\n                        [129.055896, 49.382258],\n                        [129.047437, 49.408909],\n                        [129.027221, 49.44199],\n                        [129.0136, 49.457261],\n                        [128.998546, 49.460942],\n                        [128.962797, 49.460396],\n                        [128.948603, 49.461862],\n                        [128.932545, 49.468166],\n                        [128.896463, 49.488538],\n                        [128.871372, 49.492352],\n                        [128.827834, 49.484042],\n                        [128.80628, 49.476411],\n                        [128.792038, 49.473243],\n                        [128.779899, 49.474469],\n                        [128.761403, 49.482032],\n                        [128.752848, 49.495145],\n                        [128.754521, 49.506518],\n                        [128.763076, 49.515744],\n                        [128.79782, 49.542765],\n                        [128.808574, 49.547902],\n                        [128.812779, 49.558209],\n                        [128.808765, 49.575042],\n                        [128.802791, 49.582113],\n                        [128.784056, 49.589795],\n                        [128.744294, 49.594961],\n                        [128.735548, 49.590101],\n                        [128.72728, 49.568003],\n                        [128.714902, 49.564841],\n                        [128.698557, 49.569499],\n                        [128.675139, 49.572628],\n                        [128.656214, 49.577558],\n                        [128.619653, 49.593466],\n                        [128.586008, 49.596695],\n                        [128.553748, 49.604578],\n                        [128.538025, 49.604443],\n                        [128.50003, 49.593942],\n                        [128.468966, 49.591189],\n                        [128.414674, 49.592005],\n                        [128.38987, 49.590101],\n                        [128.377397, 49.58677],\n                        [128.371805, 49.579903],\n                        [128.370133, 49.568275],\n                        [128.360001, 49.555488],\n                        [128.34313, 49.545079],\n                        [128.326069, 49.546542],\n                        [128.291898, 49.564739],\n                        [128.275839, 49.566745],\n                        [128.243293, 49.56314],\n                        [128.219159, 49.546235],\n                        [128.206828, 49.541915],\n                        [128.185752, 49.539363],\n                        [128.158893, 49.543037],\n                        [128.140254, 49.550896],\n                        [128.12195, 49.552937],\n                        [128.100062, 49.549977],\n                        [128.085485, 49.551202],\n                        [128.070718, 49.556644],\n                        [128.031289, 49.575484],\n                        [128.023547, 49.565317],\n                        [128.025746, 49.546576],\n                        [128.017717, 49.548243],\n                        [127.999699, 49.556882],\n                        [127.986317, 49.569907],\n                        [127.983545, 49.56042],\n                        [127.989519, 49.554297],\n                        [127.988611, 49.546678],\n                        [127.981777, 49.541915],\n                        [127.970259, 49.523743],\n                        [127.968157, 49.510978],\n                        [127.961083, 49.507131],\n                        [127.938096, 49.508629],\n                        [127.92046, 49.499878],\n                        [127.902443, 49.498346],\n                        [127.873194, 49.488265],\n                        [127.862059, 49.477774],\n                        [127.840457, 49.470142],\n                        [127.839167, 49.464792],\n                        [127.816609, 49.449626],\n                        [127.806334, 49.429033],\n                        [127.801363, 49.424259],\n                        [127.799022, 49.413003],\n                        [127.792283, 49.399663],\n                        [127.794625, 49.389187],\n                        [127.802941, 49.383691],\n                        [127.798687, 49.379254],\n                        [127.786644, 49.378298],\n                        [127.778328, 49.362832],\n                        [127.776799, 49.353031],\n                        [127.766571, 49.348761],\n                        [127.767336, 49.339709],\n                        [127.761888, 49.331748],\n                        [127.76184, 49.323273],\n                        [127.755531, 49.322829],\n                        [127.750035, 49.302217],\n                        [127.736176, 49.292096],\n                        [127.740142, 49.28146],\n                        [127.719735, 49.281494],\n                        [127.718445, 49.275405],\n                        [127.70946, 49.266476],\n                        [127.699711, 49.261207],\n                        [127.697178, 49.2481],\n                        [127.69302, 49.243239],\n                        [127.666973, 49.224204],\n                        [127.65708, 49.220779],\n                        [127.639971, 49.204441],\n                        [127.629409, 49.189332],\n                        [127.602837, 49.169418],\n                        [127.595047, 49.16119],\n                        [127.593374, 49.149051],\n                        [127.584963, 49.14713],\n                        [127.585632, 49.139584],\n                        [127.571199, 49.135228],\n                        [127.569622, 49.126685],\n                        [127.555523, 49.10908],\n                        [127.547542, 49.103966],\n                        [127.683509, 49.070177],\n                        [127.812595, 49.057569],\n                        [127.912862, 49.061176],\n                        [127.971167, 49.103932],\n                        [128.004669, 49.083022],\n                        [128.054946, 49.061623],\n                        [128.35006, 49.026121],\n                        [128.35331, 48.998196],\n                        [128.359523, 48.994756],\n                        [128.361769, 48.946914],\n                        [128.388867, 48.894648],\n                        [128.402296, 48.875616],\n                        [128.391256, 48.827453],\n                        [128.38165, 48.819341],\n                        [128.366548, 48.814818],\n                        [128.365162, 48.793994],\n                        [128.358328, 48.781316],\n                        [128.351446, 48.776824],\n                        [128.289651, 48.765939],\n                        [128.258061, 48.763243],\n                        [128.249841, 48.759614],\n                        [128.260833, 48.744231],\n                        [128.291037, 48.72701],\n                        [128.291037, 48.700717],\n                        [128.267667, 48.67808],\n                        [128.25505, 48.669736],\n                        [128.252565, 48.652662],\n                        [128.188046, 48.64906],\n                        [128.178392, 48.643587],\n                        [128.183888, 48.633608],\n                        [128.204486, 48.628168],\n                        [128.237463, 48.631806],\n                        [128.25591, 48.629242],\n                        [128.253951, 48.603661],\n                        [128.285541, 48.577304],\n                        [128.299257, 48.550065],\n                        [128.299257, 48.531874],\n                        [128.361052, 48.495474],\n                        [128.40502, 48.47636],\n                        [128.421509, 48.464541],\n                        [128.433839, 48.429901],\n                        [128.440721, 48.399833],\n                        [128.443445, 48.377027],\n                        [128.428343, 48.351492],\n                        [128.431879, 48.330198]\n                    ]\n                ]\n            ]\n        }\n    }, {\n        \"type\": \"Feature\",\n        \"properties\": { \"adcode\": 231124, \"name\": \"孙吴县\", \"center\": [127.327315, 49.423941], \"centroid\": [127.326919, 49.397597], \"childrenNum\": 0, \"level\": \"district\", \"parent\": { \"adcode\": 231100 }, \"subFeatureIndex\": 2, \"acroutes\": [100000, 230000, 231100] },\n        \"geometry\": {\n            \"type\": \"MultiPolygon\",\n            \"coordinates\": [\n                [\n                    [\n                        [128.031289, 49.575484],\n                        [128.001372, 49.592276],\n                        [127.981634, 49.587858],\n                        [127.958742, 49.596151],\n                        [127.945408, 49.595199],\n                        [127.921273, 49.581875],\n                        [127.897568, 49.578951],\n                        [127.869275, 49.582929],\n                        [127.841509, 49.590101],\n                        [127.828318, 49.589557],\n                        [127.81508, 49.593908],\n                        [127.80251, 49.604578],\n                        [127.796537, 49.614397],\n                        [127.776082, 49.635897],\n                        [127.763656, 49.641975],\n                        [127.721169, 49.657319],\n                        [127.705302, 49.665126],\n                        [127.691586, 49.677375],\n                        [127.678013, 49.69783],\n                        [127.675528, 49.73285],\n                        [127.674859, 49.764188],\n                        [127.667881, 49.775837],\n                        [127.654165, 49.779934],\n                        [127.582955, 49.7864],\n                        [127.56403, 49.793475],\n                        [127.544722, 49.80867],\n                        [127.534303, 49.82156],\n                        [127.514948, 49.81608],\n                        [127.491625, 49.812527],\n                        [127.483692, 49.806301],\n                        [127.471362, 49.804102],\n                        [127.389638, 49.804542],\n                        [127.379315, 49.803222],\n                        [127.375205, 49.797435],\n                        [127.373819, 49.776616],\n                        [127.365599, 49.77174],\n                        [127.34978, 49.767304],\n                        [127.34089, 49.761987],\n                        [127.344331, 49.757109],\n                        [127.34156, 49.746879],\n                        [127.323016, 49.738035],\n                        [127.294867, 49.714479],\n                        [127.290088, 49.704273],\n                        [127.277375, 49.686501],\n                        [127.293577, 49.666449],\n                        [127.278331, 49.663972],\n                        [127.270923, 49.65827],\n                        [127.272357, 49.641092],\n                        [127.262942, 49.63498],\n                        [127.256204, 49.634811],\n                        [127.250612, 49.62795],\n                        [127.236227, 49.620546],\n                        [127.22447, 49.611374],\n                        [127.202294, 49.612597],\n                        [127.191733, 49.608588],\n                        [127.168936, 49.608622],\n                        [127.16105, 49.613005],\n                        [127.141981, 49.614262],\n                        [127.129794, 49.624588],\n                        [127.121574, 49.626354],\n                        [127.117464, 49.619459],\n                        [127.103509, 49.615722],\n                        [127.09266, 49.609845],\n                        [127.09873, 49.60485],\n                        [127.087929, 49.598972],\n                        [127.067044, 49.598972],\n                        [127.039946, 49.593942],\n                        [127.025848, 49.592786],\n                        [127.016719, 49.585716],\n                        [127.014425, 49.573138],\n                        [127.009694, 49.569295],\n                        [126.998989, 49.571098],\n                        [127.005058, 49.573784],\n                        [126.997411, 49.577252],\n                        [126.975188, 49.574396],\n                        [126.965439, 49.577966],\n                        [126.940922, 49.567085],\n                        [126.931554, 49.565929],\n                        [126.935043, 49.573376],\n                        [126.927683, 49.575994],\n                        [126.905173, 49.565079],\n                        [126.902879, 49.556882],\n                        [126.873918, 49.552801],\n                        [126.859628, 49.552427],\n                        [126.852937, 49.546678],\n                        [126.859102, 49.53756],\n                        [126.856617, 49.528338],\n                        [126.845577, 49.514995],\n                        [126.831957, 49.507506],\n                        [126.839794, 49.500662],\n                        [126.847776, 49.500594],\n                        [126.827177, 49.493476],\n                        [126.828038, 49.483871],\n                        [126.822733, 49.467961],\n                        [126.815946, 49.464383],\n                        [126.790951, 49.459749],\n                        [126.774559, 49.449694],\n                        [126.767486, 49.437183],\n                        [126.750328, 49.427839],\n                        [126.739002, 49.417028],\n                        [126.732215, 49.416073],\n                        [126.694125, 49.424463],\n                        [126.678593, 49.434591],\n                        [126.666358, 49.43616],\n                        [126.67845, 49.431625],\n                        [126.681795, 49.425759],\n                        [126.699956, 49.411843],\n                        [126.704018, 49.399629],\n                        [126.71501, 49.393555],\n                        [126.716683, 49.385432],\n                        [126.73169, 49.364368],\n                        [126.720889, 49.355046],\n                        [126.729587, 49.347361],\n                        [126.728726, 49.337078],\n                        [126.735322, 49.328639],\n                        [126.730399, 49.320608],\n                        [126.73255, 49.313498],\n                        [126.722561, 49.304063],\n                        [126.731546, 49.295037],\n                        [126.731355, 49.2791],\n                        [126.741009, 49.277732],\n                        [126.750042, 49.259154],\n                        [126.7477, 49.2481],\n                        [126.751762, 49.241494],\n                        [126.775849, 49.22821],\n                        [126.79683, 49.225608],\n                        [126.810928, 49.21756],\n                        [126.826078, 49.203585],\n                        [126.831479, 49.185048],\n                        [126.850834, 49.197795],\n                        [126.879318, 49.196664],\n                        [126.906368, 49.184842],\n                        [126.909284, 49.175691],\n                        [126.905604, 49.16863],\n                        [126.895806, 49.160881],\n                        [126.904218, 49.15104],\n                        [126.912151, 49.146547],\n                        [126.932558, 49.148365],\n                        [126.956597, 49.135262],\n                        [126.958127, 49.130836],\n                        [126.948951, 49.122739],\n                        [126.962667, 49.114675],\n                        [126.964387, 49.108119],\n                        [126.9825, 49.105614],\n                        [126.995882, 49.11313],\n                        [127.01022, 49.11416],\n                        [127.011988, 49.102353],\n                        [127.029432, 49.089821],\n                        [127.047497, 49.080687],\n                        [127.064941, 49.079038],\n                        [127.075694, 49.069352],\n                        [127.102983, 49.058874],\n                        [127.102697, 49.050868],\n                        [127.112255, 49.04781],\n                        [127.131324, 49.054889],\n                        [127.143797, 49.047226],\n                        [127.172425, 49.052174],\n                        [127.166116, 49.029455],\n                        [127.156032, 49.017903],\n                        [127.161719, 49.008549],\n                        [127.188483, 48.999469],\n                        [127.187861, 48.992554],\n                        [127.198567, 48.985776],\n                        [127.208125, 48.988597],\n                        [127.216775, 48.984709],\n                        [127.24459, 48.98784],\n                        [127.252954, 48.986842],\n                        [127.259453, 48.998093],\n                        [127.272262, 49.00167],\n                        [127.272166, 49.005041],\n                        [127.294294, 49.001773],\n                        [127.349541, 49.036193],\n                        [127.363257, 49.0486],\n                        [127.378837, 49.058359],\n                        [127.404358, 49.057225],\n                        [127.408659, 49.050353],\n                        [127.422614, 49.043411],\n                        [127.437047, 49.029765],\n                        [127.449139, 49.025708],\n                        [127.484074, 49.030315],\n                        [127.503334, 49.042105],\n                        [127.517959, 49.047913],\n                        [127.519153, 49.059321],\n                        [127.530289, 49.07423],\n                        [127.5268, 49.088276],\n                        [127.532248, 49.093701],\n                        [127.542428, 49.095933],\n                        [127.547542, 49.103966],\n                        [127.555523, 49.10908],\n                        [127.569622, 49.126685],\n                        [127.571199, 49.135228],\n                        [127.585632, 49.139584],\n                        [127.584963, 49.14713],\n                        [127.593374, 49.149051],\n                        [127.595047, 49.16119],\n                        [127.602837, 49.169418],\n                        [127.629409, 49.189332],\n                        [127.639971, 49.204441],\n                        [127.65708, 49.220779],\n                        [127.666973, 49.224204],\n                        [127.69302, 49.243239],\n                        [127.697178, 49.2481],\n                        [127.699711, 49.261207],\n                        [127.70946, 49.266476],\n                        [127.718445, 49.275405],\n                        [127.719735, 49.281494],\n                        [127.740142, 49.28146],\n                        [127.736176, 49.292096],\n                        [127.750035, 49.302217],\n                        [127.755531, 49.322829],\n                        [127.76184, 49.323273],\n                        [127.761888, 49.331748],\n                        [127.767336, 49.339709],\n                        [127.766571, 49.348761],\n                        [127.776799, 49.353031],\n                        [127.778328, 49.362832],\n                        [127.786644, 49.378298],\n                        [127.798687, 49.379254],\n                        [127.802941, 49.383691],\n                        [127.794625, 49.389187],\n                        [127.792283, 49.399663],\n                        [127.799022, 49.413003],\n                        [127.801363, 49.424259],\n                        [127.806334, 49.429033],\n                        [127.816609, 49.449626],\n                        [127.839167, 49.464792],\n                        [127.840457, 49.470142],\n                        [127.862059, 49.477774],\n                        [127.873194, 49.488265],\n                        [127.902443, 49.498346],\n                        [127.92046, 49.499878],\n                        [127.938096, 49.508629],\n                        [127.961083, 49.507131],\n                        [127.968157, 49.510978],\n                        [127.970259, 49.523743],\n                        [127.981777, 49.541915],\n                        [127.988611, 49.546678],\n                        [127.989519, 49.554297],\n                        [127.983545, 49.56042],\n                        [127.986317, 49.569907],\n                        [127.999699, 49.556882],\n                        [128.017717, 49.548243],\n                        [128.025746, 49.546576],\n                        [128.023547, 49.565317],\n                        [128.031289, 49.575484]\n                    ]\n                ]\n            ]\n        }\n    }, {\n        \"type\": \"Feature\",\n        \"properties\": { \"adcode\": 231181, \"name\": \"北安市\", \"center\": [126.508737, 48.245437], \"centroid\": [127.106492, 48.110797], \"childrenNum\": 0, \"level\": \"district\", \"parent\": { \"adcode\": 231100 }, \"subFeatureIndex\": 3, \"acroutes\": [100000, 230000, 231100] },\n        \"geometry\": {\n            \"type\": \"MultiPolygon\",\n            \"coordinates\": [\n                [\n                    [\n                        [126.284981, 48.273242],\n                        [126.300513, 48.262004],\n                        [126.304049, 48.249227],\n                        [126.322593, 48.240497],\n                        [126.323309, 48.225969],\n                        [126.328089, 48.223349],\n                        [126.329236, 48.2056],\n                        [126.319391, 48.20235],\n                        [126.32178, 48.186238],\n                        [126.313273, 48.183861],\n                        [126.319247, 48.178162],\n                        [126.327515, 48.180015],\n                        [126.331482, 48.175295],\n                        [126.347301, 48.17477],\n                        [126.355139, 48.180015],\n                        [126.349404, 48.182532],\n                        [126.362355, 48.190223],\n                        [126.375689, 48.194277],\n                        [126.383479, 48.193089],\n                        [126.391843, 48.197283],\n                        [126.397912, 48.192215],\n                        [126.413158, 48.195046],\n                        [126.416264, 48.190083],\n                        [126.429933, 48.193333],\n                        [126.442837, 48.187286],\n                        [126.446803, 48.197108],\n                        [126.478728, 48.204831],\n                        [126.482743, 48.200358],\n                        [126.49383, 48.2056],\n                        [126.490294, 48.209269],\n                        [126.504679, 48.211296],\n                        [126.520259, 48.219087],\n                        [126.534597, 48.218039],\n                        [126.560452, 48.21189],\n                        [126.57326, 48.215419],\n                        [126.584539, 48.202246],\n                        [126.594575, 48.196864],\n                        [126.594145, 48.186762],\n                        [126.599211, 48.174176],\n                        [126.611732, 48.165259],\n                        [126.61546, 48.152493],\n                        [126.628555, 48.153472],\n                        [126.63257, 48.147386],\n                        [126.65398, 48.139024],\n                        [126.660241, 48.131606],\n                        [126.666549, 48.131256],\n                        [126.666741, 48.124746],\n                        [126.654793, 48.118656],\n                        [126.615747, 48.113125],\n                        [126.621482, 48.096774],\n                        [126.608052, 48.094813],\n                        [126.610681, 48.087669],\n                        [126.600358, 48.086933],\n                        [126.600358, 48.078912],\n                        [126.605615, 48.068682],\n                        [126.590083, 48.063952],\n                        [126.600979, 48.053299],\n                        [126.616798, 48.053754],\n                        [126.618949, 48.04054],\n                        [126.62736, 48.040259],\n                        [126.622868, 48.035106],\n                        [126.628746, 48.004945],\n                        [126.630084, 47.989823],\n                        [126.623155, 47.958618],\n                        [126.631231, 47.949383],\n                        [126.621195, 47.941445],\n                        [126.605854, 47.935333],\n                        [126.598399, 47.926832],\n                        [126.581241, 47.913655],\n                        [126.578183, 47.902092],\n                        [126.579951, 47.895448],\n                        [126.572543, 47.894182],\n                        [126.57694, 47.884795],\n                        [126.567238, 47.883037],\n                        [126.571062, 47.871713],\n                        [126.580955, 47.85817],\n                        [126.587837, 47.842336],\n                        [126.596678, 47.840119],\n                        [126.59577, 47.827519],\n                        [126.564753, 47.79611],\n                        [126.565088, 47.786141],\n                        [126.559926, 47.777967],\n                        [126.567286, 47.756576],\n                        [126.565709, 47.750548],\n                        [126.568624, 47.73588],\n                        [126.566378, 47.726711],\n                        [126.552566, 47.727769],\n                        [126.530773, 47.724842],\n                        [126.518586, 47.725476],\n                        [126.524082, 47.692241],\n                        [126.518491, 47.690618],\n                        [126.523461, 47.665836],\n                        [126.526424, 47.625036],\n                        [126.522075, 47.623693],\n                        [126.526281, 47.603122],\n                        [126.539615, 47.588908],\n                        [126.554335, 47.588554],\n                        [126.557059, 47.58569],\n                        [126.565135, 47.595343],\n                        [126.574885, 47.595732],\n                        [126.57737, 47.609485],\n                        [126.581719, 47.610156],\n                        [126.587406, 47.638569],\n                        [126.594719, 47.644751],\n                        [126.610346, 47.645422],\n                        [126.624397, 47.660186],\n                        [126.642558, 47.66407],\n                        [126.650874, 47.672756],\n                        [126.656274, 47.670214],\n                        [126.670707, 47.675863],\n                        [126.675439, 47.684406],\n                        [126.685714, 47.682394],\n                        [126.700386, 47.689453],\n                        [126.698235, 47.692277],\n                        [126.715823, 47.699758],\n                        [126.716874, 47.705721],\n                        [126.710852, 47.713025],\n                        [126.721414, 47.720926],\n                        [126.739958, 47.728968],\n                        [126.746983, 47.727804],\n                        [126.756589, 47.733694],\n                        [126.770162, 47.734752],\n                        [126.77843, 47.731507],\n                        [126.797021, 47.729709],\n                        [126.800271, 47.741064],\n                        [126.818001, 47.740887],\n                        [126.844239, 47.743038],\n                        [126.842136, 47.734787],\n                        [126.848779, 47.731225],\n                        [126.864981, 47.732036],\n                        [126.868326, 47.727416],\n                        [126.883428, 47.726781],\n                        [126.888829, 47.729356],\n                        [126.903883, 47.716658],\n                        [126.917934, 47.713518],\n                        [126.941639, 47.715494],\n                        [126.966729, 47.722196],\n                        [126.983791, 47.722972],\n                        [126.98446, 47.731754],\n                        [126.996934, 47.734787],\n                        [126.997172, 47.740852],\n                        [127.01973, 47.749737],\n                        [127.03359, 47.749596],\n                        [127.039707, 47.754531],\n                        [127.059302, 47.758972],\n                        [127.071728, 47.764295],\n                        [127.076507, 47.76151],\n                        [127.084536, 47.766515],\n                        [127.102123, 47.769123],\n                        [127.107858, 47.775677],\n                        [127.119854, 47.78068],\n                        [127.136485, 47.7761],\n                        [127.150202, 47.788678],\n                        [127.156128, 47.785718],\n                        [127.17037, 47.786811],\n                        [127.186093, 47.777615],\n                        [127.190442, 47.781174],\n                        [127.207026, 47.77994],\n                        [127.213239, 47.782653],\n                        [127.227194, 47.780786],\n                        [127.246263, 47.772435],\n                        [127.253384, 47.772611],\n                        [127.280577, 47.781737],\n                        [127.283684, 47.788959],\n                        [127.30844, 47.790051],\n                        [127.311355, 47.793151],\n                        [127.325836, 47.786564],\n                        [127.33635, 47.790897],\n                        [127.340508, 47.786071],\n                        [127.364547, 47.78755],\n                        [127.366841, 47.784521],\n                        [127.405839, 47.784697],\n                        [127.424335, 47.795265],\n                        [127.447705, 47.801957],\n                        [127.464289, 47.800513],\n                        [127.462759, 47.804563],\n                        [127.488328, 47.813155],\n                        [127.489236, 47.818436],\n                        [127.507922, 47.822555],\n                        [127.513132, 47.828857],\n                        [127.541663, 47.832623],\n                        [127.582, 47.826181],\n                        [127.60948, 47.833538],\n                        [127.623626, 47.833714],\n                        [127.629074, 47.837515],\n                        [127.660712, 47.83938],\n                        [127.665635, 47.82604],\n                        [127.663819, 47.814775],\n                        [127.677201, 47.803119],\n                        [127.679447, 47.791637],\n                        [127.687285, 47.787691],\n                        [127.692972, 47.777157],\n                        [127.708265, 47.795617],\n                        [127.721981, 47.797448],\n                        [127.732066, 47.805654],\n                        [127.746451, 47.810444],\n                        [127.765424, 47.82252],\n                        [127.764086, 47.840964],\n                        [127.78693, 47.846102],\n                        [127.805569, 47.864537],\n                        [127.812069, 47.86714],\n                        [127.803657, 47.87952],\n                        [127.802941, 47.894534],\n                        [127.812833, 47.898787],\n                        [127.81594, 47.912706],\n                        [127.851019, 47.923213],\n                        [127.846861, 47.931926],\n                        [127.850015, 47.940637],\n                        [127.8655, 47.940708],\n                        [127.871235, 47.951174],\n                        [127.881558, 47.948996],\n                        [127.891929, 47.954966],\n                        [127.887341, 47.962375],\n                        [127.897903, 47.965991],\n                        [127.890734, 47.977154],\n                        [127.892885, 47.982734],\n                        [127.876444, 47.997262],\n                        [127.889348, 48.009856],\n                        [127.876062, 48.02911],\n                        [127.861342, 48.030688],\n                        [127.842512, 48.027111],\n                        [127.827553, 48.049373],\n                        [127.820241, 48.054841],\n                        [127.82244, 48.070294],\n                        [127.818951, 48.080909],\n                        [127.806621, 48.087704],\n                        [127.805139, 48.099436],\n                        [127.810348, 48.113685],\n                        [127.803419, 48.118761],\n                        [127.811256, 48.126391],\n                        [127.8168, 48.126321],\n                        [127.819477, 48.13619],\n                        [127.814793, 48.144622],\n                        [127.813168, 48.15802],\n                        [127.804518, 48.166273],\n                        [127.785067, 48.176484],\n                        [127.774074, 48.184525],\n                        [127.768674, 48.193019],\n                        [127.751517, 48.191691],\n                        [127.729389, 48.195955],\n                        [127.723272, 48.193963],\n                        [127.702482, 48.204307],\n                        [127.686807, 48.24385],\n                        [127.67419, 48.248284],\n                        [127.665922, 48.257152],\n                        [127.666447, 48.266087],\n                        [127.653783, 48.270659],\n                        [127.653974, 48.277883],\n                        [127.644224, 48.281966],\n                        [127.63567, 48.290967],\n                        [127.634093, 48.304536],\n                        [127.626302, 48.318554],\n                        [127.633137, 48.336751],\n                        [127.62095, 48.345429],\n                        [127.617748, 48.361945],\n                        [127.606278, 48.366508],\n                        [127.604175, 48.377097],\n                        [127.61727, 48.395064],\n                        [127.612921, 48.399833],\n                        [127.593135, 48.410589],\n                        [127.585249, 48.41125],\n                        [127.581665, 48.416923],\n                        [127.558438, 48.418454],\n                        [127.546395, 48.426666],\n                        [127.541472, 48.438007],\n                        [127.519584, 48.433485],\n                        [127.502809, 48.435015],\n                        [127.493011, 48.430562],\n                        [127.483835, 48.434076],\n                        [127.462234, 48.432093],\n                        [127.442352, 48.437416],\n                        [127.424717, 48.43418],\n                        [127.411335, 48.446389],\n                        [127.411574, 48.450737],\n                        [127.389447, 48.453762],\n                        [127.381657, 48.448267],\n                        [127.360676, 48.455118],\n                        [127.345478, 48.469269],\n                        [127.322013, 48.471355],\n                        [127.317998, 48.467774],\n                        [127.305333, 48.468921],\n                        [127.289514, 48.458004],\n                        [127.278427, 48.455083],\n                        [127.259406, 48.458526],\n                        [127.245833, 48.458213],\n                        [127.227194, 48.470625],\n                        [127.218926, 48.468157],\n                        [127.212522, 48.473406],\n                        [127.201434, 48.47344],\n                        [127.19374, 48.480635],\n                        [127.196846, 48.488073],\n                        [127.186762, 48.484285],\n                        [127.170991, 48.486161],\n                        [127.164587, 48.497003],\n                        [127.155172, 48.50218],\n                        [127.157322, 48.510725],\n                        [127.14418, 48.517811],\n                        [127.114023, 48.518158],\n                        [127.113259, 48.523853],\n                        [127.094572, 48.518714],\n                        [127.078849, 48.531249],\n                        [127.082481, 48.538332],\n                        [127.071393, 48.537811],\n                        [127.059971, 48.545067],\n                        [127.048692, 48.541456],\n                        [127.039325, 48.542394],\n                        [127.035645, 48.547531],\n                        [127.015668, 48.554264],\n                        [127.006014, 48.544754],\n                        [126.991915, 48.542081],\n                        [126.984986, 48.545796],\n                        [126.981975, 48.53861],\n                        [126.971174, 48.539929],\n                        [126.9706, 48.546872],\n                        [126.961138, 48.541109],\n                        [126.9414, 48.541665],\n                        [126.934518, 48.54826],\n                        [126.931316, 48.541665],\n                        [126.935712, 48.526111],\n                        [126.941973, 48.51472],\n                        [126.941017, 48.508571],\n                        [126.931793, 48.492277],\n                        [126.900155, 48.487239],\n                        [126.886391, 48.488594],\n                        [126.881086, 48.485571],\n                        [126.866653, 48.490992],\n                        [126.850213, 48.489011],\n                        [126.839125, 48.477924],\n                        [126.809256, 48.475248],\n                        [126.812266, 48.458491],\n                        [126.816998, 48.450945],\n                        [126.812935, 48.434563],\n                        [126.802947, 48.430945],\n                        [126.793484, 48.423325],\n                        [126.783161, 48.420472],\n                        [126.766721, 48.406343],\n                        [126.745645, 48.41654],\n                        [126.724521, 48.419707],\n                        [126.694364, 48.417584],\n                        [126.670564, 48.410832],\n                        [126.66545, 48.417862],\n                        [126.656035, 48.421829],\n                        [126.620765, 48.42263],\n                        [126.61742, 48.433728],\n                        [126.602461, 48.453136],\n                        [126.585017, 48.456544],\n                        [126.581576, 48.461899],\n                        [126.569246, 48.465132],\n                        [126.566378, 48.473336],\n                        [126.55314, 48.473823],\n                        [126.556103, 48.468921],\n                        [126.550416, 48.455014],\n                        [126.520546, 48.431467],\n                        [126.512039, 48.418941],\n                        [126.501142, 48.415705],\n                        [126.489386, 48.420055],\n                        [126.463483, 48.418002],\n                        [126.458608, 48.414139],\n                        [126.463769, 48.391548],\n                        [126.453876, 48.386638],\n                        [126.449097, 48.373614],\n                        [126.428642, 48.371524],\n                        [126.413301, 48.372046],\n                        [126.415786, 48.362467],\n                        [126.422477, 48.364314],\n                        [126.429168, 48.351144],\n                        [126.412393, 48.344941],\n                        [126.414974, 48.332534],\n                        [126.403361, 48.332394],\n                        [126.395953, 48.337971],\n                        [126.392321, 48.346196],\n                        [126.383097, 48.345603],\n                        [126.381329, 48.352085],\n                        [126.375259, 48.350412],\n                        [126.378318, 48.344488],\n                        [126.369572, 48.34048],\n                        [126.367182, 48.348147],\n                        [126.363024, 48.343722],\n                        [126.351076, 48.344],\n                        [126.344959, 48.338145],\n                        [126.347827, 48.329396],\n                        [126.35533, 48.322319],\n                        [126.354661, 48.315242],\n                        [126.3127, 48.300385],\n                        [126.293918, 48.288455],\n                        [126.284981, 48.273242]\n                    ]\n                ]\n            ]\n        }\n    }, {\n        \"type\": \"Feature\",\n        \"properties\": { \"adcode\": 231182, \"name\": \"五大连池市\", \"center\": [126.197694, 48.512688], \"centroid\": [127.185552, 48.685818], \"childrenNum\": 0, \"level\": \"district\", \"parent\": { \"adcode\": 231100 }, \"subFeatureIndex\": 4, \"acroutes\": [100000, 230000, 231100] },\n        \"geometry\": {\n            \"type\": \"MultiPolygon\",\n            \"coordinates\": [\n                [\n                    [\n                        [125.998899, 48.747273],\n                        [126.007884, 48.73818],\n                        [126.004252, 48.72618],\n                        [125.989676, 48.719677],\n                        [125.989915, 48.711443],\n                        [125.972853, 48.705907],\n                        [125.955743, 48.710544],\n                        [125.873733, 48.668489],\n                        [125.892467, 48.6369],\n                        [125.867663, 48.63787],\n                        [125.857484, 48.622761],\n                        [125.842238, 48.625187],\n                        [125.841665, 48.612501],\n                        [125.827184, 48.610283],\n                        [125.816096, 48.600228],\n                        [125.805582, 48.603037],\n                        [125.786991, 48.602066],\n                        [125.782212, 48.606434],\n                        [125.774135, 48.601026],\n                        [125.767157, 48.588819],\n                        [125.748997, 48.596345],\n                        [125.726439, 48.598321],\n                        [125.72988, 48.610768],\n                        [125.715399, 48.60952],\n                        [125.70694, 48.561517],\n                        [125.707848, 48.559019],\n                        [125.724288, 48.565681],\n                        [125.737718, 48.556798],\n                        [125.755687, 48.560858],\n                        [125.776525, 48.556867],\n                        [125.799751, 48.556312],\n                        [125.800373, 48.508676],\n                        [125.808067, 48.489602],\n                        [125.799226, 48.495161],\n                        [125.792009, 48.493598],\n                        [125.789667, 48.486266],\n                        [125.801376, 48.47709],\n                        [125.807685, 48.452927],\n                        [125.806203, 48.449659],\n                        [125.820493, 48.392975],\n                        [125.83722, 48.372081],\n                        [125.843433, 48.357973],\n                        [125.860781, 48.352991],\n                        [125.878799, 48.353095],\n                        [125.906805, 48.343896],\n                        [125.912253, 48.339783],\n                        [125.948718, 48.341561],\n                        [125.947141, 48.318589],\n                        [125.983558, 48.306454],\n                        [126.000524, 48.307326],\n                        [126.016343, 48.300978],\n                        [126.027957, 48.293269],\n                        [126.036177, 48.293548],\n                        [126.043585, 48.285176],\n                        [126.051136, 48.282803],\n                        [126.11293, 48.286292],\n                        [126.155369, 48.291176],\n                        [126.199051, 48.274533],\n                        [126.212385, 48.281233],\n                        [126.244644, 48.283885],\n                        [126.267298, 48.274219],\n                        [126.284981, 48.273242],\n                        [126.293918, 48.288455],\n                        [126.3127, 48.300385],\n                        [126.354661, 48.315242],\n                        [126.35533, 48.322319],\n                        [126.347827, 48.329396],\n                        [126.344959, 48.338145],\n                        [126.351076, 48.344],\n                        [126.363024, 48.343722],\n                        [126.367182, 48.348147],\n                        [126.369572, 48.34048],\n                        [126.378318, 48.344488],\n                        [126.375259, 48.350412],\n                        [126.381329, 48.352085],\n                        [126.383097, 48.345603],\n                        [126.392321, 48.346196],\n                        [126.395953, 48.337971],\n                        [126.403361, 48.332394],\n                        [126.414974, 48.332534],\n                        [126.412393, 48.344941],\n                        [126.429168, 48.351144],\n                        [126.422477, 48.364314],\n                        [126.415786, 48.362467],\n                        [126.413301, 48.372046],\n                        [126.428642, 48.371524],\n                        [126.449097, 48.373614],\n                        [126.453876, 48.386638],\n                        [126.463769, 48.391548],\n                        [126.458608, 48.414139],\n                        [126.463483, 48.418002],\n                        [126.489386, 48.420055],\n                        [126.501142, 48.415705],\n                        [126.512039, 48.418941],\n                        [126.520546, 48.431467],\n                        [126.550416, 48.455014],\n                        [126.556103, 48.468921],\n                        [126.55314, 48.473823],\n                        [126.566378, 48.473336],\n                        [126.569246, 48.465132],\n                        [126.581576, 48.461899],\n                        [126.585017, 48.456544],\n                        [126.602461, 48.453136],\n                        [126.61742, 48.433728],\n                        [126.620765, 48.42263],\n                        [126.656035, 48.421829],\n                        [126.66545, 48.417862],\n                        [126.670564, 48.410832],\n                        [126.694364, 48.417584],\n                        [126.724521, 48.419707],\n                        [126.745645, 48.41654],\n                        [126.766721, 48.406343],\n                        [126.783161, 48.420472],\n                        [126.793484, 48.423325],\n                        [126.802947, 48.430945],\n                        [126.812935, 48.434563],\n                        [126.816998, 48.450945],\n                        [126.812266, 48.458491],\n                        [126.809256, 48.475248],\n                        [126.839125, 48.477924],\n                        [126.850213, 48.489011],\n                        [126.866653, 48.490992],\n                        [126.881086, 48.485571],\n                        [126.886391, 48.488594],\n                        [126.900155, 48.487239],\n                        [126.931793, 48.492277],\n                        [126.941017, 48.508571],\n                        [126.941973, 48.51472],\n                        [126.935712, 48.526111],\n                        [126.931316, 48.541665],\n                        [126.934518, 48.54826],\n                        [126.9414, 48.541665],\n                        [126.961138, 48.541109],\n                        [126.9706, 48.546872],\n                        [126.971174, 48.539929],\n                        [126.981975, 48.53861],\n                        [126.984986, 48.545796],\n                        [126.991915, 48.542081],\n                        [127.006014, 48.544754],\n                        [127.015668, 48.554264],\n                        [127.035645, 48.547531],\n                        [127.039325, 48.542394],\n                        [127.048692, 48.541456],\n                        [127.059971, 48.545067],\n                        [127.071393, 48.537811],\n                        [127.082481, 48.538332],\n                        [127.078849, 48.531249],\n                        [127.094572, 48.518714],\n                        [127.113259, 48.523853],\n                        [127.114023, 48.518158],\n                        [127.14418, 48.517811],\n                        [127.157322, 48.510725],\n                        [127.155172, 48.50218],\n                        [127.164587, 48.497003],\n                        [127.170991, 48.486161],\n                        [127.186762, 48.484285],\n                        [127.196846, 48.488073],\n                        [127.19374, 48.480635],\n                        [127.201434, 48.47344],\n                        [127.212522, 48.473406],\n                        [127.218926, 48.468157],\n                        [127.227194, 48.470625],\n                        [127.245833, 48.458213],\n                        [127.259406, 48.458526],\n                        [127.278427, 48.455083],\n                        [127.289514, 48.458004],\n                        [127.305333, 48.468921],\n                        [127.317998, 48.467774],\n                        [127.322013, 48.471355],\n                        [127.345478, 48.469269],\n                        [127.360676, 48.455118],\n                        [127.381657, 48.448267],\n                        [127.389447, 48.453762],\n                        [127.411574, 48.450737],\n                        [127.411335, 48.446389],\n                        [127.424717, 48.43418],\n                        [127.442352, 48.437416],\n                        [127.462234, 48.432093],\n                        [127.483835, 48.434076],\n                        [127.493011, 48.430562],\n                        [127.502809, 48.435015],\n                        [127.519584, 48.433485],\n                        [127.541472, 48.438007],\n                        [127.546395, 48.426666],\n                        [127.558438, 48.418454],\n                        [127.581665, 48.416923],\n                        [127.585249, 48.41125],\n                        [127.593135, 48.410589],\n                        [127.612921, 48.399833],\n                        [127.61727, 48.395064],\n                        [127.604175, 48.377097],\n                        [127.606278, 48.366508],\n                        [127.617748, 48.361945],\n                        [127.62095, 48.345429],\n                        [127.633137, 48.336751],\n                        [127.626302, 48.318554],\n                        [127.634093, 48.304536],\n                        [127.63567, 48.290967],\n                        [127.644224, 48.281966],\n                        [127.653974, 48.277883],\n                        [127.653783, 48.270659],\n                        [127.666447, 48.266087],\n                        [127.665922, 48.257152],\n                        [127.67419, 48.248284],\n                        [127.686807, 48.24385],\n                        [127.702482, 48.204307],\n                        [127.723272, 48.193963],\n                        [127.729389, 48.195955],\n                        [127.751517, 48.191691],\n                        [127.768674, 48.193019],\n                        [127.774074, 48.184525],\n                        [127.785067, 48.176484],\n                        [127.804518, 48.166273],\n                        [127.813168, 48.15802],\n                        [127.814793, 48.144622],\n                        [127.819477, 48.13619],\n                        [127.8168, 48.126321],\n                        [127.811256, 48.126391],\n                        [127.803419, 48.118761],\n                        [127.810348, 48.113685],\n                        [127.805139, 48.099436],\n                        [127.806621, 48.087704],\n                        [127.818951, 48.080909],\n                        [127.82244, 48.070294],\n                        [127.820241, 48.054841],\n                        [127.827553, 48.049373],\n                        [127.842512, 48.027111],\n                        [127.861342, 48.030688],\n                        [127.868272, 48.038436],\n                        [127.86679, 48.04755],\n                        [127.880507, 48.060868],\n                        [127.892407, 48.063286],\n                        [127.897186, 48.068016],\n                        [127.918214, 48.073692],\n                        [127.938526, 48.082905],\n                        [127.950043, 48.084166],\n                        [127.959172, 48.08991],\n                        [127.967918, 48.088544],\n                        [127.980774, 48.097685],\n                        [127.98756, 48.088544],\n                        [128.005004, 48.07632],\n                        [128.023929, 48.076355],\n                        [128.032054, 48.071765],\n                        [128.050215, 48.071555],\n                        [128.056571, 48.078737],\n                        [128.074684, 48.069208],\n                        [128.076978, 48.064933],\n                        [128.090647, 48.061394],\n                        [128.09686, 48.063496],\n                        [128.096095, 48.073517],\n                        [128.102308, 48.079963],\n                        [128.117649, 48.085602],\n                        [128.13213, 48.078281],\n                        [128.152011, 48.086548],\n                        [128.157173, 48.091977],\n                        [128.170889, 48.095549],\n                        [128.170554, 48.112705],\n                        [128.174139, 48.119146],\n                        [128.19402, 48.126076],\n                        [128.21156, 48.13486],\n                        [128.231823, 48.13619],\n                        [128.233209, 48.142663],\n                        [128.246926, 48.141333],\n                        [128.253712, 48.150499],\n                        [128.264513, 48.149974],\n                        [128.276748, 48.15753],\n                        [128.303081, 48.147386],\n                        [128.314025, 48.1491],\n                        [128.335675, 48.148575],\n                        [128.339259, 48.15683],\n                        [128.356177, 48.171763],\n                        [128.350968, 48.181134],\n                        [128.343943, 48.181728],\n                        [128.341792, 48.202106],\n                        [128.349582, 48.209164],\n                        [128.344516, 48.221218],\n                        [128.355269, 48.224886],\n                        [128.371949, 48.260433],\n                        [128.378066, 48.26511],\n                        [128.391304, 48.266297],\n                        [128.397565, 48.27223],\n                        [128.391926, 48.274324],\n                        [128.39508, 48.285873],\n                        [128.390444, 48.288978],\n                        [128.397947, 48.29749],\n                        [128.408557, 48.301048],\n                        [128.4098, 48.312243],\n                        [128.422751, 48.320541],\n                        [128.431879, 48.330198],\n                        [128.428343, 48.351492],\n                        [128.443445, 48.377027],\n                        [128.440721, 48.399833],\n                        [128.433839, 48.429901],\n                        [128.421509, 48.464541],\n                        [128.40502, 48.47636],\n                        [128.361052, 48.495474],\n                        [128.299257, 48.531874],\n                        [128.299257, 48.550065],\n                        [128.285541, 48.577304],\n                        [128.253951, 48.603661],\n                        [128.25591, 48.629242],\n                        [128.237463, 48.631806],\n                        [128.204486, 48.628168],\n                        [128.183888, 48.633608],\n                        [128.178392, 48.643587],\n                        [128.188046, 48.64906],\n                        [128.252565, 48.652662],\n                        [128.25505, 48.669736],\n                        [128.267667, 48.67808],\n                        [128.291037, 48.700717],\n                        [128.291037, 48.72701],\n                        [128.260833, 48.744231],\n                        [128.249841, 48.759614],\n                        [128.258061, 48.763243],\n                        [128.289651, 48.765939],\n                        [128.351446, 48.776824],\n                        [128.358328, 48.781316],\n                        [128.365162, 48.793994],\n                        [128.366548, 48.814818],\n                        [128.38165, 48.819341],\n                        [128.391256, 48.827453],\n                        [128.402296, 48.875616],\n                        [128.388867, 48.894648],\n                        [128.361769, 48.946914],\n                        [128.359523, 48.994756],\n                        [128.35331, 48.998196],\n                        [128.35006, 49.026121],\n                        [128.054946, 49.061623],\n                        [128.004669, 49.083022],\n                        [127.971167, 49.103932],\n                        [127.912862, 49.061176],\n                        [127.812595, 49.057569],\n                        [127.683509, 49.070177],\n                        [127.547542, 49.103966],\n                        [127.542428, 49.095933],\n                        [127.532248, 49.093701],\n                        [127.5268, 49.088276],\n                        [127.530289, 49.07423],\n                        [127.519153, 49.059321],\n                        [127.517959, 49.047913],\n                        [127.503334, 49.042105],\n                        [127.484074, 49.030315],\n                        [127.449139, 49.025708],\n                        [127.437047, 49.029765],\n                        [127.422614, 49.043411],\n                        [127.408659, 49.050353],\n                        [127.404358, 49.057225],\n                        [127.378837, 49.058359],\n                        [127.363257, 49.0486],\n                        [127.349541, 49.036193],\n                        [127.294294, 49.001773],\n                        [127.272166, 49.005041],\n                        [127.272262, 49.00167],\n                        [127.259453, 48.998093],\n                        [127.252954, 48.986842],\n                        [127.24459, 48.98784],\n                        [127.216775, 48.984709],\n                        [127.208125, 48.988597],\n                        [127.198567, 48.985776],\n                        [127.187861, 48.992554],\n                        [127.188483, 48.999469],\n                        [127.161719, 49.008549],\n                        [127.156032, 49.017903],\n                        [127.166116, 49.029455],\n                        [127.172425, 49.052174],\n                        [127.143797, 49.047226],\n                        [127.131324, 49.054889],\n                        [127.112255, 49.04781],\n                        [127.102697, 49.050868],\n                        [127.102983, 49.058874],\n                        [127.075694, 49.069352],\n                        [127.064941, 49.079038],\n                        [127.047497, 49.080687],\n                        [127.029432, 49.089821],\n                        [127.011988, 49.102353],\n                        [127.01022, 49.11416],\n                        [126.995882, 49.11313],\n                        [126.9825, 49.105614],\n                        [126.964387, 49.108119],\n                        [126.962667, 49.114675],\n                        [126.948951, 49.122739],\n                        [126.958127, 49.130836],\n                        [126.956597, 49.135262],\n                        [126.932558, 49.148365],\n                        [126.912151, 49.146547],\n                        [126.904218, 49.15104],\n                        [126.895806, 49.160881],\n                        [126.905604, 49.16863],\n                        [126.909284, 49.175691],\n                        [126.906368, 49.184842],\n                        [126.879318, 49.196664],\n                        [126.850834, 49.197795],\n                        [126.831479, 49.185048],\n                        [126.818049, 49.179119],\n                        [126.809972, 49.16143],\n                        [126.8018, 49.161053],\n                        [126.776136, 49.14809],\n                        [126.775371, 49.141094],\n                        [126.763041, 49.140613],\n                        [126.745023, 49.13437],\n                        [126.728392, 49.140099],\n                        [126.72065, 49.15056],\n                        [126.713624, 49.153646],\n                        [126.70354, 49.167601],\n                        [126.704735, 49.170481],\n                        [126.696515, 49.188578],\n                        [126.690302, 49.1894],\n                        [126.68753, 49.18306],\n                        [126.670994, 49.187584],\n                        [126.661531, 49.182889],\n                        [126.651161, 49.183232],\n                        [126.64404, 49.173395],\n                        [126.632187, 49.175006],\n                        [126.623967, 49.169795],\n                        [126.611398, 49.170069],\n                        [126.608291, 49.176171],\n                        [126.597825, 49.171372],\n                        [126.599402, 49.168115],\n                        [126.573547, 49.163967],\n                        [126.560882, 49.15879],\n                        [126.558636, 49.16527],\n                        [126.550416, 49.165167],\n                        [126.551037, 49.177371],\n                        [126.532159, 49.181072],\n                        [126.535313, 49.189263],\n                        [126.52781, 49.191388],\n                        [126.517965, 49.201803],\n                        [126.513425, 49.198926],\n                        [126.472324, 49.201392],\n                        [126.470938, 49.193204],\n                        [126.458608, 49.192655],\n                        [126.459946, 49.184157],\n                        [126.453446, 49.172949],\n                        [126.441307, 49.172675],\n                        [126.444079, 49.16767],\n                        [126.419801, 49.166058],\n                        [126.409048, 49.158755],\n                        [126.40857, 49.153303],\n                        [126.393611, 49.153372],\n                        [126.383049, 49.157761],\n                        [126.380516, 49.16455],\n                        [126.361017, 49.161498],\n                        [126.347253, 49.141471],\n                        [126.338364, 49.139378],\n                        [126.334493, 49.131008],\n                        [126.314564, 49.132655],\n                        [126.298792, 49.137183],\n                        [126.298362, 49.145964],\n                        [126.284455, 49.145552],\n                        [126.274132, 49.15272],\n                        [126.256258, 49.151246],\n                        [126.256306, 49.155669],\n                        [126.230546, 49.151554],\n                        [126.228395, 49.140991],\n                        [126.235421, 49.140339],\n                        [126.233604, 49.134061],\n                        [126.224859, 49.13437],\n                        [126.213819, 49.126754],\n                        [126.22094, 49.117454],\n                        [126.218454, 49.112444],\n                        [126.223138, 49.10376],\n                        [126.21683, 49.095589],\n                        [126.228347, 49.096379],\n                        [126.229255, 49.0834],\n                        [126.223329, 49.069215],\n                        [126.212528, 49.065127],\n                        [126.198717, 49.067188],\n                        [126.187438, 49.06073],\n                        [126.175012, 49.062654],\n                        [126.1677, 49.057706],\n                        [126.161965, 49.061657],\n                        [126.157807, 49.055576],\n                        [126.169277, 49.05403],\n                        [126.167174, 49.020069],\n                        [126.175968, 49.014223],\n                        [126.15991, 48.985879],\n                        [126.17353, 48.973387],\n                        [126.166983, 48.964369],\n                        [126.131904, 48.953835],\n                        [126.11121, 48.954626],\n                        [126.091998, 48.966848],\n                        [126.08416, 48.964059],\n                        [126.08005, 48.954867],\n                        [126.090325, 48.94316],\n                        [126.079333, 48.936754],\n                        [126.081483, 48.933379],\n                        [126.063944, 48.926524],\n                        [126.052474, 48.916808],\n                        [126.038184, 48.910847],\n                        [126.024898, 48.910399],\n                        [126.024038, 48.907056],\n                        [126.003965, 48.90616],\n                        [125.999521, 48.903679],\n                        [126.001576, 48.890959],\n                        [125.980882, 48.886029],\n                        [125.976294, 48.873789],\n                        [125.968313, 48.86527],\n                        [125.9794, 48.857405],\n                        [125.976963, 48.835391],\n                        [125.970463, 48.831905],\n                        [125.96382, 48.816026],\n                        [125.963773, 48.803665],\n                        [125.968934, 48.797552],\n                        [125.965875, 48.791162],\n                        [125.966783, 48.77907],\n                        [125.979831, 48.776721],\n                        [125.983128, 48.769706],\n                        [125.997991, 48.769844],\n                        [126.002627, 48.755743],\n                        [125.998899, 48.747273]\n                    ]\n                ]\n            ]\n        }\n    }, {\n        \"type\": \"Feature\",\n        \"properties\": { \"adcode\": 231183, \"name\": \"嫩江市\", \"center\": [125.229904, 49.177461], \"centroid\": [125.76615, 49.604607], \"childrenNum\": 0, \"level\": \"district\", \"parent\": { \"adcode\": 231100 }, \"subFeatureIndex\": 5, \"acroutes\": [100000, 230000, 231100] },\n        \"geometry\": {\n            \"type\": \"MultiPolygon\",\n            \"coordinates\": [\n                [\n                    [\n                        [126.334015, 50.982991],\n                        [126.335162, 50.988932],\n                        [126.310023, 50.995269],\n                        [126.276091, 50.994873],\n                        [126.256927, 50.998371],\n                        [126.243641, 51.003155],\n                        [126.225098, 51.001835],\n                        [126.215013, 50.990582],\n                        [126.195658, 50.981704],\n                        [126.181559, 50.970314],\n                        [126.163112, 50.976224],\n                        [126.159288, 50.971767],\n                        [126.144855, 50.975167],\n                        [126.129849, 50.974837],\n                        [126.135966, 50.957039],\n                        [126.13133, 50.945808],\n                        [126.103754, 50.923042],\n                        [126.06858, 50.921356],\n                        [126.056488, 50.918943],\n                        [126.029438, 50.924793],\n                        [126.020023, 50.920794],\n                        [126.019976, 50.915737],\n                        [126.029821, 50.907802],\n                        [126.021457, 50.901619],\n                        [126.016152, 50.909356],\n                        [126.006642, 50.904827],\n                        [125.996605, 50.906678],\n                        [125.995172, 50.902678],\n                        [126.004395, 50.898875],\n                        [125.994455, 50.8957],\n                        [125.99498, 50.888192],\n                        [126.001241, 50.882271],\n                        [125.997513, 50.87271],\n                        [125.982125, 50.871685],\n                        [125.973952, 50.878897],\n                        [125.975481, 50.894179],\n                        [125.968122, 50.900363],\n                        [125.958898, 50.900231],\n                        [125.955027, 50.895105],\n                        [125.949244, 50.877045],\n                        [125.942027, 50.870394],\n                        [125.955696, 50.866324],\n                        [125.945564, 50.855436],\n                        [125.92401, 50.853847],\n                        [125.907904, 50.858812],\n                        [125.894713, 50.857686],\n                        [125.916124, 50.85153],\n                        [125.924774, 50.844909],\n                        [125.920473, 50.831366],\n                        [125.905514, 50.823914],\n                        [125.887831, 50.823053],\n                        [125.878417, 50.816627],\n                        [125.888644, 50.809537],\n                        [125.889456, 50.804866],\n                        [125.863362, 50.791444],\n                        [125.841091, 50.794493],\n                        [125.832536, 50.786207],\n                        [125.834305, 50.779179],\n                        [125.846587, 50.768635],\n                        [125.839896, 50.756264],\n                        [125.828426, 50.756961],\n                        [125.826658, 50.761704],\n                        [125.812607, 50.767242],\n                        [125.803909, 50.77341],\n                        [125.794924, 50.773244],\n                        [125.778006, 50.767176],\n                        [125.762044, 50.75444],\n                        [125.758651, 50.747208],\n                        [125.765485, 50.744919],\n                        [125.779966, 50.747506],\n                        [125.795594, 50.738681],\n                        [125.794016, 50.73367],\n                        [125.784267, 50.730783],\n                        [125.78073, 50.725705],\n                        [125.793347, 50.718801],\n                        [125.802237, 50.719033],\n                        [125.804913, 50.713422],\n                        [125.817817, 50.710102],\n                        [125.825129, 50.704922],\n                        [125.819155, 50.689811],\n                        [125.814089, 50.686589],\n                        [125.788616, 50.67938],\n                        [125.790623, 50.673333],\n                        [125.803861, 50.663762],\n                        [125.803623, 50.65758],\n                        [125.793538, 50.650932],\n                        [125.792869, 50.645645],\n                        [125.801376, 50.632012],\n                        [125.814662, 50.622699],\n                        [125.808115, 50.603701],\n                        [125.816813, 50.595514],\n                        [125.818486, 50.584396],\n                        [125.823982, 50.578503],\n                        [125.829287, 50.561653],\n                        [125.822309, 50.558755],\n                        [125.822739, 50.548428],\n                        [125.811604, 50.547895],\n                        [125.799512, 50.540364],\n                        [125.794877, 50.5328],\n                        [125.786131, 50.529967],\n                        [125.770646, 50.531366],\n                        [125.767396, 50.526167],\n                        [125.772988, 50.5196],\n                        [125.762904, 50.516399],\n                        [125.764338, 50.512165],\n                        [125.75282, 50.50693],\n                        [125.740155, 50.510498],\n                        [125.752007, 50.513265],\n                        [125.752246, 50.519033],\n                        [125.740585, 50.523334],\n                        [125.72295, 50.516099],\n                        [125.704455, 50.498393],\n                        [125.686151, 50.495624],\n                        [125.681945, 50.488553],\n                        [125.699245, 50.486918],\n                        [125.695565, 50.480646],\n                        [125.685386, 50.481447],\n                        [125.674537, 50.476942],\n                        [125.666556, 50.478811],\n                        [125.65456, 50.471236],\n                        [125.646483, 50.45151],\n                        [125.632194, 50.443897],\n                        [125.621775, 50.44343],\n                        [125.615753, 50.44954],\n                        [125.590758, 50.452311],\n                        [125.580196, 50.44934],\n                        [125.577663, 50.443563],\n                        [125.58856, 50.441727],\n                        [125.586314, 50.437419],\n                        [125.568631, 50.442295],\n                        [125.562561, 50.440224],\n                        [125.565381, 50.428969],\n                        [125.583637, 50.408056],\n                        [125.574844, 50.401238],\n                        [125.560841, 50.40575],\n                        [125.536515, 50.420017],\n                        [125.527339, 50.419616],\n                        [125.525618, 50.413736],\n                        [125.513527, 50.406686],\n                        [125.524232, 50.403812],\n                        [125.53704, 50.381249],\n                        [125.523802, 50.373859],\n                        [125.52428, 50.366067],\n                        [125.542059, 50.363491],\n                        [125.546694, 50.360414],\n                        [125.539908, 50.352921],\n                        [125.522512, 50.351014],\n                        [125.517685, 50.34014],\n                        [125.530493, 50.331037],\n                        [125.526287, 50.326284],\n                        [125.506167, 50.324677],\n                        [125.504638, 50.317613],\n                        [125.519596, 50.315939],\n                        [125.520887, 50.311687],\n                        [125.512284, 50.309711],\n                        [125.499763, 50.312825],\n                        [125.488532, 50.307836],\n                        [125.479547, 50.297186],\n                        [125.463346, 50.295779],\n                        [125.466022, 50.266999],\n                        [125.450251, 50.267468],\n                        [125.442317, 50.260295],\n                        [125.451111, 50.253088],\n                        [125.449247, 50.239475],\n                        [125.464588, 50.229916],\n                        [125.457754, 50.226931],\n                        [125.444707, 50.232298],\n                        [125.436295, 50.231258],\n                        [125.435818, 50.224516],\n                        [125.44767, 50.217538],\n                        [125.443464, 50.2067],\n                        [125.426164, 50.201599],\n                        [125.417609, 50.195826],\n                        [125.409914, 50.195289],\n                        [125.389029, 50.199216],\n                        [125.383008, 50.191328],\n                        [125.388313, 50.180988],\n                        [125.382864, 50.172358],\n                        [125.370725, 50.174776],\n                        [125.367523, 50.168597],\n                        [125.35634, 50.163962],\n                        [125.334356, 50.165003],\n                        [125.337797, 50.158991],\n                        [125.357822, 50.148677],\n                        [125.376269, 50.145788],\n                        [125.375982, 50.137421],\n                        [125.329577, 50.136009],\n                        [125.327761, 50.119303],\n                        [125.316673, 50.12243],\n                        [125.314379, 50.13648],\n                        [125.302479, 50.139504],\n                        [125.291821, 50.131337],\n                        [125.277627, 50.126396],\n                        [125.282884, 50.112142],\n                        [125.275237, 50.10619],\n                        [125.260613, 50.104004],\n                        [125.257746, 50.100776],\n                        [125.273613, 50.097984],\n                        [125.288954, 50.090517],\n                        [125.271988, 50.081534],\n                        [125.274521, 50.074569],\n                        [125.283792, 50.070262],\n                        [125.299277, 50.067771],\n                        [125.328143, 50.065954],\n                        [125.337367, 50.055923],\n                        [125.315956, 50.045688],\n                        [125.2966, 50.050032],\n                        [125.28556, 50.058717],\n                        [125.275046, 50.054745],\n                        [125.262859, 50.046193],\n                        [125.252154, 50.045554],\n                        [125.259371, 50.037573],\n                        [125.283458, 50.035889],\n                        [125.29402, 50.029288],\n                        [125.296792, 50.023393],\n                        [125.296505, 50.009512],\n                        [125.278201, 49.996403],\n                        [125.252584, 49.993774],\n                        [125.242022, 49.987841],\n                        [125.242166, 49.968925],\n                        [125.231556, 49.95766],\n                        [125.215115, 49.961539],\n                        [125.189977, 49.959785],\n                        [125.183621, 49.952701],\n                        [125.198245, 49.942478],\n                        [125.199344, 49.93502],\n                        [125.207947, 49.932253],\n                        [125.224865, 49.921316],\n                        [125.214399, 49.913686],\n                        [125.212917, 49.907271],\n                        [125.229405, 49.896228],\n                        [125.245272, 49.87214],\n                        [125.239633, 49.866665],\n                        [125.225056, 49.867172],\n                        [125.223957, 49.859669],\n                        [125.238247, 49.851286],\n                        [125.235092, 49.839993],\n                        [125.224196, 49.835766],\n                        [125.19901, 49.833567],\n                        [125.177838, 49.829475],\n                        [125.179272, 49.822],\n                        [125.188065, 49.815403],\n                        [125.208903, 49.807892],\n                        [125.22281, 49.799026],\n                        [125.226442, 49.789752],\n                        [125.222093, 49.784234],\n                        [125.228019, 49.774517],\n                        [125.221233, 49.754874],\n                        [125.20938, 49.745625],\n                        [125.204076, 49.73424],\n                        [125.220373, 49.72919],\n                        [125.225104, 49.723632],\n                        [125.218843, 49.71553],\n                        [125.216884, 49.705901],\n                        [125.220898, 49.672218],\n                        [125.218126, 49.667264],\n                        [125.199488, 49.649546],\n                        [125.19987, 49.638851],\n                        [125.191267, 49.631211],\n                        [125.185485, 49.634743],\n                        [125.189881, 49.649886],\n                        [125.18558, 49.656471],\n                        [125.164647, 49.669401],\n                        [125.140799, 49.67439],\n                        [125.132054, 49.672014],\n                        [125.127418, 49.655011],\n                        [125.131815, 49.640243],\n                        [125.139605, 49.626218],\n                        [125.154133, 49.616843],\n                        [125.163405, 49.619595],\n                        [125.168184, 49.629954],\n                        [125.173967, 49.630837],\n                        [125.174684, 49.623331],\n                        [125.182904, 49.614262],\n                        [125.198627, 49.604443],\n                        [125.205414, 49.594044],\n                        [125.217409, 49.592107],\n                        [125.226538, 49.595981],\n                        [125.234614, 49.592005],\n                        [125.22931, 49.584425],\n                        [125.234901, 49.576946],\n                        [125.21674, 49.567765],\n                        [125.231365, 49.562052],\n                        [125.23557, 49.540928],\n                        [125.2252, 49.535824],\n                        [125.216071, 49.541813],\n                        [125.211292, 49.539975],\n                        [125.221759, 49.523335],\n                        [125.224817, 49.50611],\n                        [125.240971, 49.503692],\n                        [125.240493, 49.494089],\n                        [125.229596, 49.492455],\n                        [125.228354, 49.486971],\n                        [125.255452, 49.468336],\n                        [125.270172, 49.455114],\n                        [125.269598, 49.447103],\n                        [125.256073, 49.435682],\n                        [125.259944, 49.42402],\n                        [125.259418, 49.405122],\n                        [125.257029, 49.394033],\n                        [125.271271, 49.38864],\n                        [125.277388, 49.379766],\n                        [125.271653, 49.372938],\n                        [125.274855, 49.361261],\n                        [125.256933, 49.359588],\n                        [125.265631, 49.34982],\n                        [125.261282, 49.347224],\n                        [125.264102, 49.339128],\n                        [125.251867, 49.331577],\n                        [125.261712, 49.322487],\n                        [125.25808, 49.313977],\n                        [125.239298, 49.301465],\n                        [125.226681, 49.28553],\n                        [125.214924, 49.280194],\n                        [125.226299, 49.273148],\n                        [125.230409, 49.262063],\n                        [125.237195, 49.257648],\n                        [125.227446, 49.248819],\n                        [125.2252, 49.240775],\n                        [125.228306, 49.230059],\n                        [125.226347, 49.217012],\n                        [125.217171, 49.207798],\n                        [125.220946, 49.191045],\n                        [125.205844, 49.185116],\n                        [125.194087, 49.187927],\n                        [125.185437, 49.185356],\n                        [125.176739, 49.171886],\n                        [125.163357, 49.164824],\n                        [125.159916, 49.159441],\n                        [125.160155, 49.146376],\n                        [125.155232, 49.143838],\n                        [125.133726, 49.141025],\n                        [125.117477, 49.125999],\n                        [125.101658, 49.129979],\n                        [125.088993, 49.135879],\n                        [125.062182, 49.14178],\n                        [125.056256, 49.146856],\n                        [125.039529, 49.151829],\n                        [125.03475, 49.162424],\n                        [125.041536, 49.170241],\n                        [125.039911, 49.176274],\n                        [125.004163, 49.172846],\n                        [124.982896, 49.162527],\n                        [124.971187, 49.165578],\n                        [124.955845, 49.164618],\n                        [124.919524, 49.181107],\n                        [124.906572, 49.183951],\n                        [124.876702, 49.17456],\n                        [124.860883, 49.166538],\n                        [124.847549, 49.129773],\n                        [124.833738, 49.120612],\n                        [124.818301, 49.11996],\n                        [124.80946, 49.11591],\n                        [124.807643, 49.108634],\n                        [124.826426, 49.083022],\n                        [124.829102, 49.071929],\n                        [124.816437, 49.060283],\n                        [124.823367, 49.046573],\n                        [124.812614, 49.036262],\n                        [124.808074, 49.020551],\n                        [124.789435, 48.999847],\n                        [124.778682, 48.99565],\n                        [124.765539, 48.981268],\n                        [124.756697, 48.96733],\n                        [124.749433, 48.950564],\n                        [124.749003, 48.941059],\n                        [124.754117, 48.933826],\n                        [124.750102, 48.924078],\n                        [124.767307, 48.924526],\n                        [124.798898, 48.932586],\n                        [124.832925, 48.929383],\n                        [124.834407, 48.920701],\n                        [124.859258, 48.91064],\n                        [124.868243, 48.903231],\n                        [124.895389, 48.896061],\n                        [124.911304, 48.896509],\n                        [124.924924, 48.893028],\n                        [124.941747, 48.897509],\n                        [124.949059, 48.894441],\n                        [124.973528, 48.892028],\n                        [124.99145, 48.885305],\n                        [125.013148, 48.891511],\n                        [125.022563, 48.861648],\n                        [125.012479, 48.847296],\n                        [125.015585, 48.825071],\n                        [124.992358, 48.82117],\n                        [124.982274, 48.824312],\n                        [124.992836, 48.813437],\n                        [125.01114, 48.803803],\n                        [125.021941, 48.801696],\n                        [125.036088, 48.790609],\n                        [125.041871, 48.790056],\n                        [125.041106, 48.770605],\n                        [125.046028, 48.746513],\n                        [125.066961, 48.739044],\n                        [125.096019, 48.749797],\n                        [125.089041, 48.75837],\n                        [125.102566, 48.748414],\n                        [125.108779, 48.739044],\n                        [125.127418, 48.734514],\n                        [125.128756, 48.725799],\n                        [125.192797, 48.732267],\n                        [125.203407, 48.729569],\n                        [125.212248, 48.734514],\n                        [125.229549, 48.715941],\n                        [125.239155, 48.717048],\n                        [125.250386, 48.712412],\n                        [125.25029, 48.726318],\n                        [125.309074, 48.726837],\n                        [125.413642, 48.792682],\n                        [125.430847, 48.786187],\n                        [125.45221, 48.783596],\n                        [125.457419, 48.786395],\n                        [125.462581, 48.798864],\n                        [125.462007, 48.810536],\n                        [125.468173, 48.826521],\n                        [125.483466, 48.828764],\n                        [125.494028, 48.834874],\n                        [125.513957, 48.85913],\n                        [125.52017, 48.859475],\n                        [125.540481, 48.868684],\n                        [125.565668, 48.872995],\n                        [125.603805, 48.882823],\n                        [125.622588, 48.891028],\n                        [125.631668, 48.856094],\n                        [125.640366, 48.853058],\n                        [125.686724, 48.811676],\n                        [125.697859, 48.803181],\n                        [125.722281, 48.803734],\n                        [125.758746, 48.821895],\n                        [125.789667, 48.826314],\n                        [125.801663, 48.851506],\n                        [125.807446, 48.853576],\n                        [125.816526, 48.868581],\n                        [125.825368, 48.866236],\n                        [125.838845, 48.867305],\n                        [125.839084, 48.86051],\n                        [125.849359, 48.852368],\n                        [125.857245, 48.837945],\n                        [125.874259, 48.825554],\n                        [125.895526, 48.816475],\n                        [125.91168, 48.806531],\n                        [125.921668, 48.795099],\n                        [125.960762, 48.779727],\n                        [125.998899, 48.747273],\n                        [126.002627, 48.755743],\n                        [125.997991, 48.769844],\n                        [125.983128, 48.769706],\n                        [125.979831, 48.776721],\n                        [125.966783, 48.77907],\n                        [125.965875, 48.791162],\n                        [125.968934, 48.797552],\n                        [125.963773, 48.803665],\n                        [125.96382, 48.816026],\n                        [125.970463, 48.831905],\n                        [125.976963, 48.835391],\n                        [125.9794, 48.857405],\n                        [125.968313, 48.86527],\n                        [125.976294, 48.873789],\n                        [125.980882, 48.886029],\n                        [126.001576, 48.890959],\n                        [125.999521, 48.903679],\n                        [126.003965, 48.90616],\n                        [126.024038, 48.907056],\n                        [126.024898, 48.910399],\n                        [126.038184, 48.910847],\n                        [126.052474, 48.916808],\n                        [126.063944, 48.926524],\n                        [126.081483, 48.933379],\n                        [126.079333, 48.936754],\n                        [126.090325, 48.94316],\n                        [126.08005, 48.954867],\n                        [126.08416, 48.964059],\n                        [126.091998, 48.966848],\n                        [126.11121, 48.954626],\n                        [126.131904, 48.953835],\n                        [126.166983, 48.964369],\n                        [126.17353, 48.973387],\n                        [126.15991, 48.985879],\n                        [126.175968, 49.014223],\n                        [126.167174, 49.020069],\n                        [126.169277, 49.05403],\n                        [126.157807, 49.055576],\n                        [126.161965, 49.061657],\n                        [126.1677, 49.057706],\n                        [126.175012, 49.062654],\n                        [126.187438, 49.06073],\n                        [126.198717, 49.067188],\n                        [126.212528, 49.065127],\n                        [126.223329, 49.069215],\n                        [126.229255, 49.0834],\n                        [126.228347, 49.096379],\n                        [126.21683, 49.095589],\n                        [126.223138, 49.10376],\n                        [126.218454, 49.112444],\n                        [126.22094, 49.117454],\n                        [126.213819, 49.126754],\n                        [126.224859, 49.13437],\n                        [126.233604, 49.134061],\n                        [126.235421, 49.140339],\n                        [126.228395, 49.140991],\n                        [126.230546, 49.151554],\n                        [126.256306, 49.155669],\n                        [126.256258, 49.151246],\n                        [126.274132, 49.15272],\n                        [126.284455, 49.145552],\n                        [126.298362, 49.145964],\n                        [126.298792, 49.137183],\n                        [126.314564, 49.132655],\n                        [126.334493, 49.131008],\n                        [126.338364, 49.139378],\n                        [126.347253, 49.141471],\n                        [126.361017, 49.161498],\n                        [126.380516, 49.16455],\n                        [126.383049, 49.157761],\n                        [126.393611, 49.153372],\n                        [126.40857, 49.153303],\n                        [126.409048, 49.158755],\n                        [126.419801, 49.166058],\n                        [126.444079, 49.16767],\n                        [126.441307, 49.172675],\n                        [126.453446, 49.172949],\n                        [126.459946, 49.184157],\n                        [126.458608, 49.192655],\n                        [126.470938, 49.193204],\n                        [126.472324, 49.201392],\n                        [126.513425, 49.198926],\n                        [126.517965, 49.201803],\n                        [126.52781, 49.191388],\n                        [126.535313, 49.189263],\n                        [126.532159, 49.181072],\n                        [126.551037, 49.177371],\n                        [126.550416, 49.165167],\n                        [126.558636, 49.16527],\n                        [126.560882, 49.15879],\n                        [126.573547, 49.163967],\n                        [126.599402, 49.168115],\n                        [126.597825, 49.171372],\n                        [126.608291, 49.176171],\n                        [126.611398, 49.170069],\n                        [126.623967, 49.169795],\n                        [126.632187, 49.175006],\n                        [126.64404, 49.173395],\n                        [126.651161, 49.183232],\n                        [126.661531, 49.182889],\n                        [126.670994, 49.187584],\n                        [126.68753, 49.18306],\n                        [126.690302, 49.1894],\n                        [126.696515, 49.188578],\n                        [126.704735, 49.170481],\n                        [126.70354, 49.167601],\n                        [126.713624, 49.153646],\n                        [126.72065, 49.15056],\n                        [126.728392, 49.140099],\n                        [126.745023, 49.13437],\n                        [126.763041, 49.140613],\n                        [126.775371, 49.141094],\n                        [126.776136, 49.14809],\n                        [126.8018, 49.161053],\n                        [126.809972, 49.16143],\n                        [126.818049, 49.179119],\n                        [126.831479, 49.185048],\n                        [126.826078, 49.203585],\n                        [126.810928, 49.21756],\n                        [126.79683, 49.225608],\n                        [126.775849, 49.22821],\n                        [126.751762, 49.241494],\n                        [126.7477, 49.2481],\n                        [126.750042, 49.259154],\n                        [126.741009, 49.277732],\n                        [126.731355, 49.2791],\n                        [126.731546, 49.295037],\n                        [126.722561, 49.304063],\n                        [126.73255, 49.313498],\n                        [126.730399, 49.320608],\n                        [126.735322, 49.328639],\n                        [126.728726, 49.337078],\n                        [126.729587, 49.347361],\n                        [126.720889, 49.355046],\n                        [126.73169, 49.364368],\n                        [126.716683, 49.385432],\n                        [126.71501, 49.393555],\n                        [126.704018, 49.399629],\n                        [126.699956, 49.411843],\n                        [126.681795, 49.425759],\n                        [126.67845, 49.431625],\n                        [126.666358, 49.43616],\n                        [126.661436, 49.440763],\n                        [126.64901, 49.441683],\n                        [126.646334, 49.446865],\n                        [126.631853, 49.453069],\n                        [126.613214, 49.455966],\n                        [126.609391, 49.459544],\n                        [126.589175, 49.461589],\n                        [126.57823, 49.466871],\n                        [126.571253, 49.476173],\n                        [126.556963, 49.485166],\n                        [126.54664, 49.48755],\n                        [126.540571, 49.493408],\n                        [126.548217, 49.506961],\n                        [126.539041, 49.511353],\n                        [126.543629, 49.521939],\n                        [126.519255, 49.543582],\n                        [126.528623, 49.549671],\n                        [126.549269, 49.5561],\n                        [126.545589, 49.569431],\n                        [126.537225, 49.574838],\n                        [126.541192, 49.583099],\n                        [126.547883, 49.587076],\n                        [126.552901, 49.596491],\n                        [126.55161, 49.603627],\n                        [126.536795, 49.612257],\n                        [126.525851, 49.621361],\n                        [126.517439, 49.635625],\n                        [126.525229, 49.642722],\n                        [126.527762, 49.653552],\n                        [126.536508, 49.654265],\n                        [126.536938, 49.658983],\n                        [126.511131, 49.676799],\n                        [126.485562, 49.683652],\n                        [126.478776, 49.696236],\n                        [126.484845, 49.706172],\n                        [126.484415, 49.719937],\n                        [126.500234, 49.721903],\n                        [126.499422, 49.728648],\n                        [126.504536, 49.742339],\n                        [126.490437, 49.750267],\n                        [126.465107, 49.747827],\n                        [126.456601, 49.749115],\n                        [126.429742, 49.760801],\n                        [126.420948, 49.761682],\n                        [126.411772, 49.767169],\n                        [126.391747, 49.769133],\n                        [126.376502, 49.764832],\n                        [126.375976, 49.761377],\n                        [126.361925, 49.755924],\n                        [126.343669, 49.7565],\n                        [126.336691, 49.751283],\n                        [126.322593, 49.748674],\n                        [126.312413, 49.741932],\n                        [126.295638, 49.742915],\n                        [126.284025, 49.736205],\n                        [126.277047, 49.743728],\n                        [126.261419, 49.737493],\n                        [126.245648, 49.738272],\n                        [126.237237, 49.742271],\n                        [126.22763, 49.723665],\n                        [126.21444, 49.712885],\n                        [126.230355, 49.708613],\n                        [126.231788, 49.698745],\n                        [126.223807, 49.694404],\n                        [126.208084, 49.693997],\n                        [126.192073, 49.700509],\n                        [126.177115, 49.694303],\n                        [126.174247, 49.689147],\n                        [126.178883, 49.680904],\n                        [126.173244, 49.6769],\n                        [126.154461, 49.679207],\n                        [126.153075, 49.688774],\n                        [126.129084, 49.687858],\n                        [126.122059, 49.695252],\n                        [126.110684, 49.69298],\n                        [126.102416, 49.697457],\n                        [126.093718, 49.692437],\n                        [126.085641, 49.695083],\n                        [126.085546, 49.689045],\n                        [126.078234, 49.692878],\n                        [126.061076, 49.70841],\n                        [126.058543, 49.713157],\n                        [126.060455, 49.727021],\n                        [126.068006, 49.737629],\n                        [126.063084, 49.753248],\n                        [126.065091, 49.762224],\n                        [126.073216, 49.77218],\n                        [126.086693, 49.782575],\n                        [126.087314, 49.801733],\n                        [126.074984, 49.815505],\n                        [126.081006, 49.830591],\n                        [126.045353, 49.864975],\n                        [126.037754, 49.869876],\n                        [126.032401, 49.887074],\n                        [126.029247, 49.906123],\n                        [126.020645, 49.918987],\n                        [126.02595, 49.929316],\n                        [126.019593, 49.948079],\n                        [126.001862, 49.950778],\n                        [125.984419, 49.962483],\n                        [125.981694, 49.968723],\n                        [125.986282, 49.982143],\n                        [125.983654, 49.990807],\n                        [125.966544, 50.004188],\n                        [125.961765, 50.01086],\n                        [125.95149, 50.016689],\n                        [125.931752, 50.018946],\n                        [125.919804, 50.024404],\n                        [125.911154, 50.021507],\n                        [125.881332, 50.032252],\n                        [125.875931, 50.037674],\n                        [125.879564, 50.042153],\n                        [125.872968, 50.048012],\n                        [125.887975, 50.046833],\n                        [125.909433, 50.05461],\n                        [125.89543, 50.059458],\n                        [125.889074, 50.065617],\n                        [125.895526, 50.073728],\n                        [125.901165, 50.074805],\n                        [125.910724, 50.068613],\n                        [125.922242, 50.068343],\n                        [125.937009, 50.086278],\n                        [125.931943, 50.095394],\n                        [125.909816, 50.096975],\n                        [125.899588, 50.101717],\n                        [125.889122, 50.102558],\n                        [125.877174, 50.112882],\n                        [125.87727, 50.129892],\n                        [125.885107, 50.143839],\n                        [125.842095, 50.144309],\n                        [125.834496, 50.160503],\n                        [125.833158, 50.175548],\n                        [125.834496, 50.192637],\n                        [125.831342, 50.200257],\n                        [125.850745, 50.243398],\n                        [125.852466, 50.250976],\n                        [125.874593, 50.267836],\n                        [125.881475, 50.280135],\n                        [125.895144, 50.292798],\n                        [125.893949, 50.31172],\n                        [125.891464, 50.316307],\n                        [125.89696, 50.340508],\n                        [125.896099, 50.351583],\n                        [125.907474, 50.363491],\n                        [125.918753, 50.366836],\n                        [125.9437, 50.382051],\n                        [125.959232, 50.384057],\n                        [125.976055, 50.402876],\n                        [125.99369, 50.41407],\n                        [126.008506, 50.414772],\n                        [126.027335, 50.419917],\n                        [126.032162, 50.425195],\n                        [126.042055, 50.42152],\n                        [126.054863, 50.42152],\n                        [126.066572, 50.425596],\n                        [126.074458, 50.434447],\n                        [126.090421, 50.440124],\n                        [126.089369, 50.448605],\n                        [126.106287, 50.458887],\n                        [126.113313, 50.458787],\n                        [126.134007, 50.480546],\n                        [126.14734, 50.486618],\n                        [126.14433, 50.492356],\n                        [126.14734, 50.504396],\n                        [126.161774, 50.539298],\n                        [126.163303, 50.545762],\n                        [126.154748, 50.636102],\n                        [126.157472, 50.642753],\n                        [126.177545, 50.659375],\n                        [126.165453, 50.6621],\n                        [126.161726, 50.669146],\n                        [126.165979, 50.677353],\n                        [126.177115, 50.684961],\n                        [126.187677, 50.685526],\n                        [126.204117, 50.690907],\n                        [126.201058, 50.703561],\n                        [126.211429, 50.71724],\n                        [126.208275, 50.728194],\n                        [126.223855, 50.737685],\n                        [126.219554, 50.753411],\n                        [126.226053, 50.758088],\n                        [126.222517, 50.768071],\n                        [126.226961, 50.775499],\n                        [126.220701, 50.781997],\n                        [126.224046, 50.790847],\n                        [126.233461, 50.801088],\n                        [126.232218, 50.817687],\n                        [126.235229, 50.826134],\n                        [126.253486, 50.8209],\n                        [126.268492, 50.831565],\n                        [126.272077, 50.841267],\n                        [126.283308, 50.841598],\n                        [126.290285, 50.853781],\n                        [126.306487, 50.858315],\n                        [126.314086, 50.867449],\n                        [126.327324, 50.872214],\n                        [126.344625, 50.89484],\n                        [126.335353, 50.910679],\n                        [126.342187, 50.922314],\n                        [126.333776, 50.929784],\n                        [126.335879, 50.953835],\n                        [126.344768, 50.964932],\n                        [126.334015, 50.982991]\n                    ]\n                ]\n            ]\n        }\n    }]\n}\nexport default hhs\n\n\n// WEBPACK FOOTER //\n// ./src/renderer/assets/mapJson/231100.js", "<template>\r\n  <div class=\"content\">\r\n    <div ref=\"charts\" style=\"width: calc(100vw * 0.487); height: calc(100vh * 0.51)\"></div>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { getDwCount } from \"../../../../api/dpzs\";\r\nimport axios from \"axios\";\r\nimport hhs from \"../../../assets/mapJson/231100.js\"\r\nexport default {\r\n  data() {\r\n    return {\r\n      dtList: [],\r\n    };\r\n  },\r\n  created() { },\r\n  mounted() {\r\n    this.getQxMap();\r\n  },\r\n  methods: {\r\n    async getQxMap() {\r\n      let params = {\r\n        citycode: \"231100\",\r\n      };\r\n      let data = await getDwCount(params);\r\n      this.dtList = data.map((item) => {\r\n        item.value = item.count;\r\n        return item;\r\n      });\r\n      console.log(this.dtList);\r\n      this.$nextTick(() => {\r\n        this.initCharts();\r\n      });\r\n    },\r\n    initCharts() {\r\n      const charts = echarts.init(this.$refs[\"charts\"]);\r\n\r\n      // 在这里处理响应数据，并将其用于注册地图\r\n      echarts.registerMap(\"uploadedDataURL\", hhs);\r\n\r\n      const option = {\r\n        // backgroundColor: '#020933',\r\n\r\n        tooltip: {\r\n          show: true,\r\n          trigger: \"item\",\r\n          formatter: function (params) {\r\n            return params.name + \" : \" + params.value;\r\n          },\r\n        },\r\n        visualMap: {\r\n          min: this.dtList[0].value,\r\n          max: this.dtList[this.dtList.length - 1].value,\r\n          // right: \"15%\",\r\n          right: \"10%\", //大兴安岭\r\n          text: [\"高\", \"低\"],\r\n          textStyle: {\r\n            color: \"#fff\",\r\n          },\r\n          realtime: false,\r\n          calculable: true,\r\n          inRange: {\r\n            color: [\r\n              \"#052570\",\r\n              \"#063B98\",\r\n              \"#1760E4\",\r\n              \"#0793FA\",\r\n              \"#00BDFF\",\r\n              \"#07DDF5\",\r\n            ],\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"绥化地图全览\",\r\n            type: \"map\",\r\n            map: \"uploadedDataURL\",\r\n            roam: false, //是否允许缩放\r\n            zoom: 1.25, //默认显示级别\r\n            label: {\r\n              normal: {\r\n                show: true,\r\n                color: \"#fff\",\r\n                fontSize: 12,\r\n              },\r\n              emphasis: {\r\n                show: true,\r\n                color: \"#fff\",\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                areaColor: \"#70EAF4\", // 高亮时候地图显示的颜色\r\n                borderWidth: 1, // 高亮时的边框宽度\r\n              },\r\n              label: {\r\n                fontSize: 12, // 选中地图文字字号和字体颜色\r\n                color: \"#fff\",\r\n              },\r\n            },\r\n            itemStyle: {\r\n              normal: {\r\n                areaColor: \"#3894ec\",\r\n                borderColor: \"#3fdaff\",\r\n                borderWidth: 2,\r\n                shadowColor: \"rgba(63, 218, 255, 0.5)\",\r\n                shadowBlur: 30,\r\n              },\r\n              emphasis: {\r\n                //交互时效果\r\n                areaColor: \"#2b91b7\",\r\n                color: \"#000\",\r\n                label: {\r\n                  show: true,\r\n                },\r\n              },\r\n            },\r\n            // data: [\r\n            //   { name: \"庆安县\", value: 11 },\r\n            //   { name: \"绥棱县\", value: 14 },\r\n            //   { name: \"海伦市\", value: 31 },\r\n            //   { name: \"明水县\", value: 6 },\r\n            //   { name: \"青冈县\", value: 44 },\r\n            //   { name: \"望奎县\", value: 49 },\r\n            //   { name: \"北林区\", value: 20 },\r\n            //   { name: \"安达市\", value: 4 },\r\n            //   { name: \"兰西县\", value: 5 },\r\n            //   { name: \"肇东市\", value: 21 },\r\n            // ],\r\n            data: this.dtList,\r\n          },\r\n        ],\r\n      };\r\n      // 地图注册，第一个参数的名字必须和option.geo.map一致\r\n      // echarts.registerMap(\"china\",zhongguo)\r\n\r\n      charts.setOption(option);\r\n\r\n    },\r\n  },\r\n};\r\n</script>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/homePage/components/dt1.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"content\"},[_c('div',{ref:\"charts\",staticStyle:{\"width\":\"calc(100vw * 0.487)\",\"height\":\"calc(100vh * 0.51)\"}})])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-13088202\",\"hasScoped\":false,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/homePage/components/dt1.vue\n// module id = null\n// module chunks = ", "var normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./dt1.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./dt1.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-13088202\\\",\\\"hasScoped\\\":false,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./dt1.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = null\n/* scopeId */\nvar __vue_scopeId__ = null\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/homePage/components/dt1.vue\n// module id = null\n// module chunks = ", "<template>\r\n  <div class=\"content\">\r\n    <div class=\"bgbg\">\r\n      <div\r\n        ref=\"charts\"\r\n        id=\"main\"\r\n        class=\"bg\"\r\n        style=\"width: calc(100vw * 0.18); height: calc(100vh * 0.185)\"\r\n      ></div>\r\n      <div class=\"pffb\"></div>\r\n      <div class=\"pf1\"></div>\r\n      <div class=\"pf1 pf2\"></div>\r\n      <div class=\"pf1 pf3\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { selectPffb } from \"../../../../api/dpzs\";\r\nimport axios from \"axios\";\r\n// import zhongguo from \"@/assets/mapJson/data-city.json\"\r\nexport default {\r\n  data() {\r\n    return {\r\n      btList: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.$nextTick(() => {\r\n    //   this.initCharts();\r\n    // });\r\n  },\r\n  props: {\r\n    canClick: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  mounted() {\r\n    this.getBt();\r\n  },\r\n  methods: {\r\n    async getBt() {\r\n      let params = {\r\n        citycode: \"231100\",\r\n      };\r\n      let data = await selectPffb(params);\r\n      this.btList = data;\r\n      this.$nextTick(() => {\r\n        this.initCharts();\r\n      });\r\n    },\r\n    initCharts() {\r\n      var chartDom = document.getElementById(\"main\");\r\n      var myChart = echarts.init(chartDom);\r\n      var option;\r\n      const trafficWay = this.btList;\r\n      // const trafficWay = [\r\n      //   {\r\n      //     name: \"85分以上\",\r\n      //     value: 100,\r\n      //   },\r\n      //   {\r\n      //     name: \"60-85分\",\r\n      //     value: 314,\r\n      //   },\r\n      //   {\r\n      //     name: \"60分以下\",\r\n      //     value: 513,\r\n      //   },\r\n      // ];\r\n      let sum = trafficWay.reduce((cur, pre) => {\r\n        return cur + pre.value;\r\n      }, 0);\r\n      let data = [];\r\n      let legendData = [];\r\n      var color = [\"#7FCCFF\", \"#00BE76\", \"#FEB501\"];\r\n      for (var i = 0; i < trafficWay.length; i++) {\r\n        let name = trafficWay[i].name;\r\n        legendData.push(name);\r\n        data.push(\r\n          {\r\n            value: trafficWay[i].value,\r\n            name: name,\r\n            itemStyle: {\r\n              borderWidth: 0,\r\n              borderRadius: 0,\r\n              shadowBlur: 2,\r\n              borderColor: color[i],\r\n              shadowColor: color[i],\r\n            },\r\n          },\r\n          {\r\n            value: sum / 100, // 控制每个环形之间的间隙\r\n            name: \"\",\r\n            itemStyle: {\r\n              label: {\r\n                show: false,\r\n              },\r\n              labelLine: {\r\n                show: false,\r\n              },\r\n              color: \"rgba(0, 0, 0, 0)\",\r\n              borderColor: \"rgba(0, 0, 0, 0)\",\r\n              borderWidth: 0,\r\n            },\r\n          }\r\n        );\r\n      }\r\n      let seriesOption = [\r\n        {\r\n          name: \"\",\r\n          type: \"pie\",\r\n          clockwise: false,\r\n          radius: [\"70%\", \"87%\"],\r\n          center: [\"27%\", \"46.3%\"],\r\n          emphasis: {\r\n            scale: false,\r\n          },\r\n          zlevel: 1,\r\n          label: {\r\n            show: false,\r\n          },\r\n          data: data,\r\n        },\r\n      ];\r\n      option = {\r\n        tooltip: {\r\n          show: true,\r\n          trigger: \"item\",\r\n          formatter: function (params) {\r\n            return params.name + \" : \" + params.value;\r\n          },\r\n        },\r\n        title: {\r\n          text: \"评分分布\",\r\n          // subtext: sum,\r\n          textStyle: {\r\n            color: \"#fff\",\r\n            fontSize: 24,\r\n            padding: [0, 0, 25, 0],\r\n            fontFamily: \"YouSheBiaoTiHei\",\r\n          },\r\n          // subtextStyle: {\r\n          //   fontSize: 28,\r\n          //   fontWeight: \"bolder\",\r\n          //   color: \"#19E1E3\",\r\n          // },\r\n          x: \"13.5%\",\r\n          y: \"54%\",\r\n        },\r\n        color: color,\r\n        legend: {\r\n          icon: \"rect\",\r\n          itemWidth: 2,\r\n          itemHeight: 8,\r\n          itemStyle: {\r\n            borderWidth: 2,\r\n          },\r\n          orient: \"vertical\",\r\n          data: legendData,\r\n          right: \"10%\",\r\n          top: \"40%\",\r\n          align: \"left\",\r\n          textStyle: {\r\n            color: \"#fff\",\r\n            fontSize: 14,\r\n            fontFamily: \"SourceHanSansSC-Regular\",\r\n            padding: [0, 0, 0, 10],\r\n          },\r\n          itemGap: 25, // 图例之间的间隔\r\n        },\r\n        toolbox: {\r\n          show: false,\r\n        },\r\n        series: seriesOption,\r\n      };\r\n\r\n      option && myChart.setOption(option);\r\n      // 关键代码\r\n      // myChart.on(\"legendselectchanged\", (params) => {\r\n      //   // myChart.on(\"legendselectchanged\", function (params) {\r\n      //   myChart.setOption({\r\n      //     legend: { selected: { [params.name]: true } },\r\n      //   });\r\n      //   // let that = this;\r\n      //   console.log(\"点击了\", params);\r\n      //   // do something\r\n      //   this.$emit(\"valueChanged\", params);\r\n      //   // });\r\n      // });\r\n\r\n      myChart.off('legendselectchanged') \r\n      myChart.on(\"legendselectchanged\", (params) => {\r\n        //父组件通过click-legend事件，写真正要实现的点击事件代码\r\n        // this.$emit(\"valueChanged\", {\r\n        //   series: params,\r\n        // });\r\n        this.$emit(\"valueChanged\", params);\r\n\r\n        //将默认点击事件中取消选中的legend动态设置回来\r\n        // myChart.setOption({\r\n        //   legend: { selected: { [params.name]: true } },\r\n        // });\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.bgbg {\r\n  width: calc(100vw * 0.18);\r\n  height: calc(100vh * 0.185);\r\n  background: url(../img/pingfenfenbubg.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  position: relative;\r\n}\r\n.bg {\r\n  position: absolute;\r\n  z-index: 99;\r\n}\r\n\r\n.pffb {\r\n  width: calc(100vw * 0.042);\r\n  height: calc(100vh * 0.078);\r\n  background: url(../img/pingfenfenbuico.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  position: absolute;\r\n  top: calc(100vh * 0.025);\r\n  left: calc(100vw * 0.027);\r\n  z-index: 99;\r\n}\r\n.pf1 {\r\n  width: calc(100vw * 0.058);\r\n  height: calc(100vh * 0.023);\r\n  background: url(../img/pingfenfenbutuli.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  position: absolute;\r\n  top: calc(100vh * 0.073);\r\n  left: calc(100vw * 0.111);\r\n  z-index: 98;\r\n}\r\n\r\n.pf2 {\r\n  top: calc(100vh * 0.109);\r\n}\r\n.pf3 {\r\n  top: calc(100vh * 0.145);\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/homePage/components/bing.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"bgbg\"},[_c('div',{ref:\"charts\",staticClass:\"bg\",staticStyle:{\"width\":\"calc(100vw * 0.18)\",\"height\":\"calc(100vh * 0.185)\"},attrs:{\"id\":\"main\"}}),_vm._v(\" \"),_c('div',{staticClass:\"pffb\"}),_vm._v(\" \"),_c('div',{staticClass:\"pf1\"}),_vm._v(\" \"),_c('div',{staticClass:\"pf1 pf2\"}),_vm._v(\" \"),_c('div',{staticClass:\"pf1 pf3\"})])])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-4cde178e\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/homePage/components/bing.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-4cde178e\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./bing.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./bing.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./bing.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-4cde178e\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./bing.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-4cde178e\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/homePage/components/bing.vue\n// module id = null\n// module chunks = ", "<template>\r\n  <div id=\"container\" class=\"large\">\r\n    <!-- 在此界面开发保密工作整体情况可视化大屏，要求自适应，可仿照安全态势自适应写 -->\r\n    <div class=\"con\">\r\n      <div class=\"dpTitle\">\r\n        <div class=\"dpTitleTime\">{{ currentTime }}</div>\r\n        <div class=\"dpTitleLogo\"></div>\r\n        <!-- <div class=\"dpTitleZtqk\">佳木斯市保密工作整体情况</div> -->\r\n        <!-- <div class=\"dpTitleZtqk\">绥化市保密工作整体情况</div> -->\r\n        <!-- <div class=\"dpTitleZtqk\">牡丹江市保密工作整体情况</div> -->\r\n        <!-- <div class=\"dpTitleZtqk\">某自治区保密工作整体情况</div> -->\r\n        <div class=\"dpTitleZtqk\">黑河市保密工作整体情况</div>\r\n        <div class=\"dpTitleDyfb\">机关单位地域分布</div>\r\n        <div class=\"dpTitleFh\" @click=\"fh\" :title=\"mcObj.label\">{{mcObj.label}}</div>\r\n      </div>\r\n      <div class=\"dpLeft\">\r\n        <div class=\"dpLeftTop\">\r\n          <div class=\"dpJgdw\">\r\n            <div class=\"dpJgdwSz\">{{ jgdw }}</div>\r\n            <div class=\"dpJgdwdw\">家</div>\r\n          </div>\r\n          <div class=\"dpBmxz\">\r\n            <div class=\"dpJgdwSz\">{{ bmxzgldw }}</div>\r\n            <div class=\"dpJgdwdw\">家</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"dpLeftSm\">\r\n          <div class=\"dpQNum\">{{ smryObj.total }}</div>\r\n          <div class=\"dpQWz\">总人数</div>\r\n          <div class=\"dpKNum1\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smryObj.hx.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smryObj.hx\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx\">核心</div>\r\n          <div class=\"dpKNum1 dpKNum2\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smryObj.zy.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smryObj.zy\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx dpQZy\">重要</div>\r\n          <div class=\"dpKNum1 dpKNum3\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smryObj.yb.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smryObj.yb\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx dpQYb\">一般</div>\r\n        </div>\r\n        <div class=\"dpLeftSm dpLeftSmcs\">\r\n          <div class=\"dpQNum\">{{ smcsObj.total }}</div>\r\n          <div class=\"dpQWz\">总场所</div>\r\n          <div class=\"dpKNum1\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smcsObj.hx.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smcsObj.hx\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx\">核心</div>\r\n          <div class=\"dpKNum1 dpKNum2\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smcsObj.zy.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smcsObj.zy\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx dpQZy\">重要</div>\r\n          <div class=\"dpKNum1 dpKNum3\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smcsObj.yb.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smcsObj.yb\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx dpQYb\">一般</div>\r\n        </div>\r\n        <div class=\"dpLeftSm dpLeftSmsb\">\r\n          <div class=\"dpQNum\">{{ smsbObj.total }}</div>\r\n          <div class=\"dpQWz\">总设备</div>\r\n          <div class=\"dpKNum1\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smsbObj.hx.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smsbObj.hx\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx\">核心</div>\r\n          <div class=\"dpKNum1 dpKNum2\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smsbObj.zy.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smsbObj.zy\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx dpQZy\">重要</div>\r\n          <div class=\"dpKNum1 dpKNum3\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smsbObj.yb.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smsbObj.yb\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx dpQYb\">一般</div>\r\n        </div>\r\n        <div class=\"dpLeftSm dpLeftSmzt\">\r\n          <div class=\"dpQNum\">{{ smztObj.total }}</div>\r\n          <div class=\"dpQWz\">总载体</div>\r\n          <div class=\"dpKNum1\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smztObj.hx.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smztObj.hx\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx\">核心</div>\r\n          <div class=\"dpKNum1 dpKNum2\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smztObj.zy.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smztObj.zy\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx dpQZy\">重要</div>\r\n          <div class=\"dpKNum1 dpKNum3\">\r\n            <div class=\"dpKNum1For\" v-if=\"this.smztObj.yb.length == 1\">0</div>\r\n            <div\r\n              class=\"dpKNum1For\"\r\n              v-for=\"(item, index) in this.smztObj.yb\"\r\n              :key=\"index\"\r\n            >\r\n              {{ item }}\r\n            </div>\r\n          </div>\r\n          <div class=\"dpQHx dpQYb\">一般</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"dpMap\">\r\n        <dt1 ref=\"dt\"></dt1>\r\n      </div>\r\n      <div class=\"dpInput\">\r\n        <el-input\r\n          v-model=\"dwpmqk\"\r\n          class=\"dpInputM\"\r\n          placeholder=\"单位排名情况\"\r\n        ></el-input>\r\n        <!-- <input type=\"text\" placeholder=\"单位排名情况\"> -->\r\n        <div class=\"dpSsAn\" @click=\"search\">搜索</div>\r\n      </div>\r\n      <div class=\"dpTableBtn\">\r\n        <div class=\"dpTableTitle\">\r\n          <ul>\r\n            <li>序号</li>\r\n            <li>单位名称</li>\r\n            <li>分数</li>\r\n            <li>组织机构</li>\r\n            <li>涉密岗位</li>\r\n            <li>涉密人员</li>\r\n            <li>涉密场所</li>\r\n            <li>涉密设备</li>\r\n            <li>涉密载体</li>\r\n            <li>教育培训</li>\r\n          </ul>\r\n        </div>\r\n        <div class=\"dpTableCon\" v-if=\"this.dwData.length != 0\">\r\n          <div\r\n            class=\"dpTableConMh\"\r\n            v-for=\"(item, index) in dwData\"\r\n            :key=\"index\"\r\n          >\r\n            <span class=\"table-text\">\r\n              {{ index < 9 ? 0 : \"\" }}{{ index + 1 }}\r\n            </span>\r\n            <p class=\"tb-item tb-cu\" :title=\"item.dwmc\" @click=\"rClick(item)\">\r\n              {{ item.dwmc }}\r\n            </p>\r\n            <p class=\"tb-item\" :title=\"item.fs\">{{ item.fs }}</p>\r\n            <p class=\"tb-item\" :title=\"item.zzjg\">{{ item.zzjg }}</p>\r\n            <p class=\"tb-item\" :title=\"item.smgw\">{{ item.smgw }}</p>\r\n            <p class=\"tb-item\" :title=\"item.smry\">{{ item.smry }}</p>\r\n            <p class=\"tb-item\" :title=\"item.smcs\">{{ item.smcs }}</p>\r\n            <p class=\"tb-item\" :title=\"item.smsb\">{{ item.smsb }}</p>\r\n            <p class=\"tb-item\" :title=\"item.smzt\">{{ item.smzt }}</p>\r\n            <p class=\"tb-item\" :title=\"item.jypx\">{{ item.jypx }}</p>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"this.dwData.length == 0\" class=\"dpTableConZwsj\">\r\n          暂无数据\r\n        </div>\r\n      </div>\r\n      <div class=\"dpTableRight\">\r\n        <div class=\"dpTableRightTitle\">\r\n          <ul>\r\n            <li>序号</li>\r\n            <li>县区</li>\r\n            <li>单位数量</li>\r\n          </ul>\r\n        </div>\r\n        <div class=\"dpTableRightCon\" v-if=\"this.xqData.length != 0\">\r\n          <div\r\n            class=\"dpTableRightConMh\"\r\n            v-for=\"(item, index) in xqData\"\r\n            :key=\"index\"\r\n          >\r\n            <span class=\"table-text1\">\r\n              {{ index < 9 ? 0 : \"\" }}{{ index + 1 }}\r\n            </span>\r\n            <p class=\"tb-item2\" :title=\"item.name\">{{ item.name }}</p>\r\n            <p class=\"tb-item2\" :title=\"item.count\">{{ item.count }}</p>\r\n          </div>\r\n        </div>\r\n        <div class=\"dpTableRightConZwsj\" v-if=\"this.xqData.length == 0\">\r\n          暂无数据\r\n        </div>\r\n      </div>\r\n      <div class=\"dpBingTu\">\r\n        <bing @valueChanged=\"handleValueChanged\"></bing>\r\n      </div>\r\n      <div class=\"dpPfpm\">\r\n        <div class=\"dpPfpmCon\">\r\n          <div class=\"dpDwphb\" v-for=\"(item, index) in pfData\" :key=\"index\">\r\n            <div class=\"dpJdtTitle\">\r\n              <div class=\"dpJdtTitleLeft\" :title=\"item.dwmc\">\r\n                {{ item.dwmc }}\r\n              </div>\r\n              <div class=\"dpJdtTitleRight dpJdtTitleRight5\" v-if=\"item.fs > 95\">\r\n                {{ item.fs }}%\r\n              </div>\r\n              <div\r\n                class=\"dpJdtTitleRight dpJdtTitleRight4\"\r\n                v-if=\"item.fs > 92 && item.fs <= 95\"\r\n              >\r\n                {{ item.fs }}%\r\n              </div>\r\n              <div\r\n                class=\"dpJdtTitleRight dpJdtTitleRight3\"\r\n                v-if=\"item.fs > 90 && item.fs <= 92\"\r\n              >\r\n                {{ item.fs }}%\r\n              </div>\r\n              <div\r\n                class=\"dpJdtTitleRight dpJdtTitleRight2\"\r\n                v-if=\"item.fs > 89 && item.fs <= 90\"\r\n              >\r\n                {{ item.fs }}%\r\n              </div>\r\n              <div\r\n                class=\"dpJdtTitleRight dpJdtTitleRight1\"\r\n                v-if=\"item.fs > 86 && item.fs <= 89\"\r\n              >\r\n                {{ item.fs }}%\r\n              </div>\r\n              <div class=\"dpJdtTitleRight\" v-if=\"item.fs <= 86\">\r\n                {{ item.fs }}%\r\n              </div>\r\n            </div>\r\n            <div class=\"dpJdtTx\">\r\n              <el-progress\r\n                v-if=\"item.fs > 95\"\r\n                :text-inside=\"true\"\r\n                :percentage=\"item.fs\"\r\n                class=\"custom-progress custom-progress5\"\r\n              ></el-progress>\r\n              <el-progress\r\n                :text-inside=\"true\"\r\n                :percentage=\"item.fs\"\r\n                v-if=\"item.fs > 92 && item.fs <= 95\"\r\n                class=\"custom-progress custom-progress4\"\r\n              ></el-progress>\r\n              <el-progress\r\n                :text-inside=\"true\"\r\n                v-if=\"item.fs > 90 && item.fs <= 92\"\r\n                :percentage=\"item.fs\"\r\n                class=\"custom-progress custom-progress3\"\r\n              ></el-progress>\r\n              <el-progress\r\n                :text-inside=\"true\"\r\n                v-if=\"item.fs > 89 && item.fs <= 90\"\r\n                :percentage=\"item.fs\"\r\n                class=\"custom-progress custom-progress2\"\r\n              ></el-progress>\r\n              <el-progress\r\n                :text-inside=\"true\"\r\n                :percentage=\"item.fs\"\r\n                v-if=\"item.fs > 86 && item.fs <= 89\"\r\n                class=\"custom-progress custom-progress1\"\r\n              ></el-progress>\r\n              <el-progress\r\n                :text-inside=\"true\"\r\n                v-if=\"item.fs <= 86\"\r\n                :percentage=\"item.fs\"\r\n                class=\"custom-progress\"\r\n              ></el-progress>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport dt1 from \"./components/dt1.vue\";\r\nimport bing from \"./components/bing.vue\";\r\nimport {\r\n  getAllCount,\r\n  getDwCount,\r\n  getPfList,\r\n  selectDwCount,\r\n  toDwInterface,\r\n  selectPffb,\r\n} from \"../../../api/dpzs\";\r\nimport { getLoginInfo } from \"../../../api/index\";\r\nimport store from \"../../store\";\r\n\r\nimport {\r\n  // 判断单位是否已经注册\r\n  getUserInfo,\r\n} from \"../../../api/dwzc\";\r\nimport { dateFormatLs, dateFormat } from \"@/utils/moment.js\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      dwpmqk: \"\",\r\n      dwData: [],\r\n      xqData: [],\r\n      pfData: [],\r\n      bmxzgldw: \"\",\r\n      jgdw: \"\",\r\n      smryObj: {},\r\n      smcsObj: {},\r\n      smsbObj: {},\r\n      smztObj: {},\r\n      currentTime: \"\",\r\n      mcObj: {},\r\n    };\r\n  },\r\n  components: {\r\n    dt1,\r\n    bing,\r\n  },\r\n  computed: {},\r\n  created() {},\r\n  mounted() {\r\n    // setInterval(function () {\r\n    //   this.time = dateFormat(new Date());\r\n    // }, 1000);\r\n    setInterval(() => {\r\n      this.updateTime();\r\n    }, 1000);\r\n    this.getLoginInfo();\r\n    this.getLeftNum();\r\n    this.getQxMap();\r\n    this.getBtnTable();\r\n    this.getPfPhb();\r\n  },\r\n  methods: {\r\n    async getLoginInfo() {\r\n      let res = await getLoginInfo();\r\n      console.log(res);\r\n      this.mcObj = res;\r\n    },\r\n    async handleValueChanged(data) {\r\n      console.log(\"接收到的值:\", data.selected);\r\n      let values = Object.values(data.selected);\r\n      console.log(values);\r\n      let params = {\r\n        citycode: \"231100\",\r\n        map: data.name,\r\n        gybw: values[0], //高于八五\r\n        lsbw: values[1], //六十八五\r\n        lsyx: values[2], //六十以下\r\n      };\r\n\r\n      let res = await getPfList(params);\r\n      this.pfData = res;\r\n    },\r\n    fh() {\r\n      const PubSub = require(\"pubsub-js\");\r\n      PubSub.publish(\"dataFh\", \"fh\");\r\n      this.$router.push(\"/ztqksy\");\r\n    },\r\n    async rClick(val) {\r\n      console.log(val);\r\n      let parmas = {\r\n        dwid: val.bmid,\r\n      };\r\n      let data = await toDwInterface(parmas);\r\n      if (data.code == 10000) {\r\n        //利用localstorage存储到本地\r\n        // store.dispatch(\"addToken\", res.data);\r\n        store.commit(\"addNewToken\", data.data);\r\n        // localStorage.setItem(\"user-token\", res.data)\r\n        // this.userToken = \"Bearer \" + res.data;\r\n        let dataLogin = await getUserInfo();\r\n        console.log(dataLogin);\r\n        const PubSub = require(\"pubsub-js\");\r\n        PubSub.publish(\"data\", dataLogin);\r\n        PubSub.publish(\"dataNext\", \"next\");\r\n        this.$router.push({\r\n          path: \"/ztqksy\",\r\n          query: {\r\n            dwmc: val.dwmc,\r\n            fs: val.fs,\r\n          },\r\n        });\r\n        localStorage.setItem(\"dwmc\", val.dwmc);\r\n        localStorage.setItem(\"dwjy\", 1);\r\n        store.commit(\"addAllScore\", val.fs);\r\n      }\r\n      // return\r\n\r\n      // this.$router.push({\r\n      //   path: \"/ztqksy\",\r\n      //   query: {\r\n      //     dwmc: val.dwmc,\r\n      //     fs: val.fs,\r\n      //     bmid: val.bmid,\r\n      //   },\r\n      // });\r\n      // localStorage.setItem(\"dwmc\", val.dwmc);\r\n      // localStorage.setItem(\"fs\", val.fs);\r\n      // localStorage.setItem(\"bmid\", val.bmid);\r\n    },\r\n    search() {\r\n      this.getBtnTable();\r\n    },\r\n    updateTime() {\r\n      const now = new Date();\r\n      this.currentTime = dateFormatLs(now);\r\n    },\r\n    async getLeftNum() {\r\n      let params = {\r\n        citycode: \"231100\",\r\n      };\r\n      let data = await getAllCount(params);\r\n      this.jgdw = data.jgdw;\r\n      this.bmxzgldw = data.bmxzgldw;\r\n      this.smryObj = data.smry;\r\n      this.smryObj.hx = [...String(this.smryObj.hx)].map(Number);\r\n      this.smryObj.zy = [...String(this.smryObj.zy)].map(Number);\r\n      this.smryObj.yb = [...String(this.smryObj.yb)].map(Number);\r\n      this.smcsObj = data.smcs;\r\n      this.smcsObj.hx = [...String(this.smcsObj.hx)].map(Number);\r\n      this.smcsObj.zy = [...String(this.smcsObj.zy)].map(Number);\r\n      this.smcsObj.yb = [...String(this.smcsObj.yb)].map(Number);\r\n      this.smsbObj = data.smsb;\r\n      this.smsbObj.hx = [...String(this.smsbObj.hx)].map(Number);\r\n      this.smsbObj.zy = [...String(this.smsbObj.zy)].map(Number);\r\n      this.smsbObj.yb = [...String(this.smsbObj.yb)].map(Number);\r\n      this.smztObj = data.smzt;\r\n      this.smztObj.hx = [...String(this.smztObj.hx)].map(Number);\r\n      this.smztObj.zy = [...String(this.smztObj.zy)].map(Number);\r\n      this.smztObj.yb = [...String(this.smztObj.yb)].map(Number);\r\n    },\r\n    async getQxMap() {\r\n      let params = {\r\n        citycode: \"231100\",\r\n      };\r\n      let data = await getDwCount(params);\r\n      this.xqData = data;\r\n    },\r\n    async getBtnTable() {\r\n      let params = {\r\n        dwmc: this.dwpmqk,\r\n        citycode: \"231100\",\r\n      };\r\n      let data = await selectDwCount(params);\r\n      this.dwData = data;\r\n    },\r\n    async getPfPhb() {\r\n      let params = {\r\n        citycode: \"231100\",\r\n      };\r\n      let data = await getPfList(params);\r\n      this.pfData = data;\r\n    },\r\n  },\r\n  watch: {},\r\n};\r\n</script>\r\n<style scoped>\r\n.large {\r\n  width: calc(100vw * 1920 / 1920);\r\n  height: calc(100vh * 1080 / 1080);\r\n  background: url(./img/bg.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  position: absolute;\r\n  left: 0px;\r\n  /* background-position-x: -55px; */\r\n  top: 0;\r\n}\r\n\r\n.con {\r\n  position: relative;\r\n}\r\n\r\n.dpTitle {\r\n  width: calc(100vw * 1920 / 1920);\r\n  height: calc(100vh * 0.143);\r\n  background: url(./img/head.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n}\r\n\r\n.dpTitleTime {\r\n  width: calc(100vw * 0.094);\r\n  height: calc(100vh * 0.022);\r\n  position: absolute;\r\n  top: calc(100vh * 0.008);\r\n  left: calc(100vw * 0.018);\r\n  font-family: LetsgoDigital-Regular;\r\n  font-size: calc(100vw * 24 / 1920);\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  text-shadow: 0 2px 6px rgba(0, 50, 159, 0.9);\r\n  font-weight: 700;\r\n}\r\n\r\n.dpTitleLogo {\r\n  width: calc(100vw * 0.028);\r\n  height: calc(100vh * 0.039);\r\n  position: absolute;\r\n  top: calc(100vh * 0.006);\r\n  left: calc(100vw * 0.362);\r\n  background: url(./img/baomibiaozhi.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n}\r\n\r\n.dpTitleZtqk {\r\n  width: calc(100vw * 0.241);\r\n  height: calc(100vh * 0.045);\r\n  line-height: calc(100vh * 0.045);\r\n  position: absolute;\r\n  top: calc(100vh * 0.004);\r\n  left: calc(100vw * 0.395);\r\n  font-size: calc(100vw * 36 / 1920);\r\n  color: #ffffff;\r\n  /* border: 1px solid rgba(198,242,255,1); */\r\n  font-family: SourceHanSansSC-Medium;\r\n  /* background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 34%, #4D8AFE 75%); */\r\n  /* background-image: radial-gradient(circle at 50% 25%, #FFFFFF 0%, #FFFFFF 34%, #4D8AFE 75%); */\r\n  /* -webkit-background-clip: text; */\r\n  /* color: transparent; */\r\n  letter-spacing: calc(100vw * 2 / 1920);\r\n  text-align: center;\r\n  text-shadow: 0 2px 5px rgba(4, 25, 63, 0.57);\r\n  font-weight: 700;\r\n}\r\n\r\n.dpTitleDyfb {\r\n  width: calc(100vw * 0.115);\r\n  height: calc(100vh * 0.029);\r\n  line-height: calc(100vh * 0.029);\r\n  position: absolute;\r\n  top: calc(100vh * 0.097);\r\n  left: calc(100vw * 0.443);\r\n  font-family: YouSheBiaoTiHei;\r\n  font-size: calc(100vw * 24 / 1920);\r\n  text-align: center;\r\n  color: #02fdf8;\r\n  letter-spacing: 0;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\r\n  font-weight: 400;\r\n}\r\n\r\n.dpTitleFh {\r\n  width: calc(100vw * 0.1);\r\n  height: calc(100vh * 0.027);\r\n  line-height: calc(100vh * 0.027);\r\n  position: absolute;\r\n  top: calc(100vh * 0.006);\r\n  right: calc(100vw * 0.015);\r\n  font-family: SourceHanSansSC-Medium;\r\n  font-size: calc(100vw * 20 / 1920);\r\n  color: #ffffff;\r\n  letter-spacing: calc(100vw * 0.5 / 1920);\r\n  text-align: center;\r\n  text-shadow: 0 2px 6px rgba(0, 50, 157, 0.9);\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  /* 超出部分省略号显示 */\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.dpLeft {\r\n  width: calc(100vw * 0.234);\r\n  height: calc(100vh * 1008 / 1080);\r\n  position: absolute;\r\n  top: calc(100vh * 0.049);\r\n  left: calc(100vw * 0.01);\r\n}\r\n\r\n.dpLeftTop {\r\n  width: calc(100vw * 0.234);\r\n  height: calc(100vh * 0.304);\r\n  margin-bottom: calc(100vh * 0.011);\r\n  display: flex;\r\n}\r\n\r\n.dpJgdw {\r\n  width: calc(100vw * 0.112);\r\n  height: calc(100vh * 0.304);\r\n  margin-right: calc(100vh * 0.016);\r\n  background: url(./img/jiguandanweibg.png);\r\n  background-size: 100% 100%;\r\n  position: relative;\r\n}\r\n\r\n.dpJgdwSz {\r\n  width: calc(100vw * 0.032);\r\n  height: calc(100vh * 0.037);\r\n  position: absolute;\r\n  top: calc(100vh * 0.087);\r\n  left: calc(100vw * 0.03);\r\n  font-family: LetsgoDigital-Regular;\r\n  font-size: calc(100vw * 40 / 1920);\r\n  color: #fbde95;\r\n  letter-spacing: 0;\r\n  text-align: center;\r\n  font-weight: 700;\r\n}\r\n\r\n.dpJgdwdw {\r\n  width: calc(100vw * 0.008);\r\n  height: calc(100vh * 0.021);\r\n  position: absolute;\r\n  top: calc(100vh * 0.098);\r\n  left: calc(100vw * 0.067);\r\n  font-size: calc(100vw * 15.58 / 1920);\r\n  font-family: SourceHanSansSC-Regular;\r\n  color: #fbdc8d;\r\n  letter-spacing: 0;\r\n  text-align: center;\r\n  font-weight: 400;\r\n}\r\n\r\n.dpBmxz {\r\n  width: calc(100vw * 0.112);\r\n  height: calc(100vh * 0.304);\r\n  background: url(./img/baomixingzhengguanlibg.png);\r\n  background-size: 100% 100%;\r\n  position: relative;\r\n}\r\n\r\n.dpLeftSm {\r\n  width: calc(100vw * 0.234);\r\n  height: calc(100vh * 0.146);\r\n  margin-bottom: calc(100vh * 0.011);\r\n  background: url(./img/shemirenyuanbg.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  position: relative;\r\n}\r\n\r\n.dpLeftSmcs {\r\n  background: url(./img/shemichangsuobg.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n}\r\n\r\n.dpLeftSmsb {\r\n  background: url(./img/shemishebeibg.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n}\r\n\r\n.dpLeftSmzt {\r\n  background: url(./img/shemizaitibg.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.dpQNum {\r\n  width: calc(100vw * 0.018);\r\n  height: calc(100vh * 0.02);\r\n  position: absolute;\r\n  top: calc(100vh * 0.074);\r\n  left: calc(100vw * 0.022);\r\n  font-family: LetsgoDigital-Regular;\r\n  font-size: calc(100vw * 22 / 1920);\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  text-align: center;\r\n  text-shadow: 0 2px 5px #00173a;\r\n  font-weight: 700;\r\n}\r\n\r\n.dpQWz {\r\n  width: calc(100vw * 0.022);\r\n  height: calc(100vh * 0.019);\r\n  position: absolute;\r\n  top: calc(100vh * 0.095);\r\n  left: calc(100vw * 0.022);\r\n  font-family: LetsgoDigital-Regular;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  text-align: center;\r\n  text-shadow: 0 2px 5px #00173a;\r\n  font-weight: 700;\r\n}\r\n\r\n.dpKNum1 {\r\n  width: calc(100vw * 0.03);\r\n  height: calc(100vh * 0.038);\r\n  position: absolute;\r\n  top: calc(100vh * 0.075);\r\n  left: calc(100vw * 0.06);\r\n  font-family: LetsgoDigital-Regular;\r\n  font-size: calc(100vw * 36 / 1920);\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  text-align: center;\r\n  text-shadow: 0 2px 5px #00173a;\r\n  font-weight: 700;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}\r\n\r\n.dpKNum2 {\r\n  left: calc(100vw * 0.114);\r\n}\r\n\r\n.dpKNum3 {\r\n  left: calc(100vw * 0.168);\r\n}\r\n\r\n.dpKNum1For {\r\n  width: calc(100vw * 0.03);\r\n  height: calc(100vh * 0.038);\r\n  background: url(./img/img_505.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.dpQHx {\r\n  width: calc(100vw * 0.0345);\r\n  height: calc(100vh * 0.0165);\r\n  position: absolute;\r\n  top: calc(100vh * 0.096);\r\n  left: calc(100vw * 0.073);\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  background: url(./img/left-rygl-sz-mc.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  text-align: right;\r\n  text-shadow: 0 2px 5px #00173a;\r\n  font-weight: 700;\r\n  padding-right: calc(100vw * 0.0035);\r\n  padding-bottom: calc(100vh * 0.0015);\r\n}\r\n\r\n.dpQZy {\r\n  left: calc(100vw * 0.127);\r\n}\r\n\r\n.dpQYb {\r\n  left: calc(100vw * 0.181);\r\n}\r\n\r\n.dpMap {\r\n  width: calc(100vw * 0.487);\r\n  height: calc(100vh * 0.51);\r\n  position: absolute;\r\n  top: calc(100vh * 0.153);\r\n  left: calc(100vw * 0.257);\r\n}\r\n\r\n.dpInput {\r\n  width: calc(100vw * 0.224);\r\n  height: calc(100vh * 0.047);\r\n  background: url(./img/sousuo.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  position: absolute;\r\n  top: calc(100vh * 0.681);\r\n  left: calc(100vw * 0.255);\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: space-between;\r\n}\r\n\r\n.dpInputM {\r\n  width: calc(100vw * 0.14);\r\n  height: calc(100vh * 0.032);\r\n  font-family: SourceHanSansSC-Regular;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  color: #b0d2ff;\r\n  letter-spacing: 0;\r\n  font-weight: 400;\r\n}\r\n\r\n/* ::v-deep(.el-input__inner) { */\r\n/deep/ .el-input__inner {\r\n  /* .dpInputM .el-input__inner { */\r\n  background-color: transparent !important;\r\n  border: 0px !important;\r\n  height: calc(100vh * 0.036) !important;\r\n  line-height: calc(100vh * 0.036) !important;\r\n  padding: 0 0 !important;\r\n  margin-left: calc(100vw * 0.006);\r\n  color: #fff !important;\r\n  font-family: SourceHanSansSC-Regular;\r\n}\r\n\r\n.dpSsAn {\r\n  width: calc(100vw * 0.07);\r\n  height: calc(100vh * 0.042);\r\n  font-family: SourceHanSansSC-Medium;\r\n  font-size: calc(100vw * 16 / 1920);\r\n  color: #eeeeff;\r\n  letter-spacing: 0;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.dpTableBtn {\r\n  width: calc(100vw * 0.557);\r\n  height: calc(100vh * 0.222);\r\n  position: absolute;\r\n  top: calc(100vh * 0.749);\r\n  left: calc(100vw * 0.26);\r\n  overflow: hidden;\r\n}\r\n\r\n.dpTableTitle {\r\n  width: 100%;\r\n  height: calc(100vh * 0.037);\r\n  background-image: linear-gradient(\r\n    180deg,\r\n    rgba(8, 28, 78, 0) 0%,\r\n    #0085ff 100%\r\n  );\r\n  margin-bottom: calc(100vh * 0.001);\r\n}\r\n\r\n.dpTableTitle ul li {\r\n  float: left;\r\n  font-family: SourceHanSansSC-Medium;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  color: #ddeeff;\r\n  font-weight: 500;\r\n  line-height: calc(100vh * 0.035);\r\n  text-align: center;\r\n  list-style-type: none;\r\n}\r\n\r\n.dpTableRightTitle {\r\n  width: 100%;\r\n  height: calc(100vh * 0.037);\r\n  background-image: linear-gradient(\r\n    180deg,\r\n    rgba(8, 28, 78, 0) 0%,\r\n    #0085ff 100%\r\n  );\r\n  margin-bottom: calc(100vh * 0.001);\r\n}\r\n\r\n.dpTableRightTitle ul li {\r\n  float: left;\r\n  font-family: SourceHanSansSC-Medium;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  color: #ddeeff;\r\n  font-weight: 500;\r\n  line-height: calc(100vh * 0.035);\r\n  text-align: center;\r\n  list-style-type: none;\r\n}\r\n\r\n.dpTableRightTitle ul li:nth-child(1) {\r\n  width: calc(100vw * 0.02);\r\n}\r\n\r\n.dpTableRightTitle ul li:nth-child(2) {\r\n  width: calc((100% - (100vw * 0.02)) / 2);\r\n}\r\n\r\n.dpTableRightTitle ul li:nth-child(3) {\r\n  width: calc((100% - (100vw * 0.02)) / 2);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(1) {\r\n  width: calc(100vw * 0.033);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(2) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(3) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(4) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(5) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(6) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(7) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(8) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(9) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableTitle ul li:nth-child(10) {\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n}\r\n\r\n.dpTableCon {\r\n  width: 100%;\r\n  height: calc(100vh * 0.189);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.dpTableConZwsj {\r\n  width: 100%;\r\n  height: calc(100vh * 0.189);\r\n  background: rgba(0, 51, 119, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: #fff;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  font-family: SourceHanSansSC-Bold;\r\n}\r\n\r\n.dpTableConMh {\r\n  width: 100%;\r\n  height: calc(100vh * 0.037);\r\n  background: rgba(0, 51, 119, 0.5);\r\n  margin-bottom: calc(100vh * 0.001);\r\n}\r\n\r\n.dpTableRightCon {\r\n  width: 100%;\r\n  height: calc(100vh * 0.37);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.dpTableRightConZwsj {\r\n  width: 100%;\r\n  height: calc(100vh * 0.37);\r\n  background: rgba(0, 51, 119, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: #fff;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  font-family: SourceHanSansSC-Bold;\r\n}\r\n\r\n.dpTableRightConMh {\r\n  width: 100%;\r\n  height: calc(100vh * 0.037);\r\n  background: rgba(0, 51, 119, 0.5);\r\n  margin-bottom: calc(100vh * 0.001);\r\n}\r\n\r\n.table-text1 {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  display: inline-block;\r\n  float: left;\r\n  line-height: calc(100vh * 0.037);\r\n  text-align: center;\r\n  width: calc(100vw * 0.02);\r\n  color: #fff;\r\n}\r\n\r\n.table-text {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  display: inline-block;\r\n  float: left;\r\n  line-height: calc(100vh * 0.037);\r\n  text-align: center;\r\n  width: calc(100vw * 0.033);\r\n  color: #fff;\r\n}\r\n\r\n.tb-item {\r\n  float: left;\r\n  width: calc((100% - (100vw * 0.048)) / 9);\r\n  line-height: calc(100vh * 0.037);\r\n  font-size: calc(100vw * 14 / 1920);\r\n  text-align: center;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  color: #fff;\r\n  white-space: nowrap;\r\n  font-family: SourceHanSansSC-Regular;\r\n}\r\n\r\n.tb-cu {\r\n  cursor: pointer;\r\n}\r\n\r\n.tb-item2 {\r\n  float: left;\r\n  width: calc((100% - (100vw * 0.02)) / 2);\r\n  line-height: calc(100vh * 0.037);\r\n  font-size: calc(100vw * 14 / 1920);\r\n  text-align: center;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  color: #fff;\r\n  white-space: nowrap;\r\n  font-family: SourceHanSansSC-Regular;\r\n}\r\n\r\n.text-color1 {\r\n  color: #fff05f;\r\n}\r\n\r\n.text-color2 {\r\n  color: #6cffd6;\r\n}\r\n\r\n.text-color3 {\r\n  color: #00cbe9;\r\n}\r\n\r\n.dpTableRight {\r\n  width: calc(100vw * 0.089);\r\n  height: calc(100vh * 0.407);\r\n  position: absolute;\r\n  top: calc(100vh * 0.313);\r\n  left: calc(100vw * 0.735);\r\n}\r\n\r\n.dpBingTu {\r\n  width: calc(100vw * 0.18);\r\n  height: calc(100vh * 0.185);\r\n  position: absolute;\r\n  top: calc(100vh * 0.044);\r\n  right: calc(100vw * 0.01);\r\n}\r\n\r\n.dpPfpm {\r\n  width: calc(100vw * 0.137);\r\n  height: calc(100vh * 0.655);\r\n  background: url(./img/pingfenpaimingwaikuang.png) no-repeat center;\r\n  background-size: 100% 100%;\r\n  position: absolute;\r\n  top: calc(100vh * 0.248);\r\n  right: calc(100vw * 0.01);\r\n  padding: calc(100vh * 0.06) calc(100vw * 0.009) calc(100vh * 0.019)\r\n    calc(100vw * 0.01);\r\n}\r\n\r\n.dpPfpmCon {\r\n  width: calc(100vw * 0.137);\r\n  height: calc(100vh * 0.655);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.dpDwphb {\r\n  width: calc(100vw * 0.137);\r\n  height: calc(100vh * 0.028);\r\n  margin-bottom: calc(100vh * 0.013);\r\n}\r\n\r\n.dpJdtTitle {\r\n  width: calc(100vw * 0.137);\r\n  height: calc(100vh * 0.019);\r\n  background-image: linear-gradient(\r\n    270deg,\r\n    rgba(238, 238, 238, 0) 0%,\r\n    rgba(0, 109, 252, 0.5) 100%\r\n  );\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.dpJdtTitleLeft {\r\n  font-family: SourceHanSansSC-Regular;\r\n  font-size: calc(100vw * 14 / 1920);\r\n  color: #ffffff;\r\n  text-shadow: 0 2px 2px rgba(11, 29, 62, 0.5);\r\n  font-weight: 400;\r\n  margin-left: calc(100vw * 0.002);\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.dpJdtTitleRight {\r\n  font-family: LetsgoDigital-Regular;\r\n  font-size: calc(100vw * 18 / 1920);\r\n  color: #6bf0f9;\r\n  letter-spacing: 0;\r\n  text-align: center;\r\n  font-weight: 700;\r\n}\r\n\r\n.dpJdtTitleRight1 {\r\n  color: #89fa56;\r\n}\r\n\r\n.dpJdtTitleRight2 {\r\n  color: #25d06b;\r\n}\r\n\r\n.dpJdtTitleRight3 {\r\n  color: #f9bf20;\r\n}\r\n\r\n.dpJdtTitleRight4 {\r\n  color: #f66505;\r\n}\r\n\r\n.dpJdtTitleRight5 {\r\n  color: #c62732;\r\n}\r\n\r\n/deep/.el-progress {\r\n  line-height: 0;\r\n}\r\n\r\n.custom-progress >>> .el-progress-bar__outer {\r\n  height: calc(100vh * 0.009) !important;\r\n  /* 修改为你想要的高度 */\r\n  background: rgba(4, 43, 103, 0.51) !important;\r\n  border-radius: 0;\r\n}\r\n\r\n.custom-progress >>> .el-progress-bar__inner {\r\n  border-radius: 0px 10px 10px 0;\r\n  background-image: linear-gradient(90deg, #39b1ff 0%, #68ffe9 100%);\r\n  line-height: 0;\r\n}\r\n\r\n.custom-progress1 >>> .el-progress-bar__inner {\r\n  background-image: linear-gradient(270deg, #8cff54 0%, #0f41aa 100%);\r\n}\r\n\r\n.custom-progress2 >>> .el-progress-bar__inner {\r\n  background-image: linear-gradient(270deg, #25d469 0%, #0f41aa 100%);\r\n}\r\n\r\n.custom-progress3 >>> .el-progress-bar__inner {\r\n  background-image: linear-gradient(270deg, #fec21e 0%, #0f41aa 100%);\r\n}\r\n\r\n.custom-progress4 >>> .el-progress-bar__inner {\r\n  background-image: linear-gradient(270deg, #ff6700 0%, #0f41aa 100%);\r\n}\r\n\r\n.custom-progress5 >>> .el-progress-bar__inner {\r\n  background-image: linear-gradient(270deg, #d42529 0%, #0f41aa 100%);\r\n}\r\n\r\n.custom-progress >>> .el-progress-bar__innerText {\r\n  display: none;\r\n}\r\n</style>\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/homePage/index.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"large\",attrs:{\"id\":\"container\"}},[_c('div',{staticClass:\"con\"},[_c('div',{staticClass:\"dpTitle\"},[_c('div',{staticClass:\"dpTitleTime\"},[_vm._v(_vm._s(_vm.currentTime))]),_vm._v(\" \"),_c('div',{staticClass:\"dpTitleLogo\"}),_vm._v(\" \"),_c('div',{staticClass:\"dpTitleZtqk\"},[_vm._v(\"黑河市保密工作整体情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpTitleDyfb\"},[_vm._v(\"机关单位地域分布\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpTitleFh\",attrs:{\"title\":_vm.mcObj.label},on:{\"click\":_vm.fh}},[_vm._v(_vm._s(_vm.mcObj.label))])]),_vm._v(\" \"),_c('div',{staticClass:\"dpLeft\"},[_c('div',{staticClass:\"dpLeftTop\"},[_c('div',{staticClass:\"dpJgdw\"},[_c('div',{staticClass:\"dpJgdwSz\"},[_vm._v(_vm._s(_vm.jgdw))]),_vm._v(\" \"),_c('div',{staticClass:\"dpJgdwdw\"},[_vm._v(\"家\")])]),_vm._v(\" \"),_c('div',{staticClass:\"dpBmxz\"},[_c('div',{staticClass:\"dpJgdwSz\"},[_vm._v(_vm._s(_vm.bmxzgldw))]),_vm._v(\" \"),_c('div',{staticClass:\"dpJgdwdw\"},[_vm._v(\"家\")])])]),_vm._v(\" \"),_c('div',{staticClass:\"dpLeftSm\"},[_c('div',{staticClass:\"dpQNum\"},[_vm._v(_vm._s(_vm.smryObj.total))]),_vm._v(\" \"),_c('div',{staticClass:\"dpQWz\"},[_vm._v(\"总人数\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1\"},[(this.smryObj.hx.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smryObj.hx),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx\"},[_vm._v(\"核心\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1 dpKNum2\"},[(this.smryObj.zy.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smryObj.zy),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx dpQZy\"},[_vm._v(\"重要\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1 dpKNum3\"},[(this.smryObj.yb.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smryObj.yb),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx dpQYb\"},[_vm._v(\"一般\")])]),_vm._v(\" \"),_c('div',{staticClass:\"dpLeftSm dpLeftSmcs\"},[_c('div',{staticClass:\"dpQNum\"},[_vm._v(_vm._s(_vm.smcsObj.total))]),_vm._v(\" \"),_c('div',{staticClass:\"dpQWz\"},[_vm._v(\"总场所\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1\"},[(this.smcsObj.hx.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smcsObj.hx),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx\"},[_vm._v(\"核心\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1 dpKNum2\"},[(this.smcsObj.zy.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smcsObj.zy),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx dpQZy\"},[_vm._v(\"重要\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1 dpKNum3\"},[(this.smcsObj.yb.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smcsObj.yb),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx dpQYb\"},[_vm._v(\"一般\")])]),_vm._v(\" \"),_c('div',{staticClass:\"dpLeftSm dpLeftSmsb\"},[_c('div',{staticClass:\"dpQNum\"},[_vm._v(_vm._s(_vm.smsbObj.total))]),_vm._v(\" \"),_c('div',{staticClass:\"dpQWz\"},[_vm._v(\"总设备\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1\"},[(this.smsbObj.hx.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smsbObj.hx),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx\"},[_vm._v(\"核心\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1 dpKNum2\"},[(this.smsbObj.zy.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smsbObj.zy),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx dpQZy\"},[_vm._v(\"重要\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1 dpKNum3\"},[(this.smsbObj.yb.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smsbObj.yb),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx dpQYb\"},[_vm._v(\"一般\")])]),_vm._v(\" \"),_c('div',{staticClass:\"dpLeftSm dpLeftSmzt\"},[_c('div',{staticClass:\"dpQNum\"},[_vm._v(_vm._s(_vm.smztObj.total))]),_vm._v(\" \"),_c('div',{staticClass:\"dpQWz\"},[_vm._v(\"总载体\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1\"},[(this.smztObj.hx.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smztObj.hx),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx\"},[_vm._v(\"核心\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1 dpKNum2\"},[(this.smztObj.zy.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smztObj.zy),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx dpQZy\"},[_vm._v(\"重要\")]),_vm._v(\" \"),_c('div',{staticClass:\"dpKNum1 dpKNum3\"},[(this.smztObj.yb.length == 1)?_c('div',{staticClass:\"dpKNum1For\"},[_vm._v(\"0\")]):_vm._e(),_vm._v(\" \"),_vm._l((this.smztObj.yb),function(item,index){return _c('div',{key:index,staticClass:\"dpKNum1For\"},[_vm._v(\"\\n            \"+_vm._s(item)+\"\\n          \")])})],2),_vm._v(\" \"),_c('div',{staticClass:\"dpQHx dpQYb\"},[_vm._v(\"一般\")])])]),_vm._v(\" \"),_c('div',{staticClass:\"dpMap\"},[_c('dt1',{ref:\"dt\"})],1),_vm._v(\" \"),_c('div',{staticClass:\"dpInput\"},[_c('el-input',{staticClass:\"dpInputM\",attrs:{\"placeholder\":\"单位排名情况\"},model:{value:(_vm.dwpmqk),callback:function ($$v) {_vm.dwpmqk=$$v},expression:\"dwpmqk\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dpSsAn\",on:{\"click\":_vm.search}},[_vm._v(\"搜索\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"dpTableBtn\"},[_vm._m(0),_vm._v(\" \"),(this.dwData.length != 0)?_c('div',{staticClass:\"dpTableCon\"},_vm._l((_vm.dwData),function(item,index){return _c('div',{key:index,staticClass:\"dpTableConMh\"},[_c('span',{staticClass:\"table-text\"},[_vm._v(\"\\n            \"+_vm._s(index < 9 ? 0 : \"\")+_vm._s(index + 1)+\"\\n          \")]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item tb-cu\",attrs:{\"title\":item.dwmc},on:{\"click\":function($event){return _vm.rClick(item)}}},[_vm._v(\"\\n            \"+_vm._s(item.dwmc)+\"\\n          \")]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item\",attrs:{\"title\":item.fs}},[_vm._v(_vm._s(item.fs))]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item\",attrs:{\"title\":item.zzjg}},[_vm._v(_vm._s(item.zzjg))]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item\",attrs:{\"title\":item.smgw}},[_vm._v(_vm._s(item.smgw))]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item\",attrs:{\"title\":item.smry}},[_vm._v(_vm._s(item.smry))]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item\",attrs:{\"title\":item.smcs}},[_vm._v(_vm._s(item.smcs))]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item\",attrs:{\"title\":item.smsb}},[_vm._v(_vm._s(item.smsb))]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item\",attrs:{\"title\":item.smzt}},[_vm._v(_vm._s(item.smzt))]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item\",attrs:{\"title\":item.jypx}},[_vm._v(_vm._s(item.jypx))])])}),0):_vm._e(),_vm._v(\" \"),(this.dwData.length == 0)?_c('div',{staticClass:\"dpTableConZwsj\"},[_vm._v(\"\\n        暂无数据\\n      \")]):_vm._e()]),_vm._v(\" \"),_c('div',{staticClass:\"dpTableRight\"},[_vm._m(1),_vm._v(\" \"),(this.xqData.length != 0)?_c('div',{staticClass:\"dpTableRightCon\"},_vm._l((_vm.xqData),function(item,index){return _c('div',{key:index,staticClass:\"dpTableRightConMh\"},[_c('span',{staticClass:\"table-text1\"},[_vm._v(\"\\n            \"+_vm._s(index < 9 ? 0 : \"\")+_vm._s(index + 1)+\"\\n          \")]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item2\",attrs:{\"title\":item.name}},[_vm._v(_vm._s(item.name))]),_vm._v(\" \"),_c('p',{staticClass:\"tb-item2\",attrs:{\"title\":item.count}},[_vm._v(_vm._s(item.count))])])}),0):_vm._e(),_vm._v(\" \"),(this.xqData.length == 0)?_c('div',{staticClass:\"dpTableRightConZwsj\"},[_vm._v(\"\\n        暂无数据\\n      \")]):_vm._e()]),_vm._v(\" \"),_c('div',{staticClass:\"dpBingTu\"},[_c('bing',{on:{\"valueChanged\":_vm.handleValueChanged}})],1),_vm._v(\" \"),_c('div',{staticClass:\"dpPfpm\"},[_c('div',{staticClass:\"dpPfpmCon\"},_vm._l((_vm.pfData),function(item,index){return _c('div',{key:index,staticClass:\"dpDwphb\"},[_c('div',{staticClass:\"dpJdtTitle\"},[_c('div',{staticClass:\"dpJdtTitleLeft\",attrs:{\"title\":item.dwmc}},[_vm._v(\"\\n              \"+_vm._s(item.dwmc)+\"\\n            \")]),_vm._v(\" \"),(item.fs > 95)?_c('div',{staticClass:\"dpJdtTitleRight dpJdtTitleRight5\"},[_vm._v(\"\\n              \"+_vm._s(item.fs)+\"%\\n            \")]):_vm._e(),_vm._v(\" \"),(item.fs > 92 && item.fs <= 95)?_c('div',{staticClass:\"dpJdtTitleRight dpJdtTitleRight4\"},[_vm._v(\"\\n              \"+_vm._s(item.fs)+\"%\\n            \")]):_vm._e(),_vm._v(\" \"),(item.fs > 90 && item.fs <= 92)?_c('div',{staticClass:\"dpJdtTitleRight dpJdtTitleRight3\"},[_vm._v(\"\\n              \"+_vm._s(item.fs)+\"%\\n            \")]):_vm._e(),_vm._v(\" \"),(item.fs > 89 && item.fs <= 90)?_c('div',{staticClass:\"dpJdtTitleRight dpJdtTitleRight2\"},[_vm._v(\"\\n              \"+_vm._s(item.fs)+\"%\\n            \")]):_vm._e(),_vm._v(\" \"),(item.fs > 86 && item.fs <= 89)?_c('div',{staticClass:\"dpJdtTitleRight dpJdtTitleRight1\"},[_vm._v(\"\\n              \"+_vm._s(item.fs)+\"%\\n            \")]):_vm._e(),_vm._v(\" \"),(item.fs <= 86)?_c('div',{staticClass:\"dpJdtTitleRight\"},[_vm._v(\"\\n              \"+_vm._s(item.fs)+\"%\\n            \")]):_vm._e()]),_vm._v(\" \"),_c('div',{staticClass:\"dpJdtTx\"},[(item.fs > 95)?_c('el-progress',{staticClass:\"custom-progress custom-progress5\",attrs:{\"text-inside\":true,\"percentage\":item.fs}}):_vm._e(),_vm._v(\" \"),(item.fs > 92 && item.fs <= 95)?_c('el-progress',{staticClass:\"custom-progress custom-progress4\",attrs:{\"text-inside\":true,\"percentage\":item.fs}}):_vm._e(),_vm._v(\" \"),(item.fs > 90 && item.fs <= 92)?_c('el-progress',{staticClass:\"custom-progress custom-progress3\",attrs:{\"text-inside\":true,\"percentage\":item.fs}}):_vm._e(),_vm._v(\" \"),(item.fs > 89 && item.fs <= 90)?_c('el-progress',{staticClass:\"custom-progress custom-progress2\",attrs:{\"text-inside\":true,\"percentage\":item.fs}}):_vm._e(),_vm._v(\" \"),(item.fs > 86 && item.fs <= 89)?_c('el-progress',{staticClass:\"custom-progress custom-progress1\",attrs:{\"text-inside\":true,\"percentage\":item.fs}}):_vm._e(),_vm._v(\" \"),(item.fs <= 86)?_c('el-progress',{staticClass:\"custom-progress\",attrs:{\"text-inside\":true,\"percentage\":item.fs}}):_vm._e()],1)])}),0)])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"dpTableTitle\"},[_c('ul',[_c('li',[_vm._v(\"序号\")]),_vm._v(\" \"),_c('li',[_vm._v(\"单位名称\")]),_vm._v(\" \"),_c('li',[_vm._v(\"分数\")]),_vm._v(\" \"),_c('li',[_vm._v(\"组织机构\")]),_vm._v(\" \"),_c('li',[_vm._v(\"涉密岗位\")]),_vm._v(\" \"),_c('li',[_vm._v(\"涉密人员\")]),_vm._v(\" \"),_c('li',[_vm._v(\"涉密场所\")]),_vm._v(\" \"),_c('li',[_vm._v(\"涉密设备\")]),_vm._v(\" \"),_c('li',[_vm._v(\"涉密载体\")]),_vm._v(\" \"),_c('li',[_vm._v(\"教育培训\")])])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"dpTableRightTitle\"},[_c('ul',[_c('li',[_vm._v(\"序号\")]),_vm._v(\" \"),_c('li',[_vm._v(\"县区\")]),_vm._v(\" \"),_c('li',[_vm._v(\"单位数量\")])])])}]\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-74bf911b\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/homePage/index.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-74bf911b\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./index.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./index.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./index.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-74bf911b\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./index.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-74bf911b\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/homePage/index.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}