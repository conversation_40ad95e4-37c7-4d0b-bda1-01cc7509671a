{"version": 3, "sources": ["webpack:///src/renderer/view/wdgz/blsp/cgjblxxscb.vue", "webpack:///./src/renderer/view/wdgz/blsp/cgjblxxscb.vue?9592", "webpack:///./src/renderer/view/wdgz/blsp/cgjblxxscb.vue"], "names": ["cgjblxxscb", "components", "AddLineTable", "props", "data", "headerCellStyle", "background", "color", "fwdyid", "slid", "activeName", "spznList", "checkList", "tjlist", "sm<PERSON><PERSON>", "xm", "xb", "gj", "dwzwzc", "yrsmgw", "cym", "mz", "hyzk", "zzmm", "lxdh", "sfzhm", "hjdz", "hjdgajg", "czdz", "czgajg", "imageUrl", "yjqk", "qscfqk", "qtqk", "brcn", "splx", "value1", "zzhmList", "zzid", "fjlb", "zjhm", "yxq", "checked", "deb", "ryglRyscJtcyList", "gx", "nl", "jwjlqk", "cgszd", "zw", "czbtn1", "czbtn2", "gjclList", "upccLsit", "disabled1", "disabled2", "disabled3", "btnsftg", "btnsfth", "jgyf", "id", "yjgwqk", "yw", "bmjysfwc", "sfwc", "scqk", "sfty", "zzmmoptions", "fileList", "dialogVisible_brcn", "dialogVisible", "sltshow", "sltbmcnsshow", "sltwtsshow", "dialogImageUrl", "dialogThtxVisible", "dialogBmcnsImageUrl", "dialogBmcnsVisible", "dialogWtsImageUrl", "dialogWtsVisible", "fileRow", "filebmcnsRow", "filewtsRow", "fileryxxRow", "filexzglRow", "ylth", "ylcn", "ylwt", "smryList", "page", "pageSize", "total", "formInline", "bmmc", "selectlistRow", "xsyc", "mbh<PERSON>", "imageUrlbrcn", "ylxy", "yldis", "file", "bmcnssmj", "bmxyssmj", "dialogVisible_bmcns", "bmcnsImageUrl", "dialogVisible_bmxys", "bmxysImageUrl", "show", "show1", "typezt", "lcgzList", "computed", "mounted", "this", "$route", "query", "console", "log", "lx", "spzn", "spxx", "lcgz", "methods", "zzxx", "chRadio", "returnIndex", "$router", "push", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "params", "wrap", "_context", "prev", "next", "Object", "wdgz", "sent", "code", "content", "stop", "fhry", "path", "row", "_this2", "_callee2", "result", "bmC", "zwC", "_context2", "cgjsc", "ryglCgcj", "<PERSON><PERSON><PERSON>", "zzmmxx", "jbzc", "bmzwzc", "smdjxx", "smdj", "toString", "undefined", "ryglCgcjSwzjList", "for<PERSON>ach", "item", "bmcns", "xqbmjyqkb", "hfjlb", "qssj", "jssj", "ryglCgcjTxqkList", "length", "map", "csny", "substring", "yulan", "iamgeBase64", "_validDataUrl", "s", "regex", "test", "ljbl", "beforeAvatarUpload", "isJPG", "type", "isPNG", "$message", "error", "zpzm", "zp", "zpxx", "_validDataUrl2", "ylbmtxth", "routeType", "ylbmcns", "ylwts", "blobToBase64", "blob", "callback", "fileReader", "FileReader", "onload", "e", "target", "readAsDataURL", "bmcnsyl", "cnssmj", "_validDataUrl3", "bmxysyl", "xyssmj", "_validDataUrl4", "_this3", "_callee3", "_context3", "watch", "blsp_cgjblxxscb", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "directives", "name", "rawName", "value", "expression", "attrs", "size", "on", "click", "_v", "model", "$$v", "label", "border", "header-cell-style", "stripe", "width", "align", "prop", "ref", "label-width", "placeholder", "clearable", "disabled", "$set", "scopedSlots", "_u", "key", "fn", "scope", "staticStyle", "display", "flex-direction", "_l", "index", "align-items", "justify-content", "_s", "blur", "format", "value-format", "range-separator", "start-placeholder", "end-placeholder", "position", "visible", "update:visible", "$event", "src", "alt", "slot", "change", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "4KAgYAA,cACAC,YACAC,uBAAA,GAEAC,SACAC,KALA,WAMA,OAEAC,iBACAC,WAAA,UACAC,MAAA,WAEAC,OAAA,GACAC,KAAA,GACAC,WAAA,SAEAC,YACAC,aAGAC,QACAC,OAAA,GACAC,GAAA,GACAC,GAAA,GACAC,GAAA,KACAC,OAAA,GACAC,OAAA,GACAC,IAAA,GACAC,GAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,QAAA,GACAC,KAAA,GACAC,OAAA,GACAC,SAAA,GACAC,KAAA,IACAC,OAAA,IACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,WAEAC,WAEAC,KAAA,EACAC,KAAA,OACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,UACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,UACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,OACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,UACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAJ,KAAA,EACAC,KAAA,UACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAIAC,KAAA,EAEAC,mBACAC,GAAA,GACAC,GAAA,GACAvB,KAAA,GACAwB,OAAA,GACAhC,GAAA,GACAiC,MAAA,GACAC,GAAA,GACAC,OAAA,MACAC,OAAA,KAGAC,YACAC,YAEAC,WAAA,EACAC,WAAA,EACAC,WAAA,EACAC,SAAA,EACAC,SAAA,EACAC,KAAA,GAEA3C,KACAA,GAAA,IACA4C,GAAA,IAGA5C,GAAA,IACA4C,GAAA,IAIAC,SACAC,GAAA,IACAF,GAAA,IAGAE,GAAA,IACAF,GAAA,IAIAG,WACAC,KAAA,MACAJ,GAAA,IAGAI,KAAA,MACAJ,GAAA,IAGAK,OACAC,KAAA,KACAN,GAAA,IAGAM,KAAA,MACAN,GAAA,IAIAO,eACAC,YAEAC,oBAAA,EACAC,eAAA,EACAC,QAAA,GACAC,aAAA,GACAC,WAAA,GACAC,eAAA,GACAC,mBAAA,EACAC,oBAAA,GACAC,oBAAA,EACAC,kBAAA,GACAC,kBAAA,EACAC,QAAA,GACAC,aAAA,GACAC,WAAA,GACAC,YAAA,GACAC,YAAA,GACAC,MAAA,EACAC,MAAA,EACAC,MAAA,EAEAC,YACAC,KAAA,EACAC,SAAA,GACAC,MAAA,EACAC,YACAC,KAAA,GACA9E,GAAA,IAEA+E,iBACAC,MAAA,EACAC,OAAA,GACAlE,SAAA,GACAmE,aAAA,GACAC,MAAA,EACAC,OAAA,EACAC,QACAC,SAAA,GACAC,SAAA,GAEAC,qBAAA,EACAC,cAAA,GAEAC,qBAAA,EACAC,cAAA,GAEAC,MAAA,EACAC,OAAA,EACAC,OAAA,GAEAC,cAIAC,YACAC,QAlNA,WAmNAC,KAAAJ,OAAAI,KAAAC,OAAAC,MAAAN,OACA,QAAAI,KAAAJ,SACAI,KAAAtE,KAAA,GAEAsE,KAAAzG,OAAAyG,KAAAC,OAAAC,MAAA3G,OACA4G,QAAAC,IAAA,cAAAJ,KAAAzG,QACAyG,KAAAxG,KAAAwG,KAAAC,OAAAC,MAAA1G,KACA2G,QAAAC,IAAA,YAAAJ,KAAAxG,MACAwG,KAAAK,GAAAL,KAAAC,OAAAC,MAAAG,GACAF,QAAAC,IAAAJ,KAAAK,IAEAL,KAAAM,OAGAN,KAAAO,OAQAP,KAAAQ,QAGAC,SACAC,KADA,WAEAP,QAAAC,IAAAJ,KAAArG,YAEAgH,QAJA,aAMAC,YANA,WAOAZ,KAAAa,QAAAC,KAAA,WAIAR,KAXA,WAWA,IAAAS,EAAAf,KAAA,OAAAgB,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAlI,EAAA,OAAA8H,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACAJ,GACA9H,OAAAwH,EAAAxH,QAFAgI,EAAAE,KAAA,EAIAC,OAAAC,EAAA,EAAAD,CAAAL,GAJA,OAKA,MADAlI,EAJAoI,EAAAK,MAKAC,OACAd,EAAArH,SAAAP,OAAA2I,SANA,wBAAAP,EAAAQ,SAAAX,EAAAL,KAAAC,IAoBAgB,KA/BA,WAgCAhC,KAAAa,QAAAC,MACAmB,KAAA,WACA/B,OACAgC,IAAAlC,KAAAC,OAAAC,MAAAgC,QAIA3B,KAvCA,WAuCA,IAAA4B,EAAAnC,KAAA,OAAAgB,IAAAC,EAAAC,EAAAC,KAAA,SAAAiB,IAAA,IAAAf,EAAAlI,EAAAkJ,EAAAC,EAAAC,EAAA,OAAAtB,EAAAC,EAAAI,KAAA,SAAAkB,GAAA,cAAAA,EAAAhB,KAAAgB,EAAAf,MAAA,cACAJ,GACA7H,KAAA2I,EAAA3I,MAEAL,OAJA,EAAAqJ,EAAAf,KAAA,EAKAC,OAAAe,EAAA,EAAAf,CAAAL,GALA,OAKAlI,EALAqJ,EAAAZ,KAMAzB,QAAAC,IAAAjH,GACAgJ,EAAAvI,OAAAT,OAAAuJ,UACAL,EAAAlJ,OAAAuJ,UAEAC,QAAA,GAAAN,EAAAtI,GAAA,OAAAsI,EAAAtI,GAAA,OACAsI,EAAAhI,KAAA,GAAAgI,EAAAhI,KAAA,QAAAgI,EAAAhI,KAAA,QACAgI,EAAAO,OAAA,GAAAP,EAAA/H,KAAA,UAAA+H,EAAA/H,KAAA,QAAA+H,EAAA/H,KAAA,UAAA+H,EAAA/H,KAAA,QACA+H,EAAAQ,KAAA,GAAAR,EAAAQ,KAAA,SAAAR,EAAAQ,KAAA,SAAAR,EAAAQ,KAAA,SAAAR,EAAAQ,KAAA,YAAAR,EAAAQ,KAAA,gBAAAR,EAAAQ,KAAA,QAAAR,EAAAQ,KAAA,WAAAR,EAAAQ,KAAA,WAAAR,EAAAQ,KAAA,WAAAR,EAAAQ,KAAA,WAAAR,EAAAQ,KAAA,QACAP,EAAA,IAAAD,EAAAzD,KAAA,MAAAyD,EAAAzD,KAAA,OACA2D,EAAA,IAAAF,EAAArG,GAAA,MAAAqG,EAAArG,GAAA,OACAqG,EAAAS,OAAAR,EAAAC,EAAA,MAAAF,EAAAQ,KAEAR,EAAAU,OAAA,GAAAV,EAAAW,KAAA,QAAAX,EAAAW,KAAA,QAAAX,EAAAW,KAAA,QACAb,EAAAvI,OAAAyI,EAEA,GAAAA,EAAAnH,MAAA,GAAAmH,EAAAnH,KACAiH,EAAAvI,OAAAsB,KAAAmH,EAAAnH,KAAA+H,YAEA9C,QAAAC,IAAA,KACA+B,EAAAvI,OAAAsB,KAAA,SAEAgI,GAAA/J,OAAAgK,mBACAhB,EAAA/G,SAAAjC,OAAAgK,kBAEAhB,EAAA/G,SAAAgI,QAAA,SAAAC,GACA,GAAAA,EAAA5H,SACA0G,EAAAxI,UAAAmH,KAAAuC,EAAAhI,QAGA,IAAA8G,EAAAvI,OAAA0J,QACAnB,EAAA/D,MAAA,GAEA,IAAA+D,EAAAvI,OAAA2J,YACApB,EAAA9D,MAAA,GAEA,IAAA8D,EAAAvI,OAAA4J,QACArB,EAAA7D,MAAA,GAEA,IAAA+D,EAAAoB,WAAAP,GAAAb,EAAAoB,OACAtB,EAAAvI,OAAAuB,UACAgH,EAAAvI,OAAAuB,OAAA2F,KAAAuB,EAAAoB,MACAtB,EAAAvI,OAAAuB,OAAA2F,KAAAuB,EAAAqB,OAEA,GAAAvK,OAAAwK,iBAAAC,OACAzB,EAAAxG,mBACAC,GAAA,GACAC,GAAA,GACAvB,KAAA,GACAwB,OAAA,GACAhC,GAAA,GACAiC,MAAA,GACAC,GAAA,GACAC,OAAA,MACAC,OAAA,KAGAiG,EAAAxG,iBAAAxC,OAAAwK,iBAAAE,IAAA,SAAA1K,GAQA,OAFAA,EAAA8C,OAAA,MACA9C,EAAA+C,OAAA,KACA/C,IAGAgJ,EAAAvI,OAAAkK,KAAA3B,EAAAvI,OAAAY,MAAAuJ,UAAA,UAAA5B,EAAAvI,OAAAY,MAAAuJ,UAAA,WAAA5B,EAAAvI,OAAAY,MAAAuJ,UAAA,OAzEA,yBAAAvB,EAAAT,SAAAK,EAAAD,KAAAnB,IA8EAgD,MArHA,WAsHAhE,KAAA5C,oBAAA,EACA,IAaAiG,EAbAY,EAAA,0BAAAjE,KAAApG,OAAAqB,KACA,oBAAAgJ,EAAA,KAGAC,EAAA,SAAAA,EAAAC,GACA,OAAAD,EAAAE,MAAAC,KAAAF,IAFA,IAAAF,EAAA,OAMA,GAFAC,EAAAE,MACA,6GACAF,EAAAD,GAAA,CAIAZ,EAGAY,EALAjE,KAGAhB,aAAAqE,KA0FAiB,KA/NA,WAgOAtE,KAAAvG,WAAA,UAoHA8K,mBApVA,SAoVApF,GACA,IAAAqF,EAAA,eAAArF,EAAAsF,KACAC,EAAA,cAAAvF,EAAAsF,KAIA,OAHAD,GAAAE,GACA1E,KAAA2E,SAAAC,MAAA,wBAEAJ,GAAAE,GAEAG,KA5VA,SA4VAC,GACA,IAAAb,EAAA,0BAAAa,EACAC,OAAA,EACA,oBAAAd,EAAA,KAGAe,EAAA,SAAAA,EAAAb,GACA,OAAAa,EAAAZ,MAAAC,KAAAF,IAFA,IAAAF,EAAA,OAMA,GAFAe,EAAAZ,MACA,6GACAY,EAAAf,GAAA,CAKAc,EAEAd,GAGA,OAAAc,GAGAE,SApXA,WAqXA,IAAAF,EACA5E,QAAAC,IAAAJ,KAAAkF,WACAH,EAAA/E,KAAA6E,KAAA7E,KAAApG,OAAA0J,OACAtD,KAAAvC,eAAAsH,EACA/E,KAAAtC,mBAAA,GAGAyH,QA5XA,WA6XA,IAAAJ,EACAA,EAAA/E,KAAA6E,KAAA7E,KAAApG,OAAA2J,WACAvD,KAAArC,oBAAAoH,EACA/E,KAAApC,oBAAA,GAGAwH,MAnYA,WAoYA,IAAAL,EACA5E,QAAAC,IAAAJ,KAAAkF,WACAH,EAAA/E,KAAA6E,KAAA7E,KAAApG,OAAA4J,OACAxD,KAAAnC,kBAAAkH,EACA/E,KAAAlC,kBAAA,GAGAuH,aA3YA,SA2YAC,EAAAC,GACA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAA,SAAAC,GACAJ,EAAAI,EAAAC,OAAAvD,SAEAmD,EAAAK,cAAAP,IAGAQ,QAnZA,WAoZA9F,KAAAV,qBAAA,EACA,IAaA+D,EAbAY,EAAA,0BAAAjE,KAAApG,OAAAmM,OACA,oBAAA9B,EAAA,KAGA+B,EAAA,SAAAA,EAAA7B,GACA,OAAA6B,EAAA5B,MAAAC,KAAAF,IAFA,IAAAF,EAAA,OAMA,GAFA+B,EAAA5B,MACA,6GACA4B,EAAA/B,GAAA,CAIAZ,EAGAY,EALAjE,KAGAT,cAAA8D,KAOA4C,QA1aA,WA2aAjG,KAAAR,qBAAA,EACA,IAaA6D,EAbAY,EAAA,0BAAAjE,KAAApG,OAAAsM,OACA,oBAAAjC,EAAA,KAGAkC,EAAA,SAAAA,EAAAhC,GACA,OAAAgC,EAAA/B,MAAAC,KAAAF,IAFA,IAAAF,EAAA,OAMA,GAFAkC,EAAA/B,MACA,6GACA+B,EAAAlC,GAAA,CAIAZ,EAGAY,EALAjE,KAGAP,cAAA4D,KAiEA7C,KA3fA,WA2fA,IAAA4F,EAAApG,KAAA,OAAAgB,IAAAC,EAAAC,EAAAC,KAAA,SAAAkF,IAAA,IAAAhF,EAAAlI,EAAA,OAAA8H,EAAAC,EAAAI,KAAA,SAAAgF,GAAA,cAAAA,EAAA9E,KAAA8E,EAAA7E,MAAA,cACAJ,GACA9H,OAAA6M,EAAA7M,OACAC,KAAA4M,EAAA5M,MAHA8M,EAAA7E,KAAA,EAKAC,OAAAC,EAAA,EAAAD,CAAAL,GALA,OAMA,MADAlI,EALAmN,EAAA1E,MAMAC,OACAuE,EAAAvG,SAAA1G,OAAA2I,QACAsE,EAAAjK,SAAAhD,OAAA2I,SARA,wBAAAwE,EAAAvE,SAAAsE,EAAAD,KAAApF,KAYAuF,WChnCeC,GADEC,OAFjB,WAA0B,IAAAC,EAAA1G,KAAa2G,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,kBAA4BF,EAAA,aAAkBG,aAAaC,KAAA,OAAAC,QAAA,SAAAC,MAAAT,EAAA,IAAAU,WAAA,QAA8DL,YAAA,OAAAM,OAA4B5C,KAAA,UAAA6C,KAAA,SAAgCC,IAAKC,MAAAd,EAAA1E,QAAkB0E,EAAAe,GAAA,QAAAf,EAAAe,GAAA,KAAAZ,EAAA,WAA2Ca,OAAOP,MAAAT,EAAA,WAAAnB,SAAA,SAAAoC,GAAgDjB,EAAAjN,WAAAkO,GAAmBP,WAAA,gBAA0BP,EAAA,eAAoBQ,OAAOO,MAAA,OAAAX,KAAA,WAA+BJ,EAAA,OAAYE,YAAA,0CAAoDF,EAAA,aAAkBE,YAAA,KAAAM,OAAwB5C,KAAA,WAAiB8C,IAAKC,MAAAd,EAAApC,QAAkBoC,EAAAe,GAAA,cAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAkDE,YAAA,eAAAM,OAAkCQ,OAAA,GAAA1O,KAAAuN,EAAAhN,SAAAoO,qBAAqDzO,WAAA,UAAAC,MAAA,WAA0CyO,OAAA,MAAclB,EAAA,mBAAwBQ,OAAO5C,KAAA,QAAAuD,MAAA,KAAAJ,MAAA,KAAAK,MAAA,YAA2DvB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,OAAAN,MAAA,WAA8B,OAAAlB,EAAAe,GAAA,KAAAZ,EAAA,eAAwCQ,OAAOO,MAAA,OAAAX,KAAA,YAAgCJ,EAAA,KAAUE,YAAA,kBAA4BL,EAAAe,GAAA,eAAAf,EAAAe,GAAA,KAAAZ,EAAA,KAA4CE,YAAA,cAAwBL,EAAAe,GAAA,UAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAyCE,YAAA,uBAAiCF,EAAA,WAAgBsB,IAAA,WAAAd,OAAsBK,MAAAhB,EAAA9M,OAAAwO,cAAA,WAA0CvB,EAAA,OAAYE,YAAA,uBAAiCF,EAAA,OAAYE,YAAA,kBAA4BF,EAAA,gBAAqBQ,OAAOO,MAAA,QAAcf,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,GAAA2L,SAAA,SAAAoC,GAA+CjB,EAAA8B,KAAA9B,EAAA9M,OAAA,KAAA+N,IAAgCP,WAAA,gBAAyB,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCQ,OAAOO,MAAA,MAAaa,YAAA/B,EAAAgC,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAhC,EAAA,YAAuBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,QAAA2L,SAAA,SAAAoC,GAAoDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,UAAA+N,IAAqCP,WAAA,2BAAqCV,EAAAe,GAAA,KAAAZ,EAAA,gBAAiCQ,OAAOO,MAAA,WAAiBf,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,KAAA2L,SAAA,SAAAoC,GAAiDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,OAAA+N,IAAkCP,WAAA,kBAA2B,OAAAV,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,kBAA4BF,EAAA,gBAAqBQ,OAAOO,MAAA,UAAgBf,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,OAAA2L,SAAA,SAAAoC,GAAmDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,SAAA+N,IAAoCP,WAAA,oBAA6B,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCQ,OAAOO,MAAA,UAAgBf,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,KAAA2L,SAAA,SAAAoC,GAAiDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,OAAA+N,IAAkCP,WAAA,kBAA2B,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCQ,OAAOO,MAAA,QAAea,YAAA/B,EAAAgC,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAhC,EAAA,YAAuBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,OAAA2L,SAAA,SAAAoC,GAAmDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,SAAA+N,IAAoCP,WAAA,2BAAoC,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,kBAA4BF,EAAA,gBAAqBQ,OAAOO,MAAA,WAAkBa,YAAA/B,EAAAgC,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAhC,EAAA,YAAuBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,OAAA2L,SAAA,SAAAoC,GAAmDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,SAAA+N,IAAoCP,WAAA,0BAAoCV,EAAAe,GAAA,KAAAZ,EAAA,gBAAiCQ,OAAOO,MAAA,UAAgBf,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,MAAA2L,SAAA,SAAAoC,GAAkDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,QAAA+N,IAAmCP,WAAA,mBAA4B,SAAAV,EAAAe,GAAA,KAAAZ,EAAA,KAAgCE,YAAA,cAAwBL,EAAAe,GAAA,UAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAyCE,YAAA,uBAAiCF,EAAA,WAAgBsB,IAAA,WAAAd,OAAsBK,MAAAhB,EAAA9M,OAAAwO,cAAA,WAA0CvB,EAAA,OAAYE,YAAA,uBAAiCF,EAAA,OAAYE,YAAA,4BAAsCF,EAAA,gBAAqBQ,OAAOO,MAAA,YAAkBf,EAAA,YAAiBQ,OAAOO,MAAA,IAAAW,SAAA,IAA0Bb,OAAQP,MAAAT,EAAA9M,OAAA,KAAA2L,SAAA,SAAAoC,GAAiDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,OAAA+N,IAAkCP,WAAA,iBAA2BV,EAAAe,GAAA,eAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAmDQ,OAAOO,MAAA,IAAAW,SAAA,IAA0Bb,OAAQP,MAAAT,EAAA9M,OAAA,KAAA2L,SAAA,SAAAoC,GAAiDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,OAAA+N,IAAkCP,WAAA,iBAA2BV,EAAAe,GAAA,qBAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAoDE,YAAA,oCAA8CF,EAAA,gBAAqBQ,OAAOO,MAAA,UAAgBf,EAAA,OAAYiC,aAAaC,QAAA,OAAAC,iBAAA,WAA4CtC,EAAAuC,GAAAvC,EAAA,kBAAArD,EAAA6F,GAA4C,OAAArC,EAAA,OAAiB8B,IAAAtF,EAAAhI,OAAcwL,EAAA,OAAYiC,aAAaC,QAAA,OAAAI,cAAA,SAAAC,kBAAA,mBAA2EvC,EAAA,qBAA0BQ,OAAOkB,SAAA,IAAcb,OAAQP,MAAAT,EAAA,UAAAnB,SAAA,SAAAoC,GAA+CjB,EAAA/M,UAAAgO,GAAkBP,WAAA,eAAyBP,EAAA,eAAoBiC,aAAad,MAAA,SAAgBX,OAAQO,MAAAvE,EAAAhI,QAAmBqL,EAAAe,GAAAf,EAAA2C,GAAAhG,EAAA/H,UAAA,GAAAoL,EAAAe,GAAA,KAAAZ,EAAA,OAAAH,EAAAe,GAAA,SAAAZ,EAAA,YAAuFiC,aAAad,MAAA,SAAgBX,OAAQkB,SAAA,IAAchB,IAAK+B,KAAA5C,EAAAhG,MAAgBgH,OAAQP,MAAA9D,EAAA,KAAAkC,SAAA,SAAAoC,GAA2CjB,EAAA8B,KAAAnF,EAAA,OAAAsE,IAA4BP,WAAA,gBAAyB,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,OAAAH,EAAAe,GAAA,QAAAZ,EAAA,kBAAgEiC,aAAad,MAAA,SAAgBX,OAAQ5C,KAAA,OAAA4D,YAAA,OAAAkB,OAAA,aAAAC,eAAA,aAAAjB,SAAA,IAAmGb,OAAQP,MAAA9D,EAAA,IAAAkC,SAAA,SAAAoC,GAA0CjB,EAAA8B,KAAAnF,EAAA,MAAAsE,IAA2BP,WAAA,eAAwB,WAAY,SAAAV,EAAAe,GAAA,KAAAZ,EAAA,OAAiCE,YAAA,4BAAsCF,EAAA,gBAAqBQ,OAAOO,MAAA,kBAAwBf,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,GAAA2L,SAAA,SAAAoC,GAA+CjB,EAAA8B,KAAA9B,EAAA9M,OAAA,KAAA+N,IAAgCP,WAAA,gBAAyB,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCQ,OAAOO,MAAA,cAAoBf,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,KAAA2L,SAAA,SAAAoC,GAAiDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,OAAA+N,IAAkCP,WAAA,kBAA2B,OAAAV,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,4BAAsCF,EAAA,gBAAqBQ,OAAOO,MAAA,UAAgBf,EAAA,kBAAuBiC,aAAad,MAAA,QAAeX,OAAQ5C,KAAA,YAAAgF,kBAAA,IAAAC,oBAAA,OAAAC,kBAAA,OAAAJ,OAAA,aAAAC,eAAA,aAAAjB,SAAA,IAA6Jb,OAAQP,MAAAT,EAAA9M,OAAA,OAAA2L,SAAA,SAAAoC,GAAmDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,SAAA+N,IAAoCP,WAAA,oBAA6B,OAAAV,EAAAe,GAAA,KAAAZ,EAAA,OAAgCE,YAAA,4BAAsCF,EAAA,gBAAqBQ,OAAOO,MAAA,aAAmBf,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8Cb,OAAQP,MAAAT,EAAA9M,OAAA,GAAA2L,SAAA,SAAAoC,GAA+CjB,EAAA8B,KAAA9B,EAAA9M,OAAA,KAAA+N,IAAgCP,WAAA,gBAAyB,eAAAV,EAAAe,GAAA,KAAAZ,EAAA,KAAsCE,YAAA,cAAwBL,EAAAe,GAAA,YAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAAgDE,YAAA,eAAAM,OAAkCQ,OAAA,GAAA1O,KAAAuN,EAAA/K,iBAAAmM,qBAA6DzO,WAAA,UAAAC,MAAA,WAA0CyO,OAAA,MAAclB,EAAA,mBAAwBQ,OAAO5C,KAAA,QAAAuD,MAAA,KAAAJ,MAAA,KAAAK,MAAA,YAA2DvB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,KAAAN,MAAA,SAA4Ba,YAAA/B,EAAAgC,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAhC,EAAA,YAAuBQ,OAAOgB,YAAA,GAAAE,SAAA,IAA+Bb,OAAQP,MAAA0B,EAAA3G,IAAA,GAAAqD,SAAA,SAAAoC,GAA8CjB,EAAA8B,KAAAK,EAAA3G,IAAA,KAAAyF,IAA+BP,WAAA,yBAAmCV,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,KAAAN,MAAA,MAAyBa,YAAA/B,EAAAgC,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAhC,EAAA,YAAuBQ,OAAOgB,YAAA,GAAAE,SAAA,IAA+Bb,OAAQP,MAAA0B,EAAA3G,IAAA,GAAAqD,SAAA,SAAAoC,GAA8CjB,EAAA8B,KAAAK,EAAA3G,IAAA,KAAAyF,IAA+BP,WAAA,yBAAmCV,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,KAAAN,MAAA,MAAyBa,YAAA/B,EAAAgC,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAhC,EAAA,YAAuBQ,OAAOgB,YAAA,GAAAE,SAAA,IAA+Bb,OAAQP,MAAA0B,EAAA3G,IAAA,GAAAqD,SAAA,SAAAoC,GAA8CjB,EAAA8B,KAAAK,EAAA3G,IAAA,KAAAyF,IAA+BP,WAAA,yBAAmCV,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,OAAAN,MAAA,QAA6Ba,YAAA/B,EAAAgC,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAhC,EAAA,aAAwBQ,OAAOgB,YAAA,OAAoBX,OAAQP,MAAA0B,EAAA3G,IAAA,KAAAqD,SAAA,SAAAoC,GAAgDjB,EAAA8B,KAAAK,EAAA3G,IAAA,OAAAyF,IAAiCP,WAAA,mBAA8BV,EAAAuC,GAAAvC,EAAA,qBAAArD,GAAyC,OAAAwD,EAAA,aAAuB8B,IAAAtF,EAAA8D,MAAAE,OAAsBO,MAAAvE,EAAAuE,MAAAT,MAAA9D,EAAA8D,MAAAoB,SAAA,QAAuD,UAAU7B,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,KAAAN,MAAA,eAAkCa,YAAA/B,EAAAgC,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAhC,EAAA,YAAuBQ,OAAOgB,YAAA,GAAAE,SAAA,IAA+Bb,OAAQP,MAAA0B,EAAA3G,IAAA,GAAAqD,SAAA,SAAAoC,GAA8CjB,EAAA8B,KAAAK,EAAA3G,IAAA,KAAAyF,IAA+BP,WAAA,0BAAmC,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,8BAAA+B,aAAuDc,SAAA,cAAuB/C,EAAA,OAAYE,YAAA,kBAA4BF,EAAA,OAAYE,YAAA,SAAmBL,EAAAe,GAAA,uDAAAZ,EAAA,aAAgFG,aAAaC,KAAA,OAAAC,QAAA,SAAAC,MAAAT,EAAA,KAAAU,WAAA,SAAgEL,YAAA,cAAAM,OAAmCC,KAAA,OAAA7C,KAAA,WAA+B8C,IAAKC,MAAAd,EAAAzB,YAAsByB,EAAAe,GAAA,QAAAf,EAAAe,GAAA,KAAAZ,EAAA,aAA6CQ,OAAOwC,QAAAnD,EAAAhJ,mBAAgC6J,IAAKuC,iBAAA,SAAAC,GAAkCrD,EAAAhJ,kBAAAqM,MAA+BlD,EAAA,OAAYiC,aAAad,MAAA,QAAeX,OAAQ2C,IAAAtD,EAAAjJ,eAAAwM,IAAA,MAAmCvD,EAAAe,GAAA,KAAAZ,EAAA,OAAwBE,YAAA,gBAAAM,OAAmC6C,KAAA,UAAgBA,KAAA,WAAerD,EAAA,aAAkBQ,OAAOC,KAAA,SAAeC,IAAKC,MAAA,SAAAuC,GAAyBrD,EAAAhJ,mBAAA,MAAgCgJ,EAAAe,GAAA,mBAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAkDE,YAAA,SAAmBL,EAAAe,GAAA,4DAAAZ,EAAA,aAAqFG,aAAaC,KAAA,OAAAC,QAAA,SAAAC,MAAAT,EAAA,KAAAU,WAAA,SAAgEL,YAAA,eAAAM,OAAoCC,KAAA,OAAA7C,KAAA,WAA+B8C,IAAKC,MAAAd,EAAAvB,WAAqBuB,EAAAe,GAAA,QAAAf,EAAAe,GAAA,KAAAZ,EAAA,aAA6CQ,OAAOwC,QAAAnD,EAAA9I,oBAAiC2J,IAAKuC,iBAAA,SAAAC,GAAkCrD,EAAA9I,mBAAAmM,MAAgClD,EAAA,OAAYiC,aAAad,MAAA,QAAeX,OAAQ2C,IAAAtD,EAAA/I,oBAAAsM,IAAA,MAAwCvD,EAAAe,GAAA,KAAAZ,EAAA,OAAwBE,YAAA,gBAAAM,OAAmC6C,KAAA,UAAgBA,KAAA,WAAerD,EAAA,aAAkBQ,OAAOC,KAAA,SAAeC,IAAKC,MAAA,SAAAuC,GAAyBrD,EAAA9I,oBAAA,MAAiC8I,EAAAe,GAAA,mBAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAAkDE,YAAA,SAAmBL,EAAAe,GAAA,uDAAAZ,EAAA,aAAgFG,aAAaC,KAAA,OAAAC,QAAA,SAAAC,MAAAT,EAAA,KAAAU,WAAA,SAAgEL,YAAA,eAAAM,OAAoCC,KAAA,OAAA7C,KAAA,WAA+B8C,IAAKC,MAAAd,EAAAtB,SAAmBsB,EAAAe,GAAA,QAAAf,EAAAe,GAAA,KAAAZ,EAAA,aAA6CQ,OAAOwC,QAAAnD,EAAA5I,kBAA+ByJ,IAAKuC,iBAAA,SAAAC,GAAkCrD,EAAA5I,iBAAAiM,MAA8BlD,EAAA,OAAYiC,aAAad,MAAA,QAAeX,OAAQ2C,IAAAtD,EAAA7I,kBAAAoM,IAAA,MAAsCvD,EAAAe,GAAA,KAAAZ,EAAA,OAAwBE,YAAA,gBAAAM,OAAmC6C,KAAA,UAAgBA,KAAA,WAAerD,EAAA,aAAkBQ,OAAOC,KAAA,SAAeC,IAAKC,MAAA,SAAAuC,GAAyBrD,EAAA5I,kBAAA,MAA+B4I,EAAAe,GAAA,uBAAAf,EAAAe,GAAA,KAAAZ,EAAA,KAAoDE,YAAA,cAAwBL,EAAAe,GAAA,YAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA2CE,YAAA,4CAAsDF,EAAA,gBAAqBQ,OAAOO,MAAA,SAAAM,KAAA,SAAgCxB,EAAAuC,GAAAvC,EAAA,cAAArD,GAAkC,OAAAwD,EAAA,YAAsB8B,IAAAtF,EAAA1G,GAAA0K,OAAmBO,MAAAvE,EAAA1G,GAAA4L,SAAA7B,EAAArK,WAAyCkL,IAAK4C,OAAAzD,EAAA/F,SAAqB+G,OAAQP,MAAAT,EAAA9M,OAAA,KAAA2L,SAAA,SAAAoC,GAAiDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,OAAA+N,IAAkCP,WAAA,iBAA2BV,EAAAe,GAAAf,EAAA2C,GAAAhG,EAAApG,WAA8B,GAAAyJ,EAAAe,GAAA,KAAAZ,EAAA,gBAAoCE,YAAA,aAAAM,OAAgCO,MAAA,OAAAM,KAAA,iBAAoC,GAAAxB,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,4CAAsDF,EAAA,gBAAqBQ,OAAOO,MAAA,UAAAM,KAAA,WAAkCrB,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAE,SAAA7B,EAAArK,UAAAiM,UAAA,IAAyDZ,OAAQP,MAAAT,EAAA9M,OAAA,OAAA2L,SAAA,SAAAoC,GAAmDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,SAAA+N,IAAoCP,WAAA,oBAA6B,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCQ,OAAOO,MAAA,KAAAM,KAAA,YAA8BrB,EAAA,kBAAuBQ,OAAOkB,SAAA7B,EAAArK,UAAAkN,OAAA,aAAAC,eAAA,aAAA/E,KAAA,OAAA4D,YAAA,QAA8GX,OAAQP,MAAAT,EAAA9M,OAAA,OAAA2L,SAAA,SAAAoC,GAAmDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,SAAA+N,IAAoCP,WAAA,oBAA6B,OAAAV,EAAAe,GAAA,KAAAZ,EAAA,KAA8BE,YAAA,cAAwBL,EAAAe,GAAA,WAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA0CE,YAAA,4CAAsDF,EAAA,gBAAqBQ,OAAOO,MAAA,SAAAM,KAAA,SAAgCxB,EAAAuC,GAAAvC,EAAA,cAAArD,GAAkC,OAAAwD,EAAA,YAAsB8B,IAAAtF,EAAA1G,GAAA0K,OAAmBO,MAAAvE,EAAA1G,GAAA4L,SAAA7B,EAAApK,WAAyCiL,IAAK4C,OAAAzD,EAAA/F,SAAqB+G,OAAQP,MAAAT,EAAA9M,OAAA,MAAA2L,SAAA,SAAAoC,GAAkDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,QAAA+N,IAAmCP,WAAA,kBAA4BV,EAAAe,GAAAf,EAAA2C,GAAAhG,EAAApG,WAA8B,GAAAyJ,EAAAe,GAAA,KAAAZ,EAAA,gBAAoCE,YAAA,aAAAM,OAAgCO,MAAA,OAAAM,KAAA,iBAAoC,GAAAxB,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,4CAAsDF,EAAA,gBAAqBQ,OAAOO,MAAA,UAAAM,KAAA,WAAkCrB,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAE,SAAA7B,EAAApK,UAAAgM,UAAA,IAAyDZ,OAAQP,MAAAT,EAAA9M,OAAA,QAAA2L,SAAA,SAAAoC,GAAoDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,UAAA+N,IAAqCP,WAAA,qBAA8B,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCQ,OAAOO,MAAA,KAAAM,KAAA,YAA8BrB,EAAA,kBAAuBQ,OAAOkB,SAAA7B,EAAApK,UAAAiN,OAAA,aAAAC,eAAA,aAAA/E,KAAA,OAAA4D,YAAA,QAA8GX,OAAQP,MAAAT,EAAA9M,OAAA,QAAA2L,SAAA,SAAAoC,GAAoDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,UAAA+N,IAAqCP,WAAA,qBAA8B,OAAAV,EAAAe,GAAA,KAAAZ,EAAA,KAA8BE,YAAA,cAAwBL,EAAAe,GAAA,aAAAf,EAAAe,GAAA,KAAAZ,EAAA,OAA4CE,YAAA,4CAAsDF,EAAA,gBAAqBQ,OAAOO,MAAA,SAAAM,KAAA,SAAgCxB,EAAAuC,GAAAvC,EAAA,cAAArD,GAAkC,OAAAwD,EAAA,YAAsB8B,IAAAtF,EAAA1G,GAAA0K,OAAmBO,MAAAvE,EAAA1G,GAAA4L,SAAA7B,EAAAnK,WAAyCgL,IAAK4C,OAAAzD,EAAA/F,SAAqB+G,OAAQP,MAAAT,EAAA9M,OAAA,KAAA2L,SAAA,SAAAoC,GAAiDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,OAAA+N,IAAkCP,WAAA,iBAA2BV,EAAAe,GAAAf,EAAA2C,GAAAhG,EAAApG,WAA8B,GAAAyJ,EAAAe,GAAA,KAAAZ,EAAA,gBAAoCE,YAAA,aAAAM,OAAgCO,MAAA,OAAAM,KAAA,iBAAoC,GAAAxB,EAAAe,GAAA,KAAAZ,EAAA,OAA4BE,YAAA,4CAAsDF,EAAA,gBAAqBQ,OAAOO,MAAA,UAAAM,KAAA,WAAkCrB,EAAA,YAAiBQ,OAAOgB,YAAA,GAAAE,SAAA7B,EAAAnK,UAAA+L,UAAA,IAAyDZ,OAAQP,MAAAT,EAAA9M,OAAA,OAAA2L,SAAA,SAAAoC,GAAmDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,SAAA+N,IAAoCP,WAAA,oBAA6B,GAAAV,EAAAe,GAAA,KAAAZ,EAAA,gBAAqCQ,OAAOO,MAAA,KAAAM,KAAA,YAA8BrB,EAAA,kBAAuBQ,OAAOkB,SAAA7B,EAAAnK,UAAAgN,OAAA,aAAAC,eAAA,aAAA/E,KAAA,OAAA4D,YAAA,QAA8GX,OAAQP,MAAAT,EAAA9M,OAAA,OAAA2L,SAAA,SAAAoC,GAAmDjB,EAAA8B,KAAA9B,EAAA9M,OAAA,SAAA+N,IAAoCP,WAAA,oBAA6B,OAAAV,EAAAe,GAAA,KAAAZ,EAAA,KAA8BE,YAAA,cAAwBL,EAAAe,GAAA,UAAAf,EAAAe,GAAA,KAAAZ,EAAA,YAA8CE,YAAA,eAAAM,OAAkCQ,OAAA,GAAA1O,KAAAuN,EAAAvK,SAAA2L,qBAAqDzO,WAAA,UAAAC,MAAA,WAA0CyO,OAAA,MAAclB,EAAA,mBAAwBQ,OAAOa,KAAA,OAAAN,MAAA,UAA8BlB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,QAAAN,MAAA,SAA8BlB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,OAAAN,MAAA,UAA8BlB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,OAAAN,MAAA,UAA8BlB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,SAAAN,MAAA,YAAkClB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,OAAAN,MAAA,WAA8B,aAAAlB,EAAAe,GAAA,KAAAZ,EAAA,eAA8CQ,OAAOO,MAAA,OAAAX,KAAA,WAA+BJ,EAAA,YAAiBE,YAAA,eAAAM,OAAkCQ,OAAA,GAAA1O,KAAAuN,EAAA7G,SAAAiI,qBAAqDzO,WAAA,UAAAC,MAAA,WAA0CyO,OAAA,MAAclB,EAAA,mBAAwBQ,OAAOa,KAAA,OAAAN,MAAA,UAA8BlB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,QAAAN,MAAA,SAA8BlB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,OAAAN,MAAA,UAA8BlB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,OAAAN,MAAA,UAA8BlB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,SAAAN,MAAA,YAAkClB,EAAAe,GAAA,KAAAZ,EAAA,mBAAoCQ,OAAOa,KAAA,OAAAN,MAAA,WAA8B,gBAEnqgBwC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACExR,EACAyN,GATF,EAVA,SAAAgE,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/98.cbc513c8dd160a827dd2.js", "sourcesContent": ["<template>\r\n  <div class=\"sec-container\">\r\n    <el-button class=\"fhry\" v-show=\"deb\" type=\"primary\" size=\"small\" @click=\"fhry\">返回</el-button>\r\n    <el-tabs v-model=\"activeName\">\r\n      <el-tab-pane label=\"审批指南\" name=\"first\">\r\n        <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n          <el-button @click=\"ljbl\" class=\"fr\" type=\"success\">立即办理</el-button>\r\n        </div>\r\n        <el-table border class=\"sec-el-table\" :data=\"spznList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"hjmc\" label=\"办理流程\"></el-table-column>\r\n        </el-table>\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"审批信息\" name=\"second\">\r\n        <!-- 标题 -->\r\n        <p class=\"sec-title-big\">涉密人员保密审查表</p>\r\n        <p class=\"sec-title\">基本信息</p>\r\n        <div class=\"sec-form-container\">\r\n          <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n            <!-- 第一部分包括姓名到常住地公安start -->\r\n            <div class=\"sec-header-section\">\r\n              <div class=\"sec-form-left\">\r\n                <el-form-item label=\"姓名\">\r\n                  <el-input placeholder=\"\" v-model=\"tjlist.xm\" clearable disabled></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"性别\">\r\n                  <!-- <el-input placeholder=\"\" v-model=\"tjlist.xb\" clearable disabled></el-input> -->\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input placeholder=\"\" v-model=\"tjlist.xingbie\" clearable disabled></el-input>\r\n                  </template>\r\n                </el-form-item>\r\n                <el-form-item label=\"出生年月日\">\r\n                  <el-input placeholder=\"\" v-model=\"tjlist.csny\" clearable disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"sec-form-left\">\r\n                <el-form-item label=\"政治面貌\">\r\n                  <el-input placeholder=\"\" v-model=\"tjlist.zzmmxx\" clearable disabled></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"涉密岗位\">\r\n                  <el-input placeholder=\"\" v-model=\"tjlist.gwmc\" clearable disabled></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"涉密等级\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input placeholder=\"\" v-model=\"tjlist.smdjxx\" clearable disabled></el-input>\r\n                    <!-- <p class=\"hyzk\" v-if=\"tjlist.hyzk == 0\">未婚</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.hyzk == 1\">已婚</p> -->\r\n                  </template>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"sec-form-left\">\r\n                <el-form-item label=\"工作单位及职务\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input placeholder=\"\" v-model=\"tjlist.bmzwzc\" clearable disabled></el-input>\r\n                    <!-- <p class=\"hyzk\" v-if=\"tjlist.zzmm == 1\">中共党员</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 2\">团员</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 3\">民主党派</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 4\">群众</p> -->\r\n                  </template>\r\n                </el-form-item>\r\n                <el-form-item label=\"身份证号\">\r\n                  <el-input placeholder=\"\" v-model=\"tjlist.sfzhm\" clearable disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 单位及职务、职称到涉密等级end -->\r\n            <!-- 主要学习及工作经历start -->\r\n            <p class=\"sec-title\">审批事项</p>\r\n            <div class=\"sec-form-container\">\r\n              <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n                <div class=\"sec-header-section\">\r\n                  <div class=\"sec-form-left longLabel\">\r\n                    <el-form-item label=\"审批事件类型\">\r\n                      <el-radio v-model=\"tjlist.splx\" label=\"1\" disabled>新申办从出入境证件</el-radio>\r\n                      <el-radio v-model=\"tjlist.splx\" label=\"0\" disabled>申请出国（境）</el-radio>\r\n                    </el-form-item>\r\n                  </div>\r\n                  <div class=\"sec-form-left longLabel widthzz\">\r\n                    <el-form-item label=\"证件类型\">\r\n                      <div style=\"display: flex; flex-direction: column;\">\r\n                        <div v-for=\"(item, index) in zzhmList\" :key=\"item.zzid\">\r\n                          <div style=\"display: flex; align-items: center;justify-content: space-between;\">\r\n                            <el-checkbox-group v-model=\"checkList\" disabled>\r\n                              <el-checkbox :label=\"item.zzid\" style=\"width: 200px;\">{{ item.fjlb\r\n                              }}</el-checkbox></el-checkbox-group>\r\n                            <div>证件号码:<el-input v-model=\"item.zjhm\" style=\"width: 200px;\" @blur=\"zzxx\"\r\n                                disabled></el-input></div>\r\n                            <div>有效期:<el-date-picker v-model=\"item.yxq\" type=\"date\" placeholder=\"选择日期\"\r\n                                style=\"width: 200px;\" format=\"yyyy-MM-dd\" value-format=\"yyyy-MM-dd\" disabled>\r\n                              </el-date-picker></div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <!-- <div style=\"display: flex; flex-direction: column;\">\r\n                    <div style=\"display: flex; align-items: center;justify-content: space-between;\">\r\n                      <el-checkbox-group v-model=\"checkList\">\r\n                        <el-checkbox label=\"1\">因公护照</el-checkbox></el-checkbox-group>\r\n                      <div>证件号码:<el-input style=\"width: 200px;\"></el-input></div>\r\n                      <div>有效期:<el-input style=\"width: 200px;\"></el-input></div>\r\n                    </div>\r\n                    <div style=\"display: flex; align-items: center;justify-content: space-between;\">\r\n                      <el-checkbox-group v-model=\"checkList\">\r\n                        <el-checkbox label=\"2\">因公港澳通行证</el-checkbox></el-checkbox-group>\r\n                      <div>证件号码:<el-input style=\"width: 500px;\"></el-input></div>\r\n                    </div>\r\n                    <div style=\"display: flex; align-items: center;justify-content: space-between;\">\r\n                      <el-checkbox-group v-model=\"checkList\">\r\n                        <el-checkbox label=\"3\">因公台湾通行证</el-checkbox></el-checkbox-group>\r\n                      <div>证件号码:<el-input style=\"width: 500px;\"></el-input></div>\r\n                    </div>\r\n                    <div style=\"display: flex; align-items: center;justify-content: space-between;\">\r\n                      <el-checkbox-group v-model=\"checkList\">\r\n                        <el-checkbox label=\"4\">因私护照</el-checkbox></el-checkbox-group>\r\n                      <div>证件号码:<el-input style=\"width: 500px;\"></el-input></div>\r\n                    </div>\r\n                    <div style=\"display: flex; align-items: center;justify-content: space-between;\">\r\n                      <el-checkbox-group v-model=\"checkList\">\r\n                        <el-checkbox label=\"5\">因私港澳通行证</el-checkbox></el-checkbox-group>\r\n                      <div>证件号码:<el-input style=\"width: 500px;\"></el-input></div>\r\n                    </div>\r\n                    <div style=\"display: flex; align-items: center;justify-content: space-between;\">\r\n                      <el-checkbox-group v-model=\"checkList\">\r\n                        <el-checkbox label=\"6\">因私台湾通行证</el-checkbox></el-checkbox-group>\r\n                      <div>证件号码:<el-input style=\"width: 500px;\"></el-input></div>\r\n                    </div>\r\n                  </div> -->\r\n                    </el-form-item>\r\n                  </div>\r\n                  <div class=\"sec-form-left longLabel\">\r\n                    <el-form-item label=\"本年度因私出国(境)次数\">\r\n                      <el-input placeholder=\"\" v-model=\"tjlist.cs\" clearable disabled></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"前往国家(地区)\">\r\n                      <el-input placeholder=\"\" v-model=\"tjlist.qwgj\" clearable disabled></el-input>\r\n                    </el-form-item>\r\n                  </div>\r\n                  <div class=\"sec-form-left longLabel\">\r\n                    <el-form-item label=\"起止日期\">\r\n                      <el-date-picker v-model=\"tjlist.value1\" style=\"width:100%\" type=\"daterange\" range-separator=\"至\"\r\n                        start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" format=\"yyyy-MM-dd\" value-format=\"yyyy-MM-dd\"\r\n                        disabled>\r\n                      </el-date-picker>\r\n                    </el-form-item>\r\n                  </div>\r\n                  <div class=\"sec-form-left longLabel\">\r\n                    <el-form-item label=\"出国（境）事由\">\r\n                      <el-input placeholder=\"\" v-model=\"tjlist.sy\" clearable disabled></el-input>\r\n                    </el-form-item>\r\n                  </div>\r\n                </div>\r\n              </el-form>\r\n            </div>\r\n            <!-- 主要学习及工作经历end -->\r\n            <!-- 家庭成员及主要社会关系情况start -->\r\n            <p class=\"sec-title\">同行人员情况</p>\r\n            <el-table border class=\"sec-el-table\" :data=\"ryglRyscJtcyList\"\r\n              :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n              <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n              <el-table-column prop=\"gx\" label=\"与本人关系\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-input v-model=\"scope.row.gx\" placeholder=\"\" disabled></el-input>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"xm\" label=\"姓名\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-input v-model=\"scope.row.xm\" placeholder=\"\" disabled></el-input>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column prop=\"nl\" label=\"年龄\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-input v-model=\"scope.row.nl\" placeholder=\"\" disabled></el-input>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"zzmm\" label=\"政治面貌\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-select v-model=\"scope.row.zzmm\" placeholder=\"请选择\">\r\n                    <el-option v-for=\"item in zzmmoptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\r\n                      disabled>\r\n                    </el-option>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"zw\" label=\"工作单位,职务及居住地\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-input v-model=\"scope.row.zw\" placeholder=\"\" disabled></el-input>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- <el-table-column label=\"操作\" width=\"140\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button v-if=\"scope.row.czbtn1 != ''\" size=\"medium\" type=\"text\"\r\n                    @click=\"cyjshgxAddRow(ryglRyscJtcyList)\">{{ scope.row.czbtn1 }}\r\n                  </el-button>\r\n                  <el-button v-if=\"scope.row.czbtn2 != ''\" size=\"medium\" type=\"text\"\r\n                    @click=\"cyjshgxDelRow(scope.$index, ryglRyscJtcyList)\">{{ scope.row.czbtn2 }}\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column> -->\r\n            </el-table>\r\n            <div class=\"sec-form-five haveBorderTop\" style=\"position: relative;\">\r\n              <div class=\"sec-left-text\">\r\n                <div class=\"flex\">\r\n                  1.上传保密提醒谈话确认扫描件\r\n                  <el-button class=\"upload-demo\" v-show=\"ylth\" size=\"mini\" type=\"primary\" @click=\"ylbmtxth\">预览</el-button>\r\n                  <el-dialog :visible.sync=\"dialogThtxVisible\">\r\n                    <img :src=\"dialogImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button size=\"small\" @click=\"dialogThtxVisible = false\">取 消</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n                </div>\r\n                <div class=\"flex\">\r\n                  2.上传离岗离职涉密人员保密承诺书扫描件\r\n                  <el-button class=\"upload-demo2\" v-show=\"ylcn\" size=\"mini\" type=\"primary\" @click=\"ylbmcns\">预览</el-button>\r\n                  <el-dialog :visible.sync=\"dialogBmcnsVisible\">\r\n                    <img :src=\"dialogBmcnsImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button size=\"small\" @click=\"dialogBmcnsVisible = false\">取 消</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n                </div>\r\n                <div class=\"flex\">\r\n                  3.上传脱密期委托管理书扫描件\r\n                  <el-button class=\"upload-demo3\" v-show=\"ylwt\" size=\"mini\" type=\"primary\" @click=\"ylwts\">预览</el-button>\r\n                  <el-dialog :visible.sync=\"dialogWtsVisible\">\r\n                    <img :src=\"dialogWtsImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button size=\"small\" @click=\"dialogWtsVisible = false\">取 消</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <p class=\"sec-title\">所在部门意见</p>\r\n            <div class=\"sec-form-second haveBorderTop longLabel\">\r\n              <el-form-item label=\"信息属实，拟\" prop=\"bmsc\">\r\n                <el-radio v-model=\"tjlist.bmsc\" v-for=\"item in scqk\" :label=\"item.id\" @change=\"chRadio\"\r\n                  :disabled=\"disabled1\" :key=\"item.id\">{{\r\n                    item.sfty }}</el-radio>\r\n              </el-form-item>\r\n              <el-form-item label=\"出国出境\" prop=\"gtzzsmgwgz\" class=\"gtzzsmgwgz\"></el-form-item>\r\n            </div>\r\n            <div class=\"sec-form-second haveBorderTop longLabel\">\r\n              <el-form-item label=\"部门领导审批人\" prop=\"bmspr\">\r\n                <!-- <el-input placeholder=\"\" :disabled=\"disabled2\" v-model=\"tjlist.bmspr\" clearable></el-input> -->\r\n                <el-input placeholder=\"\" :disabled=\"disabled1\" v-model=\"tjlist.bmscxm\" clearable></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"日期\" prop=\"bmscrq\">\r\n                <!-- <el-date-picker disabled v-model=\"tjlist.bmscrq\" format=\"yyyy-MM-dd\" -->\r\n                <el-date-picker :disabled=\"disabled1\" v-model=\"tjlist.bmscsj\" format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </div>\r\n            <p class=\"sec-title\">保密办意见</p>\r\n            <div class=\"sec-form-second haveBorderTop longLabel\">\r\n              <el-form-item label=\"信息属实，拟\" prop=\"bmsc\">\r\n                <el-radio v-model=\"tjlist.bmbsc\" v-for=\"item in scqk\" :label=\"item.id\" @change=\"chRadio\"\r\n                  :disabled=\"disabled2\" :key=\"item.id\">{{\r\n                    item.sfty }}</el-radio>\r\n              </el-form-item>\r\n              <el-form-item label=\"出国出境\" prop=\"gtzzsmgwgz\" class=\"gtzzsmgwgz\"></el-form-item>\r\n            </div>\r\n            <div class=\"sec-form-second haveBorderTop longLabel\">\r\n              <el-form-item label=\"部门领导审批人\" prop=\"bmspr\">\r\n                <!-- <el-input placeholder=\"\" :disabled=\"disabled2\" v-model=\"tjlist.bmspr\" clearable></el-input> -->\r\n                <el-input placeholder=\"\" :disabled=\"disabled2\" v-model=\"tjlist.bmbscxm\" clearable></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"日期\" prop=\"bmscrq\">\r\n                <!-- <el-date-picker disabled v-model=\"tjlist.bmscrq\" format=\"yyyy-MM-dd\" -->\r\n                <el-date-picker :disabled=\"disabled2\" v-model=\"tjlist.bmbscsj\" format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </div>\r\n            <p class=\"sec-title\">人力资源部意见</p>\r\n            <div class=\"sec-form-second haveBorderTop longLabel\">\r\n              <el-form-item label=\"信息属实，拟\" prop=\"bmsc\">\r\n                <el-radio v-model=\"tjlist.rlsc\" v-for=\"item in scqk\" :label=\"item.id\" @change=\"chRadio\"\r\n                  :disabled=\"disabled3\" :key=\"item.id\">{{\r\n                    item.sfty }}</el-radio>\r\n              </el-form-item>\r\n              <el-form-item label=\"出国出境\" prop=\"gtzzsmgwgz\" class=\"gtzzsmgwgz\"></el-form-item>\r\n            </div>\r\n            <div class=\"sec-form-second haveBorderTop longLabel\">\r\n              <el-form-item label=\"部门领导审批人\" prop=\"bmspr\">\r\n                <!-- <el-input placeholder=\"\" :disabled=\"disabled2\" v-model=\"tjlist.bmspr\" clearable></el-input> -->\r\n                <el-input placeholder=\"\" :disabled=\"disabled3\" v-model=\"tjlist.rlscxm\" clearable></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"日期\" prop=\"bmscrq\">\r\n                <!-- <el-date-picker disabled v-model=\"tjlist.bmscrq\" format=\"yyyy-MM-dd\" -->\r\n                <el-date-picker :disabled=\"disabled3\" v-model=\"tjlist.rlscsj\" format=\"yyyy-MM-dd\"\r\n                  value-format=\"yyyy-MM-dd\" type=\"date\" placeholder=\"选择日期\">\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </div>\r\n            <p class=\"sec-title\">轨迹处理</p>\r\n            <el-table border class=\"sec-el-table\" :data=\"gjclList\"\r\n              :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n              <el-table-column prop=\"hjmc\" label=\"办理环节\"></el-table-column>\r\n              <el-table-column prop=\"clrid\" label=\"办理人\"></el-table-column>\r\n              <el-table-column prop=\"bllx\" label=\"办理类型\"></el-table-column>\r\n              <el-table-column prop=\"clyj\" label=\"办理意见\"></el-table-column>\r\n              <el-table-column prop=\"xybclr\" label=\"下一步办理人\"></el-table-column>\r\n              <el-table-column prop=\"clsj\" label=\"办理时间\"></el-table-column>\r\n            </el-table>\r\n\r\n          </el-form>\r\n        </div>\r\n        <!-- 涉密人员任用审查列表end -->\r\n        <!-- 发起申请弹框start -->\r\n        <!-- <el-dialog title=\"人员选择\" :close-on-click-modal=\"false\" :visible.sync=\"dialogVisible\" width=\"40%\">\r\n                    <div class=\"dlFqsqContainer\">\r\n                        <label for=\"\">部门:</label>\r\n                        <el-input class=\"input1\" v-model=\"formInline.bmmc\" clearable placeholder=\"部门\"></el-input>\r\n                        <label for=\"\">姓名:</label>\r\n                        <el-input class=\"input2\" v-model=\"formInline.xm\" clearable placeholder=\"姓名\"></el-input>\r\n                        <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\"\r\n                            @click=\"onSubmit\">查询</el-button>\r\n                        <el-table class=\"tb-container\" ref=\"multipleTable\" :data=\"smryList\" border\r\n                            :header-cell-style=\"headerCellStyle\" stripe @selection-change=\"selectRow\" @select=\"handleSelect\"\r\n                            @row-click=\"handleRowClick\">\r\n                            <el-table-column type=\"selection\" width=\"55\" align=\"center\"> </el-table-column>\r\n                            <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"xm\" label=\"姓名\"></el-table-column>\r\n                            <el-table-column prop=\"bmmc\" label=\"部门\"></el-table-column>\r\n                            <el-table-column prop=\"gwmc\" label=\"岗位\"></el-table-column>\r\n                        </el-table>\r\n                        <el-pagination class=\"paginationContainer\" background @current-change=\"handleCurrentChange\"\r\n                            @size-change=\"handleSizeChange\" :pager-count=\"5\" :current-page=\"page\"\r\n                            :page-sizes=\"[5, 10, 20, 30]\" :page-size=\"pageSize\"\r\n                            layout=\"total, prev, pager, sizes,next, jumper\" :total=\"total\">\r\n                        </el-pagination>\r\n                    </div>\r\n                    <span slot=\"footer\" class=\"dialog-footer\">\r\n                        <el-button type=\"primary\" v-if=\"xsyc\" @click=\"submit('formName')\">确 定</el-button>\r\n                        <el-button type=\"warning\" @click=\"dialogVisible = false\">关 闭</el-button>\r\n                    </span>\r\n                </el-dialog> -->\r\n        <!-- 发起申请弹框end -->\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"流程跟踪\" name=\"third\">\r\n        <el-table border class=\"sec-el-table\" :data=\"lcgzList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column prop=\"hjmc\" label=\"办理环节\"></el-table-column>\r\n          <el-table-column prop=\"clrid\" label=\"办理人\"></el-table-column>\r\n          <el-table-column prop=\"bllx\" label=\"办理类型\"></el-table-column>\r\n          <el-table-column prop=\"clyj\" label=\"办理意见\"></el-table-column>\r\n          <el-table-column prop=\"xybclr\" label=\"下一步办理人\"></el-table-column>\r\n          <el-table-column prop=\"clsj\" label=\"办理时间\"></el-table-column>\r\n        </el-table>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  //审批指南\r\n  getBlzn,\r\n  //审批信息\r\n  getRyscInfoBySlid,\r\n  //审批信息\r\n  getZgfsInfoBySlid,\r\n  //判断实例所处环节\r\n  getSchj,\r\n  //事项审核\r\n  getSxsh,\r\n  //查询审批用户列表\r\n  getSpUserList,\r\n  //非第一环节选择审批人\r\n  tjclr,\r\n  //修改任用审查详情记录\r\n  updateRysc,\r\n  //流程跟踪\r\n  getSpGjxx,\r\n} from '../../../../api/wdgz'\r\nimport {\r\n  selectRyglCgcjBySlId\r\n} from '../../../../api/cgjsc'\r\nimport { getZpBySmryid } from '../../../../api/index'\r\nimport AddLineTable from \"../../../components/common/addLineTable.vue\"; //人工纠错组件\r\nexport default {\r\n  components: {\r\n    AddLineTable\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: '#EEF7FF',\r\n        color: '#4D91F8'\r\n      },\r\n      fwdyid: '',\r\n      slid: '',\r\n      activeName: 'second',\r\n      //审批指南\r\n      spznList: [],\r\n      checkList: [],\r\n      // form表单提交数据\r\n      //审批信息\r\n      tjlist: {\r\n        smryid: '',\r\n        xm: '',\r\n        xb: '',\r\n        gj: '中国',\r\n        dwzwzc: '',\r\n        yrsmgw: '',\r\n        cym: '',\r\n        mz: '',\r\n        hyzk: '',\r\n        zzmm: '',\r\n        lxdh: '',\r\n        sfzhm: '',\r\n        hjdz: '',\r\n        hjdgajg: '',\r\n        czdz: '',\r\n        czgajg: '',\r\n        imageUrl: '',\r\n        yjqk: '0', // 拥有外籍、境外永久居留权或者长期居留许可情况\r\n        qscfqk: '0', // 配偶子女有关情况\r\n        qtqk: '', // 其他需要说明的情况\r\n        brcn: '',\r\n        splx: '',\r\n        value1: [],\r\n      },\r\n      zzhmList: [\r\n        {\r\n          zzid: 1,\r\n          fjlb: '因公护照',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 2,\r\n          fjlb: '因公港澳通行证',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 3,\r\n          fjlb: '因公台湾通行证',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 4,\r\n          fjlb: '因私护照',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 5,\r\n          fjlb: '因私港澳通行证',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n        {\r\n          zzid: 6,\r\n          fjlb: '因私台湾通行证',\r\n          zjhm: '',\r\n          yxq: '',\r\n          checked: 0\r\n        },\r\n\r\n      ],\r\n      deb: true,\r\n      // 家庭成员及社会关系\r\n      ryglRyscJtcyList: [{\r\n        \"gx\": \"\",//关系描述\r\n        \"nl\": \"\",//年龄\r\n        \"zzmm\": \"\",//政治面貌\r\n        \"jwjlqk\": '',//是否有外籍、境外居留权、长期居留许可\r\n        \"xm\": \"\",//姓名\r\n        \"cgszd\": \"\",//工作(学习)单位\r\n        \"zw\": \"\",//职务\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      //轨迹处理\r\n      gjclList: [],\r\n      upccLsit: {},\r\n      //判断实例所处环节\r\n      disabled1: true,\r\n      disabled2: true,\r\n      disabled3: true,\r\n      btnsftg: true,\r\n      btnsfth: true,\r\n      jgyf: '',\r\n      //性别\r\n      xb: [{\r\n        xb: '男',\r\n        id: 1\r\n      },\r\n      {\r\n        xb: '女',\r\n        id: 2\r\n      },\r\n      ],\r\n      //移居国(境)外情况\r\n      yjgwqk: [{\r\n        yw: '有',\r\n        id: 1\r\n      },\r\n      {\r\n        yw: '无',\r\n        id: 0\r\n      },\r\n      ],\r\n      //上岗保密教育、签订保密承诺书\r\n      bmjysfwc: [{\r\n        sfwc: '已完成',\r\n        id: 1\r\n      },\r\n      {\r\n        sfwc: '未完成',\r\n        id: 0\r\n      },\r\n      ],\r\n      scqk: [{\r\n        sfty: '同意',\r\n        id: 1\r\n      },\r\n      {\r\n        sfty: '不同意',\r\n        id: 0\r\n      },\r\n      ],\r\n      // 政治面貌下拉选项\r\n      zzmmoptions: [],\r\n      fileList: [],\r\n      //本人承诺\r\n      dialogVisible_brcn: false,\r\n      dialogVisible: false,\r\n      sltshow: '', // 文档的缩略图显示\r\n      sltbmcnsshow: '', // 文档的缩略图显示\r\n      sltwtsshow: '', // 文档的缩略图显示\r\n      dialogImageUrl: '',\r\n      dialogThtxVisible: false,\r\n      dialogBmcnsImageUrl: '',\r\n      dialogBmcnsVisible: false,\r\n      dialogWtsImageUrl: '',\r\n      dialogWtsVisible: false,\r\n      fileRow: '',\r\n      filebmcnsRow: '',\r\n      filewtsRow: '',\r\n      fileryxxRow: '',\r\n      filexzglRow: '',\r\n      ylth: false,\r\n      ylcn: false,\r\n      ylwt: false,\r\n      //人员任用\r\n      smryList: [],\r\n      page: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      formInline: {\r\n        'bmmc': '',\r\n        'xm': ''\r\n      }, // 搜索条件\r\n      selectlistRow: [], //列表的值\r\n      xsyc: true,\r\n      mbhjid: '',\r\n      imageUrl: '',\r\n      imageUrlbrcn: '',\r\n      ylxy: true,\r\n      yldis: false,\r\n      file: {},\r\n      bmcnssmj: '',\r\n      bmxyssmj: '',\r\n      //保密承诺书预览\r\n      dialogVisible_bmcns: false,\r\n      bmcnsImageUrl: '',\r\n      //保密承诺书预览\r\n      dialogVisible_bmxys: false,\r\n      bmxysImageUrl: '',\r\n      //上传扫描件按钮显示隐藏\r\n      show: true,\r\n      show1: true,\r\n      typezt: '',\r\n      //流程跟踪\r\n      lcgzList: [],\r\n\r\n    }\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.typezt = this.$route.query.typezt\r\n    if (this.typezt != 'fhxq') {\r\n      this.deb = false\r\n    }\r\n    this.fwdyid = this.$route.query.fwdyid\r\n    console.log(\"this.fwdyid\", this.fwdyid);\r\n    this.slid = this.$route.query.slid\r\n    console.log(\"this.slid\", this.slid);\r\n    this.lx = this.$route.query.lx\r\n    console.log(this.lx);\r\n    //审批指南初始化列表\r\n    this.spzn()\r\n    //审批信息初始化列表\r\n    // this.spxxxgcc()\r\n    this.spxx()\r\n    //判断实例所处环节\r\n    // this.pdschj()\r\n    // //事项审核\r\n    // this.sxsh()\r\n    //初始化el-dialog列表数据\r\n    // this.splist()\r\n    //流程跟踪初始化列表\r\n    this.lcgz()\r\n\r\n  },\r\n  methods: {\r\n    zzxx() {\r\n      console.log(this.checkList);\r\n    },\r\n    chRadio() { },\r\n    // 返回\r\n    returnIndex() {\r\n      this.$router.push('/cgjsc')\r\n    },\r\n    //审批指南\r\n    //审批指南初始化列表\r\n    async spzn() {\r\n      let params = {\r\n        fwdyid: this.fwdyid,\r\n      }\r\n      let data = await getBlzn(params)\r\n      if (data.code == 10000) {\r\n        this.spznList = data.data.content\r\n      }\r\n    },\r\n    //审批信息\r\n    //审批信息初始化数据\r\n    // async spxxxgcc() {\r\n    //     // let params = {\r\n    //     //     slid: this.slid\r\n    //     // }\r\n    //     // let data = await getRyscInfoBySlid(params)\r\n    //     // this.upccLsit = data\r\n    //     // console.log('this.upccLsit', this.upccLsit);\r\n    //     // this.chRadio()\r\n    // },\r\n    fhry() {\r\n      this.$router.push({\r\n        path: '/ryspxqy',\r\n        query: {\r\n          row: this.$route.query.row\r\n        }\r\n      })\r\n    },\r\n    async spxx() {\r\n      let params = {\r\n        slid: this.slid\r\n      }\r\n      let data;\r\n      data = await selectRyglCgcjBySlId(params);\r\n      console.log(data);\r\n      this.tjlist = data.data.ryglCgcj\r\n      let result = data.data.ryglCgcj\r\n      // 初始化各状态值显示其代表的中文名\r\n      result.xingbie = result.xb == 2 ? '女' : result.xb == 1 ? '男' : ''\r\n      result.hyzk = result.hyzk == 0 ? '未婚' : result.hyzk == 1 ? '已婚' : ''\r\n      result.zzmmxx = result.zzmm == 1 ? '中共党员' : result.zzmm == 2 ? '团员' : result.zzmm == 3 ? '民主党派' : result.zzmm == 4 ? '群众' : ''\r\n      result.jbzc = result.jbzc == 1 ? '省部级' : result.jbzc == 2 ? '厅局级' : result.jbzc == 3 ? '县处级' : result.jbzc == 4 ? '乡科级及以下' : result.jbzc == 5 ? '高级(含正高、副高)' : result.jbzc == 6 ? '中级' : result.jbzc == 7 ? '初级及以下' : result.jbzc == 8 ? '试用期人员' : result.jbzc == 9 ? '工勤人员' : result.jbzc == 10 ? '企业职员' : result.jbzc == 11 ? '其他' : ''\r\n      let bmC = result.bmmc != '' ? '部门：' + result.bmmc + '、' : ''\r\n      let zwC = result.zw != '' ? '职务：' + result.zw + '、' : ''\r\n      result.bmzwzc = bmC + zwC + '职称：' + result.jbzc\r\n      // result.gwmc = result.gwmc && result.gwmc.length === 1 ? result.gwmc.toString() : result.gwmc && result.gwmc.length > 1 ? result.gwmc.join('/') : ''\r\n      result.smdjxx = result.smdj == 1 ? '核心' : result.smdj == 2 ? '重要' : result.smdj == 3 ? '一般' : ''\r\n      this.tjlist = result\r\n\r\n      if (result.splx == 1 || result.splx == 0) {\r\n        this.tjlist.splx = result.splx.toString()\r\n      } else {\r\n        console.log(123);\r\n        this.tjlist.splx = ''\r\n      }\r\n      if (data.data.ryglCgcjSwzjList != undefined) {\r\n        this.zzhmList = data.data.ryglCgcjSwzjList\r\n      }\r\n      this.zzhmList.forEach((item) => {\r\n        if (item.checked == 1) {\r\n          this.checkList.push(item.zzid)\r\n        }\r\n      })\r\n      if (this.tjlist.bmcns != '') {\r\n        this.ylth = true\r\n      }\r\n      if (this.tjlist.xqbmjyqkb != '') {\r\n        this.ylcn = true\r\n      }\r\n      if (this.tjlist.hfjlb != '') {\r\n        this.ylwt = true\r\n      }\r\n      if (result.qssj != '' || result.qssj != undefined) {\r\n        this.tjlist.value1 = []\r\n        this.tjlist.value1.push(result.qssj);\r\n        this.tjlist.value1.push(result.jssj);\r\n      }\r\n      if (data.data.ryglCgcjTxqkList.length == 0) {\r\n        this.ryglRyscJtcyList = [{\r\n          \"gx\": \"\",//关系描述\r\n          \"nl\": \"\",//年龄\r\n          \"zzmm\": \"\",//政治面貌\r\n          \"jwjlqk\": '',//是否有外籍、境外居留权、长期居留许可\r\n          \"xm\": \"\",//姓名\r\n          \"cgszd\": \"\",//工作(学习)单位\r\n          \"zw\": \"\",//职务\r\n          'czbtn1': '增加行',\r\n          'czbtn2': ''\r\n        }]\r\n      } else {\r\n        this.ryglRyscJtcyList = data.data.ryglCgcjTxqkList.map((data) => {\r\n          // if (data.jwjlqk == 0) {\r\n          //   data.jwjlqk = '否'\r\n          // } else if (data.jwjlqk == 1) {\r\n          //   data.jwjlqk = '是'\r\n          // }\r\n          data.czbtn1 = '增加行'\r\n          data.czbtn2 = '删除'\r\n          return data\r\n        })\r\n      }\r\n      this.tjlist.csny = this.tjlist.sfzhm.substring(6, 10) + \"-\" + this.tjlist.sfzhm.substring(10, 12) + \"-\" + this.tjlist.sfzhm.substring(12, 14);\r\n\r\n\r\n    },\r\n    // 预览\r\n    yulan() {\r\n      this.dialogVisible_brcn = true\r\n      const iamgeBase64 = \"data:image/jpeg;base64,\" + this.tjlist.brcn;\r\n      if (typeof iamgeBase64 === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64)) {\r\n          // debugger;\r\n          let that = this;\r\n\r\n          function previwImg(item) {\r\n            that.imageUrlbrcn = item;\r\n          }\r\n          previwImg(iamgeBase64);\r\n        }\r\n      }\r\n    },\r\n    // chRadio(val) {\r\n    //     console.log(val);\r\n    //     if (val == 1) {\r\n    //         this.btnsftg = false\r\n    //         this.btnsfth = true\r\n    //     } else if (val == 0) {\r\n    //         this.btnsftg = true\r\n    //         this.btnsfth = false\r\n    //     } else if (this.upccLsit.rysc.bmsc == 1) {\r\n    //         this.btnsftg = false\r\n    //         this.btnsfth = true\r\n    //     } else if (this.upccLsit.rysc.bmsc == 0) {\r\n    //         this.btnsftg = true\r\n    //         this.btnsfth = false\r\n    //     } else if (this.upccLsit.rysc.rlsc == 1) {\r\n    //         this.btnsftg = false\r\n    //         this.btnsfth = true\r\n    //     } else if (this.upccLsit.rysc.rlsc == 0) {\r\n    //         this.btnsftg = true\r\n    //         this.btnsfth = false\r\n    //     } else if (this.upccLsit.rysc.bmbsc == 1) {\r\n    //         this.btnsftg = false\r\n    //         this.btnsfth = true\r\n    //     } else if (this.upccLsit.rysc.bmbsc == 0) {\r\n    //         this.btnsftg = true\r\n    //         this.btnsfth = false\r\n    //     }\r\n    // },\r\n\r\n    // 通过\r\n    // async save(index) {\r\n    //     // let jgbz = index\r\n    //     // if (this.tjlist.pxqk != undefined) {\r\n    //     //     if (this.tjlist.cnsqk != undefined) {\r\n    //     //         if (this.tjlist.xysqk != undefined) {\r\n    //     //             if (this.tjlist.cnsrq != undefined) {\r\n    //     //                 let obj = {\r\n    //     //                     pxqk: this.tjlist.pxqk,\r\n    //     //                     cnsqk: this.tjlist.cnsqk,\r\n    //     //                     xysqk: this.tjlist.xysqk,\r\n    //     //                     cnsrq: this.tjlist.cnsrq,\r\n    //     //                     bmsc: this.tjlist.bmsc,\r\n    //     //                     bmscrq: this.tjlist.bmscrq,\r\n    //     //                     rlsc: this.tjlist.rlsc,\r\n    //     //                     rlscrq: this.tjlist.rlscrq,\r\n    //     //                     scqk: this.tjlist.scqk,\r\n    //     //                     bmbscrq: this.tjlist.bmbscrq,\r\n    //     //                 }\r\n    //     //                 let params = {\r\n    //     //                     ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,\r\n    //     //                     ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,\r\n    //     //                     ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,\r\n    //     //                     ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,\r\n    //     //                     ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,\r\n    //     //                     ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,\r\n    //     //                     rysc: Object.assign(this.upccLsit.rysc, obj)\r\n    //     //                 }\r\n    //     //                 let data = await updateRysc(params)\r\n    //     //                 if (data.code == 10000) {\r\n    //     //                     if (this.tjlist.bmscrq != undefined || this.tjlist.rlscrq != undefined || this.tjlist.bmbscrq != undefined) {\r\n    //     //                         if (this.tjlist.bmsc == 1 || this.tjlist.rlsc == 1 || this.tjlist.bmbsc == 1) {\r\n    //     //                             if (jgbz == 1) {\r\n    //     //                                 this.jgyf = 1\r\n    //     //                             }\r\n    //     //                             this.sxsh()\r\n    //     //                             this.spxx()\r\n    //     //                         }\r\n    //     //                         if (this.tjlist.bmsc == 2 || this.tjlist.rlsc == 2 || this.tjlist.bmbsc == 2) {\r\n    //     //                             if (jgbz == 2) {\r\n    //     //                                 this.jgyf = 2\r\n    //     //                             } else if (jgbz == 3) {\r\n    //     //                                 this.jgyf = 3\r\n    //     //                             }\r\n    //     //                             this.sxsh()\r\n    //     //                             this.spxx()\r\n    //     //                         }\r\n    //     //                     } else { this.$message.warning('请选择日期') }\r\n    //     //                 }\r\n    //     //             } else { this.$message.warning('请选择日期') }\r\n    //     //         } else { this.$message.warning('是否签订保密协议书') }\r\n    //     //     } else { this.$message.warning('是否签订保密承诺书') }\r\n    //     // } else { this.$message.warning('是否进行保密教育培训') }\r\n    // },\r\n    //立即办理\r\n    ljbl() {\r\n      this.activeName = 'second'\r\n    },\r\n    // //判断实例所处环节\r\n    // async pdschj() {\r\n    //     // let params = {\r\n    //     //     fwdyid: this.fwdyid,\r\n    //     //     slid: this.slid\r\n    //     // }\r\n    //     // let data = await getSchj(params)\r\n    //     // if (data.code == 10000) {\r\n    //     //     if (data.data.content == 1) {\r\n    //     //         this.disabled2 = true\r\n    //     //         this.disabled3 = true\r\n    //     //     }\r\n    //     //     if (data.data.content == 2) {\r\n    //     //         this.disabled1 = true\r\n    //     //         this.disabled3 = true\r\n    //     //     }\r\n    //     //     if (data.data.content == 3) {\r\n    //     //         this.disabled1 = true\r\n    //     //         this.disabled2 = true\r\n    //     //     }\r\n    //     // }\r\n    // },\r\n    // //事项审核\r\n    // async sxsh() {\r\n    //     // let params = {\r\n    //     //     fwdyid: this.fwdyid,\r\n    //     //     slid: this.slid,\r\n    //     //     jg: this.jgyf,\r\n    //     //     smryid: this.tjlist.smryid\r\n    //     // }\r\n    //     // let data = await getSxsh(params)\r\n    //     // if (data.code == 10000) {\r\n    //     //     if (data.data.zt == 0) {\r\n    //     //         this.$message({\r\n    //     //             message: data.data.msg,\r\n    //     //             type: 'success'\r\n    //     //         });\r\n    //     //         // this.smryList = data.data.blrarr\r\n    //     //         this.mbhjid = data.data.mbhjid\r\n    //     //         this.splist()\r\n    //     //         this.dialogVisible = true\r\n    //     //     } else if (data.data.zt == 1) {\r\n    //     //         this.$message({\r\n    //     //             message: data.data.msg,\r\n    //     //             type: 'success'\r\n    //     //         });\r\n    //     //     } else if (data.data.zt == 2) {\r\n    //     //         this.$message({\r\n    //     //             message: data.data.msg\r\n    //     //         });\r\n    //     //     } else if (data.data.zt == 3) {\r\n    //     //         this.$message({\r\n    //     //             message: data.data.msg\r\n    //     //         });\r\n    //     //     }\r\n    //     // }\r\n    // },\r\n    //初始化el-dialog列表数据\r\n    // async splist() {\r\n    //     // let params = {\r\n    //     //     fwdyid: this.fwdyid,\r\n    //     //     'xm': this.formInline.xm,\r\n    //     //     'bmmc': this.formInline.bmmc,\r\n    //     //     page: this.page,\r\n    //     //     pageSize: this.pageSize,\r\n    //     //     qshjid: this.mbhjid,\r\n    //     // }\r\n    //     // let data = await getSpUserList(params)\r\n    //     // this.smryList = data.records\r\n    //     // this.total = data.total\r\n    // },\r\n    // onSubmit() {\r\n    //     this.splist()\r\n    // },\r\n    // selectRow(selection) {\r\n    //     if (selection.length <= 1) {\r\n    //         console.log('点击选中数据：', selection);\r\n    //         this.selectlistRow = selection\r\n    //         this.xsyc = true\r\n    //     } else if (selection.length > 1) {\r\n    //         this.$message.warning('只能选中一条数据')\r\n    //         this.xsyc = false\r\n    //     }\r\n\r\n    // },\r\n    // handleSelect(selection, val) {\r\n    //     //只能选择一行，选择其他，清除上一行\r\n    //     if (selection.length > 1) {\r\n    //         let del_row = selection.shift()\r\n    //         this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中\r\n    //     }\r\n    // },\r\n    // // 点击行触发，选中或不选中复选框\r\n    // handleRowClick(row, column, event) {\r\n    //     this.$refs.multipleTable.toggleRowSelection(row)\r\n    //     this.selectChange(this.selectlistRow)\r\n    // },\r\n    // async submit() {\r\n    //     let params = {\r\n    //         fwdyid: this.fwdyid,\r\n    //         slid: this.slid,\r\n    //         shry: this.selectlistRow[0].yhid,\r\n    //         mbhjid: this.mbhjid,\r\n    //     }\r\n    //     let data = await tjclr(params)\r\n    //     if (data.code == 10000) {\r\n    //         this.$message({\r\n    //             message: data.message,\r\n    //             type: 'success'\r\n    //         });\r\n    //         this.dialogVisible = false\r\n    //     }\r\n    // },\r\n    //上传文件\r\n    beforeAvatarUpload(file) {\r\n      const isJPG = file.type === 'image/jpeg';\r\n      const isPNG = file.type === 'image/png';\r\n      if (!isJPG && !isPNG) {\r\n        this.$message.error('上传缩略图只能是 JPG/PNG 格式!');\r\n      }\r\n      return isJPG || isPNG;\r\n    },\r\n    zpzm(zp) {\r\n      const iamgeBase64 = \"data:image/jpeg;base64,\" + zp;\r\n      let zpxx\r\n      if (typeof iamgeBase64 === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64)) {\r\n          // debugger;\r\n          // let that = this;\r\n\r\n          function previwImg(item) {\r\n            zpxx = item;\r\n          }\r\n          previwImg(iamgeBase64);\r\n        }\r\n      }\r\n      return zpxx\r\n    },\r\n    // 预览\r\n    ylbmtxth() {\r\n      let zpxx\r\n      console.log(this.routeType)\r\n      zpxx = this.zpzm(this.tjlist.bmcns)\r\n      this.dialogImageUrl = zpxx\r\n      this.dialogThtxVisible = true\r\n    },\r\n    // 预览\r\n    ylbmcns() {\r\n      let zpxx\r\n      zpxx = this.zpzm(this.tjlist.xqbmjyqkb)\r\n      this.dialogBmcnsImageUrl = zpxx\r\n      this.dialogBmcnsVisible = true\r\n    },\r\n    // 预览\r\n    ylwts() {\r\n      let zpxx\r\n      console.log(this.routeType)\r\n      zpxx = this.zpzm(this.tjlist.hfjlb)\r\n      this.dialogWtsImageUrl = zpxx\r\n      this.dialogWtsVisible = true\r\n    },\r\n    // 64码\r\n    blobToBase64(blob, callback) {\r\n      const fileReader = new FileReader();\r\n      fileReader.onload = (e) => {\r\n        callback(e.target.result);\r\n      };\r\n      fileReader.readAsDataURL(blob);\r\n    },\r\n    //保密承诺书预览\r\n    bmcnsyl() {\r\n      this.dialogVisible_bmcns = true\r\n      const iamgeBase64 = \"data:image/jpeg;base64,\" + this.tjlist.cnssmj;\r\n      if (typeof iamgeBase64 === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64)) {\r\n          // debugger;\r\n          let that = this;\r\n\r\n          function previwImg(item) {\r\n            that.bmcnsImageUrl = item;\r\n          }\r\n          previwImg(iamgeBase64);\r\n        }\r\n      }\r\n    },\r\n    //保密协议书预览\r\n    bmxysyl() {\r\n      this.dialogVisible_bmxys = true\r\n      const iamgeBase64 = \"data:image/jpeg;base64,\" + this.tjlist.xyssmj;\r\n      if (typeof iamgeBase64 === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64)) {\r\n          // debugger;\r\n          let that = this;\r\n\r\n          function previwImg(item) {\r\n            that.bmxysImageUrl = item;\r\n          }\r\n          previwImg(iamgeBase64);\r\n        }\r\n      }\r\n    },\r\n    // //保密承诺书扫描件\r\n    // async bmcns(item) {\r\n    //     // this.file = item.file\r\n    //     this.file = URL.createObjectURL(item.file);\r\n    //     this.bmcnssmj = item.file\r\n    //     this.blobToBase64(item.file, async (dataurl) => {\r\n    //         this.upccLsit.rysc.cnssmj = dataurl.split(',')[1]\r\n    //         let params = {\r\n    //             ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,\r\n    //             ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,\r\n    //             ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,\r\n    //             ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,\r\n    //             ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,\r\n    //             ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,\r\n    //             rysc: this.upccLsit.rysc\r\n    //         }\r\n    //         let data = await updateRysc(params)\r\n    //         if (data.code == 10000) {\r\n    //             this.$message.success('上传保密承诺书成功！')\r\n    //             this.spxx()\r\n    //         }\r\n    //     });\r\n    // },\r\n    // //签订保密协议书\r\n    // async bmxys(item) {\r\n    //     // this.file = item.file\r\n    //     this.file = URL.createObjectURL(item.file);\r\n    //     this.bmxyssmj = item.file\r\n    //     this.blobToBase64(item.file, async (dataurl) => {\r\n    //         this.upccLsit.rysc.xyssmj = dataurl.split(',')[1]\r\n    //         let params = {\r\n    //             ryglRyscScjlList: this.upccLsit.ryglRyscScjlList,\r\n    //             ryglRyscJtcyList: this.upccLsit.ryglRyscJtcyList,\r\n    //             ryglRyscSwzjList: this.upccLsit.ryglRyscSwzjList,\r\n    //             ryglRyscYccgList: this.upccLsit.ryglRyscYccgList,\r\n    //             ryglRyscJwzzqkList: this.upccLsit.ryglRyscJwzzqkList,\r\n    //             ryglRyscCfjlList: this.upccLsit.ryglRyscCfjlList,\r\n    //             rysc: this.upccLsit.rysc\r\n    //         }\r\n    //         let data = await updateRysc(params)\r\n    //         if (data.code == 10000) {\r\n    //             this.$message.success('上传保密承诺书成功！')\r\n    //             this.spxx()\r\n    //         }\r\n    //     });\r\n    // },\r\n    // //列表分页--跳转页数\r\n    // handleCurrentChange(val) {\r\n    //     this.page = val\r\n    //     this.splist()\r\n    // },\r\n    // //列表分页--更改每页显示个数\r\n    // handleSizeChange(val) {\r\n    //     this.page = 1\r\n    //     this.pageSize = val\r\n    //     this.splist()\r\n    // },\r\n    //流程跟踪\r\n    //流程跟踪初始化列表\r\n    async lcgz() {\r\n      let params = {\r\n        fwdyid: this.fwdyid,\r\n        slid: this.slid\r\n      }\r\n      let data = await getSpGjxx(params)\r\n      if (data.code == 10000) {\r\n        this.lcgzList = data.data.content\r\n        this.gjclList = data.data.content\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.sec-container {\r\n  width: 100%;\r\n  height: calc(100% - 50px);\r\n  overflow-y: overlay;\r\n}\r\n\r\n.sec-container>>>.el-input.is-disabled .el-input__inner {\r\n  color: #000000;\r\n}\r\n\r\n.sec-title {\r\n  border-left: 5px solid #1b72d8;\r\n  color: #1b72d8;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  text-indent: 10px;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  /* height: 40px; */\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n  width: 258px;\r\n  position: absolute;\r\n  right: 0px;\r\n  top: 0;\r\n  height: 245px;\r\n  border: 1px solid #CDD2D9;\r\n  border-left: 0;\r\n  background: #ffffff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.sec-form-second {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-third {\r\n  border: 1px solid #CDD2D9;\r\n  /* height: 40px;  */\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-four {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  overflow: hidden;\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.yulan {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  color: #3874D5;\r\n  font-weight: 600;\r\n  float: left;\r\n  margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n  width: 178px;\r\n  height: 178px;\r\n  display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 178px;\r\n  height: 178px;\r\n  line-height: 178px;\r\n  text-align: center;\r\n  border: 2px solid #EBEBEB;\r\n}\r\n\r\n.sec-form-six {\r\n  border: 1px solid #CDD2D9;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.ml10 {\r\n  margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n  margin-top: 10px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n  border: none;\r\n}\r\n\r\n.sec-left-text {\r\n  float: left;\r\n  margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n  border-top: 1px solid #CDD2D9;\r\n}\r\n\r\n\r\n>>>.longLabel .el-form-item__content {\r\n  padding-left: 20px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n  border-top: 0;\r\n} */\r\n.sec-form-second .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n  width: 100%;\r\n  border: 1px solid #EBEEF5;\r\n  height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n.fhry {\r\n  float: right;\r\n  z-index: 99;\r\n  margin-top: 5px;\r\n  position: relative;\r\n}\r\n\r\n.hyzk {\r\n  padding-left: 15px;\r\n  background-color: #F5F7FA;\r\n  width: calc(100% - 16px);\r\n  border-right: 1px solid #CDD2D9;\r\n  color: #000000;\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n  border: none !important;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n  width: 200px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n  border: none;\r\n  border-right: 1px solid #CDD2D9;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n  border: 1px solid #CDD2D9;;\r\n} */\r\n>>>.el-form-item__label {\r\n  border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n/* /deep/.sec-form-container .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n} */\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n\r\n>>>.widthzz .el-form-item__label {\r\n  height: 270px;\r\n  line-height: 270px;\r\n}\r\n\r\n>>>.widthzz .el-input__inner {\r\n  border-right: none;\r\n}\r\n\r\n.flex {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-demo {\r\n  margin-left: 114px;\r\n}\r\n\r\n.upload-demo2 {\r\n  margin-left: 34px;\r\n}\r\n\r\n.upload-demo3 {\r\n  margin-left: 114px;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/wdgz/blsp/cgjblxxscb.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"sec-container\"},[_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.deb),expression:\"deb\"}],staticClass:\"fhry\",attrs:{\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":_vm.fhry}},[_vm._v(\"返回\")]),_vm._v(\" \"),_c('el-tabs',{model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:\"activeName\"}},[_c('el-tab-pane',{attrs:{\"label\":\"审批指南\",\"name\":\"first\"}},[_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.ljbl}},[_vm._v(\"立即办理\")])],1),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.spznList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"hjmc\",\"label\":\"办理流程\"}})],1)],1),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"label\":\"审批信息\",\"name\":\"second\"}},[_c('p',{staticClass:\"sec-title-big\"},[_vm._v(\"涉密人员保密审查表\")]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"基本信息\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"姓名\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xm\", $$v)},expression:\"tjlist.xm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"性别\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xingbie),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xingbie\", $$v)},expression:\"tjlist.xingbie\"}})]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"出生年月日\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.csny),callback:function ($$v) {_vm.$set(_vm.tjlist, \"csny\", $$v)},expression:\"tjlist.csny\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"政治面貌\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.zzmmxx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zzmmxx\", $$v)},expression:\"tjlist.zzmmxx\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"涉密岗位\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.gwmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gwmc\", $$v)},expression:\"tjlist.gwmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"涉密等级\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.smdjxx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"smdjxx\", $$v)},expression:\"tjlist.smdjxx\"}})]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"工作单位及职务\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.bmzwzc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmzwzc\", $$v)},expression:\"tjlist.bmzwzc\"}})]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"身份证号\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.sfzhm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sfzhm\", $$v)},expression:\"tjlist.sfzhm\"}})],1)],1)]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"审批事项\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"审批事件类型\"}},[_c('el-radio',{attrs:{\"label\":\"1\",\"disabled\":\"\"},model:{value:(_vm.tjlist.splx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"splx\", $$v)},expression:\"tjlist.splx\"}},[_vm._v(\"新申办从出入境证件\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"0\",\"disabled\":\"\"},model:{value:(_vm.tjlist.splx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"splx\", $$v)},expression:\"tjlist.splx\"}},[_vm._v(\"申请出国（境）\")])],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left longLabel widthzz\"},[_c('el-form-item',{attrs:{\"label\":\"证件类型\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"flex-direction\":\"column\"}},_vm._l((_vm.zzhmList),function(item,index){return _c('div',{key:item.zzid},[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"justify-content\":\"space-between\"}},[_c('el-checkbox-group',{attrs:{\"disabled\":\"\"},model:{value:(_vm.checkList),callback:function ($$v) {_vm.checkList=$$v},expression:\"checkList\"}},[_c('el-checkbox',{staticStyle:{\"width\":\"200px\"},attrs:{\"label\":item.zzid}},[_vm._v(_vm._s(item.fjlb))])],1),_vm._v(\" \"),_c('div',[_vm._v(\"证件号码:\"),_c('el-input',{staticStyle:{\"width\":\"200px\"},attrs:{\"disabled\":\"\"},on:{\"blur\":_vm.zzxx},model:{value:(item.zjhm),callback:function ($$v) {_vm.$set(item, \"zjhm\", $$v)},expression:\"item.zjhm\"}})],1),_vm._v(\" \"),_c('div',[_vm._v(\"有效期:\"),_c('el-date-picker',{staticStyle:{\"width\":\"200px\"},attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"disabled\":\"\"},model:{value:(item.yxq),callback:function ($$v) {_vm.$set(item, \"yxq\", $$v)},expression:\"item.yxq\"}})],1)],1)])}),0)])],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"本年度因私出国(境)次数\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.cs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"cs\", $$v)},expression:\"tjlist.cs\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"前往国家(地区)\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.qwgj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qwgj\", $$v)},expression:\"tjlist.qwgj\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"起止日期\"}},[_c('el-date-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"disabled\":\"\"},model:{value:(_vm.tjlist.value1),callback:function ($$v) {_vm.$set(_vm.tjlist, \"value1\", $$v)},expression:\"tjlist.value1\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"出国（境）事由\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.sy),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sy\", $$v)},expression:\"tjlist.sy\"}})],1)],1)])])],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"同行人员情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscJtcyList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gx\",\"label\":\"与本人关系\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":\"\"},model:{value:(scope.row.gx),callback:function ($$v) {_vm.$set(scope.row, \"gx\", $$v)},expression:\"scope.row.gx\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xm\",\"label\":\"姓名\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":\"\"},model:{value:(scope.row.xm),callback:function ($$v) {_vm.$set(scope.row, \"xm\", $$v)},expression:\"scope.row.xm\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"nl\",\"label\":\"年龄\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":\"\"},model:{value:(scope.row.nl),callback:function ($$v) {_vm.$set(scope.row, \"nl\", $$v)},expression:\"scope.row.nl\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzmm\",\"label\":\"政治面貌\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-select',{attrs:{\"placeholder\":\"请选择\"},model:{value:(scope.row.zzmm),callback:function ($$v) {_vm.$set(scope.row, \"zzmm\", $$v)},expression:\"scope.row.zzmm\"}},_vm._l((_vm.zzmmoptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value,\"disabled\":\"\"}})}),1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zw\",\"label\":\"工作单位,职务及居住地\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":\"\"},model:{value:(scope.row.zw),callback:function ($$v) {_vm.$set(scope.row, \"zw\", $$v)},expression:\"scope.row.zw\"}})]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-five haveBorderTop\",staticStyle:{\"position\":\"relative\"}},[_c('div',{staticClass:\"sec-left-text\"},[_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n                1.上传保密提醒谈话确认扫描件\\n                \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylth),expression:\"ylth\"}],staticClass:\"upload-demo\",attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylbmtxth}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogThtxVisible},on:{\"update:visible\":function($event){_vm.dialogThtxVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogThtxVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n                2.上传离岗离职涉密人员保密承诺书扫描件\\n                \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylcn),expression:\"ylcn\"}],staticClass:\"upload-demo2\",attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylbmcns}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogBmcnsVisible},on:{\"update:visible\":function($event){_vm.dialogBmcnsVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogBmcnsImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogBmcnsVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n                3.上传脱密期委托管理书扫描件\\n                \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylwt),expression:\"ylwt\"}],staticClass:\"upload-demo3\",attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylwts}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogWtsVisible},on:{\"update:visible\":function($event){_vm.dialogWtsVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogWtsImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogWtsVisible = false}}},[_vm._v(\"取 消\")])],1)])],1)])]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"所在部门意见\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"信息属实，拟\",\"prop\":\"bmsc\"}},_vm._l((_vm.scqk),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":_vm.disabled1},on:{\"change\":_vm.chRadio},model:{value:(_vm.tjlist.bmsc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmsc\", $$v)},expression:\"tjlist.bmsc\"}},[_vm._v(_vm._s(item.sfty))])}),1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"gtzzsmgwgz\",attrs:{\"label\":\"出国出境\",\"prop\":\"gtzzsmgwgz\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"部门领导审批人\",\"prop\":\"bmspr\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":_vm.disabled1,\"clearable\":\"\"},model:{value:(_vm.tjlist.bmscxm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmscxm\", $$v)},expression:\"tjlist.bmscxm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"日期\",\"prop\":\"bmscrq\"}},[_c('el-date-picker',{attrs:{\"disabled\":_vm.disabled1,\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.tjlist.bmscsj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmscsj\", $$v)},expression:\"tjlist.bmscsj\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"保密办意见\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"信息属实，拟\",\"prop\":\"bmsc\"}},_vm._l((_vm.scqk),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":_vm.disabled2},on:{\"change\":_vm.chRadio},model:{value:(_vm.tjlist.bmbsc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmbsc\", $$v)},expression:\"tjlist.bmbsc\"}},[_vm._v(_vm._s(item.sfty))])}),1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"gtzzsmgwgz\",attrs:{\"label\":\"出国出境\",\"prop\":\"gtzzsmgwgz\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"部门领导审批人\",\"prop\":\"bmspr\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":_vm.disabled2,\"clearable\":\"\"},model:{value:(_vm.tjlist.bmbscxm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmbscxm\", $$v)},expression:\"tjlist.bmbscxm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"日期\",\"prop\":\"bmscrq\"}},[_c('el-date-picker',{attrs:{\"disabled\":_vm.disabled2,\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.tjlist.bmbscsj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmbscsj\", $$v)},expression:\"tjlist.bmbscsj\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"人力资源部意见\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"信息属实，拟\",\"prop\":\"bmsc\"}},_vm._l((_vm.scqk),function(item){return _c('el-radio',{key:item.id,attrs:{\"label\":item.id,\"disabled\":_vm.disabled3},on:{\"change\":_vm.chRadio},model:{value:(_vm.tjlist.rlsc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"rlsc\", $$v)},expression:\"tjlist.rlsc\"}},[_vm._v(_vm._s(item.sfty))])}),1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"gtzzsmgwgz\",attrs:{\"label\":\"出国出境\",\"prop\":\"gtzzsmgwgz\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"部门领导审批人\",\"prop\":\"bmspr\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"disabled\":_vm.disabled3,\"clearable\":\"\"},model:{value:(_vm.tjlist.rlscxm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"rlscxm\", $$v)},expression:\"tjlist.rlscxm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"日期\",\"prop\":\"bmscrq\"}},[_c('el-date-picker',{attrs:{\"disabled\":_vm.disabled3,\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.tjlist.rlscsj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"rlscsj\", $$v)},expression:\"tjlist.rlscsj\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"轨迹处理\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.gjclList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"hjmc\",\"label\":\"办理环节\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clrid\",\"label\":\"办理人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bllx\",\"label\":\"办理类型\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clyj\",\"label\":\"办理意见\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xybclr\",\"label\":\"下一步办理人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clsj\",\"label\":\"办理时间\"}})],1)],1)],1)]),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"label\":\"流程跟踪\",\"name\":\"third\"}},[_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.lcgzList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"hjmc\",\"label\":\"办理环节\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clrid\",\"label\":\"办理人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bllx\",\"label\":\"办理类型\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clyj\",\"label\":\"办理意见\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xybclr\",\"label\":\"下一步办理人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clsj\",\"label\":\"办理时间\"}})],1)],1)],1)],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-80e08f8a\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/wdgz/blsp/cgjblxxscb.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-80e08f8a\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./cgjblxxscb.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./cgjblxxscb.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./cgjblxxscb.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-80e08f8a\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./cgjblxxscb.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-80e08f8a\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/wdgz/blsp/cgjblxxscb.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}