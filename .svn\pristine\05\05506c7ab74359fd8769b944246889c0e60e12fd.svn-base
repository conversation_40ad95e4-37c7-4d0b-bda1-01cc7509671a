<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item label="台账时间" style="font-weight: 700;">
                  <!-- <el-input v-model="formInline.tzsj" clearable placeholder="台账时间" class="widthw">
                  </el-input> -->
                  <el-select v-model="formInline.tzsj" placeholder="台账时间">
                    <el-option v-for="item in yearSelect" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.tjnf" clearable placeholder="年度"
                    oninput="value=value.replace(/[^\d.]/g,'')" @blur="tjnf = $event.target.value" class="widths">
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <!-- <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item> -->
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" @click="fh()">返回
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList">
                    导出
                  </el-button>
                </el-form-item>
                <!-- <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="dialogVisible = true" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item> -->
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="bmqsxqdqlList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 41px - 3px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <!-- <el-table-column prop="mc" label="名称"></el-table-column> -->
                  <el-table-column prop="tjnf" label="年度" width="60" align="center"></el-table-column>
                  <el-table-column prop="gjmmzs" label="国家秘密数量"></el-table-column>
                  <el-table-column prop="dmzrrs" label="定密责任人数量"></el-table-column>
                  <el-table-column prop="dmsqxzs" label="定密授权数量"></el-table-column>
                  <el-table-column prop="gjmmylbs" label="国家秘密事项数量"></el-table-column>
                  <el-table-column prop="dmzds" label="定密制度数量"></el-table-column>
                  <el-table-column prop="dmpxcs" label="定密培训次数"></el-table-column>
                  <el-table-column prop="gzmms" label="工作秘密数量"></el-table-column>
                  <el-table-column prop="tznf" label="台账时间"></el-table-column>
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <!-- <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button> -->
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs">
                <el-radio label="追加（导入时已有的记录信息不变，只添加新的记录）"></el-radio>
                <el-radio label="覆盖（导入时更新已有的记录信息，并添加新的记录"></el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>

        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->
        <el-dialog title="定密情况年度统计信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="50%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" size="mini">
            <div class="div-tabs">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="国家秘密统计情况" name="gjmmtjqk" style="padding: 0 10px;">
                  <div style="margin-bottom:10px">一，国家秘密统计情况</div>
                  <el-form-item label="年度" class="one-line">
                    <el-input placeholder="年度" v-model="tjlist.tjnf" clearable style="width:100px" disabled></el-input>
                  </el-form-item>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">原始国家秘密数</div>
                      <el-form-item label="绝密" prop="ysums" class="one-line">
                        <el-input placeholder="绝密" v-model="tjlist.ysums" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(1)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机密" prop="ysims" class="one-line">
                        <el-input placeholder="机密" v-model="tjlist.ysims" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(1)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘密" prop="ysmms" class="one-line">
                        <el-input placeholder="秘密" v-model="tjlist.ysmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(1)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="tjlist.ysgjmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <div class="input-tou">派生国家秘密数</div>

                      <el-form-item label="绝密" prop="psums" class="one-line">
                        <el-input placeholder="绝密" v-model="tjlist.psums" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(1)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机密" prop="psims" class="one-line">
                        <el-input placeholder="机密" v-model="tjlist.psims" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(1)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘密" prop="psmms" class="one-line">
                        <el-input placeholder="秘密" v-model="tjlist.psmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(1)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="tjlist.psgjmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">国家秘密总数</div>

                      <el-form-item label="绝密" class="one-line">
                        <el-input placeholder="绝密" v-model="tjlist.umzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="机密" class="one-line">
                        <el-input placeholder="机密" v-model="tjlist.imzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="秘密" class="one-line">
                        <el-input placeholder="秘密" v-model="tjlist.mmzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="tjlist.gjmmzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>

                  </div>

                  <div style="display:flex">
                    <el-form-item label="变更数" prop="bgs" class="one-line">
                      <el-input placeholder="变更数" v-model="tjlist.bgs" clearable style="width: 100%;" type="number"
                        onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                    </el-form-item>
                    <el-form-item label="解密数" prop="jms" class="one-line">
                      <el-input placeholder="解密数" v-model="tjlist.jms" clearable style="width: 100%;" type="number"
                        onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                    </el-form-item>
                    <div class="one-line" style="border-width: 0;"></div>
                  </div>

                </el-tab-pane>
                <el-tab-pane label="定密责任人数" name="dmzrrs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">二，定密责任人数</div>
                    <el-button size="small" @click="$router.push('/dmzrr')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">法定定密责任人数</div>

                      <el-form-item label="绝、机、秘" prop="fdjjms" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="tjlist.fdjjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(2)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" prop="fdjms" class="one-line">
                        <el-input placeholder="机、秘" v-model="tjlist.fdjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(2)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘" prop="fdms" class="one-line">
                        <el-input placeholder="秘" v-model="tjlist.fdms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(2)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="tjlist.fdzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">指定定密责任人数</div>

                      <el-form-item label="绝、机、秘" prop="zdjjms" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="tjlist.zdjjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(2)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" prop="zdjms" class="one-line">
                        <el-input placeholder="机、秘" v-model="tjlist.zdjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(2)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘" prop="zdms" class="one-line">
                        <el-input placeholder="秘" v-model="tjlist.zdms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(2)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="tjlist.zdzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">定密责任人数</div>

                      <el-form-item label="绝、机、秘" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="tjlist.jjmzrrs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled>
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line">
                        <el-input placeholder="机、秘" v-model="tjlist.jmzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line">
                        <el-input placeholder="秘" v-model="tjlist.mzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="tjlist.dmzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>

                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密授权数" name="dmsqs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">三、定密授权数</div>
                    <el-button size="small" @click="$router.push('/dmsq')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">现在总数</div>

                      <el-form-item label="绝、机、秘" class="one-line" prop="jjmsqs">
                        <el-input placeholder="绝、机、秘" v-model="tjlist.jjmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(3)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line" prop="jmsqs">
                        <el-input placeholder="机、秘" v-model="tjlist.jmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(3)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line" prop="msqs">
                        <el-input placeholder="秘" v-model="tjlist.msqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(3)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="tjlist.dmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">新增数</div>

                      <el-form-item label="绝、机、秘" class="one-line" prop="jjmsqxzs">
                        <el-input placeholder="绝、机、秘" v-model="tjlist.jjmsqxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                          @blur="jszs(3)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line" prop="jmsqxzs">
                        <el-input placeholder="机、秘" v-model="tjlist.jmsqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(3)"></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line" prop="msqxzs">
                        <el-input placeholder="秘" v-model="tjlist.msqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(3)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="tjlist.dmsqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="国家秘密事项一栏表（细目）数" name="gjmmsxylbs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">四、国家秘密事项一栏表（细目）数</div>
                    <el-button size="small" @click="$router.push('/gjmmsx')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">现在总数</div>

                      <el-form-item label="一览表数" class="one-line" prop="gjmmylbs">
                        <el-input placeholder="一览表数" v-model="tjlist.gjmmylbs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="一栏表条目数" class="one-line" prop="gjmmylbtms">
                        <el-input placeholder="一栏表条目数" v-model="tjlist.gjmmylbtms" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">新增数</div>

                      <el-form-item label="一览表数" class="one-line" prop="gjmmylbxzs">
                        <el-input placeholder="一览表数" v-model="tjlist.gjmmylbxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="一栏表条目数" class="one-line" prop="gjmmylbtmxzs">
                        <el-input placeholder="一栏表条目数" v-model="tjlist.gjmmylbtmxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密制度数" name="dmzds">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">五、定密制度数</div>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>

                      <el-form-item label="现在总数" prop="dmzds" class="one-line">
                        <el-input placeholder="现在总数" v-model="tjlist.dmzds" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="新制定修订数" prop="dmzdxzs" class="one-line">
                        <el-input placeholder="新制定修订数" v-model="tjlist.dmzdxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密培训数" name="dmpxs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">六、定密培训数</div>
                    <el-button size="small" @click="$router.push('/dmpx')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>

                      <el-form-item label="培训次数" prop="dmpxcs" class="one-line">
                        <el-input placeholder="培训次数" v-model="tjlist.dmpxcs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="总学时数" prop="zxss" class="one-line">
                        <el-input placeholder="总学时数" v-model="tjlist.zxss" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="总人数" prop="zrs" class="one-line">
                        <el-input placeholder="总人数" v-model="tjlist.zrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="工作秘密数" name="gzmms">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">七、工作秘密数</div>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>

                      <el-form-item label="工作秘密确定数" prop="gzmms" class="one-line larger-title">
                        <el-input placeholder="工作秘密确定数" v-model="tjlist.gzmms" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="工作秘密清单应制定数" prop="gzmmyzds" class="one-line larger-title">
                        <el-input placeholder="工作秘密清单应制定数" v-model="tjlist.gzmmyzds" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="工作秘密清单实制定数" prop="gzmmszds" class="one-line larger-title">
                        <el-input placeholder="工作秘密清单实制定数" v-model="tjlist.gzmmszds" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>

            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="tjlist.bz"></el-input>
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog title="修改定密情况年度统计信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="50%"
          class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" size="mini">
            <div class="div-tabs">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="国家秘密统计情况" name="gjmmtjqk" style="padding: 0 10px;">
                  <div style="margin-bottom:10px">一，国家秘密统计情况</div>

                  <el-form-item label="年度" class="one-line">
                    <el-input placeholder="年度" v-model="xglist.tjnf" clearable style="width:100px" disabled></el-input>
                  </el-form-item>

                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">原始国家秘密数</div>

                      <el-form-item label="绝密" class="one-line" prop="ysums">
                        <el-input placeholder="绝密" v-model="xglist.ysums" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(4)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机密" class="one-line" prop="ysims">
                        <el-input placeholder="机密" v-model="xglist.ysims" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(4)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘密" class="one-line" prop="ysmms">
                        <el-input placeholder="秘密" v-model="xglist.ysmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(4)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.ysgjmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">派生国家秘密数</div>

                      <el-form-item label="绝密" class="one-line" prop="psums">
                        <el-input placeholder="绝密" v-model="xglist.psums" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(4)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机密" class="one-line" prop="psims">
                        <el-input placeholder="机密" v-model="xglist.psims" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(4)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘密" class="one-line" prop="psmms">
                        <el-input placeholder="秘密" v-model="xglist.psmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(4)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.psgjmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">国家秘密总数</div>

                      <el-form-item label="绝密" class="one-line">
                        <el-input placeholder="绝密" v-model="xglist.umzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="机密" class="one-line">
                        <el-input placeholder="机密" v-model="xglist.imzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="秘密" class="one-line">
                        <el-input placeholder="秘密" v-model="xglist.mmzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.gjmmzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>

                  </div>

                  <div style="display:flex">
                    <el-form-item label="变更数" class="one-line" prop="bgs">
                      <el-input placeholder="变更数" v-model="xglist.bgs" clearable style="width: 100%;" type="number"
                        onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                    </el-form-item>
                    <el-form-item label="解密数" class="one-line" prop="jms">
                      <el-input placeholder="解密数" v-model="xglist.jms" clearable style="width: 100%;" type="number"
                        onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                    </el-form-item>
                    <div class="one-line" style="border-width: 0;"></div>
                  </div>

                </el-tab-pane>
                <el-tab-pane label="定密责任人数" name="dmzrrs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">二，定密责任人数</div>
                    <el-button size="small" @click="$router.push('/dmzrr')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">法定定密责任人数</div>

                      <el-form-item label="绝、机、秘" class="one-line" prop="fdjjms">
                        <el-input placeholder="绝、机、秘" v-model="xglist.fdjjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(5)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line" prop="fdjms">
                        <el-input placeholder="机、秘" v-model="xglist.fdjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(5)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line" prop="fdms">
                        <el-input placeholder="秘" v-model="xglist.fdms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(5)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.fdzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">指定定密责任人数</div>

                      <el-form-item label="绝、机、秘" class="one-line" prop="zdjjms">
                        <el-input placeholder="绝、机、秘" v-model="xglist.zdjjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(5)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line" prop="zdjms">
                        <el-input placeholder="机、秘" v-model="xglist.zdjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(5)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line" prop="zdms">
                        <el-input placeholder="秘" v-model="xglist.zdms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(5)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.zdzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">定密责任人数</div>

                      <el-form-item label="绝、机、秘" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="xglist.jjmzrrs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled>
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line">
                        <el-input placeholder="机、秘" v-model="xglist.jmzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line">
                        <el-input placeholder="秘" v-model="xglist.mzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.dmzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>

                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密授权数" name="dmsqs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">三、定密授权数</div>
                    <el-button size="small" @click="$router.push('/dmsq')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">现在总数</div>

                      <el-form-item label="绝、机、秘" class="one-line" prop="jjmsqs">
                        <el-input placeholder="绝、机、秘" v-model="xglist.jjmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(6)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line" prop="jmsqs">
                        <el-input placeholder="机、秘" v-model="xglist.jmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(6)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line" prop="msqs">
                        <el-input placeholder="秘" v-model="xglist.msqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(6)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.dmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">新增数</div>

                      <el-form-item label="绝、机、秘" class="one-line" prop="jjmsqxzs">
                        <el-input placeholder="绝、机、秘" v-model="xglist.jjmsqxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                          @blur="jszs(6)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line" prop="jmsqxzs">
                        <el-input placeholder="机、秘" v-model="xglist.jmsqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(6)"></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line" prop="msqxzs">
                        <el-input placeholder="秘" v-model="xglist.msqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" @blur="jszs(6)">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.dmsqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))" disabled></el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="国家秘密事项一栏表（细目）数" name="gjmmsxylbs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">四、国家秘密事项一栏表（细目）数</div>
                    <el-button size="small" @click="$router.push('/gjmmsx')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">现在总数</div>

                      <el-form-item label="一览表数" class="one-line" prop="gjmmylbs">
                        <el-input placeholder="一览表数" v-model="xglist.gjmmylbs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="一栏表条目数" class="one-line" prop="gjmmylbtms">
                        <el-input placeholder="一栏表条目数" v-model="xglist.gjmmylbtms" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                    <div>
                      <div class="input-tou">新增数</div>

                      <el-form-item label="一览表数" class="one-line" prop="gjmmylbxzs">
                        <el-input placeholder="一览表数" v-model="xglist.gjmmylbxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="一栏表条目数" class="one-line" prop="gjmmylbtmxzs">
                        <el-input placeholder="一栏表条目数" v-model="xglist.gjmmylbtmxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密制度数" name="dmzds">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">五、定密制度数</div>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>

                      <el-form-item label="现在总数" prop="dmzds" class="one-line">
                        <el-input placeholder="现在总数" v-model="xglist.dmzds" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="新制定修订数" prop="dmzdxzs" class="one-line">
                        <el-input placeholder="新制定修订数" v-model="xglist.dmzdxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密培训数" name="dmpxs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">六、定密培训数</div>
                    <el-button size="small" @click="$router.push('/dmpx')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>

                      <el-form-item label="培训次数" prop="dmpxcs" class="one-line">
                        <el-input placeholder="培训次数" v-model="xglist.dmpxcs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="总学时数" prop="zxss" class="one-line">
                        <el-input placeholder="总学时数" v-model="xglist.zxss" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="总人数" prop="zrs" class="one-line">
                        <el-input placeholder="总人数" v-model="xglist.zrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="工作秘密数" name="gzmms">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">七、工作秘密数</div>

                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>

                      <el-form-item label="工作秘密确定数" prop="gzmms" class="one-line larger-title">
                        <el-input placeholder="工作秘密确定数" v-model="xglist.gzmms" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="工作秘密清单应制定数" prop="gzmmyzds" class="one-line larger-title">
                        <el-input placeholder="工作秘密清单应制定数" v-model="xglist.gzmmyzds" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="工作秘密清单实制定数" prop="gzmmszds" class="one-line larger-title">
                        <el-input placeholder="工作秘密清单实制定数" v-model="xglist.gzmmszds" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>

                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>

            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="定密情况年度统计信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="50%"
          class="xg">
          <el-form ref="form" :model="xglist" :rules="rules" size="mini">
            <div class="div-tabs">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="国家秘密统计情况" name="gjmmtjqk" style="padding: 0 10px;">
                  <div style="margin-bottom:10px">一，国家秘密统计情况</div>
                  <el-form-item label="年度" class="one-line">
                    <el-input placeholder="年度" v-model="xglist.tjnf" clearable style="width:100px" disabled></el-input>
                  </el-form-item>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">原始国家秘密数</div>
                      <el-form-item label="绝密" class="one-line">
                        <el-input placeholder="绝密" v-model="xglist.ysums" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="机密" class="one-line">
                        <el-input placeholder="机密" v-model="xglist.ysims" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="秘密" class="one-line">
                        <el-input placeholder="秘密" v-model="xglist.ysmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.ysgjmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <div class="input-tou">派生国家秘密数</div>
                      <el-form-item label="绝密" class="one-line">
                        <el-input placeholder="绝密" v-model="xglist.psums" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="机密" class="one-line">
                        <el-input placeholder="机密" v-model="xglist.psims" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="秘密" class="one-line">
                        <el-input placeholder="秘密" v-model="xglist.psmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.psgjmms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <div class="input-tou">国家秘密总数</div>
                      <el-form-item label="绝密" class="one-line">
                        <el-input placeholder="绝密" v-model="xglist.umzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="机密" class="one-line">
                        <el-input placeholder="机密" v-model="xglist.imzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="秘密" class="one-line">
                        <el-input placeholder="秘密" v-model="xglist.mmzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.gjmmzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                    </div>

                  </div>
                  <div style="display:flex">
                    <el-form-item label="变更数" class="one-line">
                      <el-input placeholder="变更数" v-model="xglist.bgs" clearable style="width: 100%;" type="number"
                        onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                    </el-form-item>
                    <el-form-item label="解密数" class="one-line">
                      <el-input placeholder="解密数" v-model="xglist.jms" clearable style="width: 100%;" type="number"
                        onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                    </el-form-item>
                    <div class="one-line" style="border-width: 0;"></div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密责任人数" name="dmzrrs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">二，定密责任人数</div>
                    <el-button size="small" @click="$router.push('/dmzrr')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">法定定密责任人数</div>
                      <el-form-item label="绝、机、秘" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="xglist.fdjjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line">
                        <el-input placeholder="机、秘" v-model="xglist.fdjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line">
                        <el-input placeholder="秘" v-model="xglist.fdms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.fdzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <div class="input-tou">指定定密责任人数</div>
                      <el-form-item label="绝、机、秘" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="xglist.zdjjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line">
                        <el-input placeholder="机、秘" v-model="xglist.zdjms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line">
                        <el-input placeholder="秘" v-model="xglist.zdms" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.zdzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <div class="input-tou">定密责任人数</div>
                      <el-form-item label="绝、机、秘" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="xglist.jjmzrrs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line">
                        <el-input placeholder="机、秘" v-model="xglist.jmzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line">
                        <el-input placeholder="秘" v-model="xglist.mzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.dmzrrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                    </div>

                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密授权数" name="dmsqs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">三、定密授权数</div>
                    <el-button size="small" @click="$router.push('/dmsq')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">现在总数</div>
                      <el-form-item label="绝、机、秘" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="xglist.jjmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line">
                        <el-input placeholder="机、秘" v-model="xglist.jmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line">
                        <el-input placeholder="秘" v-model="xglist.msqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.dmsqs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <div class="input-tou">新增数</div>
                      <el-form-item label="绝、机、秘" class="one-line">
                        <el-input placeholder="绝、机、秘" v-model="xglist.jjmsqxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="机、秘" class="one-line">
                        <el-input placeholder="机、秘" v-model="xglist.jmsqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="秘" class="one-line">
                        <el-input placeholder="秘" v-model="xglist.msqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                      <el-form-item label="合计" class="one-line">
                        <el-input placeholder="合计" v-model="xglist.dmsqxzs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"></el-input>
                      </el-form-item>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="国家秘密事项一栏表（细目）数" name="gjmmsxylbs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">四、国家秘密事项一栏表（细目）数</div>
                    <el-button size="small" @click="$router.push('/gjmmsx')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <div class="input-tou">现在总数</div>
                      <el-form-item label="一览表数" class="one-line">
                        <el-input placeholder="一览表数" v-model="xglist.gjmmylbs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="一栏表条目数" class="one-line">
                        <el-input placeholder="一栏表条目数" v-model="xglist.gjmmylbtms" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                    </div>
                    <div>
                      <div class="input-tou">新增数</div>
                      <el-form-item label="一览表数" class="one-line">
                        <el-input placeholder="一览表数" v-model="xglist.gjmmylbxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="一栏表条目数" class="one-line">
                        <el-input placeholder="一栏表条目数" v-model="xglist.gjmmylbtmxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密制度数" name="dmzds">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">五、定密制度数</div>

                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <el-form-item label="现在总数" class="one-line">
                        <el-input placeholder="现在总数" v-model="xglist.dmzds" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="新制定修订数" class="one-line">
                        <el-input placeholder="新制定修订数" v-model="xglist.dmzdxzs" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="定密培训数" name="dmpxs" style="padding: 0 10px;">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">六、定密培训数</div>
                    <el-button size="small" @click="$router.push('/dmpx')">查看详情</el-button>
                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <el-form-item label="培训次数" class="one-line">
                        <el-input placeholder="培训次数" v-model="xglist.dmpxcs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="总学时数" class="one-line">
                        <el-input placeholder="总学时数" v-model="xglist.zxss" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="总人数" class="one-line">
                        <el-input placeholder="总人数" v-model="xglist.zrs" clearable style="width: 100%;" type="number"
                          onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="工作秘密数" name="gzmms">
                  <div style="display:flex;justify-content: space-between;align-items: center;">
                    <div style="margin-bottom:10px">七、工作秘密数</div>

                  </div>
                  <div style="display:flex;justify-content: space-between;">
                    <div>
                      <el-form-item label="工作秘密确定数" class="one-line larger-title">
                        <el-input placeholder="工作秘密确定数" v-model="xglist.gzmms" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="工作秘密清单应制定数" class="one-line larger-title">
                        <el-input placeholder="工作秘密清单应制定数" v-model="xglist.gzmmyzds" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                      <el-form-item label="工作秘密清单实制定数" class="one-line larger-title">
                        <el-input placeholder="工作秘密清单实制定数" v-model="xglist.gzmmszds" clearable style="width: 100%;"
                          type="number" onKeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))">
                        </el-input>
                      </el-form-item>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-form>
          <el-form ref="formName" :model="xglist" :rules="rules" label-width="40px" size="mini" disabled>
            <el-form-item label="备注" prop="bz" class="one-line-textarea">
              <el-input type="textarea" v-model="xglist.bz"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">

            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>

      </div>
    </div>
  </div>
</template>
<script>
import {
  saveNdtj,
  removeNdtj,
  updateNdtj,
  getNdtjList
} from '../../../api/index'
import {
  getNdtjHistoryPage
} from '../../../api/lstz'
import {
  exportLsNdtjData
} from '../../../api/dcwj'
export default {
  components: {},
  props: {},
  data() {
    return {
      yearSelect: [],
      excelList: [],
      pdaqcp: 0, //提示信息判断
      sblxxz: [], //下拉框数据
      bmqsxqdqlList: [], //列表数据
      tableDataCopy: [], //查询备份数据
      xglist: {}, //修改与详情数据
      updateItemOld: {},
      xgdialogVisible: false, //修改弹框
      xqdialogVisible: false, //详情弹框
      formInline: {
        tzsj: new Date().getFullYear().toString()
      }, //查询区域数据
      tjlist: {
        tjnf: new Date().getFullYear().toString(),
        ysums: 0,
        ysims: 0,
        ysmms: 0,
        ysgjmms: 0,
        psums: 0,
        psims: 0,
        psmms: 0,
        psgjmms: 0,
        umzs: 0,
        imzs: 0,
        mmzs: 0,
        gjmmzs: 0,
        bgs: 0,
        jms: 0,
        fdjjms: 0,
        fdjms: 0,
        fdms: 0,
        fdzrrs: 0,
        zdjjms: 0,
        zdjms: 0,
        zdms: 0,
        zdzrrs: 0,
        jjmzrrs: 0,
        jmzrrs: 0,
        mzrrs: 0,
        dmzrrs: 0,
        jjmsqs: 0,
        jmsqs: 0,
        msqs: 0,
        dmsqs: 0,
        jjmsqxzs: 0,
        jmsqxzs: 0,
        msqxzs: 0,
        dmsqxzs: 0,
        gjmmylbs: 0,
        gjmmylbtms: 0,
        gjmmylbxzs: 0,
        gjmmylbtmxzs: 0,
        dmzds: 0,
        dmzdxzs: 0,
        dmpxcs: 0,
        zxss: 0,
        zrs: 0,
        gzmms: 0,
        gzmmyzds: 0,
        gzmmszds: 0,
        bz: '',
      }, //添加数据
      rules: {
        ysums: [{
          required: true,
          message: '请输入绝密数量',
          trigger: 'blur'
        },],
        ysims: [{
          required: true,
          message: '请输入机密数量',
          trigger: 'blur'
        },],
        ysmms: [{
          required: true,
          message: '请输入秘密数量',
          trigger: 'blur'
        },],
        psums: [{
          required: true,
          message: '请输入绝密数量',
          trigger: 'blur'
        },],
        psims: [{
          required: true,
          message: '请输入机密数量',
          trigger: 'blur'
        },],
        psmms: [{
          required: true,
          message: '请输入秘密数量',
          trigger: 'blur'
        },],
        bgs: [{
          required: true,
          message: '请输入变更数数量',
          trigger: 'blur'
        },],
        jms: [{
          required: true,
          message: '请输入解密数数量',
          trigger: 'blur'
        },],
        fdjjms: [{
          required: true,
          message: '请输入绝、机、秘数量',
          trigger: 'blur'
        },],
        fdjms: [{
          required: true,
          message: '请输入机、秘数量',
          trigger: 'blur'
        },],
        fdms: [{
          required: true,
          message: '请输入秘数量',
          trigger: 'blur'
        },],
        zdjjms: [{
          required: true,
          message: '请输入绝、机、秘数量',
          trigger: 'blur'
        },],
        zdjms: [{
          required: true,
          message: '请输入机、秘数量',
          trigger: 'blur'
        },],
        zdms: [{
          required: true,
          message: '请输入秘数量',
          trigger: 'blur'
        },],
        jjmsqs: [{
          required: true,
          message: '请输入绝、机、秘数量',
          trigger: 'blur'
        },],
        jmsqs: [{
          required: true,
          message: '请输入机、秘数量',
          trigger: 'blur'
        },],
        msqs: [{
          required: true,
          message: '请输入秘数量',
          trigger: 'blur'
        },],
        jjmsqxzs: [{
          required: true,
          message: '请输入绝、机、秘数量',
          trigger: 'blur'
        },],
        jmsqxzs: [{
          required: true,
          message: '请输入机、秘数量',
          trigger: 'blur'
        },],
        msqxzs: [{
          required: true,
          message: '请输入秘数量',
          trigger: 'blur'
        },],
        gjmmylbs: [{
          required: true,
          message: '请输入一览表数数量',
          trigger: 'blur'
        },],
        gjmmylbtms: [{
          required: true,
          message: '请输入一栏表条目数数量',
          trigger: 'blur'
        },],
        gjmmylbxzs: [{
          required: true,
          message: '请输入一览表数数量',
          trigger: 'blur'
        },],
        gjmmylbtmxzs: [{
          required: true,
          message: '请输入一栏表条目数数量',
          trigger: 'blur'
        },],
        dmzds: [{
          required: true,
          message: '请输入现在总数数量',
          trigger: 'blur'
        },],
        dmzdxzs: [{
          required: true,
          message: '请输入新制定修订数数量',
          trigger: 'blur'
        },],
        dmpxcs: [{
          required: true,
          message: '请输入培训次数',
          trigger: 'blur'
        },],
        zxss: [{
          required: true,
          message: '请输入总学时数',
          trigger: 'blur'
        },],
        zrs: [{
          required: true,
          message: '请输入总人数',
          trigger: 'blur'
        },],
        gzmms: [{
          required: true,
          message: '请输入工作秘密确定数',
          trigger: 'blur'
        },],
        gzmmyzds: [{
          required: true,
          message: '请输入工作秘密清单应制定数',
          trigger: 'blur'
        },],
        gzmmszds: [{
          required: true,
          message: '请输入工作秘密清单实制定数',
          trigger: 'blur'
        },],

      }, //校验
      page: 1, //当前页
      pageSize: 10, //每页条数
      total: 0, //总共数据数
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      activeName: 'gjmmtjqk',
      dwmc: '',
      dwdm: '',
      dwlxr: '',
      dwlxdh: '',
      year: '',
      yue: '',
      ri: '',
      Date: '',
      xh: [],
      dr_dialog: false,
      //数据导入方式
      sjdrfs: ''
    };
  },
  computed: {},
  mounted() {
    //获取最近十年的年份
    let yearArr = []
    for (let i = new Date().getFullYear(); i > new Date().getFullYear() - 10; i--) {
      yearArr.push(
        {
          label: i.toString(),
          value: i.toString()
        })
    }
    yearArr.unshift({
      label: "全部",
      value: ""
    })
    this.yearSelect = yearArr
    this.bmqsxqdqk()

  },
  methods: {
    mbxzgb() {

    },
    mbdc() {

    },

    chooseFile() { },
    //导出
    async exportList() {
      var param = {
        tjnf: this.formInline.tjnf,
        nf: this.formInline.tzsj
      }

      var returnData = await exportLsNdtjData(param);
      var date = new Date()
      var sj = date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate()
      this.dom_download(returnData, "定密情况年度统计记录信息表-" + sj + ".xls");
    },

    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          let that = this
          updateNdtj(this.xglist).then(() => {
            that.bmqsxqdqk();
          })

          // 关闭dialog
          this.$message.success("修改成功");
          this.xgdialogVisible = false;


        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    fh() {
      this.$router.go(-1)
    },
    //详情弹框
    xqyl(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row));

      this.xglist = JSON.parse(JSON.stringify(row));
      // this.form1.ywlx = row.ywlx
      console.log("old", row);
      console.log("this.xglist.ywlx", this.xglist);
      this.xqdialogVisible = true;
    },
    //修改弹框
    updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row));

      this.xglist = JSON.parse(JSON.stringify(row));
      // this.form1.ywlx = row.ywlx
      console.log("old", row);
      console.log("this.xglist.ywlx", this.xglist);
      this.xgdialogVisible = true;
    },
    //查询
    onSubmit() {
      this.page = 1
      this.bmqsxqdqk()
    },
    //查询方法
    filterFunc(val, target, filterArr) {

    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    //获取列表的值
    async bmqsxqdqk() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        tjnf: this.formInline.tjnf,
        // tznf: this.formInline.tzsj
      };
      if(this.formInline.tzsj){
        params.tznf = this.formInline.tzsj
      }
      if (this.formInline.tjnf == '') {
        params.tjnf = undefined
      }
      // Object.assign(params, this.formInline);
      let resList = await getNdtjHistoryPage(params);
      this.bmqsxqdqlList = resList.records;
      this.total = resList.total;
    },
    //删除
    shanchu(id) {
      let that = this
      if (this.selectlistRow != '') {
        this.$confirm("是否继续删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let valArr = this.selectlistRow;
            // console.log("....", val);
            valArr.forEach(function (item) {
              let params = {
                jlid: item.jlid,
                dwid: item.dwid,
              }
              removeNdtj(params).then(() => {
                that.bmqsxqdqk();
              });
              console.log("删除：", item);
              console.log("删除：", item);
            });
            // let params = valArr;
            this.$message({
              message: "删除成功",
              type: "success",
            });
          })
          .catch(() => {
            this.$message("已取消删除");
          });
      } else {
        this.$message({
          message: '未选择删除记录，请选择下列列表',
          type: 'warning'
        });
      }
    },
    //添加
    showDialog() {
      this.resetForm();
      this.dialogVisible = true;
    },
    //确定添加成员组
    submitTj(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            dwid: '111',
            dwmc: '111',
            tjnf: this.tjlist.tjnf,
            ysums: this.tjlist.ysums, //原始总数
            ysims: this.tjlist.ysims, //原始机密
            ysmms: this.tjlist.ysmms, //原始秘密
            ysgjmms: this.tjlist.ysgjmms, //原始国家秘密总数
            psums: this.tjlist.psums, //派生绝密
            psims: this.tjlist.psims, //派生机密
            psmms: this.tjlist.psmms, //派生秘密
            psgjmms: this.tjlist.psgjmms, //派生总数
            umzs: this.tjlist.umzs, //绝密总数
            imzs: this.tjlist.imzs, //机密总数
            mmzs: this.tjlist.mmzs, //秘密总数
            gjmmzs: this.tjlist.gjmmzs, //国家秘密总数
            bgs: this.tjlist.bgs, //变更数
            jms: this.tjlist.jms, //解密数
            fdjjms: this.tjlist.fdjjms, //法定绝机密数
            fdjms: this.tjlist.fdjms, //法定机密数
            fdms: this.tjlist.fdms, //法定密数
            fdzrrs: this.tjlist.fdzrrs, //法定总数
            zdjjms: this.tjlist.zdjjms, //指定绝机密
            zdjms: this.tjlist.zdjms, //指定机密
            zdms: this.tjlist.zdms, //指定密
            zdzrrs: this.tjlist.zdzrrs, //指定总数
            jjmzrrs: this.tjlist.jjmzrrs, //绝机密数
            jmzrrs: this.tjlist.jmzrrs, //机密数
            mzrrs: this.tjlist.mzrrs, //密数
            dmzrrs: this.tjlist.dmzrrs, //定密责任人总数

            jjmsqs: this.tjlist.jjmsqs, //定密授权绝机密
            jmsqs: this.tjlist.jmsqs, //定密授权机密
            msqs: this.tjlist.msqs, //定密授权密
            dmsqs: this.tjlist.dmsqs, //定密授权总数
            jjmsqxzs: this.tjlist.jjmsqxzs, //定密授权新增绝机密
            jmsqxzs: this.tjlist.jmsqxzs, //定密授权新增机密
            msqxzs: this.tjlist.msqxzs, //定密授权新增密
            dmsqxzs: this.tjlist.dmsqxzs, //定密授权新增总数
            gjmmylbs: this.tjlist.gjmmylbs, //国家秘密事项表数
            gjmmylbtms: this.tjlist.gjmmylbtms, //国家秘密事项条目数
            gjmmylbxzs: this.tjlist.gjmmylbxzs, //国家秘密事项新增表数
            gjmmylbtmxzs: this.tjlist.gjmmylbtmxzs, //国家秘密事项新增条目数
            dmzds: this.tjlist.dmzds, //定密制度数
            dmzdxzs: this.tjlist.dmzdxzs, //定密制度新增数
            dmpxcs: this.tjlist.dmpxcs, //定密培训次数
            zxss: this.tjlist.zxss, //总学时数
            zrs: this.tjlist.zrs, //总人数
            gzmms: this.tjlist.gzmms, //工作秘密确定数
            gzmmyzds: this.tjlist.gzmmyzds, //工作秘密清单应制定数
            gzmmszds: this.tjlist.gzmmszds, //工作秘密清单实制定数
            sbnf: this.tjlist.tjnf,
            bz: this.tjlist.bz,
            cjrid: '111'
            // dmqkndtjid: getUuid()
          };
          let that = this
          saveNdtj(params).then(() => {
            that.resetForm();
            that.bmqsxqdqk();
          });
          this.dialogVisible = false;

          this.$message({
            message: '添加成功',
            type: 'success'
          });



        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },

    deleteTkglBtn() { },
    //选中列表的数据
    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      this.bmqsxqdqk();
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.bmqsxqdqk();
    },
    //添加重置
    resetForm() {
      this.tjlist.ysums = 0
      this.tjlist.ysims = 0
      this.tjlist.ysmms = 0
      this.tjlist.ysgjmms = 0
      this.tjlist.psums = 0
      this.tjlist.psims = 0
      this.tjlist.psmms = 0
      this.tjlist.psgjmms = 0
      this.tjlist.umzs = 0
      this.tjlist.imzs = 0
      this.tjlist.mmzs = 0
      this.tjlist.gjmmzs = 0
      this.tjlist.bgs = 0
      this.tjlist.jms = 0
      this.tjlist.fdjjms = 0
      this.tjlist.fdjms = 0
      this.tjlist.fdms = 0
      this.tjlist.fdzrrs = 0
      this.tjlist.zdjjms = 0
      this.tjlist.zdjms = 0
      this.tjlist.zdms = 0
      this.tjlist.zdzrrs = 0
      this.tjlist.jjmzrrs = 0
      this.tjlist.jmzrrs = 0
      this.tjlist.mzrrs = 0
      this.tjlist.dmzrrs = 0
      this.tjlist.jjmsqs = 0
      this.tjlist.jmsqs = 0
      this.tjlist.msqs = 0
      this.tjlist.dmsqs = 0
      this.tjlist.jjmsqxzs = 0
      this.tjlist.jmsqxzs = 0
      this.tjlist.msqxzs = 0
      this.tjlist.dmsqxzs = 0
      this.tjlist.gjmmylbs = 0
      this.tjlist.gjmmylbtms = 0
      this.tjlist.gjmmylbxzs = 0
      this.tjlist.gjmmylbtmxzs = 0
      this.tjlist.dmzds = 0
      this.tjlist.dmzdxzs = 0
      this.tjlist.dmpxcs = 0
      this.tjlist.zxss = 0
      this.tjlist.zrs = 0
      this.tjlist.gzmms = 0
      this.tjlist.gzmmyzds = 0
      this.tjlist.gzmmszds = 0
      this.tjlist.bz = ''
    },
    handleClose(done) {
      this.resetForm();
      this.dialogVisible = false;
    },
    // 弹框关闭触发
    close(formName) {
      console.log(1);
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    //取消校验
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    jszs(index) {
      if (index == 1) {
        this.tjlist.umzs = this.tjlist.ysums * 1 + this.tjlist.psums * 1;
        this.tjlist.imzs = this.tjlist.ysims * 1 + this.tjlist.psims * 1;
        this.tjlist.mmzs = this.tjlist.ysmms * 1 + this.tjlist.psmms * 1;
        this.tjlist.ysgjmms = this.tjlist.ysums * 1 + this.tjlist.ysims * 1 + this.tjlist.ysmms * 1;
        this.tjlist.psgjmms = this.tjlist.psums * 1 + this.tjlist.psims * 1 + this.tjlist.psmms * 1;
        this.tjlist.gjmmzs = this.tjlist.psgjmms * 1 + this.tjlist.ysgjmms * 1;
      } else if (index == 2) {
        this.tjlist.jjmzrrs = this.tjlist.fdjjms * 1 + this.tjlist.zdjjms * 1;
        this.tjlist.jmzrrs = this.tjlist.fdjms * 1 + this.tjlist.zdjms * 1;
        this.tjlist.mzrrs = this.tjlist.fdms * 1 + this.tjlist.zdms * 1;
        this.tjlist.fdzrrs = this.tjlist.fdjjms * 1 + this.tjlist.fdjms * 1 + this.tjlist.fdms * 1;
        this.tjlist.zdzrrs = this.tjlist.zdjjms * 1 + this.tjlist.zdjms * 1 + this.tjlist.zdms * 1;
        this.tjlist.dmzrrs = this.tjlist.fdzrrs * 1 + this.tjlist.zdzrrs * 1;
      } else if (index == 3) {
        this.tjlist.dmsqs = this.tjlist.jjmsqs * 1 + this.tjlist.jmsqs * 1 + this.tjlist.msqs * 1;
        this.tjlist.dmsqxzs = this.tjlist.jjmsqxzs * 1 + this.tjlist.jmsqxzs * 1 + this.tjlist.msqxzs * 1;
      } else if (index == 4) {
        this.xglist.umzs = this.xglist.ysums * 1 + this.xglist.psums * 1;
        this.xglist.imzs = this.xglist.ysims * 1 + this.xglist.psims * 1;
        this.xglist.mmzs = this.xglist.ysmms * 1 + this.xglist.psmms * 1;
        this.xglist.ysgjmms = this.xglist.ysums * 1 + this.xglist.ysims * 1 + this.xglist.ysmms * 1;
        this.xglist.psgjmms = this.xglist.psums * 1 + this.xglist.psims * 1 + this.xglist.psmms * 1;
        this.xglist.gjmmzs = this.xglist.psgjmms * 1 + this.xglist.ysgjmms * 1;
      } else if (index == 5) {
        this.xglist.jjmzrrs = this.xglist.fdjjms * 1 + this.xglist.zdjjms * 1;
        this.xglist.jmzrrs = this.xglist.fdjms * 1 + this.xglist.zdjms * 1;
        this.xglist.mzrrs = this.xglist.fdms * 1 + this.xglist.zdms * 1;
        this.xglist.fdzrrs = this.xglist.fdjjms * 1 + this.xglist.fdjms * 1 + this.xglist.fdms * 1;
        this.xglist.zdzrrs = this.xglist.zdjjms * 1 + this.xglist.zdjms * 1 + this.xglist.zdms * 1;
        this.xglist.dmzrrs = this.xglist.fdzrrs * 1 + this.xglist.zdzrrs * 1;
      } else if (index == 6) {
        this.xglist.dmsqs = this.xglist.jjmsqs * 1 + this.xglist.jmsqs * 1 + this.xglist.msqs * 1;
        this.xglist.dmsqxzs = this.xglist.jjmsqxzs * 1 + this.xglist.jmsqxzs * 1 + this.xglist.msqxzs * 1;
      }
    },
  },
  watch: {},
};

</script>

<style scoped>
.bg_con {
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
} */

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.widthx {
  width: 8vw;
}

.cd {
  width: 184px;
}

.input-tou {
  margin-bottom: 10px;
}

/deep/.el-form--inline .el-form-item {
  margin-right: 9px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
</style>
