{"version": 3, "sources": ["webpack:///src/renderer/view/zczp/childPage/ccdry.vue", "webpack:///./src/renderer/view/zczp/childPage/ccdry.vue?3695", "webpack:///./src/renderer/view/zczp/childPage/ccdry.vue"], "names": ["ccdry", "data", "dialogObj", "pageInfo", "page", "pageSize", "total", "scList", "smryAllList", "dialogVisibleTjry", "rwid", "dwxx", "multipleSelection", "jcjd", "computed", "components", "mounted", "_this", "this", "$route", "query", "rwmc", "console", "log", "getDwxxList", "then", "getCcdnryListByRwid", "methods", "toXqxx", "row", "$router", "push", "path", "xm", "zw", "bm", "ryid", "djzt", "dlzt", "dj", "yc", "_this2", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "params", "wrap", "_context", "prev", "next", "dwid", "Object", "zczp", "sent", "code", "$message", "message", "type", "stop", "_this3", "_callee2", "_context2", "dwzc", "handleSelectionChange", "val", "dialogSmryQdxz", "_this4", "_callee4", "_context4", "for<PERSON>ach", "_ref", "_callee3", "item", "selection", "_context3", "sm<PERSON><PERSON>", "bmmc", "success", "warning", "_x", "apply", "arguments", "_this5", "_callee5", "ccdnryListPage", "_context5", "records", "save", "_this6", "_callee6", "updateScrwListParams", "_context6", "jczt", "error", "saveToNext", "_this7", "_callee7", "_context7", "jzsj", "moment", "Date", "abrupt", "fhsyb", "_this8", "_callee8", "_context8", "handleCurrentChange", "handleSizeChange", "tjry", "_this9", "_callee9", "_context9", "returnSy", "go", "watch", "childPage_ccdry", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "height", "width", "staticClass", "float", "attrs", "inline", "size", "_v", "_s", "icon", "on", "click", "clear", "border", "header-cell-style", "background", "color", "stripe", "label", "align", "prop", "scopedSlots", "_u", "key", "fn", "scoped", "_e", "$event", "pager-count", "current-page", "page-sizes", "page-size", "layout", "current-change", "size-change", "title", "visible", "update:visible", "ref", "selection-change", "slot", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "8MAkHAA,GACAC,KADA,WAEA,OACAC,aAGAC,UACAC,KAAA,EACAC,SAAA,GACAC,MAAA,GAEAC,UACAC,eACAC,mBAAA,EACAC,KAAA,GACAC,QACAC,qBACAC,KAAA,KAGAC,YACAC,cAEAC,QAvBA,WAuBA,IAAAC,EAAAC,KACAA,KAAAR,KAAAQ,KAAAC,OAAAC,MAAAV,KACAQ,KAAAhB,UAAAmB,KAAAH,KAAAC,OAAAC,MAAAC,KACAH,KAAAL,KAAAK,KAAAC,OAAAC,MAAAP,KACAS,QAAAC,IAAA,iBACAD,QAAAC,IAAAL,KAAAL,MACAS,QAAAC,IAAA,iBAEAL,KAAAM,cAAAC,KAAA,WACAR,EAAAS,yBAeAC,SAEAC,OAFA,SAEAC,GACAP,QAAAC,IAAAM,GACAX,KAAAY,QAAAC,MACAC,KAAA,eACAZ,OACAV,KAAAmB,EAAAnB,KACAuB,GAAAJ,EAAAI,GACAC,GAAAL,EAAAK,GACAC,GAAAN,EAAAM,GACAC,KAAAP,EAAAO,KACAC,KAAAR,EAAAQ,KACAhB,KAAAH,KAAAhB,UAAAmB,KACAR,KAAAK,KAAAL,KACAyB,KAAA,MAKAC,GApBA,SAoBAV,GACAP,QAAAC,IAAAM,GACAX,KAAAY,QAAAC,MACAC,KAAA,WACAZ,OACAV,KAAAmB,EAAAnB,KACAuB,GAAAJ,EAAAI,GACAC,GAAAL,EAAAK,GACAC,GAAAN,EAAAM,GACAC,KAAAP,EAAAO,KACAC,KAAAR,EAAAQ,KACAhB,KAAAH,KAAAhB,UAAAmB,KACAR,KAAAK,KAAAL,KACAyB,KAAA,MAKAE,GAtCA,SAsCAX,GAAA,IAAAY,EAAAvB,KAAA,OAAAwB,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAA,OAAAJ,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACA7B,QAAAC,IAAAM,GACAkB,GACArC,KAAAmB,EAAAnB,KACA0C,KAAAvB,EAAAuB,KACAhB,KAAAP,EAAAO,MALAa,EAAAE,KAAA,EAOAE,OAAAC,EAAA,EAAAD,CAAAN,GAPA,OAQA,KARAE,EAAAM,KAQAC,OACAf,EAAAgB,UACAC,QAAA,OACAC,KAAA,YAEAlB,EAAAf,uBAbA,wBAAAuB,EAAAW,SAAAd,EAAAL,KAAAC,IA0BAlB,YAhEA,WAgEA,IAAAqC,EAAA3C,KAAA,OAAAwB,IAAAC,EAAAC,EAAAC,KAAA,SAAAiB,IAAA,IAAA7D,EAAA,OAAA0C,EAAAC,EAAAI,KAAA,SAAAe,GAAA,cAAAA,EAAAb,KAAAa,EAAAZ,MAAA,cAAAY,EAAAZ,KAAA,EACAE,OAAAW,EAAA,EAAAX,GADA,OACApD,EADA8D,EAAAR,KAEAjC,QAAAC,IAAAtB,GACA4D,EAAAlD,KAAAV,EAHA,wBAAA8D,EAAAH,SAAAE,EAAAD,KAAAnB,IAKAuB,sBArEA,SAqEAC,GACAhD,KAAAN,kBAAAsD,EACA5C,QAAAC,IAAA2C,IAGAC,eA1EA,WA0EA,IAAAC,EAAAlD,KAAA,OAAAwB,IAAAC,EAAAC,EAAAC,KAAA,SAAAwB,IAAA,OAAA1B,EAAAC,EAAAI,KAAA,SAAAsB,GAAA,cAAAA,EAAApB,KAAAoB,EAAAnB,MAAA,OACAiB,EAAAxD,kBACA2D,QAAA,eAAAC,EAAA9B,IAAAC,EAAAC,EAAAC,KAAA,SAAA4B,EAAAC,GAAA,IAAA3B,EAAA4B,EAAA,OAAAhC,EAAAC,EAAAI,KAAA,SAAA4B,GAAA,cAAAA,EAAA1B,KAAA0B,EAAAzB,MAAA,cACAJ,GACArC,KAAA0D,EAAA1D,KACA0C,KAAAgB,EAAAzD,KAAAyC,KACAhB,KAAAsC,EAAAG,OACA5C,GAAAyC,EAAAzC,GACAE,GAAAuC,EAAAI,KACA5C,GAAAwC,EAAAxC,GACAG,KAAA,KARAuC,EAAAzB,KAAA,EAUAE,OAAAC,EAAA,EAAAD,CAAAN,GAVA,OAWA,MADA4B,EAVAC,EAAArB,MAWAC,MACAY,EAAA1C,sBACA0C,EAAAX,SAAAsB,QAAA,QACAX,EAAA3D,mBAAA,GACA,OAAAkE,EAAAnB,MACAY,EAAAX,SAAAuB,QAAA,UAhBA,wBAAAJ,EAAAhB,SAAAa,EAAAL,MAAA,gBAAAa,GAAA,OAAAT,EAAAU,MAAAhE,KAAAiE,YAAA,IAFA,wBAAAb,EAAAV,SAAAS,EAAAD,KAAA1B,IA6CAhB,oBAvHA,WAuHA,IAAA0D,EAAAlE,KAAA,OAAAwB,IAAAC,EAAAC,EAAAC,KAAA,SAAAwC,IAAA,IAAAtC,EAAAuC,EAAA,OAAA3C,EAAAC,EAAAI,KAAA,SAAAuC,GAAA,cAAAA,EAAArC,KAAAqC,EAAApC,MAAA,cACAJ,GACArC,KAAA0E,EAAA1E,MAEAY,QAAAC,IAAA,UAAAwB,GAJAwC,EAAApC,KAAA,EAKAE,OAAAC,EAAA,EAAAD,CAAAN,GALA,OAKAuC,EALAC,EAAAhC,KAMA6B,EAAA7E,OAAA+E,EAAAE,QACAJ,EAAAjF,SAAAG,MAAAgF,EAAAhF,MAPA,wBAAAiF,EAAA3B,SAAAyB,EAAAD,KAAA1C,IAUA+C,KAjIA,WAiIA,IAAAC,EAAAxE,KAAA,OAAAwB,IAAAC,EAAAC,EAAAC,KAAA,SAAA8C,IAAA,IAAAC,EAAA,OAAAjD,EAAAC,EAAAI,KAAA,SAAA6C,GAAA,cAAAA,EAAA3C,KAAA2C,EAAA1C,MAAA,cAEAyC,GACAlF,KAAAgF,EAAAhF,KACAoF,KAAA,aAJAD,EAAA1C,KAAA,EAMAE,OAAAC,EAAA,EAAAD,CAAAuC,GANA,OAOA,KAPAC,EAAAtC,KAOAC,KACAkC,EAAAjC,SAAAsB,QAAA,UAEAW,EAAAjC,SAAAsC,MAAA,UAVA,wBAAAF,EAAAjC,SAAA+B,EAAAD,KAAAhD,IAcAsD,WA/IA,WA+IA,IAAAC,EAAA/E,KAAA,OAAAwB,IAAAC,EAAAC,EAAAC,KAAA,SAAAqD,IAAA,IAAAN,EAAA,OAAAjD,EAAAC,EAAAI,KAAA,SAAAmD,GAAA,cAAAA,EAAAjD,KAAAiD,EAAAhD,MAAA,cAEAyC,GACAlF,KAAAuF,EAAAvF,KACAoF,KAAA,YACAM,KAAA/C,OAAAgD,EAAA,EAAAhD,CAAA,IAAAiD,OALAH,EAAAhD,KAAA,EAOAE,OAAAC,EAAA,EAAAD,CAAAuC,GAPA,UAQA,KARAO,EAAA5C,KAQAC,KARA,CAAA2C,EAAAhD,KAAA,eAUA8C,EAAAxC,SAAAsB,QAAA,QACAkB,EAAAnE,QAAAC,MACAC,KAAA,QACAZ,OAEAV,KAAAuF,EAAAvF,KACAW,KAAA4E,EAAA/F,UAAAmB,KACAR,KAAAoF,EAAApF,QAjBAsF,EAAAI,OAAA,iBAsBAN,EAAAxC,SAAAsB,QAAA,QAtBA,wBAAAoB,EAAAvC,SAAAsC,EAAAD,KAAAvD,IAwBA8D,MAvKA,WAuKA,IAAAC,EAAAvF,KAAA,OAAAwB,IAAAC,EAAAC,EAAAC,KAAA,SAAA6D,IAAA,IAAAd,EAAA,OAAAjD,EAAAC,EAAAI,KAAA,SAAA2D,GAAA,cAAAA,EAAAzD,KAAAyD,EAAAxD,MAAA,cAEAyC,GACAlF,KAAA+F,EAAA/F,MAHAiG,EAAAxD,KAAA,EAKAE,OAAAC,EAAA,EAAAD,CAAAuC,GALA,OAMA,KANAe,EAAApD,KAMAC,OACAiD,EAAAhD,SAAAsB,QAAA,eACA0B,EAAA3E,QAAAC,MACAC,KAAA,WACAZ,OAEAV,KAAA+F,EAAA/F,KACAW,KAAAoF,EAAAvG,UAAAmB,KACAR,KAAA4F,EAAA5F,SAdA,wBAAA8F,EAAA/C,SAAA8C,EAAAD,KAAA/D,IAuBAkE,oBA9LA,SA8LA1C,GACAhD,KAAAf,SAAAC,KAAA8D,EACAhD,KAAAQ,uBAGAmF,iBAnMA,SAmMA3C,GACAhD,KAAAf,SAAAC,KAAA,EACAc,KAAAf,SAAAE,SAAA6D,EACAhD,KAAAQ,uBAEAoF,KAxMA,WAwMA,IAAAC,EAAA7F,KAAA,OAAAwB,IAAAC,EAAAC,EAAAC,KAAA,SAAAmE,IAAA,IAAA/G,EAAA,OAAA0C,EAAAC,EAAAI,KAAA,SAAAiE,GAAA,cAAAA,EAAA/D,KAAA+D,EAAA9D,MAAA,cAAA8D,EAAA9D,KAAA,EACAE,OAAAC,EAAA,EAAAD,GADA,OACApD,EADAgH,EAAA1D,KAEAjC,QAAAC,IAAAtB,QACA,KAAAA,EAAAuD,OACAuD,EAAAvG,YAAAP,OACA8G,EAAAtG,mBAAA,GALA,wBAAAwG,EAAArD,SAAAoD,EAAAD,KAAArE,IASAwE,SAjNA,WAkNAhG,KAAAY,QAAAqF,IAAA,KAGAC,UCnXeC,GADEC,OAFjB,WAA0B,IAAAC,EAAArG,KAAasG,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,OAAA,OAAAC,MAAA,UAAgCJ,EAAA,OAAYK,YAAA,SAAmBL,EAAA,WAAgBK,YAAA,mBAAAH,aAA4CI,MAAA,QAAeC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,OAAAH,EAAAa,GAAA,UAAAb,EAAAc,GAAAd,EAAArH,UAAAmB,YAAA,GAAAkG,EAAAa,GAAA,KAAAV,EAAA,WAA2FK,YAAA,mBAAAH,aAA4CI,MAAA,SAAgBC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOtE,KAAA,SAAAwE,KAAA,SAAAG,KAAA,sBAA4DC,IAAKC,MAAAjB,EAAAf,SAAmBe,EAAAa,GAAA,qCAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAA6EE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOtE,KAAA,UAAAwE,KAAA,SAAAG,KAAA,wBAA+DC,IAAKC,MAAAjB,EAAAvB,cAAwBuB,EAAAa,GAAA,sCAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAA8EE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOtE,KAAA,UAAAwE,KAAA,SAAAG,KAAA,wBAA+DC,IAAKC,MAAAjB,EAAA9B,QAAkB8B,EAAAa,GAAA,oCAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAA4EE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOtE,KAAA,UAAAwE,KAAA,SAAAG,KAAA,wBAA+DC,IAAKC,MAAAjB,EAAAT,QAAkBS,EAAAa,GAAA,wCAAAb,EAAAa,GAAA,KAAAV,EAAA,OAAuEE,aAAaa,MAAA,WAAgB,GAAAlB,EAAAa,GAAA,KAAAV,EAAA,OAA4BE,aAAaC,OAAA,qCAA4CH,EAAA,YAAiBE,aAAaE,MAAA,OAAAY,OAAA,qBAA4CT,OAAQhI,KAAAsH,EAAAhH,OAAAmI,OAAA,GAAAC,qBAAmDC,WAAA,UAAAC,MAAA,WAA0ChB,OAAA,OAAAiB,OAAA,MAA8BpB,EAAA,mBAAwBO,OAAOtE,KAAA,QAAAmE,MAAA,KAAAiB,MAAA,KAAAC,MAAA,YAA2DzB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOgB,KAAA,KAAAF,MAAA,QAA0BxB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOgB,KAAA,KAAAF,MAAA,UAA4BxB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOgB,KAAA,KAAAF,MAAA,QAA0BxB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOc,MAAA,OAAAjB,MAAA,OAA6BoB,YAAA3B,EAAA4B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAkC,OAAA5B,EAAA,UAAA4B,EAAAzH,IAAAQ,KAAAqF,EAAA,QAAAH,EAAAa,GAAA,SAAAb,EAAAgC,KAAAhC,EAAAa,GAAA,QAAAkB,EAAAzH,IAAAQ,KAAAqF,EAAA,QAAAH,EAAAa,GAAA,UAAAb,EAAAgC,KAAAhC,EAAAa,GAAA,QAAAkB,EAAAzH,IAAAQ,KAAAqF,EAAA,QAAAH,EAAAa,GAAA,UAAAb,EAAAgC,cAAoOhC,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOgB,KAAA,GAAAF,MAAA,KAAAjB,MAAA,OAAqCoB,YAAA3B,EAAA4B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAkC,UAAAA,EAAAzH,IAAAQ,KAAAqF,EAAA,aAA+CO,OAAOE,KAAA,QAAAxE,KAAA,QAA6B4E,IAAKC,MAAA,SAAAgB,GAAyB,OAAAjC,EAAAhF,GAAA+G,EAAAzH,SAA4B0F,EAAAa,GAAA,QAAAb,EAAAgC,KAAAhC,EAAAa,GAAA,QAAAkB,EAAAzH,IAAAQ,KAAAqF,EAAA,aAA6EO,OAAOE,KAAA,QAAAxE,KAAA,QAA6B4E,IAAKC,MAAA,SAAAgB,GAAyB,OAAAjC,EAAAhF,GAAA+G,EAAAzH,SAA4B0F,EAAAa,GAAA,UAAAb,EAAAgC,KAAAhC,EAAAa,GAAA,QAAAkB,EAAAzH,IAAAQ,KAAAqF,EAAA,aAA+EO,OAAOE,KAAA,QAAAxE,KAAA,QAA6B4E,IAAKC,MAAA,SAAAgB,GAAyB,OAAAjC,EAAA3F,OAAA0H,EAAAzH,SAAgC0F,EAAAa,GAAA,QAAAb,EAAAgC,KAAAhC,EAAAa,GAAA,KAAAV,EAAA,aAAsDO,OAAOE,KAAA,QAAAxE,KAAA,QAA6B4E,IAAKC,MAAA,SAAAgB,GAAyB,OAAAjC,EAAA/E,GAAA8G,EAAAzH,SAA4B0F,EAAAa,GAAA,gBAAsB,OAAAb,EAAAa,GAAA,KAAAV,EAAA,OAAgCE,aAAac,OAAA,uBAA8BhB,EAAA,iBAAsBO,OAAOW,WAAA,GAAAa,cAAA,EAAAC,eAAAnC,EAAApH,SAAAC,KAAAuJ,cAAA,YAAAC,YAAArC,EAAApH,SAAAE,SAAAwJ,OAAA,yCAAAvJ,MAAAiH,EAAApH,SAAAG,OAA6MiI,IAAKuB,iBAAAvC,EAAAX,oBAAAmD,cAAAxC,EAAAV,qBAA6E,GAAAU,EAAAa,GAAA,KAAAV,EAAA,aAAkCO,OAAO+B,MAAA,OAAAC,QAAA1C,EAAA9G,kBAAAqH,MAAA,OAA6DS,IAAK2B,iBAAA,SAAAV,GAAkCjC,EAAA9G,kBAAA+I,MAA+B9B,EAAA,OAAAA,EAAA,YAA2ByC,IAAA,eAAAvC,aAAgCE,MAAA,OAAAY,OAAA,qBAA4CT,OAAQhI,KAAAsH,EAAA/G,YAAAkI,OAAA,GAAAC,qBAAwDC,WAAA,UAAAC,MAAA,WAA0ChB,OAAA,OAAAiB,OAAA,IAA6BP,IAAK6B,mBAAA7C,EAAAtD,yBAA8CyD,EAAA,mBAAwBO,OAAOtE,KAAA,YAAAmE,MAAA,QAAiCP,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOtE,KAAA,QAAAmE,MAAA,KAAAiB,MAAA,QAA0CxB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOgB,KAAA,KAAAF,MAAA,QAA0BxB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOgB,KAAA,OAAAF,MAAA,UAA8BxB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOgB,KAAA,KAAAF,MAAA,SAA0B,OAAAxB,EAAAa,GAAA,KAAAV,EAAA,QAAiCK,YAAA,gBAAAE,OAAmCoC,KAAA,UAAgBA,KAAA,WAAe3C,EAAA,aAAkBO,OAAOtE,KAAA,UAAAwE,KAAA,UAAiCI,IAAKC,MAAAjB,EAAApD,kBAA4BoD,EAAAa,GAAA,SAAAb,EAAAa,GAAA,KAAAV,EAAA,aAA8CO,OAAOtE,KAAA,UAAAwE,KAAA,UAAiCI,IAAKC,MAAA,SAAAgB,GAAyBjC,EAAA9G,mBAAA,MAAgC8G,EAAAa,GAAA,oBAE1nJkC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEzK,EACAqH,GATF,EAVA,SAAAqD,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/59.1a99911b4e089f4e3bbc.js", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%;width: 100%;\">\r\n    <!---->\r\n    <div class=\"mhcx\">\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:left\">\r\n        <el-form-item style=\"float: right;\">\r\n          <div>当前审查任务：{{ dialogObj.rwmc }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!---->\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:right\">\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"danger\" size=\"medium\" icon=\"el-icon-caret-left\" @click=\"fhsyb\">\r\n            返回上一步\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"primary\" size=\"medium\" icon=\"el-icon-document-add\" @click=\"saveToNext\">\r\n            保存至下一步\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"warning\" size=\"medium\" icon=\"el-icon-document-add\" @click=\"save\">\r\n            临时保存\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"warning\" size=\"medium\" icon=\"el-icon-document-add\" @click=\"tjry\">\r\n            添加人员\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div style=\"clear: both;\"></div>\r\n    </div>\r\n    <!-- 表格区域 -->\r\n    <div style=\"height: calc(100% - 58px - 34px - 42px);\">\r\n      <el-table :data=\"scList\" border :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\"\r\n        style=\"width: 100%;border:1px solid #EBEEF5;\" height=\"100%\" stripe>\r\n        <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"xm\" label=\"姓名\"></el-table-column>\r\n        <el-table-column prop=\"bm\" label=\"所在部门\"></el-table-column>\r\n        <el-table-column prop=\"zw\" label=\"职务\"></el-table-column>\r\n        <el-table-column label=\"登记状态\" width=\"250\">\r\n          <template slot-scope=\"scoped\">\r\n            <div>\r\n              <span v-if=\"scoped.row.djzt == 0\">待登记</span>\r\n              <span v-if=\"scoped.row.djzt == 1\">继续登记</span>\r\n              <span v-if=\"scoped.row.djzt == 2\">完成登记</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scoped\">\r\n            <el-button v-if=\"scoped.row.djzt == 0\" size=\"small\" type=\"text\" @click=\"dj(scoped.row)\">登记</el-button>\r\n            <el-button v-if=\"scoped.row.djzt == 1\" size=\"small\" type=\"text\" @click=\"dj(scoped.row)\">继续登记</el-button>\r\n            <el-button v-if=\"scoped.row.djzt == 2\" size=\"small\" type=\"text\" @click=\"toXqxx(scoped.row)\">详情</el-button>\r\n            <el-button size=\"small\" type=\"text\" @click=\"yc(scoped.row)\">移除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <!-- 分页组件区域 -->\r\n    <div style=\"border: 1px solid #ebeef5\">\r\n      <el-pagination background @current-change=\"handleCurrentChange\" @size-change=\"handleSizeChange\" :pager-count=\"5\"\r\n        :current-page=\"pageInfo.page\" :page-sizes=\"[5, 10, 20, 30]\" :page-size=\"pageInfo.pageSize\"\r\n        layout=\"total, prev, pager, sizes,next, jumper\" :total=\"pageInfo.total\">\r\n      </el-pagination>\r\n    </div>\r\n    <!---->\r\n    <!--添加人员dialog-->\r\n    <el-dialog title=\"添加人员\" :visible.sync=\"dialogVisibleTjry\" width=\"50%\">\r\n      <div>\r\n        <el-table :data=\"smryAllList\" ref=\"smryAllTable\" border @selection-change=\"handleSelectionChange\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" style=\"width: 100%;border:1px solid #EBEEF5;\"\r\n          height=\"50vh\" stripe>\r\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\"></el-table-column>\r\n          <el-table-column prop=\"xm\" label=\"姓名\"></el-table-column>\r\n          <el-table-column prop=\"bmmc\" label=\"所在部门\"></el-table-column>\r\n          <el-table-column prop=\"zw\" label=\"职务\"></el-table-column>\r\n        </el-table>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" size=\"medium\" @click=\"dialogSmryQdxz\">保 存</el-button>\r\n        <el-button type=\"warning\" size=\"medium\" @click=\"dialogVisibleTjry = false\">关 闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!---->\r\n    <!---->\r\n    <!---->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\n// 涉密人员\r\n// import { getsmry } from '../../../../db/smrydb'\r\n\r\nimport { setZczpIdsObj, getZczpIdsObj, removeZczpIdsObjField, } from '../../../../utils/windowLocation'\r\n\r\nimport { writeOptionsLog } from '../../../../utils/logUtils'\r\nimport { dateFormatNYR } from '../../../../utils/moment'\r\n\r\nimport {\r\n  getSmryList,\r\n  addZczpRyxx,\r\n  deleteZczpRyxx,\r\n  selectZczpRyxxPage,\r\n  updateJcrw\r\n} from '../../../../api/zczp'\r\nimport {\r\n  // 获取注册信息\r\n  getDwxx,\r\n} from '../../../../api/dwzc'\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogObj: {\r\n        // rwid: '93BC8D4D-2FDE-4BB9-85E5-F6214393B82E'\r\n      },\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 10,\r\n        total: 0\r\n      },\r\n      scList: [],\r\n      smryAllList: [],\r\n      dialogVisibleTjry: false,\r\n      rwid: '',\r\n      dwxx: {},\r\n      multipleSelection: [],\r\n      jcjd:'',\r\n    }\r\n  },\r\n  computed: {},\r\n  components: {\r\n  },\r\n  mounted() {\r\n    this.rwid = this.$route.query.rwid\r\n    this.dialogObj.rwmc = this.$route.query.rwmc\r\n    this.jcjd = this.$route.query.jcjd\r\n    console.log(\"=============\")\r\n    console.log(this.jcjd)\r\n    console.log(\"=============\")\r\n    // // 获取抽查的人员（分页）\r\n    this.getDwxxList().then(() => {\r\n      this.getCcdnryListByRwid()\r\n    })\r\n\r\n    // //\r\n    // // this.dialogObj.rwid = this.$route.query.rwid\r\n    // this.dialogObj.rwid = getZczpIdsObj().rwid\r\n    // //\r\n    // let scrw = selectScrwByRwid(this.dialogObj.rwid)\r\n    // console.log('scrw', scrw)\r\n    // this.dialogObj.rwmc = scrw.rwmc\r\n\r\n    // // 获取所有涉密人员\r\n    // this.smryAllList = getsmry({}).list_total\r\n    // // console.log(this.smryAllList)\r\n  },\r\n  methods: {\r\n    // 调转到详情信息页面（不可编辑的页面）\r\n    toXqxx(row) {\r\n      console.log(row);\r\n      this.$router.push({\r\n        path: '/ccdryDjXqxx',\r\n        query: {\r\n          rwid: row.rwid,\r\n          xm: row.xm,\r\n          zw: row.zw,\r\n          bm: row.bm,\r\n          ryid: row.ryid,\r\n          djzt: row.djzt,\r\n          rwmc: this.dialogObj.rwmc,\r\n          jcjd: this.jcjd,\r\n          dlzt: 1\r\n        }\r\n      })\r\n    },\r\n    // 登记\r\n    dj(row) {\r\n      console.log(row);\r\n      this.$router.push({\r\n        path: '/ccdryDj',\r\n        query: {\r\n          rwid: row.rwid,\r\n          xm: row.xm,\r\n          zw: row.zw,\r\n          bm: row.bm,\r\n          ryid: row.ryid,\r\n          djzt: row.djzt,\r\n          rwmc: this.dialogObj.rwmc,\r\n          jcjd: this.jcjd,\r\n          dlzt: 1\r\n        }\r\n      })\r\n    },\r\n    // 移除，使用[抽查的人员流水ID]移除该记录的[抽查的人员表]和[人员评分记录表]\r\n    async yc(row) {\r\n      console.log(row)\r\n      let params = {\r\n        rwid: row.rwid,\r\n        dwid: row.dwid,\r\n        ryid: row.ryid,\r\n      }\r\n      let data = await deleteZczpRyxx(params)\r\n      if (data.code == 10000) {\r\n        this.$message({\r\n          message: '删除成功',\r\n          type: 'success'\r\n        });\r\n        this.getCcdnryListByRwid()\r\n      }\r\n      // removeZczpIdsObjField(row.ccdryid)\r\n      // let bool = deleteCcdryListByID(row.ccdryid)\r\n      // if (bool) {\r\n      //   this.$message.success('移除成功')\r\n      //   // 写入操作日志\r\n      //   writeOptionsLog('yybs-ccdry', '人员' + row.xm + '登记信息移除成功', row)\r\n      //   // 刷新数据\r\n      //   this.getCcdnryListByRwid()\r\n      // }\r\n    },\r\n    // 获取单位信息集合（默认选中最后一个，也就是最新的单位信息）\r\n    async getDwxxList() {\r\n      let data = await getDwxx()\r\n      console.log(data);\r\n      this.dwxx = data\r\n    },\r\n    handleSelectionChange(val) {\r\n      this.multipleSelection = val;\r\n      console.log(val);\r\n    },\r\n    // dialog涉密人员确认选择事件触发\r\n    async dialogSmryQdxz() {\r\n      let newArr = this.multipleSelection\r\n      newArr.forEach(async (item) => {\r\n        let params = {\r\n          rwid: this.rwid,\r\n          dwid: this.dwxx.dwid,\r\n          ryid: item.smryid,\r\n          xm: item.xm,\r\n          bm: item.bmmc,\r\n          zw: item.zw,\r\n          djzt: '0',\r\n        }\r\n        let selection = await addZczpRyxx(params)\r\n        if (selection.code == 10000) {\r\n          this.getCcdnryListByRwid()\r\n          this.$message.success('添加成功')\r\n          this.dialogVisibleTjry = false\r\n        } else if (selection.code == 10001) {\r\n          this.$message.warning('人员添加重复')\r\n        }\r\n      })\r\n\r\n      // let selection = this.$refs.smryAllTable.selection\r\n      // console.log('selection', selection)\r\n      // // 加入到抽查的人员表中\r\n      // let bool = insertCcdryList(selection, this.dialogObj.rwid)\r\n      // if (bool) {\r\n      //   // 将任务状态码拨到4-抽查的人员临时保存\r\n      //   // 更新任务状态码\r\n      //   let updateScrwListParams = {\r\n      //     rwid: this.dialogObj.rwid,\r\n      //     zt: 4\r\n      //   }\r\n      //   bool = updateScrwListZt(updateScrwListParams)\r\n      //   if (bool) {\r\n      //     // 写入操作日志\r\n      //     writeOptionsLog('yybs-ccdry', '添加抽查的人员', this.dialogObj)\r\n      //     // 获取抽查的的人员\r\n      //     this.getCcdnryListByRwid()\r\n      //     this.dialogVisibleTjry = false\r\n      //   }\r\n      // }\r\n\r\n    },\r\n    // 获取任务下抽查的人员信息\r\n    async getCcdnryListByRwid() {\r\n      let params = {\r\n        rwid: this.rwid,\r\n      }\r\n      console.log('1111111', params);\r\n      let ccdnryListPage = await selectZczpRyxxPage(params)\r\n      this.scList = ccdnryListPage.records\r\n      this.pageInfo.total = ccdnryListPage.total\r\n    },\r\n    // 临时保存\r\n    async save() {\r\n      // 更新任务状态码\r\n      let updateScrwListParams = {\r\n        rwid: this.rwid,\r\n        jczt: '抽查的人员临时保存'\r\n      }\r\n      let bool = await updateJcrw(updateScrwListParams)\r\n      if (bool.code == 10000) {\r\n        this.$message.success('临时保存成功')\r\n      } else {\r\n        this.$message.error('临时保存失败')\r\n      }\r\n    },\r\n    //\r\n    async saveToNext() {\r\n      // 更新任务状态码\r\n      let updateScrwListParams = {\r\n        rwid: this.rwid,\r\n        jczt: '抽查的人员保存完成',\r\n        jzsj: dateFormatNYR(new Date())\r\n      }\r\n      let bool = await updateJcrw(updateScrwListParams)\r\n      if (bool.code == 10000) {\r\n        // 写入操作日志\r\n        this.$message.success('保存成功')\r\n        this.$router.push({\r\n          path: '/jczj',\r\n          query: {\r\n            // 任务ID\r\n            rwid: this.rwid,\r\n            rwmc: this.dialogObj.rwmc,\r\n            jcjd: this.jcjd,\r\n          }\r\n        })\r\n        return\r\n      }\r\n      this.$message.success('保存失败')\r\n    },\r\n    async fhsyb() {\r\n      // 更新任务状态码\r\n      let updateScrwListParams = {\r\n        rwid: this.rwid,\r\n      }\r\n      let bool = await updateJcrw(updateScrwListParams)\r\n      if (bool.code == 10000) {\r\n        this.$message.success('抽查的人员记录录入成功')\r\n        this.$router.push({\r\n          path: '/ccdnsjg',\r\n          query: {\r\n            // 任务ID\r\n            rwid: this.rwid,\r\n            rwmc: this.dialogObj.rwmc,\r\n            jcjd: this.jcjd\r\n          }\r\n        })\r\n      }\r\n    },\r\n    //\r\n    // handleSizeChange() { },\r\n    // handleCurrentChange(val) { },\r\n    //列表分页--跳转页数\r\n    handleCurrentChange(val) {\r\n      this.pageInfo.page = val\r\n      this.getCcdnryListByRwid()\r\n    },\r\n    //列表分页--更改每页显示个数\r\n    handleSizeChange(val) {\r\n      this.pageInfo.page = 1\r\n      this.pageInfo.pageSize = val\r\n      this.getCcdnryListByRwid()\r\n    },\r\n    async tjry() {\r\n      let data = await getSmryList()\r\n      console.log(data.data);\r\n      if (data.code == 10000) {\r\n        this.smryAllList = data.data\r\n        this.dialogVisibleTjry = true\r\n      }\r\n    },\r\n    //\r\n    returnSy() {\r\n      this.$router.go(-1)\r\n    }\r\n  },\r\n  watch: {},\r\n\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/zczp/childPage/ccdry.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"height\":\"100%\",\"width\":\"100%\"}},[_c('div',{staticClass:\"mhcx\"},[_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"left\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('div',[_vm._v(\"当前审查任务：\"+_vm._s(_vm.dialogObj.rwmc))])])],1),_vm._v(\" \"),_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"right\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"medium\",\"icon\":\"el-icon-caret-left\"},on:{\"click\":_vm.fhsyb}},[_vm._v(\"\\n          返回上一步\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\",\"icon\":\"el-icon-document-add\"},on:{\"click\":_vm.saveToNext}},[_vm._v(\"\\n          保存至下一步\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"medium\",\"icon\":\"el-icon-document-add\"},on:{\"click\":_vm.save}},[_vm._v(\"\\n          临时保存\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"medium\",\"icon\":\"el-icon-document-add\"},on:{\"click\":_vm.tjry}},[_vm._v(\"\\n          添加人员\\n        \")])],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1),_vm._v(\" \"),_c('div',{staticStyle:{\"height\":\"calc(100% - 58px - 34px - 42px)\"}},[_c('el-table',{staticStyle:{\"width\":\"100%\",\"border\":\"1px solid #EBEEF5\"},attrs:{\"data\":_vm.scList,\"border\":\"\",\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"height\":\"100%\",\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xm\",\"label\":\"姓名\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bm\",\"label\":\"所在部门\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zw\",\"label\":\"职务\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"登记状态\",\"width\":\"250\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scoped){return [_c('div',[(scoped.row.djzt == 0)?_c('span',[_vm._v(\"待登记\")]):_vm._e(),_vm._v(\" \"),(scoped.row.djzt == 1)?_c('span',[_vm._v(\"继续登记\")]):_vm._e(),_vm._v(\" \"),(scoped.row.djzt == 2)?_c('span',[_vm._v(\"完成登记\")]):_vm._e()])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"\",\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scoped){return [(scoped.row.djzt == 0)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.dj(scoped.row)}}},[_vm._v(\"登记\")]):_vm._e(),_vm._v(\" \"),(scoped.row.djzt == 1)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.dj(scoped.row)}}},[_vm._v(\"继续登记\")]):_vm._e(),_vm._v(\" \"),(scoped.row.djzt == 2)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.toXqxx(scoped.row)}}},[_vm._v(\"详情\")]):_vm._e(),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.yc(scoped.row)}}},[_vm._v(\"移除\")])]}}])})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"border\":\"1px solid #ebeef5\"}},[_c('el-pagination',{attrs:{\"background\":\"\",\"pager-count\":5,\"current-page\":_vm.pageInfo.page,\"page-sizes\":[5, 10, 20, 30],\"page-size\":_vm.pageInfo.pageSize,\"layout\":\"total, prev, pager, sizes,next, jumper\",\"total\":_vm.pageInfo.total},on:{\"current-change\":_vm.handleCurrentChange,\"size-change\":_vm.handleSizeChange}})],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"添加人员\",\"visible\":_vm.dialogVisibleTjry,\"width\":\"50%\"},on:{\"update:visible\":function($event){_vm.dialogVisibleTjry=$event}}},[_c('div',[_c('el-table',{ref:\"smryAllTable\",staticStyle:{\"width\":\"100%\",\"border\":\"1px solid #EBEEF5\"},attrs:{\"data\":_vm.smryAllList,\"border\":\"\",\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"height\":\"50vh\",\"stripe\":\"\"},on:{\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xm\",\"label\":\"姓名\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bmmc\",\"label\":\"所在部门\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zw\",\"label\":\"职务\"}})],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\"},on:{\"click\":_vm.dialogSmryQdxz}},[_vm._v(\"保 存\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"medium\"},on:{\"click\":function($event){_vm.dialogVisibleTjry = false}}},[_vm._v(\"关 闭\")])],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-c43be726\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/zczp/childPage/ccdry.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-c43be726\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ccdry.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdry.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdry.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-c43be726\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ccdry.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-c43be726\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/zczp/childPage/ccdry.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}