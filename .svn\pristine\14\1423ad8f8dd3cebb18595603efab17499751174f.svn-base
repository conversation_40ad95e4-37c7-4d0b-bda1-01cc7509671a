webpackJsonp([38],{"5aCs":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n("mvHQ"),s=n.n(i),a=n("Xxa5"),r=n.n(a),o=n("exGp"),l=n.n(o),c=n("0hE6"),m=(n("1clA"),n("l/JR")),g=function(t){return Object(m.b)(m.a+"/user/saveYhxx","post",t)},u=function(t){return Object(m.b)(m.a+"/user/deleteYhxxByIds","post",t)},d=function(t){return Object(m.b)(m.a+"/user/resetPasswordById","post",t)},p=function(t){return Object(m.b)(m.a+"/user/selectYhmXx","post",t)},f=function(t){return Object(m.b)(m.a+"/user/updateYhlxAndJs","post",t)},h=n("urfq"),x=n("gyMJ"),b=n("DQfH"),v=n("wBMR"),y=n("kCU4"),w={data:function(){var t=this;return{tjjy:!0,xmlist:[],pageInfo:{page:1,pageSize:10,total:0},xglist:{},regionOption:[],regionParams:{label:"label",value:"label",children:"childrenRegionVo",expandTrigger:"click",checkStrictly:!0},dialogVisibleSettingModify:!1,dialogVisiblejs:!1,dialogVisibleSetting:!1,settingForm:{mm:"12345678",yhm:""},yhlxlist:[{yhlxid:1,yhlxmc:"管理员"},{yhlxid:2,yhlxmc:"普通用户"}],settingFormOld:{},cszlx:1,settingList:[],pickerOptions:{disabledDate:function(e){return null!=t.selectDate&&t.selectDate.getFullYear()!=e.getFullYear()},onPick:function(e){e.minDate&&!e.maxDate?t.selectDate=e.minDate:t.selectDate=null}},jldwList:[]}},components:{hsoft_top_title:c.a},methods:{xgmm:function(){var t=this.xgmmxx;if("12345678"!=this.settingForm.mm){var e=/(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{10,16}/.test(this.settingForm.mm);if(console.log(e),1!=e)return void this.$message.error("密码必须是大写字母，小写字母，数字，特殊字符组成，且长度为10到16位！");var n={userId:t.yhid,password:this.settingForm.mm};if("root"==t.yhm)return void this.$message.warning("超级管理员账号不能修改密码");d(n);this.$message({message:t.yhm+"用户密码重置成功",type:"success"}),this.dialogVisibleSettingModify=!1}else{var i={userId:t.yhid,password:this.settingForm.mm};if("root"==t.yhm)return void this.$message.warning("超级管理员账号不能修改密码");d(i);this.$message({message:t.yhm+"用户密码重置成功",type:"success"}),this.dialogVisibleSettingModify=!1}},zzjg:function(){var t=this;return l()(r.a.mark(function e(){var n,i,s;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(x._14)();case 2:n=e.sent,console.log(n),t.zzjgmc=n,i=[],console.log(t.zzjgmc),t.zzjgmc.forEach(function(e){var n=[];t.zzjgmc.forEach(function(t){e.bmm==t.fbmm&&(n.push(t),e.childrenRegionVo=n)}),i.push(e)}),s=[],i.forEach(function(t){""==t.fbmm&&s.push(t)}),console.log(s),s[0].childrenRegionVo.forEach(function(e){t.regionOption.push(e)});case 12:case"end":return e.stop()}},e,t)}))()},bmmcxz:function(t){var e=this;if(console.log(t),!t){var n=this.$refs.cascaderArr.getCheckedNodes()[0].data;console.log(n),this.settingForm.bmid=n.bmm,this.xmmh().then(function(){var t=e.restaurantsxm;0==t.length?(e.$message.error("选择部门没有用户"),e.tjjy=!0):e.tjjy=!1,e.xmlist=t})}},xmxz:function(t){var e=this;return l()(r.a.mark(function n(){var i,s;return r.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return console.log(t),i={},i=e.xmlist.find(function(e){return e.smryid===t}),console.log(i),e.settingForm.gwmc=e.settingForm.bmmc.join("/")+"-"+i.gwmc.join(","),e.smryid=i.smryid,e.settingForm.yhm=v.a.chineseToPinYin(i.xm).toLocaleLowerCase(),(s=new FormData).append("yhm",e.settingForm.yhm),n.next=11,p(s);case 11:0!=n.sent.data.content&&e.$message.error("用户名已存在，请手动修改用户名"),e.settingForm.xm=i.xm;case 14:case"end":return n.stop()}},n,e)}))()},change:function(){this.$forceUpdate()},onInputBlur:function(){var t=this;return l()(r.a.mark(function e(){var n;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return(n=new FormData).append("yhm",t.settingForm.yhm),e.next=4,p(n);case 4:0!=e.sent.data.content&&t.$message.error("用户名已存在，请手动修改用户名");case 6:case"end":return e.stop()}},e,t)}))()},getLogin:function(){var t=this;return l()(r.a.mark(function e(){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(y.g)();case 2:t.dwxxList=e.sent;case 3:case"end":return e.stop()}},e,t)}))()},getjldw:function(){var t=this;return l()(r.a.mark(function e(){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(b.d)();case 2:t.jldwList=e.sent;case 3:case"end":return e.stop()}},e,t)}))()},showAddDialog:function(){this.tjjy=!0,this.settingForm={gwmc:"",dwid:this.dwxxList.dwid,bmid:"",mm:"12345678"},console.log(this.settingForm),this.dialogVisibleSetting=!0},showDeleDialog:function(){var t=this;this.$confirm("是否继续删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(l()(r.a.mark(function e(){var n;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=[],t.coplb.forEach(function(t){n.push(t.yhid)}),e.next=4,u(n);case 4:1e4==e.sent.code&&t.getSettingList(),t.$message({message:"删除成功",type:"success"});case 7:case"end":return e.stop()}},e,t)}))).catch(function(){t.$message("已取消删除")})},selectDatelb:function(t){console.log(t),this.coplb=t},formatTime:function(t){return Object(h.b)(new Date(t))},handleCurrentChange:function(t){this.pageInfo.page=t,this.getSettingList()},handleSizeChange:function(t){this.pageInfo.pageSize=t,this.getSettingList()},jsgl:function(t){console.log(t),this.xglist=JSON.parse(s()(t)),this.dialogVisiblejs=!0},updataSetting:function(){var t=this;return l()(r.a.mark(function e(){var n;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log(t.xglist),n={yhm:t.xglist.yhm,yhlx:t.xglist.yhlx},e.next=4,f(n);case 4:1e4==e.sent.code&&(t.$message.success("修改成功"),t.dialogVisiblejs=!1,t.getSettingList());case 6:case"end":return e.stop()}},e,t)}))()},modifySetting:function(t){this.xgmmxx=t,this.dialogVisibleSettingModify=!0,this.settingForm=JSON.parse(s()(t)),this.settingForm.mm="12345678","root"!=t.yhm||this.$message.warning("超级管理员账号不能修改密码")},modifySettingDialog:function(){var t=this;return l()(r.a.mark(function e(){var n;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return 2==(n=JSON.parse(s()(t.settingForm))).cszlx&&(n.cszDate2=void 0),3==n.cszlx&&(n.cszDate=t.settingForm.cszDate[0],n.cszDate2=t.settingForm.cszDate[1],console.log(n),console.log(" this.settingForm.cszDate",t.settingForm.cszDate)),e.next=5,Object(b.e)(n);case 5:(e.sent.code=1e4)&&t.getSettingList(),t.dialogVisibleSettingModify=!1;case 8:case"end":return e.stop()}},e,t)}))()},deleteSetting:function(t){var e=this;return l()(r.a.mark(function n(){var i;return r.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return i={settingid:t.settingid},n.next=3,Object(b.b)(i);case 3:(n.sent.code=1e4)&&e.getSettingList();case 5:case"end":return n.stop()}},n,e)}))()},getSettingList:function(){var t=this;return l()(r.a.mark(function e(){var n,i;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n={page:t.pageInfo.page,pageSize:t.pageInfo.pageSize},e.next=3,s=n,Object(m.b)(m.a+"/user/getYhxxListByPage","get",s);case 3:i=e.sent,console.log(i),t.settingList=i.records,t.settingList.forEach(function(t){1==t.yhlx?t.yhlx="管理员":t.yhlx="普通用户"}),t.pageInfo.total=i.total;case 8:case"end":return e.stop()}var s},e,t)}))()},addSetting:function(){var t=this;return l()(r.a.mark(function e(){var n,i,s;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return(n=new FormData).append("bmmc",t.settingForm.bmmc.join("/")),n.append("dwid",t.settingForm.dwid),n.append("yhm",t.settingForm.yhm),n.append("bmid",t.settingForm.bmid),n.append("yhlx",t.settingForm.yhlx),n.append("xm",t.settingForm.xm),n.append("mm",t.settingForm.mm),n.append("gwmc",t.settingForm.gwmc),n.append("smryid",t.smryid),(i=new FormData).append("yhm",t.settingForm.yhm),e.next=14,p(i);case 14:if(0==(s=e.sent).data.content){e.next=19;break}t.$message.error("用户名已存在，请手动修改用户名"),e.next=29;break;case 19:if(10013!=s.code){e.next=23;break}t.$message.error(t.settingForm.xm+"已存在"),e.next=29;break;case 23:return console.log("没有人员"),e.next=26,g(n);case 26:(e.sent.code=1e4)&&t.getSettingList(),t.dialogVisibleSetting=!1;case 29:case"end":return e.stop()}},e,t)}))()},fordw:function(t){var e=void 0;return this.jldwList.forEach(function(n){t.cszdw==n.id&&(e=n.mc)}),e},querySearchxm:function(t,e){var n=this.restaurantsxm;console.log("restaurants",n);var i=t?n.filter(this.createFilterxm(t)):n;console.log("results",i),e(i),console.log("cb(results.zw)",i)},createFilterxm:function(t){return function(e){return e.xm.toLowerCase().indexOf(t.toLowerCase())>-1}},xmmh:function(){var t=this;return l()(r.a.mark(function e(){var n,i;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n={bmid:t.settingForm.bmid},e.next=3,Object(x.x)(n);case 3:i=e.sent,console.log(i),t.restaurantsxm=i,t.restaurantszj=i,t.xmlist=i;case 8:case"end":return e.stop()}},e,t)}))()}},mounted:function(){this.zzjg(),this.getjldw(),this.getSettingList(),this.getLogin(),console.log(new Date(2022,11,1))}},F={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{height:"100%"}},[n("hsoft_top_title",{scopedSlots:t._u([{key:"left",fn:function(){return[t._v("用户管理")]},proxy:!0}])}),t._v(" "),n("div",{staticStyle:{padding:"10px 0","text-align":"right"}},[n("el-button",{attrs:{type:"success"},on:{click:t.showAddDialog}},[t._v("添加")]),t._v(" "),n("el-button",{attrs:{type:"danger"},on:{click:t.showDeleDialog}},[t._v("删除")])],1),t._v(" "),n("el-table",{staticClass:"table",staticStyle:{width:"100%",border:"1px solid #EBEEF5"},attrs:{data:t.settingList,border:"","header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},height:"calc(100% - 32px - 60px - 32px - 10px - 10px)",stripe:""},on:{select:t.selectDatelb}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),n("el-table-column",{attrs:{type:"index",width:"60",label:"序号",align:"center"}}),t._v(" "),n("el-table-column",{attrs:{prop:"bmmc",label:"所在部门"}}),t._v(" "),n("el-table-column",{attrs:{prop:"xm",label:"姓名"}}),t._v(" "),n("el-table-column",{attrs:{prop:"yhm",label:"用户名",align:"center"}}),t._v(" "),n("el-table-column",{attrs:{prop:"gwmc",label:"岗位",width:""}}),t._v(" "),n("el-table-column",{attrs:{prop:"yhlx",label:"角色"}}),t._v(" "),n("el-table-column",{attrs:{prop:"",label:"操作",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{size:"small",type:"text"},on:{click:function(n){return t.jsgl(e.row)}}},[t._v("角色管理")]),t._v(" "),n("el-button",{attrs:{size:"small",type:"text"},on:{click:function(n){return t.modifySetting(e.row)}}},[t._v("密码重置")])]}}])})],1),t._v(" "),n("el-pagination",{staticStyle:{"padding-top":"10px"},attrs:{background:"","pager-count":5,"current-page":t.pageInfo.page,"page-sizes":[5,10,20,30],"page-size":t.pageInfo.pageSize,layout:"total, prev, pager, sizes,next, jumper",total:t.pageInfo.total},on:{"current-change":t.handleCurrentChange,"size-change":t.handleSizeChange}}),t._v(" "),n("el-dialog",{attrs:{title:"添加系统用户",visible:t.dialogVisibleSetting,width:"35%"},on:{"update:visible":function(e){t.dialogVisibleSetting=e}}},[n("div",[n("el-form",{attrs:{model:t.settingForm,"label-position":"right","label-width":"120px",size:"mini"}},[n("div",{staticStyle:{display:"flex"}},[n("el-form-item",{staticClass:"one-line",attrs:{label:"所在部门"}},[n("el-cascader",{ref:"cascaderArr",staticStyle:{width:"100%"},attrs:{options:t.regionOption,props:t.regionParams,filterable:""},on:{"visible-change":function(e){return t.bmmcxz(e)}},model:{value:t.settingForm.bmmc,callback:function(e){t.$set(t.settingForm,"bmmc",e)},expression:"settingForm.bmmc"}})],1)],1),t._v(" "),n("el-form-item",{staticClass:"one-line",attrs:{label:"姓名"}},[n("el-select",{attrs:{placeholder:"请选择",disabled:t.tjjy},on:{change:function(e){return t.xmxz(e)}},model:{value:t.settingForm.xm,callback:function(e){t.$set(t.settingForm,"xm",e)},expression:"settingForm.xm"}},t._l(t.xmlist,function(t){return n("el-option",{key:t.smryid,attrs:{label:t.xm,value:t.smryid}})}),1)],1),t._v(" "),n("el-form-item",{staticClass:"one-line",attrs:{label:"角色"}},[n("el-select",{attrs:{placeholder:"请选择",disabled:t.tjjy},model:{value:t.settingForm.yhlx,callback:function(e){t.$set(t.settingForm,"yhlx",e)},expression:"settingForm.yhlx"}},t._l(t.yhlxlist,function(t){return n("el-option",{key:t.yhlxid,attrs:{label:t.yhlxmc,value:t.yhlxid}})}),1)],1),t._v(" "),n("div",{staticStyle:{display:"flex"}},[n("el-form-item",{staticClass:"one-line",attrs:{label:"岗位"}},[n("el-input",{attrs:{disabled:""},model:{value:t.settingForm.gwmc,callback:function(e){t.$set(t.settingForm,"gwmc",e)},expression:"settingForm.gwmc"}})],1)],1),t._v(" "),n("div",{staticStyle:{display:"flex"}},[n("el-form-item",{staticClass:"one-line",attrs:{label:"用户名"}},[n("el-input",{attrs:{oninput:"value=value.replace(/[^a-z0-9]/g, '')",disabled:t.tjjy},on:{blur:t.onInputBlur,input:function(e){return t.change(e)}},model:{value:t.settingForm.yhm,callback:function(e){t.$set(t.settingForm,"yhm",e)},expression:"settingForm.yhm"}})],1)],1),t._v(" "),n("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"初始密码"}},[n("el-input",{attrs:{disabled:""},model:{value:t.settingForm.mm,callback:function(e){t.$set(t.settingForm,"mm",e)},expression:"settingForm.mm"}})],1)],1)],1),t._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addSetting()}}},[t._v("保 存")]),t._v(" "),n("el-button",{attrs:{type:"warning"},on:{click:function(e){t.dialogVisibleSetting=!1}}},[t._v("关 闭")])],1)]),t._v(" "),n("el-dialog",{attrs:{title:"修改系统用户",visible:t.dialogVisiblejs,width:"35%"},on:{"update:visible":function(e){t.dialogVisiblejs=e}}},[n("div",[n("el-form",{attrs:{model:t.xglist,"label-position":"right","label-width":"120px",size:"mini"}},[n("el-form-item",{staticClass:"one-line",attrs:{label:"角色"}},[n("el-select",{attrs:{placeholder:"请选择"},model:{value:t.xglist.yhlx,callback:function(e){t.$set(t.xglist,"yhlx",e)},expression:"xglist.yhlx"}},t._l(t.yhlxlist,function(t){return n("el-option",{key:t.yhlxid,attrs:{label:t.yhlxmc,value:t.yhlxid}})}),1)],1)],1)],1),t._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.updataSetting()}}},[t._v("保 存")]),t._v(" "),n("el-button",{attrs:{type:"warning"},on:{click:function(e){t.dialogVisiblejs=!1}}},[t._v("关 闭")])],1)]),t._v(" "),n("el-dialog",{attrs:{title:"密码重置",visible:t.dialogVisibleSettingModify,width:"35%"},on:{"update:visible":function(e){t.dialogVisibleSettingModify=e}}},[n("el-form",[n("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"密码"}},[n("el-input",{attrs:{disabled:""},model:{value:t.settingForm.mm,callback:function(e){t.$set(t.settingForm,"mm",e)},expression:"settingForm.mm"}})],1)],1),t._v(" "),n("span",{staticClass:"dialog-footer",staticStyle:{"margin-top":"-40px",display:"block"},attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.xgmm()}}},[t._v("保 存")]),t._v(" "),n("el-button",{attrs:{type:"warning"},on:{click:function(e){t.dialogVisibleSettingModify=!1}}},[t._v("关 闭")])],1)],1)],1)},staticRenderFns:[]};var S=n("VU/8")(w,F,!1,function(t){n("EGLS")},"data-v-f0df5052",null);e.default=S.exports},EGLS:function(t,e){}});
//# sourceMappingURL=38.c3bd46667e8f16b0bfb7.js.map