{"version": 3, "sources": ["webpack:///./src/renderer/view/ztqk/img/img1026_1.png", "webpack:///src/renderer/view/ztqk/ckjg.vue", "webpack:///./src/renderer/view/ztqk/ckjg.vue?082f", "webpack:///./src/renderer/view/ztqk/ckjg.vue"], "names": ["module", "exports", "ckjg", "data", "activeName", "activeNames", "computed", "methods", "handleClick", "tab", "event", "console", "log", "getLoadEcharts", "this", "$echarts", "init", "document", "getElementById", "setOption", "series", "type", "radius", "label", "normal", "color", "insideColor", "textStyle", "fontSize", "fontWeight", "fontFamily", "outline", "show", "borderDistance", "itemStyle", "borderColor", "borderWidth", "backgroundStyle", "watch", "mounted", "ztqk_ckjg", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "padding", "overflow-y", "height", "staticClass", "attrs", "id", "_v", "float", "round", "model", "value", "callback", "$$v", "expression", "name", "slot", "src", "__webpack_require__", "alt", "size", "staticRenderFns", "Component", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "wCAAAA,EAAAC,QAAA,sVC+LA,IAAAC,GACAC,KADA,WAEA,OACAC,WAAA,QACAC,aAAA,mCAGAC,YACAC,SACAC,YADA,SACAC,EAAAC,GACAC,QAAAC,IAAAH,EAAAC,IAEAG,eAJA,WAKAC,KAAAC,SAAAC,KACAC,SAAAC,eAAA,WAiCAC,WA7BAC,SACAC,KAAA,aACAC,OAAA,MACAnB,MAAA,YAEAoB,OACAC,QACAC,MAAA,OACAC,YAAA,cACAC,WACAC,SAAA,GACAC,WAAA,OACAC,WAAA,qBAIAC,SACAC,MAAA,EACAC,eAAA,EACAC,WACAC,YAAA,gBACAC,YAAA,IAGAC,yBAQAC,SACAC,QAnDA,WAoDAzB,KAAAD,mBChPe2B,GADEC,OAFjB,WAA0B,IAAAC,EAAA5B,KAAa6B,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,QAAA,SAAAC,aAAA,SAAAC,OAAA,UAA0DL,EAAA,OAAYM,YAAA,QAAkBN,EAAA,OAAYM,YAAA,cAAAC,OAAiCC,GAAA,YAAeX,EAAAY,GAAA,KAAAT,EAAA,OAAwBM,YAAA,WAAqBT,EAAAY,GAAA,gBAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAAqDE,aAAaQ,MAAA,SAAgBH,OAAQ/B,KAAA,UAAAmC,MAAA,MAA6Bd,EAAAY,GAAA,UAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+CO,OAAO/B,KAAA,UAAAmC,MAAA,MAA6Bd,EAAAY,GAAA,cAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA6CM,YAAA,UAAoBN,EAAA,eAAoBY,OAAOC,MAAAhB,EAAA,YAAAiB,SAAA,SAAAC,GAAiDlB,EAAArC,YAAAuD,GAAoBC,WAAA,iBAA2BhB,EAAA,oBAAyBO,OAAOU,KAAA,OAAYjB,EAAA,YAAiBkB,KAAA,UAAalB,EAAA,OAAYO,OAAOY,IAAMC,EAAQ,QAAqBC,IAAA,MAAYxB,EAAAY,GAAA,kFAAAT,EAAA,QAAsGM,YAAA,aAAuBT,EAAAY,GAAA,kBAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAAiDM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,0FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,gBAAAZ,EAAAY,GAAA,KAAAT,EAAA,oBAA4DO,OAAOU,KAAA,OAAYjB,EAAA,YAAiBkB,KAAA,UAAalB,EAAA,OAAYO,OAAOY,IAAMC,EAAQ,QAAqBC,IAAA,MAAYxB,EAAAY,GAAA,kFAAAT,EAAA,QAAsGM,YAAA,aAAuBT,EAAAY,GAAA,kBAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAAiDM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,0FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,gBAAAZ,EAAAY,GAAA,KAAAT,EAAA,oBAA4DO,OAAOU,KAAA,OAAYjB,EAAA,YAAiBkB,KAAA,UAAalB,EAAA,OAAYO,OAAOY,IAAMC,EAAQ,QAAqBC,IAAA,MAAYxB,EAAAY,GAAA,kFAAAT,EAAA,QAAsGM,YAAA,aAAuBT,EAAAY,GAAA,kBAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAAiDM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,0FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,0FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,gBAAAZ,EAAAY,GAAA,KAAAT,EAAA,oBAA4DO,OAAOU,KAAA,OAAYjB,EAAA,YAAiBkB,KAAA,UAAalB,EAAA,OAAYO,OAAOY,IAAMC,EAAQ,QAAqBC,IAAA,MAAYxB,EAAAY,GAAA,kFAAAT,EAAA,QAAsGM,YAAA,aAAuBT,EAAAY,GAAA,kBAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAAiDM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,gBAAAZ,EAAAY,GAAA,KAAAT,EAAA,oBAA4DO,OAAOU,KAAA,OAAYjB,EAAA,YAAiBkB,KAAA,UAAalB,EAAA,OAAYO,OAAOY,IAAMC,EAAQ,QAAqBC,IAAA,MAAYxB,EAAAY,GAAA,kFAAAT,EAAA,QAAsGM,YAAA,aAAuBT,EAAAY,GAAA,kBAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAAiDM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,gBAAAZ,EAAAY,GAAA,KAAAT,EAAA,oBAA4DO,OAAOU,KAAA,OAAYjB,EAAA,YAAiBkB,KAAA,UAAalB,EAAA,OAAYO,OAAOY,IAAMC,EAAQ,QAAqBC,IAAA,MAAYxB,EAAAY,GAAA,kFAAAT,EAAA,QAAsGM,YAAA,aAAuBT,EAAAY,GAAA,kBAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAAiDM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,yFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA8HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,0FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,4FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAAiIM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,6FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAAkIM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,6FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAAkIM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,0FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,0FAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,YAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAA2CM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,gBAAAZ,EAAAY,GAAA,KAAAT,EAAA,oBAA4DO,OAAOU,KAAA,OAAYjB,EAAA,YAAiBkB,KAAA,UAAalB,EAAA,OAAYO,OAAOY,IAAMC,EAAQ,QAAqBC,IAAA,MAAYxB,EAAAY,GAAA,kFAAAT,EAAA,QAAsGM,YAAA,aAAuBT,EAAAY,GAAA,kBAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAAiDM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,gBAAAZ,EAAAY,GAAA,KAAAT,EAAA,oBAA4DO,OAAOU,KAAA,OAAYjB,EAAA,YAAiBkB,KAAA,UAAalB,EAAA,OAAYO,OAAOY,IAAMC,EAAQ,QAAqBC,IAAA,MAAYxB,EAAAY,GAAA,kFAAAT,EAAA,QAAsGM,YAAA,aAAuBT,EAAAY,GAAA,kBAAAZ,EAAAY,GAAA,KAAAT,EAAA,OAAiDM,YAAA,UAAoBN,EAAA,OAAYM,YAAA,SAAmBT,EAAAY,GAAA,wFAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6HM,YAAA,OAAAC,OAA0B/B,KAAA,UAAA8C,KAAA,OAAAX,MAAA,MAA2Cd,EAAAY,GAAA,2BAEvrRc,oBCCjB,IAcAC,EAdyBJ,EAAQ,OAcjCK,CACEpE,EACAsC,GATF,EAVA,SAAA+B,GACEN,EAAQ,SAaV,kBAEA,MAUeO,EAAA,QAAAH,EAAiB", "file": "js/22.ee837162592a488de353.js", "sourcesContent": ["module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAQCAYAAAAxtt7zAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAABESURBVBiV1c8xEYAwGMXg73EHEmqNFQxUDC6piZ+B64IDsiVbsp21V7kg0bMedYcGxVimQGiLDz8KxZjyzkUvRjES/QHVVhHbsxPiTAAAAABJRU5ErkJggg==\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/ztqk/img/img1026_1.png\n// module id = 3MME\n// module chunks = 22", "<template>\r\n  <div style=\"padding:0 20px;overflow-y: scroll;height: 76vh;\">\r\n                    <div class=\"sqt\">\r\n                        <div class=\"header-left\" id=\"echart\"> </div>\r\n                        <div class=\"sqt-sm\">扫描完成，未发现问题</div>\r\n                        <el-button style=\"float:right;\" type=\"success\" round>一键检测</el-button>\r\n                        <el-button type=\"warning\" round>全部忽略</el-button>\r\n                    </div>\r\n                    <div class=\"zdknr\">\r\n                        <el-collapse v-model=\"activeNames\">\r\n                            <el-collapse-item name=\"1\">\r\n                                <template slot=\"title\">\r\n                                    <img src=\"./img/img1026_1.png\" alt=\"\">\r\n                                    &nbsp;&nbsp;保密制度\r\n                                    <span class=\"glwtzwfx\">该类信息暂未发现问题</span>\r\n                                </template>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看保密制度管理\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                            </el-collapse-item>\r\n                            <el-collapse-item name=\"2\">\r\n                                <template slot=\"title\">\r\n                                    <img src=\"./img/img1026_1.png\" alt=\"\">\r\n                                    &nbsp;&nbsp;组织机构\r\n                                    <span class=\"glwtzwfx\">该类信息暂未发现问题</span>\r\n                                </template>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看组织机构管理\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                            </el-collapse-item>\r\n                            <el-collapse-item name=\"3\">\r\n                                <template slot=\"title\">\r\n                                    <img src=\"./img/img1026_1.png\" alt=\"\">\r\n                                    &nbsp;&nbsp;涉密人员\r\n                                    <span class=\"glwtzwfx\">该类信息暂未发现问题</span>\r\n                                </template>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看涉密岗位\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看在岗涉密人员\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看人员新增汇总\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看岗位变更\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看离岗离职\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                            </el-collapse-item>\r\n                            <el-collapse-item name=\"4\">\r\n                                <template slot=\"title\">\r\n                                    <img src=\"./img/img1026_1.png\" alt=\"\">\r\n                                    &nbsp;&nbsp;教育培训\r\n                                    <span class=\"glwtzwfx\">该类信息暂未发现问题</span>\r\n                                </template>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看培训清单\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                            </el-collapse-item>\r\n                            <el-collapse-item name=\"5\">\r\n                                <template slot=\"title\">\r\n                                    <img src=\"./img/img1026_1.png\" alt=\"\">\r\n                                    &nbsp;&nbsp;涉密场所\r\n                                    <span class=\"glwtzwfx\">该类信息暂未发现问题</span>\r\n                                </template>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看场所管理\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看场所变更\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                            </el-collapse-item>\r\n                            <el-collapse-item name=\"6\">\r\n                                <template slot=\"title\">\r\n                                    <img src=\"./img/img1026_1.png\" alt=\"\">\r\n                                    &nbsp;&nbsp;设备信息\r\n                                    <span class=\"glwtzwfx\">该类信息暂未发现问题</span>\r\n                                </template>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看涉密计算机\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看非涉密计算机\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看涉密移动存储介质\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看涉密办公自动化设备\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看非密办公自动化设备\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看涉密网络设备\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看非密网络设备\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看安全产品\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                            </el-collapse-item>\r\n                            <el-collapse-item name=\"7\">\r\n                                <template slot=\"title\">\r\n                                    <img src=\"./img/img1026_1.png\" alt=\"\">\r\n                                    &nbsp;&nbsp;涉密载体\r\n                                    <span class=\"glwtzwfx\">该类信息暂未发现问题</span>\r\n                                </template>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看载体管理\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                            </el-collapse-item>\r\n                            <el-collapse-item name=\"8\">\r\n                                <template slot=\"title\">\r\n                                    <img src=\"./img/img1026_1.png\" alt=\"\">\r\n                                    &nbsp;&nbsp;自查自评\r\n                                    <span class=\"glwtzwfx\">该类信息暂未发现问题</span>\r\n                                </template>\r\n                                <div class=\"kuang\">\r\n                                    <div class=\"bmzd\">\r\n                                        查看自查自评\r\n                                    </div>\r\n                                    <el-button class=\"ckan\" type=\"success\" size=\"mini\" round>查看</el-button>\r\n                                </div>\r\n                            </el-collapse-item>\r\n                        </el-collapse>\r\n                    </div>\r\n                </div>\r\n</template>\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                activeName: 'first',\r\n                activeNames: ['1', '2', '3', '4', '5', '6', '7', '8'],\r\n            }\r\n        },\r\n        computed: {},\r\n        methods: {\r\n            handleClick(tab, event) {\r\n                console.log(tab, event);\r\n            },\r\n            getLoadEcharts() {\r\n                var myChart = this.$echarts.init(\r\n                    document.getElementById(\"echart\")\r\n                );\r\n\r\n                var option = {\r\n                    series: [{\r\n                        type: 'liquidFill',\r\n                        radius: '90%',\r\n                        data: [1, 0.9, 0.7, 0.5],\r\n                        // color: ['#294D99', '#156ACF', '#1598ED', '#45BDFF'],\r\n                        label: {\r\n                            normal: {\r\n                                color: '#fff',\r\n                                insideColor: 'transparent',\r\n                                textStyle: {\r\n                                    fontSize: 16,\r\n                                    fontWeight: 'bold',\r\n                                    fontFamily: 'Microsoft YaHei'\r\n                                }\r\n                            }\r\n                        },\r\n                        outline: {\r\n                            show: true,\r\n                            borderDistance: 5,\r\n                            itemStyle: {\r\n                                borderColor: 'rgba(0,0,0,0)',\r\n                                borderWidth: 2\r\n                            }\r\n                        },\r\n                        backgroundStyle: {\r\n                            // color: 'rgba(67,209,100,1)' \r\n                        }\r\n                    }]\r\n                };\r\n                myChart.setOption(option);\r\n            }\r\n        },\r\n        watch: {},\r\n        mounted() {\r\n            this.getLoadEcharts()\r\n        }\r\n    };\r\n</script>\r\n<style scoped>\r\n .header-left {\r\n        width: 150px;\r\n        height: 150px;\r\n    }\r\n\r\n    .kuang {\r\n        height: 36px;\r\n        line-height: 36px;\r\n    }\r\n\r\n    .bmzd {\r\n        display: inline-block;\r\n    }\r\n\r\n    .ckan {\r\n        float: right;\r\n        margin-right: 30px;\r\n    }\r\n\r\n    .glwtzwfx {\r\n        font-size: 14px;\r\n        color: #999999;\r\n        letter-spacing: 0;\r\n        line-height: 19.6px;\r\n        font-weight: 400;\r\n        margin-left: 10%;\r\n    }\r\n\r\n    /deep/ .el-collapse-item__header {\r\n        font-size: 16px;\r\n        color: #3D3D3D;\r\n        letter-spacing: 0;\r\n        line-height: 22.4px;\r\n        font-weight: 400;\r\n        background-color: rgba(0, 0, 0, 0);\r\n    }\r\n\r\n    /deep/ .el-collapse-item__wrap {\r\n        background-color: rgba(0, 0, 0, 0);\r\n    }\r\n\r\n    .kuang:hover {\r\n        background-color: rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    /deep/ .el-collapse-item__content {\r\n        padding-top: 5px;\r\n        padding-bottom: 5px;\r\n    }\r\n\r\n    .sqt {\r\n        width: 100%;\r\n        height: 20%;\r\n        margin-top: 5px;\r\n        margin-bottom: 10px;\r\n        display: flex;\r\n        align-items: center;\r\n    }\r\n\r\n    .sqt-sm {\r\n        margin-left: 50px;\r\n        margin-right: 40%;\r\n        font-size: 28px;\r\n        color: #3D3D3D;\r\n        letter-spacing: 0;\r\n        line-height: 28px;\r\n        font-weight: 400;\r\n    }\r\n\r\n    .yjscsm {\r\n        font-size: 12px;\r\n        color: #666666;\r\n        letter-spacing: 0;\r\n        line-height: 18px;\r\n        font-weight: 400;\r\n    }\r\n\r\n    .zdwb {\r\n        width: 100%;\r\n        height: 100%;\r\n        box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.1);\r\n        /* background: #FFFFFF; */\r\n        /* overflow-y: scroll; */\r\n    }\r\n\r\n    .mk_dbgz {\r\n        width: 100%;\r\n        height: 12vw;\r\n        /* background-color: rgba(255, 255, 1, 0.5); */\r\n\r\n    }\r\n\r\n    .mk_bt {\r\n        width: 100%;\r\n        height: 3vw;\r\n        border-bottom: 1px solid rgba(216, 216, 216, 1);\r\n\r\n    }\r\n\r\n    .mk_btl {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 20px;\r\n        font-size: .9vw;\r\n        height: 100%;\r\n    }\r\n\r\n    .mk-nr {\r\n        display: flex;\r\n        align-items: center;\r\n        height: 9vw;\r\n    }\r\n\r\n    .mk-nr-div {\r\n        width: 9vw;\r\n        height: 9vw;\r\n    }\r\n\r\n    .nr-div {\r\n        margin-top: 2vw;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n\r\n    .yuan {\r\n        width: 60px;\r\n        height: 60px;\r\n        background: url(./img/img1026_18.png) no-repeat center;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n\r\n    .ym-wz {\r\n        font-size: 0.8vw;\r\n        color: #333333;\r\n        letter-spacing: 0;\r\n        text-align: center;\r\n        line-height: 19.6px;\r\n        font-weight: 400;\r\n        margin-top: .5vw;\r\n    }\r\n</style>\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/ztqk/ckjg.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"padding\":\"0 20px\",\"overflow-y\":\"scroll\",\"height\":\"76vh\"}},[_c('div',{staticClass:\"sqt\"},[_c('div',{staticClass:\"header-left\",attrs:{\"id\":\"echart\"}}),_vm._v(\" \"),_c('div',{staticClass:\"sqt-sm\"},[_vm._v(\"扫描完成，未发现问题\")]),_vm._v(\" \"),_c('el-button',{staticStyle:{\"float\":\"right\"},attrs:{\"type\":\"success\",\"round\":\"\"}},[_vm._v(\"一键检测\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\",\"round\":\"\"}},[_vm._v(\"全部忽略\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"zdknr\"},[_c('el-collapse',{model:{value:(_vm.activeNames),callback:function ($$v) {_vm.activeNames=$$v},expression:\"activeNames\"}},[_c('el-collapse-item',{attrs:{\"name\":\"1\"}},[_c('template',{slot:\"title\"},[_c('img',{attrs:{\"src\":require(\"./img/img1026_1.png\"),\"alt\":\"\"}}),_vm._v(\"\\n                                    保密制度\\n                                  \"),_c('span',{staticClass:\"glwtzwfx\"},[_vm._v(\"该类信息暂未发现问题\")])]),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看保密制度管理\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1)],2),_vm._v(\" \"),_c('el-collapse-item',{attrs:{\"name\":\"2\"}},[_c('template',{slot:\"title\"},[_c('img',{attrs:{\"src\":require(\"./img/img1026_1.png\"),\"alt\":\"\"}}),_vm._v(\"\\n                                    组织机构\\n                                  \"),_c('span',{staticClass:\"glwtzwfx\"},[_vm._v(\"该类信息暂未发现问题\")])]),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看组织机构管理\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1)],2),_vm._v(\" \"),_c('el-collapse-item',{attrs:{\"name\":\"3\"}},[_c('template',{slot:\"title\"},[_c('img',{attrs:{\"src\":require(\"./img/img1026_1.png\"),\"alt\":\"\"}}),_vm._v(\"\\n                                    涉密人员\\n                                  \"),_c('span',{staticClass:\"glwtzwfx\"},[_vm._v(\"该类信息暂未发现问题\")])]),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看涉密岗位\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看在岗涉密人员\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看人员新增汇总\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看岗位变更\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看离岗离职\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1)],2),_vm._v(\" \"),_c('el-collapse-item',{attrs:{\"name\":\"4\"}},[_c('template',{slot:\"title\"},[_c('img',{attrs:{\"src\":require(\"./img/img1026_1.png\"),\"alt\":\"\"}}),_vm._v(\"\\n                                    教育培训\\n                                  \"),_c('span',{staticClass:\"glwtzwfx\"},[_vm._v(\"该类信息暂未发现问题\")])]),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看培训清单\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1)],2),_vm._v(\" \"),_c('el-collapse-item',{attrs:{\"name\":\"5\"}},[_c('template',{slot:\"title\"},[_c('img',{attrs:{\"src\":require(\"./img/img1026_1.png\"),\"alt\":\"\"}}),_vm._v(\"\\n                                    涉密场所\\n                                  \"),_c('span',{staticClass:\"glwtzwfx\"},[_vm._v(\"该类信息暂未发现问题\")])]),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看场所管理\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看场所变更\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1)],2),_vm._v(\" \"),_c('el-collapse-item',{attrs:{\"name\":\"6\"}},[_c('template',{slot:\"title\"},[_c('img',{attrs:{\"src\":require(\"./img/img1026_1.png\"),\"alt\":\"\"}}),_vm._v(\"\\n                                    设备信息\\n                                  \"),_c('span',{staticClass:\"glwtzwfx\"},[_vm._v(\"该类信息暂未发现问题\")])]),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看涉密计算机\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看非涉密计算机\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看涉密移动存储介质\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看涉密办公自动化设备\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看非密办公自动化设备\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看涉密网络设备\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看非密网络设备\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看安全产品\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1)],2),_vm._v(\" \"),_c('el-collapse-item',{attrs:{\"name\":\"7\"}},[_c('template',{slot:\"title\"},[_c('img',{attrs:{\"src\":require(\"./img/img1026_1.png\"),\"alt\":\"\"}}),_vm._v(\"\\n                                    涉密载体\\n                                  \"),_c('span',{staticClass:\"glwtzwfx\"},[_vm._v(\"该类信息暂未发现问题\")])]),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看载体管理\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1)],2),_vm._v(\" \"),_c('el-collapse-item',{attrs:{\"name\":\"8\"}},[_c('template',{slot:\"title\"},[_c('img',{attrs:{\"src\":require(\"./img/img1026_1.png\"),\"alt\":\"\"}}),_vm._v(\"\\n                                    自查自评\\n                                  \"),_c('span',{staticClass:\"glwtzwfx\"},[_vm._v(\"该类信息暂未发现问题\")])]),_vm._v(\" \"),_c('div',{staticClass:\"kuang\"},[_c('div',{staticClass:\"bmzd\"},[_vm._v(\"\\n                                      查看自查自评\\n                                  \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"ckan\",attrs:{\"type\":\"success\",\"size\":\"mini\",\"round\":\"\"}},[_vm._v(\"查看\")])],1)],2)],1)],1)])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-13b08344\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/ztqk/ckjg.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-13b08344\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ckjg.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ckjg.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ckjg.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-13b08344\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ckjg.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-13b08344\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/ztqk/ckjg.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}