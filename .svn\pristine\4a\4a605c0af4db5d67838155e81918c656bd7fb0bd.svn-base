{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/lzlgTable.vue", "webpack:///./src/renderer/view/rcgz/lzlgTable.vue?74fc", "webpack:///./src/renderer/view/rcgz/lzlgTable.vue"], "names": ["lzlgTable", "components", "AddLineTable", "BaseTable", "baseTable", "props", "data", "table<PERSON><PERSON>", "value1", "loading", "ry<PERSON><PERSON>ose", "bm", "xm", "regionOption", "page", "pageSize", "radioIdSelect", "ryDatas", "total", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "headerCellStyle", "background", "color", "tjlist", "sm<PERSON><PERSON>", "xb", "gj", "dwzwzc", "yrsmgw", "cym", "mz", "hyzk", "zzmm", "lxdh", "sfzhm", "hjdz", "hjdgajg", "czdz", "czgajg", "imageUrl", "yjqk", "qscfqk", "qtqk", "txthsmj", "bmcnssmj", "wtssmj", "ryjbxxbsmj", "wtsfjsmj", "ldfs", "qxdw", "ryglRyscScjlList", "qssj", "zzsj", "szdw", "zw", "zmr", "czbtn1", "czbtn2", "ryglRyscJtcyList", "gxms", "jwjlqk", "cgszd", "ryglRyscYccgList", "cggj", "sy", "ryglRyscJwzzqkList", "jgmc", "zznr", "ryglRyscCfjlList", "cfdw", "cfsj", "cfjg", "cfyy", "ryglRyscSwzjList", "zjmc", "fjlb", "cyqk", "zjhm", "yxq", "qzmc", "ryInfo", "zzmmoptions", "ynoptions", "sltshow", "sltbmcnsshow", "sltwtsshow", "sltryxxshow", "sltxzglshow", "routeType", "pdfBase64", "fileList", "dialogImageUrl", "dialogVisible", "dialogBmcnsImageUrl", "dialogBmcnsVisible", "dialogWtsImageUrl", "dialogWtsVisible", "dialogRyxxImageUrl", "dialogRyxxVisible", "dialogXzglImageUrl", "dialogXzglVisible", "approvalDialogVisible", "fileRow", "filebmcnsRow", "filewtsRow", "fileryxxRow", "filexzglRow", "applyColumns", "name", "prop", "scopeType", "formatter", "handleColumnApply", "scqk", "sfty", "id", "disabled1", "disabled2", "disabled3", "ylth", "ylcn", "ylwt", "ylxz", "ylgz", "computed", "mounted", "this", "onfwid", "yhDatas", "$route", "query", "datas", "console", "log", "type", "routezt", "zt", "getOrganization", "result", "iamgeBase64", "extends_default", "zp", "<PERSON><PERSON><PERSON>", "hyzkComputed", "zzmmComputed", "jbzcComputed", "jbzc", "item", "bmC", "bmmc", "zwC", "bmzwzcComputed", "Array", "isArray", "gwmc", "length", "toString", "join", "smdjComputed", "smdj", "undefined", "csny", "substring", "csqxsfhs", "smztsfqt", "xtqxsfhs", "xxsbsfqt", "tmqssj", "push", "tmjssj", "_validDataUrl", "s", "regex", "test", "methods", "wdzlxz", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "returnData", "date", "sj", "wrap", "_context", "prev", "next", "Object", "api", "sent", "Date", "getFullYear", "getMonth", "getDate", "dom_download", "stop", "content", "fileName", "blob", "Blob", "url", "window", "URL", "createObjectURL", "dom", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "chRadio", "blobToBase64", "callback", "fileReader", "FileReader", "onload", "e", "target", "readAsDataURL", "handleSelectionChange", "index", "row", "addRow", "delRow", "rows", "splice", "cyjshgxAddRow", "ybrgx", "sfywjjwjlqcqjlxk", "dw", "cyjshgxDelRow", "yscgqkAddRow", "qsrq", "zzrq", "jsnsdgjhdq", "yscgqkDelRow", "jsjwzzqkAddRow", "gjdq", "jsjwzzqkDelRow", "clhwffzqkAddRow", "_data$push", "cljg", "clyy", "defineProperty_default", "clhwffzqkDelRow", "zpzm", "zpxx", "_validDataUrl2", "httpRequest", "_this2", "file", "dataurl", "split", "ylbmtxth", "httpBmcnsRequest", "_this3", "ylbmcns", "httpWtsRequest", "_this4", "ylwts", "httpRyxxRequest", "_this5", "ylryxx", "httpXzglRequest", "_this6", "ylxzgl", "shanchu", "_this7", "_callee2", "params", "_context2", "fwlx", "fwdyid", "save", "_this8", "_callee3", "param", "resDatas", "res", "_params", "_context3", "lcslclzt", "dwid", "lcslid", "bmid", "sgsj", "sqsj", "tmqgldw", "code", "$router", "$message", "message", "slid", "_this9", "_callee4", "zzjgList", "shu", "shuList", "list", "_context4", "zzjgmc", "for<PERSON>ach", "childrenRegionVo", "item1", "bmm", "fbmm", "handleSelectionChange1", "handleCurrentChangeRy", "val", "chooseApproval", "handleSizeChangeRy", "searchRy", "bmSelectChange", "_this10", "_callee5", "resData", "_context5", "records", "error", "saveAndSubmit", "_this11", "_callee6", "paramStatus", "_res", "_params2", "_context6", "keys_default", "clrid", "yhid", "returnIndex", "watch", "rcgz_lzlgTable", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "_v", "ref", "attrs", "model", "label-width", "placeholder", "clearable", "disabled", "$$v", "$set", "scopedSlots", "_u", "key", "fn", "scope", "staticStyle", "border-right", "padding-left", "src", "_e", "border", "slice", "header-cell-style", "stripe", "width", "align", "align-items", "range-separator", "start-placeholder", "end-placeholder", "format", "value-format", "size", "on", "position", "action", "http-request", "show-file-list", "margin-left", "visible", "update:visible", "$event", "alt", "slot", "plain", "title", "close-on-click-modal", "destroy-on-close", "for", "options", "filterable", "change", "icon", "tableHeight", "showIndex", "tableData", "columns", "showSingleSelection", "handleColumn", "showPagination", "currentPage", "totalCount", "handleCurrentChange", "handleSizeChange", "clear", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "6OAsPAA,GACAC,YACAC,uBAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAOA,OACAC,SAAA,EACAC,OAAA,GACAC,SAAA,EAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,gBACAC,KAAA,EACAC,SAAA,GACAC,cAAA,GACAC,WACAC,MAAA,EACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,iBACAC,WAAA,UACAC,MAAA,WAGAC,QACAC,OAAA,GACAjB,GAAA,GACAkB,GAAA,GACAC,GAAA,KACAC,OAAA,GACAC,OAAA,GACAC,IAAA,GACAC,GAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,QAAA,GACAC,KAAA,GACAC,OAAA,GACAC,SAAA,GACAC,KAAA,IACAC,OAAA,IACAC,KAAA,GACAC,QAAA,GACAC,SAAA,GACAC,OAAA,GACAC,WAAA,GACAC,SAAA,GACAC,KAAA,IACAC,KAAA,GACA9C,WAGA+C,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,KAGAC,mBACAC,KAAA,GACA3B,KAAA,GACA4B,OAAA,GACArD,GAAA,GACAsD,MAAA,GACAP,GAAA,GACAE,OAAA,MACAC,OAAA,KAGAK,mBACAC,KAAA,GACAC,GAAA,GACAZ,KAAA,GACAD,KAAA,GAEAK,OAAA,MACAC,OAAA,KAGAQ,qBACAb,KAAA,GACAc,KAAA,GAEAC,KAAA,GACAzC,GAAA,GACA8B,OAAA,MACAC,OAAA,KAGAW,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAhB,OAAA,MACAC,OAAA,KAGAgB,mBACAC,KAAA,gBACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,GACAC,KAAA,eAEAL,KAAA,mBACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,GACAC,KAAA,eAEAL,KAAA,iBACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,GACAC,KAAA,mBAEAL,KAAA,eACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,GACAC,KAAA,mBAEAC,UAEAC,cACAjE,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAEAC,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAEAmE,YACAlE,MAAA,IACAD,MAAA,MAEAC,MAAA,IACAD,MAAA,MAEAoE,QAAA,GACAC,aAAA,GACAC,WAAA,GACAC,YAAA,GACAC,YAAA,GACAC,UAAA,GACAC,UAAA,GACAC,YACAC,eAAA,GACAC,eAAA,EACAC,oBAAA,GACAC,oBAAA,EACAC,kBAAA,GACAC,kBAAA,EACAC,mBAAA,GACAC,mBAAA,EACAC,mBAAA,GACAC,mBAAA,EACAC,uBAAA,EACAC,QAAA,GACAC,aAAA,GACAC,WAAA,GACAC,YAAA,GACAC,YAAA,GAEAC,eACAC,KAAA,KACAC,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAC,qBACAC,OAEAC,KAAA,KACAC,GAAA,IAGAD,KAAA,MACAC,GAAA,IAGAC,WAAA,EACAC,WAAA,EACAC,WAAA,EACAC,MAAA,EACAC,MAAA,EACAC,MAAA,EACAC,MAAA,EACAC,MAAA,IAGAC,YAMAC,QA9OA,WA+OAC,KAAAC,SACAD,KAAAE,QAAAF,KAAAG,OAAAC,MAAAC,MACAC,QAAAC,IAAAP,KAAAE,SACAF,KAAA9C,OAAA8C,KAAAG,OAAAC,MAAAC,MACAL,KAAAtC,UAAAsC,KAAAG,OAAAC,MAAAI,KACAR,KAAAS,QAAAT,KAAAG,OAAAC,MAAAM,GACAJ,QAAAC,IAAAP,KAAAS,SACAT,KAAAW,kBACA,IAAAC,KACAC,EAAA,GAEAb,KAAAG,OAAAC,MAAAI,KAEAI,EAAeE,OACfd,KAAAvG,OACAuG,KAAAG,OAAAC,MAAAC,OAEAQ,EAAA,0BAAAb,KAAAG,OAAAC,MAAAC,MAAAU,GAWAH,EAAAI,QAAA,GAAAJ,EAAAjH,GAAA,OAAAiH,EAAAjH,GAAA,OACAiH,EAAAK,aAAA,GAAAL,EAAA3G,KAAA,QAAA2G,EAAA3G,KAAA,QACA2G,EAAAM,aAAA,GAAAN,EAAA1G,KAAA,UAAA0G,EAAA1G,KAAA,QAAA0G,EAAA1G,KAAA,UAAA0G,EAAA1G,KAAA,QACA0G,EAAAO,aAAA,GAAAP,EAAAQ,KAAA,SAAAR,EAAAQ,KAAA,SAAAR,EAAAQ,KAAA,SAAAR,EAAAQ,KAAA,YAAAR,EAAAQ,KAAA,gBAAAR,EAAAQ,KAAA,QAAAR,EAAAQ,KAAA,WAAAR,EAAAQ,KAAA,WAAAR,EAAAQ,KAAA,WAAAR,EAAAQ,KAAA,WAAAR,EAAAQ,KAAA,QACA,IA+CAC,EA/CAC,EAAA,IAAAV,EAAAW,KAAA,MAAAX,EAAAW,KAAA,OACAC,EAAA,IAAAZ,EAAApF,GAAA,MAAAoF,EAAApF,GAAA,OAmCA,GAlCAoF,EAAAa,eAAAH,EAAAE,EAAA,MAAAZ,EAAAO,aAEAO,MAAAC,QAAAf,EAAAgB,QACAhB,EAAAgB,KAAAhB,EAAAgB,MAAA,IAAAhB,EAAAgB,KAAAC,OAAAjB,EAAAgB,KAAAE,WAAAlB,EAAAgB,MAAAhB,EAAAgB,KAAAC,OAAA,EAAAjB,EAAAgB,KAAAG,KAAA,SAGAnB,EAAAoB,aAAA,GAAApB,EAAAqB,KAAA,QAAArB,EAAAqB,KAAA,QAAArB,EAAAqB,KAAA,QAEAjC,KAAAvG,OAAAmH,EACA,IAAAZ,KAAAvG,OAAAW,YAAA8H,GAAAlC,KAAAvG,OAAAW,MACA4F,KAAAvG,OAAA0I,KAAAvB,EAAAuB,KAEAnC,KAAAvG,OAAA0I,KAAAnC,KAAAvG,OAAAW,MAAAgI,UAAA,UAAApC,KAAAvG,OAAAW,MAAAgI,UAAA,WAAApC,KAAAvG,OAAAW,MAAAgI,UAAA,OAEA9B,QAAAC,IAAAK,EAAAyB,UACA,IAAAzB,EAAAyB,eAAAH,GAAAtB,EAAAyB,UACArC,KAAArD,iBAAA,GAAAG,KAAA,IACAkD,KAAArD,iBAAA,GAAAG,KAAA,IACAkD,KAAArD,iBAAA,GAAAG,KAAA,IACAkD,KAAArD,iBAAA,GAAAG,KAAA,MAEAkD,KAAArD,iBAAA,GAAAG,KAAA8D,EAAA0B,SAAAR,WACA9B,KAAArD,iBAAA,GAAAG,KAAA8D,EAAA2B,SAAAT,WACA9B,KAAArD,iBAAA,GAAAG,KAAA8D,EAAA4B,SAAAV,WACA9B,KAAArD,iBAAA,GAAAG,KAAA8D,EAAAyB,SAAAP,YAEA,IAAAlB,EAAA6B,aAAAP,GAAAtB,EAAA6B,SACAzC,KAAAvG,OAAApB,OAAAqK,KAAA9B,EAAA6B,QACAzC,KAAAvG,OAAApB,OAAAqK,KAAA9B,EAAA+B,SAEArC,QAAAC,IAAAP,KAAAvG,QACAuG,KAAAvG,OAAAiB,KAAAkG,EAAAlG,KAAAoH,WACA9B,KAAAvG,OAAAkB,OAAAiG,EAAAjG,OAAAmH,WAEA,iBAAAjB,EAAA,KAGA+B,EAAA,SAAAA,EAAAC,GACA,OAAAD,EAAAE,MAAAC,KAAAF,IAFA,IAAAhC,EAAA,OAMA,GAFA+B,EAAAE,MACA,6GACAF,EAAA/B,GAAA,CAGAQ,EAGAR,EALAb,KAGAvG,OAAAgB,SAAA4G,GAKA,IAAArB,KAAAvG,OAAAoB,UACAmF,KAAAP,MAAA,GAEA,IAAAO,KAAAvG,OAAAqB,WACAkF,KAAAN,MAAA,GAEA,IAAAM,KAAAvG,OAAAsB,SACAiF,KAAAL,MAAA,GAEA,IAAAK,KAAAvG,OAAAuB,aACAgF,KAAAJ,MAAA,GAEA,IAAAI,KAAAvG,OAAAwB,WACA+E,KAAAH,MAAA,IAGAmD,SACAC,OADA,WACA,IAAAC,EAAAlD,KAAA,OAAAmD,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAN,EAAAC,EAAAM,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAP,EADAI,EAAAK,KAEAR,EAAA,IAAAS,KACAR,EAAAD,EAAAU,cAAA,IAAAV,EAAAW,WAAA,GAAAX,EAAAY,UACAnB,EAAAoB,aAAAd,EAAA,QAAAE,EAAA,QAJA,wBAAAE,EAAAW,SAAAhB,EAAAL,KAAAC,IAOAmB,aARA,SAQAE,EAAAC,GACA,IAAAC,EAAA,IAAAC,MAAAH,IACAI,EAAAC,OAAAC,IAAAC,gBAAAL,GACAM,EAAAC,SAAAC,cAAA,KACAF,EAAAG,MAAAC,QAAA,OACAJ,EAAAK,KAAAT,EACAI,EAAAM,aAAA,WAAAb,GACAQ,SAAAM,KAAAC,YAAAR,GACAA,EAAAS,SAEAC,QAlBA,aAmBAC,aAnBA,SAmBAjB,EAAAkB,GACA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAA,SAAAC,GACAJ,EAAAI,EAAAC,OAAArF,SAEAiF,EAAAK,cAAAxB,IAEAyB,sBA1BA,SA0BAC,EAAAC,GACArG,KAAAnH,cAAAwN,GAGAC,OA9BA,SA8BAnO,GACAA,EAAAuK,MACArH,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,QAIA4K,OA1CA,SA0CAH,EAAAI,GACAA,EAAAC,OAAAL,EAAA,IAGAM,cA9CA,SA8CAvO,GACAA,EAAAuK,MACAiE,MAAA,GACAlO,GAAA,GACAmO,iBAAA,GACAC,GAAA,GACArL,GAAA,GACAtB,KAAA,GACAwB,OAAA,MACAC,OAAA,QAIAmL,cA3DA,SA2DAV,EAAAI,GACAA,EAAAC,OAAAL,EAAA,IAGAW,aA/DA,SA+DA5O,GACAA,EAAAuK,MACAsE,KAAA,GACAC,KAAA,GACAC,WAAA,GACAhL,GAAA,GACAR,OAAA,MACAC,OAAA,QAIAwL,aA1EA,SA0EAf,EAAAI,GACAA,EAAAC,OAAAL,EAAA,IAGAgB,eA9EA,SA8EAjP,GACAA,EAAAuK,MACAsE,KAAA,GACAK,KAAA,GACAjL,KAAA,GACAC,KAAA,GACAX,OAAA,MACAC,OAAA,QAIA2L,eAzFA,SAyFAlB,EAAAI,GACAA,EAAAC,OAAAL,EAAA,IAGAmB,gBA7FA,SA6FApP,GAAA,IAAAqP,EACArP,EAAAuK,MAAA8E,GACAR,KAAA,GACAS,KAAA,GACAC,KAAA,IAHAC,IAAAH,EAAA,OAIA,IAJAG,IAAAH,EAKA,gBALAG,IAAAH,EAMA,eANAA,KAUAI,gBAxGA,SAwGAxB,EAAAI,GACAA,EAAAC,OAAAL,EAAA,IAEAyB,KA3GA,SA2GA9G,GACA,IAAAF,EAAA,0BAAAE,EACA+G,OAAA,EACA,oBAAAjH,EAAA,KAGAkH,EAAA,SAAAA,EAAAlF,GACA,OAAAkF,EAAAjF,MAAAC,KAAAF,IAFA,IAAAhC,EAAA,OAMA,GAFAkH,EAAAjF,MACA,6GACAiF,EAAAlH,GAAA,CAKAiH,EAEAjH,GAGA,OAAAiH,GAGAE,YAnIA,SAmIA7P,GAAA,IAAA8P,EAAAjI,KACAA,KAAA3C,QAAAyH,IAAAC,gBAAA5M,EAAA+P,MACAlI,KAAAxB,QAAArG,EAAA+P,KACAlI,KAAA2F,aAAAxN,EAAA+P,KAAA,SAAAC,GACAF,EAAAxO,OAAAoB,QAAAsN,EAAAC,MAAA,QACA9H,QAAAC,IAAA0H,EAAAxO,OAAAoB,SACA,IAAAoN,EAAAxO,OAAAoB,UACAoN,EAAAxI,MAAA,MAKA4I,SA/IA,WAgJA,IAAAP,OAAA,EACAxH,QAAAC,IAAAP,KAAAtC,WACA,OAAAsC,KAAAtC,UACAsC,KAAAnC,eAAAiH,IAAAC,gBAAA/E,KAAAxB,UAEAsJ,EAAA9H,KAAA6H,KAAA7H,KAAAvG,OAAAoB,SACAmF,KAAAnC,eAAAiK,GAEA9H,KAAAlC,eAAA,GAGAwK,iBA3JA,SA2JAnQ,GAAA,IAAAoQ,EAAAvI,KACAA,KAAA1C,aAAAwH,IAAAC,gBAAA5M,EAAA+P,MACAlI,KAAAvB,aAAAtG,EAAA+P,KACAlI,KAAA2F,aAAAxN,EAAA+P,KAAA,SAAAC,GACAI,EAAA9O,OAAAqB,SAAAqN,EAAAC,MAAA,QACA9H,QAAAC,IAAAgI,EAAA9O,OAAAqB,UACA,IAAAyN,EAAA9O,OAAAqB,WACAyN,EAAA7I,MAAA,MAKA8I,QAvKA,WAwKA,IAAAV,OAAA,EACA,OAAA9H,KAAAtC,UACAsC,KAAAjC,oBAAA+G,IAAAC,gBAAA/E,KAAAvB,eAEAqJ,EAAA9H,KAAA6H,KAAA7H,KAAAvG,OAAAqB,UACAkF,KAAAjC,oBAAA+J,GAEA9H,KAAAhC,oBAAA,GAGAyK,eAlLA,SAkLAtQ,GAAA,IAAAuQ,EAAA1I,KACAA,KAAAzC,WAAAuH,IAAAC,gBAAA5M,EAAA+P,MACAlI,KAAAtB,WAAAvG,EAAA+P,KACAlI,KAAA2F,aAAAxN,EAAA+P,KAAA,SAAAC,GACAO,EAAAjP,OAAAsB,OAAAoN,EAAAC,MAAA,QACA9H,QAAAC,IAAAmI,EAAAjP,OAAAsB,QACA,IAAA2N,EAAAjP,OAAAsB,SACA2N,EAAA/I,MAAA,MAKAgJ,MA9LA,WA+LA,IAAAb,OAAA,EACAxH,QAAAC,IAAAP,KAAAtC,WACA,OAAAsC,KAAAtC,UACAsC,KAAA/B,kBAAA6G,IAAAC,gBAAA/E,KAAAtB,aAEAoJ,EAAA9H,KAAA6H,KAAA7H,KAAAvG,OAAAsB,QACAiF,KAAA/B,kBAAA6J,GAEA9H,KAAA9B,kBAAA,GAGA0K,gBA1MA,SA0MAzQ,GAAA,IAAA0Q,EAAA7I,KACAA,KAAAxC,YAAAsH,IAAAC,gBAAA5M,EAAA+P,MACAlI,KAAArB,YAAAxG,EAAA+P,KACAlI,KAAA2F,aAAAxN,EAAA+P,KAAA,SAAAC,GACAU,EAAApP,OAAAuB,WAAAmN,EAAAC,MAAA,QACA9H,QAAAC,IAAAsI,EAAApP,OAAAuB,YACA,IAAA6N,EAAApP,OAAAuB,aACA6N,EAAAjJ,MAAA,MAKAkJ,OAtNA,WAuNA,IAAAhB,OAAA,EACAxH,QAAAC,IAAAP,KAAAtC,WACA,OAAAsC,KAAAtC,UACAsC,KAAA7B,mBAAA2G,IAAAC,gBAAA/E,KAAArB,cAEAmJ,EAAA9H,KAAA6H,KAAA7H,KAAAvG,OAAAuB,YACAgF,KAAA7B,mBAAA2J,GAEA9H,KAAA5B,mBAAA,GAGA2K,gBAlOA,SAkOA5Q,GAAA,IAAA6Q,EAAAhJ,KACAA,KAAAvC,YAAAqH,IAAAC,gBAAA5M,EAAA+P,MACAlI,KAAApB,YAAAzG,EAAA+P,KACAlI,KAAA2F,aAAAxN,EAAA+P,KAAA,SAAAC,GACAa,EAAAvP,OAAAwB,SAAAkN,EAAAC,MAAA,QACA9H,QAAAC,IAAAyI,EAAAvP,OAAAwB,UACA,IAAA+N,EAAAvP,OAAAwB,WACA+N,EAAAnJ,MAAA,MAKAoJ,OA9OA,WA+OA,IAAAnB,OAAA,EACAxH,QAAAC,IAAAP,KAAAtC,WACA,OAAAsC,KAAAtC,UACAsC,KAAA3B,mBAAAyG,IAAAC,gBAAA/E,KAAApB,cAEAkJ,EAAA9H,KAAA6H,KAAA7H,KAAAvG,OAAAwB,UACA+E,KAAA3B,mBAAAyJ,GAEA9H,KAAA1B,mBAAA,GAGA4K,QA1PA,WA2PAlJ,KAAAvG,OAAAoB,QAAA,GACAmF,KAAA3C,QAAA,IAEA4C,OA9PA,WA8PA,IAAAkJ,EAAAnJ,KAAA,OAAAmD,IAAAC,EAAAC,EAAAC,KAAA,SAAA8F,IAAA,IAAAC,EAAAlR,EAAA,OAAAiL,EAAAC,EAAAM,KAAA,SAAA2F,GAAA,cAAAA,EAAAzF,KAAAyF,EAAAxF,MAAA,cACAuF,GACAE,KAAA,GAFAD,EAAAxF,KAAA,EAIAC,OAAAC,EAAA,EAAAD,CAAAsF,GAJA,OAIAlR,EAJAmR,EAAArF,KAKA3D,QAAAC,IAAApI,GACAgR,EAAAK,OAAArR,OAAAqR,OANA,wBAAAF,EAAA/E,SAAA6E,EAAAD,KAAAhG,IASAsG,KAvQA,WAuQA,IAAAC,EAAA1J,KAAA,OAAAmD,IAAAC,EAAAC,EAAAC,KAAA,SAAAqG,IAAA,IAAAC,EAAAP,EAAAQ,EAAAC,EAAAC,EAAA,OAAA3G,EAAAC,EAAAM,KAAA,SAAAqG,GAAA,cAAAA,EAAAnG,KAAAmG,EAAAlG,MAAA,UACA8F,GACAJ,OAAAE,EAAAF,OACAS,SAAA,GAUA,UAAAP,EAAAhM,UAbA,CAAAsM,EAAAlG,KAAA,YAcA4F,EAAAjQ,OAAAyQ,KAAAR,EAAAxM,OAAAgN,KACAR,EAAAjQ,OAAA0Q,OAAAT,EAAAxM,OAAAiN,OACAd,GACAc,OAAAT,EAAAjQ,OAAA0Q,OACAzQ,OAAAgQ,EAAAjQ,OAAAC,OACA0Q,KAAAV,EAAAjQ,OAAA2Q,KACA7I,KAAAmI,EAAAjQ,OAAA8H,KACA9I,GAAAiR,EAAAjQ,OAAAhB,GACAyB,KAAAwP,EAAAjQ,OAAAS,KACAiI,KAAAuH,EAAAjQ,OAAA0I,KACA3G,GAAAkO,EAAAjQ,OAAA+B,GACA4F,KAAAsI,EAAAjQ,OAAA2H,KACAjH,KAAAuP,EAAAjQ,OAAAU,KACAyH,KAAA8H,EAAAjQ,OAAAmI,KACAK,KAAAyH,EAAAjQ,OAAAwI,KACAiI,KAAAR,EAAAjQ,OAAAyQ,KACAG,KAAAX,EAAAjQ,OAAA4Q,KACAnP,KAAAwO,EAAAjQ,OAAAyB,KACAC,KAAAuO,EAAAjQ,OAAA0B,KACAmP,KAAAZ,EAAAjQ,OAAA6Q,KACAhI,SAAAoH,EAAA/M,iBAAA,GAAAG,KACAyF,SAAAmH,EAAA/M,iBAAA,GAAAG,KACA0F,SAAAkH,EAAA/M,iBAAA,GAAAG,KACAuF,SAAAqH,EAAA/M,iBAAA,GAAAG,KACA2F,OAAAiH,EAAAjQ,OAAApB,OAAA,GACAsK,OAAA+G,EAAAjQ,OAAApB,OAAA,GACAkS,QAAAb,EAAAjQ,OAAA8Q,QACA1P,QAAA6O,EAAAjQ,OAAAoB,QACAC,SAAA4O,EAAAjQ,OAAAqB,SACAC,OAAA2O,EAAAjQ,OAAAsB,OACAC,WAAA0O,EAAAjQ,OAAAuB,WACAC,SAAAyO,EAAAjQ,OAAAwB,UAEA4O,OA/CA,OAgDA3H,GAAAwH,EAAAjJ,QAhDA,CAAAuJ,EAAAlG,KAAA,gBAAAkG,EAAAlG,KAAA,EAiDAC,OAAAC,EAAA,IAAAD,CAAAsF,GAjDA,OAiDAQ,EAjDAG,EAAA/F,KAAA+F,EAAAlG,KAAA,oBAkDA,GAAA4F,EAAAjJ,QAlDA,CAAAuJ,EAAAlG,KAAA,gBAAAkG,EAAAlG,KAAA,GAmDAC,OAAAC,EAAA,IAAAD,CAAAsF,GAnDA,QAmDAQ,EAnDAG,EAAA/F,KAAA,QAqDA,KAAA4F,EAAAW,OACAd,EAAAe,QAAA/H,KAAA,SACAgH,EAAAgB,UACAC,QAAA,OACAnK,KAAA,aAzDAwJ,EAAAlG,KAAA,wBA6DA8F,EAAAlQ,OAAAgQ,EAAAxJ,QAAAxG,OACAgQ,EAAAjQ,OAAAyQ,KAAAR,EAAAxJ,QAAAgK,KA9DAF,EAAAlG,KAAA,GA+DAC,OAAAC,EAAA,EAAAD,CAAA6F,GA/DA,WAgEA,MADAE,EA/DAE,EAAA/F,MAgEAuG,KAhEA,CAAAR,EAAAlG,KAAA,gBAiEA4F,EAAAjQ,OAAA0Q,OAAAL,EAAA3R,KAAAyS,KACAb,GACAI,OAAAT,EAAAjQ,OAAA0Q,OACAzQ,OAAAgQ,EAAAjQ,OAAAC,OACA0Q,KAAAV,EAAAjQ,OAAA2Q,KACA7I,KAAAmI,EAAAjQ,OAAA8H,KACA9I,GAAAiR,EAAAjQ,OAAAhB,GACAyB,KAAAwP,EAAAjQ,OAAAS,KACAiI,KAAAuH,EAAAjQ,OAAA0I,KACA3G,GAAAkO,EAAAjQ,OAAA+B,GACA4F,KAAAsI,EAAAjQ,OAAA2H,KACAjH,KAAAuP,EAAAjQ,OAAAU,KACAyH,KAAA8H,EAAAjQ,OAAAmI,KACAK,KAAAyH,EAAAjQ,OAAAwI,KACAiI,KAAAR,EAAAjQ,OAAAyQ,KACAG,KAAAX,EAAAjQ,OAAA4Q,KACAnP,KAAAwO,EAAAjQ,OAAAyB,KACAC,KAAAuO,EAAAjQ,OAAA0B,KACAmP,KAAAZ,EAAAjQ,OAAA6Q,KACAhI,SAAAoH,EAAA/M,iBAAA,GAAAG,KACAyF,SAAAmH,EAAA/M,iBAAA,GAAAG,KACA0F,SAAAkH,EAAA/M,iBAAA,GAAAG,KACAuF,SAAAqH,EAAA/M,iBAAA,GAAAG,KACA2F,OAAAiH,EAAAjQ,OAAApB,OAAA,GACAsK,OAAA+G,EAAAjQ,OAAApB,OAAA,GACAkS,QAAAb,EAAAjQ,OAAA8Q,QACA1P,QAAA6O,EAAAjQ,OAAAoB,QACAC,SAAA4O,EAAAjQ,OAAAqB,SACAC,OAAA2O,EAAAjQ,OAAAsB,OACAC,WAAA0O,EAAAjQ,OAAAuB,WACAC,SAAAyO,EAAAjQ,OAAAwB,UA/FA+O,EAAAlG,KAAA,GAiGAC,OAAAC,EAAA,IAAAD,CAAAgG,GAjGA,QAkGA,KAlGAC,EAAA/F,KAkGAuG,MACAd,EAAAe,QAAA/H,KAAA,SACAgH,EAAAgB,UACAC,QAAA,OACAnK,KAAA,aAGAuD,OAAAC,EAAA,EAAAD,EAAA6G,KAAAd,EAAA3R,KAAAyS,OAzGA,yBAAAZ,EAAAzF,SAAAoF,EAAAD,KAAAvG,IA+GAxC,gBAtXA,WAsXA,IAAAkK,EAAA7K,KAAA,OAAAmD,IAAAC,EAAAC,EAAAC,KAAA,SAAAwH,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAA9H,EAAAC,EAAAM,KAAA,SAAAwH,GAAA,cAAAA,EAAAtH,KAAAsH,EAAArH,MAAA,cAAAqH,EAAArH,KAAA,EACAC,OAAAC,EAAA,IAAAD,GADA,cACAgH,EADAI,EAAAlH,KAEA4G,EAAAO,OAAAL,EACAC,KACAH,EAAAO,OAAAC,QAAA,SAAAhK,GACA,IAAAiK,KACAT,EAAAO,OAAAC,QAAA,SAAAE,GACAlK,EAAAmK,KAAAD,EAAAE,OACAH,EAAA5I,KAAA6I,GACAlK,EAAAiK,sBAGAN,EAAAtI,KAAArB,KAEA4J,KAdAE,EAAArH,KAAA,EAeAC,OAAAC,EAAA,EAAAD,GAfA,OAgBA,KADAmH,EAfAC,EAAAlH,MAgBAwH,MACAT,EAAAK,QAAA,SAAAhK,GACA,IAAAA,EAAAoK,MACAR,EAAAvI,KAAArB,KAIA,IAAA6J,EAAAO,MACAT,EAAAK,QAAA,SAAAhK,GACAf,QAAAC,IAAAc,GACAA,EAAAoK,MAAAP,EAAAO,MACAR,EAAAvI,KAAArB,KAIA4J,EAAA,GAAAK,iBAAAD,QAAA,SAAAhK,GACAwJ,EAAAnS,aAAAgK,KAAArB,KAhCA,yBAAA8J,EAAA5G,SAAAuG,EAAAD,KAAA1H,IAmCAuI,uBAzZA,SAyZAtF,EAAAC,GACArG,KAAAnH,cAAAwN,GAEAsF,sBA5ZA,SA4ZAC,GACA5L,KAAArH,KAAAiT,EACA5L,KAAA6L,kBAGAC,mBAjaA,SAiaAF,GACA5L,KAAArH,KAAA,EACAqH,KAAApH,SAAAgT,EACA5L,KAAA6L,kBAGAE,SAvaA,WAwaA/L,KAAA5H,WACA4H,KAAA6L,kBAGAG,eA5aA,SA4aA3K,QACAa,GAAAb,IACArB,KAAAzH,SAAAC,GAAA6I,EAAAU,KAAA,OAIA8J,eAlbA,WAkbA,IAAAI,EAAAjM,KAAA,OAAAmD,IAAAC,EAAAC,EAAAC,KAAA,SAAA4I,IAAA,IAAAtC,EAAAuC,EAAA,OAAA/I,EAAAC,EAAAM,KAAA,SAAAyI,GAAA,cAAAA,EAAAvI,KAAAuI,EAAAtI,MAAA,cAEAmI,EAAA1N,uBAAA,EACAqL,GACAjR,KAAAsT,EAAAtT,KACAC,SAAAqT,EAAArT,SACA4Q,OAAAyC,EAAAzC,OACAjI,KAAA0K,EAAA1T,SAAAC,GACAC,GAAAwT,EAAA1T,SAAAE,IARA2T,EAAAtI,KAAA,EAUAC,OAAAC,EAAA,GAAAD,CAAA6F,GAVA,QAUAuC,EAVAC,EAAAnI,MAWAoI,SAEAJ,EAAAnT,QAAAqT,EAAAE,QACAJ,EAAAlT,MAAAoT,EAAApT,OAEAkT,EAAAvB,SAAA4B,MAAA,WAhBA,wBAAAF,EAAA7H,SAAA2H,EAAAD,KAAA9I,IAoBAoJ,cAtcA,WAscA,IAAAC,EAAAxM,KAAA,OAAAmD,IAAAC,EAAAC,EAAAC,KAAA,SAAAmJ,IAAA,IAAA7C,EAAAP,EAAAqD,EAAAC,EAAAC,EAAA,OAAAxJ,EAAAC,EAAAM,KAAA,SAAAkJ,GAAA,cAAAA,EAAAhJ,KAAAgJ,EAAA/I,MAAA,YACA,IAAA0I,EAAA3T,eAAAiU,IAAAN,EAAA3T,eAAAgJ,OAAA,GADA,CAAAgL,EAAA/I,KAAA,YAEA8F,GACAJ,OAAAgD,EAAAhD,QASA,UAAAgD,EAAA9O,gBAAAwE,GAAAsK,EAAA/L,QAZA,CAAAoM,EAAA/I,KAAA,gBAaA8F,EAAAK,SAAA,EACAL,EAAAlQ,OAAA8S,EAAAtP,OAAAxD,OACAkQ,EAAAgB,KAAA4B,EAAAtP,OAAAiN,OACAP,EAAAmD,MAAAP,EAAA3T,cAAAmU,KAhBAH,EAAA/I,KAAA,EAiBAC,OAAAC,EAAA,EAAAD,CAAA6F,GAjBA,UAkBA,KAlBAiD,EAAA5I,KAkBAuG,KAlBA,CAAAqC,EAAA/I,KAAA,gBAmBA0I,EAAA/S,OAAAyQ,KAAAsC,EAAAtP,OAAAgN,KACAsC,EAAA/S,OAAA0Q,OAAAqC,EAAAtP,OAAAiN,OACAd,GACAc,OAAAqC,EAAA/S,OAAA0Q,OACAzQ,OAAA8S,EAAA/S,OAAAC,OACA0Q,KAAAoC,EAAA/S,OAAA2Q,KACA7I,KAAAiL,EAAA/S,OAAA8H,KACA9I,GAAA+T,EAAA/S,OAAAhB,GACAyB,KAAAsS,EAAA/S,OAAAS,KACAiI,KAAAqK,EAAA/S,OAAA0I,KACA3G,GAAAgR,EAAA/S,OAAA+B,GACA4F,KAAAoL,EAAA/S,OAAA2H,KACAjH,KAAAqS,EAAA/S,OAAAU,KACAyH,KAAA4K,EAAA/S,OAAAmI,KACAK,KAAAuK,EAAA/S,OAAAwI,KACAiI,KAAAsC,EAAA/S,OAAAyQ,KACAG,KAAAmC,EAAA/S,OAAA4Q,KACAnP,KAAAsR,EAAA/S,OAAAyB,KACAC,KAAAqR,EAAA/S,OAAA0B,KACAmP,KAAAkC,EAAA/S,OAAA6Q,KACAhI,SAAAkK,EAAA7P,iBAAA,GAAAG,KACAyF,SAAAiK,EAAA7P,iBAAA,GAAAG,KACA0F,SAAAgK,EAAA7P,iBAAA,GAAAG,KACAuF,SAAAmK,EAAA7P,iBAAA,GAAAG,KACA2F,OAAA+J,EAAA/S,OAAApB,OAAA,GACAsK,OAAA6J,EAAA/S,OAAApB,OAAA,GACAkS,QAAAiC,EAAA/S,OAAA8Q,QACA1P,QAAA2R,EAAA/S,OAAAoB,QACAC,SAAA0R,EAAA/S,OAAAqB,SACAC,OAAAyR,EAAA/S,OAAAsB,OACAC,WAAAwR,EAAA/S,OAAAuB,WACAC,SAAAuR,EAAA/S,OAAAwB,UAlDA4R,EAAA/I,KAAA,GAoDAC,OAAAC,EAAA,IAAAD,CAAAsF,GApDA,WAqDA,KArDAwD,EAAA5I,KAqDAuG,KArDA,CAAAqC,EAAA/I,KAAA,gBAsDA4I,GACAlD,OAAAgD,EAAAhD,OACAoB,KAAA4B,EAAA/S,OAAA0Q,aAxDA,EAAA0C,EAAA/I,KAAA,GA4DAC,OAAAC,EAAA,IAAAD,CAAA2I,GA5DA,QA+DA,KA/DAG,EAAA5I,KA+DAuG,OACAgC,EAAA/B,QAAA/H,KAAA,SACA8J,EAAA9B,UACAC,QAAA,UACAnK,KAAA,aAnEA,QAAAqM,EAAA/I,KAAA,wBAyEA8F,EAAAK,SAAA,EACAL,EAAAmD,MAAAP,EAAA3T,cAAAmU,KACApD,EAAAlQ,OAAA8S,EAAAtM,QAAAxG,OA3EAmT,EAAA/I,KAAA,GA4EAC,OAAAC,EAAA,EAAAD,CAAA6F,GA5EA,WA6EA,MADA+C,EA5EAE,EAAA5I,MA6EAuG,KA7EA,CAAAqC,EAAA/I,KAAA,gBA8EA0I,EAAA/S,OAAAyQ,KAAAsC,EAAAtM,QAAAgK,KACAsC,EAAA/S,OAAA0Q,OAAAwC,EAAAxU,KAAAyS,KACAgC,GACAzC,OAAAqC,EAAA/S,OAAA0Q,OACAzQ,OAAA8S,EAAA/S,OAAAC,OACA0Q,KAAAoC,EAAA/S,OAAA2Q,KACA7I,KAAAiL,EAAA/S,OAAA8H,KACA9I,GAAA+T,EAAA/S,OAAAhB,GACAyB,KAAAsS,EAAA/S,OAAAS,KACAiI,KAAAqK,EAAA/S,OAAA0I,KACA3G,GAAAgR,EAAA/S,OAAA+B,GACA4F,KAAAoL,EAAA/S,OAAA2H,KACAjH,KAAAqS,EAAA/S,OAAAU,KACAyH,KAAA4K,EAAA/S,OAAAmI,KACAK,KAAAuK,EAAA/S,OAAAwI,KACAiI,KAAAsC,EAAA/S,OAAAyQ,KACAG,KAAAmC,EAAA/S,OAAA4Q,KACAnP,KAAAsR,EAAA/S,OAAAyB,KACAC,KAAAqR,EAAA/S,OAAA0B,KACAmP,KAAAkC,EAAA/S,OAAA6Q,KACAhI,SAAAkK,EAAA7P,iBAAA,GAAAG,KACAyF,SAAAiK,EAAA7P,iBAAA,GAAAG,KACA0F,SAAAgK,EAAA7P,iBAAA,GAAAG,KACAuF,SAAAmK,EAAA7P,iBAAA,GAAAG,KACA2F,OAAA+J,EAAA/S,OAAApB,OAAA,GACAsK,OAAA6J,EAAA/S,OAAApB,OAAA,GACAkS,QAAAiC,EAAA/S,OAAA8Q,QACA1P,QAAA2R,EAAA/S,OAAAoB,QACAC,SAAA0R,EAAA/S,OAAAqB,SACAC,OAAAyR,EAAA/S,OAAAsB,OACAC,WAAAwR,EAAA/S,OAAAuB,WACAC,SAAAuR,EAAA/S,OAAAwB,UA7GA4R,EAAA/I,KAAA,GA+GAC,OAAAC,EAAA,IAAAD,CAAA6I,GA/GA,QAgHA,KAhHAC,EAAA5I,KAgHAuG,MACAgC,EAAA/B,QAAA/H,KAAA,SACA8J,EAAA9B,UACAC,QAAA,UACAnK,KAAA,aAGAuD,OAAAC,EAAA,EAAAD,EAAA6G,KAAA+B,EAAAxU,KAAAyS,OAvHA,QAAAiC,EAAA/I,KAAA,iBA4HA0I,EAAA9B,UACAC,QAAA,SACAnK,KAAA,YA9HA,yBAAAqM,EAAAtI,SAAAkI,EAAAD,KAAArJ,IAmIA8J,YAzkBA,WA0kBAjN,KAAAyK,QAAA/H,KAAA,WAGAwK,UCppCeC,GADEC,OAFjB,WAA0B,IAAAC,EAAArN,KAAasN,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAa5O,KAAA,UAAA6O,QAAA,YAAAzU,MAAAmU,EAAA,QAAAO,WAAA,YAA4EC,YAAA,kBAA8BL,EAAA,KAAUK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,uBAAiCL,EAAA,WAAgBO,IAAA,WAAAC,OAAsBC,MAAAZ,EAAA5T,OAAAyU,cAAA,WAA0CV,EAAA,OAAYK,YAAA,uBAAiCL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAO/U,MAAA,QAAcuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,GAAAmM,SAAA,SAAA0I,GAA+CjB,EAAAkB,KAAAlB,EAAA5T,OAAA,KAAA6U,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO/U,MAAA,QAAeuV,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,aAAAmM,SAAA,SAAA0I,GAAyDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,eAAA6U,IAA0CV,WAAA,gCAA0CP,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAO/U,MAAA,UAAgBuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAO/U,MAAA,UAAgBuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO/U,MAAA,YAAkBuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,GAAAmM,SAAA,SAAA0I,GAA+CjB,EAAAkB,KAAAlB,EAAA5T,OAAA,KAAA6U,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO/U,MAAA,UAAgBuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAO/U,MAAA,UAAgBuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO/U,MAAA,UAAgBuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,aAAAmM,SAAA,SAAA0I,GAAyDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,eAAA6U,IAA0CV,WAAA,0BAAmC,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAO/U,MAAA,gBAAsBuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,iCAA2CL,EAAA,gBAAqBQ,OAAO/U,MAAA,QAAeuV,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,OAAkBqB,aAAaC,eAAA,uBAAoCtB,EAAA,YAAiBqB,aAAaE,eAAA,QAAsBf,OAAQ/U,MAAA,KAAYgV,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,eAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAmDQ,OAAO/U,MAAA,KAAYgV,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,aAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAiDqB,aAAaE,eAAA,QAAsBf,OAAQ/U,MAAA,KAAYgV,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAgDQ,OAAO/U,MAAA,KAAYgV,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA4CQ,OAAO/U,MAAA,KAAYgV,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,mBAA0BT,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAO/U,MAAA,cAAoBuU,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,IAAgCH,OAAQ/U,MAAAmU,EAAA5T,OAAA,KAAAmM,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,OAAA6U,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,mBAA6BL,EAAA,OAAAH,EAAA5T,OAAA,SAAA+T,EAAA,OAA4CK,YAAA,SAAAG,OAA4BgB,IAAA3B,EAAA5T,OAAAgB,YAA2B4S,EAAA4B,WAAA5B,EAAAS,GAAA,KAAAN,EAAA,KAAqCK,YAAA,cAAwBR,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAuDK,YAAA,eAAAG,OAAkCkB,OAAA,GAAA/W,KAAAkV,EAAA1Q,iBAAAwS,MAAA,KAAAC,qBAAyE7V,WAAA,UAAAC,MAAA,WAA0C6V,OAAA,MAAc7B,EAAA,mBAAwBQ,OAAOxN,KAAA,QAAA8O,MAAA,KAAArW,MAAA,KAAAsW,MAAA,YAA2DlC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOjP,KAAA,OAAA9F,MAAA,UAA8BoU,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOjP,KAAA,OAAA9F,MAAA,QAA6BuV,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,OAAkBqB,aAAazJ,QAAA,OAAAoK,cAAA,YAAyChC,EAAA,YAAiBQ,OAAO/U,MAAA,KAAYgV,OAAQ/U,MAAA0V,EAAAvI,IAAA,KAAAT,SAAA,SAAA0I,GAAgDjB,EAAAkB,KAAAK,EAAAvI,IAAA,OAAAiI,IAAiCV,WAAA,oBAA8BP,EAAAS,GAAA,OAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA2CQ,OAAO/U,MAAA,KAAYgV,OAAQ/U,MAAA0V,EAAAvI,IAAA,KAAAT,SAAA,SAAA0I,GAAgDjB,EAAAkB,KAAAK,EAAAvI,IAAA,OAAAiI,IAAiCV,WAAA,oBAA8BP,EAAAS,GAAA,OAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,qBAA6D,GAAAT,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,WAAAT,EAAAS,GAAA,KAAAN,EAAA,OAA0CK,YAAA,iCAA2CL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,OAAAH,EAAAS,GAAA,aAAAN,EAAA,kBAAqDqB,aAAaC,eAAA,KAAmBd,OAAQxN,KAAA,YAAAiP,kBAAA,IAAAC,oBAAA,OAAAC,kBAAA,OAAAC,OAAA,aAAAC,eAAA,cAA+I5B,OAAQ/U,MAAAmU,EAAA5T,OAAA,OAAAmM,SAAA,SAAA0I,GAAmDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,SAAA6U,IAAoCV,WAAA,oBAA6B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,OAA4BK,YAAA,OAAAgB,aAAgCzJ,QAAA,OAAAoK,cAAA,YAAyCnC,EAAAS,GAAA,gBAAAN,EAAA,YAAwCqB,aAAaS,MAAA,QAAAR,eAAA,QAAsCb,OAAQ/U,MAAAmU,EAAA5T,OAAA,QAAAmM,SAAA,SAAA0I,GAAoDjB,EAAAkB,KAAAlB,EAAA5T,OAAA,UAAA6U,IAAqCV,WAAA,oBAA8BP,EAAAS,GAAA,gBAAAT,EAAAS,GAAA,KAAAN,EAAA,KAA6CK,YAAA,cAAwBR,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCK,YAAA,iCAA2CL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,KAAAH,EAAAS,GAAA,gBAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,qBAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,gBAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,2BAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,oBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6PQ,OAAO8B,KAAA,QAAAtP,KAAA,WAAgCuP,IAAKtK,MAAA4H,EAAApK,UAAoBoK,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAyCK,YAAA,cAAwBR,EAAAS,GAAA,WAAAT,EAAAS,GAAA,KAAAN,EAAA,OAA0CK,YAAA,8BAAAgB,aAAuDmB,SAAA,cAAuBxC,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,OAAYK,YAAA,SAAmBR,EAAAS,GAAA,+CAAAN,EAAA,aAAwEK,YAAA,cAAAG,OAAiCiC,OAAA,IAAAC,eAAA7C,EAAArF,YAAAmI,kBAAA,KAAoE3C,EAAA,aAAkBQ,OAAO8B,KAAA,OAAAtP,KAAA,aAAgC6M,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAiDE,aAAa5O,KAAA,OAAA6O,QAAA,SAAAzU,MAAAmU,EAAA,KAAAO,WAAA,SAAgEiB,aAAeuB,cAAA,QAAqBpC,OAAQ8B,KAAA,OAAAtP,KAAA,WAA+BuP,IAAKtK,MAAA4H,EAAAhF,YAAsBgF,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CQ,OAAOqC,QAAAhD,EAAAvP,eAA4BiS,IAAKO,iBAAA,SAAAC,GAAkClD,EAAAvP,cAAAyS,MAA2B/C,EAAA,OAAYqB,aAAaS,MAAA,QAAetB,OAAQgB,IAAA3B,EAAAxP,eAAA2S,IAAA,MAAmCnD,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCyC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBQ,OAAO8B,KAAA,SAAeC,IAAKtK,MAAA,SAAA8K,GAAyBlD,EAAAvP,eAAA,MAA4BuP,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkDK,YAAA,SAAmBR,EAAAS,GAAA,oDAAAN,EAAA,aAA6EK,YAAA,eAAAG,OAAkCiC,OAAA,IAAAC,eAAA7C,EAAA/E,iBAAA6H,kBAAA,KAAyE3C,EAAA,aAAkBQ,OAAO8B,KAAA,OAAAtP,KAAA,aAAgC6M,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAiDE,aAAa5O,KAAA,OAAA6O,QAAA,SAAAzU,MAAAmU,EAAA,KAAAO,WAAA,SAAgEiB,aAAeuB,cAAA,QAAqBpC,OAAQ8B,KAAA,OAAAtP,KAAA,WAA+BuP,IAAKtK,MAAA4H,EAAA7E,WAAqB6E,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CQ,OAAOqC,QAAAhD,EAAArP,oBAAiC+R,IAAKO,iBAAA,SAAAC,GAAkClD,EAAArP,mBAAAuS,MAAgC/C,EAAA,OAAYqB,aAAaS,MAAA,QAAetB,OAAQgB,IAAA3B,EAAAtP,oBAAAyS,IAAA,MAAwCnD,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCyC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBQ,OAAO8B,KAAA,SAAeC,IAAKtK,MAAA,SAAA8K,GAAyBlD,EAAArP,oBAAA,MAAiCqP,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkDK,YAAA,SAAmBR,EAAAS,GAAA,+CAAAN,EAAA,aAAwEK,YAAA,eAAAG,OAAkCiC,OAAA,IAAAC,eAAA7C,EAAA5E,eAAA0H,kBAAA,KAAuE3C,EAAA,aAAkBQ,OAAO8B,KAAA,OAAAtP,KAAA,aAAgC6M,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAiDE,aAAa5O,KAAA,OAAA6O,QAAA,SAAAzU,MAAAmU,EAAA,KAAAO,WAAA,SAAgEiB,aAAeuB,cAAA,QAAqBpC,OAAQ8B,KAAA,OAAAtP,KAAA,WAA+BuP,IAAKtK,MAAA4H,EAAA1E,SAAmB0E,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CQ,OAAOqC,QAAAhD,EAAAnP,kBAA+B6R,IAAKO,iBAAA,SAAAC,GAAkClD,EAAAnP,iBAAAqS,MAA8B/C,EAAA,OAAYqB,aAAaS,MAAA,QAAetB,OAAQgB,IAAA3B,EAAApP,kBAAAuS,IAAA,MAAsCnD,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCyC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBQ,OAAO8B,KAAA,SAAeC,IAAKtK,MAAA,SAAA8K,GAAyBlD,EAAAnP,kBAAA,MAA+BmP,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkDK,YAAA,SAAmBR,EAAAS,GAAA,0DAAAN,EAAA,aAAmFK,YAAA,eAAAG,OAAkCiC,OAAA,IAAAC,eAAA7C,EAAAzE,gBAAAuH,kBAAA,KAAwE3C,EAAA,aAAkBQ,OAAO8B,KAAA,OAAAtP,KAAA,aAAgC6M,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAiDE,aAAa5O,KAAA,OAAA6O,QAAA,SAAAzU,MAAAmU,EAAA,KAAAO,WAAA,SAAgEiB,aAAeuB,cAAA,QAAqBpC,OAAQ8B,KAAA,OAAAtP,KAAA,WAA+BuP,IAAKtK,MAAA4H,EAAAvE,UAAoBuE,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CQ,OAAOqC,QAAAhD,EAAAjP,mBAAgC2R,IAAKO,iBAAA,SAAAC,GAAkClD,EAAAjP,kBAAAmS,MAA+B/C,EAAA,OAAYqB,aAAaS,MAAA,QAAetB,OAAQgB,IAAA3B,EAAAlP,mBAAAqS,IAAA,MAAuCnD,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCyC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBQ,OAAO8B,KAAA,SAAeC,IAAKtK,MAAA,SAAA8K,GAAyBlD,EAAAjP,mBAAA,MAAgCiP,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkDK,YAAA,SAAmBR,EAAAS,GAAA,gDAAAN,EAAA,aAAyEK,YAAA,eAAAG,OAAkCiC,OAAA,IAAAC,eAAA7C,EAAAtE,gBAAAoH,kBAAA,KAAwE3C,EAAA,aAAkBQ,OAAO8B,KAAA,OAAAtP,KAAA,aAAgC6M,EAAAS,GAAA,YAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAiDE,aAAa5O,KAAA,OAAA6O,QAAA,SAAAzU,MAAAmU,EAAA,KAAAO,WAAA,SAAgEiB,aAAeuB,cAAA,QAAqBpC,OAAQ8B,KAAA,OAAAtP,KAAA,WAA+BuP,IAAKtK,MAAA4H,EAAApE,UAAoBoE,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CQ,OAAOqC,QAAAhD,EAAA/O,mBAAgCyR,IAAKO,iBAAA,SAAAC,GAAkClD,EAAA/O,kBAAAiS,MAA+B/C,EAAA,OAAYqB,aAAaS,MAAA,QAAetB,OAAQgB,IAAA3B,EAAAhP,mBAAAmS,IAAA,MAAuCnD,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCyC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBQ,OAAO8B,KAAA,SAAeC,IAAKtK,MAAA,SAAA8K,GAAyBlD,EAAA/O,mBAAA,MAAgC+O,EAAAS,GAAA,uBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAsDK,YAAA,0CAAoDL,EAAA,aAAkBK,YAAA,UAAAG,OAA6B0C,MAAA,IAAWX,IAAKtK,MAAA4H,EAAAJ,eAAyBI,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CK,YAAA,KAAAG,OAAwBxN,KAAA,WAAiBuP,IAAKtK,MAAA4H,EAAAxB,kBAA4BwB,EAAAS,GAAA,WAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAgDK,YAAA,KAAAG,OAAwBxN,KAAA,WAAiBuP,IAAKtK,MAAA4H,EAAA5D,QAAkB4D,EAAAS,GAAA,sBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA2DQ,OAAO2C,MAAA,QAAAC,wBAAA,EAAAP,QAAAhD,EAAA9O,sBAAA+Q,MAAA,MAAAuB,oBAAA,GAAuHd,IAAKO,iBAAA,SAAAC,GAAkClD,EAAA9O,sBAAAgS,MAAmC/C,EAAA,OAAYK,YAAA,oBAA8BL,EAAA,SAAcQ,OAAO8C,IAAA,MAAUzD,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,eAAgDO,IAAA,cAAAC,OAAyB+C,QAAA1D,EAAA3U,aAAAR,MAAAmV,EAAArU,aAAAgY,WAAA,GAAA5C,UAAA,IAAmF2B,IAAKkB,OAAA5D,EAAArB,gBAA4BiC,OAAQ/U,MAAAmU,EAAA9U,SAAA,GAAAqN,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA9U,SAAA,KAAA+V,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,SAA0BQ,OAAO8C,IAAA,MAAUzD,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA6CK,YAAA,SAAAG,OAA4BI,UAAA,GAAAD,YAAA,MAAkCF,OAAQ/U,MAAAmU,EAAA9U,SAAA,GAAAqN,SAAA,SAAA0I,GAAiDjB,EAAAkB,KAAAlB,EAAA9U,SAAA,KAAA+V,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,aAA8BK,YAAA,eAAAG,OAAkCxN,KAAA,UAAA0Q,KAAA,kBAAyCnB,IAAKtK,MAAA4H,EAAAtB,YAAsBsB,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CkB,IAAArB,EAAAjV,SAAAyV,YAAA,YAAAG,OAAgDmD,YAAA,MAAAC,WAAA,EAAAC,UAAAhE,EAAAvU,QAAAwY,QAAAjE,EAAAxO,aAAA0S,qBAAA,EAAAC,aAAAnE,EAAAnO,kBAAAuS,gBAAA,EAAAC,YAAArE,EAAA1U,KAAAC,SAAAyU,EAAAzU,SAAA+Y,WAAAtE,EAAAtU,OAAoPgX,IAAK6B,oBAAAvE,EAAA1B,sBAAAkG,iBAAAxE,EAAAvB,mBAAA3F,sBAAAkH,EAAAlH,0BAA6I,GAAAkH,EAAAS,GAAA,KAAAN,EAAA,QAA6BK,YAAA,gBAAAG,OAAmCyC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBK,YAAA,UAAAG,OAA6BxN,KAAA,WAAiBuP,IAAKtK,MAAA,SAAA8K,GAAyBlD,EAAA9O,uBAAA,MAAoC8O,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA8CK,YAAA,KAAAG,OAAwBxN,KAAA,WAAiBuP,IAAKtK,MAAA4H,EAAAd,iBAA2Bc,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCqB,aAAaiD,MAAA,WAAgB,UAEn8bC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEra,EACAsV,GATF,EAVA,SAAAgF,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/69.9e08b7658ada582ce2b9.js", "sourcesContent": ["<template>\r\n  <div class=\"sec-container\" v-loading=\"loading\">\r\n    <!-- 标题 -->\r\n    <p class=\"sec-title\">基本信息</p>\r\n    <div class=\"sec-form-container\">\r\n      <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n        <!-- 第一部分包括姓名到常住地公安start -->\r\n        <div class=\"sec-header-section\">\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"姓名\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.xm\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"政治面貌\">\r\n              <template slot-scope=\"scope\">\r\n                <!-- <p class=\"hyzk\" v-if=\"tjlist.zzmm == 1\">中共党员</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 2\">团员</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 3\">民主党派</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 4\">群众</p> -->\r\n                <el-input placeholder=\"\" v-model=\"tjlist.zzmmComputed\" clearable disabled></el-input>\r\n              </template>\r\n            </el-form-item>\r\n            <el-form-item label=\"出生年月\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.csny\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"所在部门\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.bmmc\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"职务（职称）\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.zw\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.lxdh\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"涉密岗位\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.gwmc\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"涉密等级\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.smdjComputed\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"初步进入涉密岗位日期\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.sgsj\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left sec-form-left1\">\r\n            <el-form-item label=\"流动方式\" >\r\n              <template slot-scope=\"scope\">\r\n                <div style=\"border-right: 1px solid #CDD2D9;\" >\r\n                  <!-- <el-radio v-model=\"tjlist.ldfs\" label=\"1\" style=\"padding-left: 10px;\">离职</el-radio>\r\n                  <el-radio v-model=\"tjlist.ldfs\" label=\"2\">离岗</el-radio\r\n                  <el-radio v-model=\"tjlist.ldfs\" label=\"3\">退休</el-radio>\r\n                  <el-radio v-model=\"tjlist.ldfs\" label=\"4\">其他</el-radio> -->\r\n                  <el-radio v-model=\"tjlist.ldfs\" label=\"1\" style=\"padding-left: 10px;\">本单位其他非涉密岗</el-radio>\r\n                  <el-radio v-model=\"tjlist.ldfs\" label=\"2\">其他机关、单位</el-radio>\r\n                  <el-radio v-model=\"tjlist.ldfs\" label=\"3\" style=\"padding-left: 10px;\">民营资质企业</el-radio>\r\n                  <el-radio v-model=\"tjlist.ldfs\" label=\"4\">其他</el-radio>\r\n                  <el-radio v-model=\"tjlist.ldfs\" label=\"5\">退休</el-radio>\r\n                </div>\r\n              </template>\r\n            </el-form-item>\r\n            <el-form-item label=\"拟进入部门或单位\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.qxdw\" clearable></el-input>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- 电子照片 -->\r\n          <div class=\"sec-header-pic\">\r\n            <div>\r\n              <img v-if=\"tjlist.imageUrl\" :src=\"tjlist.imageUrl\" class=\"avatar\" style=\"\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 单位及职务、职称到涉密等级end -->\r\n        <!-- 主要学习及工作经历start -->\r\n        <p class=\"sec-title\">涉密载体和设备交接清退情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscSwzjList.slice(0, 4)\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"zjmc\" label=\"设备名称\"></el-table-column>\r\n          <el-table-column prop=\"cyqk\" label=\"设备情况\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;\r\n    align-items: center;\">\r\n                <el-radio v-model=\"scope.row.cyqk\" label=\"1\">是</el-radio>\r\n                <el-radio v-model=\"scope.row.cyqk\" label=\"0\">否</el-radio>\r\n                <p>已收回</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 主要学习及工作经历end -->\r\n        <!-- 家庭成员及主要社会关系情况start -->\r\n        <p class=\"sec-title\">脱密期管理</p>\r\n        <div class=\"sec-form-third haveBorderTop\">\r\n          <div class=\"sec-left-text\">\r\n            <div>1.脱密期限为： <el-date-picker v-model=\"tjlist.value1\" type=\"daterange\" range-separator=\"至\"\r\n                start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" style=\"border-right: 0;\" format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </div>\r\n            <div style=\"display: flex;align-items: center;\" class=\"brno\">2.脱密期管理委托单位：<el-input v-model=\"tjlist.tmqgldw\"\r\n                style=\"width: 150px;border-right: none;\"></el-input> 进行。</div>\r\n\r\n          </div>\r\n        </div>\r\n        <p class=\"sec-title\">下载</p>\r\n        <div class=\"sec-form-third haveBorderTop\">\r\n          <div class=\"sec-left-text\">\r\n            <p>1.保密提醒谈话确认</p>\r\n            <p>2.离岗离职涉密人员保密承诺书</p>\r\n            <p>3.脱密期委托管理书</p>\r\n            <p>4.脱密期委托/协助管理涉密人员基本信息表</p>\r\n            <p>5.脱密期协助管理告知函</p>\r\n          </div>\r\n          <el-button size=\"small\" type=\"primary\" @click=\"wdzlxz\">下载</el-button>\r\n        </div>\r\n        <p class=\"sec-title\">上传扫描件</p>\r\n        <div class=\"sec-form-five haveBorderTop\" style=\"position: relative;\">\r\n          <div class=\"sec-left-text\">\r\n            <div class=\"flex\">\r\n              1.上传保密提醒谈话确认扫描件\r\n              <el-upload class=\"upload-demo\" action=\"#\" :http-request=\"httpRequest\" :show-file-list=\"false\">\r\n                <el-button size=\"mini\" type=\"primary\">上传</el-button>\r\n              </el-upload>\r\n              <el-button style=\"margin-left: 10px;\" v-show=\"ylth\" size=\"mini\" type=\"primary\"\r\n                @click=\"ylbmtxth\">预览</el-button>\r\n              <el-dialog :visible.sync=\"dialogVisible\">\r\n                <img :src=\"dialogImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                <div slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\r\n                </div>\r\n              </el-dialog>\r\n            </div>\r\n            <div class=\"flex\">\r\n              2.上传离岗离职涉密人员保密承诺书扫描件\r\n              <el-upload class=\"upload-demo2\" action=\"#\" :http-request=\"httpBmcnsRequest\" :show-file-list=\"false\">\r\n                <el-button size=\"mini\" type=\"primary\">上传</el-button>\r\n              </el-upload>\r\n              <el-button style=\"margin-left: 10px;\" v-show=\"ylcn\" size=\"mini\" type=\"primary\"\r\n                @click=\"ylbmcns\">预览</el-button>\r\n              <el-dialog :visible.sync=\"dialogBmcnsVisible\">\r\n                <img :src=\"dialogBmcnsImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                <div slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button size=\"small\" @click=\"dialogBmcnsVisible = false\">取 消</el-button>\r\n                </div>\r\n              </el-dialog>\r\n            </div>\r\n            <div class=\"flex\">\r\n              3.上传脱密期委托管理书扫描件\r\n              <el-upload class=\"upload-demo3\" action=\"#\" :http-request=\"httpWtsRequest\" :show-file-list=\"false\">\r\n                <el-button size=\"mini\" type=\"primary\">上传</el-button>\r\n              </el-upload>\r\n              <el-button style=\"margin-left: 10px;\" v-show=\"ylwt\" size=\"mini\" type=\"primary\" @click=\"ylwts\">预览</el-button>\r\n              <el-dialog :visible.sync=\"dialogWtsVisible\">\r\n                <img :src=\"dialogWtsImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                <div slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button size=\"small\" @click=\"dialogWtsVisible = false\">取 消</el-button>\r\n                </div>\r\n              </el-dialog>\r\n            </div>\r\n            <div class=\"flex\">\r\n              4.上传脱密期委托/协助管理涉密人员基本信息表扫描件\r\n              <el-upload class=\"upload-demo4\" action=\"#\" :http-request=\"httpRyxxRequest\" :show-file-list=\"false\">\r\n                <el-button size=\"mini\" type=\"primary\">上传</el-button>\r\n              </el-upload>\r\n              <el-button style=\"margin-left: 10px;\" v-show=\"ylxz\" size=\"mini\" type=\"primary\"\r\n                @click=\"ylryxx\">预览</el-button>\r\n              <el-dialog :visible.sync=\"dialogRyxxVisible\">\r\n                <img :src=\"dialogRyxxImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                <div slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button size=\"small\" @click=\"dialogRyxxVisible = false\">取 消</el-button>\r\n                </div>\r\n              </el-dialog>\r\n            </div>\r\n            <div class=\"flex\">\r\n              5.上传脱密期协助管理告知函描件\r\n              <el-upload class=\"upload-demo5\" action=\"#\" :http-request=\"httpXzglRequest\" :show-file-list=\"false\">\r\n                <el-button size=\"mini\" type=\"primary\">上传</el-button>\r\n              </el-upload>\r\n              <el-button style=\"margin-left: 10px;\" v-show=\"ylgz\" size=\"mini\" type=\"primary\"\r\n                @click=\"ylxzgl\">预览</el-button>\r\n              <el-dialog :visible.sync=\"dialogXzglVisible\">\r\n                <img :src=\"dialogXzglImageUrl\" alt=\"\" style=\"width: 100%\">\r\n                <div slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button size=\"small\" @click=\"dialogXzglVisible = false\">取 消</el-button>\r\n                </div>\r\n              </el-dialog>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 下载end -->\r\n        <!-- 底部操作按钮start -->\r\n        <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n          <el-button @click=\"returnIndex\" class=\"fr ml10\" plain>返回</el-button>\r\n          <el-button @click=\"chooseApproval\" class=\"fr\" type=\"success\">保存并提交</el-button>\r\n          <el-button @click=\"save\" class=\"fr\" type=\"primary\">临时保存</el-button>\r\n        </div>\r\n        <!-- 底部操作按钮end -->\r\n\r\n      </el-form>\r\n    </div>\r\n    <!-- 发起申请弹框start -->\r\n    <el-dialog title=\"选择审批人\" :close-on-click-modal=\"false\" :visible.sync=\"approvalDialogVisible\" width=\"40%\" :destroy-on-close=\"true\">\r\n      <div class=\"dlFqsqContainer\">\r\n        <label for=\"\">部门:</label>\r\n        <el-cascader v-model=\"ryChoose.bm\" :options=\"regionOption\" :props=\"regionParams\" filterable clearable\r\n          ref=\"cascaderArr\" @change=\"bmSelectChange\"></el-cascader>\r\n        <label for=\"\">姓名:</label>\r\n        <el-input class=\"input2\" v-model=\"ryChoose.xm\" clearable placeholder=\"姓名\"></el-input>\r\n        <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\" @click=\"searchRy\">查询</el-button>\r\n        <BaseTable class=\"baseTable\" :tableHeight=\"'300'\" :key=\"tableKey\" :showIndex=true :tableData=\"ryDatas\" :columns=\"applyColumns\"\r\n          :showSingleSelection=\"true\" :handleColumn=\"handleColumnApply\" :showPagination=true :currentPage=\"page\"\r\n          :pageSize=\"pageSize\" :totalCount=\"total\" @handleCurrentChange=\"handleCurrentChangeRy\"\r\n          @handleSizeChange=\"handleSizeChangeRy\" @handleSelectionChange=\"handleSelectionChange\">\r\n        </BaseTable>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"warning\" class=\"fr ml10\" @click=\"approvalDialogVisible = false\">关 闭</el-button>\r\n        <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">提交</el-button>\r\n        <!-- <el-button @click=\"save\" class=\"fr\" type=\"primary\">保存</el-button> -->\r\n        <div style=\"clear:both\"></div>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 发起申请弹框end -->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  submitLzlg,\r\n  getLcSLid,\r\n  updateLzlgscb,\r\n  updateSlzt,\r\n  getZzjgList,\r\n  getSpUserList,\r\n  getCurZgfsjl,\r\n  getLoginInfo,\r\n  getFwdyidByFwlx,\r\n  downloadLzlgwdZip,\r\n  deleteSlxxBySlid\r\n} from '../../../api/index'\r\nimport BaseTable from '../../components/common/baseTable.vue'\r\nimport AddLineTable from \"../../components/common/addLineTable.vue\"; //人工纠错组件\r\nexport default {\r\n  components: {\r\n    AddLineTable,\r\n    BaseTable\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      tableKey:1,\r\n      value1: '',\r\n      loading: false,\r\n      // 弹框人员选择条件\r\n      ryChoose: {\r\n        'bm': '',\r\n        'xm': ''\r\n      },\r\n      regionOption: [], // 部门下拉\r\n      page: 1, // 审批人弹框当前页\r\n      pageSize: 10, // 审批人弹框每页条数\r\n      radioIdSelect: '', // 审批人弹框人员单选\r\n      ryDatas: [], // 弹框人员选择\r\n      total: 0, // 弹框人员总数\r\n      regionParams: {\r\n        label: 'label', //这里可以配置你们后端返回的属性\r\n        value: 'label',\r\n        children: 'childrenRegionVo',\r\n        expandTrigger: 'click',\r\n        checkStrictly: true\r\n      }, //地域信息配置参数\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: '#EEF7FF',\r\n        color: '#4D91F8'\r\n      },\r\n      // form表单提交数据\r\n      tjlist: {\r\n        smryid: '',\r\n        xm: '',\r\n        xb: '',\r\n        gj: '中国',\r\n        dwzwzc: '',\r\n        yrsmgw: '',\r\n        cym: '',\r\n        mz: '',\r\n        hyzk: '',\r\n        zzmm: '',\r\n        lxdh: '',\r\n        sfzhm: '',\r\n        hjdz: '',\r\n        hjdgajg: '',\r\n        czdz: '',\r\n        czgajg: '',\r\n        imageUrl: '',\r\n        yjqk: '0', // 拥有外籍、境外永久居留权或者长期居留许可情况\r\n        qscfqk: '0', // 配偶子女有关情况\r\n        qtqk: '', // 其他需要说明的情况\r\n        txthsmj: '',\r\n        bmcnssmj: '',\r\n        wtssmj: '',\r\n        ryjbxxbsmj: '',\r\n        wtsfjsmj: '',\r\n        ldfs: '1',\r\n        qxdw: '',\r\n        value1: [],\r\n      },\r\n      // 主要学习及工作经历\r\n      ryglRyscScjlList: [{\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 家庭成员及社会关系\r\n      ryglRyscJtcyList: [{\r\n        \"gxms\": \"\", //关系描述\r\n        \"zzmm\": \"\", //政治面貌\r\n        \"jwjlqk\": '', //是否有外籍、境外居留权、长期居留许可\r\n        \"xm\": \"\", //姓名\r\n        \"cgszd\": \"\", //工作(学习)单位\r\n        \"zw\": \"\", //职务\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 因私出国(境)情况\r\n      ryglRyscYccgList: [{\r\n        \"cggj\": \"\", //出国国家\r\n        \"sy\": \"\", //事由\r\n        \"zzsj\": \"\", //终止时间\r\n        \"qssj\": \"\", //起始时间\r\n        // \"bz\": \"\",//备注\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 接受境外资助情况\r\n      ryglRyscJwzzqkList: [{\r\n        \"zzsj\": \"\", //时间\r\n        \"jgmc\": \"\", //机构名称\r\n        // \"bz\": \"\",//备注\r\n        \"zznr\": \"\", //资助内容\r\n        \"gj\": \"\", //国家\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 处分或者违法犯罪情况\r\n      ryglRyscCfjlList: [{\r\n        \"cfdw\": \"\", //处罚单位\r\n        \"cfsj\": \"\", //处罚时间\r\n        \"cfjg\": \"\", //处罚结果\r\n        \"cfyy\": \"\", //处罚原因\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 持有因公出入境证件情况\r\n      ryglRyscSwzjList: [{\r\n        'zjmc': '涉密载体（含纸质、光盘等）',\r\n        'fjlb': 1,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': '',\r\n        'qzmc': '部门保密员核定签字：'\r\n      }, {\r\n        'zjmc': '信息设备（含计算机、存储介质等）',\r\n        'fjlb': 2,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': '',\r\n        'qzmc': '部门保密员核定签字：'\r\n      }, {\r\n        'zjmc': '涉密信息系统访问权限回收情况',\r\n        'fjlb': 3,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': '',\r\n        'qzmc': '系统管理员(三员)核定签字：'\r\n      }, {\r\n        'zjmc': '涉密场所出入权限回收情况',\r\n        'fjlb': 4,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': '',\r\n        'qzmc': '涉密场所管理员核定签字：  '\r\n      }],\r\n      ryInfo: {},\r\n      // 政治面貌下拉选项\r\n      zzmmoptions: [{\r\n        value: '中央党员',\r\n        label: '中央党员'\r\n      }, {\r\n        value: '团员',\r\n        label: '团员'\r\n      }, {\r\n        value: '民主党派',\r\n        label: '民主党派'\r\n      }, {\r\n        value: '群众',\r\n        label: '群众'\r\n      }],\r\n      ynoptions: [{\r\n        value: '1',\r\n        label: '是'\r\n      }, {\r\n        value: '0',\r\n        label: '否'\r\n      }],\r\n      sltshow: '', // 文档的缩略图显示\r\n      sltbmcnsshow: '', // 文档的缩略图显示\r\n      sltwtsshow: '', // 文档的缩略图显示\r\n      sltryxxshow: '', // 文档的缩略图显示\r\n      sltxzglshow: '', // 文档的缩略图显示\r\n      routeType: '',\r\n      pdfBase64: '',\r\n      fileList: [],\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n      dialogBmcnsImageUrl: '',\r\n      dialogBmcnsVisible: false,\r\n      dialogWtsImageUrl: '',\r\n      dialogWtsVisible: false,\r\n      dialogRyxxImageUrl: '',\r\n      dialogRyxxVisible: false,\r\n      dialogXzglImageUrl: '',\r\n      dialogXzglVisible: false,\r\n      approvalDialogVisible: false, // 选择申请人弹框\r\n      fileRow: '',\r\n      filebmcnsRow: '',\r\n      filewtsRow: '',\r\n      fileryxxRow: '',\r\n      filexzglRow: '',\r\n      // 选择审核人table\r\n      applyColumns: [{\r\n        name: '姓名',\r\n        prop: 'xm',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      },\r\n      {\r\n        name: '部门',\r\n        prop: 'bmmc',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      },\r\n      {\r\n        name: '岗位',\r\n        prop: 'gwmc',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      }\r\n      ],\r\n      handleColumnApply: [],\r\n      scqk: [\r\n        {\r\n          sfty: '同意',\r\n          id: 1\r\n        },\r\n        {\r\n          sfty: '不同意',\r\n          id: 0\r\n        },\r\n      ],\r\n      disabled1: false,\r\n      disabled2: false,\r\n      disabled3: false,\r\n      ylth: false,\r\n      ylcn: false,\r\n      ylwt: false,\r\n      ylxz: false,\r\n      ylgz: false,\r\n    }\r\n  },\r\n  computed: {\r\n    // selectedLabel() {\r\n    //   const option = this.ynoptions.find(o => o.value === this.selectedValue);\r\n    //   return option ? option.label : '';\r\n    // }\r\n  },\r\n  mounted() {\r\n    this.onfwid()\r\n    this.yhDatas = this.$route.query.datas\r\n    console.log(this.yhDatas);\r\n    this.ryInfo = this.$route.query.datas\r\n    this.routeType = this.$route.query.type\r\n    this.routezt = this.$route.query.zt\r\n    console.log(this.routezt);\r\n    this.getOrganization()\r\n    let result = {}\r\n    let iamgeBase64 = ''\r\n    let iamgeBase64Brcn = ''\r\n    if (this.$route.query.type == 'add') {\r\n      // 首次发起申请\r\n      result = {\r\n        ...this.tjlist,\r\n        ...this.$route.query.datas\r\n      }\r\n      iamgeBase64 = \"data:image/jpeg;base64,\" + this.$route.query.datas.zp\r\n    } else {\r\n      // 保存 继续编辑\r\n      result = {\r\n        ...this.tjlist,\r\n        ...this.$route.query.datas\r\n      }\r\n      iamgeBase64 = \"data:image/jpeg;base64,\" + this.$route.query.datas.zp\r\n\r\n    }\r\n    // 初始化各状态值显示其代表的中文名\r\n    result.xingbie = result.xb == 2 ? '女' : result.xb == 1 ? '男' : ''\r\n    result.hyzkComputed = result.hyzk == 0 ? '未婚' : result.hyzk == 1 ? '已婚' : ''\r\n    result.zzmmComputed = result.zzmm == 1 ? '中共党员' : result.zzmm == 2 ? '团员' : result.zzmm == 3 ? '民主党派' : result.zzmm == 4 ? '群众' : ''\r\n    result.jbzcComputed = result.jbzc == 1 ? '省部级' : result.jbzc == 2 ? '厅局级' : result.jbzc == 3 ? '县处级' : result.jbzc == 4 ? '乡科级及以下' : result.jbzc == 5 ? '高级(含正高、副高)' : result.jbzc == 6 ? '中级' : result.jbzc == 7 ? '初级及以下' : result.jbzc == 8 ? '试用期人员' : result.jbzc == 9 ? '工勤人员' : result.jbzc == 10 ? '企业职员' : result.jbzc == 11 ? '其他' : ''\r\n    let bmC = result.bmmc != '' ? '部门：' + result.bmmc + '、' : ''\r\n    let zwC = result.zw != '' ? '职务：' + result.zw + '、' : ''\r\n    result.bmzwzcComputed = bmC + zwC + '职称：' + result.jbzcComputed\r\n    // result.gwmcxx = result.gwmc && result.gwmc.length === 1 ? result.gwmc.toString() : result.gwmc && result.gwmc.length > 1 ? result.gwmc.join('/') : ''\r\n    if (Array.isArray(result.gwmc)) {\r\n      result.gwmc = result.gwmc && result.gwmc.length === 1 ? result.gwmc.toString() : result.gwmc && result.gwmc.length > 1 ? result.gwmc.join('/') : ''\r\n    }\r\n    // && result.gwmc.length === 1 ? result.gwmc.toString() : result.gwmc && result.gwmc.length > 1 ? result.gwmc.join('/') : ''\r\n    result.smdjComputed = result.smdj == 1 ? '核心' : result.smdj == 2 ? '重要' : result.smdj == 3 ? '一般' : ''\r\n\r\n    this.tjlist = result\r\n    if (this.tjlist.sfzhm == '' || this.tjlist.sfzhm == undefined) {\r\n      this.tjlist.csny = result.csny\r\n    } else {\r\n      this.tjlist.csny = this.tjlist.sfzhm.substring(6, 10) + \"-\" + this.tjlist.sfzhm.substring(10, 12) + \"-\" + this.tjlist.sfzhm.substring(12, 14);\r\n    }\r\n    console.log(result.csqxsfhs);\r\n    if (result.csqxsfhs == '' || result.csqxsfhs == undefined) {\r\n      this.ryglRyscSwzjList[0].cyqk = '1'\r\n      this.ryglRyscSwzjList[1].cyqk = '1'\r\n      this.ryglRyscSwzjList[2].cyqk = '1'\r\n      this.ryglRyscSwzjList[3].cyqk = '1'\r\n    } else {\r\n      this.ryglRyscSwzjList[0].cyqk = result.smztsfqt.toString()\r\n      this.ryglRyscSwzjList[1].cyqk = result.xtqxsfhs.toString()\r\n      this.ryglRyscSwzjList[2].cyqk = result.xxsbsfqt.toString()\r\n      this.ryglRyscSwzjList[3].cyqk = result.csqxsfhs.toString()\r\n    }\r\n    if (result.tmqssj != '' || result.tmqssj != undefined) {\r\n      this.tjlist.value1.push(result.tmqssj);\r\n      this.tjlist.value1.push(result.tmjssj);\r\n    }\r\n    console.log(this.tjlist);\r\n    this.tjlist.yjqk = result.yjqk.toString()\r\n    this.tjlist.qscfqk = result.qscfqk.toString()\r\n\r\n    if (typeof iamgeBase64 === \"string\") {\r\n      // 复制某条消息\r\n      if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n      function validDataUrl(s) {\r\n        return validDataUrl.regex.test(s);\r\n      }\r\n      validDataUrl.regex =\r\n        /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n      if (validDataUrl(iamgeBase64)) {\r\n        let that = this;\r\n\r\n        function previwImg(item) {\r\n          that.tjlist.imageUrl = item;\r\n        }\r\n        previwImg(iamgeBase64);\r\n      }\r\n    }\r\n    if (this.tjlist.txthsmj != '') {\r\n      this.ylth = true\r\n    }\r\n    if (this.tjlist.bmcnssmj != '') {\r\n      this.ylcn = true\r\n    }\r\n    if (this.tjlist.wtssmj != '') {\r\n      this.ylwt = true\r\n    }\r\n    if (this.tjlist.ryjbxxbsmj != '') {\r\n      this.ylxz = true\r\n    }\r\n    if (this.tjlist.wtsfjsmj != '') {\r\n      this.ylgz = true\r\n    }\r\n  },\r\n  methods: {\r\n    async wdzlxz() {\r\n      var returnData = await downloadLzlgwdZip();\r\n      var date = new Date()\r\n      var sj = date.getFullYear() + \"\" + (date.getMonth() + 1) + \"\" + date.getDate()\r\n      this.dom_download(returnData, '离岗离职' + '-' + sj + \".zip\");\r\n    },\r\n    //处理下载流\r\n    dom_download(content, fileName) {\r\n      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象\r\n      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象\r\n      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download\r\n      dom.style.display = 'none'\r\n      dom.href = url\r\n      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件\r\n      document.body.appendChild(dom)\r\n      dom.click()\r\n    },\r\n    chRadio() { },\r\n    blobToBase64(blob, callback) {\r\n      const fileReader = new FileReader();\r\n      fileReader.onload = (e) => {\r\n        callback(e.target.result);\r\n      };\r\n      fileReader.readAsDataURL(blob);\r\n    },\r\n    handleSelectionChange(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    // 主要学习及工作经历增加行\r\n    addRow(data) {\r\n      data.push({\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除',\r\n      })\r\n    },\r\n    // 主要学习及工作经历删除行\r\n    delRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 家庭成员及主要社会关系情况 添加行\r\n    cyjshgxAddRow(data) {\r\n      data.push({\r\n        'ybrgx': '',\r\n        'xm': '',\r\n        'sfywjjwjlqcqjlxk': '',\r\n        'dw': '',\r\n        'zw': '',\r\n        'zzmm': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 家庭成员及主要社会关系情况 删除行\r\n    cyjshgxDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 因私出国(境)情况 添加行\r\n    yscgqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'zzrq': '',\r\n        'jsnsdgjhdq': '',\r\n        'sy': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 因私出国(境)情况 删除行\r\n    yscgqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 接受境外资助情况 添加行\r\n    jsjwzzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'gjdq': '',\r\n        'jgmc': '',\r\n        'zznr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 接受境外资助情况 删除行\r\n    jsjwzzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 处分或者违法犯罪情况 添加行\r\n    clhwffzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'cljg': '',\r\n        'clyy': '',\r\n        'cljg': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 处分或者违法犯罪情况 删除行\r\n    clhwffzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    zpzm(zp) {\r\n      const iamgeBase64 = \"data:image/jpeg;base64,\" + zp;\r\n      let zpxx\r\n      if (typeof iamgeBase64 === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64)) {\r\n          // debugger;\r\n          // let that = this;\r\n\r\n          function previwImg(item) {\r\n            zpxx = item;\r\n          }\r\n          previwImg(iamgeBase64);\r\n        }\r\n      }\r\n      return zpxx\r\n    },\r\n    // 上传保密谈话记录表凭证\r\n    httpRequest(data) {\r\n      this.sltshow = URL.createObjectURL(data.file);\r\n      this.fileRow = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.txthsmj = dataurl.split(',')[1]\r\n        console.log(this.tjlist.txthsmj);\r\n        if (this.tjlist.txthsmj != '') {\r\n          this.ylth = true\r\n        }\r\n      });\r\n    },\r\n    // 预览\r\n    ylbmtxth() {\r\n      let zpxx\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogImageUrl = URL.createObjectURL(this.fileRow)\r\n      } else {\r\n        zpxx = this.zpzm(this.tjlist.txthsmj)\r\n        this.dialogImageUrl = zpxx\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // 上传离岗离职涉密人员保密承诺书凭证\r\n    httpBmcnsRequest(data) {\r\n      this.sltbmcnsshow = URL.createObjectURL(data.file);\r\n      this.filebmcnsRow = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.bmcnssmj = dataurl.split(',')[1]\r\n        console.log(this.tjlist.bmcnssmj);\r\n        if (this.tjlist.bmcnssmj != '') {\r\n          this.ylcn = true\r\n        }\r\n      });\r\n    },\r\n    // 预览\r\n    ylbmcns() {\r\n      let zpxx\r\n      if (this.routeType == 'add') {\r\n        this.dialogBmcnsImageUrl = URL.createObjectURL(this.filebmcnsRow)\r\n      } else {\r\n        zpxx = this.zpzm(this.tjlist.bmcnssmj)\r\n        this.dialogBmcnsImageUrl = zpxx\r\n      }\r\n      this.dialogBmcnsVisible = true\r\n    },\r\n    // 上传脱密期委托管理书凭证\r\n    httpWtsRequest(data) {\r\n      this.sltwtsshow = URL.createObjectURL(data.file);\r\n      this.filewtsRow = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.wtssmj = dataurl.split(',')[1]\r\n        console.log(this.tjlist.wtssmj);\r\n        if (this.tjlist.wtssmj != '') {\r\n          this.ylwt = true\r\n        }\r\n      });\r\n    },\r\n    // 预览\r\n    ylwts() {\r\n      let zpxx\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogWtsImageUrl = URL.createObjectURL(this.filewtsRow)\r\n      } else {\r\n        zpxx = this.zpzm(this.tjlist.wtssmj)\r\n        this.dialogWtsImageUrl = zpxx\r\n      }\r\n      this.dialogWtsVisible = true\r\n    },\r\n    // 上传脱密期委托/协助管理涉密人员基本信息表凭证\r\n    httpRyxxRequest(data) {\r\n      this.sltryxxshow = URL.createObjectURL(data.file);\r\n      this.fileryxxRow = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.ryjbxxbsmj = dataurl.split(',')[1]\r\n        console.log(this.tjlist.ryjbxxbsmj);\r\n        if (this.tjlist.ryjbxxbsmj != '') {\r\n          this.ylxz = true\r\n        }\r\n      });\r\n    },\r\n    // 预览\r\n    ylryxx() {\r\n      let zpxx\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogRyxxImageUrl = URL.createObjectURL(this.fileryxxRow)\r\n      } else {\r\n        zpxx = this.zpzm(this.tjlist.ryjbxxbsmj)\r\n        this.dialogRyxxImageUrl = zpxx\r\n      }\r\n      this.dialogRyxxVisible = true\r\n    },\r\n    // 上传脱密期协助管理告知函凭证\r\n    httpXzglRequest(data) {\r\n      this.sltxzglshow = URL.createObjectURL(data.file);\r\n      this.filexzglRow = data.file\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.wtsfjsmj = dataurl.split(',')[1]\r\n        console.log(this.tjlist.wtsfjsmj);\r\n        if (this.tjlist.wtsfjsmj != '') {\r\n          this.ylgz = true\r\n        }\r\n      });\r\n    },\r\n    // 预览\r\n    ylxzgl() {\r\n      let zpxx\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogXzglImageUrl = URL.createObjectURL(this.filexzglRow)\r\n      } else {\r\n        zpxx = this.zpzm(this.tjlist.wtsfjsmj)\r\n        this.dialogXzglImageUrl = zpxx\r\n      }\r\n      this.dialogXzglVisible = true\r\n    },\r\n    // 删除\r\n    shanchu() {\r\n      this.tjlist.txthsmj = ''\r\n      this.sltshow = ''\r\n    },\r\n    async onfwid() {\r\n      let params = {\r\n        fwlx: 4\r\n      }\r\n      let data = await getFwdyidByFwlx(params)\r\n      console.log(data);\r\n      this.fwdyid = data.data.fwdyid\r\n    },\r\n    // 保存\r\n    async save() {\r\n      let param = {\r\n        'fwdyid': this.fwdyid,\r\n        'lcslclzt': 3\r\n      }\r\n      // this.ryglRyscJtcyList.forEach((e) => {\r\n      //   if (e.jwjlqk == '否') {\r\n      //     e.jwjlqk = 0\r\n      //   } else if (e.jwjlqk == '是') {\r\n      //     e.jwjlqk = 1\r\n      //   }\r\n      // })\r\n      // console.log(this.tjlist.gwmc);\r\n      if (this.routeType == 'update') {\r\n        this.tjlist.dwid = this.ryInfo.dwid\r\n        this.tjlist.lcslid = this.ryInfo.lcslid\r\n        let params = {\r\n          lcslid: this.tjlist.lcslid,\r\n          smryid: this.tjlist.smryid,\r\n          bmid: this.tjlist.bmid,\r\n          bmmc: this.tjlist.bmmc,\r\n          xm: this.tjlist.xm,\r\n          zzmm: this.tjlist.zzmm,\r\n          csny: this.tjlist.csny,\r\n          zw: this.tjlist.zw,\r\n          jbzc: this.tjlist.jbzc,\r\n          lxdh: this.tjlist.lxdh,\r\n          gwmc: this.tjlist.gwmc,\r\n          smdj: this.tjlist.smdj,\r\n          dwid: this.tjlist.dwid,\r\n          sgsj: this.tjlist.sgsj,\r\n          ldfs: this.tjlist.ldfs,\r\n          qxdw: this.tjlist.qxdw,\r\n          sqsj: this.tjlist.sqsj,\r\n          smztsfqt: this.ryglRyscSwzjList[0].cyqk,\r\n          xtqxsfhs: this.ryglRyscSwzjList[1].cyqk,\r\n          xxsbsfqt: this.ryglRyscSwzjList[2].cyqk,\r\n          csqxsfhs: this.ryglRyscSwzjList[3].cyqk,\r\n          tmqssj: this.tjlist.value1[0],\r\n          tmjssj: this.tjlist.value1[1],\r\n          tmqgldw: this.tjlist.tmqgldw,\r\n          txthsmj: this.tjlist.txthsmj,\r\n          bmcnssmj: this.tjlist.bmcnssmj,\r\n          wtssmj: this.tjlist.wtssmj,\r\n          ryjbxxbsmj: this.tjlist.ryjbxxbsmj,\r\n          wtsfjsmj: this.tjlist.wtsfjsmj,\r\n        }\r\n        let resDatas\r\n        if (this.routezt == undefined) {\r\n          resDatas = await updateLzlgscb(params)\r\n        } else if (this.routezt == 1) {\r\n          resDatas = await submitLzlg(params)\r\n        }\r\n        if (resDatas.code == 10000) {\r\n          this.$router.push('/zgfs')\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n        }\r\n      } else {\r\n        param.smryid = this.yhDatas.smryid\r\n        this.tjlist.dwid = this.yhDatas.dwid\r\n        let res = await getLcSLid(param)\r\n        if (res.code == 10000) {\r\n          this.tjlist.lcslid = res.data.slid\r\n          let params = {\r\n            lcslid: this.tjlist.lcslid,\r\n            smryid: this.tjlist.smryid,\r\n            bmid: this.tjlist.bmid,\r\n            bmmc: this.tjlist.bmmc,\r\n            xm: this.tjlist.xm,\r\n            zzmm: this.tjlist.zzmm,\r\n            csny: this.tjlist.csny,\r\n            zw: this.tjlist.zw,\r\n            jbzc: this.tjlist.jbzc,\r\n            lxdh: this.tjlist.lxdh,\r\n            gwmc: this.tjlist.gwmc,\r\n            smdj: this.tjlist.smdj,\r\n            dwid: this.tjlist.dwid,\r\n            sgsj: this.tjlist.sgsj,\r\n            ldfs: this.tjlist.ldfs,\r\n            qxdw: this.tjlist.qxdw,\r\n            sqsj: this.tjlist.sqsj,\r\n            smztsfqt: this.ryglRyscSwzjList[0].cyqk,\r\n            xtqxsfhs: this.ryglRyscSwzjList[1].cyqk,\r\n            xxsbsfqt: this.ryglRyscSwzjList[2].cyqk,\r\n            csqxsfhs: this.ryglRyscSwzjList[3].cyqk,\r\n            tmqssj: this.tjlist.value1[0],\r\n            tmjssj: this.tjlist.value1[1],\r\n            tmqgldw: this.tjlist.tmqgldw,\r\n            txthsmj: this.tjlist.txthsmj,\r\n            bmcnssmj: this.tjlist.bmcnssmj,\r\n            wtssmj: this.tjlist.wtssmj,\r\n            ryjbxxbsmj: this.tjlist.ryjbxxbsmj,\r\n            wtsfjsmj: this.tjlist.wtsfjsmj,\r\n          }\r\n          let resDatas = await submitLzlg(params)\r\n          if (resDatas.code == 10000) {\r\n            this.$router.push('/lzlg')\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n          }else {\r\n          deleteSlxxBySlid({ slid: res.data.slid })\r\n        }\r\n        }\r\n      }\r\n    },\r\n    //全部组织机构List\r\n    async getOrganization() {\r\n      let zzjgList = await getZzjgList()\r\n      this.zzjgmc = zzjgList\r\n      let shu = []\r\n      this.zzjgmc.forEach(item => {\r\n        let childrenRegionVo = []\r\n        this.zzjgmc.forEach(item1 => {\r\n          if (item.bmm == item1.fbmm) {\r\n            childrenRegionVo.push(item1)\r\n            item.childrenRegionVo = childrenRegionVo\r\n          }\r\n        });\r\n        shu.push(item)\r\n      })\r\n      let shuList = []\r\n      let list = await getLoginInfo()\r\n      if (list.fbmm == '') {\r\n        shu.forEach(item => {\r\n          if (item.fbmm == '') {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      if (list.fbmm != '') {\r\n        shu.forEach(item => {\r\n          console.log(item);\r\n          if (item.fbmm == list.fbmm) {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      shuList[0].childrenRegionVo.forEach(item => {\r\n        this.regionOption.push(item)\r\n      })\r\n    },\r\n    handleSelectionChange1(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    handleCurrentChangeRy(val) {\r\n      this.page = val\r\n      this.chooseApproval()\r\n    },\r\n    //列表分页--更改每页显示个数\r\n    handleSizeChangeRy(val) {\r\n      this.page = 1\r\n      this.pageSize = val\r\n      this.chooseApproval()\r\n    },\r\n    // 人员搜索\r\n    searchRy() {\r\n      this.tableKey++\r\n      this.chooseApproval()\r\n    },\r\n    // 发起申请选择人员 人员下拉\r\n    bmSelectChange(item) {\r\n      if (item != undefined) {\r\n        this.ryChoose.bm = item.join('/')\r\n      }\r\n    },\r\n    // 选择审批人\r\n    async chooseApproval() {\r\n     // this.getOrganization()\r\n      this.approvalDialogVisible = true\r\n      let param = {\r\n        'page': this.page,\r\n        'pageSize': this.pageSize,\r\n        'fwdyid': this.fwdyid,\r\n        'bmmc': this.ryChoose.bm,\r\n        'xm': this.ryChoose.xm\r\n      }\r\n      let resData = await getSpUserList(param)\r\n      if (resData.records) {\r\n        // this.loading = false\r\n        this.ryDatas = resData.records\r\n        this.total = resData.total\r\n      } else {\r\n        this.$message.error('数据获取失败！')\r\n      }\r\n    },\r\n    // 保存并提交\r\n    async saveAndSubmit() {\r\n      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {\r\n        let param = {\r\n          'fwdyid': this.fwdyid\r\n        }\r\n        // this.ryglRyscJtcyList.forEach((e) => {\r\n        //   if (e.jwjlqk == '否') {\r\n        //     e.jwjlqk = 0\r\n        //   } else if (e.jwjlqk == '是') {\r\n        //     e.jwjlqk = 1\r\n        //   }\r\n        // })\r\n        if (this.routeType == 'update' && this.routezt == undefined) {\r\n          param.lcslclzt = 2\r\n          param.smryid = this.ryInfo.smryid\r\n          param.slid = this.ryInfo.lcslid\r\n          param.clrid = this.radioIdSelect.yhid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = this.ryInfo.dwid\r\n            this.tjlist.lcslid = this.ryInfo.lcslid\r\n            let params = {\r\n              lcslid: this.tjlist.lcslid,\r\n              smryid: this.tjlist.smryid,\r\n              bmid: this.tjlist.bmid,\r\n              bmmc: this.tjlist.bmmc,\r\n              xm: this.tjlist.xm,\r\n              zzmm: this.tjlist.zzmm,\r\n              csny: this.tjlist.csny,\r\n              zw: this.tjlist.zw,\r\n              jbzc: this.tjlist.jbzc,\r\n              lxdh: this.tjlist.lxdh,\r\n              gwmc: this.tjlist.gwmc,\r\n              smdj: this.tjlist.smdj,\r\n              dwid: this.tjlist.dwid,\r\n              sgsj: this.tjlist.sgsj,\r\n              ldfs: this.tjlist.ldfs,\r\n              qxdw: this.tjlist.qxdw,\r\n              sqsj: this.tjlist.sqsj,\r\n              smztsfqt: this.ryglRyscSwzjList[0].cyqk,\r\n              xtqxsfhs: this.ryglRyscSwzjList[1].cyqk,\r\n              xxsbsfqt: this.ryglRyscSwzjList[2].cyqk,\r\n              csqxsfhs: this.ryglRyscSwzjList[3].cyqk,\r\n              tmqssj: this.tjlist.value1[0],\r\n              tmjssj: this.tjlist.value1[1],\r\n              tmqgldw: this.tjlist.tmqgldw,\r\n              txthsmj: this.tjlist.txthsmj,\r\n              bmcnssmj: this.tjlist.bmcnssmj,\r\n              wtssmj: this.tjlist.wtssmj,\r\n              ryjbxxbsmj: this.tjlist.ryjbxxbsmj,\r\n              wtsfjsmj: this.tjlist.wtsfjsmj,\r\n            }\r\n            let resDatas = await updateLzlgscb(params)\r\n            if (resDatas.code == 10000) {\r\n              let paramStatus = {\r\n                'fwdyid': this.fwdyid,\r\n                'slid': this.tjlist.lcslid\r\n              }\r\n              let resStatus\r\n\r\n              resStatus = await updateSlzt(paramStatus)\r\n\r\n\r\n              if (resStatus.code == 10000) {\r\n                this.$router.push('/lzlg')\r\n                this.$message({\r\n                  message: '保存并提交成功',\r\n                  type: 'success'\r\n                })\r\n              }\r\n            }\r\n          }\r\n        } else {\r\n          param.lcslclzt = 0\r\n          param.clrid = this.radioIdSelect.yhid\r\n          param.smryid = this.yhDatas.smryid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = this.yhDatas.dwid\r\n            this.tjlist.lcslid = res.data.slid\r\n            let params = {\r\n              lcslid: this.tjlist.lcslid,\r\n              smryid: this.tjlist.smryid,\r\n              bmid: this.tjlist.bmid,\r\n              bmmc: this.tjlist.bmmc,\r\n              xm: this.tjlist.xm,\r\n              zzmm: this.tjlist.zzmm,\r\n              csny: this.tjlist.csny,\r\n              zw: this.tjlist.zw,\r\n              jbzc: this.tjlist.jbzc,\r\n              lxdh: this.tjlist.lxdh,\r\n              gwmc: this.tjlist.gwmc,\r\n              smdj: this.tjlist.smdj,\r\n              dwid: this.tjlist.dwid,\r\n              sgsj: this.tjlist.sgsj,\r\n              ldfs: this.tjlist.ldfs,\r\n              qxdw: this.tjlist.qxdw,\r\n              sqsj: this.tjlist.sqsj,\r\n              smztsfqt: this.ryglRyscSwzjList[0].cyqk,\r\n              xtqxsfhs: this.ryglRyscSwzjList[1].cyqk,\r\n              xxsbsfqt: this.ryglRyscSwzjList[2].cyqk,\r\n              csqxsfhs: this.ryglRyscSwzjList[3].cyqk,\r\n              tmqssj: this.tjlist.value1[0],\r\n              tmjssj: this.tjlist.value1[1],\r\n              tmqgldw: this.tjlist.tmqgldw,\r\n              txthsmj: this.tjlist.txthsmj,\r\n              bmcnssmj: this.tjlist.bmcnssmj,\r\n              wtssmj: this.tjlist.wtssmj,\r\n              ryjbxxbsmj: this.tjlist.ryjbxxbsmj,\r\n              wtsfjsmj: this.tjlist.wtsfjsmj,\r\n            }\r\n            let resDatas = await submitLzlg(params)\r\n            if (resDatas.code == 10000) {\r\n              this.$router.push('/zgfs')\r\n              this.$message({\r\n                message: '保存并提交成功',\r\n                type: 'success'\r\n              })\r\n            }else {\r\n          deleteSlxxBySlid({ slid: res.data.slid })\r\n        }\r\n          }\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: '请选择审批人',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 返回\r\n    returnIndex() {\r\n      this.$router.push('/zgfs')\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.sec-container {\r\n  width: 100%;\r\n  height: calc(100% - 50px);\r\n  overflow-y: overlay;\r\n}\r\n\r\n.sec-title {\r\n  border-left: 5px solid #1b72d8;\r\n  color: #1b72d8;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  text-indent: 10px;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n  width: calc(100% - 260px);\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n  width: 258px;\r\n  position: absolute;\r\n  right: 0px;\r\n  top: 0;\r\n  height: 203px;\r\n  border: 1px solid #CDD2D9;\r\n  border-left: 0;\r\n  background: #ffffff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.flex {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-demo {\r\n  margin-left: 217px;\r\n}\r\n\r\n.upload-demo2 {\r\n  margin-left: 137px;\r\n}\r\n\r\n.upload-demo3 {\r\n  margin-left: 217px;\r\n}\r\n\r\n.upload-demo4 {\r\n  margin-left: 49px;\r\n}\r\n\r\n.upload-demo5 {\r\n  margin-left: 201px;\r\n}\r\n\r\n.sec-form-second {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-third {\r\n  border: 1px solid #CDD2D9;\r\n  /* height: 40px;  */\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-four {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  overflow: hidden;\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.yulan {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  color: #3874D5;\r\n  font-weight: 600;\r\n  float: left;\r\n  margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n  width: 140px;\r\n  height: 180px;\r\n  display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 178px;\r\n  height: 178px;\r\n  line-height: 178px;\r\n  text-align: center;\r\n  border: 2px solid #EBEBEB;\r\n}\r\n\r\n.sec-form-six {\r\n  border: 1px solid #CDD2D9;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.ml10 {\r\n  margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n  margin-top: 10px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n  border: none;\r\n}\r\n\r\n.sec-left-text {\r\n  float: left;\r\n  margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n  border-top: 1px solid #CDD2D9;\r\n}\r\n\r\n>>>.longLabel .el-form-item__label {\r\n  width: 500px !important;\r\n}\r\n\r\n>>>.longLabel .el-form-item__content {\r\n  margin-left: 500px !important;\r\n  padding-left: 20px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n  border-top: 0;\r\n} */\r\n.sec-form-second .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n  width: 100%;\r\n  border: 1px solid #EBEEF5;\r\n  height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n.hyzk {\r\n  padding-left: 15px;\r\n  background-color: #F5F7FA;\r\n  width: calc(100% - 16px);\r\n  border-right: 1px solid #CDD2D9;\r\n  color: #C0C4CC;\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n  border: none !important;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n  width: 200px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n  border: none;\r\n  border-right: 1px solid #CDD2D9;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n  border: 1px solid #CDD2D9;;\r\n} */\r\n>>>.el-form-item__label {\r\n  border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n>>>.brno .el-input__inner {\r\n  border-right: none;\r\n}\r\n\r\n/* /deep/.sec-form-container .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n} */\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n.sec-form-left1{\r\n  height: 80px;\r\n}\r\n.sec-form-left1>>>.el-form-item__label{\r\n  line-height: 80px;\r\n}\r\n\r\n.sec-form-left1>>>.el-input__inner{\r\n  line-height: 80px;\r\n  height: 80px;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/lzlgTable.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"sec-container\"},[_c('p',{staticClass:\"sec-title\"},[_vm._v(\"基本信息\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"姓名\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xm\", $$v)},expression:\"tjlist.xm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"政治面貌\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.zzmmComputed),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zzmmComputed\", $$v)},expression:\"tjlist.zzmmComputed\"}})]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"出生年月\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.csny),callback:function ($$v) {_vm.$set(_vm.tjlist, \"csny\", $$v)},expression:\"tjlist.csny\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"所在部门\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.bmmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bmmc\", $$v)},expression:\"tjlist.bmmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"职务（职称）\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.zw),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zw\", $$v)},expression:\"tjlist.zw\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"联系电话\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.lxdh),callback:function ($$v) {_vm.$set(_vm.tjlist, \"lxdh\", $$v)},expression:\"tjlist.lxdh\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"涉密岗位\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.gwmc),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gwmc\", $$v)},expression:\"tjlist.gwmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"涉密等级\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.smdjComputed),callback:function ($$v) {_vm.$set(_vm.tjlist, \"smdjComputed\", $$v)},expression:\"tjlist.smdjComputed\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"初步进入涉密岗位日期\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.sgsj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sgsj\", $$v)},expression:\"tjlist.sgsj\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left sec-form-left1\"},[_c('el-form-item',{attrs:{\"label\":\"流动方式\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticStyle:{\"border-right\":\"1px solid #CDD2D9\"}},[_c('el-radio',{staticStyle:{\"padding-left\":\"10px\"},attrs:{\"label\":\"1\"},model:{value:(_vm.tjlist.ldfs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"ldfs\", $$v)},expression:\"tjlist.ldfs\"}},[_vm._v(\"本单位其他非涉密岗\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"2\"},model:{value:(_vm.tjlist.ldfs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"ldfs\", $$v)},expression:\"tjlist.ldfs\"}},[_vm._v(\"其他机关、单位\")]),_vm._v(\" \"),_c('el-radio',{staticStyle:{\"padding-left\":\"10px\"},attrs:{\"label\":\"3\"},model:{value:(_vm.tjlist.ldfs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"ldfs\", $$v)},expression:\"tjlist.ldfs\"}},[_vm._v(\"民营资质企业\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"4\"},model:{value:(_vm.tjlist.ldfs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"ldfs\", $$v)},expression:\"tjlist.ldfs\"}},[_vm._v(\"其他\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"5\"},model:{value:(_vm.tjlist.ldfs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"ldfs\", $$v)},expression:\"tjlist.ldfs\"}},[_vm._v(\"退休\")])],1)]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"拟进入部门或单位\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\"},model:{value:(_vm.tjlist.qxdw),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qxdw\", $$v)},expression:\"tjlist.qxdw\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-header-pic\"},[_c('div',[(_vm.tjlist.imageUrl)?_c('img',{staticClass:\"avatar\",attrs:{\"src\":_vm.tjlist.imageUrl}}):_vm._e()])])]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"涉密载体和设备交接清退情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscSwzjList.slice(0, 4),\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zjmc\",\"label\":\"设备名称\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cyqk\",\"label\":\"设备情况\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\"}},[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(scope.row.cyqk),callback:function ($$v) {_vm.$set(scope.row, \"cyqk\", $$v)},expression:\"scope.row.cyqk\"}},[_vm._v(\"是\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"0\"},model:{value:(scope.row.cyqk),callback:function ($$v) {_vm.$set(scope.row, \"cyqk\", $$v)},expression:\"scope.row.cyqk\"}},[_vm._v(\"否\")]),_vm._v(\" \"),_c('p',[_vm._v(\"已收回\")])],1)]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"脱密期管理\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-third haveBorderTop\"},[_c('div',{staticClass:\"sec-left-text\"},[_c('div',[_vm._v(\"1.脱密期限为： \"),_c('el-date-picker',{staticStyle:{\"border-right\":\"0\"},attrs:{\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(_vm.tjlist.value1),callback:function ($$v) {_vm.$set(_vm.tjlist, \"value1\", $$v)},expression:\"tjlist.value1\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"brno\",staticStyle:{\"display\":\"flex\",\"align-items\":\"center\"}},[_vm._v(\"2.脱密期管理委托单位：\"),_c('el-input',{staticStyle:{\"width\":\"150px\",\"border-right\":\"none\"},model:{value:(_vm.tjlist.tmqgldw),callback:function ($$v) {_vm.$set(_vm.tjlist, \"tmqgldw\", $$v)},expression:\"tjlist.tmqgldw\"}}),_vm._v(\" 进行。\")],1)])]),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"下载\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-third haveBorderTop\"},[_c('div',{staticClass:\"sec-left-text\"},[_c('p',[_vm._v(\"1.保密提醒谈话确认\")]),_vm._v(\" \"),_c('p',[_vm._v(\"2.离岗离职涉密人员保密承诺书\")]),_vm._v(\" \"),_c('p',[_vm._v(\"3.脱密期委托管理书\")]),_vm._v(\" \"),_c('p',[_vm._v(\"4.脱密期委托/协助管理涉密人员基本信息表\")]),_vm._v(\" \"),_c('p',[_vm._v(\"5.脱密期协助管理告知函\")])]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":_vm.wdzlxz}},[_vm._v(\"下载\")])],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"上传扫描件\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-five haveBorderTop\",staticStyle:{\"position\":\"relative\"}},[_c('div',{staticClass:\"sec-left-text\"},[_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n            1.上传保密提醒谈话确认扫描件\\n            \"),_c('el-upload',{staticClass:\"upload-demo\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpRequest,\"show-file-list\":false}},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylth),expression:\"ylth\"}],staticStyle:{\"margin-left\":\"10px\"},attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylbmtxth}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogVisible},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n            2.上传离岗离职涉密人员保密承诺书扫描件\\n            \"),_c('el-upload',{staticClass:\"upload-demo2\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpBmcnsRequest,\"show-file-list\":false}},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylcn),expression:\"ylcn\"}],staticStyle:{\"margin-left\":\"10px\"},attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylbmcns}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogBmcnsVisible},on:{\"update:visible\":function($event){_vm.dialogBmcnsVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogBmcnsImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogBmcnsVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n            3.上传脱密期委托管理书扫描件\\n            \"),_c('el-upload',{staticClass:\"upload-demo3\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpWtsRequest,\"show-file-list\":false}},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylwt),expression:\"ylwt\"}],staticStyle:{\"margin-left\":\"10px\"},attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylwts}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogWtsVisible},on:{\"update:visible\":function($event){_vm.dialogWtsVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogWtsImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogWtsVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n            4.上传脱密期委托/协助管理涉密人员基本信息表扫描件\\n            \"),_c('el-upload',{staticClass:\"upload-demo4\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpRyxxRequest,\"show-file-list\":false}},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylxz),expression:\"ylxz\"}],staticStyle:{\"margin-left\":\"10px\"},attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylryxx}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogRyxxVisible},on:{\"update:visible\":function($event){_vm.dialogRyxxVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogRyxxImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogRyxxVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"flex\"},[_vm._v(\"\\n            5.上传脱密期协助管理告知函描件\\n            \"),_c('el-upload',{staticClass:\"upload-demo5\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpXzglRequest,\"show-file-list\":false}},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"primary\"}},[_vm._v(\"上传\")])],1),_vm._v(\" \"),_c('el-button',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.ylgz),expression:\"ylgz\"}],staticStyle:{\"margin-left\":\"10px\"},attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":_vm.ylxzgl}},[_vm._v(\"预览\")]),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogXzglVisible},on:{\"update:visible\":function($event){_vm.dialogXzglVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogXzglImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogXzglVisible = false}}},[_vm._v(\"取 消\")])],1)])],1)])]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"plain\":\"\"},on:{\"click\":_vm.returnIndex}},[_vm._v(\"返回\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.chooseApproval}},[_vm._v(\"保存并提交\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.save}},[_vm._v(\"临时保存\")])],1)],1)],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"选择审批人\",\"close-on-click-modal\":false,\"visible\":_vm.approvalDialogVisible,\"width\":\"40%\",\"destroy-on-close\":true},on:{\"update:visible\":function($event){_vm.approvalDialogVisible=$event}}},[_c('div',{staticClass:\"dlFqsqContainer\"},[_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"部门:\")]),_vm._v(\" \"),_c('el-cascader',{ref:\"cascaderArr\",attrs:{\"options\":_vm.regionOption,\"props\":_vm.regionParams,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.bmSelectChange},model:{value:(_vm.ryChoose.bm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"bm\", $$v)},expression:\"ryChoose.bm\"}}),_vm._v(\" \"),_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"姓名:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"姓名\"},model:{value:(_vm.ryChoose.xm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"xm\", $$v)},expression:\"ryChoose.xm\"}}),_vm._v(\" \"),_c('el-button',{staticClass:\"searchButton\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchRy}},[_vm._v(\"查询\")]),_vm._v(\" \"),_c('BaseTable',{key:_vm.tableKey,staticClass:\"baseTable\",attrs:{\"tableHeight\":'300',\"showIndex\":true,\"tableData\":_vm.ryDatas,\"columns\":_vm.applyColumns,\"showSingleSelection\":true,\"handleColumn\":_vm.handleColumnApply,\"showPagination\":true,\"currentPage\":_vm.page,\"pageSize\":_vm.pageSize,\"totalCount\":_vm.total},on:{\"handleCurrentChange\":_vm.handleCurrentChangeRy,\"handleSizeChange\":_vm.handleSizeChangeRy,\"handleSelectionChange\":_vm.handleSelectionChange}})],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.approvalDialogVisible = false}}},[_vm._v(\"关 闭\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.saveAndSubmit}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-ac285e04\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/lzlgTable.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-ac285e04\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./lzlgTable.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./lzlgTable.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./lzlgTable.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-ac285e04\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./lzlgTable.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-ac285e04\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/lzlgTable.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}