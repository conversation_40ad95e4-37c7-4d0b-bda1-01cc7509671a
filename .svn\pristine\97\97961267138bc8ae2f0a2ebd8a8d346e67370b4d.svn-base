import axios from 'axios'
import {
    Message
} from 'element-ui'
// import store from '@/store'
import router from '../renderer/router'
import store from '../renderer/store'
import {
    updateToken
} from "@/api";
// create an axios instance
// var BASE_URLS = 'http://222.171.203.214:18080'
var instance = axios.create({
    // baseURL: '/api', // api的base_url
    timeout: 60000 // request timeout
})

const ok = "10000";
// 添加请求拦截器

// request interceptor
instance.interceptors.request.use(
    config => {
        // console.log(store.state.Counter.token)
        if (store.state.Counter.token) {
            config.headers['Authorization'] = `${store.state.Counter.token}` // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
        }
        return config
    },
    error => {
        // Do something with request error
        Message.error("对不起，出错了")
        console.log(error) // for debug
        Promise.reject(error)
    }
)

// respone interceptor
instance.interceptors.response.use(
        response => {
            // debugger
            const res = response.data
            const errCode = res.code
            if (errCode !== undefined) {
                // debugger
                // 50008:非法的token; 50012:其他客户端登录了;  50014:Token 过期了;
                if (response.headers.updatetoken == 'update') { //等于true===》  要token
                    store.dispatch('UpdateToken')
                        // let data = updateToken()
                        // console.log(data)
                }

                if (errCode === 10003) {
                    Message({
                            message: '用户名或密码错误,请重新登录',
                            type: 'error',
                            duration: 5 * 1000
                        })
                        // store.dispatch('FedLogOut').then(() => {
                        //   location.reload() // 为了重新实例化vue-router对象 避免bug
                        // })
                    router.push("/login");
                    return Promise.reject(new Error('token expired'))
                } else if (errCode === 10007) {
                    router.push("/login");
                    // if (store.state.route_path != "/login") {
                    // 	this.$store.commit('SET_TOKEN','');
                    //     Message({message: '登录时间超时,请重新登录',type: 'error',duration: 5 * 1000})
                    //     router.push("/login");
                    // }
                } else if (errCode === 10004) {
                    // PubSub.publish('logout','当前用户在别处登录')
                    if (store.state.route_path != "/login") {
                        store.commit('SET_TOKEN', '');
                        router.push("/login");
                        Message({ message: '当前用户在别处登录', type: 'error', duration: 5 * 1000 })
                    }
                }
                // else if (errCode != ok) {
                //      Message({
                //        message: res.message,
                //        type: 'error',
                //        duration: 5 * 1000
                //      })
                //    }
                return response.data;
            } else {
                return response.data
            }
        },
        error => {
            // console.log(error) // for debug
            // Message({
            //   message: error.message,
            //   type: 'error',
            //   duration: 5 * 1000
            // })
            return Promise.reject(error)
        }
    )
    export const BASE_URL = '' // 101部署
    // export const BASE_URL = '/bmgj' //张龙超
// export const BASE_URL = '/api'
export const createUploadAPI = (url, method, data) => {
    let config = {}
    config.data = data
    config.headers = {
        'Content-Type': 'multipart/form-data;charset=UTF-8'
    }
    config.responseType = 'json'

    return instance({
        url,
        method,
        ...config
    })
}
export const createAPI = (url, method, data) => {
    let config = {}
    if (method === 'get') {
        config.params = data
    } else {
        config.data = data
    }
    return instance({
        url,
        method,
        ...config
    })
}

export const createDownloadAPI = (url, method, data) => {
    const config = {};
    if (method === "get") {
        config.params = data;
    } else {
        config.data = data; // 包一层{xx：token，xxx：data}
    }

    config.responseType = "blob"; //接收返回的类型

    return instance({
        url,
        method,
        ...config
    });
};


export const createFormAPI = (url, method, data) => {
    let config = {}
    config.data = data
    config.headers = {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    config.responseType = 'json'
    config.transformRequest = [
        function(data) {
            let ret = ''
            for (let it in data) {
                ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
            }
            return ret
        }
    ]
    return instance({
        url,
        method,
        ...config
    })
}
export const createImgAPI = (url, method, data) => {
        let config = {}
        config.data = data
        config.headers = {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        config.responseType = 'blob'
        config.transformRequest = [
            function(data) {
                let ret = ''
                for (let it in data) {
                    ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
                }
                return ret
            }
        ]
        return instance({
            url,
            method,
            ...config
        })
    }
    // 组织架构导出
export const createFileAPI = (url, method, data) => {
        let config = {}
        config.data = data
        config.headers = {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        config.responseType = 'arraybuffer'
        config.transformRequest = [
            function(data) {
                let ret = ''
                for (let it in data) {
                    ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
                }
                return ret
            }
        ]
        return instance({
            url,
            method,
            ...config
        })
    }
    // // 员工导出
    // export const createDown = (url, method, data) => {
    //   let config = {}
    //   if (method === 'get') {
    //     config.params = data
    //   } else {
    //     config.data = data
    //   }
    //   config.headers = {
    //     'Cache-Control': 'no-cache',
    //     'Content-Type': 'application/x-www-form-urlencoded'
    //   }
    //   config.responseType = 'blob'
    //   return instance({
    //     url,
    //     method,
    //     ...config
    //   })
    // }


// 员工导出
export const createDown = (url, method, data) => {
    let config = {};
    if (method === "get") {
        config.params = data;
    } else {
        config.data = data;
    }
    config.headers = {
        // application/ms-excel;charset=utf-8
        // 'Cache-Control': 'no-cache',
        //'Content-Type': 'application/ms-excel'
    };
    config.responseType = "blob";
    return instance({
        url,
        method,
        ...config
    });
};
