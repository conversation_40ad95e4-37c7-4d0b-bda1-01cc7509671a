webpackJsonp([222],{KMXP:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var a=t("Xxa5"),s=t.n(a),n=t("exGp"),o=t.n(n),i=t("0hE6"),c=(t("L6bb"),t("1clA")),l=t("kCU4"),u=(t("urfq"),{data:function(){var e,r,t,a=this;return{form:{xm:"",yhm:"",passwordOld:"",password:"",passwordCheck:"",oldCode:""},rules:{passwordOld:[{required:!0,validator:(e=o()(s.a.mark(function e(r,t,n){var o;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log(t),!t){e.next=10;break}return o={password:a.form.passwordOld},e.next=5,Object(l.q)(o);case 5:a.oldCode=e.sent,1e4===a.oldCode.code?n():n(new Error("请输入正确的旧密码")),e.next=11;break;case 10:n(new Error("旧密码不能为空"));case 11:case"end":return e.stop()}},e,a)})),function(r,t,a){return e.apply(this,arguments)}),trigger:"blur"}],passwordNew:[{required:!0,validator:(r=o()(s.a.mark(function e(r,t,n){var o;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(o=/(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,16}/,!t){e.next=20;break}if(!(t.length<6)){e.next=7;break}return a.changeFlag=2,e.abrupt("return",n(new Error("密码不能少于6位！")));case 7:if(!(t.length>16)){e.next=12;break}return a.changeFlag=2,e.abrupt("return",n(new Error("密码最长不能超过16位！")));case 12:if(o.test(t)){e.next=16;break}return e.abrupt("return",n(new Error("密码必须同时包含大小写字母、数字和特殊字符且至少6位")));case 16:a.changeFlag=1,n();case 18:e.next=21;break;case 20:n(new Error("新密码不能为空"));case 21:case"end":return e.stop()}},e,a)})),function(e,t,a){return r.apply(this,arguments)}),trigger:"blur"}],passwordCheck:[{required:!0,validator:(t=o()(s.a.mark(function e(r,t,n){return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=15;break}if(!(t.length<1)){e.next=6;break}return a.changeAgainFlag=2,e.abrupt("return",n(new Error("重复密码不能为空！")));case 6:if(a.form.passwordNew==a.form.passwordCheck){e.next=11;break}return a.changeAgainFlag=2,e.abrupt("return",n(new Error("两次输入密码不一致！")));case 11:a.changeAgainFlag=1,n();case 13:e.next=16;break;case 15:n(new Error("请再次输入新密码"));case 16:case"end":return e.stop()}},e,a)})),function(e,r,a){return t.apply(this,arguments)}),trigger:"blur"}]}}},components:{hsoft_top_title:i.a},mounted:function(){this.getUser()},methods:{getUser:function(){var e=this;return o()(s.a.mark(function r(){var t;return s.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,Object(l.j)();case 2:t=r.sent,e.form=t,console.log("获取登陆数据:",e.form);case 5:case"end":return r.stop()}},r,e)}))()},yzjmm:function(e){var r=this;return o()(s.a.mark(function e(){var t;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t={password:r.form.passwordOld},e.next=3,Object(l.q)(t);case 3:r.oldCode=e.sent,console.log("验证旧密码：",r.oldCode);case 5:case"end":return e.stop()}},e,r)}))()},updatePassword:function(){var e=this;return o()(s.a.mark(function r(){return s.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:console.log(e.$refs),e.$refs.form.validate(function(r){if(r){if("root"==e.form.yhm)return void e.$message.warning("超级管理员账号不能修改密码");var t={xm:e.form.xm,newPassword:e.form.passwordNew};if(console.log("修改密码入参",t),Object(l.p)(t))return e.$message.success("修改密码成功"),Object(c.b)(),void e.$router.push("/");e.$message.error("修改密码失败")}});case 2:case"end":return r.stop()}},r,e)}))()}}}),d={render:function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",[t("hsoft_top_title",{scopedSlots:e._u([{key:"left",fn:function(){return[e._v("修改密码")]},proxy:!0}])}),e._v(" "),t("div",{staticClass:"article"},[t("div",[t("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px",size:"mini","label-position":"right",rules:e.rules}},[t("el-form-item",{staticStyle:{position:"relative"},attrs:{label:"姓名",prop:"xm"}},[t("el-input",{attrs:{size:"medium"},model:{value:e.form.xm,callback:function(r){e.$set(e.form,"xm",r)},expression:"form.xm"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"用户名",prop:"yhm"}},[t("el-input",{attrs:{size:"medium",disabled:""},model:{value:e.form.yhm,callback:function(r){e.$set(e.form,"yhm",r)},expression:"form.yhm"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"旧密码",prop:"passwordOld"}},[t("el-input",{attrs:{"show-password":"",size:"medium"},on:{blur:e.yzjmm},model:{value:e.form.passwordOld,callback:function(r){e.$set(e.form,"passwordOld",r)},expression:"form.passwordOld"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"新密码",prop:"passwordNew"}},[t("el-input",{attrs:{"show-password":"",size:"medium"},model:{value:e.form.passwordNew,callback:function(r){e.$set(e.form,"passwordNew",r)},expression:"form.passwordNew"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"确认密码",prop:"passwordCheck"}},[t("el-input",{attrs:{"show-password":"",size:"medium"},model:{value:e.form.passwordCheck,callback:function(r){e.$set(e.form,"passwordCheck",r)},expression:"form.passwordCheck"}})],1)],1),e._v(" "),t("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"flex-end"}},[t("el-button",{attrs:{type:"primary"},on:{click:e.updatePassword}},[e._v("保 存")]),e._v(" "),t("el-button",{attrs:{type:"warning"},on:{click:function(r){return e.$router.go(-1)}}},[e._v("返 回")])],1)],1)])],1)},staticRenderFns:[]};var p=t("VU/8")(u,d,!1,function(e){t("UTnl")},"data-v-2468b7e7",null);r.default=p.exports},UTnl:function(e,r){}});
//# sourceMappingURL=222.c50caa7f05f1ee5e4e59.js.map