webpackJsonp([215],{"41k/":function(t,e,u){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n={data:function(){return{}},computed:{},methods:{tabsCode:function(){console.log(this.$route.query.activeName),this.$route.query.activeName?this.$router.push(this.$route.query.activeName):this.$router.push("/lsSmgwgl")}},watch:{},mounted:function(){this.tabsCode()}},r={render:function(){var t=this.$createElement;return(this._self._c||t)("div")},staticRenderFns:[]};var o=u("VU/8")(n,r,!1,function(t){u("UR46")},"data-v-276faa7c",null);e.default=o.exports},UR46:function(t,e){}});
//# sourceMappingURL=215.b7754389dfe6a0ed2405.js.map