{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/smsb/sbbfspTable.vue", "webpack:///./src/renderer/view/rcgz/smsb/sbbfspTable.vue?676a", "webpack:///./src/renderer/view/rcgz/smsb/sbbfspTable.vue"], "names": ["sbbfspTable", "components", "AddLineTable", "BaseTable", "baseTable", "props", "data", "table<PERSON><PERSON>", "value1", "loading", "ry<PERSON><PERSON>ose", "bm", "xm", "gwmclist", "smdjxz", "regionOption", "page", "pageSize", "radioIdSelect", "ryDatas", "total", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "headerCellStyle", "background", "color", "tjlist", "sm<PERSON><PERSON>", "xqr", "szbm", "bfrq", "sbGlSpList", "bfyy", "cqcs", "zzqx", "checkList", "gpRadio", "upRadio", "zjzRadio", "drsbList", "drid", "sblb", "sfybmbh", "bmbh", "yxq", "checked", "smdjList", "smdjid", "smdjmc", "ryInfo", "sltshow", "routeType", "pdfBase64", "fileList", "dialogImageUrl", "dialogVisible", "approvalDialogVisible", "fileRow", "applyColumns", "name", "prop", "scopeType", "formatter", "handleColumnApply", "scqk", "sfty", "id", "disabled2", "rydialogVisible", "formInlinery", "table1Data", "table2Data", "restaurants", "computed", "mounted", "this", "dqlogin", "onfwid", "smdj", "gwxx", "rydata", "smry", "getOrganization", "console", "log", "$route", "query", "datas", "type", "yhDatas", "ztqs", "split", "for<PERSON>ach", "item", "mj", "methods", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "Object", "dwzc", "sent", "bmmc", "stop", "handleChange", "index", "_this2", "_callee2", "resList", "params", "_context2", "join", "api", "querySearch", "queryString", "cb", "results", "filter", "createFilter", "restaurant", "toLowerCase", "indexOf", "_this3", "_callee3", "_context3", "zxfw", "_this4", "_callee4", "param", "list", "_context4", "bmid", "bmm", "chRadio", "_this5", "_callee5", "_context5", "qblist", "_this6", "_callee6", "_context6", "xlxz", "handleSelectionChange", "row", "_this7", "_callee7", "_context7", "fwlx", "fwdyid", "jyxx", "undefined", "$message", "error", "length", "save", "_this8", "_callee8", "res", "szbmArr", "resDatas", "_resDatas", "_context8", "abrupt", "lcslclzt", "push", "j<PERSON>", "code", "slid", "JSON", "parse", "stringify_default", "sbbf", "splx", "yj<PERSON>", "sbjlid", "$router", "message", "_this9", "_callee9", "zzjgList", "shu", "shuList", "_context9", "zzjgmc", "childrenRegionVo", "item1", "fbmm", "handleSelectionChange1", "handleCurrentChangeRy", "val", "chooseApproval", "handleSizeChangeRy", "searchRy", "bmSelectChange", "_this10", "_callee10", "resData", "_context10", "records", "saveAndSubmit", "_this11", "_callee11", "_res", "_params", "_resDatas2", "_context11", "keys_default", "clrid", "yhid", "returnIndex", "watch", "smsb_sbbfspTable", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "_v", "ref", "attrs", "model", "label-width", "scopedSlots", "_u", "key", "fn", "scope", "staticStyle", "width", "options", "filterable", "clearable", "on", "change", "$event", "callback", "$$v", "$set", "value-key", "fetch-suggestions", "placeholder", "trim", "format", "value-format", "border", "header-cell-style", "stripe", "align", "plain", "click", "title", "close-on-click-modal", "visible", "destroy-on-close", "update:visible", "for", "icon", "tableHeight", "showIndex", "tableData", "columns", "showSingleSelection", "handleColumn", "showPagination", "currentPage", "totalCount", "handleCurrentChange", "handleSizeChange", "slot", "clear", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "ySAuIAA,GACAC,YACAC,uBAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAOA,OACAC,SAAA,EACAC,OAAA,GACAC,SAAA,EAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,YACAC,UACAC,gBACAC,KAAA,EACAC,SAAA,GACAC,cAAA,GACAC,WACAC,MAAA,EACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,iBACAC,WAAA,UACAC,MAAA,WAGAC,QACAC,OAAA,GACAC,IAAA,GACAC,QACAC,KAAA,GAEAC,cAEAC,KAAA,GACAC,KAAA,GACAC,KAAA,IAEAC,aACAC,SAAA,EACAC,SAAA,EACAC,UAAA,EACAC,WAEAC,KAAA,EACAC,KAAA,QACAC,SAAA,EACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAL,KAAA,EACAC,KAAA,SACAC,SAAA,EACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAL,KAAA,EACAC,KAAA,WACAC,SAAA,EACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAL,KAAA,EACAC,KAAA,UACAC,SAAA,EACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAL,KAAA,EACAC,KAAA,OACAC,SAAA,EACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAL,KAAA,EACAC,KAAA,MACAC,SAAA,EACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAGAL,KAAA,EACAC,KAAA,KACAC,SAAA,EACAC,KAAA,GACAC,IAAA,GACAC,QAAA,IAIAd,cACAe,WAEAC,OAAA,IACAC,OAAA,OAGAD,OAAA,IACAC,OAAA,OAGAD,OAAA,IACAC,OAAA,OAGAD,OAAA,IACAC,OAAA,OAGAC,UAEAC,QAAA,GACAC,UAAA,GACAC,UAAA,GACAC,YACAC,eAAA,GACAC,eAAA,EACAC,uBAAA,EACAC,QAAA,GAEAC,eACAC,KAAA,KACAC,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAC,qBACAC,OAEAC,KAAA,KACAC,GAAA,IAGAD,KAAA,MACAC,GAAA,IAGAC,WAAA,EAEAC,iBAAA,EACAC,cACA9D,GAAA,IAEA+D,cACAC,cACAC,iBAGAC,YAGAC,QA1LA,WA2LAC,KAAAC,UACAD,KAAAE,SACAF,KAAAG,OACAH,KAAAI,OACAJ,KAAAK,SACAL,KAAAM,OACAN,KAAAO,kBACAC,QAAAC,IAAAT,KAAAU,OAAAC,MAAAC,MAAA,2BACAZ,KAAAxB,UAAAwB,KAAAU,OAAAC,MAAAE,KACAb,KAAAc,QAAAd,KAAAU,OAAAC,MAAAC,MACA,UAAAZ,KAAAxB,WACAwB,KAAAjD,OAAAiD,KAAAU,OAAAC,MAAAC,MACAZ,KAAA5C,WAAA4C,KAAAU,OAAAC,MAAAI,KACAf,KAAAjD,OAAAG,KAAA8C,KAAAjD,OAAAG,KAAA8D,MAAA,MAGAhB,KAAA5C,WAAA4C,KAAAU,OAAAC,MAAAC,MAEAZ,KAAA5C,WAAA6D,QAAA,SAAAC,GACAV,QAAAC,IAAAS,GACA,GAAAA,EAAAC,GACAD,EAAAC,GAAA,KACA,GAAAD,EAAAC,GACAD,EAAAC,GAAA,KACA,GAAAD,EAAAC,GACAD,EAAAC,GAAA,KACA,GAAAD,EAAAC,KACAD,EAAAC,GAAA,SAIAC,SACAnB,QADA,WACA,IAAAoB,EAAArB,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAnG,EAAA,OAAAgG,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAxG,EADAqG,EAAAK,KAEAZ,EAAAtE,OAAAG,KAAA3B,EAAA2G,KAAAlB,MAAA,KACAK,EAAAtE,OAAAE,IAAA1B,EAAAM,GAHA,wBAAA+F,EAAAO,SAAAT,EAAAL,KAAAC,IAKAc,aANA,SAMAC,GAAA,IAAAC,EAAAtC,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAAc,IAAA,IAAAC,EAAAC,EAAA,OAAAlB,EAAAC,EAAAG,KAAA,SAAAe,GAAA,cAAAA,EAAAb,KAAAa,EAAAZ,MAAA,cACAU,OADA,EAEAC,OAFA,EAKAA,GACAP,KAAAI,EAAAvF,OAAAG,KAAAyF,KAAA,MANAD,EAAAZ,KAAA,EAQAC,OAAAa,EAAA,EAAAb,CAAAU,GARA,OAQAD,EARAE,EAAAT,KASAK,EAAAvF,OAAAE,IAAA,GAQAqF,EAAAzC,YAAA2C,EAjBA,wBAAAE,EAAAP,SAAAI,EAAAD,KAAAhB,IAoBAuB,YA1BA,SA0BAC,EAAAC,GACA,IAAAlD,EAAAG,KAAAH,YACAW,QAAAC,IAAA,cAAAZ,GACA,IAAAmD,EAAAF,EAAAjD,EAAAoD,OAAAjD,KAAAkD,aAAAJ,IAAAjD,EACAW,QAAAC,IAAA,UAAAuC,GAEAD,EAAAC,GACAxC,QAAAC,IAAA,mBAAAuC,IAEAE,aAnCA,SAmCAJ,GACA,gBAAAK,GACA,OAAAA,EAAAtH,GAAAuH,cAAAC,QAAAP,EAAAM,gBAAA,IAGA9C,KAxCA,WAwCA,IAAAgD,EAAAtD,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAA8B,IAAA,OAAAhC,EAAAC,EAAAG,KAAA,SAAA6B,GAAA,cAAAA,EAAA3B,KAAA2B,EAAA1B,MAAA,cAAA0B,EAAA1B,KAAA,EACAC,OAAAa,EAAA,EAAAb,GADA,OACAuB,EAAAzD,YADA2D,EAAAvB,KAAA,wBAAAuB,EAAArB,SAAAoB,EAAAD,KAAAhC,IAIAmC,KA5CA,WA6CAzD,KAAAP,iBAAA,GAEAY,OA/CA,WA+CA,IAAAqD,EAAA1D,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAAkC,IAAA,IAAAC,EAAAC,EAAA,OAAAtC,EAAAC,EAAAG,KAAA,SAAAmC,GAAA,cAAAA,EAAAjC,KAAAiC,EAAAhC,MAAA,cACA8B,GACAG,KAAAL,EAAAM,KAFAF,EAAAhC,KAAA,EAIAC,OAAAa,EAAA,EAAAb,CAAA6B,GAJA,OAIAC,EAJAC,EAAA7B,KAKAyB,EAAA/D,WAAAkE,EALA,wBAAAC,EAAA3B,SAAAwB,EAAAD,KAAApC,IAOA2C,QAtDA,aAuDA7D,KAvDA,WAuDA,IAAA8D,EAAAlE,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAA0C,IAAA,IAAAP,EAAArI,EAAA,OAAAgG,EAAAC,EAAAG,KAAA,SAAAyC,GAAA,cAAAA,EAAAvC,KAAAuC,EAAAtC,MAAA,cACA8B,GACA1B,KAAAgC,EAAAnH,OAAAmF,MAFAkC,EAAAtC,KAAA,EAIAC,OAAAsC,EAAA,EAAAtC,CAAA6B,GAJA,OAIArI,EAJA6I,EAAAnC,KAKAiC,EAAApI,SAAAP,EACAiF,QAAAC,IAAAlF,GANA,wBAAA6I,EAAAjC,SAAAgC,EAAAD,KAAA5C,IASAnB,KAhEA,WAgEA,IAAAmE,EAAAtE,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAA8C,IAAA,IAAAhJ,EAAA,OAAAgG,EAAAC,EAAAG,KAAA,SAAA6C,GAAA,cAAAA,EAAA3C,KAAA2C,EAAA1C,MAAA,cAAA0C,EAAA1C,KAAA,EACAC,OAAA0C,EAAA,EAAA1C,GADA,OACAxG,EADAiJ,EAAAvC,KAEAqC,EAAAvI,OAAAR,EAFA,wBAAAiJ,EAAArC,SAAAoC,EAAAD,KAAAhD,IAKAoD,sBArEA,SAqEArC,EAAAsC,GACA3E,KAAA7D,cAAAwI,GAEAzE,OAxEA,WAwEA,IAAA0E,EAAA5E,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAAoD,IAAA,IAAApC,EAAAlH,EAAA,OAAAgG,EAAAC,EAAAG,KAAA,SAAAmD,GAAA,cAAAA,EAAAjD,KAAAiD,EAAAhD,MAAA,cACAW,GACAsC,KAAA,IAFAD,EAAAhD,KAAA,EAIAC,OAAAa,EAAA,EAAAb,CAAAU,GAJA,OAIAlH,EAJAuJ,EAAA7C,KAKAzB,QAAAC,IAAAlF,GACAqJ,EAAAI,OAAAzJ,OAAAyJ,OANA,wBAAAF,EAAA3C,SAAA0C,EAAAD,KAAAtD,IAQA2D,KAhFA,WAiFA,UAAAjF,KAAAjD,OAAAE,UAAAiI,GAAAlF,KAAAjD,OAAAE,KACA+C,KAAAmF,SAAAC,MAAA,WACA,GAEA,GAAApF,KAAAjD,OAAAG,KAAAmI,aAAAH,GAAAlF,KAAAjD,OAAAG,MACA8C,KAAAmF,SAAAC,MAAA,YACA,GAEA,IAAApF,KAAAjD,OAAAI,WAAA+H,GAAAlF,KAAAjD,OAAAI,MACA6C,KAAAmF,SAAAC,MAAA,YACA,GAEA,IAAApF,KAAAjD,OAAAM,WAAA6H,GAAAlF,KAAAjD,OAAAM,MACA2C,KAAAmF,SAAAC,MAAA,YACA,GAEA,IAAApF,KAAAjD,OAAAO,WAAA4H,GAAAlF,KAAAjD,OAAAO,MACA0C,KAAAmF,SAAAC,MAAA,YACA,GAEA,IAAApF,KAAAjD,OAAAQ,WAAA2H,GAAAlF,KAAAjD,OAAAQ,MACAyC,KAAAmF,SAAAC,MAAA,YACA,QAFA,GAMAE,KA3GA,WA2GA,IAAAC,EAAAvF,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAA+D,IAAA,IAAA5B,EAAArE,EAAAkG,EAAAhD,EAAAiD,EAAAC,EAAAC,EAAA,OAAArE,EAAAC,EAAAG,KAAA,SAAAkE,GAAA,cAAAA,EAAAhE,KAAAgE,EAAA/D,MAAA,WACAyD,EAAAN,OADA,CAAAY,EAAA/D,KAAA,eAAA+D,EAAAC,OAAA,wBAIAlC,GACAoB,OAAAO,EAAAP,OACAe,SAAA,GAEAxG,KACAgG,EAAAnI,WAAA6D,QAAA,SAAAC,GACA3B,EAAAyG,KAAA9E,EAAA+E,QAEArC,EAAA5G,OAAAuC,EAAAoD,KAAA,KAZAkD,EAAA/D,KAAA,EAaAC,OAAAa,EAAA,EAAAb,CAAA6B,GAbA,UAcA,MADA6B,EAbAI,EAAA5D,MAcAiE,KAdA,CAAAL,EAAA/D,KAAA,YAeAyD,EAAAxI,OAAAoJ,KAAAV,EAAAlK,KAAA4K,KACA1D,EAAA8C,EAAAxI,OACAwI,EAAAnI,WAAA6D,QAAA,SAAAC,GACA,MAAAA,EAAAC,GACAD,EAAAC,GAAA,EACA,MAAAD,EAAAC,GACAD,EAAAC,GAAA,EACA,MAAAD,EAAAC,GACAD,EAAAC,GAAA,EACA,MAAAD,EAAAC,KACAD,EAAAC,GAAA,KAGAuE,EAAAU,KAAAC,MAAAC,IAAAf,EAAAxI,OAAAG,OAEAqI,EAAAxI,OAAAG,KAAAwI,EAAA/C,KAAA,KAEAnC,QAAAC,IAAA8E,EAAAxI,QACA,UAAAwI,EAAA/G,UAjCA,CAAAqH,EAAA/D,KAAA,gBAAA+D,EAAA/D,KAAA,GAmCAC,OAAAwE,EAAA,EAAAxE,CAAAU,GAnCA,WAoCA,MADAkD,EAnCAE,EAAA5D,MAoCAiE,KApCA,CAAAL,EAAA/D,KAAA,gBAqCAyD,EAAAnI,WAAA6D,QAAA,SAAAC,GACAA,EAAAsF,KAAA,EACAtF,EAAAuF,MAAAd,EAAApK,KACA2F,EAAAwF,OAAAxF,EAAA+E,OAxCAJ,EAAA/D,KAAA,GA0CAC,OAAAwE,EAAA,EAAAxE,EAAA0E,MAAAlB,EAAAxI,OAAAkJ,OA1CA,WA2CA,KA3CAJ,EAAA5D,KA2CAiE,KA3CA,CAAAL,EAAA/D,KAAA,gBAAA+D,EAAA/D,KAAA,GA4CAC,OAAAwE,EAAA,EAAAxE,CAAAwD,EAAAnI,YA5CA,QA6CA,KA7CAyI,EAAA5D,KA6CAiE,OACAX,EAAAoB,QAAAX,KAAA,WACAT,EAAAJ,UACAyB,QAAA,OACA/F,KAAA,aAjDA,QAAAgF,EAAA/D,KAAA,wBAAA+D,EAAA/D,KAAA,GAuDAC,OAAAwE,EAAA,EAAAxE,CAAAU,GAvDA,WAwDA,MADAmD,EAvDAC,EAAA5D,MAwDAiE,KAxDA,CAAAL,EAAA/D,KAAA,gBA0DAyD,EAAAnI,WAAA6D,QAAA,SAAAC,GACAA,EAAAsF,KAAA,EACAtF,EAAAuF,MAAAb,EAAArK,KACA2F,EAAAwF,OAAAxF,EAAA+E,OA7DAJ,EAAA/D,KAAA,GA+DAC,OAAAwE,EAAA,EAAAxE,CAAAwD,EAAAnI,YA/DA,QAgEA,KAhEAyI,EAAA5D,KAgEAiE,MACAX,EAAAoB,QAAAX,KAAA,WACAT,EAAAJ,UACAyB,QAAA,OACA/F,KAAA,aAGAkB,OAAAa,EAAA,EAAAb,EAAAoE,KAAAV,EAAAlK,KAAA4K,OAvEA,yBAAAN,EAAA1D,SAAAqD,EAAAD,KAAAjE,IAgFAf,gBA3LA,WA2LA,IAAAsG,EAAA7G,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAAqF,IAAA,IAAAC,EAAAC,EAAAC,EAAApD,EAAA,OAAAtC,EAAAC,EAAAG,KAAA,SAAAuF,GAAA,cAAAA,EAAArF,KAAAqF,EAAApF,MAAA,cAAAoF,EAAApF,KAAA,EACAC,OAAAa,EAAA,IAAAb,GADA,cACAgF,EADAG,EAAAjF,KAEA4E,EAAAM,OAAAJ,EACAC,KACAH,EAAAM,OAAAlG,QAAA,SAAAC,GACA,IAAAkG,KACAP,EAAAM,OAAAlG,QAAA,SAAAoG,GACAnG,EAAA8C,KAAAqD,EAAAC,OACAF,EAAApB,KAAAqB,GACAnG,EAAAkG,sBAGAJ,EAAAhB,KAAA9E,KAEA+F,KAdAC,EAAApF,KAAA,EAeAC,OAAAa,EAAA,EAAAb,GAfA,OAgBA,KADA8B,EAfAqD,EAAAjF,MAgBAqF,MACAN,EAAA/F,QAAA,SAAAC,GACA,IAAAA,EAAAoG,MACAL,EAAAjB,KAAA9E,KAIA,IAAA2C,EAAAyD,MACAN,EAAA/F,QAAA,SAAAC,GACAV,QAAAC,IAAAS,GACAA,EAAAoG,MAAAzD,EAAAyD,MACAL,EAAAjB,KAAA9E,KAIA+F,EAAA,GAAAG,iBAAAnG,QAAA,SAAAC,GACA2F,EAAA7K,aAAAgK,KAAA9E,KAhCA,yBAAAgG,EAAA/E,SAAA2E,EAAAD,KAAAvF,IAmCAiG,uBA9NA,SA8NAlF,EAAAsC,GACA3E,KAAA7D,cAAAwI,GAEA6C,sBAjOA,SAiOAC,GACAzH,KAAA/D,KAAAwL,EACAzH,KAAA0H,kBAGAC,mBAtOA,SAsOAF,GACAzH,KAAA/D,KAAA,EACA+D,KAAA9D,SAAAuL,EACAzH,KAAA0H,kBAGAE,SA5OA,WA6OA5H,KAAAxE,WACAwE,KAAA0H,kBAGAG,eAjPA,SAiPA3G,QACAgE,GAAAhE,IACAlB,KAAArE,SAAAC,GAAAsF,EAAAyB,KAAA,OAIA+E,eAvPA,WAuPA,IAAAI,EAAA9H,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAAsG,IAAA,IAAAnE,EAAAoE,EAAA,OAAAzG,EAAAC,EAAAG,KAAA,SAAAsG,GAAA,cAAAA,EAAApG,KAAAoG,EAAAnG,MAAA,cAEAgG,EAAAjJ,uBAAA,EACA+E,GACA3H,KAAA6L,EAAA7L,KACAC,SAAA4L,EAAA5L,SACA8I,OAAA8C,EAAA9C,OACA9C,KAAA4F,EAAAnM,SAAAC,GACAC,GAAAiM,EAAAnM,SAAAE,IARAoM,EAAAnG,KAAA,EAUAC,OAAAa,EAAA,GAAAb,CAAA6B,GAVA,QAUAoE,EAVAC,EAAAhG,MAWAiG,SACAJ,EAAA1L,QAAA4L,EAAAE,QACAJ,EAAAzL,MAAA2L,EAAA3L,OAEAyL,EAAA3C,SAAAC,MAAA,WAfA,wBAAA6C,EAAA9F,SAAA4F,EAAAD,KAAAxG,IAmBA6G,cA1QA,WA0QA,IAAAC,EAAApI,KAAA,OAAAsB,IAAAC,EAAAC,EAAAC,KAAA,SAAA4G,IAAA,IAAAzE,EAAArE,EAAAmG,EAAAD,EAAAhD,EAAAkD,EAAA2C,EAAAC,EAAAC,EAAA,OAAAjH,EAAAC,EAAAG,KAAA,SAAA8G,GAAA,cAAAA,EAAA5G,KAAA4G,EAAA3G,MAAA,YACA,IAAAsG,EAAAjM,eAAAuM,IAAAN,EAAAjM,eAAAkJ,OAAA,GADA,CAAAoD,EAAA3G,KAAA,aAEAsG,EAAAnD,OAFA,CAAAwD,EAAA3G,KAAA,eAAA2G,EAAA3C,OAAA,oBAKAlC,GACAoB,OAAAoD,EAAApD,QAEAzF,KACA6I,EAAAhL,WAAA6D,QAAA,SAAAC,GACA3B,EAAAyG,KAAA9E,EAAA+E,QAEArC,EAAA5G,OAAAuC,EAAAoD,KAAA,KACA+C,EAAAU,KAAAC,MAAAC,IAAA8B,EAAArL,OAAAG,OAEAkL,EAAArL,OAAAG,KAAAwI,EAAA/C,KAAA,KACAyF,EAAAhL,WAAA6D,QAAA,SAAAC,GACA,MAAAA,EAAAC,GACAD,EAAAC,GAAA,EACA,MAAAD,EAAAC,GACAD,EAAAC,GAAA,EACA,MAAAD,EAAAC,GACAD,EAAAC,GAAA,EACA,MAAAD,EAAAC,KACAD,EAAAC,GAAA,KAIA,UAAAiH,EAAA5J,UA5BA,CAAAiK,EAAA3G,KAAA,gBA6BA8B,EAAAmC,SAAA,EACAnC,EAAAuC,KAAAiC,EAAArL,OAAAoJ,KACAvC,EAAA+E,MAAAP,EAAAjM,cAAAyM,KA/BAH,EAAA3G,KAAA,GAgCAC,OAAAa,EAAA,EAAAb,CAAA6B,GAhCA,WAiCA,MADA6B,EAhCAgD,EAAAxG,MAiCAiE,KAjCA,CAAAuC,EAAA3G,KAAA,gBAkCAsG,EAAArL,OAAAoJ,KAAAV,EAAAlK,KAAA4K,KACA1D,EAAA2F,EAAArL,OACAyD,QAAAC,IAAA2H,EAAArL,QApCA0L,EAAA3G,KAAA,GAsCAC,OAAAwE,EAAA,EAAAxE,CAAAU,GAtCA,WAuCA,MADAkD,EAtCA8C,EAAAxG,MAuCAiE,KAvCA,CAAAuC,EAAA3G,KAAA,gBAwCAsG,EAAAhL,WAAA6D,QAAA,SAAAC,GACAA,EAAAsF,KAAA,EACAtF,EAAAwF,OAAAxF,EAAA+E,KACA/E,EAAAuF,MAAAd,EAAApK,OA3CAkN,EAAA3G,KAAA,GA6CAC,OAAAwE,EAAA,EAAAxE,EAAA0E,MAAA2B,EAAArL,OAAAkJ,OA7CA,WA8CA,KA9CAwC,EAAAxG,KA8CAiE,KA9CA,CAAAuC,EAAA3G,KAAA,gBAAA2G,EAAA3G,KAAA,GA+CAC,OAAAwE,EAAA,EAAAxE,CAAAqG,EAAAhL,YA/CA,QAgDA,KAhDAqL,EAAAxG,KAgDAiE,OACAkC,EAAAzB,QAAAX,KAAA,WACAoC,EAAAjD,UACAyB,QAAA,OACA/F,KAAA,aApDA,QAAA4H,EAAA3G,KAAA,wBA2DA8B,EAAAmC,SAAA,EACAnC,EAAA+E,MAAAP,EAAAjM,cAAAyM,KA5DAH,EAAA3G,KAAA,GA6DAC,OAAAa,EAAA,EAAAb,CAAA6B,GA7DA,WA8DA,MADA0E,EA7DAG,EAAAxG,MA8DAiE,KA9DA,CAAAuC,EAAA3G,KAAA,gBA+DAsG,EAAArL,OAAAoJ,KAAAmC,EAAA/M,KAAA4K,KACAoC,EAAAH,EAAArL,OACAyD,QAAAC,IAAA2H,EAAArL,QAjEA0L,EAAA3G,KAAA,GAkEAC,OAAAwE,EAAA,EAAAxE,CAAAwG,GAlEA,WAmEA,MADAC,EAlEAC,EAAAxG,MAmEAiE,KAnEA,CAAAuC,EAAA3G,KAAA,gBAoEAsG,EAAAhL,WAAA6D,QAAA,SAAAC,GACAA,EAAAsF,KAAA,EACAtF,EAAAuF,MAAA+B,EAAAjN,KACA2F,EAAAwF,OAAAxF,EAAA+E,OAvEAwC,EAAA3G,KAAA,GAyEAC,OAAAwE,EAAA,EAAAxE,CAAAqG,EAAAhL,YAzEA,QA0EA,KA1EAqL,EAAAxG,KA0EAiE,MACAkC,EAAAzB,QAAAX,KAAA,WACAoC,EAAAjD,UACAyB,QAAA,OACA/F,KAAA,aAGAkB,OAAAa,EAAA,EAAAb,EAAAoE,KAAAmC,EAAA/M,KAAA4K,OAjFA,QAAAsC,EAAA3G,KAAA,iBAuFAsG,EAAAjD,UACAyB,QAAA,SACA/F,KAAA,YAzFA,yBAAA4H,EAAAtG,SAAAkG,EAAAD,KAAA9G,IA8FAuH,YAxWA,WAyWA7I,KAAA2G,QAAAX,KAAA,aAGA8C,UC1sBeC,GADEC,OAFjB,WAA0B,IAAAC,EAAAjJ,KAAakJ,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAatK,KAAA,UAAAuK,QAAA,YAAA/M,MAAAyM,EAAA,QAAAO,WAAA,YAA4EC,YAAA,kBAA8BL,EAAA,KAAUK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,uBAAiCL,EAAA,WAAgBO,IAAA,WAAAC,OAAsBC,MAAAZ,EAAAlM,OAAA+M,cAAA,WAA0CV,EAAA,OAAYK,YAAA,uBAAiCL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOrN,MAAA,QAAewN,YAAAd,EAAAe,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAf,EAAA,eAA0BO,IAAA,cAAAS,aAA+BC,MAAA,QAAeT,OAAQU,QAAArB,EAAAjN,aAAAV,MAAA2N,EAAA3M,aAAAiO,WAAA,GAAAC,UAAA,IAAmFC,IAAKC,OAAA,SAAAC,GAA0B,OAAA1B,EAAA7G,aAAA,KAA4ByH,OAAQrN,MAAAyM,EAAAlM,OAAA,KAAA6N,SAAA,SAAAC,GAAiD5B,EAAA6B,KAAA7B,EAAAlM,OAAA,OAAA8N,IAAkCrB,WAAA,wBAAkCP,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAOrN,MAAA,SAAe6M,EAAA,mBAAwBK,YAAA,eAAAW,aAAwCC,MAAA,QAAeT,OAAQmB,YAAA,KAAAC,oBAAA/B,EAAApG,YAAAoI,YAAA,UAA4EpB,OAAQrN,MAAAyM,EAAAlM,OAAA,IAAA6N,SAAA,SAAAC,GAAgD5B,EAAA6B,KAAA7B,EAAAlM,OAAA,uBAAA8N,IAAAK,OAAAL,IAAwErB,WAAA,iBAA0B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOrN,MAAA,UAAgB6M,EAAA,kBAAuBK,YAAA,MAAAG,OAAyB/I,KAAA,OAAAoK,YAAA,OAAAE,OAAA,aAAAC,eAAA,cAAqFvB,OAAQrN,MAAAyM,EAAAlM,OAAA,KAAA6N,SAAA,SAAAC,GAAiD5B,EAAA6B,KAAA7B,EAAAlM,OAAA,OAAA8N,IAAkCrB,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,yCAAmDL,EAAA,gBAAqBQ,OAAOrN,MAAA,UAAgB6M,EAAA,YAAiBQ,OAAOqB,YAAA,GAAApK,KAAA,WAAA2J,UAAA,IAAkDX,OAAQrN,MAAAyM,EAAAlM,OAAA,KAAA6N,SAAA,SAAAC,GAAiD5B,EAAA6B,KAAA7B,EAAAlM,OAAA,OAAA8N,IAAkCrB,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,yCAAmDL,EAAA,gBAAqBQ,OAAOrN,MAAA,UAAgB6M,EAAA,YAAiBQ,OAAOqB,YAAA,GAAApK,KAAA,WAAA2J,UAAA,IAAkDX,OAAQrN,MAAAyM,EAAAlM,OAAA,KAAA6N,SAAA,SAAAC,GAAiD5B,EAAA6B,KAAA7B,EAAAlM,OAAA,OAAA8N,IAAkCrB,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,yCAAmDL,EAAA,gBAAqBQ,OAAOrN,MAAA,UAAgB6M,EAAA,YAAiBQ,OAAOqB,YAAA,GAAApK,KAAA,WAAA2J,UAAA,IAAkDX,OAAQrN,MAAAyM,EAAAlM,OAAA,KAAA6N,SAAA,SAAAC,GAAiD5B,EAAA6B,KAAA7B,EAAAlM,OAAA,OAAA8N,IAAkCrB,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,KAA8BK,YAAA,cAAwBR,EAAAS,GAAA,cAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAkDK,YAAA,eAAAG,OAAkCyB,OAAA,GAAA9P,KAAA0N,EAAA7L,WAAAkO,qBAAuDzO,WAAA,UAAAC,MAAA,WAA0CyO,OAAA,MAAcnC,EAAA,mBAAwBQ,OAAO/I,KAAA,QAAAwJ,MAAA,KAAA9N,MAAA,KAAAiP,MAAA,YAA2DvC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,OAAA1C,MAAA,YAAgC0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,SAAA1C,MAAA,YAAkC0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,KAAA1C,MAAA,QAA0B0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,KAAA1C,MAAA,UAA4B0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,OAAA1C,MAAA,UAA8B0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,OAAA1C,MAAA,UAA8B0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,QAAA1C,MAAA,WAAgC0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,QAAA1C,MAAA,WAAgC0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,MAAA1C,MAAA,SAA4B0M,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO3K,KAAA,OAAA1C,MAAA,WAA8B,OAAA0M,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,0CAAoDL,EAAA,aAAkBK,YAAA,UAAAG,OAA6B6B,MAAA,IAAWhB,IAAKiB,MAAAzC,EAAAJ,eAAyBI,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CK,YAAA,KAAAG,OAAwB/I,KAAA,WAAiB4J,IAAKiB,MAAAzC,EAAAvB,kBAA4BuB,EAAAS,GAAA,WAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAgDK,YAAA,KAAAG,OAAwB/I,KAAA,WAAiB4J,IAAKiB,MAAAzC,EAAA3D,QAAkB2D,EAAAS,GAAA,oBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAyDQ,OAAO+B,MAAA,QAAAC,wBAAA,EAAAC,QAAA5C,EAAApK,sBAAAwL,MAAA,MAAAyB,oBAAA,GAAuHrB,IAAKsB,iBAAA,SAAApB,GAAkC1B,EAAApK,sBAAA8L,MAAmCvB,EAAA,OAAYK,YAAA,oBAA8BL,EAAA,SAAcQ,OAAOoC,IAAA,MAAU/C,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,eAAgDO,IAAA,cAAAC,OAAyBU,QAAArB,EAAAjN,aAAAV,MAAA2N,EAAA3M,aAAAiO,WAAA,GAAAC,UAAA,IAAmFC,IAAKC,OAAAzB,EAAApB,gBAA4BgC,OAAQrN,MAAAyM,EAAAtN,SAAA,GAAAiP,SAAA,SAAAC,GAAiD5B,EAAA6B,KAAA7B,EAAAtN,SAAA,KAAAkP,IAAkCrB,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,SAA0BQ,OAAOoC,IAAA,MAAU/C,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA6CK,YAAA,SAAAG,OAA4BY,UAAA,GAAAS,YAAA,MAAkCpB,OAAQrN,MAAAyM,EAAAtN,SAAA,GAAAiP,SAAA,SAAAC,GAAiD5B,EAAA6B,KAAA7B,EAAAtN,SAAA,KAAAkP,IAAkCrB,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,aAA8BK,YAAA,eAAAG,OAAkC/I,KAAA,UAAAoL,KAAA,kBAAyCxB,IAAKiB,MAAAzC,EAAArB,YAAsBqB,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6Ca,IAAAhB,EAAAzN,SAAAiO,YAAA,YAAAG,OAAgDsC,YAAA,MAAAC,WAAA,EAAAC,UAAAnD,EAAA7M,QAAAiQ,QAAApD,EAAAlK,aAAAuN,qBAAA,EAAAC,aAAAtD,EAAA7J,kBAAAoN,gBAAA,EAAAC,YAAAxD,EAAAhN,KAAAC,SAAA+M,EAAA/M,SAAAwQ,WAAAzD,EAAA5M,OAAoPoO,IAAKkC,oBAAA1D,EAAAzB,sBAAAoF,iBAAA3D,EAAAtB,mBAAAjD,sBAAAuE,EAAAvE,0BAA6I,GAAAuE,EAAAS,GAAA,KAAAN,EAAA,QAA6BK,YAAA,gBAAAG,OAAmCiD,KAAA,UAAgBA,KAAA,WAAezD,EAAA,aAAkBK,YAAA,UAAAG,OAA6B/I,KAAA,WAAiB4J,IAAKiB,MAAA,SAAAf,GAAyB1B,EAAApK,uBAAA,MAAoCoK,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA8CK,YAAA,KAAAG,OAAwB/I,KAAA,WAAiB4J,IAAKiB,MAAAzC,EAAAd,iBAA2Bc,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCgB,aAAa0C,MAAA,WAAgB,UAEh4LC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEjS,EACA8N,GATF,EAVA,SAAAoE,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/84.822d0ea8bcc0861c7590.js", "sourcesContent": ["<template>\r\n    <div class=\"sec-container\" v-loading=\"loading\">\r\n        <!-- 标题 -->\r\n        <p class=\"sec-title\">基本信息</p>\r\n        <div class=\"sec-form-container\">\r\n            <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n                <!-- 第一部分包括姓名到常住地公安start -->\r\n                <div class=\"sec-header-section\">\r\n                    <div class=\"sec-form-left\">\r\n                        <el-form-item label=\"申请部门\">\r\n                            <template slot-scope=\"scope\">\r\n                                <el-cascader v-model=\"tjlist.szbm\" style=\"width: 100%;\" :options=\"regionOption\"\r\n                                    :props=\"regionParams\" filterable clearable ref=\"cascaderArr\"\r\n                                    @change=\"handleChange(1)\"></el-cascader>\r\n                            </template>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"申请人\">\r\n                            <el-autocomplete class=\"inline-input\" value-key=\"xm\" v-model.trim=\"tjlist.xqr\"\r\n                                :fetch-suggestions=\"querySearch\" placeholder=\"请输入申请人\" style=\"width:100%\">\r\n                            </el-autocomplete>\r\n                        </el-form-item>\r\n                    </div>\r\n                    <div class=\"sec-form-left\">\r\n                        <el-form-item label=\"报废日期\">\r\n                            <el-date-picker v-model=\"tjlist.bfrq\" class=\"riq\" type=\"date\" placeholder=\"选择日期\"\r\n                                format=\"yyyy-MM-dd\" value-format=\"yyyy-MM-dd\">\r\n                            </el-date-picker>\r\n                        </el-form-item>\r\n                    </div>\r\n                    <div class=\"sec-form-left sec-form-left-textarea\">\r\n                        <el-form-item label=\"报废原因\">\r\n                            <el-input placeholder=\"\" type=\"textarea\" v-model=\"tjlist.bfyy\" clearable></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                    <div class=\"sec-form-left sec-form-left-textarea\">\r\n                        <el-form-item label=\"采取措施\">\r\n                            <el-input placeholder=\"\" type=\"textarea\" v-model=\"tjlist.cqcs\" clearable></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                    <div class=\"sec-form-left sec-form-left-textarea\">\r\n                        <el-form-item label=\"最终去向\">\r\n                            <el-input placeholder=\"\" type=\"textarea\" v-model=\"tjlist.zzqx\" clearable></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                    <!-- <div class=\"sec-form-left\">\r\n                        <el-form-item label=\"项目经理部门\">\r\n                            <template slot-scope=\"scope\">\r\n                                <el-cascader v-model=\"tjlist.xmjlszbm\" style=\"width: 100%;\" :options=\"regionOption\"\r\n                                    :props=\"regionParams\" filterable clearable ref=\"cascaderArr\"\r\n                                    @change=\"handleChange(2)\"></el-cascader>\r\n                            </template>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"项目经理\">\r\n                            <el-autocomplete class=\"inline-input\" value-key=\"xm\" v-model.trim=\"tjlist.xmjl\"\r\n                                :fetch-suggestions=\"querySearch\" placeholder=\"请输入项目经理\" style=\"width:100%\">\r\n                            </el-autocomplete>\r\n                        </el-form-item>\r\n                    </div> -->\r\n                    <!-- 载体详细信息start -->\r\n                    <p class=\"sec-title\">报废设备详细信息</p>\r\n                    <el-table border class=\"sec-el-table\" :data=\"sbGlSpList\"\r\n                        :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n                        <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n                        <el-table-column prop=\"bmbh\" label=\"设备保密编号\"></el-table-column>\r\n                        <el-table-column prop=\"gdzcbh\" label=\"固定资产编号\"></el-table-column>\r\n                        <el-table-column prop=\"mj\" label=\"密级\"></el-table-column>\r\n                        <el-table-column prop=\"lx\" label=\"设备类型\"></el-table-column>\r\n                        <el-table-column prop=\"ppxh\" label=\"品牌型号\"></el-table-column>\r\n                        <el-table-column prop=\"cfwz\" label=\"存放位置\"></el-table-column>\r\n                        <el-table-column prop=\"zjxlh\" label=\"设备序列号\"></el-table-column>\r\n                        <el-table-column prop=\"ypxlh\" label=\"硬盘序列号\"></el-table-column>\r\n                        <el-table-column prop=\"zrr\" label=\"责任人\"></el-table-column>\r\n                        <el-table-column prop=\"zrbm\" label=\"责任部门\"></el-table-column>\r\n                    </el-table>\r\n                </div>\r\n\r\n                <!-- 底部操作按钮start -->\r\n                <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n                    <el-button @click=\"returnIndex\" class=\"fr ml10\" plain>返回</el-button>\r\n                    <el-button @click=\"chooseApproval\" class=\"fr\" type=\"success\">保存并提交</el-button>\r\n                    <el-button @click=\"save\" class=\"fr\" type=\"primary\">临时保存</el-button>\r\n                </div>\r\n                <!-- 底部操作按钮end -->\r\n\r\n            </el-form>\r\n        </div>\r\n        <!-- 发起申请弹框start -->\r\n        <el-dialog title=\"选择审批人\" :close-on-click-modal=\"false\" :visible.sync=\"approvalDialogVisible\" width=\"40%\"\r\n            :destroy-on-close=\"true\">\r\n            <div class=\"dlFqsqContainer\">\r\n                <label for=\"\">部门:</label>\r\n                <el-cascader v-model=\"ryChoose.bm\" :options=\"regionOption\" :props=\"regionParams\" filterable clearable\r\n                    ref=\"cascaderArr\" @change=\"bmSelectChange\"></el-cascader>\r\n                <label for=\"\">姓名:</label>\r\n                <el-input class=\"input2\" v-model=\"ryChoose.xm\" clearable placeholder=\"姓名\"></el-input>\r\n                <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\" @click=\"searchRy\">查询</el-button>\r\n                <BaseTable class=\"baseTable\" :tableHeight=\"'300'\" :key=\"tableKey\" :showIndex=true :tableData=\"ryDatas\"\r\n                    :columns=\"applyColumns\" :showSingleSelection=\"true\" :handleColumn=\"handleColumnApply\"\r\n                    :showPagination=true :currentPage=\"page\" :pageSize=\"pageSize\" :totalCount=\"total\"\r\n                    @handleCurrentChange=\"handleCurrentChangeRy\" @handleSizeChange=\"handleSizeChangeRy\"\r\n                    @handleSelectionChange=\"handleSelectionChange\">\r\n                </BaseTable>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"warning\" class=\"fr ml10\" @click=\"approvalDialogVisible = false\">关 闭</el-button>\r\n                <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">提交</el-button>\r\n                <!-- <el-button @click=\"save\" class=\"fr\" type=\"primary\">保存</el-button> -->\r\n                <div style=\"clear:both\"></div>\r\n            </span>\r\n        </el-dialog>\r\n        <!-- 发起申请弹框end -->\r\n    </div>\r\n</template>\r\n<script>\r\nimport {\r\n    getLcSLid,\r\n    getZzjgList,\r\n    getFwdyidByFwlx,\r\n    getLoginInfo,\r\n    getAllYhxx,\r\n    getSpUserList,\r\n    deleteSlxxBySlid,\r\n} from '../../../../api/index'\r\nimport {\r\n    saveSbglSbbf,\r\n    updateSbglSbbf,\r\n    savaSbqdBatch,\r\n    deleteSbqdByYjlid,\r\n} from '../../../../api/sbbf'\r\nimport { getUserInfo } from '../../../../api/dwzc'\r\nimport vPinyin from '../../../../utils/vue-py'\r\nimport { getAllGwxx } from '../../../../api/qblist'\r\nimport { getAllSmdj } from '../../../../api/xlxz'\r\nimport BaseTable from '../../../components/common/baseTable.vue'\r\nimport AddLineTable from \"../../../components/common/addLineTable.vue\"; //人工纠错组件\r\nexport default {\r\n    components: {\r\n        AddLineTable,\r\n        BaseTable\r\n    },\r\n    props: {},\r\n    data() {\r\n        return {\r\n            tableKey:1,\r\n            value1: '',\r\n            loading: false,\r\n            // 弹框人员选择条件\r\n            ryChoose: {\r\n                'bm': '',\r\n                'xm': ''\r\n            },\r\n            gwmclist: [],\r\n            smdjxz: [],\r\n            regionOption: [], // 部门下拉\r\n            page: 1, // 审批人弹框当前页\r\n            pageSize: 10, // 审批人弹框每页条数\r\n            radioIdSelect: '', // 审批人弹框人员单选\r\n            ryDatas: [], // 弹框人员选择\r\n            total: 0, // 弹框人员总数\r\n            regionParams: {\r\n                label: 'label', //这里可以配置你们后端返回的属性\r\n                value: 'label',\r\n                children: 'childrenRegionVo',\r\n                expandTrigger: 'click',\r\n                checkStrictly: true\r\n            }, //地域信息配置参数\r\n            // table 行样式\r\n            headerCellStyle: {\r\n                background: '#EEF7FF',\r\n                color: '#4D91F8'\r\n            },\r\n            // form表单提交数据\r\n            tjlist: {\r\n                smryid: '',\r\n                xqr: '',\r\n                szbm: [],\r\n                bfrq: '',\r\n                // xmjlszbm: [],\r\n                sbGlSpList: [],\r\n                // xmjl: '',\r\n                bfyy: '',\r\n                cqcs: '',\r\n                zzqx: '',\r\n            },\r\n            checkList: [],\r\n            gpRadio: true,\r\n            upRadio: true,\r\n            zjzRadio: true,\r\n            drsbList: [\r\n                {\r\n                    drid: 1,\r\n                    sblb: '涉密中间机',\r\n                    sfybmbh: true,\r\n                    bmbh: '',\r\n                    yxq: '',\r\n                    checked: 0\r\n                },\r\n                {\r\n                    drid: 2,\r\n                    sblb: '非涉密中间机',\r\n                    sfybmbh: false,\r\n                    bmbh: '',\r\n                    yxq: '',\r\n                    checked: 0\r\n                },\r\n                {\r\n                    drid: 3,\r\n                    sblb: '非密专用导入U盘',\r\n                    sfybmbh: false,\r\n                    bmbh: '',\r\n                    yxq: '',\r\n                    checked: 0\r\n                },\r\n                {\r\n                    drid: 4,\r\n                    sblb: '涉密专用扫描仪',\r\n                    sfybmbh: false,\r\n                    bmbh: '',\r\n                    yxq: '',\r\n                    checked: 0\r\n                },\r\n                {\r\n                    drid: 5,\r\n                    sblb: '专用红盘',\r\n                    sfybmbh: true,\r\n                    bmbh: '',\r\n                    yxq: '',\r\n                    checked: 0\r\n                },\r\n                {\r\n                    drid: 6,\r\n                    sblb: '单导盒',\r\n                    sfybmbh: true,\r\n                    bmbh: '',\r\n                    yxq: '',\r\n                    checked: 0\r\n                },\r\n                {\r\n                    drid: 7,\r\n                    sblb: '其他',\r\n                    sfybmbh: true,\r\n                    bmbh: '',\r\n                    yxq: '',\r\n                    checked: 0\r\n                }\r\n            ],\r\n            // 载体详细信息\r\n            sbGlSpList: [],\r\n            smdjList: [\r\n                {\r\n                    smdjid: '1',\r\n                    smdjmc: '绝密'\r\n                },\r\n                {\r\n                    smdjid: '2',\r\n                    smdjmc: '机密'\r\n                },\r\n                {\r\n                    smdjid: '3',\r\n                    smdjmc: '秘密'\r\n                },\r\n                {\r\n                    smdjid: '4',\r\n                    smdjmc: '内部'\r\n                },\r\n            ],\r\n            ryInfo: {},\r\n            // 政治面貌下拉选项\r\n            sltshow: '', // 文档的缩略图显示\r\n            routeType: '',\r\n            pdfBase64: '',\r\n            fileList: [],\r\n            dialogImageUrl: '',\r\n            dialogVisible: false,\r\n            approvalDialogVisible: false, // 选择申请人弹框\r\n            fileRow: '',\r\n            // 选择审核人table\r\n            applyColumns: [{\r\n                name: '姓名',\r\n                prop: 'xm',\r\n                scopeType: 'text',\r\n                formatter: false\r\n            },\r\n            {\r\n                name: '部门',\r\n                prop: 'bmmc',\r\n                scopeType: 'text',\r\n                formatter: false\r\n            },\r\n            {\r\n                name: '岗位',\r\n                prop: 'gwmc',\r\n                scopeType: 'text',\r\n                formatter: false\r\n            }\r\n            ],\r\n            handleColumnApply: [],\r\n            scqk: [\r\n                {\r\n                    sfty: '同意',\r\n                    id: 1\r\n                },\r\n                {\r\n                    sfty: '不同意',\r\n                    id: 0\r\n                },\r\n            ],\r\n            disabled2: false,\r\n            //知悉范围选择\r\n            rydialogVisible: false,\r\n            formInlinery: {\r\n                bm: ''\r\n            },\r\n            table1Data: [],\r\n            table2Data: [],\r\n            restaurants: {},\r\n        }\r\n    },\r\n    computed: {\r\n\r\n    },\r\n    mounted() {\r\n        this.dqlogin()\r\n        this.onfwid()\r\n        this.smdj()\r\n        this.gwxx()\r\n        this.rydata()\r\n        this.smry()\r\n        this.getOrganization()\r\n        console.log(this.$route.query.datas, 'this.$route.query.datas');\r\n        this.routeType = this.$route.query.type\r\n        this.yhDatas = this.$route.query.datas\r\n        if (this.routeType == 'update') {\r\n            this.tjlist = this.$route.query.datas\r\n            this.sbGlSpList = this.$route.query.ztqs\r\n            this.tjlist.szbm = this.tjlist.szbm.split('/')\r\n            // this.tjlist.xmjlszbm = this.tjlist.xmjlszbm.split('/')\r\n        } else {\r\n            this.sbGlSpList = this.$route.query.datas\r\n        }\r\n        this.sbGlSpList.forEach((item) => {\r\n            console.log(item);\r\n            if (item.mj == 1) {\r\n                item.mj = '绝密'\r\n            } else if (item.mj == 2) {\r\n                item.mj = '机密'\r\n            } else if (item.mj == 3) {\r\n                item.mj = '秘密'\r\n            } else if (item.mj == 4) {\r\n                item.mj = '内部'\r\n            }\r\n        })\r\n    },\r\n    methods: {\r\n        async dqlogin() {\r\n            let data = await getUserInfo()\r\n            this.tjlist.szbm = data.bmmc.split('/')\r\n            this.tjlist.xqr = data.xm\r\n        },\r\n        async handleChange(index) {\r\n            let resList\r\n            let params\r\n            // if (index == 1) {\r\n            // this.tjlist.xmjlszbm = this.tjlist.szbm\r\n            params = {\r\n                bmmc: this.tjlist.szbm.join('/')\r\n            }\r\n            resList = await getAllYhxx(params)\r\n            this.tjlist.xqr = \"\";\r\n            // }else if (index == 2) {\r\n            //     params = {\r\n            //         bmmc: this.tjlist.xmjlszbm.join('/')\r\n            //     }\r\n            //     resList = await getAllYhxx(params)\r\n            //     this.tjlist.xmjl = \"\";\r\n            // }\r\n            this.restaurants = resList;\r\n        },\r\n        //人员获取\r\n        querySearch(queryString, cb) {\r\n            var restaurants = this.restaurants;\r\n            console.log(\"restaurants\", restaurants);\r\n            var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;\r\n            console.log(\"results\", results);\r\n            // 调用 callback 返回建议列表的数据\r\n            cb(results);\r\n            console.log(\"cb(results.dwmc)\", results);\r\n        },\r\n        createFilter(queryString) {\r\n            return (restaurant) => {\r\n                return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);\r\n            };\r\n        },\r\n        async smry() {\r\n            this.restaurants = await getAllYhxx()\r\n        },\r\n        //培训清单\r\n        zxfw() {\r\n            this.rydialogVisible = true\r\n        },\r\n        async rydata() {\r\n            let param = {\r\n                bmid: this.bmm\r\n            }\r\n            let list = await getAllYhxx(param)\r\n            this.table1Data = list\r\n        },\r\n        chRadio() { },\r\n        async gwxx() {\r\n            let param = {\r\n                bmmc: this.tjlist.bmmc\r\n            }\r\n            let data = await getAllGwxx(param)\r\n            this.gwmclist = data\r\n            console.log(data);\r\n        },\r\n        //获取涉密等级信息\r\n        async smdj() {\r\n            let data = await getAllSmdj()\r\n            this.smdjxz = data\r\n        },\r\n\r\n        handleSelectionChange(index, row) {\r\n            this.radioIdSelect = row\r\n        },\r\n        async onfwid() {\r\n            let params = {\r\n                fwlx: 15\r\n            }\r\n            let data = await getFwdyidByFwlx(params)\r\n            console.log(data);\r\n            this.fwdyid = data.data.fwdyid\r\n        },\r\n        jyxx() {\r\n            if (this.tjlist.xqr == '' || this.tjlist.xqr == undefined) {\r\n                this.$message.error('请输入申请人')\r\n                return true\r\n            }\r\n            if (this.tjlist.szbm.length == 0 || this.tjlist.szbm == undefined) {\r\n                this.$message.error('请选择申请部门')\r\n                return true\r\n            }\r\n            if (this.tjlist.bfrq == '' || this.tjlist.bfrq == undefined) {\r\n                this.$message.error('请选择报废日期')\r\n                return true\r\n            }\r\n            if (this.tjlist.bfyy == '' || this.tjlist.bfyy == undefined) {\r\n                this.$message.error('请输入报废原因')\r\n                return true\r\n            }\r\n            if (this.tjlist.cqcs == '' || this.tjlist.cqcs == undefined) {\r\n                this.$message.error('请输入采取措施')\r\n                return true\r\n            }\r\n            if (this.tjlist.zzqx == '' || this.tjlist.zzqx == undefined) {\r\n                this.$message.error('请输入最终去向')\r\n                return true\r\n            }\r\n        },\r\n        // 保存\r\n        async save() {\r\n            if (this.jyxx()) {\r\n                return\r\n            }\r\n            let param = {\r\n                'fwdyid': this.fwdyid,\r\n                'lcslclzt': 3\r\n            }\r\n            let id = []\r\n            this.sbGlSpList.forEach(item => {\r\n                id.push(item.jlid)\r\n            })\r\n            param.smryid = id.join(',')\r\n            let res = await getLcSLid(param)\r\n            if (res.code == 10000) {\r\n                this.tjlist.slid = res.data.slid\r\n                let params = this.tjlist\r\n                this.sbGlSpList.forEach((item) => {\r\n                    if (item.mj == '绝密') {\r\n                        item.mj = 1\r\n                    } else if (item.mj == '机密') {\r\n                        item.mj = 2\r\n                    } else if (item.mj == '秘密') {\r\n                        item.mj = 3\r\n                    } else if (item.mj == '内部') {\r\n                        item.mj = 4\r\n                    }\r\n                })\r\n                let szbmArr = JSON.parse(JSON.stringify(this.tjlist.szbm))\r\n                // let xmjlszbmArr = JSON.parse(JSON.stringify(this.tjlist.xmjlszbm))\r\n                this.tjlist.szbm = szbmArr.join('/')\r\n                // this.tjlist.xmjlszbm = xmjlszbmArr.join('/')\r\n                console.log(this.tjlist);\r\n                if (this.routeType == 'update') {\r\n\r\n                    let resDatas = await updateSbglSbbf(params)\r\n                    if (resDatas.code == 10000) {\r\n                        this.sbGlSpList.forEach((item) => {\r\n                            item.splx = 7\r\n                            item.yjlid = resDatas.data\r\n                            item.sbjlid = item.jlid\r\n                        })\r\n                        let del = await deleteSbqdByYjlid({ 'yjlid': this.tjlist.jlid })\r\n                        if (del.code == 10000) {\r\n                            let data = await savaSbqdBatch(this.sbGlSpList)\r\n                            if (data.code == 10000) {\r\n                                this.$router.push('/sbbfsp')\r\n                                this.$message({\r\n                                    message: '保存成功',\r\n                                    type: 'success'\r\n                                })\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    let resDatas = await saveSbglSbbf(params)\r\n                    if (resDatas.code == 10000) {\r\n\r\n                        this.sbGlSpList.forEach((item) => {\r\n                            item.splx = 7\r\n                            item.yjlid = resDatas.data\r\n                            item.sbjlid = item.jlid\r\n                        })\r\n                        let data = await savaSbqdBatch(this.sbGlSpList)\r\n                        if (data.code == 10000) {\r\n                            this.$router.push('/sbbfsp')\r\n                            this.$message({\r\n                                message: '保存成功',\r\n                                type: 'success'\r\n                            })\r\n                        } else {\r\n                            deleteSlxxBySlid({ slid: res.data.slid })\r\n                        }\r\n                    }\r\n                }\r\n\r\n            }\r\n        },\r\n\r\n        //全部组织机构List\r\n        async getOrganization() {\r\n            let zzjgList = await getZzjgList()\r\n            this.zzjgmc = zzjgList\r\n            let shu = []\r\n            this.zzjgmc.forEach(item => {\r\n                let childrenRegionVo = []\r\n                this.zzjgmc.forEach(item1 => {\r\n                    if (item.bmm == item1.fbmm) {\r\n                        childrenRegionVo.push(item1)\r\n                        item.childrenRegionVo = childrenRegionVo\r\n                    }\r\n                });\r\n                shu.push(item)\r\n            })\r\n            let shuList = []\r\n            let list = await getLoginInfo()\r\n            if (list.fbmm == '') {\r\n                shu.forEach(item => {\r\n                    if (item.fbmm == '') {\r\n                        shuList.push(item)\r\n                    }\r\n                })\r\n            }\r\n            if (list.fbmm != '') {\r\n                shu.forEach(item => {\r\n                    console.log(item);\r\n                    if (item.fbmm == list.fbmm) {\r\n                        shuList.push(item)\r\n                    }\r\n                })\r\n            }\r\n            shuList[0].childrenRegionVo.forEach(item => {\r\n                this.regionOption.push(item)\r\n            })\r\n        },\r\n        handleSelectionChange1(index, row) {\r\n            this.radioIdSelect = row\r\n        },\r\n        handleCurrentChangeRy(val) {\r\n            this.page = val\r\n            this.chooseApproval()\r\n        },\r\n        //列表分页--更改每页显示个数\r\n        handleSizeChangeRy(val) {\r\n            this.page = 1\r\n            this.pageSize = val\r\n            this.chooseApproval()\r\n        },\r\n        // 人员搜索\r\n        searchRy() {\r\n      this.tableKey++\r\n            this.chooseApproval()\r\n        },\r\n        // 发起申请选择人员 人员下拉\r\n        bmSelectChange(item) {\r\n            if (item != undefined) {\r\n                this.ryChoose.bm = item.join('/')\r\n            }\r\n        },\r\n        // 选择审批人\r\n        async chooseApproval() {\r\n            // this.getOrganization()\r\n            this.approvalDialogVisible = true\r\n            let param = {\r\n                'page': this.page,\r\n                'pageSize': this.pageSize,\r\n                'fwdyid': this.fwdyid,\r\n                'bmmc': this.ryChoose.bm,\r\n                'xm': this.ryChoose.xm\r\n            }\r\n            let resData = await getSpUserList(param)\r\n            if (resData.records) {\r\n                this.ryDatas = resData.records\r\n                this.total = resData.total\r\n            } else {\r\n                this.$message.error('数据获取失败！')\r\n            }\r\n        },\r\n        // 保存并提交\r\n        async saveAndSubmit() {\r\n            if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {\r\n                if (this.jyxx()) {\r\n                    return\r\n                }\r\n                let param = {\r\n                    'fwdyid': this.fwdyid,\r\n                }\r\n                let id = []\r\n                this.sbGlSpList.forEach(item => {\r\n                    id.push(item.jlid)\r\n                })\r\n                param.smryid = id.join(',')\r\n                let szbmArr = JSON.parse(JSON.stringify(this.tjlist.szbm))\r\n                // let xmjlszbmArr = JSON.parse(JSON.stringify(this.tjlist.xmjlszbm))\r\n                this.tjlist.szbm = szbmArr.join('/')\r\n                this.sbGlSpList.forEach((item) => {\r\n                    if (item.mj == '绝密') {\r\n                        item.mj = 1\r\n                    } else if (item.mj == '机密') {\r\n                        item.mj = 2\r\n                    } else if (item.mj == '秘密') {\r\n                        item.mj = 3\r\n                    } else if (item.mj == '内部') {\r\n                        item.mj = 4\r\n                    }\r\n                })\r\n                // this.tjlist.xmjlszbm = xmjlszbmArr.join('/')\r\n                if (this.routeType == 'update') {\r\n                    param.lcslclzt = 2\r\n                    param.slid = this.tjlist.slid\r\n                    param.clrid = this.radioIdSelect.yhid\r\n                    let res = await getLcSLid(param)\r\n                    if (res.code == 10000) {\r\n                        this.tjlist.slid = res.data.slid\r\n                        let params = this.tjlist\r\n                        console.log(this.tjlist);\r\n\r\n                        let resDatas = await updateSbglSbbf(params)\r\n                        if (resDatas.code == 10000) {\r\n                            this.sbGlSpList.forEach((item) => {\r\n                                item.splx = 7\r\n                                item.sbjlid = item.jlid\r\n                                item.yjlid = resDatas.data\r\n                            })\r\n                            let del = await deleteSbqdByYjlid({ 'yjlid': this.tjlist.jlid })\r\n                            if (del.code == 10000) {\r\n                                let data = await savaSbqdBatch(this.sbGlSpList)\r\n                                if (data.code == 10000) {\r\n                                    this.$router.push('/sbbfsp')\r\n                                    this.$message({\r\n                                        message: '保存成功',\r\n                                        type: 'success'\r\n                                    })\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    param.lcslclzt = 0\r\n                    param.clrid = this.radioIdSelect.yhid\r\n                    let res = await getLcSLid(param)\r\n                    if (res.code == 10000) {\r\n                        this.tjlist.slid = res.data.slid\r\n                        let params = this.tjlist\r\n                        console.log(this.tjlist);\r\n                        let resDatas = await saveSbglSbbf(params)\r\n                        if (resDatas.code == 10000) {\r\n                            this.sbGlSpList.forEach((item) => {\r\n                                item.splx = 7\r\n                                item.yjlid = resDatas.data\r\n                                item.sbjlid = item.jlid\r\n                            })\r\n                            let data = await savaSbqdBatch(this.sbGlSpList)\r\n                            if (data.code == 10000) {\r\n                                this.$router.push('/sbbfsp')\r\n                                this.$message({\r\n                                    message: '保存成功',\r\n                                    type: 'success'\r\n                                })\r\n                            } else {\r\n                                deleteSlxxBySlid({ slid: res.data.slid })\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            } else {\r\n                this.$message({\r\n                    message: '请选择审批人',\r\n                    type: 'warning'\r\n                })\r\n            }\r\n        },\r\n        // 返回\r\n        returnIndex() {\r\n            this.$router.push('/sbbfsp')\r\n        }\r\n    },\r\n    watch: {\r\n\r\n    }\r\n}\r\n\r\n</script>\r\n  \r\n<style scoped>\r\n.sec-container {\r\n    width: 100%;\r\n    height: calc(100% - 50px);\r\n    overflow-y: overlay;\r\n}\r\n\r\n.sec-title {\r\n    border-left: 5px solid #1b72d8;\r\n    color: #1b72d8;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    text-indent: 10px;\r\n    margin-bottom: 20px;\r\n    margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n    /* width: 100%; */\r\n    border: 1px solid #CDD2D9;\r\n    height: 40px;\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    overflow: hidden;\r\n    border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n    border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n    float: left;\r\n    width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n    width: 100%;\r\n    position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n    width: 258px;\r\n    position: absolute;\r\n    right: 0px;\r\n    top: 0;\r\n    height: 163px;\r\n    border: 1px solid #CDD2D9;\r\n    border-left: 0;\r\n    background: #ffffff;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.sec-form-second {\r\n    /* width: 100%; */\r\n    border: 1px solid #CDD2D9;\r\n    height: 40px;\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    overflow: hidden;\r\n    border-right: 0px;\r\n    border-top: 0;\r\n}\r\n\r\n\r\n>>>.wd .el-form-item__label {\r\n    height: 371px;\r\n    line-height: 371px;\r\n}\r\n\r\n>>>.wd1 .el-radio {\r\n    display: block;\r\n    margin: 10px 0;\r\n    /* width: 200px; */\r\n}\r\n\r\n>>>.wd1 .el-form-item__label {\r\n    height: 180px;\r\n    line-height: 180px;\r\n}\r\n\r\n>>>.longLabel .el-form-item__label {\r\n    width: 225px !important;\r\n    /* height: 184px;\r\n  line-height: 184px; */\r\n}\r\n\r\n>>>.longLabel .el-form-item__content {\r\n    margin-left: 225px !important;\r\n    padding-left: 20px;\r\n    border-right: 1px solid #CDD2D9;\r\n    background: #ffffff;\r\n    line-height: 48px;\r\n}\r\n\r\n.sec-form-third {\r\n    border: 1px solid #CDD2D9;\r\n    /* height: 40px;  */\r\n    /* display: flex;\r\n    justify-content: space-evenly; */\r\n    overflow: hidden;\r\n    /* border-right: 0px; */\r\n    border-top: 0;\r\n    background: #ffffff;\r\n    padding: 10px;\r\n}\r\n\r\n/* /deep/.el-checkbox-group {\r\n    display: flex;\r\n    justify-content: center;\r\n    background-color: #F5F7FA;\r\n    border-right: 1px solid #CDD2D9;\r\n} */\r\n\r\n.checkbox {\r\n    display: inline-block !important;\r\n    background-color: rgba(255, 255, 255, 0) !important;\r\n    border-right: none !important;\r\n}\r\n\r\n.sec-form-four {\r\n    border: 1px solid #CDD2D9;\r\n    height: auto;\r\n    min-height: 100px;\r\n    /* display: flex;\r\n    justify-content: space-evenly; */\r\n    overflow: hidden;\r\n    /* border-right: 0px; */\r\n    border-top: 0;\r\n    background: #ffffff;\r\n    padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n    border: 1px solid #CDD2D9;\r\n    height: auto;\r\n    min-height: 100px;\r\n    overflow: hidden;\r\n    border-top: 0;\r\n    background: #ffffff;\r\n    padding: 10px;\r\n}\r\n\r\n.yulan {\r\n    text-align: center;\r\n    cursor: pointer;\r\n    color: #3874D5;\r\n    font-weight: 600;\r\n    float: left;\r\n    margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n    width: 178px;\r\n    height: 178px;\r\n    display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n    font-size: 28px;\r\n    color: #8c939d;\r\n    width: 178px;\r\n    height: 178px;\r\n    line-height: 178px;\r\n    text-align: center;\r\n    border: 2px solid #EBEBEB;\r\n}\r\n\r\n.sec-form-six {\r\n    border: 1px solid #CDD2D9;\r\n    overflow: hidden;\r\n    background: #ffffff;\r\n    padding: 10px;\r\n}\r\n\r\n.ml10 {\r\n    margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n    margin-top: 10px;\r\n    border-right: 1px solid #CDD2D9;\r\n    background: #ffffff;\r\n}\r\n\r\n.sec-form-left-textarea {\r\n    height: 54px !important;\r\n}\r\n\r\n.sec-form-left-textarea>>>.el-form-item__label {\r\n    line-height: 54px !important;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n    border: none;\r\n}\r\n\r\n.sec-left-text {\r\n    float: left;\r\n    margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n    border-top: 1px solid #CDD2D9;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n    border-top: 0;\r\n  } */\r\n.sec-form-second .el-form-item {\r\n    float: left;\r\n    width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n    width: 100%;\r\n    border: 1px solid #EBEEF5;\r\n    height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n.hyzk {\r\n    padding-left: 15px;\r\n    background-color: #F5F7FA;\r\n    width: calc(100% - 16px);\r\n    border-right: 1px solid #CDD2D9;\r\n    color: #C0C4CC;\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n    border: none !important;\r\n    border-radius: 0;\r\n}\r\n\r\n.riq {\r\n    width: 100% !important;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n    width: 200px;\r\n    text-align: center;\r\n    font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n    border: none;\r\n    border-right: 1px solid #CDD2D9;\r\n    border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n    margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n    border: 1px solid #CDD2D9;;\r\n  } */\r\n>>>.el-form-item__label {\r\n    border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n/* /deep/.sec-form-container .el-form-item {\r\n    margin-top: 5px;\r\n    margin-bottom: 5px;\r\n  } */\r\n.riq {\r\n    width: 100% !important;\r\n}\r\n\r\n.widthw {\r\n    width: 6vw;\r\n}\r\n\r\n.dlFqsqContainer {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n    font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n    width: 150px;\r\n    margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n    margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n    height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n    margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n    margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n    margin-top: 20px;\r\n}\r\n</style>\r\n  \n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/smsb/sbbfspTable.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"sec-container\"},[_c('p',{staticClass:\"sec-title\"},[_vm._v(\"基本信息\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"申请部门\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-cascader',{ref:\"cascaderArr\",staticStyle:{\"width\":\"100%\"},attrs:{\"options\":_vm.regionOption,\"props\":_vm.regionParams,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":function($event){return _vm.handleChange(1)}},model:{value:(_vm.tjlist.szbm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"szbm\", $$v)},expression:\"tjlist.szbm\"}})]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"申请人\"}},[_c('el-autocomplete',{staticClass:\"inline-input\",staticStyle:{\"width\":\"100%\"},attrs:{\"value-key\":\"xm\",\"fetch-suggestions\":_vm.querySearch,\"placeholder\":\"请输入申请人\"},model:{value:(_vm.tjlist.xqr),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xqr\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"tjlist.xqr\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"报废日期\"}},[_c('el-date-picker',{staticClass:\"riq\",attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(_vm.tjlist.bfrq),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bfrq\", $$v)},expression:\"tjlist.bfrq\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left sec-form-left-textarea\"},[_c('el-form-item',{attrs:{\"label\":\"报废原因\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"type\":\"textarea\",\"clearable\":\"\"},model:{value:(_vm.tjlist.bfyy),callback:function ($$v) {_vm.$set(_vm.tjlist, \"bfyy\", $$v)},expression:\"tjlist.bfyy\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left sec-form-left-textarea\"},[_c('el-form-item',{attrs:{\"label\":\"采取措施\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"type\":\"textarea\",\"clearable\":\"\"},model:{value:(_vm.tjlist.cqcs),callback:function ($$v) {_vm.$set(_vm.tjlist, \"cqcs\", $$v)},expression:\"tjlist.cqcs\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left sec-form-left-textarea\"},[_c('el-form-item',{attrs:{\"label\":\"最终去向\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"type\":\"textarea\",\"clearable\":\"\"},model:{value:(_vm.tjlist.zzqx),callback:function ($$v) {_vm.$set(_vm.tjlist, \"zzqx\", $$v)},expression:\"tjlist.zzqx\"}})],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"报废设备详细信息\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.sbGlSpList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bmbh\",\"label\":\"设备保密编号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gdzcbh\",\"label\":\"固定资产编号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"mj\",\"label\":\"密级\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"lx\",\"label\":\"设备类型\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"ppxh\",\"label\":\"品牌型号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfwz\",\"label\":\"存放位置\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zjxlh\",\"label\":\"设备序列号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"ypxlh\",\"label\":\"硬盘序列号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zrr\",\"label\":\"责任人\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zrbm\",\"label\":\"责任部门\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"plain\":\"\"},on:{\"click\":_vm.returnIndex}},[_vm._v(\"返回\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.chooseApproval}},[_vm._v(\"保存并提交\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.save}},[_vm._v(\"临时保存\")])],1)])],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"选择审批人\",\"close-on-click-modal\":false,\"visible\":_vm.approvalDialogVisible,\"width\":\"40%\",\"destroy-on-close\":true},on:{\"update:visible\":function($event){_vm.approvalDialogVisible=$event}}},[_c('div',{staticClass:\"dlFqsqContainer\"},[_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"部门:\")]),_vm._v(\" \"),_c('el-cascader',{ref:\"cascaderArr\",attrs:{\"options\":_vm.regionOption,\"props\":_vm.regionParams,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.bmSelectChange},model:{value:(_vm.ryChoose.bm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"bm\", $$v)},expression:\"ryChoose.bm\"}}),_vm._v(\" \"),_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"姓名:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"姓名\"},model:{value:(_vm.ryChoose.xm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"xm\", $$v)},expression:\"ryChoose.xm\"}}),_vm._v(\" \"),_c('el-button',{staticClass:\"searchButton\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchRy}},[_vm._v(\"查询\")]),_vm._v(\" \"),_c('BaseTable',{key:_vm.tableKey,staticClass:\"baseTable\",attrs:{\"tableHeight\":'300',\"showIndex\":true,\"tableData\":_vm.ryDatas,\"columns\":_vm.applyColumns,\"showSingleSelection\":true,\"handleColumn\":_vm.handleColumnApply,\"showPagination\":true,\"currentPage\":_vm.page,\"pageSize\":_vm.pageSize,\"totalCount\":_vm.total},on:{\"handleCurrentChange\":_vm.handleCurrentChangeRy,\"handleSizeChange\":_vm.handleSizeChangeRy,\"handleSelectionChange\":_vm.handleSelectionChange}})],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.approvalDialogVisible = false}}},[_vm._v(\"关 闭\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.saveAndSubmit}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-95b6e692\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/smsb/sbbfspTable.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-95b6e692\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./sbbfspTable.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./sbbfspTable.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./sbbfspTable.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-95b6e692\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./sbbfspTable.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-95b6e692\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/smsb/sbbfspTable.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}