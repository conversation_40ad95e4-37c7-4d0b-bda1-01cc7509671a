<template>
  <div class="bg_con">
    <div class="container">
      <!-- 涉密人员任用审查列表start -->
      <div class="table_content">
        <el-table class="tb-container table" :data="smryList" border @selection-change="selectRow"
          :header-cell-style="headerCellStyle" stripe height="calc(100% - 54px)">
          <el-table-column type="selection" width="55" align="center"> </el-table-column>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="xqr" label="申请人"></el-table-column>
          <el-table-column prop="szbm" label="所在部门"></el-table-column>
          <el-table-column prop="ztbh" label="载体编号"></el-table-column>
          <el-table-column prop="ztmc" label="载体名称"></el-table-column>
          <el-table-column prop="zxfw" label="知悉范围"></el-table-column>
          <el-table-column prop="xmbh" label="项目编号"></el-table-column>
          <el-table-column prop="ys" label="页数"></el-table-column>
          <el-table-column prop="fs" label="份数"></el-table-column>
          <el-table-column prop="fhcs" label="防护措施"></el-table-column>
          <el-table-column prop="jtgj" label="交通工具"></el-table-column>
          <el-table-column prop="" label="操作" width="140">
            <template slot-scope="scoped">
              <el-button size="medium" type="text" @click="updateItem(scoped.row)">查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5"
          :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper" :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import {
  selectZtJscdDj,
} from '../../../../api/ztjs'
import {
  getFwdyidByFwlx,
} from '../../../../api/index'
export default {
  components: {},
  // props: ['msg'],
  props: {
    msg: {
      type: Object,
      require: false,
      default: ""
    }
  },
  data() {
    return {
      // table 行样式
      headerCellStyle: { background: '#EEF7FF', color: '#4D91F8' },
      formInline: {}, // 搜索条件
      dialogVisible: false, // 发起申请弹框
      fwlxOptions: [],
      smryList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      ztid: '',
      jbxx: {},
      fwdyid:'',
    }
  },
  computed: {},
  mounted() {
    this.onfwid()
    this.getYbsx()
  },
  methods: {
    async onfwid() {
      let params = {
        fwlx: 20
      }
      let data = await getFwdyidByFwlx(params)
      console.log(data);
      this.fwdyid = data.data.fwdyid
    },
    async getYbsx() {
      this.$nextTick(async () => {
        this.jbxx = JSON.parse(JSON.stringify(this.msg))
        console.log(this.ztid);
        let params = {
          page: this.page,
          pageSize: this.pageSize,
          ztid: this.jbxx.ztid,
        }
        let data = await selectZtJscdDj(params)
        this.smryList = data.records
        this.total = data.total
      })
    },
    // 人员选择弹框保存按钮
    submit() {
      this.$router.push('/ryscTable')
    },
    updateItem(row) {
      console.log("关于办理信息", row);
      // return
      this.$router.push({
        path: '/ztqsblxxscb',
        query: {
          typezt: 'fhxq',
          fwdyid: this.fwdyid,
          slid: row.slid,
          row: this.jbxx
        }
      })
    },
    onSubmit() {
      this.getYbsx()
    },
    cz() {
      this.formInline = {}
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.getYbsx()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.getYbsx()
    },
    handleClose() {

    },
    close() {

    },
    selectRow(val) {
      console.log(val);
    },
  },
  watch: {

  }
}

</script>

<style scoped>
.fl {
  float: left;
}

.fr {
  float: right;
}

.container {
  width: 100%;
  position: relative;
  overflow: hidden;
  height: 100%;
  /* box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13); */
  border-radius: 8px;
}

.fileInput {
  display: none;
  position: absolute;
  top: 10px;
  right: 0;
  opacity: 0;
  cursor: pointer;
  height: 32px;
  width: 56px;
  z-index: 1;
}

.elFormLabel {
  font-weight: 700;
}

.elFormLabel .el-radio {
  margin-right: 0;
}

.table_content {
  height: 100%;
}

.tb-container {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

.bg_con {
  width: 100%;
  height: calc(100% - 38px);
}

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.widthw {
  width: 6vw;
}

/* 发起申请弹框 */
.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}
</style>
