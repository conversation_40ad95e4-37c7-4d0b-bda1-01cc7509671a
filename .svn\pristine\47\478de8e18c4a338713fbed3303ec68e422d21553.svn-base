module.exports = {
    // 基本路径
    publicPath: './',
    // 输出文件目录
    outputDir: 'dist',
    // webpack-dev-server 相关配置
    devServer: {
        proxy: {
            '/api': {
                // target: 'http://101.43.246.156:10011/',
                target: 'http://192.168.1.111:10011',
                changeOrigin: true,
                // ws: true,
                pathRewrite: {
                    '^/api': '/'
                }
            }
        }
    },
    chainWebpack: (config) => {
        // 处理vue-pdf打包文件404
        config.module
            .rule('worker')
            .test(/\.worker\.js$/)
            .use('worker-loader').loader('worker-loader')
            .options({
                inline: true,
                fallback: false,
            }).end()
    },
}