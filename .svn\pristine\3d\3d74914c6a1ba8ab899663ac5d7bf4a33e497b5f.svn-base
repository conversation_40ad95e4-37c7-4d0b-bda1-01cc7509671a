{"version": 3, "sources": ["webpack:///src/renderer/view/zczp/childPage/ccdnsjg.vue", "webpack:///./src/renderer/view/zczp/childPage/ccdnsjg.vue?79eb", "webpack:///./src/renderer/view/zczp/childPage/ccdnsjg.vue"], "names": ["ccdnsjg", "data", "dialogObj", "pageInfo", "page", "pageSize", "total", "scList", "dialogVisibleTjnsjg", "zzjgAllList", "dwxx", "rwmc", "jcjd", "computed", "components", "methods", "getDwxxList", "_this", "this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "Object", "dwzc", "sent", "console", "log", "stop", "zz<PERSON>gall", "_this2", "_callee2", "_context2", "zczp", "toXqxx", "row", "windowLocation", "ccdnsjgid", "$router", "push", "path", "query", "rwid", "jgid", "bmmc", "dwmc", "dwid", "fhsyb", "saveToNext", "_this3", "_callee3", "updateScrwListParams", "_context3", "jczt", "$message", "success", "tjnsjg", "dj", "djzt", "zt", "yc", "_this4", "params", "then", "getCcdnsjgListByRwid", "handleSizeChange", "val", "handleCurrentChange", "returnSy", "go", "_this5", "_callee4", "ccdnsjgListPage", "_context4", "records", "dialogZzjgQdxz", "_this6", "_callee6", "selection", "_context6", "$refs", "zzjgAllTable", "for<PERSON>ach", "_ref", "_callee5", "item", "_context5", "bmm", "label", "_x", "apply", "arguments", "code", "message", "type", "watch", "mounted", "_this7", "$route", "childPage_ccdnsjg", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "height", "width", "staticClass", "float", "attrs", "inline", "size", "_v", "_s", "icon", "on", "click", "clear", "border", "header-cell-style", "background", "color", "stripe", "align", "prop", "scopedSlots", "_u", "key", "fn", "scoped", "_e", "$event", "pager-count", "current-page", "page-sizes", "page-size", "layout", "current-change", "size-change", "title", "visible", "update:visible", "ref", "slot", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "wLA8GAA,cACAC,KADA,WAEA,OACAC,aAGAC,UACAC,KAAA,EACAC,SAAA,GACAC,MAAA,GAEAC,UAEAC,qBAAA,EAEAC,eACAC,QACAC,KAAA,GAGAC,KAAA,KAGAC,YACAC,cAEAC,SACAC,YADA,WACA,IAAAC,EAAAC,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAtB,EAAA,OAAAmB,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACA3B,EADAwB,EAAAK,KAEAC,QAAAC,IAAA/B,GACAgB,EAAAP,KAAAT,EAHA,wBAAAwB,EAAAQ,SAAAV,EAAAN,KAAAE,IAKAe,QANA,WAMA,IAAAC,EAAAjB,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAc,IAAA,IAAAnC,EAAA,OAAAmB,EAAAC,EAAAG,KAAA,SAAAa,GAAA,cAAAA,EAAAX,KAAAW,EAAAV,MAAA,cAAAU,EAAAV,KAAA,EACAC,OAAAU,EAAA,EAAAV,GADA,OACA3B,EADAoC,EAAAP,KAEAC,QAAAC,IAAA/B,GACAkC,EAAA1B,YAAAR,OAHA,wBAAAoC,EAAAJ,SAAAG,EAAAD,KAAAhB,IAMAoB,OAZA,SAYAC,GACMZ,OAAAa,EAAA,EAAAb,CAAN,YAAAY,EAAAE,WACAxB,KAAAyB,QAAAC,MACAC,KAAA,iBACAC,OACAC,KAAA7B,KAAA6B,KACAC,KAAAR,EAAAQ,KACAC,KAAAT,EAAAS,KACAC,KAAAhC,KAAAR,KAAAwC,KACAC,KAAAjC,KAAAR,KAAAyC,KACAxC,KAAAO,KAAAP,KACAC,KAAAM,KAAAN,SAKAwC,MA5BA,WAuCAlC,KAAAyB,QAAAC,MACAC,KAAA,UACAC,OAEAC,KAAA7B,KAAA6B,KACApC,KAAAO,KAAAP,KACAC,KAAAM,KAAAN,SAMAyC,WAnDA,WAmDA,IAAAC,EAAApC,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAiC,IAAA,IAAAC,EAAA,OAAApC,EAAAC,EAAAG,KAAA,SAAAiC,GAAA,cAAAA,EAAA/B,KAAA+B,EAAA9B,MAAA,cAEA6B,GACAT,KAAAO,EAAAP,KACAW,KAAA,eAJAD,EAAA9B,KAAA,EAMAC,OAAAU,EAAA,EAAAV,CAAA4B,GANA,OAAAC,EAAA3B,OAQAwB,EAAAK,SAAAC,QAAA,iBAGAN,EAAAX,QAAAC,MACAC,KAAA,SACAC,OAEAC,KAAAO,EAAAP,KACApC,KAAA2C,EAAA3C,KACAC,KAAA0C,EAAA1C,SAjBA,wBAAA6C,EAAAxB,SAAAsB,EAAAD,KAAAnC,IAuBA0C,OA1EA,WA2EA3C,KAAAV,qBAAA,GAGAsD,GA9EA,SA8EAtB,GACAT,QAAAC,IAAAQ,GACA,IAAAuB,EAAAvB,EAAAuB,KACMnC,OAAAa,EAAA,EAAAb,CAAN,YAAAY,EAAAE,WACAxB,KAAAyB,QAAAC,MACAC,KAAA,aACAC,OACAC,KAAA7B,KAAA6B,KACAC,KAAAR,EAAAQ,KACAC,KAAAT,EAAAS,KACAC,KAAAhC,KAAAR,KAAAwC,KACAC,KAAAjC,KAAAR,KAAAyC,KACAxC,KAAAO,KAAAP,KACAoD,OACAnD,KAAAM,KAAAN,KACAoD,GAAA,MAKAC,GAlGA,SAkGAzB,GAAA,IAAA0B,EAAAhD,KACAa,QAAAC,IAAAQ,GACA,IAAA2B,GACApB,KAAA7B,KAAA6B,KACAI,KAAAjC,KAAAR,KAAAyC,KACAH,KAAAR,EAAAQ,MAEApB,OAAAU,EAAA,EAAAV,CAAAuC,GAAAC,KAAA,WACAF,EAAAG,0BAGAnD,KAAAyC,SAAAC,QAAA,SASAU,iBAtHA,SAsHAC,GACArD,KAAAf,SAAAC,KAAA,EACAc,KAAAf,SAAAE,SAAAkE,EACArD,KAAAmD,wBAEAG,oBA3HA,SA2HAD,GACArD,KAAAf,SAAAC,KAAAmE,EACArD,KAAAmD,wBAEAI,SA/HA,WAgIAvD,KAAAyB,QAAA+B,IAAA,IAGAL,qBAnIA,WAmIA,IAAAM,EAAAzD,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAsD,IAAA,IAAAT,EAAAU,EAAA,OAAAzD,EAAAC,EAAAG,KAAA,SAAAsD,GAAA,cAAAA,EAAApD,KAAAoD,EAAAnD,MAAA,cACAI,QAAAC,IAAA2C,EAAAjE,KAAAyC,MACAgB,GACA/D,KAAAuE,EAAAxE,SAAAC,KACAC,SAAAsE,EAAAxE,SAAAE,SACA0C,KAAA4B,EAAA5B,KACAI,KAAAwB,EAAAjE,KAAAyC,MANA2B,EAAAnD,KAAA,EAQAC,OAAAU,EAAA,EAAAV,CAAAuC,GARA,OAQAU,EARAC,EAAAhD,KASAC,QAAAC,IAAA6C,GACAF,EAAApE,OAAAsE,EAAA5E,KAAA8E,QACAJ,EAAAxE,SAAAG,MAAAuE,EAAA5E,KAAAK,MAXA,wBAAAwE,EAAA7C,SAAA2C,EAAAD,KAAAxD,IAcA6D,eAjJA,WAiJA,IAAAC,EAAA/D,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAA4D,IAAA,IAAAC,EAAAnB,EAAA,OAAA5C,EAAAC,EAAAG,KAAA,SAAA4D,GAAA,cAAAA,EAAA1D,KAAA0D,EAAAzD,MAAA,cACAwD,EAAAF,EAAAI,MAAAC,aAAAH,UACApD,QAAAC,IAAA,YAAAmD,GACAA,EAAAI,QAAA,eAAAC,EAAArE,IAAAC,EAAAC,EAAAC,KAAA,SAAAmE,EAAAC,GAAA,IAAAvB,EAAA,OAAA/C,EAAAC,EAAAG,KAAA,SAAAmE,GAAA,cAAAA,EAAAjE,KAAAiE,EAAAhE,MAAA,cACAI,QAAAC,IAAA0D,GACAvB,GACApB,KAAAkC,EAAAlC,KACAI,KAAA8B,EAAAvE,KAAAyC,KACAH,KAAA0C,EAAAE,IACA3C,KAAAyC,EAAAG,MACA9B,KAAA,EACApD,KAAAsE,EAAAtE,MARAgF,EAAAhE,KAAA,EAUAC,OAAAU,EAAA,EAAAV,CAAAuC,GAVA,OAAAwB,EAAA7D,KAAA,wBAAA6D,EAAA1D,SAAAwD,EAAAR,MAAA,gBAAAa,GAAA,OAAAN,EAAAO,MAAA7E,KAAA8E,YAAA,IAYAhC,GACAjB,KAAAkC,EAAAlC,KACAW,KAAA,eAjBA0B,EAAAzD,KAAA,EAmBAC,OAAAU,EAAA,EAAAV,CAAAoC,GAnBA,OAoBA,KApBAoB,EAAAtD,KAoBAmE,MACAhB,EAAAtB,UACAuC,QAAA,YACAC,KAAA,YAiBAlB,EAAAzE,qBAAA,EACAyE,EAAAZ,uBAzCA,yBAAAe,EAAAnD,SAAAiD,EAAAD,KAAA9D,KA+CAiF,SASAC,QAnOA,WAmOA,IAAAC,EAAApF,KACAa,QAAAC,IAAAd,KAAAqF,OAAAzD,OACA5B,KAAAP,KAAAO,KAAAqF,OAAAzD,MAAAnC,KACAO,KAAA6B,KAAA7B,KAAAqF,OAAAzD,MAAAC,KACA7B,KAAAN,KAAAM,KAAAqF,OAAAzD,MAAAlC,KACAmB,QAAAC,IAAA,iBACAD,QAAAC,IAAAd,KAAAqF,OAAAzD,MAAAlC,MACAmB,QAAAC,IAAA,iBACAd,KAAAF,cAAAoD,KAAA,WACAkC,EAAAjC,yBAEAnD,KAAAgB,aCzVesE,GADEC,OAFjB,WAA0B,IAAAC,EAAAxF,KAAayF,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,OAAA,OAAAC,MAAA,UAAgCJ,EAAA,OAAYK,YAAA,SAAmBL,EAAA,WAAgBK,YAAA,mBAAAH,aAA4CI,MAAA,QAAeC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,OAAAH,EAAAa,GAAA,UAAAb,EAAAc,GAAAd,EAAA/F,YAAA,GAAA+F,EAAAa,GAAA,KAAAV,EAAA,WAAiFK,YAAA,mBAAAH,aAA4CI,MAAA,SAAgBC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOjB,KAAA,SAAAmB,KAAA,SAAAG,KAAA,sBAA4DC,IAAKC,MAAAjB,EAAAtD,SAAmBsD,EAAAa,GAAA,qCAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAA6EE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOjB,KAAA,UAAAmB,KAAA,SAAAG,KAAA,wBAA+DC,IAAKC,MAAAjB,EAAArD,cAAwBqD,EAAAa,GAAA,sCAAAb,EAAAa,GAAA,KAAAV,EAAA,gBAA8EE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOjB,KAAA,UAAAmB,KAAA,SAAAG,KAAA,wBAA+DC,IAAKC,MAAAjB,EAAA7C,UAAoB6C,EAAAa,GAAA,0CAAAb,EAAAa,GAAA,KAAAV,EAAA,OAAyEE,aAAaa,MAAA,WAAgB,GAAAlB,EAAAa,GAAA,KAAAV,EAAA,OAA4BE,aAAaC,OAAA,qCAA4CH,EAAA,YAAiBE,aAAaE,MAAA,OAAAY,OAAA,qBAA4CT,OAAQnH,KAAAyG,EAAAnG,OAAAsH,OAAA,GAAAC,qBAAmDC,WAAA,UAAAC,MAAA,WAA0ChB,OAAA,OAAAiB,OAAA,MAA8BpB,EAAA,mBAAwBO,OAAOjB,KAAA,QAAAc,MAAA,KAAApB,MAAA,KAAAqC,MAAA,YAA2DxB,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOe,KAAA,OAAAtC,MAAA,UAA8Ba,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOvB,MAAA,OAAAoB,MAAA,OAA6BmB,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAkC,OAAA3B,EAAA,UAAA2B,EAAAhG,IAAAuB,KAAA8C,EAAA,QAAAH,EAAAa,GAAA,SAAAb,EAAA+B,KAAA/B,EAAAa,GAAA,QAAAiB,EAAAhG,IAAAuB,KAAA8C,EAAA,QAAAH,EAAAa,GAAA,UAAAb,EAAA+B,KAAA/B,EAAAa,GAAA,QAAAiB,EAAAhG,IAAAuB,KAAA8C,EAAA,QAAAH,EAAAa,GAAA,UAAAb,EAAA+B,cAAoO/B,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOe,KAAA,GAAAtC,MAAA,KAAAoB,MAAA,OAAqCmB,YAAA1B,EAAA2B,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAkC,UAAAA,EAAAhG,IAAAuB,KAAA8C,EAAA,aAA+CO,OAAOE,KAAA,QAAAnB,KAAA,QAA6BuB,IAAKC,MAAA,SAAAe,GAAyB,OAAAhC,EAAA5C,GAAA0E,EAAAhG,SAA4BkE,EAAAa,GAAA,QAAAb,EAAA+B,KAAA/B,EAAAa,GAAA,QAAAiB,EAAAhG,IAAAuB,KAAA8C,EAAA,aAA6EO,OAAOE,KAAA,QAAAnB,KAAA,QAA6BuB,IAAKC,MAAA,SAAAe,GAAyB,OAAAhC,EAAA5C,GAAA0E,EAAAhG,SAA4BkE,EAAAa,GAAA,UAAAb,EAAA+B,KAAA/B,EAAAa,GAAA,QAAAiB,EAAAhG,IAAAuB,KAAA8C,EAAA,aAA+EO,OAAOE,KAAA,QAAAnB,KAAA,QAA6BuB,IAAKC,MAAA,SAAAe,GAAyB,OAAAhC,EAAAnE,OAAAiG,EAAAhG,SAAgCkE,EAAAa,GAAA,QAAAb,EAAA+B,KAAA/B,EAAAa,GAAA,KAAAV,EAAA,aAAsDO,OAAOE,KAAA,QAAAnB,KAAA,QAA6BuB,IAAKC,MAAA,SAAAe,GAAyB,OAAAhC,EAAAzC,GAAAuE,EAAAhG,SAA4BkE,EAAAa,GAAA,gBAAsB,OAAAb,EAAAa,GAAA,KAAAV,EAAA,OAAgCE,aAAac,OAAA,uBAA8BhB,EAAA,iBAAsBO,OAAOW,WAAA,GAAAY,cAAA,EAAAC,eAAAlC,EAAAvG,SAAAC,KAAAyI,cAAA,YAAAC,YAAApC,EAAAvG,SAAAE,SAAA0I,OAAA,yCAAAzI,MAAAoG,EAAAvG,SAAAG,OAA6MoH,IAAKsB,iBAAAtC,EAAAlC,oBAAAyE,cAAAvC,EAAApC,qBAA6E,GAAAoC,EAAAa,GAAA,KAAAV,EAAA,aAAkCO,OAAO8B,MAAA,SAAAC,QAAAzC,EAAAlG,oBAAAyG,MAAA,OAAiES,IAAK0B,iBAAA,SAAAV,GAAkChC,EAAAlG,oBAAAkI,MAAiC7B,EAAA,OAAAA,EAAA,YAA2BwC,IAAA,eAAAtC,aAAgCE,MAAA,OAAAY,OAAA,qBAA4CT,OAAQnH,KAAAyG,EAAAjG,YAAAoH,OAAA,GAAAC,qBAAwDC,WAAA,UAAAC,MAAA,WAA0ChB,OAAA,OAAAiB,OAAA,MAA8BpB,EAAA,mBAAwBO,OAAOjB,KAAA,YAAAc,MAAA,QAAiCP,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOjB,KAAA,QAAAc,MAAA,KAAApB,MAAA,QAA0Ca,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOe,KAAA,QAAAtC,MAAA,UAA+Ba,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOvB,MAAA,YAAkBa,EAAAa,GAAA,eAAAb,EAAAa,GAAA,KAAAV,EAAA,QAA+CK,YAAA,gBAAAE,OAAmCkC,KAAA,UAAgBA,KAAA,WAAezC,EAAA,aAAkBO,OAAOjB,KAAA,UAAAmB,KAAA,UAAiCI,IAAKC,MAAAjB,EAAA1B,kBAA4B0B,EAAAa,GAAA,SAAAb,EAAAa,GAAA,KAAAV,EAAA,aAA8CO,OAAOjB,KAAA,UAAAmB,KAAA,UAAiCI,IAAKC,MAAA,SAAAe,GAAyBhC,EAAAlG,qBAAA,MAAkCkG,EAAAa,GAAA,oBAE3qIgC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACE1J,EACAwG,GATF,EAVA,SAAAmD,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/55.7e0173c712b8c5d79570.js", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%;width: 100%;\">\r\n    <!---->\r\n    <div class=\"mhcx\">\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:left\">\r\n        <el-form-item style=\"float: right;\">\r\n          <div>当前审查任务：{{ rwmc }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!---->\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:right\">\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"danger\" size=\"medium\" icon=\"el-icon-caret-left\" @click=\"fhsyb\">\r\n            返回上一步\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"primary\" size=\"medium\" icon=\"el-icon-document-add\" @click=\"saveToNext\">\r\n            保存至下一步\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"warning\" size=\"medium\" icon=\"el-icon-document-add\" @click=\"tjnsjg\">\r\n            添加内设机构\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div style=\"clear: both;\"></div>\r\n    </div>\r\n    <!---->\r\n    <!-- 表格区域 -->\r\n    <div style=\"height: calc(100% - 58px - 34px - 42px);\">\r\n      <el-table :data=\"scList\" border :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\"\r\n        style=\"width: 100%;border:1px solid #EBEEF5;\" height=\"100%\" stripe>\r\n        <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bmmc\" label=\"内设机构\"></el-table-column>\r\n        <el-table-column label=\"登记状态\" width=\"250\">\r\n          <template slot-scope=\"scoped\">\r\n            <div>\r\n              <span v-if=\"scoped.row.djzt == 0\">待登记</span>\r\n              <span v-if=\"scoped.row.djzt == 1\">继续登记</span>\r\n              <span v-if=\"scoped.row.djzt == 2\">完成登记</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"\" label=\"操作\" width=\"120\">\r\n          <template slot-scope=\"scoped\">\r\n            <el-button v-if=\"scoped.row.djzt == 0\" size=\"small\" type=\"text\" @click=\"dj(scoped.row)\">登记</el-button>\r\n            <el-button v-if=\"scoped.row.djzt == 1\" size=\"small\" type=\"text\" @click=\"dj(scoped.row)\">继续登记</el-button>\r\n            <el-button v-if=\"scoped.row.djzt == 2\" size=\"small\" type=\"text\" @click=\"toXqxx(scoped.row)\">详情</el-button>\r\n            <el-button size=\"small\" type=\"text\" @click=\"yc(scoped.row)\">移除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <!---->\r\n    <!-- 分页组件区域 -->\r\n    <div style=\"border: 1px solid #ebeef5\">\r\n      <el-pagination background @current-change=\"handleCurrentChange\" @size-change=\"handleSizeChange\" :pager-count=\"5\"\r\n        :current-page=\"pageInfo.page\" :page-sizes=\"[5, 10, 20, 30]\" :page-size=\"pageInfo.pageSize\"\r\n        layout=\"total, prev, pager, sizes,next, jumper\" :total=\"pageInfo.total\">\r\n      </el-pagination>\r\n    </div>\r\n    <!--添加内设机构dialog-->\r\n    <el-dialog title=\"添加内设机构\" :visible.sync=\"dialogVisibleTjnsjg\" width=\"50%\">\r\n      <div>\r\n        <el-table :data=\"zzjgAllList\" ref=\"zzjgAllTable\" border\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" style=\"width: 100%;border:1px solid #EBEEF5;\"\r\n          height=\"50vh\" stripe>\r\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\"></el-table-column>\r\n          <el-table-column prop=\"label\" label=\"内设机构\"></el-table-column>\r\n          <el-table-column label=\"是否保密部门\">是</el-table-column>\r\n        </el-table>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" size=\"medium\" @click=\"dialogZzjgQdxz\">保 存</el-button>\r\n        <el-button type=\"warning\" size=\"medium\" @click=\"dialogVisibleTjnsjg = false\">关 闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!---->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\n// 组织机构\r\n// import { selectAllZzjg } from '../../../../db/zzjgdb'\r\n\r\nimport { setZczpIdsObj, getZczpIdsObj, removeZczpIdsObjField } from '../../../../utils/windowLocation'\r\nimport { getAllBmZzjgByDwid, addZczpZzjgxx, updateJcrw, selectZczpZzjgxxPage,deleteZczpZzjgxx, } from \"../../../../api/zczp\"\r\nimport {\r\n  // 获取注册信息\r\n  getDwxx,\r\n} from '../../../../api/dwzc'\r\n// import {\r\n//   // 通过任务ID获取审查任务信息\r\n//   selectScrwByRwid,\r\n//   // 通过任务ID获取抽查的内设机构集合\r\n//   selectCcdnsjgListByRwid,\r\n//   // 插入抽查的内设机构列表中(批量)\r\n//   insertCcdnsjgList,\r\n//   // 通过抽查的内设机构流水ID移除抽查的内设机构表\r\n//   deleteCcdnsjgListByID,\r\n//   // 更新审查任务\r\n//   updateScrwListZt\r\n// } from '../../../../db/zczpdb'\r\n\r\nimport { writeOptionsLog } from '../../../../utils/logUtils'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogObj: {\r\n        // rwid: 'D66215EC-1B9A-408A-BEE8-7A9396165EA7'\r\n      },\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 10,\r\n        total: 0\r\n      },\r\n      scList: [],\r\n      // dialog\r\n      dialogVisibleTjnsjg: false,\r\n      // dialog内所有的内设机构\r\n      zzjgAllList: [],\r\n      dwxx: {},\r\n      rwmc:'',\r\n      // rwid: '',\r\n      //\r\n      jcjd:'',\r\n    }\r\n  },\r\n  computed: {},\r\n  components: {\r\n  },\r\n  methods: {\r\n    async getDwxxList() {\r\n      let data = await getDwxx()\r\n      console.log(data);\r\n      this.dwxx = data\r\n    },\r\n    async zzjgall() {\r\n      let data = await getAllBmZzjgByDwid()\r\n      console.log(data);\r\n      this.zzjgAllList = data.data\r\n    },\r\n    // 调转到详情信息页面（不可编辑的页面）\r\n    toXqxx(row) {\r\n      setZczpIdsObj('ccdnsjgid', row.ccdnsjgid)\r\n      this.$router.push({\r\n        path: '/ccdnsjgDjXqxx',\r\n        query: {\r\n          rwid: this.rwid,\r\n          jgid: row.jgid,\r\n          bmmc:row.bmmc,\r\n          dwmc:this.dwxx.dwmc,\r\n          dwid:this.dwxx.dwid,\r\n          rwmc:this.rwmc,\r\n          jcjd:this.jcjd,\r\n        }\r\n      })\r\n    },\r\n    // 返回上一步\r\n    fhsyb() {\r\n      // 更新任务状态码\r\n      // let updateScrwListParams = {\r\n      //   rwid: this.dialogObj.rwid,\r\n      //   zt: 1\r\n      // }\r\n      // let bool = updateScrwListZt(updateScrwListParams)\r\n      // if (bool) {\r\n      //   this.$message.success('抽查的内设机构记录录入成功')\r\n        // 写入操作日志\r\n        // writeOptionsLog('yybs-ccdnsjg', '返回上一步', this.dialogObj)\r\n        this.$router.push({\r\n          path: '/xjzczp',\r\n          query: {\r\n            // 任务ID\r\n            rwid: this.rwid,\r\n            rwmc:this.rwmc,\r\n            jcjd:this.jcjd\r\n          }\r\n        })\r\n      // }\r\n    },\r\n    // 保存至下一步\r\n    async saveToNext() {\r\n      // 更新任务状态码\r\n      let updateScrwListParams = {\r\n        rwid: this.rwid,\r\n        jczt: '抽查的内设机构保存完成'\r\n      }\r\n      let bool = await updateJcrw(updateScrwListParams)\r\n      if (bool) {\r\n        this.$message.success('抽查的内设机构记录录入成功')\r\n        // 写入操作日志\r\n        // writeOptionsLog('yybs-ccdnsjg', '抽查的内设机构保存完成', this.dialogObj)\r\n        this.$router.push({\r\n          path: '/ccdry',\r\n          query: {\r\n            // 任务ID\r\n            rwid: this.rwid,\r\n            rwmc: this.rwmc,\r\n            jcjd:this.jcjd,\r\n          }\r\n        })\r\n      }\r\n    },\r\n    // 添加内设机构\r\n    tjnsjg() {\r\n      this.dialogVisibleTjnsjg = true\r\n    },\r\n    // 登记\r\n    dj(row) {\r\n      console.log(row);\r\n      let djzt = row.djzt;\r\n      setZczpIdsObj('ccdnsjgid', row.ccdnsjgid)\r\n      this.$router.push({\r\n        path: '/ccdnsjgDj',\r\n        query: {\r\n          rwid: this.rwid,\r\n          jgid: row.jgid,\r\n          bmmc:row.bmmc,\r\n          dwmc:this.dwxx.dwmc,\r\n          dwid:this.dwxx.dwid,\r\n          rwmc:this.rwmc,\r\n          djzt:djzt,\r\n          jcjd:this.jcjd,\r\n          zt:0\r\n        }\r\n      })\r\n    },\r\n    // 移除，使用[抽查的内设机构流水ID]移除该记录的[抽查的内设机构表]和[内设机构评分记录表]\r\n    yc(row) {\r\n      console.log(row)\r\n      let params = {\r\n        rwid:this.rwid,\r\n        dwid:this.dwxx.dwid,\r\n        jgid:row.jgid,\r\n      }\r\n      let bool = deleteZczpZzjgxx(params).then(()=>{\r\n        this.getCcdnsjgListByRwid()\r\n      })\r\n      if (bool) {\r\n        this.$message.success('移除成功')\r\n        // local自查自评对象中移除该属性\r\n        // removeZczpIdsObjField('ccdnsjgid')\r\n        // 写入操作日志\r\n        // writeOptionsLog('yybs-ccdnsjgDj', '内设机构' + row.zzjgmc + '登记信息移除成功', row)\r\n        // 刷新数据\r\n        \r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      this.pageInfo.page = 1\r\n      this.pageInfo.pageSize = val\r\n      this.getCcdnsjgListByRwid()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.pageInfo.page = val\r\n      this.getCcdnsjgListByRwid()\r\n    },\r\n    returnSy() {\r\n      this.$router.go(-1)\r\n    },\r\n    // 获取任务下抽查的内设机构信息\r\n    async getCcdnsjgListByRwid() {\r\n      console.log(this.dwxx.dwid);\r\n      let params = {\r\n        page:this.pageInfo.page,\r\n        pageSize:this.pageInfo.pageSize,\r\n        rwid: this.rwid,\r\n        dwid: this.dwxx.dwid\r\n      }\r\n      let ccdnsjgListPage = await selectZczpZzjgxxPage(params)\r\n      console.log(ccdnsjgListPage);\r\n      this.scList = ccdnsjgListPage.data.records\r\n      this.pageInfo.total = ccdnsjgListPage.data.total\r\n    },\r\n    // dialog组织机构确认选择事件触发\r\n    async dialogZzjgQdxz() {\r\n      let selection = this.$refs.zzjgAllTable.selection\r\n      console.log('selection', selection)\r\n      selection.forEach(async (item) => {\r\n        console.log(item);\r\n        let params = {\r\n          rwid: this.rwid,\r\n          dwid: this.dwxx.dwid,\r\n          jgid: item.bmm,\r\n          bmmc: item.label,\r\n          djzt: 0,\r\n          rwmc:this.rwmc,\r\n        }\r\n        let data = await addZczpZzjgxx(params)\r\n      });\r\n      let zt = {\r\n        rwid: this.rwid,\r\n        jczt: '抽查的内设机构临时保存'\r\n      }\r\n      let xx = await updateJcrw(zt)\r\n      if (xx.code == 10000) {\r\n        this.$message({\r\n          message: '内设单位创建成功!',\r\n          type: 'success'\r\n        });\r\n      }\r\n      // 加入到抽查的内设机构表中\r\n      // let bool = insertCcdnsjgList(selection, this.dialogObj.rwid)\r\n      // if (bool) {\r\n      //   // 将任务状态码拨到2-抽查的内设机构临时保存\r\n      //   // 更新任务状态码\r\n      //   let updateScrwListParams = {\r\n      //     rwid: this.dialogObj.rwid,\r\n      //     zt: 2\r\n      //   }\r\n      //   bool = updateScrwListZt(updateScrwListParams)\r\n      //   if (bool) {\r\n      //     // 写入操作日志\r\n      //     writeOptionsLog('yybs-ccdnsjg', '添加抽查的内设机构', this.dialogObj)\r\n      //     // 获取抽查的的内设机构\r\n      this.dialogVisibleTjnsjg = false\r\n      this.getCcdnsjgListByRwid()\r\n\r\n      //   }\r\n      // }\r\n    }\r\n  },\r\n  watch: {\r\n    // 'rwid'(newVal, oldVal) {\r\n    //   console.log('内设机构 rwid', newVal, oldVal)\r\n    //   // 获取审查任务表历史信息\r\n    //   let scrw = selectScrwByRwid(newVal)\r\n    //   console.log('scrw历史信息', scrw)\r\n    //   this.dialogObj = scrw\r\n    // }\r\n  },\r\n  mounted() {\r\n    console.log(this.$route.query);\r\n    this.rwmc = this.$route.query.rwmc,\r\n    this.rwid = this.$route.query.rwid,\r\n    this.jcjd = this.$route.query.jcjd,\r\n    console.log(\"=============\")\r\n    console.log(this.$route.query.jcjd)\r\n    console.log(\"=============\")\r\n    this.getDwxxList().then(()=>{\r\n      this.getCcdnsjgListByRwid()\r\n    })\r\n    this.zzjgall()\r\n\r\n    // let params = this.$route.query\r\n    // let params = getZczpIdsObj()\r\n    // console.log('内设机构 params', params)\r\n    // if (params) {\r\n    //   this.dialogObj = params\r\n    // }\r\n    // 获取所有的组织机构\r\n    // this.zzjgAllList = selectAllZzjg()\r\n\r\n    // console.log('获取所有的组织机构', this.zzjgAllList)\r\n    ////////////\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped></style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/zczp/childPage/ccdnsjg.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"height\":\"100%\",\"width\":\"100%\"}},[_c('div',{staticClass:\"mhcx\"},[_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"left\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('div',[_vm._v(\"当前审查任务：\"+_vm._s(_vm.rwmc))])])],1),_vm._v(\" \"),_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"right\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"medium\",\"icon\":\"el-icon-caret-left\"},on:{\"click\":_vm.fhsyb}},[_vm._v(\"\\n          返回上一步\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\",\"icon\":\"el-icon-document-add\"},on:{\"click\":_vm.saveToNext}},[_vm._v(\"\\n          保存至下一步\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"medium\",\"icon\":\"el-icon-document-add\"},on:{\"click\":_vm.tjnsjg}},[_vm._v(\"\\n          添加内设机构\\n        \")])],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1),_vm._v(\" \"),_c('div',{staticStyle:{\"height\":\"calc(100% - 58px - 34px - 42px)\"}},[_c('el-table',{staticStyle:{\"width\":\"100%\",\"border\":\"1px solid #EBEEF5\"},attrs:{\"data\":_vm.scList,\"border\":\"\",\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"height\":\"100%\",\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"bmmc\",\"label\":\"内设机构\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"登记状态\",\"width\":\"250\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scoped){return [_c('div',[(scoped.row.djzt == 0)?_c('span',[_vm._v(\"待登记\")]):_vm._e(),_vm._v(\" \"),(scoped.row.djzt == 1)?_c('span',[_vm._v(\"继续登记\")]):_vm._e(),_vm._v(\" \"),(scoped.row.djzt == 2)?_c('span',[_vm._v(\"完成登记\")]):_vm._e()])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"\",\"label\":\"操作\",\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scoped){return [(scoped.row.djzt == 0)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.dj(scoped.row)}}},[_vm._v(\"登记\")]):_vm._e(),_vm._v(\" \"),(scoped.row.djzt == 1)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.dj(scoped.row)}}},[_vm._v(\"继续登记\")]):_vm._e(),_vm._v(\" \"),(scoped.row.djzt == 2)?_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.toXqxx(scoped.row)}}},[_vm._v(\"详情\")]):_vm._e(),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.yc(scoped.row)}}},[_vm._v(\"移除\")])]}}])})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"border\":\"1px solid #ebeef5\"}},[_c('el-pagination',{attrs:{\"background\":\"\",\"pager-count\":5,\"current-page\":_vm.pageInfo.page,\"page-sizes\":[5, 10, 20, 30],\"page-size\":_vm.pageInfo.pageSize,\"layout\":\"total, prev, pager, sizes,next, jumper\",\"total\":_vm.pageInfo.total},on:{\"current-change\":_vm.handleCurrentChange,\"size-change\":_vm.handleSizeChange}})],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"添加内设机构\",\"visible\":_vm.dialogVisibleTjnsjg,\"width\":\"50%\"},on:{\"update:visible\":function($event){_vm.dialogVisibleTjnsjg=$event}}},[_c('div',[_c('el-table',{ref:\"zzjgAllTable\",staticStyle:{\"width\":\"100%\",\"border\":\"1px solid #EBEEF5\"},attrs:{\"data\":_vm.zzjgAllList,\"border\":\"\",\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"height\":\"50vh\",\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"label\",\"label\":\"内设机构\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"是否保密部门\"}},[_vm._v(\"是\")])],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\"},on:{\"click\":_vm.dialogZzjgQdxz}},[_vm._v(\"保 存\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"medium\"},on:{\"click\":function($event){_vm.dialogVisibleTjnsjg = false}}},[_vm._v(\"关 闭\")])],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-ca2b581a\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/zczp/childPage/ccdnsjg.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-ca2b581a\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ccdnsjg.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdnsjg.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdnsjg.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-ca2b581a\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ccdnsjg.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-ca2b581a\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/zczp/childPage/ccdnsjg.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}