<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div
      style="width: 100%; position: relative; overflow: hidden; height: 100%"
    >
      <div class="dabg" style="height: 100%">
        <div class="content" style="height: 100%">
          <div class="table" style="height: 100%">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form
                :inline="true"
                :model="formInline"
                size="medium"
                class="demo-form-inline"
                style="float: left"
              >
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 10px;
                  "
                >
                  <el-form-item style="font-weight: 700" label="设备名称">
                    <el-input
                      v-model="formInline.equipmentName"
                      clearable
                      placeholder="设备名称"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700" label="设备编号">
                    <el-input
                      v-model="formInline.equipmentCode"
                      clearable
                      placeholder="设备编号"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700" label="资产主要类型">
                    <el-select
                      v-model="formInline.equipmentMainType"
                      clearable
                      @change="sbzylxChange"
                      placeholder="请选择主要类型"
                      class="widthx"
                    >
                      <el-option
                        v-for="item in sbzylxList"
                        :label="item.mc"
                        :value="item.id"
                        :key="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item style="font-weight: 700" label="设备类型">
                    <el-select
                      v-model="formInline.equipmentType"
                      clearable
                      placeholder="请选择类型"
                      class="widthx"
                    >
                      <el-option
                        v-for="item in sblxList"
                        :label="item.mc"
                        :value="item.csz"
                        :key="item.csz"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item style="font-weight: 700" label="使用状态">
                    <el-select
                      v-model="formInline.useStatus"
                      clearable
                      placeholder="请选择类型"
                      class="widthx"
                    >
                      <el-option
                        v-for="item in syztList"
                        :label="item.mc"
                        :value="item.id"
                        :key="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                  "
                >
                  <el-form-item style="font-weight: 700" label="机房名称">
                    <el-input
                      v-model="formInline.computerRoomName"
                      clearable
                      placeholder="机房名称"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700" label="机房编号">
                    <el-input
                      v-model="formInline.computerRoomCode"
                      clearable
                      placeholder="机房编号"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700" label="机柜名称">
                    <el-input
                      v-model="formInline.cabinetName"
                      clearable
                      placeholder="机柜名称"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item style="font-weight: 700" label="机柜编号">
                    <el-input
                      v-model="formInline.cabinetCode"
                      clearable
                      placeholder="机柜编号"
                    >
                    </el-input>
                  </el-form-item>
                </div>

                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    float: right;
                  "
                >
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-search"
                      @click="onSubmit"
                      >查询</el-button
                    >
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      type="warning"
                      icon="el-icon-circle-close"
                      @click="cz"
                      >重置</el-button
                    >
                  </el-form-item>
                  <!-- <el-form-item>
                    <el-button
                      type="success"
                      size="medium"
                      @click="xzaqcp"
                      icon="el-icon-plus"
                    >
                      新增
                    </el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      type="danger"
                      size="medium"
                      @click="shanchu"
                      icon="el-icon-delete-solid"
                    >
                      删除
                    </el-button>
                  </el-form-item> -->
                  <!-- <el-form-item>
                    <el-button type="primary" @click="qygddy(4)" size="medium">
                      巡检工单打印
                    </el-button>
                  </el-form-item> -->
                  <el-form-item>
                    <el-button type="primary" @click="qygddy(1)"
                      >迁移工单打印</el-button
                    >
                  </el-form-item>
                  <!-- <el-form-item>
                    <el-button type="primary" @click="qygddy(2)"
                      >销毁工单打印</el-button
                    >
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="qygddy(3)"
                      >故障处理打印</el-button
                    >
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      type="primary"
                      size="medium"
                      @click="uploadFile()"
                    >
                      上传巡检回执单
                    </el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      type="primary"
                      size="medium"
                      @click="uploadFileXh()"
                    >
                      上传销毁回执单
                    </el-button>
                  </el-form-item> -->
                  <el-form-item>
                    <el-button
                      type="primary"
                      size="medium"
                      @click="uploadFileQy()"
                    >
                      上传迁移回执单
                    </el-button>
                  </el-form-item>
                  <!-- <el-form-item>
                    <el-button
                      type="primary"
                      size="medium"
                      @click="uploadFileGz()"
                    >
                      上传故障处理回执单
                    </el-button>
                  </el-form-item> -->
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                  "
                ></div>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%">
              <div class="table_content" style="height: 100%">
                <el-table
                  :data="aqcpList"
                  border
                  @selection-change="selectRow"
                  :header-cell-style="{
                    background: '#EEF7FF',
                    color: '#4D91F8',
                  }"
                  style="width: 100%; border: 1px solid #ebeef5"
                  height="calc(100% - 193px)"
                  stripe
                >
                  <el-table-column type="selection" width="55" align="center">
                  </el-table-column>
                  <el-table-column
                    type="index"
                    width="60"
                    label="序号"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="equipmentCode"
                    label="设备编号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="equipmentName"
                    label="设备名称"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="computerRoomCode"
                    label="机房编号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="computerRoomName"
                    label="机房名称"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="cabinetCode"
                    label="机柜编号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="cabinetName"
                    label="机柜名称"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="equipmentMainType"
                    label="资产主要类型"
                    :formatter="forsbzylx"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="equipmentType"
                    label="设备类型"
                    sortable
                  ></el-table-column>
                  <el-table-column prop="ip" label="IP地址"></el-table-column>
                  <el-table-column
                    prop="equipmentModel"
                    label="设备型号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="equipmentSerialNumber"
                    label="序列号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="useStatus"
                    :formatter="forlx"
                    label="迁移状态"
                    sortable
                  ></el-table-column>
                  <!-- <el-table-column prop="approveStatus" label="审批状态"></el-table-column> -->
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <!-- <el-button
                        size="medium"
                        type="text"
                        @click="updateItem(scoped.row)"
                        >修改
                      </el-button> -->
                      <el-button
                        size="medium"
                        type="text"
                        @click="xqyl(scoped.row)"
                        >详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5">
                  <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    :pager-count="5"
                    :current-page="page"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper"
                    :total="total"
                  >
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="qydialogVisible"
      @close="close('formName')"
      width="30%"
    >
      <template #title>
        <img
          src="./img/title.png"
          style="margin-right: 15px"
          alt="机房巡检工单图标"
        />
        {{ dialogTitle }}
      </template>
      <el-form
        ref="formName"
        :rules="qyRules"
        :model="formqy"
        size="mini"
        label-width="180px"
      >
        <!-- <el-form-item label="迁移设备" prop="computerRoomld" v-if="gdlx == 1">
          <el-select
            v-model="formqy.computerRoomld"
            clearable
            @change="computerRoomChange"
            placeholder="请选择迁移设备"
            class="widthx"
          >
            <el-option
              v-for="item in computerRoomList"
              :label="item.name"
              :value="item.code"
              :key="item.code"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item
          label="迁移所在地"
          prop="relocationLocation"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationLocation"
            clearable
            @change="locationChange"
            placeholder="请选择迁移所在地"
            class="widthx"
          >
            <el-option
              v-for="item in locationList"
              :label="item.name"
              :value="item.code"
              :key="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移所在区县"
          prop="relocationArea"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationArea"
            clearable
            @change="areaChange"
            placeholder="请选择迁移所在区县"
            class="widthx"
          >
            <el-option
              v-for="item in areaList"
              :label="item.name"
              :value="item.code"
              :key="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移所在机构"
          prop="relocationInstitution"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationInstitution"
            clearable
            @change="institutionChange"
            placeholder="请选择迁移所在机构"
            class="widthx"
          >
            <el-option
              v-for="item in institutionList"
              :label="item.dwmc"
              :value="item.dwid"
              :key="item.dwid"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移机房"
          prop="relocationComputerRoomId"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationComputerRoomId"
            clearable
            @change="computerChange"
            placeholder="请选择迁移机房"
            class="widthx"
          >
            <el-option
              v-for="item in computerList"
              :label="item.computerRoomName"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移机柜"
          prop="relocationCabinetId"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationCabinetId"
            clearable
            placeholder="请选择迁移机柜"
            class="widthx"
          >
            <el-option
              v-for="item in cabinetList"
              :label="item.cabinet_name"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移截至时间"
          prop="relocationTime"
          v-if="gdlx == 1"
        >
          <el-date-picker
            :disabled="disabled"
            v-model="formqy.relocationTime"
            style="width: 100%"
            clearable
            type="date"
            placeholder="选择时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="销毁截至时间" prop="destroyTime" v-if="gdlx == 2">
          <el-date-picker
            :disabled="disabled"
            v-model="formqy.destroyTime"
            style="width: 100%"
            clearable
            type="date"
            placeholder="选择时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="故障处理截至时间"
          prop="maintenanceTime"
          v-if="gdlx == 3"
        >
          <el-date-picker
            :disabled="disabled"
            v-model="formqy.maintenanceTime"
            style="width: 100%"
            clearable
            type="date"
            placeholder="选择时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="机房巡检截至时间"
          prop="inspectionTime"
          v-if="gdlx == 4"
        >
          <el-date-picker
            :disabled="disabled"
            v-model="formqy.inspectionTime"
            style="width: 100%"
            clearable
            type="date"
            placeholder="选择时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          v-if="gdlx != 4"
          @click="printQy(gdlx, 'formName')"
          >确 定</el-button
        >
        <el-button type="primary" v-if="gdlx == 4" @click="print()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="温度数据"
      :visible.sync="wddialogVisible"
      width="1100px"
      :before-close="handleClose"
    >
      <div class="top_title">
        <el-date-picker
          v-model="tMonth"
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择月份"
        >
        </el-date-picker>
        <el-button type="primary" @click="tMonthClick">查询</el-button>
      </div>

      <div ref="charts" id="main" style="width: 1040px; height: 600px"></div>
      <div style="color: #fff; font-size: 20px; font-weight: 700">湿度数据</div>
      <!-- <div class="top_title">
        <el-date-picker
          v-model="hMonth"
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择月份"
        >
        </el-date-picker>
        <el-button type="primary" @click="hMonthClick">查询</el-button>
      </div> -->
      <div ref="charts" id="main1" style="width: 1040px; height: 600px"></div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisibleQygd" ref="dialog" width="28.7%">
      <template #title>
        <img
          src="./img/title.png"
          style="margin-right: 15px"
          alt="机房巡检工单图标"
        />
        {{ dialogTitle }}
      </template>
      <div class="formDialog">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">资产编号：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.computerRoomCode }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">资产名称：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.computerRoomName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">机房地址：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.address }}</div>
        </div>
      </div>
      <div class="fjx"></div>
      <div class="formDialog formDialog1">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">提交人：</div>
          <div class="formDialogCon1">{{ this.xjgddyQbj.createByName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">打印时间：</div>
          <div class="formDialogCon1">
            {{ this.xjgddyQbj.printTime }}
          </div>
        </div>
        <div class="flexAlign">
          <div class="formDialogItem">操作时间：</div>
          <div class="formDialogCon1">
            {{ this.xjgddyQbj.operateTime }}
          </div>
        </div>
        <div class="ewm">
          <img :src="img" width="130" height="130" />
        </div>
      </div>
      <div class="btnRight" v-if="!isPrinting">
        <el-button
          @click="printToPDFQy"
          type="primary"
          class="btnRightItem"
          size="mini"
          >打 印</el-button
        >
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" width="28.7%" ref="dialog">
      <template #title>
        <img
          src="./img/title.png"
          style="margin-right: 15px"
          alt="机房巡检工单图标"
        />
        机房巡检工单
      </template>
      <div class="formDialog">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">资产编号：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.computerRoomCode }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">资产名称：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.computerRoomName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">机房地址：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.address }}</div>
        </div>
      </div>
      <div class="fjx"></div>
      <div class="formDialog formDialog1">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">提交人：</div>
          <div class="formDialogCon1">{{ this.xjgddyQbj.createByName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">打印时间：</div>
          <div class="formDialogCon1">
            {{ this.xjgddyQbj.printTime }}
          </div>
        </div>
        <div class="flexAlign">
          <div class="formDialogItem">操作时间：</div>
          <div class="formDialogCon1">
            {{ this.xjgddyQbj.operateTime }}
          </div>
        </div>

        <div class="ewm">
          <img :src="img" width="130" height="130" />
        </div>
      </div>
      <div class="btnRight" v-if="!isPrinting">
        <el-button
          type="primary"
          @click="printToPDF"
          class="btnRightItem"
          size="mini"
          >打 印</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import * as echarts from "echarts";
import {
  sbQueryByCondition,
  deleteEquipment,
  deleteCabinet,
  getEquipmentType,
  uploadMigrateEquipment,
  uploadDestructionEquipment,
  uploadFaultHandling,
  queryCabinetPage, //分页查询
  getCabinetTemerature,
  getCabinetHumidity,
  uploadInspectionForm,
  downloadInspectionForm,
  downloadInspectionComputerRoom,
  getCity,
  getArea,
  getDwxx,
  getComputerRoomList,
  getCabinetList,
  migrateEquipment,
  destructionEquipment,
  faultHandling,
  downloadMigrateEquipment,
  downloadDestructionEquipment,
  downloadFaultHandling,
  downloadMigrate,
  downloadDestruction,
  downloadFaultHandlingEquipment,
  downloadInspection,
  getEquipmentByComputerRoom,
} from "../../../../api/shma";
import AraleQRCode from "arale-qrcode";

export default {
  components: {},
  props: {},
  data() {
    return {
      disabled: false,
      isPrinting: false, //是否正在打印
      wddialogVisible: false,
      tMonth: "", //选择月份
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" },
      ],
      xjgddyQbj: {},
      qyRules: {
        computerRoomld: [
          { required: true, message: "请选择迁移设备", trigger: "change" },
        ],
        relocationLocation: [
          { required: true, message: "请选择迁移所在地", trigger: "change" },
        ],
        relocationArea: [
          { required: true, message: "请选择迁移所在区县", trigger: "change" },
        ],
        relocationInstitution: [
          { required: true, message: "请选择迁移所在机构", trigger: "change" },
        ],
        relocationComputerRoomId: [
          { required: true, message: "请选择迁移机房", trigger: "change" },
        ],
        relocationCabinetId: [
          { required: true, message: "请选择迁移机柜", trigger: "change" },
        ],
        relocationTime: [
          { required: true, message: "请选择迁移时间", trigger: "change" },
        ],
        destroyTime: [
          { required: true, message: "请选择销毁截至时间", trigger: "change" },
        ],
        maintenanceTime: [
          {
            required: true,
            message: "请选择故障处理截至时间",
            trigger: "change",
          },
        ],
        inspectionTime: [
          {
            required: true,
            message: "请选择机房巡检截至时间",
            trigger: "change",
          },
        ],
      },
      sblxList: [],
      locationList: [],
      computerRoomList: [],
      areaList: [],
      institutionList: [],
      computerList: [],
      cabinetList: [],
      gdlx: "",

      spztList: [
        { id: 1, mc: "正在审批" },
        { id: 2, mc: "审批通过" },
        { id: 3, mc: "审批拒绝" },
      ],
      sbzylxList: [
        { id: 1, mc: "设备" },
        // { id: 4, mc: "机柜" },
        { id: 2, mc: "线缆" },
        { id: 3, mc: "长传链路" },
      ],
      sblxList: [],
      sblxAllList: [],
      aqcpList: [], //列表数据
      formInline: {
        equipmentMainType: 1,
      }, //查询区域数据
      page: 1, //当前页
      pageSize: 10, //每页条数
      total: 0, //总共数据数
      selectlistRow: [], //列表的值
      qydialogVisible: false, //迁移工单打印弹框
      dialogVisibleQygd: false, //详情弹框
      dialogVisible: false, //添加弹窗状态
      form: {
        file: {},
      },
      formqy: {
        id: "",
        relocationLocation: "",
        relocationArea: "",
        relocationInstitution: "",
        relocationComputerRoomId: "",
        relocationCabinetId: "",
        relocationTime: "",
        destroyTime: "",
        maintenanceTime: "",
        inspectionTime: "",
      },
      dialogTitle: "",
      img: "",
      fileName: null,
      sfzg: 0, //是否是机柜
    };
  },
  computed: {},
  mounted() {
    this.aqcp();
    this.sbzylxChange(1);
    this.getAllEquipmentType();
  },
  methods: {
    async printToPDF() {
      let data = await downloadInspectionComputerRoom(this.selectlistRow);
      console.log(data, "data326");
      this.dom_download(data, "机房巡检工单.docx");

      let data2 = await downloadInspection(this.selectlistRow);
      this.dom_download(data2, "机房备份巡检工单.docx");
    },

    // 获取存储的图片给到页面
    getImg() {
      this.img = localStorage.getItem("image");
      localStorage.removeItem("image");
    },
    async printQy(val, formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (val == 1) {
            this.dialogVisibleQygd = true;
            this.dialogTitle = "设备迁移工单";
            let params = this.formqy;
            let data = await migrateEquipment(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = {
                id: this.formqy.id,
                computerRoomCode:
                  data.data.inspectionOrderList.computerRoomCode,
                computerRoomName:
                  data.data.inspectionOrderList.computerRoomName,
                address: data.data.inspectionOrderList.address,
                createByName: data.data.inspectionOrderList.createByName,
                printTime: data.data.inspectionOrderList.printTime,
                operateTime: data.data.inspectionOrderList.relocationTime,
                barcodePath: data.data.barcodePath,
              };
              this.makeCode(data.data.barcodePath); // 生成二维码
              this.$message.success("成功获取迁移工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          } else if (val == 2) {
            this.dialogVisibleQygd = true;
            this.dialogTitle = "设备销毁工单";
            let params = this.formqy;
            let data = await destructionEquipment(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = {
                id: this.formqy.id,
                computerRoomCode:
                  data.data.inspectionOrderList[0].computerRoomCode,
                computerRoomName:
                  data.data.inspectionOrderList[0].computerRoomName,
                address: data.data.inspectionOrderList[0].address,
                createByName: data.data.inspectionOrderList[0].createByName,
                printTime: data.data.inspectionOrderList[0].printTime,
                operateTime: data.data.inspectionOrderList[0].destroyTime,
                barcodePath: data.data.barcodePath,
              };
              this.makeCode(data.data.barcodePath); // 生成二维码
              this.$message.success("成功获取销毁工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          } else if (val == 3) {
            this.dialogVisibleQygd = true;
            this.dialogTitle = "设备故障处理工单";
            let params = this.formqy;
            let data = await faultHandling(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = {
                id: this.formqy.id,
                computerRoomCode:
                  data.data.inspectionOrderList[0].computerRoomCode,
                computerRoomName:
                  data.data.inspectionOrderList[0].computerRoomName,
                address: data.data.inspectionOrderList[0].address,
                createByName: data.data.inspectionOrderList[0].createByName,
                printTime: data.data.inspectionOrderList[0].printTime,
                operateTime: data.data.inspectionOrderList[0].maintenanceTime,
                barcodePath: data.data.barcodePath,
              };
              this.makeCode(data.data.barcodePath); // 生成二维码
              this.$message.success("成功获取故障处理工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    uploadFileXh() {
      this.uploadZipXh();
    },

    async uploadZipXh() {
      let resData = await uploadDestructionEquipment();
      console.log(resData);
      if (resData.code == 10000) {
        this.aqcp();
        this.$message({
          title: "提示",
          message: "上传成功",
          type: "success",
        });
      } else {
        this.$message({
          title: "提示",
          message: resData.message,
          type: "error",
        });
      }
    },
    uploadFileQy() {
      this.uploadZipQy();
    },

    async uploadZipQy() {
      let resData = await uploadMigrateEquipment();
      console.log(resData);
      if (resData.code == 10000) {
        this.aqcp();
        this.$message({
          title: "提示",
          message: "上传成功",
          type: "success",
        });
      } else {
        this.$message({
          title: "提示",
          message: resData.message,
          type: "error",
        });
      }
    },
    uploadFileGz(item) {
      this.uploadZipGz();
    },

    async uploadZipGz() {
      let resData = await uploadFaultHandling();
      console.log(resData);
      if (resData.code == 10000) {
        this.aqcp();
        this.$message({
          title: "提示",
          message: "上传成功",
          type: "success",
        });
      } else {
        this.$message({
          title: "提示",
          message: resData.message,
          type: "error",
        });
      }
    },

    async printQtgd(val, formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (val == 1) {
            this.dialogVisible = true;
            this.dialogTitle = "设备迁移工单";
            let params = this.formqy;
            params.equipmentCode = this.form.equipmentCode;
            let data = await migrateEquipment(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = data.data; // 将迁移工单信息存储到全局变量
              this.makeCode(data.data.scanCode); // 生成二维码
              this.$message.success("成功获取迁移工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          } else if (val == 2) {
            this.dialogVisible = true;
            this.dialogTitle = "设备销毁工单";
            let params = this.formqy;
            params.equipmentCode = this.form.equipmentCode;
            let data = await destructionEquipment(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = data.data; // 将迁移工单信息存储到全局变量
              this.makeCode(data.data.scanCode); // 生成二维码
              this.$message.success("成功获取销毁工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          } else if (val == 3) {
            this.dialogVisible = true;
            this.dialogTitle = "设备故障处理工单";
            let params = this.formqy;
            params.equipmentCode = this.form.equipmentCode;
            let data = await faultHandling(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = data.data; // 将迁移工单信息存储到全局变量
              this.makeCode(data.data.scanCode); // 生成二维码
              this.$message.success("成功获取故障处理工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    async printToPDFQy() {
      if (this.gdlx == 1) {
        let data = await downloadMigrateEquipment(this.xjgddyQbj);
        this.dom_download(data, "设备迁移工单.docx");
        let data2 = await downloadMigrate(this.xjgddyQbj);
        this.dom_download(data2, "设备备份迁移工单.docx");
      } else if (this.gdlx == 2) {
        let data = await downloadDestructionEquipment(this.xjgddyQbj);
        this.dom_download(data, "设备销毁工单.docx");
        let data2 = await downloadDestruction(this.xjgddyQbj);
        this.dom_download(data2, "设备备份销毁工单.docx");
      } else if (this.gdlx == 3) {
        let data = await downloadFaultHandlingEquipment(this.xjgddyQbj);
        this.dom_download(data, "设备故障处理工单.docx");
        let data2 = await downloadFaultHandling(this.xjgddyQbj);
        this.dom_download(data2, "设备备份故障处理工单.docx");
      }
      this.dialogVisible = false;
      this.qydialogVisible = false;
      this.dialogVisibleQygd = false;
    },
    computerRoomChange(val) {
      console.log(val);
      this.formqy.relocationLocation = "";
      this.formqy.relocationArea = "";
      this.formqy.relocationInstitution = "";
      this.formqy.relocationComputerRoomId = "";
      this.formqy.relocationCabinetId = "";
      this.getEquipmentByComputerRoom(val);
    },
    async getEquipmentByComputerRoom(val) {
      let data = await getEquipmentByComputerRoom({ computerRoomld: val });
      console.log(data);
      this.computerRoomList = data.data;
    },
    locationChange(val) {
      console.log(val);
      this.location = val;
      this.formqy.relocationArea = "";
      this.formqy.relocationInstitution = "";
      this.formqy.relocationComputerRoomId = "";
      this.formqy.relocationCabinetId = "";
      this.getArea(val);
    },
    async getArea(val) {
      let data = await getArea({ citycode: val });
      console.log(data);
      this.areaList = data.data;
    },

    areaChange(val) {
      console.log(val);
      this.area = val;
      this.formqy.relocationInstitution = "";
      this.formqy.relocationComputerRoomId = "";
      this.formqy.relocationCabinetId = "";
      this.getDwxx(val);
    },
    async getDwxx(val) {
      let data = await getDwxx({ area: val });
      console.log(data);
      this.institutionList = data.data;
    },
    institutionChange(val) {
      console.log(val);
      this.institution = val;
      this.formqy.relocationComputerRoomId = "";
      this.formqy.relocationCabinetId = "";
      this.getComputerList(val);
    },
    async getComputerList(val) {
      let data = await getComputerRoomList({
        institution: val,
      });
      console.log(data);
      this.computerList = data.data;
    },
    computerChange(val) {
      console.log(val);
      this.jgId = val;
      this.formqy.relocationCabinetId = "";
      this.getCabinetList1(val);
    },
    async getCabinetList1(val) {
      let data = await getCabinetList({
        computerRoomId: val,
      });
      console.log(data);
      this.cabinetList = data.data;
    },
    //生成二维码方法
    makeCode(item) {
      const result = new AraleQRCode({
        render: "svg", // 定义生成的类型 'svg' or 'table dom’
        text: item, // 二维码的链接
        size: 150, //二维码大小
      });

      // 将svg xml文档转换成字符串
      const svgXml = new XMLSerializer().serializeToString(result);

      // 将svg字符串转成base64格式，通过 window.btoa方法创建一个 base-64 编码的字符串，进行二次编码解码(encodeURIComponent 字符串进行编码和解码，unescape 进行解码)。
      const src =
        "data:image/svg+xml;base64," +
        window.btoa(unescape(encodeURIComponent(svgXml)));

      // 本地存储图片
      localStorage.setItem("image", src);
      this.getImg();
    },
    async print() {
      if (this.selectlistRow.length == 0) {
        this.$message({
          message: "请选择需要打印工单的资产",
          type: "warning",
        });
      } else {
        for (let i = 0; i < this.selectlistRow.length; i++) {
          this.selectlistRow[i].inspectionTime = this.formqy.inspectionTime;
        }
        let data = await downloadInspectionForm(this.selectlistRow);
        console.log(data.data, "data326");
        if (data.code == 10000) {
          this.makeCode(data.data.barcodePath);
          const computerRoomCode = [];
          const computerRoomName = [];
          const serverNumber = [];
          const switchNumber = [];
          const address = [];
          const createByName = [];
          const printTime = [];
          const operateTime = [];
          data.data.inspectionOrderList.forEach((item) => {
            computerRoomCode.push(item.computerRoomCode);
            computerRoomName.push(item.computerRoomName);
            serverNumber.push(item.serverNumber);
            switchNumber.push(item.switchNumber);
            address.push(item.address);
            createByName.push(item.createByName);
            printTime.push(item.printTime);
            operateTime.push(item.inspectionTime);
          });
          this.xjgddyQbj = {
            computerRoomCode: computerRoomCode.join(","),
            computerRoomName: computerRoomName.join(","),
            serverNumber: serverNumber.join(","),
            switchNumber: switchNumber.join(","),
            address: address.join(","),
            createByName: createByName[0],
            printTime: printTime[0],
            operateTime: operateTime[0],
          }; // 将拼接后的字符串作为一个对象存储
          console.log(this.xjgddyQbj); // 打印拼接后的对象
          this.dialogVisible = true;
        }
      }
    },
    qygddy(val) {
      console.log(val);

      if (this.selectlistRow.length == 0) {
        this.$message({
          message: "请选择需要打印工单的资产",
          type: "warning",
        });
        return;
      }
      if (val == 1) {
        this.dialogTitle = "设备迁移工单";
      } else if (val == 2) {
        this.dialogTitle = "设备销毁工单";
      } else if (val == 3) {
        this.dialogTitle = "设备故障处理工单";
      } else if (val == 4) {
        this.dialogTitle = "机房巡检工单";
      }
      this.gdlx = val;
      // this.getEquipmentByComputerRoom();
      this.getCity();
      this.qydialogVisible = true;
      this.formqy.id = this.selectlistRow[0].id;
    },
    async getCity() {
      let data = await getCity();
      console.log(data);
      this.locationList = data.data;
    },
    uploadFile() {
      this.uploadZip();
    },

    async uploadZip() {
      let resData = await uploadInspectionForm();
      console.log(resData);
      if (resData.code == 10000) {
        this.aqcp();
        this.$message({
          title: "提示",
          message: "上传成功",
          type: "success",
        });
      } else {
        this.$message({
          title: "提示",
          message: resData.message,
          type: "error",
        });
      }
    },
    async getAllEquipmentType() {
      let data = await getEquipmentType();
      this.sblxAllList = data.data;
    },
    async getEquipmentType() {
      let data = await getEquipmentType({
        equipmentMainType: this.formInline.equipmentMainType,
      });
      this.sblxList = data.data;
    },
    async sbzylxChange(val) {
      this.formInline.equipmentType = null;
      let data = await getEquipmentType({
        equipmentMainType: val,
      });
      this.sblxList = data.data;
    },
    //获取列表的值
    async aqcpjg() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        computerRoomName: this.formInline.computerRoomName,
        cabinetName: this.formInline.cabinetName,
        computerRoomCode: this.formInline.computerRoomCode,
        cabinetCode: this.formInline.cabinetCode,
      };
      let resList = await queryCabinetPage(params);
      console.log("resList", resList);
      this.aqcpList = resList.data.records;
      this.total = resList.data.total;
    },
    //新增数据按钮事件
    xzaqcp() {
      this.$router.push({
        path: "/qyczglXqy",
        query: {
          type: "1",
        },
      });
    },
    uploadFile(item) {
      this.form.file = item.file;
      this.fileName = item.file.name;
      this.uploadZip();
    },

    async uploadZip() {
      let fd = new FormData();
      fd.append("file", this.form.file);
      let resData = await uploadDestructionEquipment(fd);
      console.log(resData);
      if (resData.code == 10000) {
        this.aqcp();
        this.$message({
          title: "提示",
          message: "上传成功",
          type: "success",
        });
      }
    },

    uploadFileGz(item) {
      this.form.file = item.file;
      this.fileName = item.file.name;
      this.uploadZipGz();
    },

    async uploadZipGz() {
      let fd = new FormData();
      fd.append("file", this.form.file);
      let resData = await uploadFaultHandling(fd);
      console.log(resData);
      if (resData.code == 10000) {
        this.aqcp();
        this.$message({
          title: "提示",
          message: "上传成功",
          type: "success",
        });
      }
    },
    //详情弹框
    xqyl(row) {
      this.$router.push({
        path: "/qyczglXqy",
        query: {
          id: row.id,
          routeType: "3",
          equipmentCode: row.equipmentCode,
        },
      });
    },
    //修改弹框
    updateItem(row) {
      if (this.sfzg == 1) {
        this.$router.push({
          path: "/jgglXqxg",
          query: {
            id: row.id,
            routeType: "2",
          },
        });
      } else {
        this.$router.push({
          path: "/qyczglXqy",
          query: {
            id: row.id,
            type: "2",
          },
        });
      }
    },
    //查询
    onSubmit() {
      this.page = 1;
      if (this.formInline.equipmentMainType == 4) {
        this.sfzg = 1;
        this.aqcpjg();
      } else {
        this.sfzg = 0;
        this.aqcp();
      }
      // this.aqcp();
    },

    //获取列表的值
    async aqcp() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        computerRoomCode: this.formInline.computerRoomCode,
        computerRoomName: this.formInline.computerRoomName,
        cabinetCode: this.formInline.cabinetCode,
        cabinetName: this.formInline.cabinetName,
        equipmentCode: this.formInline.equipmentCode,
        equipmentName: this.formInline.equipmentName,
        equipmentMainType: this.formInline.equipmentMainType,
        equipmentType: this.formInline.equipmentType,
        useStatus: this.formInline.useStatus,
      };
      let resList = await sbQueryByCondition(params);
      console.log("resList", resList);
      this.aqcpList = resList.data.records;
      for (let i = 0; i < this.aqcpList.length; i++) {
        let hxsj = "";
        this.sblxAllList.forEach((item) => {
          if (
            this.aqcpList[i].equipmentMainType == item.equipment_main_type &&
            this.aqcpList[i].equipmentType == item.csz
          ) {
            hxsj = item.mc;
          }
        });
        this.aqcpList[i].equipmentType = hxsj;
      }
      this.total = resList.data.total;
    },
    //删除
    shanchu(id) {
      let that = this;
      if (this.selectlistRow != "") {
        this.$confirm("是否继续删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            if (this.sfzg == 1) {
              deleteCabinet(this.selectlistRow).then(() => {
                that.aqcpjg();
              });
            } else {
              deleteEquipment(this.selectlistRow).then(() => {
                that.aqcp();
              });
            }
            this.$message({
              message: "删除成功",
              type: "success",
            });
          })
          .catch(() => {
            this.$message("已取消删除");
          });
      } else {
        this.$message({
          message: "未选择删除记录，请选择下列列表",
          type: "warning",
        });
      }
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },

    //选中列表的数据
    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      this.aqcp();
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.aqcp();
    },
    cz() {
      this.formInline = {
        equipmentMainType: 1,
      };
    },
    forlx(row) {
      let hxsj;
      this.syztList.forEach((item) => {
        if (row.useStatus == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },
    forsbzylx(row) {
      let hxsj;
      this.sbzylxList.forEach((item) => {
        if (row.equipmentMainType == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },
    wdClick(row) {
      this.cabinetId = row.id;
      // 获取当前年月格式为yyyy-MM
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      if (month < 10) {
        month = "0" + month;
      }
      this.tMonth = year + "-" + month;
      this.wdGet();
      this.sdGet();
    },
    async wdGet() {
      console.log("wdClick triggered");
      let params = {
        cabinetId: this.cabinetId,
        date: this.tMonth,
      };
      let data = await getCabinetTemerature(params);
      if (data.code == 10000) {
        this.categoriesTlist = data.data.categories;
        this.seriesTlist = data.data.series;
        this.wddialogVisible = true;
        this.$nextTick(() => {
          this.initCharts();
        });
      }
    },
    initCharts() {
      var chartDom = document.getElementById("main");
      var myChart = echarts.init(chartDom);
      var option;
      let seriesData = {
        categories: this.categoriesTlist,
        series: this.seriesTlist,
      };
      const colorList = ["#1678FF", "#00D052", "#F6ED0D", "#FD2BFF", "#9E87FF"];
      let series = [];
      seriesData.series.forEach((item, index) => {
        console.log(seriesData.series.length - 1, index, "0909");
        series.push({
          name: item.name,
          type: "line",
          data: item.data,
          symbolSize: 7,
          symbol: "circle",
          smooth: true,
          // yAxisIndex: seriesData.series.length - 1 == index ? 1 : 0,
          showSymbol: true,
          lineStyle: {
            width: 2,
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              {
                offset: 0,
                color: colorList[index],
              },
              {
                offset: 1,
                color: colorList[index],
              },
            ]),
          },
          itemStyle: {
            color: colorList[index], // 设置圆形的填充色
            borderColor: "#ffffff", // 设置边框颜色
            borderWidth: 1, // 设置边框宽度
          },
          markLine: {
            data: [
              {
                name: "规定最高温度28℃",
                yAxis: 28,
                lineStyle: {
                  color: "red",
                  type: "solid",
                },
              },
              {
                name: "规定最低温度10℃",
                yAxis: 10,
                lineStyle: {
                  color: "blue",
                  type: "solid",
                },
              },
            ],
            symbol: "none", // 不显示符号
            label: {
              show: true,
              position: "end",
              formatter: "{b}",
            },
          },
        });
      });
      option = {
        backgroundColor: "#2e5495",
        //你的代码
        legend: {
          icon: "circle",
          top: "5%",
          right: "5%",
          itemWidth: 6,
          itemGap: 20,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          top: "25%",
          bottom: "15%",
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var str = `${params[0].name}<br/>`;
            params.forEach((par) => {
              str += `${par.marker} ${par.seriesName}：${
                par.value
              } ${(par.seriesName = "℃")}<br/>`;
            });
            return str;
          },
        },
        xAxis: [
          {
            type: "category",
            data: seriesData.categories ? seriesData.categories : [],
            axisLine: {
              lineStyle: {
                color: "#3DECFF",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#fff",
              },
              // 默认x轴字体大小
              fontSize: 12,
              // margin:文字到x轴的距离
              margin: 15,
            },
            axisPointer: {
              label: {
                // padding: [11, 5, 7],
                padding: [0, 0, 10, 0],
                // 这里的margin和axisLabel的margin要一致!
                margin: 15,
                // 移入时的字体大小
                fontSize: 12,
                backgroundColor: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#fff", // 0% 处的颜色
                    },
                    {
                      // offset: 0.9,
                      offset: 0.86,
                      color: "#fff", // 0% 处的颜色
                    },
                    {
                      offset: 0.86,
                      color: "#33c0cd", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#33c0cd", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            boundaryGap: false,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "单位：℃",
            nameTextStyle: {
              color: "#fff",
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "rgba(255,255,255,0.5)",
              },
            },
          },
          {
            splitLine: {
              show: false,
            },
          },
        ],
        series: [...series],
      };

      option && myChart.setOption(option);
    },
    sdClick(row) {
      this.cabinetId = row.id;
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      if (month < 10) {
        month = "0" + month;
      }
      this.hMonth = year + "-" + month;
      this.wdGet();
      this.sdGet();
    },
    async sdGet() {
      let params = {
        cabinetId: this.cabinetId,
        date: this.tMonth,
      };
      let data = await getCabinetHumidity(params);
      if (data.code == 10000) {
        this.categoriesHlist = data.data.categories;
        this.seriesHlist = data.data.series;
        this.wddialogVisible = true;
        this.$nextTick(() => {
          this.initCharts1();
        });
      }
    },
    initCharts1() {
      var chartDom = document.getElementById("main1");
      var myChart = echarts.init(chartDom);
      var option;
      let seriesData = {
        categories: this.categoriesHlist,
        series: this.seriesHlist,
      };
      const colorList = ["#1678FF", "#00D052", "#F6ED0D", "#FD2BFF", "#9E87FF"];
      let series = [];
      seriesData.series.forEach((item, index) => {
        console.log(seriesData.series.length - 1, index, "0909");
        series.push({
          name: item.name,
          type: "line",
          data: item.data,
          symbolSize: 7,
          symbol: "circle",
          smooth: true,
          // yAxisIndex: seriesData.series.length - 1 == index ? 1 : 0,
          showSymbol: true,
          lineStyle: {
            width: 2,
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              {
                offset: 0,
                color: colorList[index],
              },
              {
                offset: 1,
                color: colorList[index],
              },
            ]),
          },
          itemStyle: {
            color: colorList[index], // 设置圆形的填充色
            borderColor: "#ffffff", // 设置边框颜色
            borderWidth: 1, // 设置边框宽度
          },
          markLine: {
            data: [
              {
                name: "规定最高湿度70%",
                yAxis: 70,
                lineStyle: {
                  color: "red",
                  type: "solid",
                },
              },
              {
                name: "规定最低湿度30%",
                yAxis: 30,
                lineStyle: {
                  color: "blue",
                  type: "solid",
                },
              },
            ],
            symbol: "none", // 不显示符号
            label: {
              show: true,
              position: "end",
              formatter: "{b}",
            },
          },
        });
      });
      option = {
        backgroundColor: "#2e5495",
        //你的代码
        legend: {
          icon: "circle",
          top: "5%",
          right: "5%",
          itemWidth: 6,
          itemGap: 20,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          top: "25%",
          bottom: "15%",
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var str = `${params[0].name}<br/>`;
            params.forEach((par) => {
              str += `${par.marker} ${par.seriesName}：${
                par.value
              } ${(par.seriesName = "℃")}<br/>`;
            });
            return str;
          },
        },
        xAxis: [
          {
            type: "category",
            data: seriesData.categories ? seriesData.categories : [],
            axisLine: {
              lineStyle: {
                color: "#3DECFF",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#fff",
              },
              // 默认x轴字体大小
              fontSize: 12,
              // margin:文字到x轴的距离
              margin: 15,
            },
            axisPointer: {
              label: {
                // padding: [11, 5, 7],
                padding: [0, 0, 10, 0],
                // 这里的margin和axisLabel的margin要一致!
                margin: 15,
                // 移入时的字体大小
                fontSize: 12,
                backgroundColor: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#fff", // 0% 处的颜色
                    },
                    {
                      // offset: 0.9,
                      offset: 0.86,
                      color: "#fff", // 0% 处的颜色
                    },
                    {
                      offset: 0.86,
                      color: "#33c0cd", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#33c0cd", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            boundaryGap: false,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "单位：%",
            nameTextStyle: {
              color: "#fff",
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "rgba(255,255,255,0.5)",
              },
            },
          },
          {
            splitLine: {
              show: false,
            },
          },
        ],
        series: [...series],
      };

      option && myChart.setOption(option);
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    tMonthClick() {
      // console.log("tMonthClick triggered");
      this.wdGet();
      this.sdGet();
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.cd {
  width: 191px;
}

/deep/.el-form--inline .el-form-item {
  margin-right: 9px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

.formDialogItem {
  width: 115px;
  height: 24px;
  margin-right: 10px;
}
.formDialogCon {
  width: 320px;
  height: 24px;
}
.formDialogCon1 {
  width: 168px;
  height: 24px;
}
.dialog-footer {
  display: block;
  margin-top: 10px;
}
/deep/.el-dialog__wrapper .el-dialog {
  background-image: linear-gradient(180deg, #f0f7fe 0%, #ffffff 32%);
  border: 1px solid rgba(151, 151, 151, 1);
  border-radius: 4px;
  padding-left: 6px;
  padding-right: 6px;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__header {
  padding: 15px 14.5px 13.5px 14px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  display: flex;
  align-items: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__body {
  padding: 0;
}
.formDialog {
  padding: 23.5px 45px 25px 45px;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #000116;
  font-weight: 400;
  position: relative;
}
.formDialog1 {
  padding: 38px 45px 49px 45px;
  margin-bottom: 10px;
}
.flexAlign {
  display: flex;
  align-items: center;
}
.formDialogItem {
  width: 100px;
  height: 24px;
  margin-right: 10px;
}
.formDialogCon {
  width: 320px;
  height: 24px;
}
.formDialogCon1 {
  width: 168px;
  height: 24px;
}
.fjx {
  width: 510px;
  height: 1px;
  background-color: rgba(229, 229, 229, 1);
  margin: 0 auto;
}
.ewm {
  position: absolute;
  width: 145px;
  height: 145px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 18px;
  right: 31.54px;
  border: 4px solid #d8e4fa;
  border-radius: 10px;
}
.btnRight {
  height: 40px;
  position: relative;
}
.btnRightItem {
  position: absolute;
  right: 31.54px;
}

@media print {
  .el-dialog {
    width: 100%;
    box-shadow: none;
    position: static; /* 确保内容在打印时可见 */
  }
  .el-dialog__header {
    padding: 10px;
  }
  .el-dialog__title {
    font-size: 16px;
    font-weight: bold;
  }
  .el-dialog__body {
    padding: 10px;
  }
  .el-dialog__footer {
    padding: 10px;
  }
  .el-button {
    display: none !important; /* 隐藏打印按钮 */
  }
  .formDialog,
  .formDialog1 {
    position: static; /* 确保内容在打印时可见 */
  }
  .ewm {
    position: static; /* 确保内容在打印时可见 */
    width: 130px;
    height: 130px;
    margin-top: 10px; /* 调整间距 */
  }
  .btnRight,
  .btnRightItem {
    display: none !important; /* 确保打印按钮不显示 */
  }
}
.top_title {
  margin-bottom: 20px;
  float: left;
  position: relative;
  z-index: 99999;
  margin-left: 85px;
  margin-top: 25px;
}

/deep/.el-dialog__body {
  padding: 0px !important;
}
</style>
