webpackJsonp([234],{RVkg:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=s("mvHQ"),a=s.n(i),l=s("Xxa5"),n=s.n(l),o=s("exGp"),r=s.n(o),c=s("0hE6"),d=(s("1clA"),s("urfq")),g=s("DQfH"),m={data:function(){var t=this;return{pageInfo:{page:1,pageSize:10,total:0},dialogVisibleSettingModify:!1,dialogVisibleSetting:!1,settingForm:{},settingFormOld:{},cszlx:1,settingList:[],pickerOptions:{disabledDate:function(e){if(null==t.selectDate)return!1},onPick:function(e){e.minDate&&!e.maxDate?t.selectDate=e.minDate:t.selectDate=null}},jldwList:[]}},components:{hsoft_top_title:c.a},mounted:function(){this.getjldw(),this.getSettingList();var t=(new Date).getFullYear()+"";console.log(t)},methods:{getjldw:function(){var t=this;return r()(n.a.mark(function e(){return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.d)();case 2:t.jldwList=e.sent;case 3:case"end":return e.stop()}},e,t)}))()},showAddDialog:function(){this.settingForm={cszlx:1},console.log(this.settingForm),this.dialogVisibleSetting=!0},formatTime:function(t){return Object(d.b)(new Date(t))},handleCurrentChange:function(t){this.pageInfo.page=t,this.getSettingList()},handleSizeChange:function(t){this.pageInfo.pageSize=t,this.getSettingList()},modifySetting:function(t){console.log(t),this.settingFormOld=JSON.parse(a()(t)),console.log("this.settingFormOld",this.settingFormOld),this.settingForm=JSON.parse(a()(t)),console.log("this.settingForm",this.settingForm),2==this.settingForm.cszlx&&(console.log(111111111111),this.settingForm.cszDate3=t.cszDate),3==this.settingForm.cszlx&&(this.$set(this.settingForm,"cszDate",[t.cszDate,t.cszDate2]),console.log(this.$set(this.settingForm,"cszDate",[t.cszDate,t.cszDate2]))),this.dialogVisibleSettingModify=!0},modifySettingDialog:function(){var t=this;return r()(n.a.mark(function e(){var s,i,l;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return 1==(s=JSON.parse(a()(t.settingForm))).cszlx&&(s.cszDate=void 0,s.cszDate2=void 0),2==s.cszlx&&(i=(new Date).getFullYear(),s.cszDate=i+"年"+t.settingForm.cszDate3,s.cszDate2=void 0),3==s.cszlx&&(l=(new Date).getFullYear(),s.cszDate=l+"年"+t.settingForm.cszDate[0],s.cszDate2=l+"年"+t.settingForm.cszDate[1],console.log(s),console.log(" this.settingForm.cszDate",t.settingForm.cszDate)),e.next=6,Object(g.e)(s);case 6:(e.sent.code=1e4)&&t.getSettingList(),t.dialogVisibleSettingModify=!1;case 9:case"end":return e.stop()}},e,t)}))()},deleteSetting:function(t){var e=this;return r()(n.a.mark(function s(){var i;return n.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return i={settingid:t.settingid},s.next=3,Object(g.b)(i);case 3:(s.sent.code=1e4)&&e.getSettingList();case 5:case"end":return s.stop()}},s,e)}))()},getSettingList:function(){var t=this;return r()(n.a.mark(function e(){var s,i;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s={page:t.pageInfo.page,pageSize:t.pageInfo.pageSize},e.next=3,Object(g.c)(s);case 3:i=e.sent,t.settingList=i.records,t.settingList.forEach(function(t){1!=t.cszlx&&(t.cszDate=t.cszDate.slice(5,11),t.cszDate2=t.cszDate2.slice(5,11))}),t.pageInfo.total=i.total;case 7:case"end":return e.stop()}},e,t)}))()},addSetting:function(){var t=this;return r()(n.a.mark(function e(){var s;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=JSON.parse(a()(t.settingForm)),console.log("表单数据",s),1!=s.cszlx&&(s.cszdw=8,2==s.cszlx&&(s.cszDate=t.settingForm.cszDate3)),3==s.cszlx&&(s.cszDate=t.settingForm.cszDate[0],s.cszDate2=t.settingForm.cszDate[1]),console.log(s),e.next=7,Object(g.a)(s);case 7:(e.sent.code=1e4)&&t.getSettingList(),t.dialogVisibleSetting=!1;case 10:case"end":return e.stop()}},e,t)}))()},fordw:function(t){var e=void 0;return this.jldwList.forEach(function(s){t.cszdw==s.id&&(e=s.mc)}),e}}},p={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticStyle:{height:"100%"}},[s("hsoft_top_title",{scopedSlots:t._u([{key:"left",fn:function(){return[t._v("参数设置")]},proxy:!0}])}),t._v(" "),s("div",{staticStyle:{padding:"10px 0","text-align":"right"}},[s("el-button",{attrs:{type:"success"},on:{click:t.showAddDialog}},[t._v("添加")])],1),t._v(" "),s("el-table",{staticClass:"table",staticStyle:{width:"100%",border:"1px solid #EBEEF5"},attrs:{data:t.settingList,border:"","header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},height:"calc(100% - 32px - 60px - 32px - 10px - 10px)",stripe:""}},[s("el-table-column",{attrs:{type:"index",width:"60",label:"序号",align:"center"}}),t._v(" "),s("el-table-column",{attrs:{prop:"csbs",label:"参数标识",width:"120"}}),t._v(" "),s("el-table-column",{attrs:{prop:"cssm",label:"参数说明"}}),t._v(" "),s("el-table-column",{attrs:{prop:"",label:"参数值",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",[1==e.row.cszlx?s("span",[t._v(t._s(e.row.cszNum))]):t._e(),t._v(" "),2==e.row.cszlx?s("span",[t._v(t._s(e.row.cszDate))]):t._e(),t._v(" "),3==e.row.cszlx?s("span",[t._v("\n            "+t._s(e.row.cszDate)+"\n            -\n            "),t._v("\n            "+t._s(e.row.cszDate2)+"\n          ")]):t._e()])]}}])}),t._v(" "),s("el-table-column",{attrs:{prop:"cszdw",label:"单位",width:"80",align:"center",formatter:t.fordw}}),t._v(" "),s("el-table-column",{attrs:{prop:"csbz",label:"备注",width:""}}),t._v(" "),s("el-table-column",{attrs:{prop:"",label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-button",{attrs:{size:"small",type:"text"},on:{click:function(s){return t.modifySetting(e.row)}}},[t._v("修改")]),t._v(" "),s("el-button",{staticStyle:{color:"#F56C6C"},attrs:{size:"small",type:"text"},on:{click:function(s){return t.deleteSetting(e.row)}}},[t._v("删除")])]}}])})],1),t._v(" "),s("el-pagination",{staticStyle:{"padding-top":"10px"},attrs:{background:"","pager-count":5,"current-page":t.pageInfo.page,"page-sizes":[5,10,20,30],"page-size":t.pageInfo.pageSize,layout:"total, prev, pager, sizes,next, jumper",total:t.pageInfo.total},on:{"current-change":t.handleCurrentChange,"size-change":t.handleSizeChange}}),t._v(" "),s("el-dialog",{attrs:{title:"添加系统参数",visible:t.dialogVisibleSetting,width:"35%"},on:{"update:visible":function(e){t.dialogVisibleSetting=e}}},[s("div",[s("el-form",{attrs:{model:t.settingForm,"label-position":"right","label-width":"120px",size:"mini"}},[s("div",{staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"参数标识"}},[s("el-input",{model:{value:t.settingForm.csbs,callback:function(e){t.$set(t.settingForm,"csbs",e)},expression:"settingForm.csbs"}})],1)],1),t._v(" "),s("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"参数说明"}},[s("el-input",{attrs:{type:"textarea"},model:{value:t.settingForm.cssm,callback:function(e){t.$set(t.settingForm,"cssm",e)},expression:"settingForm.cssm"}})],1),t._v(" "),s("div",{staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"参数值类型"}},[s("el-select",{staticStyle:{width:"100%"},model:{value:t.settingForm.cszlx,callback:function(e){t.$set(t.settingForm,"cszlx",e)},expression:"settingForm.cszlx"}},[s("el-option",{attrs:{label:"数字类型",value:1}}),t._v(" "),s("el-option",{attrs:{label:"日期",value:2}}),t._v(" "),s("el-option",{attrs:{label:"日期范围（范围）",value:3}})],1)],1)],1),t._v(" "),s("div",{staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"参数值"}},[1==t.settingForm.cszlx?s("el-input",{attrs:{type:"number",onKeypress:"return(/[\\d]/.test(String.fromCharCode(event.keyCode)))"},model:{value:t.settingForm.cszNum,callback:function(e){t.$set(t.settingForm,"cszNum",e)},expression:"settingForm.cszNum"}}):t._e(),t._v(" "),2==t.settingForm.cszlx?s("el-date-picker",{staticStyle:{width:"calc(100%)"},attrs:{type:"datetime",format:"MM月dd日","value-format":"yyyy年MM月dd日",placeholder:"选择日期时间"},model:{value:t.settingForm.cszDate3,callback:function(e){t.$set(t.settingForm,"cszDate3",e)},expression:"settingForm.cszDate3"}}):t._e(),t._v(" "),3==t.settingForm.cszlx?s("div",[s("el-date-picker",{staticStyle:{width:"calc(100% - 20px)"},attrs:{type:"daterange",format:"yyyy年MM月dd日","value-format":"yyyy年MM月dd日","picker-options":t.pickerOptions,"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.settingForm.cszDate,callback:function(e){t.$set(t.settingForm,"cszDate",e)},expression:"settingForm.cszDate"}}),t._v(" "),s("el-popover",{attrs:{placement:"bottom",width:"100",trigger:"hover"}},[s("div",[s("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[s("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),s("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),s("div",[t._v("\n                    从开始日期0点开始，到结束日期0点结束\n                  ")])]),t._v(" "),s("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"})])],1):t._e()],1)],1),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:1==t.settingForm.cszlx,expression:"settingForm.cszlx == 1"}],staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"参数值计量单位"}},[s("el-select",{staticClass:"widthx",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择类型"},model:{value:t.settingForm.cszdw,callback:function(e){t.$set(t.settingForm,"cszdw",e)},expression:"settingForm.cszdw"}},t._l(t.jldwList,function(t){return s("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),s("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"备注"}},[s("el-input",{attrs:{type:"textarea"},model:{value:t.settingForm.csbz,callback:function(e){t.$set(t.settingForm,"csbz",e)},expression:"settingForm.csbz"}})],1),t._v(" "),s("div",{staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"分组号"}},[s("el-input",{model:{value:t.settingForm.fzh,callback:function(e){t.$set(t.settingForm,"fzh",e)},expression:"settingForm.fzh"}})],1)],1)],1)],1),t._v(" "),s("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addSetting()}}},[t._v("保 存")]),t._v(" "),s("el-button",{attrs:{type:"warning"},on:{click:function(e){t.dialogVisibleSetting=!1}}},[t._v("关 闭")])],1)]),t._v(" "),s("el-dialog",{attrs:{title:"修改系统参数",visible:t.dialogVisibleSettingModify,width:"35%"},on:{"update:visible":function(e){t.dialogVisibleSettingModify=e}}},[s("el-form",{attrs:{"label-position":"right","label-width":"120px",size:"mini"}},[s("div",{staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"参数标识"}},[s("el-input",{attrs:{disabled:""},model:{value:t.settingForm.csbs,callback:function(e){t.$set(t.settingForm,"csbs",e)},expression:"settingForm.csbs"}})],1)],1),t._v(" "),s("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"参数说明"}},[s("el-input",{attrs:{type:"textarea",disabled:""},model:{value:t.settingForm.cssm,callback:function(e){t.$set(t.settingForm,"cssm",e)},expression:"settingForm.cssm"}})],1),t._v(" "),s("div",{staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"参数值类型"}},[s("el-select",{staticStyle:{width:"100%"},model:{value:t.settingForm.cszlx,callback:function(e){t.$set(t.settingForm,"cszlx",e)},expression:"settingForm.cszlx"}},[s("el-option",{attrs:{label:"数字类型",value:1}}),t._v(" "),s("el-option",{attrs:{label:"日期",value:2}}),t._v(" "),s("el-option",{attrs:{label:"日期范围（范围）",value:3}})],1)],1)],1),t._v(" "),s("div",{staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"参数值"}},[1==t.settingForm.cszlx?s("el-input",{attrs:{type:"number",onKeypress:"return(/[\\d]/.test(String.fromCharCode(event.keyCode)))"},model:{value:t.settingForm.cszNum,callback:function(e){t.$set(t.settingForm,"cszNum",e)},expression:"settingForm.cszNum"}}):t._e(),t._v(" "),2==t.settingForm.cszlx?s("el-date-picker",{staticStyle:{width:"calc(100% - 20px)"},attrs:{type:"datetime",format:"MM月dd日","value-format":"MM月dd日",placeholder:"选择日期时间"},model:{value:t.settingForm.cszDate3,callback:function(e){t.$set(t.settingForm,"cszDate3",e)},expression:"settingForm.cszDate3"}}):t._e(),t._v(" "),3==t.settingForm.cszlx?s("div",[s("el-date-picker",{staticStyle:{width:"calc(100% - 20px)"},attrs:{type:"daterange",format:"MM月dd日","value-format":"MM月dd日","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.settingForm.cszDate,callback:function(e){t.$set(t.settingForm,"cszDate",e)},expression:"settingForm.cszDate"}}),t._v(" "),s("el-popover",{attrs:{placement:"bottom",width:"100",trigger:"hover"}},[s("div",[s("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[s("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),s("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),s("div",[t._v("\n                  从开始日期0点开始，到结束日期0点结束\n                ")])]),t._v(" "),s("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"})])],1):t._e()],1)],1),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:1==t.settingForm.cszlx,expression:"settingForm.cszlx == 1"}],staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"参数值计量单位"}},[s("el-select",{staticClass:"widthx",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择类型",disabled:""},model:{value:t.settingForm.cszdw,callback:function(e){t.$set(t.settingForm,"cszdw",e)},expression:"settingForm.cszdw"}},t._l(t.jldwList,function(t){return s("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),s("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"备注"}},[s("el-input",{attrs:{type:"textarea",disabled:""},model:{value:t.settingForm.csbz,callback:function(e){t.$set(t.settingForm,"csbz",e)},expression:"settingForm.csbz"}})],1),t._v(" "),s("div",{staticStyle:{display:"flex"}},[s("el-form-item",{staticClass:"one-line",attrs:{label:"分组号"}},[s("el-input",{model:{value:t.settingForm.fzh,callback:function(e){t.$set(t.settingForm,"fzh",e)},expression:"settingForm.fzh"}})],1)],1)],1),t._v(" "),s("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.modifySettingDialog()}}},[t._v("确 定")]),t._v(" "),s("el-button",{attrs:{type:"warning"},on:{click:function(e){t.dialogVisibleSettingModify=!1}}},[t._v("取 消")])],1)],1)],1)},staticRenderFns:[]};var u=s("VU/8")(m,p,!1,function(t){s("stN7")},"data-v-1c2dc2dd",null);e.default=u.exports},stN7:function(t,e){}});
//# sourceMappingURL=234.135523344440e5ba3571.js.map