{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/zgfcTable.vue", "webpack:///./src/renderer/view/rcgz/zgfcTable.vue?50d2", "webpack:///./src/renderer/view/rcgz/zgfcTable.vue"], "names": ["zgfcTable", "components", "AddLineTable", "BaseTable", "baseTable", "props", "data", "table<PERSON><PERSON>", "loading", "ry<PERSON><PERSON>ose", "bm", "xm", "regionOption", "page", "pageSize", "radioIdSelect", "ryDatas", "total", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "headerCellStyle", "background", "color", "tjlist", "sm<PERSON><PERSON>", "xb", "gj", "dwzwzc", "yrsmgw", "cym", "mz", "hyzk", "zzmm", "lxdh", "sfzhm", "hjdz", "hjdgajg", "czdz", "czgajg", "imageUrl", "yjqk", "qscfqk", "qtqk", "brcn", "ryglRyscScjlList", "qssj", "zzsj", "szdw", "zw", "zmr", "czbtn1", "czbtn2", "ryglRyscJtcyList", "gxms", "jwjlqk", "cgszd", "ryglRyscYccgList", "cggj", "sy", "ryglRyscJwzzqkList", "jgmc", "zznr", "ryglRyscCfjlList", "cfdw", "cfsj", "cfjg", "cfyy", "ryglRyscSwzjList", "zjmc", "fjlb", "cyqk", "zjhm", "yxq", "ryInfo", "zzmmoptions", "ynoptions", "sltshow", "routeType", "pdfBase64", "fileList", "dialogImageUrl", "dialogVisible", "approvalDialogVisible", "fileRow", "applyColumns", "name", "prop", "scopeType", "formatter", "handleColumnApply", "computed", "mounted", "this", "onfwid", "getOrganization", "yhDatas", "$route", "query", "datas", "zgfs", "type", "routezt", "zt", "console", "log", "item", "result", "iamgeBase64", "iamgeBase64Brcn", "extends_default", "zp", "_validDataUrl", "s", "regex", "test", "ryglZgfsScjlList", "length", "map", "ryglZgfsJtcyList", "ryglZgfsYccgList", "ryglZgfsJwzzqkList", "ryglZgfsCfjlList", "ryglZgfsSwzjList", "toString", "_validDataUrl2", "_that", "_previwImg", "methods", "blobToBase64", "blob", "callback", "fileReader", "FileReader", "onload", "e", "target", "readAsDataURL", "handleSelectionChange", "index", "row", "addRow", "push", "delRow", "rows", "splice", "cyjshgxAddRow", "ybrgx", "sfywjjwjlqcqjlxk", "dw", "cyjshgxDelRow", "yscgqkAddRow", "qsrq", "zzrq", "jsnsdgjhdq", "yscgqkDelRow", "jsjwzzqkAddRow", "gjdq", "jsjwzzqkDelRow", "clhwffzqkAddRow", "_data$push", "cljg", "clyy", "defineProperty_default", "clhwffzqkDelRow", "httpRequest", "_this", "URL", "createObjectURL", "file", "dataurl", "split", "yulan", "shanchu", "_this2", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "params", "wrap", "_context", "prev", "next", "fwlx", "Object", "api", "sent", "fwdyid", "stop", "save", "_this3", "_callee2", "param", "resDatas", "res", "_params", "_context2", "lcslclzt", "for<PERSON>ach", "dwid", "lcslid", "undefined", "code", "$router", "$message", "message", "slid", "_this4", "_callee3", "zzjgList", "shu", "shuList", "list", "_context3", "zzjgmc", "childrenRegionVo", "item1", "bmm", "fbmm", "handleSelectionChange1", "handleCurrentChangeRy", "val", "chooseApproval", "handleSizeChangeRy", "searchRy", "bmSelectChange", "join", "_this5", "_callee4", "resData", "_context4", "bmmc", "records", "error", "saveAndSubmit", "_this6", "_callee5", "paramStatus", "_res", "_params2", "_context5", "keys_default", "clrid", "yhid", "pxqk", "cnsqk", "xysqk", "rlspr", "cnsrq", "bmsc", "bmspr", "bmscrq", "rlsc", "rlldspr", "rlscrq", "bmbsc", "bmbldspr", "bmbscrq", "returnIndex", "watch", "rcgz_zgfcTable", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "_v", "ref", "attrs", "model", "label-width", "placeholder", "clearable", "disabled", "$$v", "$set", "scopedSlots", "_u", "key", "fn", "scope", "_e", "src", "_s", "jbzc", "gwmc", "smdj", "border", "header-cell-style", "stripe", "width", "align", "value-format", "size", "on", "click", "$event", "$index", "_l", "slice", "autosize", "staticStyle", "position", "action", "http-request", "show-file-list", "visible", "update:visible", "alt", "slot", "plain", "title", "close-on-click-modal", "destroy-on-close", "for", "options", "filterable", "change", "icon", "tableHeight", "showIndex", "tableData", "columns", "showSingleSelection", "handleColumn", "showPagination", "currentPage", "totalCount", "handleCurrentChange", "handleSizeChange", "clear", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "iQAoeAA,GACAC,YACAC,uBAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAOA,OACAC,SAAA,EACAC,SAAA,EAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,gBACAC,KAAA,EACAC,SAAA,GACAC,cAAA,GACAC,WACAC,MAAA,EACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,iBACAC,WAAA,UACAC,MAAA,WAGAC,QACAC,OAAA,GACAjB,GAAA,GACAkB,GAAA,GACAC,GAAA,KACAC,OAAA,GACAC,OAAA,GACAC,IAAA,GACAC,GAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,QAAA,GACAC,KAAA,GACAC,OAAA,GACAC,SAAA,GACAC,KAAA,IACAC,OAAA,IACAC,KAAA,GACAC,KAAA,IAGAC,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,KAGAC,mBACAC,KAAA,GACArB,KAAA,GACAsB,OAAA,GACA/C,GAAA,GACAgD,MAAA,GACAP,GAAA,GACAE,OAAA,MACAC,OAAA,KAGAK,mBACAC,KAAA,GACAC,GAAA,GACAZ,KAAA,GACAD,KAAA,GAEAK,OAAA,MACAC,OAAA,KAGAQ,qBACAb,KAAA,GACAc,KAAA,GAEAC,KAAA,GACAnC,GAAA,GACAwB,OAAA,MACAC,OAAA,KAGAW,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAhB,OAAA,MACAC,OAAA,KAGAgB,mBACAC,KAAA,KACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,QACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,QACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,KACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,QACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAJ,KAAA,QACAC,KAAA,EACAC,KAAA,IACAC,KAAA,GACAC,IAAA,KAEAC,UAEAC,cACA1D,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAEAC,MAAA,OACAD,MAAA,SAEAC,MAAA,KACAD,MAAA,OAEA4D,YACA3D,MAAA,IACAD,MAAA,MAEAC,MAAA,IACAD,MAAA,MAEA6D,QAAA,GACAC,UAAA,GACAC,UAAA,GACAC,YACAC,eAAA,GACAC,eAAA,EACAC,uBAAA,EACAC,QAAA,GAEAC,eACAC,KAAA,KACAC,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAH,KAAA,KACAC,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAC,uBAGAC,YAMAC,QA5MA,WA6MAC,KAAAC,SACAD,KAAAE,kBACAF,KAAAG,QAAAH,KAAAI,OAAAC,MAAAC,MACAN,KAAAnB,OAAAmB,KAAAI,OAAAC,MAAAC,MAAAC,KACAP,KAAAf,UAAAe,KAAAI,OAAAC,MAAAG,KACAR,KAAAS,QAAAT,KAAAI,OAAAC,MAAAK,GACAC,QAAAC,IAAAZ,KAAAG,QAAA,eACAQ,QAAAC,IAAAZ,KAAAS,SACAE,QAAAC,IAAAZ,KAAAI,OAAAC,MAAAC,MAAAC,KAAA,qBACA,IA6BAM,EA7BAC,KACAC,EAAA,GACAC,EAAA,GACA,UAAAhB,KAAAI,OAAAC,MAAAG,KAEAM,EAAeG,OACfjB,KAAArE,OACAqE,KAAAI,OAAAC,MAAAC,OAEAS,EAAA,0BAAAf,KAAAI,OAAAC,MAAAC,MAAAY,OACA,CAQA,GANAJ,EAAeG,OACfjB,KAAArE,OACAqE,KAAAI,OAAAC,MAAAC,MAAAC,MAEAQ,EAAA,0BAAAf,KAAAI,OAAAC,MAAAC,MAAAC,KAAAW,GAEA,iBADAF,EAAA,0BAAAhB,KAAAI,OAAAC,MAAAC,MAAAC,KAAAxD,MACA,KAGAoE,EAAA,SAAAA,EAAAC,GACA,OAAAD,EAAAE,MAAAC,KAAAF,IAFA,IAAAJ,EAAA,OAMA,GAFAG,EAAAE,MACA,6GACAF,EAAAH,GAAA,CAGAH,EAGAG,EALAhB,KAGAhB,QAAA6B,GAMA,GAAAb,KAAAI,OAAAC,MAAAC,MAAAiB,iBAAAC,OACAxB,KAAAhD,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,KAGAyC,KAAAhD,iBAAAgD,KAAAI,OAAAC,MAAAC,MAAAiB,iBAAAE,IAAA,SAAAnH,GAGA,OAFAA,EAAAgD,OAAA,MACAhD,EAAAiD,OAAA,KACAjD,IAIA,GAAA0F,KAAAI,OAAAC,MAAAC,MAAAoB,iBAAAF,OACAxB,KAAAxC,mBACAC,KAAA,GACArB,KAAA,GACAsB,OAAA,GACA/C,GAAA,GACAgD,MAAA,GACAP,GAAA,GACAE,OAAA,MACAC,OAAA,KAGAyC,KAAAxC,iBAAAwC,KAAAI,OAAAC,MAAAC,MAAAoB,iBAAAD,IAAA,SAAAnH,GAQA,OAPA,GAAAA,EAAAoD,OACApD,EAAAoD,OAAA,IACA,GAAApD,EAAAoD,SACApD,EAAAoD,OAAA,KAEApD,EAAAgD,OAAA,MACAhD,EAAAiD,OAAA,KACAjD,IAIA,GAAA0F,KAAAI,OAAAC,MAAAC,MAAAqB,iBAAAH,OACAxB,KAAApC,mBACAC,KAAA,GACAC,GAAA,GACAZ,KAAA,GACAD,KAAA,GAEAK,OAAA,MACAC,OAAA,KAGAyC,KAAApC,iBAAAoC,KAAAI,OAAAC,MAAAC,MAAAqB,iBAAAF,IAAA,SAAAnH,GAGA,OAFAA,EAAAgD,OAAA,MACAhD,EAAAiD,OAAA,KACAjD,IAIA,GAAA0F,KAAAI,OAAAC,MAAAC,MAAAsB,mBAAAJ,OACAxB,KAAAjC,qBACAb,KAAA,GACAc,KAAA,GAEAC,KAAA,GACAnC,GAAA,GACAwB,OAAA,MACAC,OAAA,KAGAyC,KAAAjC,mBAAAiC,KAAAI,OAAAC,MAAAC,MAAAsB,mBAAAH,IAAA,SAAAnH,GAGA,OAFAA,EAAAgD,OAAA,MACAhD,EAAAiD,OAAA,KACAjD,IAIA,GAAA0F,KAAAI,OAAAC,MAAAC,MAAAuB,iBAAAL,OACAxB,KAAA9B,mBACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAhB,OAAA,MACAC,OAAA,KAGAyC,KAAA9B,iBAAA8B,KAAAI,OAAAC,MAAAC,MAAAuB,iBAAAJ,IAAA,SAAAnH,GAGA,OAFAA,EAAAgD,OAAA,MACAhD,EAAAiD,OAAA,KACAjD,IAIA0F,KAAAI,OAAAC,MAAAC,MAAAwB,iBAAAN,OAAA,IACAxB,KAAAzB,iBAAAyB,KAAAI,OAAAC,MAAAC,MAAAwB,iBAAAL,IAAA,SAAAnH,GACA,OAAAA,KAGA0F,KAAAzB,iBAAA,GAAAC,KAAA,KACAwB,KAAAzB,iBAAA,GAAAC,KAAA,QACAwB,KAAAzB,iBAAA,GAAAC,KAAA,QACAwB,KAAAzB,iBAAA,GAAAC,KAAA,KACAwB,KAAAzB,iBAAA,GAAAC,KAAA,QACAwB,KAAAzB,iBAAA,GAAAC,KAAA,QAKA,GAHAwB,KAAArE,OAAAmF,EACAd,KAAArE,OAAAiB,KAAAkE,EAAAlE,KAAAmF,WACA/B,KAAArE,OAAAkB,OAAAiE,EAAAjE,OAAAkF,WACA,iBAAAhB,EAAA,KAGAiB,EAAA,SAAAA,EAAAZ,GACA,OAAAY,EAAAX,MAAAC,KAAAF,IAFA,IAAAL,EAAA,OAMA,GAFAiB,EAAAX,MACA,6GACAW,EAAAjB,GAAA,KACAkB,EAAAjC,MAEA,SAAAa,GACAoB,EAAAtG,OAAAgB,SAAAkE,EAEAqB,CAAAnB,MAIAoB,SACAC,aADA,SACAC,EAAAC,GACA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAA,SAAAC,GACAJ,EAAAI,EAAAC,OAAA7B,SAEAyB,EAAAK,cAAAP,IAEAQ,sBARA,SAQAC,EAAAC,GACA/C,KAAAjF,cAAAgI,GAGAC,OAZA,SAYA1I,GACAA,EAAA2I,MACAhG,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,GAAA,GACAC,IAAA,GACAC,OAAA,MACAC,OAAA,QAIA2F,OAxBA,SAwBAJ,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGAO,cA5BA,SA4BA/I,GACAA,EAAA2I,MACAK,MAAA,GACA3I,GAAA,GACA4I,iBAAA,GACAC,GAAA,GACApG,GAAA,GACAhB,KAAA,GACAkB,OAAA,MACAC,OAAA,QAIAkG,cAzCA,SAyCAX,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGAY,aA7CA,SA6CApJ,GACAA,EAAA2I,MACAU,KAAA,GACAC,KAAA,GACAC,WAAA,GACA/F,GAAA,GACAR,OAAA,MACAC,OAAA,QAIAuG,aAxDA,SAwDAhB,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGAiB,eA5DA,SA4DAzJ,GACAA,EAAA2I,MACAU,KAAA,GACAK,KAAA,GACAhG,KAAA,GACAC,KAAA,GACAX,OAAA,MACAC,OAAA,QAIA0G,eAvEA,SAuEAnB,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGAoB,gBA3EA,SA2EA5J,GAAA,IAAA6J,EACA7J,EAAA2I,MAAAkB,GACAR,KAAA,GACAS,KAAA,GACAC,KAAA,IAHAC,IAAAH,EAAA,OAIA,IAJAG,IAAAH,EAKA,gBALAG,IAAAH,EAMA,eANAA,KAUAI,gBAtFA,SAsFAzB,EAAAK,GACAA,EAAAC,OAAAN,EAAA,IAGA0B,YA1FA,SA0FAlK,GAAA,IAAAmK,EAAAzE,KACAA,KAAAhB,QAAA0F,IAAAC,gBAAArK,EAAAsK,MACA5E,KAAAT,QAAAjF,EAAAsK,KAEA5E,KAAAoC,aAAA9H,EAAAsK,KAAA,SAAAC,GACAJ,EAAA9I,OAAAoB,KAAA8H,EAAAC,MAAA,WAIAC,MAnGA,WAoGApE,QAAAC,IAAAZ,KAAAf,WACA,OAAAe,KAAAf,UACAe,KAAAZ,eAAAsF,IAAAC,gBAAA3E,KAAAT,SAEAS,KAAAZ,eAAAY,KAAAhB,QAEAgB,KAAAX,eAAA,GAGA2F,QA7GA,WA8GAhF,KAAArE,OAAAoB,KAAA,GACAiD,KAAAhB,QAAA,IAEAiB,OAjHA,WAiHA,IAAAgF,EAAAjF,KAAA,OAAAkF,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAAjL,EAAA,OAAA6K,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACAJ,GACAK,KAAA,GAFAH,EAAAE,KAAA,EAIAE,OAAAC,EAAA,EAAAD,CAAAN,GAJA,OAIAjL,EAJAmL,EAAAM,KAKApF,QAAAC,IAAAtG,GACA2K,EAAAe,OAAA1L,OAAA0L,OANA,wBAAAP,EAAAQ,SAAAX,EAAAL,KAAAC,IASAgB,KA1HA,WA0HA,IAAAC,EAAAnG,KAAA,OAAAkF,IAAAC,EAAAC,EAAAC,KAAA,SAAAe,IAAA,IAAAC,EAAAd,EAAAe,EAAAC,EAAAC,EAAA,OAAArB,EAAAC,EAAAI,KAAA,SAAAiB,GAAA,cAAAA,EAAAf,KAAAe,EAAAd,MAAA,UACAU,GACAL,OAAAG,EAAAH,OACAU,SAAA,GAEAP,EAAA3I,iBAAAmJ,QAAA,SAAAjE,GACA,KAAAA,EAAAhF,OACAgF,EAAAhF,OAAA,EACA,KAAAgF,EAAAhF,SACAgF,EAAAhF,OAAA,KAGA,UAAAyI,EAAAlH,UAZA,CAAAwH,EAAAd,KAAA,YAaAQ,EAAAxK,OAAAiL,KAAAT,EAAAtH,OAAA+H,KACAT,EAAAxK,OAAAkL,OAAAV,EAAAtH,OAAAgI,OACAtB,GACAhF,KAAA4F,EAAAxK,OACA4F,iBAAA4E,EAAAnJ,iBACA0E,iBAAAyE,EAAA3I,iBACAmE,iBAAAwE,EAAAvI,iBACAgE,mBAAAuE,EAAApI,mBACA8D,iBAAAsE,EAAAjI,iBACA4D,iBAAAqE,EAAA5H,kBAEA+H,OAxBA,OAyBAQ,GAAAX,EAAA1F,QAzBA,CAAAgG,EAAAd,KAAA,gBAAAc,EAAAd,KAAA,GA0BAE,OAAAC,EAAA,KAAAD,CAAAN,GA1BA,QA0BAe,EA1BAG,EAAAV,KAAAU,EAAAd,KAAA,oBA2BA,GAAAQ,EAAA1F,QA3BA,CAAAgG,EAAAd,KAAA,gBAAAc,EAAAd,KAAA,GA4BAE,OAAAC,EAAA,IAAAD,CAAAN,GA5BA,QA4BAe,EA5BAG,EAAAV,KAAA,QA8BA,KAAAO,EAAAS,OACAZ,EAAAa,QAAA/D,KAAA,SACAkD,EAAAc,UACAC,QAAA,OACA1G,KAAA,aAlCAiG,EAAAd,KAAA,wBAsCAU,EAAAzK,OAAAuK,EAAAhG,QAAAvE,OACAuK,EAAAxK,OAAAiL,KAAAT,EAAAhG,QAAAyG,KAvCAH,EAAAd,KAAA,GAwCAE,OAAAC,EAAA,EAAAD,CAAAQ,GAxCA,WAyCA,MADAE,EAxCAE,EAAAV,MAyCAgB,KAzCA,CAAAN,EAAAd,KAAA,gBA0CAQ,EAAAxK,OAAAkL,OAAAN,EAAAjM,KAAA6M,KACAX,GACAjG,KAAA4F,EAAAxK,OACA4F,iBAAA4E,EAAAnJ,iBACA0E,iBAAAyE,EAAA3I,iBACAmE,iBAAAwE,EAAAvI,iBACAgE,mBAAAuE,EAAApI,mBACA8D,iBAAAsE,EAAAjI,iBACA4D,iBAAAqE,EAAA5H,kBAlDAkI,EAAAd,KAAA,GAoDAE,OAAAC,EAAA,IAAAD,CAAAW,GApDA,QAqDA,KArDAC,EAAAV,KAqDAgB,MACAZ,EAAAa,QAAA/D,KAAA,SACAkD,EAAAc,UACAC,QAAA,OACA1G,KAAA,aAGAqF,OAAAC,EAAA,EAAAD,EAAAsB,KAAAZ,EAAAjM,KAAA6M,OA5DA,yBAAAV,EAAAR,SAAAG,EAAAD,KAAAjB,IAkEAhF,gBA5LA,WA4LA,IAAAkH,EAAApH,KAAA,OAAAkF,IAAAC,EAAAC,EAAAC,KAAA,SAAAgC,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAtC,EAAAC,EAAAI,KAAA,SAAAkC,GAAA,cAAAA,EAAAhC,KAAAgC,EAAA/B,MAAA,cAAA+B,EAAA/B,KAAA,EACAE,OAAAC,EAAA,IAAAD,GADA,cACAyB,EADAI,EAAA3B,KAEAqB,EAAAO,OAAAL,EACAC,KACAH,EAAAO,OAAAhB,QAAA,SAAA9F,GACA,IAAA+G,KACAR,EAAAO,OAAAhB,QAAA,SAAAkB,GACAhH,EAAAiH,KAAAD,EAAAE,OACAH,EAAA3E,KAAA4E,GACAhH,EAAA+G,sBAGAL,EAAAtE,KAAApC,KAEA2G,KAdAE,EAAA/B,KAAA,EAeAE,OAAAC,EAAA,EAAAD,GAfA,OAgBA,KADA4B,EAfAC,EAAA3B,MAgBAgC,MACAR,EAAAZ,QAAA,SAAA9F,GACA,IAAAA,EAAAkH,MACAP,EAAAvE,KAAApC,KAIA,IAAA4G,EAAAM,MACAR,EAAAZ,QAAA,SAAA9F,GACAF,QAAAC,IAAAC,GACAA,EAAAkH,MAAAN,EAAAM,MACAP,EAAAvE,KAAApC,KAIA2G,EAAA,GAAAI,iBAAAjB,QAAA,SAAA9F,GACAuG,EAAAxM,aAAAqI,KAAApC,KAEAF,QAAAC,IAAAwG,EAAAxM,cAlCA,yBAAA8M,EAAAzB,SAAAoB,EAAAD,KAAAlC,IAoCA8C,uBAhOA,SAgOAlF,EAAAC,GACA/C,KAAAjF,cAAAgI,GAEAkF,sBAnOA,SAmOAC,GACAlI,KAAAnF,KAAAqN,EACAlI,KAAAmI,kBAGAC,mBAxOA,SAwOAF,GACAlI,KAAAnF,KAAA,EACAmF,KAAAlF,SAAAoN,EACAlI,KAAAmI,kBAGAE,SA9OA,WA+OArI,KAAAzF,WACAyF,KAAAmI,kBAGAG,eAnPA,SAmPAzH,QACAiG,GAAAjG,IACAb,KAAAvF,SAAAC,GAAAmG,EAAA0H,KAAA,OAIAJ,eAzPA,WAyPA,IAAAK,EAAAxI,KAAA,OAAAkF,IAAAC,EAAAC,EAAAC,KAAA,SAAAoD,IAAA,IAAApC,EAAAqC,EAAA,OAAAvD,EAAAC,EAAAI,KAAA,SAAAmD,GAAA,cAAAA,EAAAjD,KAAAiD,EAAAhD,MAAA,cAEA6C,EAAAlJ,uBAAA,EACA+G,GACAxL,KAAA2N,EAAA3N,KACAC,SAAA0N,EAAA1N,SACAkL,OAAAwC,EAAAxC,OACA4C,KAAAJ,EAAA/N,SAAAC,GACAC,GAAA6N,EAAA/N,SAAAE,IARAgO,EAAAhD,KAAA,EAUAE,OAAAC,EAAA,GAAAD,CAAAQ,GAVA,QAUAqC,EAVAC,EAAA5C,MAWA8C,SAEAL,EAAAxN,QAAA0N,EAAAG,QACAL,EAAAvN,MAAAyN,EAAAzN,OAEAuN,EAAAvB,SAAA6B,MAAA,WAhBA,wBAAAH,EAAA1C,SAAAwC,EAAAD,KAAAtD,IAoBA6D,cA7QA,WA6QA,IAAAC,EAAAhJ,KAAA,OAAAkF,IAAAC,EAAAC,EAAAC,KAAA,SAAA4D,IAAA,IAAA5C,EAAAd,EAAA2D,EAAAtN,EAAAgL,EAAAuC,EAAAC,EAAA,OAAAjE,EAAAC,EAAAI,KAAA,SAAA6D,GAAA,cAAAA,EAAA3D,KAAA2D,EAAA1D,MAAA,YACA,IAAAqD,EAAAjO,eAAAuO,IAAAN,EAAAjO,eAAAyG,OAAA,GADA,CAAA6H,EAAA1D,KAAA,YAEAU,GACAL,OAAAgD,EAAAhD,QAEAgD,EAAAxL,iBAAAmJ,QAAA,SAAAjE,GACA,KAAAA,EAAAhF,OACAgF,EAAAhF,OAAA,EACA,KAAAgF,EAAAhF,SACAgF,EAAAhF,OAAA,KAGA,UAAAsL,EAAA/J,gBAAA6H,GAAAkC,EAAAvI,QAZA,CAAA4I,EAAA1D,KAAA,gBAaAU,EAAAK,SAAA,EACAL,EAAAzK,OAAAoN,EAAAnK,OAAAjD,OACAyK,EAAAc,KAAA6B,EAAAnK,OAAAgI,OACAR,EAAAkD,MAAAP,EAAAjO,cAAAyO,KAhBAH,EAAA1D,KAAA,GAiBAE,OAAAC,EAAA,EAAAD,CAAAQ,GAjBA,WAkBA,KAlBAgD,EAAAtD,KAkBAgB,KAlBA,CAAAsC,EAAA1D,KAAA,gBAmBAqD,EAAArN,OAAAiL,KAAAoC,EAAAnK,OAAA+H,KACAoC,EAAArN,OAAAkL,OAAAmC,EAAAnK,OAAAgI,OACAtB,GACAhF,KAAAyI,EAAArN,OACA4F,iBAAAyH,EAAAhM,iBACA0E,iBAAAsH,EAAAxL,iBACAmE,iBAAAqH,EAAApL,iBACAgE,mBAAAoH,EAAAjL,mBACA8D,iBAAAmH,EAAA9K,iBACA4D,iBAAAkH,EAAAzK,kBA5BA8K,EAAA1D,KAAA,GA8BAE,OAAAC,EAAA,KAAAD,CAAAN,GA9BA,WA+BA,KA/BA8D,EAAAtD,KA+BAgB,KA/BA,CAAAsC,EAAA1D,KAAA,gBAgCAuD,GACAlD,OAAAgD,EAAAhD,OACAmB,KAAA6B,EAAArN,OAAAkL,aAlCA,EAAAwC,EAAA1D,KAAA,GAsCAE,OAAAC,EAAA,IAAAD,CAAAqD,GAtCA,QAyCA,KAzCAG,EAAAtD,KAyCAgB,OACAiC,EAAAhC,QAAA/D,KAAA,SACA+F,EAAA/B,UACAC,QAAA,UACA1G,KAAA,aA7CA,QAAA6I,EAAA1D,KAAA,wBAmDAhF,QAAAC,IAAAoI,EAAAnK,QACAjD,EAAA,GACAgL,EAAA,GACAoC,EAAAnK,QACAjD,EAAAoN,EAAAnK,OAAAjD,OACAgL,EAAAoC,EAAAnK,OAAA+H,OAEAhL,EAAAoN,EAAA7I,QAAAvE,OACAgL,EAAAoC,EAAA7I,QAAAyG,MAEAjG,QAAAC,IAAAoI,EAAA5I,OAAAC,MAAAC,OACA+F,EAAAK,SAAA,EACAL,EAAAkD,MAAAP,EAAAjO,cAAAyO,KACAnD,EAAAzK,SAhEAyN,EAAA1D,KAAA,GAiEAE,OAAAC,EAAA,EAAAD,CAAAQ,GAjEA,WAkEA,MADA8C,EAjEAE,EAAAtD,MAkEAgB,KAlEA,CAAAsC,EAAA1D,KAAA,gBAmEAqD,EAAArN,OAAAiL,OACAoC,EAAArN,OAAAkL,OAAAsC,EAAA7O,KAAA6M,KACA6B,EAAArN,OAAA8N,KAAA,GACAT,EAAArN,OAAA+N,MAAA,GACAV,EAAArN,OAAAgO,MAAA,GACAX,EAAArN,OAAAiO,MAAA,GACAZ,EAAArN,OAAAkO,MAAA,GACAb,EAAArN,OAAAmO,KAAA,GACAd,EAAArN,OAAAoO,MAAA,GACAf,EAAArN,OAAAqO,OAAA,GACAhB,EAAArN,OAAAsO,KAAA,GACAjB,EAAArN,OAAAuO,QAAA,GACAlB,EAAArN,OAAAwO,OAAA,GACAnB,EAAArN,OAAAyO,MAAA,GACApB,EAAArN,OAAA0O,SAAA,GACArB,EAAArN,OAAA2O,QAAA,GACAlB,GACA7I,KAAAyI,EAAArN,OACA4F,iBAAAyH,EAAAhM,iBACA0E,iBAAAsH,EAAAxL,iBACAmE,iBAAAqH,EAAApL,iBACAgE,mBAAAoH,EAAAjL,mBACA8D,iBAAAmH,EAAA9K,iBACA4D,iBAAAkH,EAAAzK,kBA1FA8K,EAAA1D,KAAA,GA4FAE,OAAAC,EAAA,IAAAD,CAAAuD,GA5FA,QA6FA,KA7FAC,EAAAtD,KA6FAgB,MACAiC,EAAAhC,QAAA/D,KAAA,SACA+F,EAAA/B,UACAC,QAAA,UACA1G,KAAA,aAGAqF,OAAAC,EAAA,EAAAD,EAAAsB,KAAAgC,EAAA7O,KAAA6M,OApGA,QAAAkC,EAAA1D,KAAA,iBAyGAqD,EAAA/B,UACAC,QAAA,SACA1G,KAAA,YA3GA,yBAAA6I,EAAApD,SAAAgD,EAAAD,KAAA9D,IAgHAqF,YA7XA,WA8XAvK,KAAAgH,QAAA/D,KAAA,WAGAuH,UC5tCeC,GADEC,OAFjB,WAA0B,IAAAC,EAAA3K,KAAa4K,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAavL,KAAA,UAAAwL,QAAA,YAAA7P,MAAAuP,EAAA,QAAAO,WAAA,YAA4EC,YAAA,kBAA8BL,EAAA,KAAUK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,uBAAiCL,EAAA,WAAgBO,IAAA,WAAAC,OAAsBC,MAAAZ,EAAAhP,OAAA6P,cAAA,WAA0CV,EAAA,OAAYK,YAAA,uBAAiCL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnQ,MAAA,QAAc2P,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,GAAA2G,SAAA,SAAAsJ,GAA+CjB,EAAAkB,KAAAlB,EAAAhP,OAAA,KAAAiQ,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnQ,MAAA,MAAa2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,UAAAvB,EAAAhP,OAAAE,GAAAiP,EAAA,KAAqCK,YAAA,SAAmBR,EAAAS,GAAA,OAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAAE,GAAAiP,EAAA,KAAkEK,YAAA,SAAmBR,EAAAS,GAAA,OAAAT,EAAAwB,YAA8BxB,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAOnQ,MAAA,QAAc2P,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,IAAgCH,OAAQnQ,MAAAuP,EAAAhP,OAAA,GAAA2G,SAAA,SAAAsJ,GAA+CjB,EAAAkB,KAAAlB,EAAAhP,OAAA,KAAAiQ,IAAgCV,WAAA,gBAAyB,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnQ,MAAA,SAAe2P,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,IAAA2G,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,MAAAiQ,IAAiCV,WAAA,iBAA0B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnQ,MAAA,QAAc2P,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,GAAA2G,SAAA,SAAAsJ,GAA+CjB,EAAAkB,KAAAlB,EAAAhP,OAAA,KAAAiQ,IAAgCV,WAAA,gBAAyB,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnQ,MAAA,QAAe2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,UAAAvB,EAAAhP,OAAAQ,KAAA2O,EAAA,KAAuCK,YAAA,SAAmBR,EAAAS,GAAA,QAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAAQ,KAAA2O,EAAA,KAAqEK,YAAA,SAAmBR,EAAAS,GAAA,QAAAT,EAAAwB,aAA+B,GAAAxB,EAAAS,GAAA,KAAAN,EAAA,OAA4BK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnQ,MAAA,QAAe2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,UAAAvB,EAAAhP,OAAAS,KAAA0O,EAAA,KAAuCK,YAAA,SAAmBR,EAAAS,GAAA,UAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAAS,KAAA0O,EAAA,KAAuEK,YAAA,SAAmBR,EAAAS,GAAA,QAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAAS,KAAA0O,EAAA,KAAqEK,YAAA,SAAmBR,EAAAS,GAAA,UAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAAS,KAAA0O,EAAA,KAAuEK,YAAA,SAAmBR,EAAAS,GAAA,QAAAT,EAAAwB,YAA+BxB,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAOnQ,MAAA,UAAgB2P,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,KAAA2G,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,OAAAiQ,IAAkCV,WAAA,kBAA2B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnQ,MAAA,UAAgB2P,EAAA,YAAiBQ,OAAOG,YAAA,GAAAC,UAAA,GAAAC,SAAA,IAA8CJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,MAAA2G,SAAA,SAAAsJ,GAAkDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,QAAAiQ,IAAmCV,WAAA,mBAA4B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnQ,MAAA,UAAgB2P,EAAA,YAAiBQ,OAAOG,YAAA,OAAAC,UAAA,GAAAC,SAAA,IAAkDJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,KAAA2G,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,OAAAiQ,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnQ,MAAA,aAAmB2P,EAAA,YAAiBQ,OAAOG,YAAA,QAAAC,UAAA,GAAAC,SAAA,IAAmDJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,QAAA2G,SAAA,SAAAsJ,GAAoDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,UAAAiQ,IAAqCV,WAAA,qBAA8B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,kBAA4BL,EAAA,gBAAqBQ,OAAOnQ,MAAA,UAAgB2P,EAAA,YAAiBQ,OAAOG,YAAA,OAAAC,UAAA,GAAAC,SAAA,IAAkDJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,KAAA2G,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,OAAAiQ,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,gBAAqCQ,OAAOnQ,MAAA,aAAmB2P,EAAA,YAAiBQ,OAAOG,YAAA,QAAAC,UAAA,GAAAC,SAAA,IAAmDJ,OAAQnQ,MAAAuP,EAAAhP,OAAA,OAAA2G,SAAA,SAAAsJ,GAAmDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,SAAAiQ,IAAoCV,WAAA,oBAA6B,OAAAP,EAAAS,GAAA,KAAAN,EAAA,OAAgCK,YAAA,mBAA6BL,EAAA,OAAAH,EAAAhP,OAAA,SAAAmP,EAAA,OAA4CK,YAAA,SAAAG,OAA4Bc,IAAAzB,EAAAhP,OAAAgB,YAA2BgO,EAAAwB,WAAAxB,EAAAS,GAAA,KAAAN,EAAA,OAAuCK,YAAA,oBAA8BL,EAAA,gBAAqBQ,OAAOnQ,MAAA,YAAmB2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,KAAgBK,YAAA,SAAmBR,EAAAhP,OAAA,KAAAmP,EAAA,QAAAH,EAAAS,GAAAT,EAAA0B,GAAA1B,EAAAhP,OAAAiN,MAAA,OAAA+B,EAAAwB,KAAAxB,EAAAS,GAAA,KAAAT,EAAAhP,OAAA,GAAAmP,EAAA,QAAAH,EAAAS,GAAAT,EAAA0B,GAAA1B,EAAAhP,OAAAyB,IAAA,OAAAuN,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,SAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,SAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,SAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,YAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,gBAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,QAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,WAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,WAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,UAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,SAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,UAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,SAAAT,EAAAhP,OAAA2Q,KAAAxB,EAAA,QAAAH,EAAAS,GAAA,QAAAT,EAAAwB,eAAs8B,GAAAxB,EAAAS,GAAA,KAAAN,EAAA,OAA4BK,YAAA,oBAA8BL,EAAA,gBAAqBQ,OAAOnQ,MAAA,aAAoB2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAvB,EAAAhP,OAAA4Q,MAAA,IAAA5B,EAAAhP,OAAA4Q,KAAA/K,OAAAsJ,EAAA,KAAkEK,YAAA,SAAmBR,EAAAS,GAAA,IAAAT,EAAA0B,GAAA1B,EAAAhP,OAAA4Q,KAAAxK,eAAA4I,EAAAhP,OAAA4Q,MAAA5B,EAAAhP,OAAA4Q,KAAA/K,OAAA,EAAAsJ,EAAA,KAA2GK,YAAA,SAAmBR,EAAAS,GAAA,IAAAT,EAAA0B,GAAA1B,EAAAhP,OAAA4Q,KAAAhE,KAAA,aAAAoC,EAAAwB,YAAoExB,EAAAS,GAAA,KAAAN,EAAA,gBAAiCQ,OAAOnQ,MAAA,QAAe2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,UAAAvB,EAAAhP,OAAA6Q,KAAA1B,EAAA,KAAuCK,YAAA,SAAmBR,EAAAS,GAAA,QAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA6Q,KAAA1B,EAAA,KAAqEK,YAAA,SAAmBR,EAAAS,GAAA,QAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,QAAAT,EAAAhP,OAAA6Q,KAAA1B,EAAA,KAAqEK,YAAA,SAAmBR,EAAAS,GAAA,QAAAT,EAAAwB,aAA+B,GAAAxB,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,eAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAmDK,YAAA,eAAAG,OAAkCmB,OAAA,GAAAnS,KAAAqQ,EAAA3N,iBAAA0P,qBAA6DjR,WAAA,UAAAC,MAAA,WAA0CiR,OAAA,MAAc7B,EAAA,mBAAwBQ,OAAO9K,KAAA,QAAAoM,MAAA,KAAAzR,MAAA,KAAA0R,MAAA,YAA2DlC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,kBAA6BQ,OAAO9K,KAAA,OAAAiL,YAAA,OAAAqB,eAAA,cAA+DvB,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,kBAA6BQ,OAAO9K,KAAA,OAAAiL,YAAA,OAAAqB,eAAA,cAA+DvB,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,eAAoC2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,KAAAvE,MAAA,QAA2B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,GAAAT,SAAA,SAAAsJ,GAA8CjB,EAAAkB,KAAAK,EAAAnJ,IAAA,KAAA6I,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,MAAAvE,MAAA,OAA2B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,IAAAT,SAAA,SAAAsJ,GAA+CjB,EAAAkB,KAAAK,EAAAnJ,IAAA,MAAA6I,IAAgCV,WAAA,0BAAoCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOnQ,MAAA,KAAAyR,MAAA,OAA2Bd,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,WAAAA,EAAAnJ,IAAAzF,OAAAwN,EAAA,aAAiDQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAA3H,OAAA2H,EAAA3N,sBAA0C2N,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAzF,QAAA,oBAAAqN,EAAAwB,KAAAxB,EAAAS,GAAA,SAAAc,EAAAnJ,IAAAxF,OAAAuN,EAAA,aAAoHQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAAzH,OAAAgJ,EAAAiB,OAAAxC,EAAA3N,sBAAwD2N,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAxF,QAAA,oBAAAoN,EAAAwB,aAAoE,GAAAxB,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAuDK,YAAA,eAAAG,OAAkCmB,OAAA,GAAAnS,KAAAqQ,EAAAnN,iBAAAkP,qBAA6DjR,WAAA,UAAAC,MAAA,WAA0CiR,OAAA,MAAc7B,EAAA,mBAAwBQ,OAAO9K,KAAA,QAAAoM,MAAA,KAAAzR,MAAA,KAAA0R,MAAA,YAA2DlC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,SAA8B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,KAAAvE,MAAA,MAAyB2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,GAAAT,SAAA,SAAAsJ,GAA8CjB,EAAAkB,KAAAK,EAAAnJ,IAAA,KAAA6I,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,SAAAvE,MAAA,sBAA6C2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,aAAwBQ,OAAOG,YAAA,OAAoBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,OAAAT,SAAA,SAAAsJ,GAAkDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,SAAA6I,IAAmCV,WAAA,qBAAgCP,EAAAyC,GAAAzC,EAAA,mBAAA9J,GAAuC,OAAAiK,EAAA,aAAuBkB,IAAAnL,EAAAzF,MAAAkQ,OAAsBnQ,MAAA0F,EAAA1F,MAAAC,MAAAyF,EAAAzF,WAAyC,UAAUuP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,QAAAvE,MAAA,MAA4B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,MAAAT,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,QAAA6I,IAAkCV,WAAA,4BAAsCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,KAAAvE,MAAA,MAAyB2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,GAAAT,SAAA,SAAAsJ,GAA8CjB,EAAAkB,KAAAK,EAAAnJ,IAAA,KAAA6I,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,aAAwBQ,OAAOG,YAAA,OAAoBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,mBAA8BP,EAAAyC,GAAAzC,EAAA,qBAAA9J,GAAyC,OAAAiK,EAAA,aAAuBkB,IAAAnL,EAAAzF,MAAAkQ,OAAsBnQ,MAAA0F,EAAA1F,MAAAC,MAAAyF,EAAAzF,WAAyC,UAAUuP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOnQ,MAAA,KAAAyR,MAAA,OAA2Bd,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,WAAAA,EAAAnJ,IAAAzF,OAAAwN,EAAA,aAAiDQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAAtH,cAAAsH,EAAAnN,sBAAiDmN,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAzF,QAAA,oBAAAqN,EAAAwB,KAAAxB,EAAAS,GAAA,SAAAc,EAAAnJ,IAAAxF,OAAAuN,EAAA,aAAoHQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAAlH,cAAAyI,EAAAiB,OAAAxC,EAAAnN,sBAA+DmN,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAxF,QAAA,oBAAAoN,EAAAwB,aAAoE,GAAAxB,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,eAAAT,EAAAS,GAAA,KAAAN,EAAA,OAA8CK,YAAA,4CAAsDL,EAAA,gBAAqBQ,OAAOnQ,MAAA,4BAAkC2P,EAAA,YAAiBQ,OAAOnQ,MAAA,KAAYoQ,OAAQnQ,MAAAuP,EAAAhP,OAAA,KAAA2G,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,OAAAiQ,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,OAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA2CQ,OAAOnQ,MAAA,KAAYoQ,OAAQnQ,MAAAuP,EAAAhP,OAAA,KAAA2G,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,OAAAiQ,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,eAAAT,EAAAS,GAAA,KAAAN,EAAA,KAA4CK,YAAA,cAAwBR,EAAAS,GAAA,iBAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAqDK,YAAA,eAAAG,OAAkCmB,OAAA,GAAAnS,KAAAqQ,EAAApM,iBAAA8O,MAAA,KAAAX,qBAAyEjR,WAAA,UAAAC,MAAA,WAA0CiR,OAAA,MAAc7B,EAAA,mBAAwBQ,OAAO9K,KAAA,QAAAoM,MAAA,KAAAzR,MAAA,KAAA0R,MAAA,YAA2DlC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,UAA8BwP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOnQ,MAAA,KAAYoQ,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,oBAA8BP,EAAAS,GAAA,OAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA2CQ,OAAOnQ,MAAA,KAAYoQ,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,oBAA8BP,EAAAS,GAAA,cAAqBT,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,MAAAvE,MAAA,OAA2B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,kBAA6BQ,OAAO9K,KAAA,OAAAiL,YAAA,OAAAqB,eAAA,cAA+DvB,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,IAAAT,SAAA,SAAAsJ,GAA+CjB,EAAAkB,KAAAK,EAAAnJ,IAAA,MAAA6I,IAAgCV,WAAA,2BAAoC,GAAAP,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,iBAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAqDK,YAAA,eAAAG,OAAkCmB,OAAA,GAAAnS,KAAAqQ,EAAApM,iBAAA8O,MAAA,KAAAX,qBAAyEjR,WAAA,UAAAC,MAAA,WAA0CiR,OAAA,MAAc7B,EAAA,mBAAwBQ,OAAO9K,KAAA,QAAAoM,MAAA,KAAAzR,MAAA,KAAA0R,MAAA,YAA2DlC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,UAA8BwP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOnQ,MAAA,KAAYoQ,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,oBAA8BP,EAAAS,GAAA,OAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA2CQ,OAAOnQ,MAAA,KAAYoQ,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,oBAA8BP,EAAAS,GAAA,cAAqBT,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,MAAAvE,MAAA,MAAA2R,eAAA,cAAuDhB,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,kBAA6BQ,OAAO9K,KAAA,OAAAiL,YAAA,OAAAqB,eAAA,cAA+DvB,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,IAAAT,SAAA,SAAAsJ,GAA+CjB,EAAAkB,KAAAK,EAAAnJ,IAAA,MAAA6I,IAAgCV,WAAA,2BAAoC,GAAAP,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,eAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAmDK,YAAA,eAAAG,OAAkCmB,OAAA,GAAAnS,KAAAqQ,EAAA/M,iBAAA8O,qBAA6DjR,WAAA,UAAAC,MAAA,WAA0CiR,OAAA,MAAc7B,EAAA,mBAAwBQ,OAAO9K,KAAA,QAAAoM,MAAA,KAAAzR,MAAA,KAAA0R,MAAA,YAA2DlC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,kBAA6BQ,OAAO9K,KAAA,OAAAiL,YAAA,OAAAqB,eAAA,cAA+DvB,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,kBAA6BQ,OAAO9K,KAAA,OAAAiL,YAAA,OAAAqB,eAAA,cAA+DvB,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,cAAmC2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,KAAAvE,MAAA,MAAyB2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,GAAAT,SAAA,SAAAsJ,GAA8CjB,EAAAkB,KAAAK,EAAAnJ,IAAA,KAAA6I,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOnQ,MAAA,KAAAyR,MAAA,OAA2Bd,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,WAAAA,EAAAnJ,IAAAzF,OAAAwN,EAAA,aAAiDQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAAjH,aAAAiH,EAAA/M,sBAAgD+M,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAzF,QAAA,oBAAAqN,EAAAwB,KAAAxB,EAAAS,GAAA,SAAAc,EAAAnJ,IAAAxF,OAAAuN,EAAA,aAAoHQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAA7G,aAAAoI,EAAAiB,OAAAxC,EAAA/M,sBAA8D+M,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAxF,QAAA,oBAAAoN,EAAAwB,aAAoE,GAAAxB,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,cAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAkDK,YAAA,eAAAG,OAAkCmB,OAAA,GAAAnS,KAAAqQ,EAAA5M,mBAAA2O,qBAA+DjR,WAAA,UAAAC,MAAA,WAA0CiR,OAAA,MAAc7B,EAAA,mBAAwBQ,OAAO9K,KAAA,QAAAoM,MAAA,KAAAzR,MAAA,KAAA0R,MAAA,YAA2DlC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,kBAA6BQ,OAAO9K,KAAA,OAAAiL,YAAA,OAAAqB,eAAA,cAA+DvB,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,KAAAvE,MAAA,QAA2B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,GAAAT,SAAA,SAAAsJ,GAA8CjB,EAAAkB,KAAAK,EAAAnJ,IAAA,KAAA6I,IAA+BV,WAAA,yBAAmCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOnQ,MAAA,KAAAyR,MAAA,OAA2Bd,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,WAAAA,EAAAnJ,IAAAzF,OAAAwN,EAAA,aAAiDQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAA5G,eAAA4G,EAAA5M,wBAAoD4M,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAzF,QAAA,oBAAAqN,EAAAwB,KAAAxB,EAAAS,GAAA,SAAAc,EAAAnJ,IAAAxF,OAAAuN,EAAA,aAAoHQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAA1G,eAAAiI,EAAAiB,OAAAxC,EAAA5M,wBAAkE4M,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAxF,QAAA,oBAAAoN,EAAAwB,aAAoE,GAAAxB,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,gBAAAT,EAAAS,GAAA,KAAAN,EAAA,YAAoDK,YAAA,eAAAG,OAAkCmB,OAAA,GAAAnS,KAAAqQ,EAAAzM,iBAAAwO,qBAA6DjR,WAAA,UAAAC,MAAA,WAA0CiR,OAAA,MAAc7B,EAAA,mBAAwBQ,OAAO9K,KAAA,QAAAoM,MAAA,KAAAzR,MAAA,KAAA0R,MAAA,YAA2DlC,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,kBAA6BQ,OAAO9K,KAAA,OAAAiL,YAAA,OAAAqB,eAAA,cAA+DvB,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAO5L,KAAA,OAAAvE,MAAA,QAA6B2Q,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAApB,EAAA,YAAuBQ,OAAOG,YAAA,IAAiBF,OAAQnQ,MAAA8Q,EAAAnJ,IAAA,KAAAT,SAAA,SAAAsJ,GAAgDjB,EAAAkB,KAAAK,EAAAnJ,IAAA,OAAA6I,IAAiCV,WAAA,2BAAqCP,EAAAS,GAAA,KAAAN,EAAA,mBAAoCQ,OAAOnQ,MAAA,KAAAyR,MAAA,OAA2Bd,YAAAnB,EAAAoB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,WAAAA,EAAAnJ,IAAAzF,OAAAwN,EAAA,aAAiDQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAAzG,gBAAAyG,EAAAzM,sBAAmDyM,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAzF,QAAA,oBAAAqN,EAAAwB,KAAAxB,EAAAS,GAAA,SAAAc,EAAAnJ,IAAAxF,OAAAuN,EAAA,aAAoHQ,OAAOyB,KAAA,SAAAvM,KAAA,QAA8BwM,IAAKC,MAAA,SAAAC,GAAyB,OAAAvC,EAAApG,gBAAA2H,EAAAiB,OAAAxC,EAAAzM,sBAAiEyM,EAAAS,GAAAT,EAAA0B,GAAAH,EAAAnJ,IAAAxF,QAAA,oBAAAoN,EAAAwB,aAAoE,GAAAxB,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,cAAAT,EAAAS,GAAA,KAAAN,EAAA,OAA6CK,YAAA,iCAA2CL,EAAA,OAAYK,YAAA,kBAA4BL,EAAA,KAAAH,EAAAS,GAAA,kCAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,uBAAAT,EAAAS,GAAA,KAAAN,EAAA,KAAAH,EAAAS,GAAA,4BAAAT,EAAAS,GAAA,KAAAN,EAAA,gBAA2LQ,OAAOnQ,MAAA,MAAY2P,EAAA,YAAiBQ,OAAOnQ,MAAA,KAAYoQ,OAAQnQ,MAAAuP,EAAAhP,OAAA,OAAA2G,SAAA,SAAAsJ,GAAmDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,SAAAiQ,IAAoCV,WAAA,mBAA6BP,EAAAS,GAAA,OAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA2CQ,OAAOnQ,MAAA,KAAYoQ,OAAQnQ,MAAAuP,EAAAhP,OAAA,OAAA2G,SAAA,SAAAsJ,GAAmDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,SAAAiQ,IAAoCV,WAAA,mBAA6BP,EAAAS,GAAA,eAAAT,EAAAS,GAAA,KAAAN,EAAA,KAA4CK,YAAA,cAAwBR,EAAAS,GAAA,eAAAT,EAAAS,GAAA,KAAAN,EAAA,OAA8CK,YAAA,gCAA0CL,EAAA,YAAiBQ,OAAO9K,KAAA,WAAA8M,SAAA,GAAA7B,YAAA,SAAsDF,OAAQnQ,MAAAuP,EAAAhP,OAAA,KAAA2G,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAlB,EAAAhP,OAAA,OAAAiQ,IAAkCV,WAAA,kBAA2B,GAAAP,EAAAS,GAAA,KAAAN,EAAA,KAA0BK,YAAA,cAAwBR,EAAAS,GAAA,UAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAyCK,YAAA,8BAAAoC,aAAuDC,SAAA,cAAuB1C,EAAA,aAAkBK,YAAA,cAAAG,OAAiCmC,OAAA,IAAAC,eAAA/C,EAAAnG,YAAAmJ,kBAAA,KAAoEhD,EAAA,QAAAG,EAAA,OAA0BK,YAAA,SAAAG,OAA4Bc,IAAAzB,EAAA3L,WAAmB8L,EAAA,KAAUK,YAAA,wCAAgDR,EAAAS,GAAA,KAAAT,EAAA,QAAAG,EAAA,KAAsCK,YAAA,QAAA6B,IAAwBC,MAAAtC,EAAA5F,SAAmB4F,EAAAS,GAAA,QAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,KAAAT,EAAA,QAAAG,EAAA,KAA4DK,YAAA,QAAA6B,IAAwBC,MAAAtC,EAAA3F,WAAqB2F,EAAAS,GAAA,QAAAT,EAAAwB,KAAAxB,EAAAS,GAAA,KAAAN,EAAA,aAAsDQ,OAAOsC,QAAAjD,EAAAtL,eAA4B2N,IAAKa,iBAAA,SAAAX,GAAkCvC,EAAAtL,cAAA6N,MAA2BpC,EAAA,OAAYyC,aAAaX,MAAA,QAAetB,OAAQc,IAAAzB,EAAAvL,eAAA0O,IAAA,MAAmCnD,EAAAS,GAAA,KAAAN,EAAA,OAAwBK,YAAA,gBAAAG,OAAmCyC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBQ,OAAOyB,KAAA,SAAeC,IAAKC,MAAA,SAAAC,GAAyBvC,EAAAtL,eAAA,MAA4BsL,EAAAS,GAAA,mBAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAkDK,YAAA,0CAAoDL,EAAA,aAAkBK,YAAA,UAAAG,OAA6B0C,MAAA,IAAWhB,IAAKC,MAAAtC,EAAAJ,eAAyBI,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CK,YAAA,KAAAG,OAAwB9K,KAAA,WAAiBwM,IAAKC,MAAAtC,EAAAxC,kBAA4BwC,EAAAS,GAAA,WAAAT,EAAAS,GAAA,KAAAN,EAAA,aAAgDK,YAAA,KAAAG,OAAwB9K,KAAA,WAAiBwM,IAAKC,MAAAtC,EAAAzE,QAAkByE,EAAAS,GAAA,sBAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA2DQ,OAAO2C,MAAA,QAAAC,wBAAA,EAAAN,QAAAjD,EAAArL,sBAAAsN,MAAA,MAAAuB,oBAAA,GAAuHnB,IAAKa,iBAAA,SAAAX,GAAkCvC,EAAArL,sBAAA4N,MAAmCpC,EAAA,OAAYK,YAAA,oBAA8BL,EAAA,SAAcQ,OAAO8C,IAAA,MAAUzD,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,eAAgDO,IAAA,cAAAC,OAAyB+C,QAAA1D,EAAA/P,aAAAP,MAAAsQ,EAAAzP,aAAAoT,WAAA,GAAA5C,UAAA,IAAmFsB,IAAKuB,OAAA5D,EAAArC,gBAA4BiD,OAAQnQ,MAAAuP,EAAAlQ,SAAA,GAAA6H,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAlB,EAAAlQ,SAAA,KAAAmR,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,SAA0BQ,OAAO8C,IAAA,MAAUzD,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,YAA6CK,YAAA,SAAAG,OAA4BI,UAAA,GAAAD,YAAA,MAAkCF,OAAQnQ,MAAAuP,EAAAlQ,SAAA,GAAA6H,SAAA,SAAAsJ,GAAiDjB,EAAAkB,KAAAlB,EAAAlQ,SAAA,KAAAmR,IAAkCV,WAAA,iBAA2BP,EAAAS,GAAA,KAAAN,EAAA,aAA8BK,YAAA,eAAAG,OAAkC9K,KAAA,UAAAgO,KAAA,kBAAyCxB,IAAKC,MAAAtC,EAAAtC,YAAsBsC,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA6CkB,IAAArB,EAAApQ,SAAA4Q,YAAA,YAAAG,OAAgDmD,YAAA,MAAAC,WAAA,EAAAC,UAAAhE,EAAA3P,QAAA4T,QAAAjE,EAAAnL,aAAAqP,qBAAA,EAAAC,aAAAnE,EAAA9K,kBAAAkP,gBAAA,EAAAC,YAAArE,EAAA9P,KAAAC,SAAA6P,EAAA7P,SAAAmU,WAAAtE,EAAA1P,OAAoP+R,IAAKkC,oBAAAvE,EAAA1C,sBAAAkH,iBAAAxE,EAAAvC,mBAAAvF,sBAAA8H,EAAA9H,0BAA6I,GAAA8H,EAAAS,GAAA,KAAAN,EAAA,QAA6BK,YAAA,gBAAAG,OAAmCyC,KAAA,UAAgBA,KAAA,WAAejD,EAAA,aAAkBK,YAAA,UAAAG,OAA6B9K,KAAA,WAAiBwM,IAAKC,MAAA,SAAAC,GAAyBvC,EAAArL,uBAAA,MAAoCqL,EAAAS,GAAA,SAAAT,EAAAS,GAAA,KAAAN,EAAA,aAA8CK,YAAA,KAAAG,OAAwB9K,KAAA,WAAiBwM,IAAKC,MAAAtC,EAAA5B,iBAA2B4B,EAAAS,GAAA,QAAAT,EAAAS,GAAA,KAAAN,EAAA,OAAuCyC,aAAa6B,MAAA,WAAgB,UAE5m0BC,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACExV,EACAyQ,GATF,EAVA,SAAAgF,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/230.f04966a4ecb4ea2ec8db.js", "sourcesContent": ["<template>\r\n  <div class=\"sec-container\" v-loading=\"loading\">\r\n    <!-- 标题 -->\r\n    <p class=\"sec-title\">基本信息</p>\r\n    <div class=\"sec-form-container\">\r\n      <el-form ref=\"formName\" :model=\"tjlist\" label-width=\"225px\">\r\n        <!-- 第一部分包括姓名到常住地公安start -->\r\n        <div class=\"sec-header-section\">\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"姓名\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.xm\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"性别\">\r\n              <!-- <el-input placeholder=\"\" v-model=\"tjlist.xb\" clearable disabled></el-input> -->\r\n              <template slot-scope=\"scope\">\r\n                <p class=\"hyzk\" v-if=\"tjlist.xb == 2\">女</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.xb == 1\">男</p>\r\n              </template>\r\n            </el-form-item>\r\n            <el-form-item label=\"国籍\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.gj\" clearable></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"曾用名\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.cym\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"民族\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.mz\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"婚姻状况\">\r\n              <template slot-scope=\"scope\">\r\n                <p class=\"hyzk\" v-if=\"tjlist.hyzk == 0\">未婚</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.hyzk == 1\">已婚</p>\r\n              </template>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"政治面貌\">\r\n              <template slot-scope=\"scope\">\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 1\">中共党员</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 2\">团员</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 3\">民主党派</p>\r\n                <p class=\"hyzk\" v-if=\"tjlist.zzmm == 4\">群众</p>\r\n              </template>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.lxdh\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"身份证号\">\r\n              <el-input placeholder=\"\" v-model=\"tjlist.sfzhm\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"户籍地址\">\r\n              <el-input placeholder=\"（详细）\" v-model=\"tjlist.hjdz\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"户籍地公安机关\">\r\n              <el-input placeholder=\"街道派出所\" v-model=\"tjlist.hjdgajg\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <div class=\"sec-form-left\">\r\n            <el-form-item label=\"常住地址\">\r\n              <el-input placeholder=\"（详细）\" v-model=\"tjlist.czdz\" clearable disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"常住地公安机关\">\r\n              <el-input placeholder=\"街道派出所\" v-model=\"tjlist.czgajg\" clearable disabled></el-input>\r\n            </el-form-item>\r\n          </div>\r\n          <!-- 电子照片 -->\r\n          <div class=\"sec-header-pic\">\r\n            <div>\r\n              <img v-if=\"tjlist.imageUrl\" :src=\"tjlist.imageUrl\" class=\"avatar\" style=\"\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 第一部分包括姓名到常住地公安end -->\r\n        <!-- 单位及职务、职称到涉密等级start -->\r\n        <div class=\"sec-form-second\">\r\n          <el-form-item label=\"部门及职务、职称\">\r\n            <!-- <el-input placeholder=\"\" v-model=\"tjlist.dwzwzc\" clearable></el-input> -->\r\n            <template slot-scope=\"scope\">\r\n              <p class=\"hyzk\">\r\n                <span v-if=\"tjlist.bmmc\">{{ tjlist.bmmc }}、</span>\r\n                <span v-if=\"tjlist.zw\">{{ tjlist.zw }}、</span>\r\n                <span v-if=\"tjlist.jbzc == 1\">省部级</span> <span v-if=\"tjlist.jbzc == 2\">厅局级</span> <span\r\n                  v-if=\"tjlist.jbzc == 3\">县处级</span> <span v-if=\"tjlist.jbzc == 4\">乡科级及以下</span>\r\n                <span v-if=\"tjlist.jbzc == 5\">高级(含正高、副高)</span> <span v-if=\"tjlist.jbzc == 6\">中级</span> <span\r\n                  v-if=\"tjlist.jbzc == 7\">初级及以下</span> <span v-if=\"tjlist.jbzc == 8\">试用期人员</span>\r\n                <span v-if=\"tjlist.jbzc == 9\">工勤人员</span> <span v-if=\"tjlist.jbzc == 10\">企业职员</span> <span\r\n                  v-if=\"tjlist.jbzc == 11\">其他</span>\r\n              </p>\r\n            </template>\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"sec-form-second\">\r\n          <el-form-item label=\"已（拟）任涉密岗位\">\r\n            <template slot-scope=\"scope\">\r\n              <p class=\"hyzk\" v-if=\"tjlist.gwmc && tjlist.gwmc.length === 1\"> {{ tjlist.gwmc.toString() }}</p>\r\n              <p class=\"hyzk\" v-else-if=\"tjlist.gwmc && tjlist.gwmc.length > 1\"> {{ tjlist.gwmc.join('/') }} </p>\r\n            </template>\r\n          </el-form-item>\r\n          <el-form-item label=\"涉密等级\">\r\n            <!-- <el-input placeholder=\"\" v-model=\"tjlist.smdj\" clearable disabled></el-input> -->\r\n            <template slot-scope=\"scope\">\r\n              <p class=\"hyzk\" v-if=\"tjlist.smdj == 1\">核心</p>\r\n              <p class=\"hyzk\" v-if=\"tjlist.smdj == 2\">重要</p>\r\n              <p class=\"hyzk\" v-if=\"tjlist.smdj == 3\">一般</p>\r\n            </template>\r\n          </el-form-item>\r\n        </div>\r\n        <!-- 单位及职务、职称到涉密等级end -->\r\n        <!-- 主要学习及工作经历start -->\r\n        <p class=\"sec-title\">主要学习及工作经历</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscScjlList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"qssj\" label=\"起始日期\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.qssj\" placeholder=\"\"></el-input> -->\r\n              <el-date-picker v-model=\"scope.row.qssj\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zzsj\" label=\"终止日期\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.zzsj\" placeholder=\"\"></el-input> -->\r\n              <el-date-picker v-model=\"scope.row.zzsj\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"szdw\" label=\"主要学习经历、工作单位\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.szdw\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zw\" label=\"任职情况\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.zw\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zmr\" label=\"证明人\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.zmr\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.czbtn1 != ''\" size=\"medium\" type=\"text\" @click=\"addRow(ryglRyscScjlList)\">{{\r\n                scope.row.czbtn1 }}\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.czbtn2 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"delRow(scope.$index, ryglRyscScjlList)\">{{ scope.row.czbtn2 }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 主要学习及工作经历end -->\r\n        <!-- 家庭成员及主要社会关系情况start -->\r\n        <p class=\"sec-title\">家庭成员及主要社会关系情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscJtcyList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"gxms\" label=\"与本人关系\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.gxms\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"xm\" label=\"姓名\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.xm\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"jwjlqk\" label=\"是否有外籍、境外居留权、长期居留许可\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.jwjlqk\" placeholder=\"\"></el-input> -->\r\n              <el-select v-model=\"scope.row.jwjlqk\" placeholder=\"请选择\">\r\n                <el-option v-for=\"item in ynoptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"cgszd\" label=\"单位\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.cgszd\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zw\" label=\"职务\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.zw\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zzmm\" label=\"政治面貌\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.zzmm\" placeholder=\"请选择\">\r\n                <el-option v-for=\"item in zzmmoptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.czbtn1 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"cyjshgxAddRow(ryglRyscJtcyList)\">{{ scope.row.czbtn1 }}\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.czbtn2 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"cyjshgxDelRow(scope.$index, ryglRyscJtcyList)\">{{ scope.row.czbtn2 }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 家庭成员及主要社会关系情况end -->\r\n        <!-- 移居国(境)外情况start -->\r\n        <p class=\"sec-title\">移居国(境)外情况</p>\r\n        <div class=\"sec-form-second haveBorderTop longLabel\">\r\n          <el-form-item label=\"拥有外籍、境外永久居留权或者长期居留许可情况\">\r\n            <el-radio v-model=\"tjlist.yjqk\" label=\"1\">有</el-radio>\r\n            <el-radio v-model=\"tjlist.yjqk\" label=\"0\">无</el-radio>\r\n          </el-form-item>\r\n        </div>\r\n        <!-- 移居国(境)外情况end -->\r\n        <!-- 持有因公出入境证件情况start -->\r\n        <p class=\"sec-title\">持有因公出入境证件情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscSwzjList.slice(0, 3)\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"zjmc\" label=\"证件名称\"></el-table-column>\r\n          <el-table-column prop=\"cyqk\" label=\"持有情况\">\r\n            <template slot-scope=\"scope\">\r\n              <el-radio v-model=\"scope.row.cyqk\" label=\"1\">有</el-radio>\r\n              <el-radio v-model=\"scope.row.cyqk\" label=\"0\">无</el-radio>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zjhm\" label=\"证件号码\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.zjhm\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"yxq\" label=\"有效期\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.yxq\" placeholder=\"\"></el-input> -->\r\n              <el-date-picker v-model=\"scope.row.yxq\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 持有因公出入境证件情况end -->\r\n        <!-- 持有因私出入境证件情况start -->\r\n        <p class=\"sec-title\">持有因私出入境证件情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscSwzjList.slice(3, 6)\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"zjmc\" label=\"证件名称\"></el-table-column>\r\n          <el-table-column prop=\"cyqk\" label=\"持有情况\">\r\n            <template slot-scope=\"scope\">\r\n              <el-radio v-model=\"scope.row.cyqk\" label=\"1\">有</el-radio>\r\n              <el-radio v-model=\"scope.row.cyqk\" label=\"0\">无</el-radio>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zjhm\" label=\"证件号码\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.zjhm\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"yxq\" label=\"有效期\" value-format=\"yyyy-MM-dd\">\r\n            <template slot-scope=\"scope\">\r\n              <el-date-picker v-model=\"scope.row.yxq\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 持有因私出入境证件情况end -->\r\n        <!-- 因私出国(境)情况start -->\r\n        <p class=\"sec-title\">因私出国(境)情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscYccgList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"qssj\" label=\"起始日期\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.qssj\" placeholder=\"\"></el-input> -->\r\n              <el-date-picker v-model=\"scope.row.qssj\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zzsj\" label=\"终止日期\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.zzsj\" placeholder=\"\"></el-input> -->\r\n              <el-date-picker v-model=\"scope.row.zzsj\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"cggj\" label=\"近3年所到国家或地区\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.cggj\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"sy\" label=\"事由\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.sy\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.czbtn1 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"yscgqkAddRow(ryglRyscYccgList)\">{{ scope.row.czbtn1 }}\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.czbtn2 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"yscgqkDelRow(scope.$index, ryglRyscYccgList)\">{{ scope.row.czbtn2 }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 因私出国(境)情况end -->\r\n        <!-- 接受境外资助情况start -->\r\n        <p class=\"sec-title\">接受境外资助情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscJwzzqkList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"zzsj\" label=\"起始日期\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.zzsj\" placeholder=\"\"></el-input> -->\r\n              <el-date-picker v-model=\"scope.row.zzsj\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"gj\" label=\"国家地区\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.gj\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"jgmc\" label=\"机构名称\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.jgmc\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"zznr\" label=\"资助内容\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.zznr\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.czbtn1 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"jsjwzzqkAddRow(ryglRyscJwzzqkList)\">{{ scope.row.czbtn1 }}\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.czbtn2 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"jsjwzzqkDelRow(scope.$index, ryglRyscJwzzqkList)\">{{ scope.row.czbtn2 }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 接受境外资助情况end -->\r\n        <!-- 处分或者违法犯罪情况start -->\r\n        <p class=\"sec-title\">处分或者违法犯罪情况</p>\r\n        <el-table border class=\"sec-el-table\" :data=\"ryglRyscCfjlList\"\r\n          :header-cell-style=\"{ background: '#EEF7FF', color: '#4D91F8' }\" stripe>\r\n          <el-table-column type=\"index\" width=\"60\" label=\"序号\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"cfsj\" label=\"起始日期\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-input v-model=\"scope.row.cfsj\" placeholder=\"\"></el-input> -->\r\n              <el-date-picker v-model=\"scope.row.cfsj\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"cfjg\" label=\"处理结果\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.cfjg\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"cfyy\" label=\"处理原因\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.cfyy\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"cfdw\" label=\"处理机构\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.cfdw\" placeholder=\"\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-if=\"scope.row.czbtn1 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"clhwffzqkAddRow(ryglRyscCfjlList)\">{{ scope.row.czbtn1 }}\r\n              </el-button>\r\n              <el-button v-if=\"scope.row.czbtn2 != ''\" size=\"medium\" type=\"text\"\r\n                @click=\"clhwffzqkDelRow(scope.$index, ryglRyscCfjlList)\">{{ scope.row.czbtn2 }}\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 处分或者违法犯罪情况end -->\r\n        <!-- 配偶子女有关情况start -->\r\n        <p class=\"sec-title\">配偶子女有关情况</p>\r\n        <div class=\"sec-form-third haveBorderTop\">\r\n          <div class=\"sec-left-text\">\r\n            <p>1.在国境内外从事反对、攻击党和国家或者颠覆国家政权活动</p>\r\n            <p>2.被列为影响国家安全重点管控人员</p>\r\n            <p>3.因危害国家安全的行为收到处分或者处罚</p>\r\n          </div>\r\n          <el-form-item label=\"\">\r\n            <el-radio v-model=\"tjlist.qscfqk\" label=\"1\">有</el-radio>\r\n            <el-radio v-model=\"tjlist.qscfqk\" label=\"0\">无</el-radio>\r\n          </el-form-item>\r\n        </div>\r\n        <!-- 配偶子女有关情况end -->\r\n        <!-- 其他需要说明的情况start -->\r\n        <p class=\"sec-title\">其他需要说明的情况</p>\r\n        <div class=\"sec-form-four haveBorderTop\">\r\n          <el-input type=\"textarea\" autosize placeholder=\"请输入内容\" v-model=\"tjlist.qtqk\">\r\n          </el-input>\r\n        </div>\r\n        <!-- 其他需要说明的情况end -->\r\n        <!-- 本人承诺start -->\r\n        <p class=\"sec-title\">本人承诺</p>\r\n        <div class=\"sec-form-five haveBorderTop\" style=\"position: relative;\">\r\n          <el-upload class=\"upload-demo\" action=\"#\" :http-request=\"httpRequest\" :show-file-list=\"false\">\r\n            <img v-if=\"sltshow\" :src=\"sltshow\" class=\"avatar\">\r\n            <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n          </el-upload>\r\n          <p v-if=\"sltshow\" class=\"yulan\" @click=\"yulan\">预览</p>\r\n          <p v-if=\"sltshow\" class=\"yulan\" @click=\"shanchu\">删除</p>\r\n          <!-- 预览本人承诺扫描件 -->\r\n          <el-dialog :visible.sync=\"dialogVisible\">\r\n            <img :src=\"dialogImageUrl\" alt=\"\" style=\"width: 100%\">\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\r\n            </div>\r\n          </el-dialog>\r\n        </div>\r\n        <!-- 本人承诺end -->\r\n        <!-- 底部操作按钮start -->\r\n        <div class=\"sec-form-six haveBorderTop sec-footer\">\r\n          <el-button @click=\"returnIndex\" class=\"fr ml10\" plain>返回</el-button>\r\n          <el-button @click=\"chooseApproval\" class=\"fr\" type=\"success\">保存并提交</el-button>\r\n          <!-- <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">保存并提交</el-button>\r\n          <el-button @click=\"save\" class=\"fr\" type=\"primary\">保存</el-button> -->\r\n          <el-button @click=\"save\" class=\"fr\" type=\"primary\">临时保存</el-button>\r\n        </div>\r\n        <!-- 底部操作按钮end -->\r\n\r\n      </el-form>\r\n    </div>\r\n    <!-- 发起申请弹框start -->\r\n    <el-dialog title=\"选择审批人\" :close-on-click-modal=\"false\" :visible.sync=\"approvalDialogVisible\" width=\"40%\" :destroy-on-close=\"true\"> \r\n      <div class=\"dlFqsqContainer\">\r\n        <label for=\"\">部门:</label>\r\n        <el-cascader v-model=\"ryChoose.bm\" :options=\"regionOption\" :props=\"regionParams\" filterable clearable\r\n          ref=\"cascaderArr\" @change=\"bmSelectChange\"></el-cascader>\r\n        <label for=\"\">姓名:</label>\r\n        <el-input class=\"input2\" v-model=\"ryChoose.xm\" clearable placeholder=\"姓名\"></el-input>\r\n        <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\" @click=\"searchRy\">查询</el-button>\r\n        <BaseTable class=\"baseTable\" :tableHeight=\"'300'\" :key=\"tableKey\" :showIndex=true :tableData=\"ryDatas\" :columns=\"applyColumns\"\r\n          :showSingleSelection=\"true\" :handleColumn=\"handleColumnApply\" :showPagination=true :currentPage=\"page\"\r\n          :pageSize=\"pageSize\" :totalCount=\"total\" @handleCurrentChange=\"handleCurrentChangeRy\"\r\n          @handleSizeChange=\"handleSizeChangeRy\" @handleSelectionChange=\"handleSelectionChange\">\r\n        </BaseTable>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"warning\" class=\"fr ml10\" @click=\"approvalDialogVisible = false\">关 闭</el-button>\r\n        <el-button @click=\"saveAndSubmit\" class=\"fr\" type=\"success\">提交</el-button>\r\n        <!-- <el-button @click=\"save\" class=\"fr\" type=\"primary\">保存</el-button> -->\r\n        <div style=\"clear:both\"></div>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 发起申请弹框end -->\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  submitZgfs,\r\n  getLcSLid,\r\n  updateZgfs,\r\n  updateSlzt,\r\n  getZzjgList,\r\n  getSpUserList,\r\n  getCurZgfsjl,\r\n  getLoginInfo,\r\n  getFwdyidByFwlx,\r\n  deleteSlxxBySlid\r\n} from '../../../api/index'\r\nimport BaseTable from '../../components/common/baseTable.vue'\r\nimport AddLineTable from \"../../components/common/addLineTable.vue\"; //人工纠错组件\r\nexport default {\r\n  components: {\r\n    AddLineTable,\r\n    BaseTable\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      tableKey:1,\r\n      loading: false,\r\n      // 弹框人员选择条件\r\n      ryChoose: {\r\n        'bm': '',\r\n        'xm': ''\r\n      },\r\n      regionOption: [], // 部门下拉\r\n      page: 1, // 审批人弹框当前页\r\n      pageSize: 10, // 审批人弹框每页条数\r\n      radioIdSelect: '', // 审批人弹框人员单选\r\n      ryDatas: [], // 弹框人员选择\r\n      total: 0, // 弹框人员总数\r\n      regionParams: {\r\n        label: 'label', //这里可以配置你们后端返回的属性\r\n        value: 'label',\r\n        children: 'childrenRegionVo',\r\n        expandTrigger: 'click',\r\n        checkStrictly: true\r\n      }, //地域信息配置参数\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: '#EEF7FF',\r\n        color: '#4D91F8'\r\n      },\r\n      // form表单提交数据\r\n      tjlist: {\r\n        smryid: '',\r\n        xm: '',\r\n        xb: '',\r\n        gj: '中国',\r\n        dwzwzc: '',\r\n        yrsmgw: '',\r\n        cym: '',\r\n        mz: '',\r\n        hyzk: '',\r\n        zzmm: '',\r\n        lxdh: '',\r\n        sfzhm: '',\r\n        hjdz: '',\r\n        hjdgajg: '',\r\n        czdz: '',\r\n        czgajg: '',\r\n        imageUrl: '',\r\n        yjqk: '0', // 拥有外籍、境外永久居留权或者长期居留许可情况\r\n        qscfqk: '0', // 配偶子女有关情况\r\n        qtqk: '', // 其他需要说明的情况\r\n        brcn: ''\r\n      },\r\n      // 主要学习及工作经历\r\n      ryglRyscScjlList: [{\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 家庭成员及社会关系\r\n      ryglRyscJtcyList: [{\r\n        \"gxms\": \"\", //关系描述\r\n        \"zzmm\": \"\", //政治面貌\r\n        \"jwjlqk\": '', //是否有外籍、境外居留权、长期居留许可\r\n        \"xm\": \"\", //姓名\r\n        \"cgszd\": \"\", //工作(学习)单位\r\n        \"zw\": \"\", //职务\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 因私出国(境)情况\r\n      ryglRyscYccgList: [{\r\n        \"cggj\": \"\", //出国国家\r\n        \"sy\": \"\", //事由\r\n        \"zzsj\": \"\", //终止时间\r\n        \"qssj\": \"\", //起始时间\r\n        // \"bz\": \"\",//备注\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 接受境外资助情况\r\n      ryglRyscJwzzqkList: [{\r\n        \"zzsj\": \"\", //时间\r\n        \"jgmc\": \"\", //机构名称\r\n        // \"bz\": \"\",//备注\r\n        \"zznr\": \"\", //资助内容\r\n        \"gj\": \"\", //国家\r\n        'czbtn1': '增加行',\r\n        'czbtn2': ''\r\n      }],\r\n      // 处分或者违法犯罪情况\r\n      ryglRyscCfjlList: [{\r\n        \"cfdw\": \"\", //处罚单位\r\n        \"cfsj\": \"\", //处罚时间\r\n        \"cfjg\": \"\", //处罚结果\r\n        \"cfyy\": \"\", //处罚原因\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '',\r\n      }],\r\n      // 持有因公出入境证件情况\r\n      ryglRyscSwzjList: [{\r\n        'zjmc': '护照',\r\n        'fjlb': 1,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '港澳通行证',\r\n        'fjlb': 2,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '台湾通行证',\r\n        'fjlb': 3,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '护照',\r\n        'fjlb': 4,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '港澳通行证',\r\n        'fjlb': 5,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }, {\r\n        'zjmc': '台湾通行证',\r\n        'fjlb': 6,\r\n        'cyqk': '0',\r\n        'zjhm': '',\r\n        'yxq': ''\r\n      }],\r\n      ryInfo: {},\r\n      // 政治面貌下拉选项\r\n      zzmmoptions: [{\r\n        value: '中央党员',\r\n        label: '中央党员'\r\n      }, {\r\n        value: '团员',\r\n        label: '团员'\r\n      }, {\r\n        value: '民主党派',\r\n        label: '民主党派'\r\n      }, {\r\n        value: '群众',\r\n        label: '群众'\r\n      }],\r\n      ynoptions: [{\r\n        value: '1',\r\n        label: '是'\r\n      }, {\r\n        value: '0',\r\n        label: '否'\r\n      }],\r\n      sltshow: '', // 文档的缩略图显示\r\n      routeType: '',\r\n      pdfBase64: '',\r\n      fileList: [],\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n      approvalDialogVisible: false, // 选择申请人弹框\r\n      fileRow: '',\r\n      // 选择审核人table\r\n      applyColumns: [{\r\n        name: '姓名',\r\n        prop: 'xm',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      },\r\n      {\r\n        name: '部门',\r\n        prop: 'bmmc',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      },\r\n      {\r\n        name: '岗位',\r\n        prop: 'gwmc',\r\n        scopeType: 'text',\r\n        formatter: false\r\n      }\r\n      ],\r\n      handleColumnApply: [],\r\n    }\r\n  },\r\n  computed: {\r\n    // selectedLabel() {\r\n    //   const option = this.ynoptions.find(o => o.value === this.selectedValue);\r\n    //   return option ? option.label : '';\r\n    // }\r\n  },\r\n  mounted() {\r\n    this.onfwid()\r\n    this.getOrganization()\r\n    this.yhDatas = this.$route.query.datas\r\n    this.ryInfo = this.$route.query.datas.zgfs\r\n    this.routeType = this.$route.query.type\r\n    this.routezt = this.$route.query.zt\r\n    console.log(this.yhDatas,'22222222222');\r\n    console.log(this.routezt);\r\n    console.log(this.$route.query.datas.zgfs,'11111111111111111');\r\n    let result = {}\r\n    let iamgeBase64 = ''\r\n    let iamgeBase64Brcn = ''\r\n    if (this.$route.query.type == 'add') {\r\n      // 首次发起申请\r\n      result = {\r\n        ...this.tjlist,\r\n        ...this.$route.query.datas\r\n      }\r\n      iamgeBase64 = \"data:image/jpeg;base64,\" + this.$route.query.datas.zp\r\n    } else {\r\n      // 保存 继续编辑\r\n      result = {\r\n        ...this.tjlist,\r\n        ...this.$route.query.datas.zgfs\r\n      }\r\n      iamgeBase64 = \"data:image/jpeg;base64,\" + this.$route.query.datas.zgfs.zp\r\n      iamgeBase64Brcn = \"data:image/jpeg;base64,\" + this.$route.query.datas.zgfs.brcn\r\n      if (typeof iamgeBase64Brcn === \"string\") {\r\n        // 复制某条消息\r\n        if (!iamgeBase64Brcn) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n        function validDataUrl(s) {\r\n          return validDataUrl.regex.test(s);\r\n        }\r\n        validDataUrl.regex =\r\n          /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n        if (validDataUrl(iamgeBase64Brcn)) {\r\n          let that = this;\r\n\r\n          function previwImg(item) {\r\n            that.sltshow = item;\r\n          }\r\n          previwImg(iamgeBase64Brcn);\r\n        }\r\n      }\r\n      // 主要学习及工作经历\r\n      if (this.$route.query.datas.ryglZgfsScjlList.length == 0) {\r\n        this.ryglRyscScjlList = [{\r\n          'qssj': '',\r\n          'zzsj': '',\r\n          'szdw': '',\r\n          'zw': '',\r\n          'zmr': '',\r\n          'czbtn1': '增加行',\r\n          'czbtn2': ''\r\n        }]\r\n      } else {\r\n        this.ryglRyscScjlList = this.$route.query.datas.ryglZgfsScjlList.map((data) => {\r\n          data.czbtn1 = '增加行'\r\n          data.czbtn2 = '删除'\r\n          return data\r\n        })\r\n      }\r\n      // 家庭成员及主要社会关系情况\r\n      if (this.$route.query.datas.ryglZgfsJtcyList.length == 0) {\r\n        this.ryglRyscJtcyList = [{\r\n          \"gxms\": \"\", //关系描述\r\n          \"zzmm\": \"\", //政治面貌\r\n          \"jwjlqk\": '', //是否有外籍、境外居留权、长期居留许可\r\n          \"xm\": \"\", //姓名\r\n          \"cgszd\": \"\", //工作(学习)单位\r\n          \"zw\": \"\", //职务\r\n          'czbtn1': '增加行',\r\n          'czbtn2': ''\r\n        }]\r\n      } else {\r\n        this.ryglRyscJtcyList = this.$route.query.datas.ryglZgfsJtcyList.map((data) => {\r\n          if (data.jwjlqk == 0) {\r\n            data.jwjlqk = '否'\r\n          } else if (data.jwjlqk == 1) {\r\n            data.jwjlqk = '是'\r\n          }\r\n          data.czbtn1 = '增加行'\r\n          data.czbtn2 = '删除'\r\n          return data\r\n        })\r\n      }\r\n      // 家庭成员及主要社会关系情况\r\n      if (this.$route.query.datas.ryglZgfsYccgList.length == 0) {\r\n        this.ryglRyscYccgList = [{\r\n          \"cggj\": \"\", //出国国家\r\n          \"sy\": \"\", //事由\r\n          \"zzsj\": \"\", //终止时间\r\n          \"qssj\": \"\", //起始时间\r\n          // \"bz\": \"\",//备注\r\n          'czbtn1': '增加行',\r\n          'czbtn2': ''\r\n        }]\r\n      } else {\r\n        this.ryglRyscYccgList = this.$route.query.datas.ryglZgfsYccgList.map((data) => {\r\n          data.czbtn1 = '增加行'\r\n          data.czbtn2 = '删除'\r\n          return data\r\n        })\r\n      }\r\n      // 接受境外资助情况\r\n      if (this.$route.query.datas.ryglZgfsJwzzqkList.length == 0) {\r\n        this.ryglRyscJwzzqkList = [{\r\n          \"zzsj\": \"\", //时间\r\n          \"jgmc\": \"\", //机构名称\r\n          // \"bz\": \"\",//备注\r\n          \"zznr\": \"\", //资助内容\r\n          \"gj\": \"\", //国家\r\n          'czbtn1': '增加行',\r\n          'czbtn2': ''\r\n        }]\r\n      } else {\r\n        this.ryglRyscJwzzqkList = this.$route.query.datas.ryglZgfsJwzzqkList.map((data) => {\r\n          data.czbtn1 = '增加行'\r\n          data.czbtn2 = '删除'\r\n          return data\r\n        })\r\n      }\r\n      // 处分或者违法犯罪情况\r\n      if (this.$route.query.datas.ryglZgfsCfjlList.length == 0) {\r\n        this.ryglRyscCfjlList = [{\r\n          \"cfdw\": \"\", //处罚单位\r\n          \"cfsj\": \"\", //处罚时间\r\n          \"cfjg\": \"\", //处罚结果\r\n          \"cfyy\": \"\", //处罚原因\r\n          'czbtn1': '增加行',\r\n          'czbtn2': ''\r\n        }]\r\n      } else {\r\n        this.ryglRyscCfjlList = this.$route.query.datas.ryglZgfsCfjlList.map((data) => {\r\n          data.czbtn1 = '增加行'\r\n          data.czbtn2 = '删除'\r\n          return data\r\n        })\r\n      }\r\n      // 出入境证件情况\r\n      if (this.$route.query.datas.ryglZgfsSwzjList.length > 0) {\r\n        this.ryglRyscSwzjList = this.$route.query.datas.ryglZgfsSwzjList.map((data) => {\r\n          return data\r\n        })\r\n      }\r\n      this.ryglRyscSwzjList[0].zjmc = '护照'\r\n      this.ryglRyscSwzjList[1].zjmc = '港澳通行证'\r\n      this.ryglRyscSwzjList[2].zjmc = '台湾通行证'\r\n      this.ryglRyscSwzjList[3].zjmc = '护照'\r\n      this.ryglRyscSwzjList[4].zjmc = '港澳通行证'\r\n      this.ryglRyscSwzjList[5].zjmc = '台湾通行证'\r\n    }\r\n    this.tjlist = result\r\n    this.tjlist.yjqk = result.yjqk.toString()\r\n    this.tjlist.qscfqk = result.qscfqk.toString()\r\n    if (typeof iamgeBase64 === \"string\") {\r\n      // 复制某条消息\r\n      if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64\r\n      function validDataUrl(s) {\r\n        return validDataUrl.regex.test(s);\r\n      }\r\n      validDataUrl.regex =\r\n        /^\\s*data:([a-z]+\\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s]*?)\\s*$/i; // 如果是base64转换成图片预览\r\n      if (validDataUrl(iamgeBase64)) {\r\n        let that = this;\r\n\r\n        function previwImg(item) {\r\n          that.tjlist.imageUrl = item;\r\n        }\r\n        previwImg(iamgeBase64);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    blobToBase64(blob, callback) {\r\n      const fileReader = new FileReader();\r\n      fileReader.onload = (e) => {\r\n        callback(e.target.result);\r\n      };\r\n      fileReader.readAsDataURL(blob);\r\n    },\r\n    handleSelectionChange(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    // 主要学习及工作经历增加行\r\n    addRow(data) {\r\n      data.push({\r\n        'qssj': '',\r\n        'zzsj': '',\r\n        'szdw': '',\r\n        'zw': '',\r\n        'zmr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除',\r\n      })\r\n    },\r\n    // 主要学习及工作经历删除行\r\n    delRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 家庭成员及主要社会关系情况 添加行\r\n    cyjshgxAddRow(data) {\r\n      data.push({\r\n        'ybrgx': '',\r\n        'xm': '',\r\n        'sfywjjwjlqcqjlxk': '',\r\n        'dw': '',\r\n        'zw': '',\r\n        'zzmm': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 家庭成员及主要社会关系情况 删除行\r\n    cyjshgxDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 因私出国(境)情况 添加行\r\n    yscgqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'zzrq': '',\r\n        'jsnsdgjhdq': '',\r\n        'sy': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 因私出国(境)情况 删除行\r\n    yscgqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 接受境外资助情况 添加行\r\n    jsjwzzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'gjdq': '',\r\n        'jgmc': '',\r\n        'zznr': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 接受境外资助情况 删除行\r\n    jsjwzzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 处分或者违法犯罪情况 添加行\r\n    clhwffzqkAddRow(data) {\r\n      data.push({\r\n        'qsrq': '',\r\n        'cljg': '',\r\n        'clyy': '',\r\n        'cljg': '',\r\n        'czbtn1': '增加行',\r\n        'czbtn2': '删除'\r\n      })\r\n    },\r\n    // 处分或者违法犯罪情况 删除行\r\n    clhwffzqkDelRow(index, rows) {\r\n      rows.splice(index, 1)\r\n    },\r\n    // 上传本人承诺凭证\r\n    httpRequest(data) {\r\n      this.sltshow = URL.createObjectURL(data.file);\r\n      this.fileRow = data.file\r\n      // this.tjlist.brcn = URL.createObjectURL(this.fileRow)\r\n      this.blobToBase64(data.file, (dataurl) => {\r\n        this.tjlist.brcn = dataurl.split(',')[1]\r\n      });\r\n    },\r\n    // 预览\r\n    yulan() {\r\n      console.log(this.routeType)\r\n      if (this.routeType == 'add') {\r\n        this.dialogImageUrl = URL.createObjectURL(this.fileRow)\r\n      } else {\r\n        this.dialogImageUrl = this.sltshow\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除\r\n    shanchu() {\r\n      this.tjlist.brcn = ''\r\n      this.sltshow = ''\r\n    },\r\n    async onfwid() {\r\n      let params = {\r\n        fwlx: 2\r\n      }\r\n      let data = await getFwdyidByFwlx(params)\r\n      console.log(data);\r\n      this.fwdyid = data.data.fwdyid\r\n    },\r\n    // 保存\r\n    async save() {\r\n      let param = {\r\n        'fwdyid': this.fwdyid,\r\n        'lcslclzt': 3\r\n      }\r\n      this.ryglRyscJtcyList.forEach((e) => {\r\n        if (e.jwjlqk == '否') {\r\n          e.jwjlqk = 0\r\n        } else if (e.jwjlqk == '是') {\r\n          e.jwjlqk = 1\r\n        }\r\n      })\r\n      if (this.routeType == 'update') {\r\n        this.tjlist.dwid = this.ryInfo.dwid\r\n        this.tjlist.lcslid = this.ryInfo.lcslid\r\n        let params = {\r\n          'zgfs': this.tjlist,\r\n          'ryglZgfsScjlList': this.ryglRyscScjlList,\r\n          'ryglZgfsJtcyList': this.ryglRyscJtcyList,\r\n          'ryglZgfsYccgList': this.ryglRyscYccgList,\r\n          'ryglZgfsJwzzqkList': this.ryglRyscJwzzqkList,\r\n          'ryglZgfsCfjlList': this.ryglRyscCfjlList,\r\n          'ryglZgfsSwzjList': this.ryglRyscSwzjList\r\n        }\r\n        let resDatas\r\n        if (this.routezt == undefined) {\r\n          resDatas = await updateZgfs(params)\r\n        } else if (this.routezt == 1) {\r\n          resDatas = await submitZgfs(params)\r\n        }\r\n        if (resDatas.code == 10000) {\r\n          this.$router.push('/zgfs')\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n        }\r\n      } else {\r\n        param.smryid = this.yhDatas.smryid\r\n        this.tjlist.dwid = this.yhDatas.dwid\r\n        let res = await getLcSLid(param)\r\n        if (res.code == 10000) {\r\n          this.tjlist.lcslid = res.data.slid\r\n          let params = {\r\n            'zgfs': this.tjlist,\r\n            'ryglZgfsScjlList': this.ryglRyscScjlList,\r\n            'ryglZgfsJtcyList': this.ryglRyscJtcyList,\r\n            'ryglZgfsYccgList': this.ryglRyscYccgList,\r\n            'ryglZgfsJwzzqkList': this.ryglRyscJwzzqkList,\r\n            'ryglZgfsCfjlList': this.ryglRyscCfjlList,\r\n            'ryglZgfsSwzjList': this.ryglRyscSwzjList\r\n          }\r\n          let resDatas = await submitZgfs(params)\r\n          if (resDatas.code == 10000) {\r\n            this.$router.push('/zgfs')\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n          }else {\r\n          deleteSlxxBySlid({ slid: res.data.slid })\r\n        }\r\n        } \r\n      }\r\n    },\r\n    //全部组织机构List\r\n    async getOrganization() {\r\n      let zzjgList = await getZzjgList()\r\n      this.zzjgmc = zzjgList\r\n      let shu = []\r\n      this.zzjgmc.forEach(item => {\r\n        let childrenRegionVo = []\r\n        this.zzjgmc.forEach(item1 => {\r\n          if (item.bmm == item1.fbmm) {\r\n            childrenRegionVo.push(item1)\r\n            item.childrenRegionVo = childrenRegionVo\r\n          }\r\n        });\r\n        shu.push(item)\r\n      })\r\n      let shuList = []\r\n      let list = await getLoginInfo()\r\n      if (list.fbmm == '') {\r\n        shu.forEach(item => {\r\n          if (item.fbmm == '') {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      if (list.fbmm != '') {\r\n        shu.forEach(item => {\r\n          console.log(item);\r\n          if (item.fbmm == list.fbmm) {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      shuList[0].childrenRegionVo.forEach(item => {\r\n        this.regionOption.push(item)\r\n      })\r\n      console.log(this.regionOption);\r\n    },\r\n    handleSelectionChange1(index, row) {\r\n      this.radioIdSelect = row\r\n    },\r\n    handleCurrentChangeRy(val) {\r\n      this.page = val\r\n      this.chooseApproval()\r\n    },\r\n    //列表分页--更改每页显示个数\r\n    handleSizeChangeRy(val) {\r\n      this.page = 1\r\n      this.pageSize = val\r\n      this.chooseApproval()\r\n    },\r\n    // 人员搜索\r\n    searchRy() {\r\n      this.tableKey++\r\n      this.chooseApproval()\r\n    },\r\n    // 发起申请选择人员 人员下拉\r\n    bmSelectChange(item) {\r\n      if (item != undefined) {\r\n        this.ryChoose.bm = item.join('/')\r\n      }\r\n    },\r\n    // 选择审批人\r\n    async chooseApproval() {\r\n     // this.getOrganization()\r\n      this.approvalDialogVisible = true\r\n      let param = {\r\n        'page': this.page,\r\n        'pageSize': this.pageSize,\r\n        'fwdyid': this.fwdyid,\r\n        'bmmc': this.ryChoose.bm,\r\n        'xm': this.ryChoose.xm\r\n      }\r\n      let resData = await getSpUserList(param)\r\n      if (resData.records) {\r\n        // this.loading = false\r\n        this.ryDatas = resData.records\r\n        this.total = resData.total\r\n      } else {\r\n        this.$message.error('数据获取失败！')\r\n      }\r\n    },\r\n    // 保存并提交\r\n    async saveAndSubmit() {\r\n      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {\r\n        let param = {\r\n          'fwdyid': this.fwdyid\r\n        }\r\n        this.ryglRyscJtcyList.forEach((e) => {\r\n          if (e.jwjlqk == '否') {\r\n            e.jwjlqk = 0\r\n          } else if (e.jwjlqk == '是') {\r\n            e.jwjlqk = 1\r\n          }\r\n        })\r\n        if (this.routeType == 'update' && this.routezt == undefined) {\r\n          param.lcslclzt = 2\r\n          param.smryid = this.ryInfo.smryid\r\n          param.slid = this.ryInfo.lcslid\r\n          param.clrid = this.radioIdSelect.yhid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = this.ryInfo.dwid\r\n            this.tjlist.lcslid = this.ryInfo.lcslid\r\n            let params = {\r\n              'zgfs': this.tjlist,\r\n              'ryglZgfsScjlList': this.ryglRyscScjlList,\r\n              'ryglZgfsJtcyList': this.ryglRyscJtcyList,\r\n              'ryglZgfsYccgList': this.ryglRyscYccgList,\r\n              'ryglZgfsJwzzqkList': this.ryglRyscJwzzqkList,\r\n              'ryglZgfsCfjlList': this.ryglRyscCfjlList,\r\n              'ryglZgfsSwzjList': this.ryglRyscSwzjList\r\n            }\r\n            let resDatas = await updateZgfs(params)\r\n            if (resDatas.code == 10000) {\r\n              let paramStatus = {\r\n                'fwdyid': this.fwdyid,\r\n                'slid': this.tjlist.lcslid\r\n              }\r\n              let resStatus\r\n\r\n              resStatus = await updateSlzt(paramStatus)\r\n\r\n\r\n              if (resStatus.code == 10000) {\r\n                this.$router.push('/zgfs')\r\n                this.$message({\r\n                  message: '保存并提交成功',\r\n                  type: 'success'\r\n                })\r\n              }\r\n            }\r\n          }\r\n        } else {\r\n          console.log(this.ryInfo);\r\n          let smryid = ''\r\n          let dwid = ''\r\n          if(this.ryInfo){\r\n            smryid = this.ryInfo.smryid\r\n            dwid = this.ryInfo.dwid\r\n          }else{\r\n            smryid = this.yhDatas.smryid\r\n            dwid = this.yhDatas.dwid\r\n          }\r\n          console.log(this.$route.query.datas);\r\n          param.lcslclzt = 0\r\n          param.clrid = this.radioIdSelect.yhid\r\n          param.smryid = smryid\r\n          let res = await getLcSLid(param)\r\n          if (res.code == 10000) {\r\n            this.tjlist.dwid = dwid\r\n            this.tjlist.lcslid = res.data.slid\r\n            this.tjlist.pxqk = ''\r\n            this.tjlist.cnsqk = ''\r\n            this.tjlist.xysqk = ''\r\n            this.tjlist.rlspr = ''\r\n            this.tjlist.cnsrq = ''\r\n            this.tjlist.bmsc = ''\r\n            this.tjlist.bmspr = ''\r\n            this.tjlist.bmscrq = ''\r\n            this.tjlist.rlsc = ''\r\n            this.tjlist.rlldspr = ''\r\n            this.tjlist.rlscrq = ''\r\n            this.tjlist.bmbsc = ''\r\n            this.tjlist.bmbldspr = ''\r\n            this.tjlist.bmbscrq = ''\r\n            let params = {\r\n              'zgfs': this.tjlist,\r\n              'ryglZgfsScjlList': this.ryglRyscScjlList,\r\n              'ryglZgfsJtcyList': this.ryglRyscJtcyList,\r\n              'ryglZgfsYccgList': this.ryglRyscYccgList,\r\n              'ryglZgfsJwzzqkList': this.ryglRyscJwzzqkList,\r\n              'ryglZgfsCfjlList': this.ryglRyscCfjlList,\r\n              'ryglZgfsSwzjList': this.ryglRyscSwzjList\r\n            }\r\n            let resDatas = await submitZgfs(params)\r\n            if (resDatas.code == 10000) {\r\n              this.$router.push('/zgfs')\r\n              this.$message({\r\n                message: '保存并提交成功',\r\n                type: 'success'\r\n              })\r\n            }else{\r\n            deleteSlxxBySlid({slid:res.data.slid})\r\n          }\r\n          }\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: '请选择审批人',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    // 返回\r\n    returnIndex() {\r\n      this.$router.push('/zgfs')\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.sec-container {\r\n  width: 100%;\r\n  height: calc(100% - 50px);\r\n  overflow-y: overlay;\r\n}\r\n\r\n.sec-title {\r\n  border-left: 5px solid #1b72d8;\r\n  color: #1b72d8;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  text-indent: 10px;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.sec-form-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.sec-form-left {\r\n  width: calc(100% - 260px);\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n}\r\n\r\n.sec-form-left:not(:first-child) {\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-left .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-header-section {\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.sec-header-pic {\r\n  width: 258px;\r\n  position: absolute;\r\n  right: 0px;\r\n  top: 0;\r\n  height: 245px;\r\n  border: 1px solid #CDD2D9;\r\n  border-left: 0;\r\n  background: #ffffff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.sec-form-second {\r\n  /* width: 100%; */\r\n  border: 1px solid #CDD2D9;\r\n  height: 40px;\r\n  display: flex;\r\n  justify-content: space-evenly;\r\n  overflow: hidden;\r\n  border-right: 0px;\r\n  border-top: 0;\r\n}\r\n\r\n.sec-form-third {\r\n  border: 1px solid #CDD2D9;\r\n  /* height: 40px;  */\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-four {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  /* display: flex;\r\n  justify-content: space-evenly; */\r\n  overflow: hidden;\r\n  /* border-right: 0px; */\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.sec-form-five {\r\n  border: 1px solid #CDD2D9;\r\n  height: auto;\r\n  min-height: 100px;\r\n  overflow: hidden;\r\n  border-top: 0;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.yulan {\r\n  text-align: center;\r\n  cursor: pointer;\r\n  color: #3874D5;\r\n  font-weight: 600;\r\n  float: left;\r\n  margin-left: 10px;\r\n}\r\n\r\n.avatar {\r\n  width: 178px;\r\n  height: 178px;\r\n  display: block;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 178px;\r\n  height: 178px;\r\n  line-height: 178px;\r\n  text-align: center;\r\n  border: 2px solid #EBEBEB;\r\n}\r\n\r\n.sec-form-six {\r\n  border: 1px solid #CDD2D9;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  padding: 10px;\r\n}\r\n\r\n.ml10 {\r\n  margin-left: 10px;\r\n}\r\n\r\n.sec-footer {\r\n  margin-top: 10px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n>>>.sec-form-four .el-textarea__inner {\r\n  border: none;\r\n}\r\n\r\n.sec-left-text {\r\n  float: left;\r\n  margin-right: 130px;\r\n}\r\n\r\n.haveBorderTop {\r\n  border-top: 1px solid #CDD2D9;\r\n}\r\n\r\n>>>.longLabel .el-form-item__label {\r\n  width: 500px !important;\r\n}\r\n\r\n>>>.longLabel .el-form-item__content {\r\n  margin-left: 500px !important;\r\n  padding-left: 20px;\r\n  border-right: 1px solid #CDD2D9;\r\n  background: #ffffff;\r\n}\r\n\r\n/* .sec-form-second:not(:first-child){\r\n  border-top: 0;\r\n} */\r\n.sec-form-second .el-form-item {\r\n  float: left;\r\n  width: 100%;\r\n}\r\n\r\n.sec-el-table {\r\n  width: 100%;\r\n  border: 1px solid #EBEEF5;\r\n  height: calc(100% - 34px - 44px - 10px);\r\n}\r\n\r\n.hyzk {\r\n  padding-left: 15px;\r\n  background-color: #F5F7FA;\r\n  width: calc(100% - 16px);\r\n  border-right: 1px solid #CDD2D9;\r\n  color: #C0C4CC;\r\n}\r\n\r\n>>>.sec-el-table .el-input__inner {\r\n  border: none !important;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item__label {\r\n  width: 200px;\r\n  text-align: center;\r\n  font-size: 16px;\r\n}\r\n\r\n>>>.sec-form-container .el-input__inner {\r\n  border: none;\r\n  border-right: 1px solid #CDD2D9;\r\n  border-radius: 0;\r\n}\r\n\r\n>>>.sec-form-container .el-form-item {\r\n  margin-bottom: 0px;\r\n}\r\n\r\n/* >>>.el-form > div {\r\n  border: 1px solid #CDD2D9;;\r\n} */\r\n>>>.el-form-item__label {\r\n  border-right: 1px solid #CDD2D9;\r\n}\r\n\r\n/* /deep/.sec-form-container .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n} */\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/zgfcTable.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"sec-container\"},[_c('p',{staticClass:\"sec-title\"},[_vm._v(\"基本信息\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-container\"},[_c('el-form',{ref:\"formName\",attrs:{\"model\":_vm.tjlist,\"label-width\":\"225px\"}},[_c('div',{staticClass:\"sec-header-section\"},[_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"姓名\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.xm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"xm\", $$v)},expression:\"tjlist.xm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"性别\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(_vm.tjlist.xb == 2)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"女\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.xb == 1)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"男\")]):_vm._e()]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"国籍\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\"},model:{value:(_vm.tjlist.gj),callback:function ($$v) {_vm.$set(_vm.tjlist, \"gj\", $$v)},expression:\"tjlist.gj\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"曾用名\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.cym),callback:function ($$v) {_vm.$set(_vm.tjlist, \"cym\", $$v)},expression:\"tjlist.cym\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"民族\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.mz),callback:function ($$v) {_vm.$set(_vm.tjlist, \"mz\", $$v)},expression:\"tjlist.mz\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"婚姻状况\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(_vm.tjlist.hyzk == 0)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"未婚\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.hyzk == 1)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"已婚\")]):_vm._e()]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"政治面貌\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(_vm.tjlist.zzmm == 1)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"中共党员\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.zzmm == 2)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"团员\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.zzmm == 3)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"民主党派\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.zzmm == 4)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"群众\")]):_vm._e()]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"联系电话\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.lxdh),callback:function ($$v) {_vm.$set(_vm.tjlist, \"lxdh\", $$v)},expression:\"tjlist.lxdh\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"身份证号\"}},[_c('el-input',{attrs:{\"placeholder\":\"\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.sfzhm),callback:function ($$v) {_vm.$set(_vm.tjlist, \"sfzhm\", $$v)},expression:\"tjlist.sfzhm\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"户籍地址\"}},[_c('el-input',{attrs:{\"placeholder\":\"（详细）\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.hjdz),callback:function ($$v) {_vm.$set(_vm.tjlist, \"hjdz\", $$v)},expression:\"tjlist.hjdz\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"户籍地公安机关\"}},[_c('el-input',{attrs:{\"placeholder\":\"街道派出所\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.hjdgajg),callback:function ($$v) {_vm.$set(_vm.tjlist, \"hjdgajg\", $$v)},expression:\"tjlist.hjdgajg\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-left\"},[_c('el-form-item',{attrs:{\"label\":\"常住地址\"}},[_c('el-input',{attrs:{\"placeholder\":\"（详细）\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.czdz),callback:function ($$v) {_vm.$set(_vm.tjlist, \"czdz\", $$v)},expression:\"tjlist.czdz\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"常住地公安机关\"}},[_c('el-input',{attrs:{\"placeholder\":\"街道派出所\",\"clearable\":\"\",\"disabled\":\"\"},model:{value:(_vm.tjlist.czgajg),callback:function ($$v) {_vm.$set(_vm.tjlist, \"czgajg\", $$v)},expression:\"tjlist.czgajg\"}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-header-pic\"},[_c('div',[(_vm.tjlist.imageUrl)?_c('img',{staticClass:\"avatar\",attrs:{\"src\":_vm.tjlist.imageUrl}}):_vm._e()])])]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second\"},[_c('el-form-item',{attrs:{\"label\":\"部门及职务、职称\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('p',{staticClass:\"hyzk\"},[(_vm.tjlist.bmmc)?_c('span',[_vm._v(_vm._s(_vm.tjlist.bmmc)+\"、\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.zw)?_c('span',[_vm._v(_vm._s(_vm.tjlist.zw)+\"、\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 1)?_c('span',[_vm._v(\"省部级\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 2)?_c('span',[_vm._v(\"厅局级\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 3)?_c('span',[_vm._v(\"县处级\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 4)?_c('span',[_vm._v(\"乡科级及以下\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 5)?_c('span',[_vm._v(\"高级(含正高、副高)\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 6)?_c('span',[_vm._v(\"中级\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 7)?_c('span',[_vm._v(\"初级及以下\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 8)?_c('span',[_vm._v(\"试用期人员\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 9)?_c('span',[_vm._v(\"工勤人员\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 10)?_c('span',[_vm._v(\"企业职员\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.jbzc == 11)?_c('span',[_vm._v(\"其他\")]):_vm._e()])]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second\"},[_c('el-form-item',{attrs:{\"label\":\"已（拟）任涉密岗位\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(_vm.tjlist.gwmc && _vm.tjlist.gwmc.length === 1)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\" \"+_vm._s(_vm.tjlist.gwmc.toString()))]):(_vm.tjlist.gwmc && _vm.tjlist.gwmc.length > 1)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\" \"+_vm._s(_vm.tjlist.gwmc.join('/'))+\" \")]):_vm._e()]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"涉密等级\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(_vm.tjlist.smdj == 1)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"核心\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.smdj == 2)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"重要\")]):_vm._e(),_vm._v(\" \"),(_vm.tjlist.smdj == 3)?_c('p',{staticClass:\"hyzk\"},[_vm._v(\"一般\")]):_vm._e()]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"主要学习及工作经历\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscScjlList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"qssj\",\"label\":\"起始日期\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(scope.row.qssj),callback:function ($$v) {_vm.$set(scope.row, \"qssj\", $$v)},expression:\"scope.row.qssj\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzsj\",\"label\":\"终止日期\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(scope.row.zzsj),callback:function ($$v) {_vm.$set(scope.row, \"zzsj\", $$v)},expression:\"scope.row.zzsj\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"szdw\",\"label\":\"主要学习经历、工作单位\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.szdw),callback:function ($$v) {_vm.$set(scope.row, \"szdw\", $$v)},expression:\"scope.row.szdw\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zw\",\"label\":\"任职情况\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.zw),callback:function ($$v) {_vm.$set(scope.row, \"zw\", $$v)},expression:\"scope.row.zw\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zmr\",\"label\":\"证明人\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.zmr),callback:function ($$v) {_vm.$set(scope.row, \"zmr\", $$v)},expression:\"scope.row.zmr\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"140\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.czbtn1 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.addRow(_vm.ryglRyscScjlList)}}},[_vm._v(_vm._s(scope.row.czbtn1)+\"\\n            \")]):_vm._e(),_vm._v(\" \"),(scope.row.czbtn2 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.delRow(scope.$index, _vm.ryglRyscScjlList)}}},[_vm._v(_vm._s(scope.row.czbtn2)+\"\\n            \")]):_vm._e()]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"家庭成员及主要社会关系情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscJtcyList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gxms\",\"label\":\"与本人关系\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.gxms),callback:function ($$v) {_vm.$set(scope.row, \"gxms\", $$v)},expression:\"scope.row.gxms\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xm\",\"label\":\"姓名\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.xm),callback:function ($$v) {_vm.$set(scope.row, \"xm\", $$v)},expression:\"scope.row.xm\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"jwjlqk\",\"label\":\"是否有外籍、境外居留权、长期居留许可\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-select',{attrs:{\"placeholder\":\"请选择\"},model:{value:(scope.row.jwjlqk),callback:function ($$v) {_vm.$set(scope.row, \"jwjlqk\", $$v)},expression:\"scope.row.jwjlqk\"}},_vm._l((_vm.ynoptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cgszd\",\"label\":\"单位\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.cgszd),callback:function ($$v) {_vm.$set(scope.row, \"cgszd\", $$v)},expression:\"scope.row.cgszd\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zw\",\"label\":\"职务\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.zw),callback:function ($$v) {_vm.$set(scope.row, \"zw\", $$v)},expression:\"scope.row.zw\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzmm\",\"label\":\"政治面貌\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-select',{attrs:{\"placeholder\":\"请选择\"},model:{value:(scope.row.zzmm),callback:function ($$v) {_vm.$set(scope.row, \"zzmm\", $$v)},expression:\"scope.row.zzmm\"}},_vm._l((_vm.zzmmoptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"140\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.czbtn1 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.cyjshgxAddRow(_vm.ryglRyscJtcyList)}}},[_vm._v(_vm._s(scope.row.czbtn1)+\"\\n            \")]):_vm._e(),_vm._v(\" \"),(scope.row.czbtn2 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.cyjshgxDelRow(scope.$index, _vm.ryglRyscJtcyList)}}},[_vm._v(_vm._s(scope.row.czbtn2)+\"\\n            \")]):_vm._e()]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"移居国(境)外情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-second haveBorderTop longLabel\"},[_c('el-form-item',{attrs:{\"label\":\"拥有外籍、境外永久居留权或者长期居留许可情况\"}},[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.tjlist.yjqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"yjqk\", $$v)},expression:\"tjlist.yjqk\"}},[_vm._v(\"有\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"0\"},model:{value:(_vm.tjlist.yjqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"yjqk\", $$v)},expression:\"tjlist.yjqk\"}},[_vm._v(\"无\")])],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"持有因公出入境证件情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscSwzjList.slice(0, 3),\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zjmc\",\"label\":\"证件名称\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cyqk\",\"label\":\"持有情况\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(scope.row.cyqk),callback:function ($$v) {_vm.$set(scope.row, \"cyqk\", $$v)},expression:\"scope.row.cyqk\"}},[_vm._v(\"有\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"0\"},model:{value:(scope.row.cyqk),callback:function ($$v) {_vm.$set(scope.row, \"cyqk\", $$v)},expression:\"scope.row.cyqk\"}},[_vm._v(\"无\")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zjhm\",\"label\":\"证件号码\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.zjhm),callback:function ($$v) {_vm.$set(scope.row, \"zjhm\", $$v)},expression:\"scope.row.zjhm\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"yxq\",\"label\":\"有效期\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(scope.row.yxq),callback:function ($$v) {_vm.$set(scope.row, \"yxq\", $$v)},expression:\"scope.row.yxq\"}})]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"持有因私出入境证件情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscSwzjList.slice(3, 6),\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zjmc\",\"label\":\"证件名称\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cyqk\",\"label\":\"持有情况\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(scope.row.cyqk),callback:function ($$v) {_vm.$set(scope.row, \"cyqk\", $$v)},expression:\"scope.row.cyqk\"}},[_vm._v(\"有\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"0\"},model:{value:(scope.row.cyqk),callback:function ($$v) {_vm.$set(scope.row, \"cyqk\", $$v)},expression:\"scope.row.cyqk\"}},[_vm._v(\"无\")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zjhm\",\"label\":\"证件号码\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.zjhm),callback:function ($$v) {_vm.$set(scope.row, \"zjhm\", $$v)},expression:\"scope.row.zjhm\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"yxq\",\"label\":\"有效期\",\"value-format\":\"yyyy-MM-dd\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(scope.row.yxq),callback:function ($$v) {_vm.$set(scope.row, \"yxq\", $$v)},expression:\"scope.row.yxq\"}})]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"因私出国(境)情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscYccgList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"qssj\",\"label\":\"起始日期\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(scope.row.qssj),callback:function ($$v) {_vm.$set(scope.row, \"qssj\", $$v)},expression:\"scope.row.qssj\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzsj\",\"label\":\"终止日期\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(scope.row.zzsj),callback:function ($$v) {_vm.$set(scope.row, \"zzsj\", $$v)},expression:\"scope.row.zzsj\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cggj\",\"label\":\"近3年所到国家或地区\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.cggj),callback:function ($$v) {_vm.$set(scope.row, \"cggj\", $$v)},expression:\"scope.row.cggj\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"sy\",\"label\":\"事由\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.sy),callback:function ($$v) {_vm.$set(scope.row, \"sy\", $$v)},expression:\"scope.row.sy\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"140\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.czbtn1 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.yscgqkAddRow(_vm.ryglRyscYccgList)}}},[_vm._v(_vm._s(scope.row.czbtn1)+\"\\n            \")]):_vm._e(),_vm._v(\" \"),(scope.row.czbtn2 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.yscgqkDelRow(scope.$index, _vm.ryglRyscYccgList)}}},[_vm._v(_vm._s(scope.row.czbtn2)+\"\\n            \")]):_vm._e()]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"接受境外资助情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscJwzzqkList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zzsj\",\"label\":\"起始日期\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(scope.row.zzsj),callback:function ($$v) {_vm.$set(scope.row, \"zzsj\", $$v)},expression:\"scope.row.zzsj\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gj\",\"label\":\"国家地区\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.gj),callback:function ($$v) {_vm.$set(scope.row, \"gj\", $$v)},expression:\"scope.row.gj\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"jgmc\",\"label\":\"机构名称\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.jgmc),callback:function ($$v) {_vm.$set(scope.row, \"jgmc\", $$v)},expression:\"scope.row.jgmc\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"zznr\",\"label\":\"资助内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.zznr),callback:function ($$v) {_vm.$set(scope.row, \"zznr\", $$v)},expression:\"scope.row.zznr\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"140\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.czbtn1 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.jsjwzzqkAddRow(_vm.ryglRyscJwzzqkList)}}},[_vm._v(_vm._s(scope.row.czbtn1)+\"\\n            \")]):_vm._e(),_vm._v(\" \"),(scope.row.czbtn2 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.jsjwzzqkDelRow(scope.$index, _vm.ryglRyscJwzzqkList)}}},[_vm._v(_vm._s(scope.row.czbtn2)+\"\\n            \")]):_vm._e()]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"处分或者违法犯罪情况\")]),_vm._v(\" \"),_c('el-table',{staticClass:\"sec-el-table\",attrs:{\"border\":\"\",\"data\":_vm.ryglRyscCfjlList,\"header-cell-style\":{ background: '#EEF7FF', color: '#4D91F8' },\"stripe\":\"\"}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"width\":\"60\",\"label\":\"序号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfsj\",\"label\":\"起始日期\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-date-picker',{attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\",\"value-format\":\"yyyy-MM-dd\"},model:{value:(scope.row.cfsj),callback:function ($$v) {_vm.$set(scope.row, \"cfsj\", $$v)},expression:\"scope.row.cfsj\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfjg\",\"label\":\"处理结果\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.cfjg),callback:function ($$v) {_vm.$set(scope.row, \"cfjg\", $$v)},expression:\"scope.row.cfjg\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfyy\",\"label\":\"处理原因\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.cfyy),callback:function ($$v) {_vm.$set(scope.row, \"cfyy\", $$v)},expression:\"scope.row.cfyy\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cfdw\",\"label\":\"处理机构\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"placeholder\":\"\"},model:{value:(scope.row.cfdw),callback:function ($$v) {_vm.$set(scope.row, \"cfdw\", $$v)},expression:\"scope.row.cfdw\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"140\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.czbtn1 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.clhwffzqkAddRow(_vm.ryglRyscCfjlList)}}},[_vm._v(_vm._s(scope.row.czbtn1)+\"\\n            \")]):_vm._e(),_vm._v(\" \"),(scope.row.czbtn2 != '')?_c('el-button',{attrs:{\"size\":\"medium\",\"type\":\"text\"},on:{\"click\":function($event){return _vm.clhwffzqkDelRow(scope.$index, _vm.ryglRyscCfjlList)}}},[_vm._v(_vm._s(scope.row.czbtn2)+\"\\n            \")]):_vm._e()]}}])})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"配偶子女有关情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-third haveBorderTop\"},[_c('div',{staticClass:\"sec-left-text\"},[_c('p',[_vm._v(\"1.在国境内外从事反对、攻击党和国家或者颠覆国家政权活动\")]),_vm._v(\" \"),_c('p',[_vm._v(\"2.被列为影响国家安全重点管控人员\")]),_vm._v(\" \"),_c('p',[_vm._v(\"3.因危害国家安全的行为收到处分或者处罚\")])]),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"\"}},[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.tjlist.qscfqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qscfqk\", $$v)},expression:\"tjlist.qscfqk\"}},[_vm._v(\"有\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"0\"},model:{value:(_vm.tjlist.qscfqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qscfqk\", $$v)},expression:\"tjlist.qscfqk\"}},[_vm._v(\"无\")])],1)],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"其他需要说明的情况\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-four haveBorderTop\"},[_c('el-input',{attrs:{\"type\":\"textarea\",\"autosize\":\"\",\"placeholder\":\"请输入内容\"},model:{value:(_vm.tjlist.qtqk),callback:function ($$v) {_vm.$set(_vm.tjlist, \"qtqk\", $$v)},expression:\"tjlist.qtqk\"}})],1),_vm._v(\" \"),_c('p',{staticClass:\"sec-title\"},[_vm._v(\"本人承诺\")]),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-five haveBorderTop\",staticStyle:{\"position\":\"relative\"}},[_c('el-upload',{staticClass:\"upload-demo\",attrs:{\"action\":\"#\",\"http-request\":_vm.httpRequest,\"show-file-list\":false}},[(_vm.sltshow)?_c('img',{staticClass:\"avatar\",attrs:{\"src\":_vm.sltshow}}):_c('i',{staticClass:\"el-icon-plus avatar-uploader-icon\"})]),_vm._v(\" \"),(_vm.sltshow)?_c('p',{staticClass:\"yulan\",on:{\"click\":_vm.yulan}},[_vm._v(\"预览\")]):_vm._e(),_vm._v(\" \"),(_vm.sltshow)?_c('p',{staticClass:\"yulan\",on:{\"click\":_vm.shanchu}},[_vm._v(\"删除\")]):_vm._e(),_vm._v(\" \"),_c('el-dialog',{attrs:{\"visible\":_vm.dialogVisible},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('img',{staticStyle:{\"width\":\"100%\"},attrs:{\"src\":_vm.dialogImageUrl,\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){_vm.dialogVisible = false}}},[_vm._v(\"取 消\")])],1)])],1),_vm._v(\" \"),_c('div',{staticClass:\"sec-form-six haveBorderTop sec-footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"plain\":\"\"},on:{\"click\":_vm.returnIndex}},[_vm._v(\"返回\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.chooseApproval}},[_vm._v(\"保存并提交\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"primary\"},on:{\"click\":_vm.save}},[_vm._v(\"临时保存\")])],1)],1)],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"选择审批人\",\"close-on-click-modal\":false,\"visible\":_vm.approvalDialogVisible,\"width\":\"40%\",\"destroy-on-close\":true},on:{\"update:visible\":function($event){_vm.approvalDialogVisible=$event}}},[_c('div',{staticClass:\"dlFqsqContainer\"},[_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"部门:\")]),_vm._v(\" \"),_c('el-cascader',{ref:\"cascaderArr\",attrs:{\"options\":_vm.regionOption,\"props\":_vm.regionParams,\"filterable\":\"\",\"clearable\":\"\"},on:{\"change\":_vm.bmSelectChange},model:{value:(_vm.ryChoose.bm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"bm\", $$v)},expression:\"ryChoose.bm\"}}),_vm._v(\" \"),_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"姓名:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"姓名\"},model:{value:(_vm.ryChoose.xm),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"xm\", $$v)},expression:\"ryChoose.xm\"}}),_vm._v(\" \"),_c('el-button',{staticClass:\"searchButton\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchRy}},[_vm._v(\"查询\")]),_vm._v(\" \"),_c('BaseTable',{key:_vm.tableKey,staticClass:\"baseTable\",attrs:{\"tableHeight\":'300',\"showIndex\":true,\"tableData\":_vm.ryDatas,\"columns\":_vm.applyColumns,\"showSingleSelection\":true,\"handleColumn\":_vm.handleColumnApply,\"showPagination\":true,\"currentPage\":_vm.page,\"pageSize\":_vm.pageSize,\"totalCount\":_vm.total},on:{\"handleCurrentChange\":_vm.handleCurrentChangeRy,\"handleSizeChange\":_vm.handleSizeChangeRy,\"handleSelectionChange\":_vm.handleSelectionChange}})],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{staticClass:\"fr ml10\",attrs:{\"type\":\"warning\"},on:{\"click\":function($event){_vm.approvalDialogVisible = false}}},[_vm._v(\"关 闭\")]),_vm._v(\" \"),_c('el-button',{staticClass:\"fr\",attrs:{\"type\":\"success\"},on:{\"click\":_vm.saveAndSubmit}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1)])],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-1f5e65b4\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/zgfcTable.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-1f5e65b4\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./zgfcTable.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./zgfcTable.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./zgfcTable.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-1f5e65b4\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./zgfcTable.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-1f5e65b4\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/zgfcTable.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}