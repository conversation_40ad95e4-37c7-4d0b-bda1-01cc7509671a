<template>
    <div class="sec-container" v-loading="loading">
        <!-- 标题 -->
        <p class="sec-title">添加场所审定</p>
        <div class="sec-form-container">
            <el-form ref="formName" :model="tjlist" label-width="225px">
                <!-- 第一部分包括姓名到常住地公安start -->
                <div class="sec-header-section">
                    <div class="sec-form-left">
                        <el-form-item label="申请部门">
                            <template slot-scope="scope">
                                <el-cascader v-model="tjlist.sqbm" style="width: 100%;" :options="regionOption"
                                    :props="regionParams" filterable clearable ref="cascaderArr"
                                    @change="handleChange(2)"></el-cascader>
                            </template>
                        </el-form-item>
                        <el-form-item label="申请人">
                            <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.xqr"
                                :fetch-suggestions="querySearch" placeholder="请输入申请人" style="width:100%">
                            </el-autocomplete>
                        </el-form-item>

                    </div>
                    <div class="sec-form-left">
                        <el-form-item label="场所名称">
                            <el-input placeholder="" v-model="tjlist.csmc" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="涉密程度" class="longLabel">
                            <el-radio-group v-model="tjlist.smcd">
                                <el-radio v-for="item in sbmjxz" :v-model="tjlist.smcd" :label="item.id" :value="item.id"
                                    :key="item.id">{{ item.mc }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </div>
                    <div class="sec-form-left">
                        <el-form-item label="责任人部门">
                            <template slot-scope="scope">
                                <el-cascader v-model="tjlist.zrbm" style="width: 100%;" :options="regionOption"
                                    :props="regionParams" filterable clearable ref="cascaderArr"
                                    @change="handleChange(1)"></el-cascader>
                            </template>
                        </el-form-item>
                        <el-form-item label="责任人">
                            <el-autocomplete class="inline-input" value-key="xm" v-model.trim="tjlist.zrr"
                                :fetch-suggestions="querySearch" placeholder="请输入申请人" style="width:100%">
                            </el-autocomplete>
                        </el-form-item>

                    </div>
                    <div class="sec-form-left">
                        <el-form-item label="填写日期">
                            <el-date-picker v-model="tjlist.sqrq" type="date" class="rip" placeholder="选择日期"
                                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="责任人电话">
                            <el-input placeholder="" v-model="tjlist.zrrdh" clearable></el-input>
                        </el-form-item>

                    </div>
                    <div class="sec-form-left">
                        <el-form-item label="用途">
                            <el-input placeholder="" v-model="tjlist.yt" clearable></el-input>
                        </el-form-item>
                    </div>
                    <div class="sec-form-left">
                        <el-form-item label="所在位置">
                            <div style="display: flex;justify-content:space-between">
                                <el-input placeholder="" v-model="tjlist.szwz" clearable></el-input>
                                <el-upload action="/posts" v-show="!dis" style="margin-right: 10px;" :show-file-list="false"
                                    :data="{}" :http-request="scwj" :before-upload="beforeAvatarUpload">
                                    <el-button slot="trigger" type="primary">上传现场示意图</el-button>
                                </el-upload>
                                <el-button slot="trigger" type="primary" @click="yl">预览</el-button>
                                <el-dialog :visible.sync="dialogVisible_scyl">
                                    <img :src="scylImageUrl" alt="" style="width: 100%">
                                    <div slot="footer" class="dialog-footer">
                                        <el-button size="small" @click="dialogVisible_scyl = false">取 消</el-button>
                                    </div>
                                </el-dialog>
                            </div>
                        </el-form-item>
                    </div>
                    <p class="sec-title">已采取防护措施情况</p>
                    <div class="sec-form-third haveBorderTop">
                        <div class="sec-left-text">
                            <div>
                                人工防护措施：<el-checkbox-group v-model="tjlist.fhcs" class="checkbox">
                                    <el-checkbox v-for="item in rgfhcslist" :label="item.xdfsmc" :value="item.xdfsmc"
                                        :key="item.xdfsid"></el-checkbox>
                                </el-checkbox-group>
                            </div>
                            <div style="margin-top: 10px;">物理防护措施 <el-checkbox-group v-model="tjlist.fhcs" class="checkbox">
                                    <el-checkbox v-for="item in wlfhcslist" :label="item.xdfsmc" :value="item.xdfsmc"
                                        :key="item.xdfsid"></el-checkbox>
                                </el-checkbox-group>
                            </div>
                            <div style="margin-top: 10px;">技术防护措施 <el-checkbox-group v-model="tjlist.fhcs" class="checkbox">
                                    <el-checkbox v-for="item in jsfhcslist" :label="item.xdfsmc" :value="item.xdfsmc"
                                        :key="item.xdfsid"></el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                    </div>
                    <!-- <p class="sec-title">备注：本表只适用于涉密人员由高涉密等级调整到低涉密等级</p> -->
                </div>

                <!-- 底部操作按钮start -->
                <div class="sec-form-six haveBorderTop sec-footer">
                    <el-button @click="returnIndex" class="fr ml10" plain>返回</el-button>
                    <el-button @click="chooseApproval" class="fr" type="success">保存并提交</el-button>
                    <!-- <el-button @click="saveAndSubmit" class="fr" type="success">保存并提交</el-button>
            <el-button @click="save" class="fr" type="primary">保存</el-button> -->
                    <el-button @click="save" class="fr" type="primary">临时保存</el-button>
                </div>
                <!-- 底部操作按钮end -->

            </el-form>
        </div>
        <!-- 发起申请弹框start -->
        <el-dialog title="选择审批人" :close-on-click-modal="false" :visible.sync="approvalDialogVisible" width="40%" :destroy-on-close="true">
            <div class="dlFqsqContainer">
                <label for="">部门:</label>
                <el-cascader v-model="ryChoose.bm" :options="regionOption" :props="regionParams" filterable clearable
                    ref="cascaderArr" @change="bmSelectChange"></el-cascader>
                <label for="">姓名:</label>
                <el-input class="input2" v-model="ryChoose.xm" clearable placeholder="姓名"></el-input>
                <el-button class="searchButton" type="primary" icon="el-icon-search" @click="searchRy">查询</el-button>
                <BaseTable class="baseTable" :tableHeight="'300'" :key="tableKey" :showIndex=true :tableData="ryDatas"
                    :columns="applyColumns" :showSingleSelection="true" :handleColumn="handleColumnApply"
                    :showPagination=true :currentPage="page" :pageSize="pageSize" :totalCount="total"
                    @handleCurrentChange="handleCurrentChangeRy" @handleSizeChange="handleSizeChangeRy"
                    @handleSelectionChange="handleSelectionChange">
                </BaseTable>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="warning" class="fr ml10" @click="approvalDialogVisible = false">关 闭</el-button>
                <el-button @click="saveAndSubmit" class="fr" type="success">提交</el-button>
                <!-- <el-button @click="save" class="fr" type="primary">保存</el-button> -->
                <div style="clear:both"></div>
            </span>
        </el-dialog>
        <!-- 发起申请弹框end -->
        <el-dialog title="涉密计算机详细信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="46%" class="xg"
            :before-close="handleClose" @close="close('formName')">
            <el-form ref="formName" :model="smsb" :rules="rules" label-width="150px" size="mini">
                <div style="display:flex">
                    <el-form-item label="存放位置" prop="cfwz" class="one-line">
                        <el-input placeholder="存放位置" v-model="smsb.cfwz" clearable>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="启用日期" prop="qyrq" class="one-line">
                        <!-- <el-input v-model="smsb.sgsj" clearable></el-input> -->
                        <el-date-picker v-model="smsb.qyrq" clearable type="date" placeholder="选择日期" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" style="width:100%">
                        </el-date-picker>
                    </el-form-item>
                </div>
                <div style="display:flex">
                    <el-form-item label="设备类型" prop="lx" class="one-line">
                        <div style="display: flex;">
                            <el-select v-model="smsb.fl" placeholder="分类" style="width: 100%;margin-right: 5px;"
                                @change="sbfl">
                                <el-option v-for="item in smsbfl" :key="item.flid" :label="item.flmc" :value="item.flid">
                                </el-option>
                            </el-select>
                            <el-select v-model="smsb.lx" placeholder="请选择" style="width: 100%;">
                                <el-option v-for="item in sblxxz" :key="item.id" :label="item.mc" :value="item.mc">
                                </el-option>
                            </el-select>
                        </div>
                    </el-form-item>
                    <el-form-item label="品牌型号" prop="ppxh" class="one-line">
                        <el-input placeholder="品牌型号" v-model="smsb.ppxh" clearable>
                        </el-input>
                    </el-form-item>
                </div>
                <div style="display:flex">
                    <el-form-item label="保密管理编号" prop="bmglbh" class="one-line">
                        <el-input placeholder="保密管理编号" v-model="smsb.bmglbh" clearable>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="固定资产编号" prop="gdzcbh" class="one-line">
                        <el-input placeholder="固定资产编号" v-model="smsb.gdzcbh" clearable>
                        </el-input>
                    </el-form-item>
                </div>
                <div style="display:flex">
                    <el-form-item label="设备序列号" prop="sbxlh" class="one-line">
                        <el-input placeholder="设备序列号" v-model="smsb.sbxlh" clearable>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="硬盘序列号" prop="ypxlh" class="one-line">
                        <el-input placeholder="硬盘序列号" v-model="smsb.ypxlh" clearable>
                        </el-input>
                    </el-form-item>
                </div>
                <el-form-item label="密 级" prop="mj">
                    <el-radio-group v-model="smsb.mj" style="width:120%">
                        <el-radio v-for="item in sbmjxz" :v-model="smsb.mj" :label="item.id" :value="item.id" :key="item.id"
                            @change.native="choose">
                            {{ item.mc }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="主要配置参数" prop="pzcs" class="one-line">
                    <el-input placeholder="主要配置参数" v-model="smsb.pzcs" clearable>
                    </el-input>
                </el-form-item>
                <div style="display:flex">
                    <el-form-item label="管理部门" prop="glbm" class="one-line">
                        <!-- <el-input placeholder="管理部门" v-model="smsb.glbm" clearable></el-input> -->
                        <el-cascader v-model="smsb.glbm" :options="regionOption" :props="regionParams" style="width: 100%;"
                            filterable ref="cascaderArr" @change="handleChange(1)">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="责任人" prop="zrr" class="one-line">
                        <el-autocomplete class="inline-input" value-key="xm" v-model.trim="smsb.zrr"
                            :fetch-suggestions="querySearch" placeholder="请输入责任人" style="width:100%">
                        </el-autocomplete>
                    </el-form-item>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
                <el-button type="warning" @click="handleClose()">关 闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {
    getAllSmsblx,//获取设备类型
    getAllSmsbmj,//获取设备密级
    getZdhsblx,
    getsmwlsblx,
    getSmydcclx,
    getKeylx
} from '../../../../api/xlxz'
import {
    getLcSLid,
    updateZgfs,
    updateSlzt,
    getZzjgList,
    getSpUserList,
    getCurZgfsjl,
    getFwdyidByFwlx,
    getAllYhxx,
    savaZtqdBatch,//添加载体清单
    deleteZtqdByYjlid,//删除载体清单
    getLoginInfo,
    deleteSlxxBySlid,
} from '../../../../api/index'
import {
    savaSbqdBatch,
    getSbqdListByYjlid,
    deleteSbqdByYjlid
} from '../../../../api/sbqd'
import {
    submitCssd,
    getCssdInfo,
    updateCssd
} from '../../../../api/cssdsc'
import { getAllGwxx } from '../../../../api/qblist'
import { getAllSmdj } from '../../../../api/xlxz'
import { getUserInfo } from '../../../../api/dwzc'
import BaseTable from '../../../components/common/baseTable.vue'
import AddLineTable from "../../../components/common/addLineTable.vue"; //人工纠错组件
export default {
    components: {
        AddLineTable,
        BaseTable
    },
    props: {},
    data() {
        return {
            tableKey:1,
            rgfhcslist: [
                {
                    xdfsid: '1',
                    xdfsmc: '保护守卫'
                },
                {
                    xdfsid: '2',
                    xdfsmc: '安保巡逻'
                },
                {
                    xdfsid: '3',
                    xdfsmc: '公司员工值班'
                },
            ],
            wlfhcslist: [
                {
                    xdfsid: '1',
                    xdfsmc: '铁门'
                },
                {
                    xdfsid: '2',
                    xdfsmc: '铁窗'
                },
                {
                    xdfsid: '3',
                    xdfsmc: '密码保险柜'
                },
                {
                    xdfsid: '4',
                    xdfsmc: '密码文件柜'
                },
                {
                    xdfsid: '5',
                    xdfsmc: '手机信号屏蔽柜'
                },
            ],
            jsfhcslist: [
                {
                    xdfsid: '1',
                    xdfsmc: '门禁系统'
                },
                {
                    xdfsid: '2',
                    xdfsmc: '红外报警器'
                },
                {
                    xdfsid: '3',
                    xdfsmc: '视频监控'
                },
                {
                    xdfsid: '4',
                    xdfsmc: '视频干扰器'
                },
                {
                    xdfsid: '5',
                    xdfsmc: '碎纸机'
                },
                {
                    xdfsid: '6',
                    xdfsmc: '手机信号屏蔽器'
                },
            ],
            sblxxz: [],//设备类型
            smsbfl: [
                {
                    flid: 1,
                    flmc: '涉密计算机'
                },
                {
                    flid: 2,
                    flmc: '涉密办公自动化设备'
                },
                {
                    flid: 3,
                    flmc: '涉密网络设备'
                },
                {
                    flid: 4,
                    flmc: '涉密存储设备'
                },
                {
                    flid: 5,
                    flmc: 'KEY'
                },
            ],
            sbmjxz: [],//设备密级
            rules: {
                cfwz: [{
                    required: true,
                    message: '请输入存放位置',
                    trigger: 'blur'
                },],
                qyrq: [{
                    required: true,
                    message: '请选择启用日期',
                    trigger: 'blur'
                },],
                lx: [{
                    required: true,
                    message: '请选择类型',
                    trigger: 'blur'
                },],
                ppxh: [{
                    required: true,
                    message: '请输入品牌型号',
                    trigger: 'blur'
                },],
                bmglbh: [{
                    required: true,
                    message: '请输入保密管理编号',
                    trigger: 'blur'
                },],
                gdzcbh: [{
                    required: true,
                    message: '请输入固定资产编号',
                    trigger: 'blur'
                },],
                sbxlh: [{
                    required: true,
                    message: '请输入设备序列号',
                    trigger: 'blur'
                },],
                ypxlh: [{
                    required: true,
                    message: '请输入硬盘序列号',
                    trigger: 'blur'
                },],
                mj: [{
                    required: true,
                    message: '请选择密级',
                    trigger: 'blur'
                },],
                pzcs: [{
                    required: true,
                    message: '请输入主要配置参数',
                    trigger: 'blur'
                },],
                glbm: [{
                    required: true,
                    message: '请输入管理部门',
                    trigger: 'blur'
                },],
                zrr: [{
                    required: true,
                    message: '请输入责任人',
                    trigger: 'blur'
                },],
            },
            radio: '',
            value1: '',
            loading: false,
            //判断实例所处环节
            disabled1: false,
            disabled2: false,
            disabled3: false,
            // 弹框人员选择条件
            ryChoose: {
                'bm': '',
                'xm': ''
            },
            gwmclist: [],
            smdjxz: [],
            regionOption: [], // 部门下拉
            page: 1, // 审批人弹框当前页
            pageSize: 10, // 审批人弹框每页条数
            radioIdSelect: '', // 审批人弹框人员单选
            ryDatas: [], // 弹框人员选择
            total: 0, // 弹框人员总数
            regionParams: {
                label: 'label', //这里可以配置你们后端返回的属性
                value: 'label',
                children: 'childrenRegionVo',
                expandTrigger: 'click',
                checkStrictly: true
            }, //地域信息配置参数
            // table 行样式
            headerCellStyle: {
                background: '#EEF7FF',
                color: '#4D91F8'
            },
            // form表单提交数据
            tjlist: {
                sqbm: '',
                smryid: '',
                xqr: '',
                szbm: '',
                zzrq: '',
                zxfw: '',
                fffw: '',
                yt: '',
                schp: '',
                scddh: '',
                zzcs: '',
                zzr: '',
                xmjl: '',
                zrr: '',
                zrbm: '',
                fhcs: [],
            },
            smsb: {
                cfwz: '',//存放位置
                qyrq: '',//启用日期
                lx: 0,//设备类型
                ppxh: '',//品牌型号
                bmglbh: '',//保密管理编号
                gdzcbh: '',//固定资产编号
                sbxlh: '',//设备序列号
                ypxlh: '',//硬盘序列号
                mj: '',//密 级
                pzcs: '',//主要配置参数
                zrr: '',//责任人
                glbm: '',//管理部门
            },
            // 载体详细信息
            ztqsQsscScjlList: [],
            ryInfo: {},

            sltshow: '', // 文档的缩略图显示
            routeType: '',
            pdfBase64: '',
            fileList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            approvalDialogVisible: false, // 选择申请人弹框
            fileRow: '',
            ztlxList: [
                {
                    lxid: 1,
                    lxmc: '纸介质'
                },
                {
                    lxid: 2,
                    lxmc: '光盘'
                },
                {
                    lxid: 3,
                    lxmc: '电磁介质'
                },
            ],
            smdjList: [
                {
                    smdjid: 1,
                    smdjmc: '绝密'
                },
                {
                    smdjid: 2,
                    smdjmc: '机密'
                },
                {
                    smdjid: 3,
                    smdjmc: '秘密'
                },
                {
                    smdjid: 4,
                    smdjmc: '内部'
                },
            ],
            // 选择审核人table
            applyColumns: [{
                name: '姓名',
                prop: 'xm',
                scopeType: 'text',
                formatter: false
            },
            {
                name: '部门',
                prop: 'bmmc',
                scopeType: 'text',
                formatter: false
            },
            {
                name: '岗位',
                prop: 'gwmc',
                scopeType: 'text',
                formatter: false
            }
            ],
            handleColumnApply: [],
            scqk: [
                {
                    sfty: '同意',
                    id: 1
                },
                {
                    sfty: '不同意',
                    id: 0
                },
            ],
            disabled2: false,
            //知悉范围选择
            rydialogVisible: false,
            scylImageUrl: '',
            dialogVisible_scyl: false,
            dis: false,
        }
    },
    computed: {
        // selectedLabel() {
        //   const option = this.ynoptions.find(o => o.value === this.selectedValue);
        //   return option ? option.label : '';
        // }
    },
    mounted() {
        this.smsblx()
        this.smmjxz()
        this.smry()
        this.getOrganization()
        this.onfwid()
        this.defaultym()
    },
    methods: {
        //base64转码
        blobToBase64(blob, callback) {
            const fileReader = new FileReader();
            fileReader.onload = (e) => {
                callback(e.target.result);
            };
            fileReader.readAsDataURL(blob);
        },
        //上传文件限制
        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isPNG = file.type === 'image/png';
            if (!isJPG && !isPNG) {
                this.$message.error('上传缩略图只能是 JPG/PNG 格式!');
            }
            return isJPG || isPNG;
        },
        //上传文件
        scwj(item) {
            // this.file = item.file
            this.filename = item.file.name
            // console.log("上传文件", "this.file", this.file, "this.filename", this.filename);
            this.tjlist.wjm = this.filename
            this.sltshow = URL.createObjectURL(item.file);
            this.fileRow = item.file
            this.blobToBase64(item.file, (dataurl) => {
                this.file = dataurl.split(',')[1]
                console.log(this.file);
            });
        },
        //图片预览
        yl() {
            let zpxx
            zpxx = this.zpzm(this.file)
            this.scylImageUrl = zpxx
            this.dialogVisible_scyl = true
        },
        zpzm(zp) {
            const iamgeBase64 = "data:image/jpeg;base64," + zp;
            let zpxx
            if (typeof iamgeBase64 === "string") {
                // 复制某条消息
                if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
                function validDataUrl(s) {
                    return validDataUrl.regex.test(s);
                }
                validDataUrl.regex =
                    /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
                if (validDataUrl(iamgeBase64)) {
                    // debugger;
                    // let that = this;

                    function previwImg(item) {
                        zpxx = item;
                    }
                    previwImg(iamgeBase64);
                }
            }
            return zpxx
        },
        async defaultym() {
            this.type = this.$route.query.type
            if (this.$route.query.type == 'add') {
                let data = await getUserInfo()
            this.tjlist.sqbm = data.bmmc.split('/')
            this.tjlist.zrbm = data.bmmc.split('/')
            this.tjlist.xqr = data.xm
            } else {
                let jlid = this.$route.query.jlid
                let data = await getCssdInfo({
                    jlid: jlid
                })
                this.tjlist = data
                this.tjlist.zrbm = this.tjlist.zrbm.split('/')
                this.tjlist.sqbm = this.tjlist.sqbm.split('/')
                this.tjlist.fhcs = this.tjlist.fhcs.split('/')
                this.file = this.tjlist.smjlj
            }
        },
        async sbfl() {
            if (this.smsb.fl == 1) {
                this.sblxxz = await getAllSmsblx()
            } else if (this.smsb.fl == 2) {
                this.sblxxz = await getZdhsblx()
            } else if (this.smsb.fl == 3) {
                this.sblxxz = await getsmwlsblx()
            } else if (this.smsb.fl == 4) {
                this.sblxxz = await getSmydcclx()
            } else if (this.smsb.fl == 5) {
                this.sblxxz = await getKeylx()
            }
        },
        //数据默认
        smsbqk() {
            this.smsb.cfwz = ''//存放位置
            this.smsb.qyrq = '';//启用日期
            this.smsb.lx = '';//设备类型
            this.smsb.ppxh = '';//品牌型号
            this.smsb.bmglbh = '';//保密管理编号
            this.smsb.gdzcbh = '';//固定资产编号
            this.smsb.sbxlh = '';//设备序列号
            this.smsb.ypxlh = '';//硬盘序列号
            this.smsb.mj = '';//密 级
            this.smsb.pzcs = '';//主要配置参数
            this.smsb.zrr = '';//责任人
            this.smsb.glbm = '';//管理部门
        },
        //给予默认保密期限
        choose() {
            if (this.smsb.mj == 1) {
                this.smsb.bmqx = 30
            } else if (this.smsb.mj == 2) {
                this.smsb.bmqx = 20
            } else {
                this.smsb.bmqx = 10
            }
        },
        //添加涉密设备
        submitsb() {
            console.log(this.ztqsQsscScjlList)
            this.smsbqk()
            this.dialogVisible = true
        },
        //确认添加设备
        submitTj(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let smsb = this.smsb
                    this.ztqsQsscScjlList.push(smsb)
                    this.ztqsQsscScjlList = JSON.parse(JSON.stringify(this.ztqsQsscScjlList))
                    // this.ztqsQsscScjlList.push(smsb)

                    this.dialogVisible = false
                    // arrLst = []
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 弹框关闭触发
        close(formName) {
            // 清空表单校验，避免再次进来会出现上次校验的记录
            this.$refs[formName].clearValidate();
        },
        handleClose(done) {
            this.dialogVisible = false
        },

        //设备类型获取
        async smsblx() {
            this.sblxxz = await getAllSmsblx()
        },
        //设备密级获取
        async smmjxz() {
            this.sbmjxz = await getAllSmsbmj()
        },
        querySearch(queryString, cb) {
            var restaurants = this.restaurants;
            console.log("restaurants", restaurants);
            var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
            console.log("results", results);
            // 调用 callback 返回建议列表的数据
            cb(results);
            console.log("cb(results.dwmc)", results);
        },
        createFilter(queryString) {
            return (restaurant) => {
                return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
            };
        },
        async smry() {
            this.restaurants = await getAllYhxx()
        },
        async handleChange(index) {
            let resList
            let params
            if (index == 1) {
                params = {
                    bmmc: this.tjlist.zrbm.join('/')
                }
                resList = await getAllYhxx(params)
                this.tjlist.zrr = "";
            } else if (index == 2) {
                this.tjlist.zrbm = this.tjlist.sqbm
                params = {
                    bmmc: this.tjlist.sqbm.join('/')
                }
                resList = await getAllYhxx(params)
                this.tjlist.xqr = "";
            }
            this.restaurants = resList;

        },
        //结束

        chRadio() { },
        async gwxx() {
            let param = {
                bmmc: this.tjlist.bmmc
            }
            let data = await getAllGwxx(param)
            this.gwmclist = data
            console.log(data);
        },
        //获取涉密等级信息
        async smdj() {
            let data = await getAllSmdj()
            this.smdjxz = data
        },
        handleSelectionChange(index, row) {
            this.radioIdSelect = row
        },


        // 删除
        shanchu() {
            this.tjlist.brcn = ''
            this.sltshow = ''
        },
        async onfwid() {
            let params = {
                fwlx: 5
            }
            let data = await getFwdyidByFwlx(params)
            console.log(data);
            this.fwdyid = data.data.fwdyid
        },
        jyxx() {
            if (this.tjlist.xqr == '' || this.tjlist.xqr == undefined) {
                this.$message.error('请输入申请人')
                return true
            }
            if (this.tjlist.sqbm.length == 0 || this.tjlist.sqbm == undefined) {
                this.$message.error('请输入申请部门')
                return true
            }
            if (this.tjlist.csmc == '' || this.tjlist.csmc == undefined) {
                this.$message.error('请输入场所名称')
                return true
            }
            if (this.tjlist.smcd == '' || this.tjlist.smcd == undefined) {
                this.$message.error('请输入涉密程度')
                return true
            }
            if (this.tjlist.zrbm.length == 0 || this.tjlist.zrbm == undefined) {
                this.$message.error('请输入责任人部门')
                return true
            }
            if (this.tjlist.zrr == '' || this.tjlist.zrr == undefined) {
                this.$message.error('请输入责任人')
                return true
            }
            if (this.tjlist.sqrq == '' || this.tjlist.sqrq == undefined) {
                this.$message.error('请输入填写日期')
                return true
            }
            if (this.tjlist.zrrdh == '' || this.tjlist.zrrdh == undefined) {
                this.$message.error('请输入责任人电话')
                return true
            }
            if (this.tjlist.szwz == '' || this.tjlist.szwz == undefined) {
                this.$message.error('请输入所在位置')
                return true
            }
        },
        // 保存
        async save() {

            let param = {
                'fwdyid': this.fwdyid,
                'lcslclzt': 3
            }
            if (this.jyxx()) {
                return
            }
            param.smryid = ''
            console.log(this.$route.query);
            if (this.$route.query.type == 'update') {
                param.slid = this.$route.query.slid
                let res = await getLcSLid(param)
                if (res.code == 10000) {
                    this.tjlist.smjlj = this.file
                    this.tjlist.fhcs = this.tjlist.fhcs.join('/')
                    this.tjlist.sqbm = this.tjlist.sqbm.join('/')
                    this.tjlist.zrbm = this.tjlist.zrbm.join('/')
                    let params = this.tjlist
                    let resDatas = await updateCssd(params)
                    if (resDatas.code == 10000) {
                        this.$router.push('/cssdsc')
                        this.$message({
                            message: '保存并提交成功',
                            type: 'success'
                        })
                    }
                }
            } else {
                let res = await getLcSLid(param)
                if (res.code == 10000) {
                    this.tjlist.slid = res.data.slid
                    this.tjlist.smjlj = this.file
                    this.tjlist.fhcs = this.tjlist.fhcs.join('/')
                    this.tjlist.sqbm = this.tjlist.sqbm.join('/')
                    this.tjlist.zrbm = this.tjlist.zrbm.join('/')
                    let params = this.tjlist
                    let resDatas = await submitCssd(params)
                    if (resDatas.code == 10000) {
                        this.$router.push('/cssdsc')
                        this.$message({
                            message: '保存成功',
                            type: 'success'
                        })
                    } else {
                        deleteSlxxBySlid({ slid: res.data.slid })
                    }
                }
            }

        },
        //全部组织机构List
        async getOrganization() {
            let zzjgList = await getZzjgList()
            this.zzjgmc = zzjgList
            let shu = []
            this.zzjgmc.forEach(item => {
                let childrenRegionVo = []
                this.zzjgmc.forEach(item1 => {
                    if (item.bmm == item1.fbmm) {
                        childrenRegionVo.push(item1)
                        item.childrenRegionVo = childrenRegionVo
                    }
                });
                shu.push(item)
            })
            let shuList = []
            let list = await getLoginInfo()
            if (list.fbmm == '') {
                shu.forEach(item => {
                    if (item.fbmm == '') {
                        shuList.push(item)
                    }
                })
            }
            if (list.fbmm != '') {
                shu.forEach(item => {
                    console.log(item);
                    if (item.fbmm == list.fbmm) {
                        shuList.push(item)
                    }
                })
            }
            shuList[0].childrenRegionVo.forEach(item => {
                this.regionOption.push(item)
            })
        },
        handleSelectionChange1(index, row) {
            this.radioIdSelect = row
        },
        handleCurrentChangeRy(val) {
            this.page = val
            this.chooseApproval()
        },
        //列表分页--更改每页显示个数
        handleSizeChangeRy(val) {
            this.page = 1
            this.pageSize = val
            this.chooseApproval()
        },
        // 人员搜索
        searchRy() {
      this.tableKey++
            this.chooseApproval()
        },
        // 发起申请选择人员 人员下拉
        bmSelectChange(item) {
            if (item != undefined) {
                this.ryChoose.bm = item.join('/')
            }
        },
        rowStyle({ row, rowIndex }) {
            if (row.sfsc == 0) {
                return 'success_class';
            } else if (row.sfsc == 1 && row.sfdfs == 1) {
                return 'success1_class';
            } else {
                return '';
            }
        },
        // 选择审批人
        async chooseApproval() {
            if (this.jyxx()) {
                return
            }
            // this.getOrganization()
            this.approvalDialogVisible = true
            let param = {
                'page': this.page,
                'pageSize': this.pageSize,
                'fwdyid': this.fwdyid,
                'bmmc': this.ryChoose.bm,
                'xm': this.ryChoose.xm
            }
            let resData = await getSpUserList(param)
            if (resData.records) {
                // this.loading = false
                this.ryDatas = resData.records
                this.total = resData.total
            } else {
                this.$message.error('数据获取失败！')
            }

        },
        // 保存并提交
        async saveAndSubmit() {
            if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {
                let param = {
                    'fwdyid': this.fwdyid
                }
                // this.tjlist.dwid = this.ryInfo.dwid
                // this.tjlist.lcslid = this.ryInfo.lcslid
                if (this.$route.query.type == 'update') {
                    param.lcslclzt = 2
                    param.smryid = ''
                    param.slid = this.$route.query.slid
                    param.clrid = this.radioIdSelect.yhid
                    let res = await getLcSLid(param)
                    if (res.code == 10000) {
                        this.tjlist.smjlj = this.file
                        this.tjlist.fhcs = this.tjlist.fhcs.join('/')
                        this.tjlist.sqbm = this.tjlist.sqbm.join('/')
                        this.tjlist.zrbm = this.tjlist.zrbm.join('/')
                        let params = this.tjlist
                        let resDatas = await updateCssd(params)
                        if (resDatas.code == 10000) {

                            let paramStatus = {
                                'fwdyid': this.fwdyid,
                                'slid': this.$route.query.slid
                            }
                            let resStatus
                            resStatus = await updateSlzt(paramStatus)
                            if (resStatus.code == 10000) {
                                this.$router.push('/cssdsc')
                                this.$message({
                                    message: '保存并提交成功',
                                    type: 'success'
                                })
                            }


                        }
                    }
                } else {
                    param.lcslclzt = 0
                    param.clrid = this.radioIdSelect.yhid
                    param.smryid = ''
                    let res = await getLcSLid(param)
                    if (res.code == 10000) {
                        this.tjlist.slid = res.data.slid
                        this.tjlist.smjlj = this.file
                        this.tjlist.fhcs = this.tjlist.fhcs.join('/')
                        this.tjlist.sqbm = this.tjlist.sqbm.join('/')
                        this.tjlist.zrbm = this.tjlist.zrbm.join('/')
                        let params = this.tjlist
                        let resDatas = await submitCssd(params)
                        if (resDatas.code == 10000) {
                            this.$router.push('/cssdsc')
                            this.$message({
                                message: '保存并提交成功',
                                type: 'success'
                            })
                        } else {
                            deleteSlxxBySlid({ slid: res.data.slid })
                        }
                    }
                }
            } else {
                this.$message({
                    message: '请选择审批人',
                    type: 'warning'
                })
            }
        },
        // 返回
        returnIndex() {
            this.$router.push('/cssdsc')
        },
        formj(row) {
            console.log(row);
            let smmj
            this.sbmjxz.forEach(item => {
                if (row.mj == item.id) {
                    smmj = item.mc
                }
            })
            return smmj
        },
        forbgmj(row) {
            let hxsj
            this.sbmjxz.forEach(item => {
                if (row.bgmj == item.id) {
                    hxsj = item.mc
                }
            })
            return hxsj
        },

    },
    watch: {

    }
}

</script>
  
<style scoped>
.sec-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: overlay;
}

.sec-title {
    border-left: 5px solid #1b72d8;
    color: #1b72d8;
    font-size: 20px;
    font-weight: 700;
    text-indent: 10px;
    margin-bottom: 20px;
    margin-top: 10px;
}

.sec-form-container {
    width: 100%;
    height: 100%;
}

.sec-form-left {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
}

.sec-form-left:not(:first-child) {
    border-top: 0;
}

.sec-form-left .el-form-item {
    float: left;
    width: 100%;
}

.sec-header-section {
    width: 100%;
    position: relative;
}

.sec-header-pic {
    width: 258px;
    position: absolute;
    right: 0px;
    top: 0;
    height: 163px;
    border: 1px solid #CDD2D9;
    border-left: 0;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sec-form-second {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
    border-top: 0;
}

.sec-form-third {
    border: 1px solid #CDD2D9;
    /* height: 40px;  */
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-four {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-five {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    overflow: hidden;
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.yulan {
    text-align: center;
    cursor: pointer;
    color: #3874D5;
    font-weight: 600;
    float: left;
    margin-left: 10px;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 2px solid #EBEBEB;
}

.sec-form-six {
    border: 1px solid #CDD2D9;
    overflow: hidden;
    background: #ffffff;
    padding: 10px;
}

.ml10 {
    margin-left: 10px;
}

.sec-footer {
    margin-top: 10px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
    border: none;
}

.sec-left-text {
    float: left;
    margin-right: 130px;
}

.haveBorderTop {
    border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
    width: 225px !important;
    /* height: 184px;
    line-height: 184px; */
}


>>>.longLabel .el-form-item__content {
    margin-left: 225px !important;
    padding-left: 20px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
    line-height: 48px;
}

/* .sec-form-second:not(:first-child){
    border-top: 0;
  } */
.sec-form-second .el-form-item {
    float: left;
    width: 100%;
}

.sec-el-table {
    width: 100%;
    border: 1px solid #EBEEF5;
    height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
    padding-left: 15px;
    background-color: #F5F7FA;
    width: calc(100% - 16px);
    border-right: 1px solid #CDD2D9;
    color: #C0C4CC;
}

>>>.sec-el-table .el-input__inner {
    border: none !important;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
    width: 200px;
    text-align: center;
    font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
    border: none;
    border-right: 1px solid #CDD2D9;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item {
    margin-bottom: 0px;
}

/* >>>.el-form > div {
    border: 1px solid #CDD2D9;;
  } */
>>>.el-form-item__label {
    border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
    margin-top: 5px;
    margin-bottom: 5px;
  } */

.widthw {
    width: 6vw;
}

.dlFqsqContainer {
    width: 100%;
    height: 100%;
}

.dlFqsqContainer label {
    font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
    width: 150px;
    margin-left: 10px;
}

.dlFqsqContainer .searchButton {
    margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
    height: 40px;
}

.dlFqsqContainer .input1 {
    margin-right: 20px;
}

.dlFqsqContainer .tb-container {
    margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
    margin-top: 20px;
}

>>>.wd .el-radio {
    display: block;
    margin: 10px 0;
}

>>>.lh .el-radio {
    line-height: 48px;
}

/deep/.el-table .success_class {
    background-color: rgb(167, 231, 243) !important;
}

/deep/.el-table .success1_class {
    background-color: rgb(111, 255, 0) !important;
}

>>>.wd .el-form-item__label {
    height: 184px;
    line-height: 184px;
}

.rip {
    width: 100% !important;
}
</style>
  