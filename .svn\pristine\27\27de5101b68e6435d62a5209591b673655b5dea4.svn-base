{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/rycs/xdsbsc.vue", "webpack:///./src/renderer/view/rcgz/rycs/xdsbsc.vue?7767", "webpack:///./src/renderer/view/rcgz/rycs/xdsbsc.vue"], "names": ["xdsbsc", "components", "BaseHeader", "baseHeader", "BaseTable", "baseTable", "props", "data", "_this", "this", "loading", "headerCellStyle", "background", "color", "formInline", "dialogVisible", "ryDatas", "page", "pageSize", "page1", "pageSize1", "ry<PERSON><PERSON>ose", "bm", "xm", "total", "total1", "radioIdSelect", "smryList", "scjtlist", "mc", "id", "dqztlist", "rowdata", "regionOption", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "params", "name", "tmjssj", "columns", "type", "placeholder", "startPlaceholder", "rangeSeparator", "endPlaceholder", "format", "disabled", "icon", "mold", "tableColumns", "prop", "scopeType", "formatter", "showOverflowTooltip", "row", "column", "cellValue", "index", "opt", "find", "d", "handleColumn", "show", "Lcfwslzt", "cjrid", "loginName", "handleColumnProp", "width", "align", "handleColumnApply", "computed", "mounted", "onfwid", "getLogin<PERSON>hm", "rysclist", "zzjg", "methods", "_this2", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "userInfo", "wrap", "_context", "prev", "next", "Object", "dwzc", "sent", "yhm", "stop", "handleSizeChange", "val", "handleCurrentChange", "selectBtn", "console", "log", "shanchu", "_this3", "length", "$message", "message", "$confirm", "confirmButtonText", "cancelButtonText", "then", "_ref", "for<PERSON>ach", "_callee2", "item", "_context2", "j<PERSON>", "ztbh", "xdsbjr", "code", "_x", "apply", "arguments", "catch", "handleBtnAll", "parameter", "JSON", "parse", "stringify_default", "_this4", "_callee3", "_context3", "sqr", "kssj", "jssj", "records", "error", "sendApplay", "_this5", "_callee4", "_context4", "$router", "push", "path", "query", "scjgsj", "dqztsj", "_this6", "_callee5", "_context5", "fwlx", "api", "fwdyid", "operateBtn", "_this7", "_callee6", "res", "_context6", "datas", "undefined", "lx", "list", "slid", "_this8", "_callee7", "zzjgList", "shu", "shuList", "_context7", "zzjgmc", "childrenRegionVo", "item1", "bmm", "fbmm", "bmSelectChange", "join", "watch", "rycs_xdsbsc", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "attrs", "on", "handleBtn", "_v", "inline", "model", "size", "click", "showSelection", "<PERSON><PERSON><PERSON><PERSON>", "showIndex", "tableData", "showPagination", "currentPage", "totalCount", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "kPAgDAA,GACAC,YACAC,WAAAC,EAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAMA,IAAAC,EAAAC,KACA,OACAC,SAAA,EAEAC,iBACAC,WAAA,UACAC,MAAA,WAEAC,cACAC,eAAA,EACAC,WACAC,KAAA,EACAC,SAAA,EACAC,MAAA,EACAC,UAAA,GAEAC,UACAC,GAAA,GACAC,GAAA,IAEAC,MAAA,EACAC,OAAA,EACAC,cAAA,GACAC,YACAC,WAEAC,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAC,WAEAF,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAE,WACAC,gBACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,QACAC,KAAA,GACAC,OAAA,IAGAC,UACAC,KAAA,cACAH,KAAA,MACAL,MAAA,OACAS,YAAA,QAGAD,KAAA,YACAH,KAAA,OACAL,MAAA,SACAU,iBAAA,SACAC,eAAA,IACAC,eAAA,SACAC,OAAA,eAGAL,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,iBACAC,KAAA,YAGAR,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,uBACAC,KAAA,YAIAC,eAEAZ,KAAA,MACAa,KAAA,MACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,QACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,EACAC,qBAAA,IAGAhB,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,OACAa,KAAA,WACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,IAkBAC,IAhBAjC,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAiC,KAAA,SAAAC,GAAA,OAAAA,EAAAlC,KAAA8B,IACA,OAAAE,IAAAjC,GAAA,MAIAY,KAAA,OACAa,KAAA,WACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,IAkBAC,IAhBAjC,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAiC,KAAA,SAAAC,GAAA,OAAAA,EAAAlC,KAAA8B,IACA,OAAAE,IAAAjC,GAAA,MAKAoC,eAEAxB,KAAA,KACAS,UAAA,EACAgB,MAAA,EACAV,UAAA,SAAAE,EAAAC,GACA,UAAAD,EAAAS,UAAAT,EAAAU,OAAA5D,EAAA6D,UACA,KACA,GAAAX,EAAAS,UAAA,GAAAT,EAAAS,UAAA,GAAAT,EAAAS,SACA,UADA,KAOAG,kBACAnC,MAAA,KACAoC,MAAA,MACAC,MAAA,QAEAC,qBAEAJ,UAAA,KAGAK,YACAC,QA5NA,WA6NAlE,KAAAmE,SACAnE,KAAAoE,cACApE,KAAAqE,WACArE,KAAAsE,QAEAC,SAEAH,YAFA,WAEA,IAAAI,EAAAxE,KAAA,OAAAyE,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAA,OAAAJ,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAL,EADAE,EAAAK,KAEAb,EAAAZ,UAAAkB,EAAAQ,IAFA,wBAAAN,EAAAO,SAAAV,EAAAL,KAAAC,IAKAe,iBAPA,SAOAC,GACAzF,KAAAU,MAAA,EACAV,KAAAW,UAAA8E,EACAzF,KAAAqE,YAEAqB,oBAZA,SAYAD,GACAzF,KAAAU,MAAA+E,EACAzF,KAAAqE,YAGAsB,UAjBA,SAiBA1C,GACAjD,KAAAuB,QAAA0B,EACA2C,QAAAC,IAAA5C,IAGA6C,QAtBA,WAsBA,IAAAC,EAAA/F,KACA,GAAAA,KAAAuB,QAAAyE,OACAhG,KAAAiG,UACAC,QAAA,aACA/D,KAAA,YAGAnC,KAAAmG,SAAA,2BACAC,kBAAA,KACAC,iBAAA,KACAlE,KAAA,YACAmE,KAAA,WACA,IAAAC,EAAAR,EAAAxE,QAAAiF,SAAAD,EAAA9B,IAAAC,EAAAC,EAAAC,KAAA,SAAA6B,EAAAC,GAAA,IAAA3E,EAAA,OAAA2C,EAAAC,EAAAI,KAAA,SAAA4B,GAAA,cAAAA,EAAA1B,KAAA0B,EAAAzB,MAAA,cACAnD,GACA6E,KAAAF,EAAAE,KACAC,KAAAH,EAAAG,MAHAF,EAAAzB,KAAA,EAKAC,OAAA2B,EAAA,EAAA3B,CAAApD,GALA,OAMA,KANA4E,EAAAtB,KAMA0B,OACAhB,EAAAE,UACAC,QAAA,OACA/D,KAAA,YAEA4D,EAAA1B,YAXA,wBAAAsC,EAAApB,SAAAkB,EAAAV,MAAA,SAAAiB,GAAA,OAAAT,EAAAU,MAAAjH,KAAAkH,gBAcAC,MAAA,WACApB,EAAAE,UACA9D,KAAA,OACA+D,QAAA,aAMAkB,aAzDA,SAyDAC,EAAAX,GACA,MAAAA,EAAA1E,MACAhC,KAAA+B,OAAAuF,KAAAC,MAAAC,IAAAH,IACArH,KAAAU,MAAA,EACAV,KAAAqE,YACA,MAAAqC,EAAA1E,OACAhC,KAAA+B,QACAC,KAAA,GACAC,OAAA,MAKAoC,SAtEA,SAsEAgD,GAAA,IAAAI,EAAAzH,KAAA,OAAAyE,IAAAC,EAAAC,EAAAC,KAAA,SAAA8C,IAAA,IAAA3F,EAAAjC,EAAA,OAAA4E,EAAAC,EAAAI,KAAA,SAAA4C,GAAA,cAAAA,EAAA1C,KAAA0C,EAAAzC,MAAA,cACAnD,GACA6F,IAAAH,EAAA1F,OAAAC,KACAxB,KAAAiH,EAAA/G,MACAD,SAAAgH,EAAA9G,WAEA,MAAA8G,EAAA1F,OAAAE,SACAF,EAAA8F,KAAAJ,EAAA1F,OAAAE,OAAA,GACAF,EAAA+F,KAAAL,EAAA1F,OAAAE,OAAA,IARA0F,EAAAzC,KAAA,EAUAC,OAAA2B,EAAA,EAAA3B,CAAApD,GAVA,OAWA,MADAjC,EAVA6H,EAAAtC,MAWA0B,MACAU,EAAAvG,SAAApB,OAAAiI,QACAN,EAAAzG,OAAAlB,OAAAiB,OAEA0G,EAAAxB,SAAA+B,MAAA,WAfA,wBAAAL,EAAApC,SAAAmC,EAAAD,KAAAhD,IAmBAwD,WAzFA,WAyFA,IAAAC,EAAAlI,KAAA,OAAAyE,IAAAC,EAAAC,EAAAC,KAAA,SAAAuD,IAAA,OAAAzD,EAAAC,EAAAI,KAAA,SAAAqD,GAAA,cAAAA,EAAAnD,KAAAmD,EAAAlD,MAAA,OACAgD,EAAAG,QAAAC,MACAC,KAAA,eACAC,OACArG,KAAA,SAJA,wBAAAiG,EAAA7C,SAAA4C,EAAAD,KAAAzD,IASAgE,OAlGA,SAkGAxF,GACA,IAAAnD,OAAA,EAMA,OALAE,KAAAmB,SAAAqF,QAAA,SAAAE,GACAA,EAAArF,IAAA4B,EAAAS,WACA5D,EAAA4G,EAAAtF,MAGAtB,GAGA4I,OA5GA,SA4GAzF,GACA,IAAAnD,OAAA,EAMA,OALAE,KAAAsB,SAAAkF,QAAA,SAAAE,GACAA,EAAArF,IAAA4B,EAAAS,WACA5D,EAAA4G,EAAAtF,MAGAtB,GAEAqE,OArHA,WAqHA,IAAAwE,EAAA3I,KAAA,OAAAyE,IAAAC,EAAAC,EAAAC,KAAA,SAAAgE,IAAA,IAAA7G,EAAAjC,EAAA,OAAA4E,EAAAC,EAAAI,KAAA,SAAA8D,GAAA,cAAAA,EAAA5D,KAAA4D,EAAA3D,MAAA,cACAnD,GACA+G,KAAA,IAFAD,EAAA3D,KAAA,EAIAC,OAAA4D,EAAA,EAAA5D,CAAApD,GAJA,OAIAjC,EAJA+I,EAAAxD,KAKAO,QAAAC,IAAA/F,GACA6I,EAAAK,OAAAlJ,OAAAkJ,OANA,wBAAAH,EAAAtD,SAAAqD,EAAAD,KAAAlE,IASAwE,WA9HA,SA8HAhG,EAAAyD,GAAA,IAAAwC,EAAAlJ,KAAA,OAAAyE,IAAAC,EAAAC,EAAAC,KAAA,SAAAuE,IAAA,IAAAC,EAAAJ,EAAA,OAAAtE,EAAAC,EAAAI,KAAA,SAAAsE,GAAA,cAAAA,EAAApE,KAAAoE,EAAAnE,MAAA,UACAU,QAAAC,IAAA5C,GAEA,MAAAyD,EAHA,CAAA2C,EAAAnE,KAAA,gBAIAgE,EAAAjJ,SAAA,EAJAoJ,EAAAnE,KAAA,EAKAC,OAAA2B,EAAA,EAAA3B,EACAyB,KAAA3D,EAAA2D,OANA,OAKAwC,EALAC,EAAAhE,KAQAO,QAAAC,IAAAuD,GAKAA,EAAAxC,MACAsC,EAAAjJ,SAAA,EACAiJ,EAAAb,QAAAC,MACAC,KAAA,eACAC,OACArG,KAAA,SACAmH,MAAAF,MAKAF,EAAAjD,SAAA+B,MAAA,UAxBAqB,EAAAnE,KAAA,iBA0BA,MAAAwB,IACAsC,EAAAE,EAAAF,OACApD,QAAAC,IAAAmD,GACA,IAAAE,EAAAF,aAAAO,GAAAL,EAAAF,OACAE,EAAAjD,SAAA+B,MAAA,cAEAkB,EAAAb,QAAAC,MACAC,KAAA,iBACAC,OACAgB,GAAA,OACAC,KAAAxG,EACA+F,SACAU,KAAAzG,EAAAyG,SAtCA,yBAAAL,EAAA9D,SAAA4D,EAAAD,KAAAzE,IA8CAH,KA5KA,WA4KA,IAAAqF,EAAA3J,KAAA,OAAAyE,IAAAC,EAAAC,EAAAC,KAAA,SAAAgF,IAAA,IAAAC,EAAAC,EAAAC,EAAAN,EAAA,OAAA/E,EAAAC,EAAAI,KAAA,SAAAiF,GAAA,cAAAA,EAAA/E,KAAA+E,EAAA9E,MAAA,cAAA8E,EAAA9E,KAAA,EACAC,OAAA4D,EAAA,IAAA5D,GADA,cACA0E,EADAG,EAAA3E,KAEAsE,EAAAM,OAAAJ,EACAC,KACAH,EAAAM,OAAAzD,QAAA,SAAAE,GACA,IAAAwD,KACAP,EAAAM,OAAAzD,QAAA,SAAA2D,GACAzD,EAAA0D,KAAAD,EAAAE,OACAH,EAAA5B,KAAA6B,GACAzD,EAAAwD,sBAGAJ,EAAAxB,KAAA5B,KAEAqD,KAdAC,EAAA9E,KAAA,EAeAC,OAAA4D,EAAA,EAAA5D,GAfA,OAgBA,KADAsE,EAfAO,EAAA3E,MAgBAgF,MACAP,EAAAtD,QAAA,SAAAE,GACA,IAAAA,EAAA2D,MACAN,EAAAzB,KAAA5B,KAIA,IAAA+C,EAAAY,MACAP,EAAAtD,QAAA,SAAAE,GACAd,QAAAC,IAAAa,GACAA,EAAA2D,MAAAZ,EAAAY,MACAN,EAAAzB,KAAA5B,KAIAqD,EAAA,GAAAG,iBAAA1D,QAAA,SAAAE,GACAiD,EAAAnI,aAAA8G,KAAA5B,KAhCA,yBAAAsD,EAAAzE,SAAAqE,EAAAD,KAAAlF,IAoCA6F,eAhNA,SAgNA5D,GACAd,QAAAC,IAAAa,QACA6C,GAAA7C,IACA1G,KAAAY,SAAAC,GAAA6F,EAAA6D,KAAA,QAIAC,UCteeC,GADEC,OAFjB,WAA0B,IAAAC,EAAA3K,KAAa4K,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAahJ,KAAA,UAAAiJ,QAAA,YAAAtJ,MAAAgJ,EAAA,QAAAO,WAAA,YAA4EC,YAAA,WAAuBL,EAAA,OAAYK,YAAA,cAAwBL,EAAA,cAAmBM,OAAOlJ,QAAAyI,EAAAzI,QAAAH,OAAA4I,EAAA5I,QAA0CsJ,IAAKC,UAAAX,EAAAvD,gBAA8BuD,EAAAY,GAAA,KAAAT,EAAA,WAA4BK,YAAA,KAAAC,OAAwBI,QAAA,EAAAC,MAAAd,EAAAtK,WAAAqL,KAAA,YAAsDZ,EAAA,gBAAqBK,YAAA,OAAiBL,EAAA,aAAkBM,OAAOjJ,KAAA,SAAAuJ,KAAA,SAAAhJ,KAAA,wBAA8D2I,IAAKM,MAAAhB,EAAA7E,WAAqB6E,EAAAY,GAAA,oDAAAZ,EAAAY,GAAA,KAAAT,EAAA,gBAA4FK,YAAA,OAAiBL,EAAA,aAAkBM,OAAOjJ,KAAA,UAAAuJ,KAAA,SAAAhJ,KAAA,gBAAuD2I,IAAKM,MAAAhB,EAAA1C,cAAwB0C,EAAAY,GAAA,0DAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+FM,OAAOQ,eAAA,EAAAC,eAAA,KAAAC,WAAA,EAAAC,UAAApB,EAAAzJ,SAAAgB,QAAAyI,EAAA/H,aAAAY,aAAAmH,EAAAnH,aAAAK,iBAAA8G,EAAA9G,iBAAAmI,gBAAA,EAAAC,YAAAtB,EAAAjK,MAAAD,SAAAkK,EAAAhK,UAAAuL,WAAAvB,EAAA3J,QAAuRqK,IAAKpC,WAAA0B,EAAA1B,WAAAtD,UAAAgF,EAAAhF,UAAAD,oBAAAiF,EAAAjF,oBAAAF,iBAAAmF,EAAAnF,qBAA6I,MAE9zC2G,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACE/M,EACAkL,GATF,EAVA,SAAA8B,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/179.44f3703c4f589fae2181.js", "sourcesContent": ["<template>\r\n    <div class=\"bg_con\" v-loading=\"loading\">\r\n        <div class=\"container\">\r\n            <BaseHeader :columns=\"columns\" :params=\"params\" @handleBtn=\"handleBtnAll\"></BaseHeader>\r\n            <el-form :inline=\"true\" :model=\"formInline\" size=\"medium\" class=\"fr\">\r\n                <el-form-item class=\"fr\">\r\n                    <el-button type=\"danger\" size=\"medium\" @click=\"shanchu\" icon=\"el-icon-delete-solid\">\r\n                        删除\r\n                    </el-button>\r\n                </el-form-item>\r\n                <el-form-item class=\"fr\">\r\n                    <el-button type=\"success\" size=\"medium\" @click=\"sendApplay\" icon=\"el-icon-plus\">\r\n                        审查申请\r\n                    </el-button>\r\n                </el-form-item>\r\n            </el-form>\r\n            <!-- 查询条件以及操作按钮end -->\r\n            <!-- 涉密人员任用审查列表start -->\r\n            <BaseTable :showSelection=true :selectionWidth=\"'55'\" :showIndex=true :tableData=\"smryList\"\r\n                :columns=\"tableColumns\" :handleColumn=\"handleColumn\" :handleColumnProp=\"handleColumnProp\"\r\n                :showPagination=true :currentPage=\"page1\" :pageSize=\"pageSize1\" :totalCount=\"total1\"\r\n                @operateBtn=\"operateBtn\" @selectBtn=\"selectBtn\" @handleCurrentChange=\"handleCurrentChange\"\r\n                @handleSizeChange=\"handleSizeChange\">\r\n            </BaseTable>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport {\r\n    getZzjgList,\r\n    getSpYhxxPage,\r\n    getLoginInfo,\r\n    getFwdyidByFwlx,\r\n    getZpBySmryid,\r\n} from '../../../../api/index'\r\nimport {\r\n} from '../../../../api/ztjs'\r\nimport {\r\n    selectCsglXdsbjrPage,\r\n    selectCsglXdsbjrByJlid,\r\n    seleteCsglXdwpjrqdByspid,\r\n    deleteCsglXdsbjr,\r\n} from '../../../../api/xdsbjr'\r\nimport {\r\n    getUserInfo,\r\n} from '../../../../api/dwzc'\r\nimport BaseHeader from '../../../components/common/baseHeader.vue'\r\nimport BaseTable from '../../../components/common/baseTable.vue'\r\nexport default {\r\n    components: {\r\n        BaseHeader,\r\n        BaseTable\r\n    },\r\n    props: {},\r\n    data() {\r\n        return {\r\n            loading: false,\r\n            // table 行样式\r\n            headerCellStyle: {\r\n                background: '#EEF7FF',\r\n                color: '#4D91F8'\r\n            },\r\n            formInline: {}, // 搜索条件\r\n            dialogVisible: false, // 发起申请弹框\r\n            ryDatas: [], // 弹框人员选择\r\n            page: 1, // 弹框人员当前页\r\n            pageSize: 5, // 弹框人员每页条数\r\n            page1: 1, // 弹框人员当前页\r\n            pageSize1: 10, // 弹框人员每页条数\r\n            // 弹框人员选择条件\r\n            ryChoose: {\r\n                'bm': '',\r\n                'xm': ''\r\n            },\r\n            total: 0, // 弹框人员总数\r\n            total1: 0, // 弹框人员总数\r\n            radioIdSelect: '', // 弹框人员单选\r\n            smryList: [], //页面数据\r\n            scjtlist: [ //审查状态数据\r\n                {\r\n                    mc: \"审批中\",\r\n                    id: 0\r\n                },\r\n                {\r\n                    mc: \"通过\",\r\n                    id: 1\r\n                },\r\n                {\r\n                    mc: \"已驳回\",\r\n                    id: 2\r\n                },\r\n                {\r\n                    mc: \"草稿\",\r\n                    id: 3\r\n                }\r\n            ],\r\n            dqztlist: [ //当前状态数据\r\n                {\r\n                    mc: \"审批中\",\r\n                    id: 0\r\n                },\r\n                {\r\n                    mc: \"已结束\",\r\n                    id: 1\r\n                },\r\n                {\r\n                    mc: \"已驳回\",\r\n                    id: 2\r\n                },\r\n                {\r\n                    mc: \"草稿\",\r\n                    id: 3\r\n                }\r\n            ],\r\n            rowdata: [], //列表选中的值\r\n            regionOption: [], // 部门下拉\r\n            regionParams: {\r\n                label: 'label', //这里可以配置你们后端返回的属性\r\n                value: 'label',\r\n                children: 'childrenRegionVo',\r\n                expandTrigger: 'click',\r\n                checkStrictly: true\r\n            }, //地域信息配置参数\r\n            // 查询条件\r\n            params: {\r\n                name: '',\r\n                tmjssj: ''\r\n            },\r\n            // 查询条件以及功能按钮\r\n            columns: [{\r\n                type: 'searchInput',\r\n                name: '申请人',\r\n                value: 'name',\r\n                placeholder: '申请人',\r\n            },\r\n            {\r\n                type: 'dataRange',\r\n                name: '审查时间',\r\n                value: 'tmjssj',\r\n                startPlaceholder: '审查起始时间',\r\n                rangeSeparator: '至',\r\n                endPlaceholder: '审查结束时间',\r\n                format: 'yyyy-MM-dd'\r\n            },\r\n            {\r\n                type: 'button',\r\n                name: '查询',\r\n                disabled: false,\r\n                icon: 'el-icon-search',\r\n                mold: 'primary'\r\n            },\r\n            {\r\n                type: 'button',\r\n                name: '重置',\r\n                disabled: false,\r\n                icon: 'el-icon-circle-close',\r\n                mold: 'warning'\r\n            }\r\n            ],\r\n            // table项\r\n            tableColumns: [\r\n                {\r\n                    name: '申请人',\r\n                    prop: 'sqr',\r\n                    scopeType: 'text',\r\n                    formatter: false\r\n                },\r\n                {\r\n                    name: '申请人部门',\r\n                    prop: 'szbm',\r\n                    scopeType: 'text',\r\n                    formatter: false\r\n                },\r\n                {\r\n                    name: '进入事由',\r\n                    prop: 'wqyy',\r\n                    scopeType: 'text',\r\n                    formatter: false,\r\n                    showOverflowTooltip: true\r\n                },\r\n                {\r\n                    name: '审查时间',\r\n                    prop: 'cjsj',\r\n                    scopeType: 'text',\r\n                    formatter: false\r\n                },\r\n                {\r\n                    name: '审查结果',\r\n                    prop: 'Lcfwslzt',\r\n                    scopeType: 'text',\r\n                    formatter: (row, column, cellValue, index) => {\r\n                        const options = [\r\n                            {\r\n                                mc: \"审批中\",\r\n                                id: 0\r\n                            },\r\n                            {\r\n                                mc: \"通过\",\r\n                                id: 1\r\n                            },\r\n                            {\r\n                                mc: \"已驳回\",\r\n                                id: 2\r\n                            },\r\n                            {\r\n                                mc: \"草稿\",\r\n                                id: 3\r\n                            }\r\n                        ]\r\n                        const opt = options.find(d => d.id === cellValue)\r\n                        return opt ? opt.mc : ''\r\n                    }\r\n                },\r\n                {\r\n                    name: '当前状态',\r\n                    prop: 'Lcfwslzt',\r\n                    scopeType: 'text',\r\n                    formatter: (row, column, cellValue, index) => {\r\n                        const options = [\r\n                            {\r\n                                mc: \"审批中\",\r\n                                id: 0\r\n                            },\r\n                            {\r\n                                mc: \"已结束\",\r\n                                id: 1\r\n                            },\r\n                            {\r\n                                mc: \"已驳回\",\r\n                                id: 2\r\n                            },\r\n                            {\r\n                                mc: \"草稿\",\r\n                                id: 3\r\n                            }\r\n                        ]\r\n                        const opt = options.find(d => d.id === cellValue)\r\n                        return opt ? opt.mc : ''\r\n                    }\r\n                }\r\n            ],\r\n            // table操作按钮\r\n            handleColumn: [\r\n                {\r\n                    name: '编辑',\r\n                    disabled: false,\r\n                    show: true,\r\n                    formatter: (row, column) => {\r\n                        if (row.Lcfwslzt == 3 && row.cjrid == this.loginName) {\r\n                            return '编辑'\r\n                        } else if (row.Lcfwslzt == 0 || row.Lcfwslzt == 1 || row.Lcfwslzt == 2) {\r\n                            return '查看'\r\n                        }\r\n                    }\r\n                }\r\n            ],\r\n            // 表格的操作\r\n            handleColumnProp: {\r\n                label: '操作',\r\n                width: '230',\r\n                align: 'left'\r\n            },\r\n            handleColumnApply: [],\r\n            // 当前登录人的用户名\r\n            loginName: ''\r\n        }\r\n    },\r\n    computed: {},\r\n    mounted() {\r\n        this.onfwid()\r\n        this.getLoginYhm() // 获取当前登录人姓名\r\n        this.rysclist() // 任用审查数据获取\r\n        this.zzjg() // 获取组织机构所有部门下拉\r\n    },\r\n    methods: {\r\n        // 获取当前登录人姓名\r\n        async getLoginYhm() {\r\n            let userInfo = await getUserInfo()\r\n            this.loginName = userInfo.yhm\r\n        },\r\n        //分页\r\n        handleSizeChange(val) {\r\n            this.page1 = 1\r\n            this.pageSize1 = val\r\n            this.rysclist()\r\n        },\r\n        handleCurrentChange(val) {\r\n            this.page1 = val\r\n            this.rysclist()\r\n        },\r\n        // table复选集合\r\n        selectBtn(row) {\r\n            this.rowdata = row\r\n            console.log(row);\r\n        },\r\n        //删除\r\n        shanchu() {\r\n            if (this.rowdata.length == 0) {\r\n                this.$message({\r\n                    message: '未选择想要删除的数据',\r\n                    type: 'warning'\r\n                });\r\n            } else {\r\n                this.$confirm('此操作将永久删除该申请, 是否继续?', '提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    this.rowdata.forEach(async (item) => {\r\n                        let params = {\r\n                            jlid: item.jlid,\r\n                            ztbh: item.ztbh,\r\n                        }\r\n                        let res = await deleteCsglXdsbjr(params)\r\n                        if (res.code == 10000) {\r\n                            this.$message({\r\n                                message: '删除成功',\r\n                                type: 'success'\r\n                            })\r\n                            this.rysclist()\r\n                        }\r\n                    })\r\n                }).catch(() => {\r\n                    this.$message({\r\n                        type: 'info',\r\n                        message: '已取消删除'\r\n                    });\r\n                });\r\n            }\r\n        },\r\n        // 点击公共头部按钮事件\r\n        handleBtnAll(parameter, item) {\r\n            if (item.name == '查询') {\r\n                this.params = JSON.parse(JSON.stringify(parameter))\r\n                this.page1 = 1\r\n                this.rysclist()\r\n            } else if (item.name == '重置') {\r\n                this.params = {\r\n                    name: '',\r\n                    tmjssj: ''\r\n                }\r\n            }\r\n        },\r\n        //任用审查数据获取\r\n        async rysclist(parameter) {\r\n            let params = {\r\n                sqr: this.params.name,\r\n                page: this.page1,\r\n                pageSize: this.pageSize1\r\n            }\r\n            if (this.params.tmjssj != null) {\r\n                params.kssj = this.params.tmjssj[0]\r\n                params.jssj = this.params.tmjssj[1]\r\n            }\r\n            let data = await selectCsglXdsbjrPage(params)\r\n            if (data.code == 10000) {\r\n                this.smryList = data.data.records\r\n                this.total1 = data.data.total\r\n            } else {\r\n                this.$message.error('数据获取失败！')\r\n            }\r\n        },\r\n        // 发起申请\r\n        async sendApplay() {\r\n            this.$router.push({\r\n                path: '/xdsbscTable',\r\n                query: {\r\n                    type: 'add',\r\n                }\r\n            })\r\n        },\r\n        //审查状态数据回想\r\n        scjgsj(row) {\r\n            let data;\r\n            this.scjtlist.forEach(item => {\r\n                if (item.id == row.Lcfwslzt) {\r\n                    data = item.mc\r\n                }\r\n            })\r\n            return data\r\n        },\r\n        //当前状态数据回想\r\n        dqztsj(row) {\r\n            let data;\r\n            this.dqztlist.forEach(item => {\r\n                if (item.id == row.Lcfwslzt) {\r\n                    data = item.mc\r\n                }\r\n            })\r\n            return data\r\n        },\r\n        async onfwid() {\r\n            let params = {\r\n                fwlx: 29\r\n            }\r\n            let data = await getFwdyidByFwlx(params)\r\n            console.log(data);\r\n            this.fwdyid = data.data.fwdyid\r\n        },\r\n        // 功能操作按钮\r\n        async operateBtn(row, item) {\r\n            console.log(row);\r\n            // 编辑方法\r\n            if (item == '编辑') {\r\n                this.loading = true\r\n                let res = await selectCsglXdsbjrByJlid({\r\n                    'jlid': row.jlid\r\n                })\r\n                console.log(res)\r\n                // let res1 = await seleteCsglXdwpjrqdByspid({\r\n                //     'spid': row.jlid\r\n                // })\r\n                // console.log(res1);\r\n                if (res.jlid) {\r\n                    this.loading = false\r\n                    this.$router.push({\r\n                        path: '/xdsbscTable',\r\n                        query: {\r\n                            type: 'update',\r\n                            datas: res,\r\n                            // ztqs: res1\r\n                        }\r\n                    })\r\n                } else {\r\n                    this.$message.error('任务不匹配！')\r\n                }\r\n            } else if (item == '查看') {  // 查看方法\r\n                let fwdyid = this.fwdyid\r\n                console.log(fwdyid);\r\n                if (this.fwdyid == '' || this.fwdyid == undefined) {\r\n                    this.$message.error('请到流程管理进行配置');\r\n                } else {\r\n                    this.$router.push({\r\n                        path: '/xdsbjrblxxscb',\r\n                        query: {\r\n                            lx: '载体签收',\r\n                            list: row,\r\n                            fwdyid: fwdyid,\r\n                            slid: row.slid\r\n                        }\r\n                    })\r\n                }\r\n\r\n            }\r\n        },\r\n        //全部组织机构List\r\n        async zzjg() {\r\n            let zzjgList = await getZzjgList()\r\n            this.zzjgmc = zzjgList\r\n            let shu = []\r\n            this.zzjgmc.forEach(item => {\r\n                let childrenRegionVo = []\r\n                this.zzjgmc.forEach(item1 => {\r\n                    if (item.bmm == item1.fbmm) {\r\n                        childrenRegionVo.push(item1)\r\n                        item.childrenRegionVo = childrenRegionVo\r\n                    }\r\n                });\r\n                shu.push(item)\r\n            })\r\n            let shuList = []\r\n            let list = await getLoginInfo()\r\n            if (list.fbmm == '') {\r\n                shu.forEach(item => {\r\n                    if (item.fbmm == '') {\r\n                        shuList.push(item)\r\n                    }\r\n                })\r\n            }\r\n            if (list.fbmm != '') {\r\n                shu.forEach(item => {\r\n                    console.log(item);\r\n                    if (item.fbmm == list.fbmm) {\r\n                        shuList.push(item)\r\n                    }\r\n                })\r\n            }\r\n            shuList[0].childrenRegionVo.forEach(item => {\r\n                this.regionOption.push(item)\r\n            })\r\n        },\r\n        // 发起申请选择人员 人员下拉\r\n        bmSelectChange(item) {\r\n            console.log(item)\r\n            if (item != undefined) {\r\n                this.ryChoose.bm = item.join('/')\r\n            }\r\n        }\r\n    },\r\n    watch: {\r\n\r\n    }\r\n}\r\n\r\n</script>\r\n  \r\n<style scoped>\r\n.fl {\r\n    float: left;\r\n}\r\n\r\n.fr {\r\n    float: right;\r\n}\r\n\r\n.container {\r\n    width: 100%;\r\n    position: relative;\r\n    overflow: hidden;\r\n    height: 100%;\r\n    /* box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13); */\r\n    border-radius: 8px;\r\n}\r\n\r\n.bg_con {\r\n    width: 100%;\r\n    height: calc(100% - 38px);\r\n}\r\n\r\n/deep/.mhcx .el-form-item {\r\n    margin-top: 5px;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.widthw {\r\n    width: 6vw;\r\n}\r\n\r\n/* 发起申请弹框 */\r\n.dlFqsqContainer {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n    font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n    width: 150px;\r\n    margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n    margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n    height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n    margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n    margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n    margin-top: 20px;\r\n}\r\n\r\n.spImg {\r\n    width: 15px;\r\n}\r\n\r\n.baseTable {\r\n    margin-top: 20px;\r\n    /* height: 400px!important; */\r\n}\r\n</style>\r\n  \n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/rycs/xdsbsc.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"bg_con\"},[_c('div',{staticClass:\"container\"},[_c('BaseHeader',{attrs:{\"columns\":_vm.columns,\"params\":_vm.params},on:{\"handleBtn\":_vm.handleBtnAll}}),_vm._v(\" \"),_c('el-form',{staticClass:\"fr\",attrs:{\"inline\":true,\"model\":_vm.formInline,\"size\":\"medium\"}},[_c('el-form-item',{staticClass:\"fr\"},[_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"medium\",\"icon\":\"el-icon-delete-solid\"},on:{\"click\":_vm.shanchu}},[_vm._v(\"\\n                    删除\\n                \")])],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"fr\"},[_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"medium\",\"icon\":\"el-icon-plus\"},on:{\"click\":_vm.sendApplay}},[_vm._v(\"\\n                    审查申请\\n                \")])],1)],1),_vm._v(\" \"),_c('BaseTable',{attrs:{\"showSelection\":true,\"selectionWidth\":'55',\"showIndex\":true,\"tableData\":_vm.smryList,\"columns\":_vm.tableColumns,\"handleColumn\":_vm.handleColumn,\"handleColumnProp\":_vm.handleColumnProp,\"showPagination\":true,\"currentPage\":_vm.page1,\"pageSize\":_vm.pageSize1,\"totalCount\":_vm.total1},on:{\"operateBtn\":_vm.operateBtn,\"selectBtn\":_vm.selectBtn,\"handleCurrentChange\":_vm.handleCurrentChange,\"handleSizeChange\":_vm.handleSizeChange}})],1)])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-45e8b437\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/rycs/xdsbsc.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-45e8b437\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./xdsbsc.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./xdsbsc.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./xdsbsc.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-45e8b437\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./xdsbsc.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-45e8b437\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/rycs/xdsbsc.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}