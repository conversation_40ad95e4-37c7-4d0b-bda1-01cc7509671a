
// import {
//     getYz
// } from "../../../db/syszjb";
// import { getDwxx } from "../../../db/dwxxDb"
// import {
//     writeTrajectoryLog
// } from '../../../utils/logUtils'
import {
    getXtcsPage
  } from '../../../api/cssz'
import {
    // 一键生成
    scSmryHistoryDatas,
    scSmcsHistoryDatas,
    scSmsbHistoryDatas,
    scSmztHistoryDatas,
    scSmsxHistoryDatas,
    reviseDbgzStatus
} from '../../../api/lstz'
import { getTzInfoDatas, getDbgzStatus, getSfDbgzRqqj, getSfDbgzSmryRqqj, getSfDbgzDmsxRqqj } from '../../../api/dbgz'
export default {
    data() {
        return {
            allCountsLength: {},
            statusArr: [],
            nowsYear: 2000, // 当前年
            zcxxIsPerfectShow: false, // 注册信息
            zcxxPerfectCount: 0, // 注册信息需要完善的个数
            dadbShow: false,
            downloadShow: false,
            download1Show: false,
            // 日常各模块
            rcUpdateCounts: 0, // 管理信息需要完善的个数
            zczpUpdateCounts: 0, // 自查自评需要完善的个数
            rcglDivShow: true, // 日常模块显示
            page: 1,
            pageSize: 10,
            zzxxIsPerfectShow: false,

            // 日常管理 完善保密制度、完善组织机构、完善人员信息、完善场所信息、完善设备信息、完善载体信息 显隐     
            bmzdIsPerfectShow: false,
            zzjgIsPerfectShow: false,
            ryxxIsPerfectShow: false,
            csxxIsPerfectShow: false,
            sbxxIsPerfectShow: false,
            ztxxIsPerfectShow: false,

            zczp1IsPerfectShow: false,
            zczp2IsPerfectShow: false,
            zczp3IsPerfectShow: false,
            zczp4IsPerfectShow: false,
            // 获取各表数据长度
            zdglListLength: 0,
            zzjgListLength: 0,
            smgwListLength: 0,
            zgsmryHzListLength: 0,
            ryxzHzListLength: 0,
            rynjbgHzListLength: 0,
            lglzListLength: 0,
            csglListLength: 0,
            csbgListLength: 0,
            smjsjListLength: 0,
            fsmjsjListLength: 0,
            ydccjzListLength: 0,
            bgzdhsbListLength: 0,
            fsmbgzdhsbListLength: 0,
            wlsbListLength: 0,
            fwlsbListLength: 0,
            aqcpListLength: 0,
            smztListLength: 0,
            dmzrrListLength: 0,
            dmsqListLength: 0,
            gjmmsxListLength: 0,
            dmpxListLength: 0,
            dmqkndtjListLength: 0,
            bmqsxqdqkListLength: 0,
            zfcgxmqkListLength: 0,
            // 重新生成按钮控制
            smryCxscShow: false,
            smcsCxscShow: false,
            smsbCxscShow: false,
            smztCxscShow: false,
            smsxCxscShow: false,
            // 一键生成生成年份
            smrySctime: true,
            smcsSctime: true,
            smsbSctime: true,
            smztSctime: true,
            smsxSctime: true,

            // 获取各模块状态
            smryStatus: {},
            smcsStatus: {},
            smsbStatus: {},
            smztStatus: {},
            smsxStatus: {},
            // 开始时间结束时间
            dbgzDateStart: '',
            dbgzDateEnd: ''
        }
    },
    computed: {
        dbscsJcomputed(){
            return `系统管理员可以设置日期范围`
        },
        // 涉密人员一键生成
        smryTzScShow() {
            return !this.smryStatus.smgwdj && this.dadbShow == true && this.smgwListLength > 0 || !this.smryStatus.smryhz && this.dadbShow == true && this.zgsmryHzListLength > 0 || !this.smryStatus.ryxzhz && this.dadbShow == true && this.ryxzHzListLength > 0 || !this.smryStatus.rynjbghz && this.dadbShow == true && this.rynjbgHzListLength > 0 || !this.smryStatus.lghz && this.dadbShow == true && this.lglzListLength > 0
        },
        // 涉密人员重新生成
        cxscSmryTzScShow() {
            return this.dadbShow == true && this.smgwListLength > 0 && this.smryCxscShow ||
                this.dadbShow == true && this.zgsmryHzListLength > 0 && this.smryCxscShow ||
                this.dadbShow == true && this.ryxzHzListLength > 0 && this.smryCxscShow ||
                this.dadbShow == true && this.rynjbgHzListLength > 0 && this.smryCxscShow ||
                this.dadbShow == true && this.lglzListLength > 0 && this.smryCxscShow
        },
        // 涉密场所一键生成
        smcsTzScShow() {
            return !this.smcsStatus.smcsdj && this.dadbShow == true && this.csglListLength > 0 || !this.smcsStatus.csbgdj && this.dadbShow == true && this.csbgListLength > 0
        },
        // 涉密场所重新生成
        cxscSmcsTzScShow() {
            return this.dadbShow == true && this.csglListLength > 0 && this.smcsCxscShow ||
                this.dadbShow == true && this.csbgListLength > 0 && this.smcsCxscShow
        },
        // 涉密设备一键生成
        smsbTzScShow() {
            return !this.smsbStatus.smjsjtz && this.dadbShow == true && this.smjsjListLength > 0 || !this.smsbStatus.fsmjsjtz && this.dadbShow == true && this.fsmjsjListLength > 0 || !this.smsbStatus.ydccjztz && this.dadbShow == true && this.ydccjzListLength > 0 || !this.smsbStatus.bgzdhsbtz && this.dadbShow == true && this.bgzdhsbListLength > 0 || !this.smsbStatus.fsmbgzdhsbtz && this.dadbShow == true && this.fsmbgzdhsbListLength > 0 || !this.smsbStatus.wlsbtz && this.dadbShow == true && this.wlsbListLength > 0 || !this.smsbStatus.fwlsbtz && this.dadbShow == true && this.fwlsbListLength > 0 || !this.smsbStatus.aqcptz && this.dadbShow == true && this.aqcpListLength > 0
        },
        // 涉密设备重新生成
        cxscSmsbTzScShow() {
            return this.dadbShow == true && this.smjsjListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.fsmjsjListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.ydccjzListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.bgzdhsbListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.fsmbgzdhsbListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.wlsbListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.fwlsbListLength > 0 && this.smsbCxscShow ||
                this.dadbShow == true && this.aqcpListLength > 0 && this.smsbCxscShow
        },
        // 涉密载体一键生成
        smztTzScShow() {
            return !this.smztStatus.smzttz && this.dadbShow == true && this.smztListLength > 0
        },
        // 涉密载体重新生成
        cxscSmztTzScShow() {
            return this.dadbShow == true && this.smztListLength > 0 && this.smztCxscShow
        },
        // 涉密事项一键生成
        smsxTzScShow() {
            return !this.smsxStatus.dmzrr && this.dadbShow == true && this.dmzrrListLength > 0 || !this.smsxStatus.dmsq && this.dadbShow == true && this.dmsqListLength > 0 || !this.smsxStatus.gjmmsx && this.dadbShow == true && this.gjmmsxListLength > 0 || !this.smsxStatus.dmpx && this.dadbShow == true && this.dmpxListLength > 0 || !this.smsxStatus.dmqkndtj && this.dadbShow == true && this.dmqkndtjListLength > 0 || !this.smsxStatus.bmqsxqdqk && this.dadbShow == true && this.bmqsxqdqkListLength > 0 || !this.smsxStatus.zfcgxmqk && this.dadbShow == true && this.zfcgxmqkListLength > 0
        },
        // 涉密事项重新生成
        cxscSmsxTzScShow() {
            return this.dadbShow == true && this.dmzrrListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.dmsqListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.gjmmsxListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.dmpxListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.dmqkndtjListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.bmqsxqdqkListLength > 0 && this.smsxCxscShow ||
                this.dadbShow == true && this.zfcgxmqkListLength > 0 && this.smsxCxscShow
        },
        // 涉密人员整体显示
        smryAllShow() {
            let allLength = this.smgwListLength + this.zgsmryHzListLength + this.ryxzHzListLength + this.rynjbgHzListLength + this.lglzListLength
            return allLength == 0 ? false : true
        },
        // 涉密场所整体显示
        smcsAllShow() {
            let allLength = this.csglListLength + this.csbgListLength
            return allLength == 0 ? false : true
        },
        // 涉密设备整体显示
        smsbAllShow() {
            let allLength = this.smjsjListLength + this.fsmjsjListLength + this.ydccjzListLength + this.bgzdhsbListLength + this.fsmbgzdhsbListLength + this.wlsbListLength + this.fwlsbListLength + this.aqcpListLength
            return allLength == 0 ? false : true
        },
        // 涉密事项整体显示
        smsxAllShow() {
            let allLength = this.dmzrrListLength + this.dmsqListLength + this.gjmmsxListLength + this.dmpxListLength + this.dmqkndtjListLength + this.bmqsxqdqkListLength + this.zfcgxmqkListLength
            return allLength == 0 ? false : true
        },
        // 涉密人员上报显示
        smryDownLoadShow() {
            return this.downloadShow == true && this.smgwListLength > 0 || this.downloadShow == true && this.zgsmryHzListLength > 0 || this.downloadShow == true && this.ryxzHzListLength > 0 || this.downloadShow == true && this.rynjbgHzListLength > 0 || this.downloadShow == true && this.lglzListLength > 0
        },
        // 涉密事项上报显示
        smsxDownloadShow() {
            return this.download1Show == true && this.dmzrrListLength > 0 || this.download1Show == true && this.dmsqListLength > 0 || this.download1Show == true && this.gjmmsxListLength > 0 || this.download1Show == true && this.dmpxListLength > 0 || this.download1Show == true && this.dmqkndtjListLength > 0 || this.download1Show == true && this.bmqsxqdqkListLength > 0 || this.download1Show == true && this.zfcgxmqkListLength > 0
        },
    },
    methods: {
        ceshi() {
            console.log(this.smryTzScShow)
            console.log(this.smryStatus.isAgain)
        },
        // 获取待办工作状态
        async getDbgzSattus() {
            let arr = await getDbgzStatus()
            let res = JSON.parse(JSON.stringify(arr.data))
            console.log(res)
            this.smryStatus = JSON.parse(JSON.stringify(res))[0]
            this.smcsStatus = JSON.parse(JSON.stringify(res))[1]
            this.smsbStatus = JSON.parse(JSON.stringify(res))[2]
            this.smztStatus = JSON.parse(JSON.stringify(res))[3]
            this.smsxStatus = JSON.parse(JSON.stringify(res))[4]
        },
        getTime(date) {
            let Y = date.getFullYear(),
                M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1),
                D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()),
                h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()),
                m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()),
                s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
            return Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s
        },
        // 一键生成年度涉密人员相关台账
        async yjscClick(name, type) {
            const loading = this.$loading({
                lock: true,
                text: '历史台账生成中，请稍后...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            let nowdate = this.getTime(new Date())
            let updataItem = {}
            switch (name) {
                case 'smry':
                    let isAgain = 0
                    if (type == 'yjsc') {
                        // 添加日志
                        // let paramsLog = {
                        //     xyybs: 'mk_dbgzrz',
                        //     id: '-1',
                        //     ymngnmc: '一键生成年度涉密人员相关台账'
                        // }
                        // writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smrySctime', new Date().getFullYear().toString())
                        this.smrySctime = false
                    } else if (type == 'cxsc') {
                        //写入日志 
                    }
                    isAgain = 1
                    let result = await scSmryHistoryDatas({ 'tznf': this.nowsYear.toString() })
                    if (result.code == 10000) {
                        updataItem = {
                            "id": 1,
                            "smgwdj": true,
                            "smryhz": true,
                            "ryxzhz": true,
                            "rynjbghz": true,
                            "lghz": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": isAgain
                        }
                        this.smryStatus = updataItem
                        this.$message({
                            message: '涉密人员相关台账生成成功',
                            type: 'success'
                        });
                    } else {
                        this.$message({
                            message: '涉密人员相关台账生成失败',
                            type: 'error'
                        });
                    }
                    this.smryCxscShow = true
                    loading.close()
                    break
                case 'smcs':
                    if (type == 'yjsc') {
                        // 添加日志
                        // let paramsLog = {
                        //     xyybs: 'mk_dbgzrz',
                        //     id: '-1',
                        //     ymngnmc: '一键生成年度涉密场所相关台账'
                        // }
                        // writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smcsSctime', new Date().getFullYear().toString())
                        this.smcsSctime = false
                    } else if (type == 'cxsc') {
                        //写入日志 
                    }
                    isAgain = 1
                    let resultSmcs = await scSmcsHistoryDatas({ 'tznf': this.nowsYear.toString() })
                    if (resultSmcs.code == 10000) {
                        updataItem = {
                            "id": 2,
                            "smcsdj": true,
                            "csbgdj": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": isAgain
                        }
                        this.smcsStatus = updataItem
                        this.$message({
                            message: '涉密场所相关台账生成成功',
                            type: 'success'
                        });
                    } else {
                        this.$message({
                            message: '涉密场所相关台账生成失败',
                            type: 'error'
                        });
                    }
                    this.smcsCxscShow = true
                    loading.close()
                    break
                case 'smsb':
                    if (type == 'yjsc') {
                        // 添加日志
                        // let paramsLog = {
                        //     xyybs: 'mk_dbgzrz',
                        //     id: '-1',
                        //     ymngnmc: '一键生成年度涉密设备相关台账'
                        // }
                        // writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smsbSctime', new Date().getFullYear().toString())
                        this.smsbSctime = false
                    } else if (type == 'cxsc') {
                        //写入日志 
                    }
                    isAgain = 1
                    let resultSmsb = await scSmsbHistoryDatas({ 'tznf': this.nowsYear.toString() })
                    if (resultSmsb.code == 10000) {
                        updataItem = {
                            "id": 3,
                            "smjsjtz": true,
                            "fsmjsjtz": true,
                            "ydccjztz": true,
                            "bgzdhsbtz": true,
                            "fsmbgzdhsbtz": true,
                            "wlsbtz": true,
                            "fwlsbtz": true,
                            "aqcptz": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": isAgain
                        }
                        this.smsbStatus = updataItem
                        this.$message({
                            message: '涉密设备相关台账生成成功',
                            type: 'success'
                        });
                    } else {
                        this.$message({
                            message: '涉密设备相关台账生成失败',
                            type: 'error'
                        });
                    }
                    this.smsbCxscShow = true
                    loading.close()
                    break
                case 'smzt':
                    if (type == 'yjsc') {
                        // 添加日志
                        // let paramsLog = {
                        //     xyybs: 'mk_dbgzrz',
                        //     id: '-1',
                        //     ymngnmc: '一键生成年度涉密载体相关台账'
                        // }
                        // writeTrajectoryLog(paramsLog)
                        window.localStorage.setItem('smztSctime', new Date().getFullYear().toString())
                        this.smztSctime = false
                    } else if (type == 'cxsc') {
                        //写入日志 \
                    }
                    isAgain = 1
                    // 获取涉密载体数据
                    let resultSmzt = await scSmztHistoryDatas({ 'tznf': this.nowsYear.toString() })
                    if (resultSmzt.code == 10000) {
                        updataItem = {
                            "id": 4,
                            "smzttz": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": isAgain
                        }
                        this.smztStatus = updataItem
                        this.$message({
                            message: '涉密载体相关台账生成成功',
                            type: 'success'
                        });
                    } else {
                        this.$message({
                            message: '涉密载体相关台账生成失败',
                            type: 'error'
                        });
                    }
                    this.smztCxscShow = true
                    loading.close()
                    break
                case 'smsx':
                    if (type == 'yjsc') {
                        // 添加日志
                        // let paramsLog = {
                        //     xyybs: 'mk_dbgzrz',
                        //     id: '-1',
                        //     ymngnmc: '一键生成年度涉密事项相关台账'
                        // }
                        // writeTrajectoryLog(paramsLog)
                    } else if (type == 'cxsc') {
                        //写入日志 
                    }
                    isAgain = 1
                    let resultSmsx = await scSmsxHistoryDatas({ 'tznf': this.nowsYear.toString() })
                    if (resultSmsx.code == 10000) {
                        updataItem = {
                            "id": 5,
                            "dmzrr": true,
                            "dmsq": true,
                            "gjmmsx": true,
                            "dmpx": true,
                            "dmqkndtj": true,
                            "bmqsxqdqk": true,
                            "zfcgxmqk": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": isAgain
                        }
                        this.smsxStatus = updataItem
                        this.$message({
                            message: '涉密相关事项台账生成成功',
                            type: 'success'
                        });
                    } else {
                        this.$message({
                            message: '涉密相关事项台账生成失败',
                            type: 'error'
                        });
                    }
                    this.smsxCxscShow = true
                    loading.close()
                    break
                default:
                    console.log(0)
            }
            await reviseDbgzStatus(updataItem)
            // this.getDbgzSattus(this.statusArr)
        },
        zcxxIsPerfect() {
            // 获取当前注册信息
            let resListArr = getDwxx()
            let lock = 0
            for (let key in resListArr[0]) {
                if (resListArr[0][key] == '') {
                    lock = lock + 1
                }
            }
            // 获取需要完善有几项
            this.zcxxPerfectCount = lock
            this.zcxxIsPerfectShow = lock == 1 ? true : false
        },
        // 日常管理---资质信息（资质单位）
        zzxxIsPerfect() {
            // let resListArr = getJgxx()
            // this.zzyhIsPerfectShow =  resListArr.length == 0 ? true : false
        },
        // 日常管理---保密制度
        bmzdIsPerfect() {
            if (this.zdglListLength == 0) {
                this.bmzdIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
                console.log(this.rcUpdateCounts)
            }
        },
        // 日常管理---组织机构
        zzjgIsPerfect() {
            if (this.zzjgListLength == 0 || this.zzjgListLength == 1) {
                this.zzjgIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
                console.log(this.rcUpdateCounts)
            }
        },
        // 日常管理---人员信息
        ryxxIsPerfect() {
            if (this.zgsmryHzListLength == 0 || this.smgwListLength == 0 || this.ryxzHzListLength == 0 || this.rynjbgHzListLength == 0 || this.lglzListLength == 0) {
                this.ryxxIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
                console.log(this.rcUpdateCounts)
            }
        },
        // 日常管理---场所信息
        csxxIsPerfect() {
            if (this.csglListLength == 0 || this.csbgListLength == 0) {
                this.csxxIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
                console.log(this.rcUpdateCounts)
            }
        },
        // 日常管理---设备信息
        sbxxIsPerfect() {
            if (this.smjsjListLength == 0 || this.fsmjsjListLength == 0 || this.ydccjzListLength == 0 || this.bgzdhsbListLength == 0 || this.fsmbgzdhsbListLength == 0 || this.wlsbListLength == 0 || this.fwlsbListLength == 0 || this.aqcpListLength == 0) {
                this.sbxxIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
                console.log(this.rcUpdateCounts)
            }
        },
        // 日常管理---载体信息
        ztxxIsPerfect() {
            if (this.smztListLength == 0) {
                this.ztxxIsPerfectShow = true
                this.rcUpdateCounts = this.rcUpdateCounts + 1
                console.log(this.rcUpdateCounts)
            }
        },
        // 自查自评-第一季度
        zczp1Perfect() {
            let resRiskDatas = getZczpRiskDatas(1)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt != 7) {
                    return item
                }
            });
            console.log(resRiskDatas)
            if (resRiskDatas.length == 0 || bhgItem) {
                this.zczp1IsPerfectShow = true
                this.zczpUpdateCounts = this.zczpUpdateCounts + 1
            }
        },
        // 自查自评-第二季度
        zczp2Perfect() {
            let resRiskDatas = getZczpRiskDatas(2)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt != 7) {
                    return item
                }
            });
            if (resRiskDatas.length == 0 || bhgItem) {
                this.zczp2IsPerfectShow = true
                this.zczpUpdateCounts = this.zczpUpdateCounts + 1
            }
        },
        // 自查自评-第三季度
        zczp3Perfect() {
            let nowMonth = new Date().getMonth() + 1
            let resRiskDatas = getZczpRiskDatas(3)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt != 7) {
                    return item
                }
            });
            if (resRiskDatas.length == 0 || bhgItem) {
                this.zczp3IsPerfectShow = true
                this.zczpUpdateCounts = this.zczpUpdateCounts + 1
            }
        },
        // 自查自评-第四季度
        zczp4Perfect() {
            let resRiskDatas = getZczpRiskDatas(4)
            let bhgItem = resRiskDatas.some((item) => {
                let { zt } = item
                if (zt != 7) {
                    return item
                }
            });
            if (resRiskDatas.length == 0 || bhgItem) {
                this.zczp4IsPerfectShow = true
                this.zczpUpdateCounts = this.zczpUpdateCounts + 1
            }
        },
        // 初始化
        async initDatas() {
            let nowdate = this.getTime(new Date())
            getTzInfoDatas().then(async res => {
                if (res.code == 10000) {
                    this.zdglListLength = res.data.zdglListLength // 制度管理
                    this.zzjgListLength = res.data.zzjgListLength // 组织机构
                    this.csglListLength = res.data.csglListLength // 场所管理
                    this.smgwListLength = res.data.smgwListLength// 涉密岗位长度
                    this.ryxzHzListLength = res.data.ryxzhzListLength // 人员新增汇总
                    this.zgsmryHzListLength = res.data.zgsmryListLength // 在岗涉密人员
                    this.rynjbgHzListLength = res.data.smgwbgListLength // 岗位变更
                    this.lglzListLength = res.data.lglzListLength // 离职离岗
                    this.csbgListLength = res.data.csbgListLength // 场所变更
                    this.smjsjListLength = res.data.smjsjListLength // 涉密计算机
                    this.fsmjsjListLength = res.data.fsmjsjListLength // 非涉密计算机
                    this.ydccjzListLength = res.data.smydccjzListLength // 移动存储介质
                    this.bgzdhsbListLength = res.data.smbgzdhsbListLength // 办公自动化设备
                    this.fsmbgzdhsbListLength = res.data.fsmbgzdhsbListLength // 非密办公自动化设备
                    this.wlsbListLength = res.data.smwlsbListLength // 网络自动化设备
                    this.fwlsbListLength = res.data.fsmwlsbListLength // 非网络自动化设备
                    this.aqcpListLength = res.data.aqcpListLength // 安全产品
                    this.smztListLength = res.data.ztglListLength // 涉密载体
                    this.dmzrrListLength = res.data.dmzrrListLength // 定密责任人
                    this.dmsqListLength = res.data.dmsqListLength // 定密授权
                    this.gjmmsxListLength = res.data.gjmmsxListLength // 国家秘密事项
                    this.dmpxListLength = res.data.dmpxListLength // 定密培训
                    this.dmqkndtjListLength = res.data.dmqkndtjListLength // 定密情况年度统计
                    this.bmqsxqdqkListLength = res.data.bmqsxqdListLength // 不明确
                    this.zfcgxmqkListLength = res.data.zfcgxmqkListLength // 政府采购


                    this.bmzdIsPerfect()
                    this.zzjgIsPerfect()
                    this.ryxxIsPerfect()
                    this.csxxIsPerfect()
                    this.sbxxIsPerfect()
                    this.ztxxIsPerfect()

                    let timeArray = await getXtcsPage()
                    let timeList = timeArray.records.filter((t)=> {
                        return t.csbs == 'csbs_dbgzscrq' || t.csbs == 'csbs_ndsmrysbrq' || t.csbs == 'csbs_nddmsxsbrq'
                    })
                    let dbgzTimeArray = timeList.find((time)=>{
                        return time.csbs == 'csbs_dbgzscrq'
                    })

                    this.dbgzDateStart = dbgzTimeArray? dbgzTimeArray.cszDate: ''
                    this.dbgzDateEnd = dbgzTimeArray? dbgzTimeArray.cszDate2: ''
                    let offsetCounts = 0
                    let resDbgzRqqj = await getSfDbgzRqqj()
                    if( resDbgzRqqj.code == 10000 && resDbgzRqqj.data == true){
                        this.dadbShow = true
                        if(resDbgzRqqj.data == true){
                            if(this.smryTzScShow || this.smcsTzScShow || this.smsbTzScShow || this.smsxTzScShow || this.smsbTzScShow || this.smztTzScShow){
                                offsetCounts = offsetCounts + 10
                                this.$notify({
                                    title: '提示',
                                    message: '待办工作未处理，待生成',
                                    type: 'warning',
                                    offset: offsetCounts
                                })
                            }
                        }else{
                            let updataItem1 = {
                                "id": 1,
                                "smgwdj": false,
                                "smryhz": false,
                                "ryxzhz": false,
                                "rynjbghz": false,
                                "lghz": false,
                                "gxsj": nowdate,
                                "dqnf": this.nowsYear.toString(),
                                "isAgain": 0
                            }
                            let updataItem2 = {
                                "id": 2,
                                "smcsdj": false,
                                "csbgdj": false,
                                "gxsj": nowdate,
                                "dqnf": this.nowsYear.toString(),
                                "isAgain": 0
                            }
                            let updataItem3 = {
                                "id": 3,
                                "smjsjtz": false,
                                "fsmjsjtz": false,
                                "ydccjztz": false,
                                "bgzdhsbtz": false,
                                "fsmbgzdhsbtz": false,
                                "wlsbtz": false,
                                "fwlsbtz": false,
                                "aqcptz": false,
                                "gxsj": nowdate,
                                "dqnf": this.nowsYear.toString(),
                                "isAgain": 0
                            }
                            let updataItem4 = {
                                "id": 4,
                                "smzttz": false,
                                "gxsj": nowdate,
                                "dqnf": this.nowsYear.toString(),
                                "isAgain": 0
                            }
                            let updataItem5 = {
                                "id": 5,
                                "dmzrr": false,
                                "dmsq": false,
                                "gjmmsx": false,
                                "dmpx": false,
                                "dmqkndtj": false,
                                "bmqsxqdqk": false,
                                "zfcgxmqk": false,
                                "gxsj": nowdate,
                                "dqnf": this.nowsYear.toString(),
                                "isAgain": 0
                            }
                            // 更新数据库状态
                            await reviseDbgzStatus(updataItem1)
                            await reviseDbgzStatus(updataItem2)
                            await reviseDbgzStatus(updataItem3)
                            await reviseDbgzStatus(updataItem4)
                            await reviseDbgzStatus(updataItem5)
                        }
                        
                    }else {
                        this.dadbShow = false
                        // this.$message.error(resDbgzRqqj.data)
                    }
                    let resDbgzRqqj1 = await getSfDbgzSmryRqqj()
                    if( resDbgzRqqj1.code == 10000){
                        if(resDbgzRqqj1.data == true) {
                            this.downloadShow = true
                        }
                        if(this.smryDownLoadShow){
                            offsetCounts = offsetCounts + 10
                            this.$notify({
                                title: '提示',
                                message: '年度涉密人员上报待完成',
                                type: 'warning',
                                offset: offsetCounts
                            })
                        }
                    }else {
                        this.downloadShow = false
                        // this.$message.error(resDbgzRqqj1.data)
                    }
                    let resDbgzRqqj2 = await getSfDbgzDmsxRqqj()
                    if( resDbgzRqqj2.code == 10000){
                        if(resDbgzRqqj2.data == true) {
                            this.download1Show = true
                        }
                        if(this.smsxDownloadShow){
                            offsetCounts = offsetCounts + 10
                            this.$notify({
                                title: '提示',
                                message: '年度定密事项上报待完成',
                                type: 'warning',
                                offset: offsetCounts
                            })
                        }
                    }else {
                        this.download1Show = false
                        // this.$message.error(resDbgzRqqj2.data)
                    }
                }
            }).catch(err => {



                console.log('err1111111111', err)

                console.log(err,'1111111111111111111111111111111');
                this.$message.error('获取总数失败11111111111！')
            })
        },
        // 初始化当前生成状态 一键生成或者重新生成
        async initScStatus() {
            // 重新生成状态
            let nowdate = this.getTime(new Date())
            this.smryCxscShow = !this.smryTzScShow ? true : false
            this.smcsCxscShow = !this.smcsTzScShow ? true : false
            this.smsbCxscShow = !this.smsbTzScShow ? true : false
            this.smztCxscShow = !this.smztTzScShow ? true : false
            this.smsxCxscShow = !this.smsxTzScShow ? true : false
            getDbgzStatus().then(async res => {
                if (res.code == 10000) {
                    if (res.data[0].isAgain == 1) {
                        // 已经进行过一键生成操作
                        let updataItem1 = {
                            "id": 1,
                            "smgwdj": true,
                            "smryhz": true,
                            "ryxzhz": true,
                            "rynjbghz": true,
                            "lghz": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": 1
                        }
                        await reviseDbgzStatus(updataItem1)
                    }
                    if (res.data[1].isAgain == 1) {
                        // 已经进行过一键生成操作
                        let updataItem2 = {
                            "id": 2,
                            "smcsdj": true,
                            "csbgdj": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": 1
                        }
                        await reviseDbgzStatus(updataItem2)
                    }
                    if (res.data[2].isAgain == 1) {
                        // 已经进行过一键生成操作
                        let updataItem3 = {
                            "id": 3,
                            "smjsjtz": true,
                            "fsmjsjtz": true,
                            "ydccjztz": true,
                            "bgzdhsbtz": true,
                            "fsmbgzdhsbtz": true,
                            "wlsbtz": true,
                            "fwlsbtz": true,
                            "aqcptz": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": 1
                        }
                        await reviseDbgzStatus(updataItem3)
                    }
                    if (res.data[3].isAgain == 1) {
                        // 已经进行过一键生成操作
                        let updataItem4 = {
                            "id": 4,
                            "smzttz": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": 1
                        }
                        await reviseDbgzStatus(updataItem4)
                    }
                    if (res.data[4].isAgain == 1) {
                        // 已经进行过一键生成操作
                        let updataItem5 = {
                            "id": 5,
                            "dmzrr": true,
                            "dmsq": true,
                            "gjmmsx": true,
                            "dmpx": true,
                            "dmqkndtj": true,
                            "bmqsxqdqk": true,
                            "zfcgxmqk": true,
                            "gxsj": nowdate,
                            "dqnf": this.nowsYear.toString(),
                            "isAgain": 1
                        }
                        await reviseDbgzStatus(updataItem5)
                    }
                }
            }).catch(err => {
                this.$message.error('文档列表获取失败！')
            })
            console.log(this.smryStatus) 
        }
    },
    created() {
        this.getDbgzSattus()
        this.nowsYear = new Date().getFullYear() // 获取当前年度
    },
    mounted() {
        this.initDatas()
        // this.zcxxIsPerfect()
        // this.zzxxIsPerfect()
        // this.zczp1Perfect()
        // this.zczp2Perfect()
        // this.zczp3Perfect()
        // this.zczp4Perfect()
        this.initScStatus()
    }
}