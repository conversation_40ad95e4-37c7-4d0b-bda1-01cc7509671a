<template>
  <div class="bg_con">
    <div class="container">
      <!-- 查询条件以及操作按钮start -->
      <div class="mhcx">
        <el-form :inline="true" :model="formInline" size="medium" class="fl">
          <el-form-item label="办理内容" class="elFormLabel">
            <el-input class="widthw" v-model="formInline.blnr" clearable placeholder="办理内容">
            </el-input>
          </el-form-item>
          <el-form-item label="服务类型" class="elFormLabel">
            <el-select v-model="formInline.fwlx" placeholder="请选择服务类型" clearable>
              <el-option v-for="item in fwlxOptions" :key="item.fwid" :label="item.fwlx" :value="item.fwid">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发起时间" class="elFormLabel">
            <el-radio v-model="formInline.fqsj" label="" border size="medium">全部</el-radio>
            <el-radio v-model="formInline.fqsj" label="1" border size="medium">近三天</el-radio>
            <el-radio v-model="formInline.fqsj" label="2" border size="medium">近七天</el-radio>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- 查询条件以及操作按钮end -->
      <!-- 涉密人员任用审查列表start -->
      <div class="table_content">
        <el-table class="tb-container table" :data="smryList" border @selection-change="selectRow"
          :header-cell-style="headerCellStyle" stripe height="calc(100% - 34px - 44px - 10px)">
          <el-table-column type="selection" width="55" align="center"> </el-table-column>
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
          <el-table-column prop="fwlxmc" label="服务类型"></el-table-column>
          <el-table-column prop="blnr" label="办理内容"></el-table-column>
          <el-table-column prop="xm" label="发起人"></el-table-column>
          <el-table-column prop="fqsj" label="发起时间"></el-table-column>
          <el-table-column prop="" label="操作" width="140">
            <template slot-scope="scoped">
              <el-button size="medium" type="text" @click="updateItem(scoped.row)">查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5"
          :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper" :total="total">
        </el-pagination>
      </div>
      <!-- 涉密人员任用审查列表end -->
      <!-- 发起申请弹框start -->
      <!-- <el-dialog title="人员选择" :close-on-click-modal="false" :visible.sync="dialogVisible" width="40%"
        :before-close="handleClose" @close="close('formName')">
        <div class="dlFqsqContainer">
          <label for="">部门:</label>
          <el-input class="input1" clearable placeholder="部门"></el-input>
          <label for="">姓名:</label>
          <el-input class="input2" clearable placeholder="姓名"></el-input>
          <el-button class="searchButton" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-table class="tb-container" :data="smryList" border @selection-change="selectRow"
            :header-cell-style="headerCellStyle" stripe>
            <el-table-column type="selection" width="55" align="center"> </el-table-column>
            <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
            <el-table-column prop="" label="姓名"></el-table-column>
            <el-table-column prop="" label="部门"></el-table-column>
            <el-table-column prop="" label="岗位"></el-table-column>
          </el-table>
          <el-pagination class="paginationContainer" background @current-change="handleCurrentChange"
            @size-change="handleSizeChange" :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]"
            :page-size="pageSize" layout="total, prev, pager, sizes,next, jumper" :total="total">
          </el-pagination>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submit('formName')">保 存</el-button>
          <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
        </span>
      </el-dialog> -->
      <!-- 发起申请弹框end -->
    </div>
  </div>
</template>
<script>
import {
  getYblbList,
  getFwlxList,
} from '../../../api/wdgz'
export default {
  components: {},
  props: {},
  data() {
    return {
      // table 行样式
      headerCellStyle: { background: '#EEF7FF', color: '#4D91F8' },
      formInline: {}, // 搜索条件
      dialogVisible: false, // 发起申请弹框
      fwlxOptions: [],
      smryList: [],
      page: 1,
      pageSize: 10,
      total: 0,
    }
  },
  computed: {},
  mounted() {
    this.getFwlx()
    this.getYbsx()
  },
  methods: {
    //服务类型查询
    async getFwlx() {
      let data = await getFwlxList()
      if (data.code == 10000) {
        this.fwlxOptions = data.data
      }
    },
    async getYbsx() {
      let params = {
        blnr: this.formInline.blnr,
        fwlx: this.formInline.fwlx,
        page: this.page,
        pageSize: this.pageSize,
        bj: this.formInline.fqsj
      }
      let data = await getYblbList(params)
      this.smryList = data.records
      this.total = data.total
    },
    // 人员选择弹框保存按钮
    submit() {
      this.$router.push('/ryscTable')
    },
    updateItem(row) {
      console.log("关于办理信息", row);
      // return
      if (row.fwlxmc == '涉密人员离岗离职') {
        this.$router.push({
          path: '/lglzblxxscb',
          query: {
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密人员等级变更') {
        this.$router.push({
          path: '/gwbgblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密人员出国境审批') {
        this.$router.push({
          path: '/cgjblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密载体制作审批') {
        this.$router.push({
          path: '/ztzzscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密载体接收传递审批') {
        this.$router.push({
          path: '/ztqsblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密载体外发审批') {
        this.$router.push({
          path: '/ztwfblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密载体借阅审批') {
        this.$router.push({
          path: '/ztjyscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密载体外出携带审批') {
        this.$router.push({
          path: '/ztwcxdblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密载体复制审批') {
        this.$router.push({
          path: '/ztfzscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密载体超期借用审批') {
        this.$router.push({
          path: '/ztcqjyscfpblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密载体销毁审批') {
        this.$router.push({
          path: '/ztxhblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备定密审批') {
        this.$router.push({
          path: '/dmsbscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备密级变更审批') {
        this.$router.push({
          path: '/dmbgscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备报废审批') {
        this.$router.push({
          path: '/sbbfblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备责任人变更审批') {
        this.$router.push({
          path: '/zrrbgscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备借用审批') {
        this.$router.push({
          path: '/sbjyscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备销毁审批') {
        this.$router.push({
          path: '/sbxhblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '信息导入审批') {
        this.$router.push({
          path: '/xxdrblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备超期借用审批') {
        this.$router.push({
          path: '/cqjyblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备携带外出审批') {
        this.$router.push({
          path: '/sbxdwcscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密设备维修审批') {
        this.$router.push({
          path: '/sbwxblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密计算机系统维护审批') {
        this.$router.push({
          path: '/xtwhblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密场所审定') {
        this.$router.push({
          path: '/cssdscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密场所变更审批') {
        this.$router.push({
          path: '/csbgscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '涉密场所门禁授权审批') {
        this.$router.push({
          path: '/mjsqblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '携带设备进入涉密场所审批') {
        this.$router.push({
          path: '/xdsbjrblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      } else if (row.fwlxmc == '无授权人员进入涉密场所审批') {
        this.$router.push({
          path: '/wsqblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      }else if (row.fwlxmc == '非密重点人员审批') {
        this.$router.push({
          path: '/fmzdryscblxxscb',
          query: {
            list: row,
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      }  else {
        this.$router.push({
          path: '/blxxscb',
          query: {
            fwdyid: row.fwdyid,
            slid: row.slid
          }
        })
      }

    },
    onSubmit() {
      this.getYbsx()
    },
    cz() {
      this.formInline = {}
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.getYbsx()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.getYbsx()
    },
    handleClose() {

    },
    close() {

    },
    selectRow(val) {
      console.log(val);
    },
  },
  watch: {

  }
}

</script>

<style scoped>
.fl {
  float: left;
}

.fr {
  float: right;
}

.container {
  width: 100%;
  position: relative;
  overflow: hidden;
  height: 100%;
  /* box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13); */
  border-radius: 8px;
}

.fileInput {
  display: none;
  position: absolute;
  top: 10px;
  right: 0;
  opacity: 0;
  cursor: pointer;
  height: 32px;
  width: 56px;
  z-index: 1;
}

.elFormLabel {
  font-weight: 700;
}

.elFormLabel .el-radio {
  margin-right: 0;
}

.table_content {
  height: 100%;
}

.tb-container {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

.bg_con {
  width: 100%;
  height: calc(100% - 38px);
}

/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.widthw {
  width: 6vw;
}

/* 发起申请弹框 */
.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}
</style>
