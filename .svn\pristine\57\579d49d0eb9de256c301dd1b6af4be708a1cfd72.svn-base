<template>
  <div style="height: 100%">
    <hsoft_top_title>
      <template #left>注册登录信息</template>
    </hsoft_top_title>
    <div class="con">
      <el-form ref="ruleForm" :model="dwxx" label-width="150px">
        <el-form-item label="上级单位">
          <el-select v-model="dwxx.sjdw" style="width: 100%;" placeholder="请选择上级单位">
            <el-option v-for="(item, index) in sjdwList" :key="index" :value="item.dwid" :label="item.dwmc"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位注册号">
          <el-input v-model="dwxx.dwzch"></el-input>
          <el-popover placement="right" width="200" trigger="hover">
            <div>
              <div style="display:flex;margin-bottom:10px">
                <i class="el-icon-info" style="color:#409eef;    position: relative;
                                            top: 2px;"></i>
                <div class="tszt">提示</div>
              </div>
              <div class="smzt">
                单位注册号由程序下发单位下发
              </div>
            </div>
            <i class="el-icon-info" style="color:#409eef;position: absolute;    top: 14px; right: -22px"
              slot="reference"></i>
          </el-popover>
        </el-form-item>
        <el-form-item label="单位名称">
          <el-input v-model="dwxx.dwmc"></el-input>
          <el-popover placement="right" width="200" trigger="hover">
            <div>
              <div style="display:flex;margin-bottom:10px">
                <i class="el-icon-info" style="color:#409eef;    position: relative;
                                            top: 2px;"></i>
                <div class="tszt">提示</div>
              </div>
              <div class="smzt">
                应填写全称，以统一社会信用代码登记信息为准，统一社会信用代码及其他登记信息可过全国组织机构统一社会信用代码数据服务中心（www.cods.org.cn）官网或官方APP、微信小程序“统一代码查询”查询。
              </div>
            </div>
            <i class="el-icon-info" style="color:#409eef;position: absolute;    top: 14px; right: -22px"
              slot="reference"></i>

          </el-popover>
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="dwxx.yhm"></el-input>
        </el-form-item>
        <el-form-item label="登录密码">
          <el-input v-model="dwxx.dlmm"></el-input>
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="dwxx.qrmm"></el-input>
        </el-form-item>
        <el-form-item label="单位类型">
          <el-select v-model="dwxx.dwlx" style="width: 100%;" placeholder="请选择单位类型">
            <el-option v-for="(item, index) in dwlxList" :key="index" :value="item.csm" :label="item.csm"></el-option>
          </el-select>
          <el-popover placement="right" width="200" trigger="hover">
            <div>
              <div style="display:flex;margin-bottom:10px">
                <i class="el-icon-info" style="color:#409eef;    position: relative;
                                            top: 2px;"></i>
                <div class="tszt">提示</div>
              </div>
              <div class="smzt">
                从下拉栏中选择：党政机关主要包括党的机关、人大机关、行政机关、政协机关、监察机关、审判机关、检察机关、民主党派机关；人民团体主要包括工会组织、共青团组织、妇女联合会组织等人民团体，也包括学会、协会、研究会等社团组织；参公事业单位指参照公务员法管理事业单位；事业单位指接受政府领导，主要提供教育、科技、文化、卫生等活动非物质生产和劳务服务的社会公共组织或机构；国有企业指由中央管理或者地方政府监管的国有企业，包括国有独资企业、国有控股企业（不包括国有参股企业）；民营保密资质（资格）企业指民间资本作为投资主体，具有印制、集成或者军工保密资质（资格）的非公有制企业；其他指上述类型之外的单位。
              </div>
            </div>
            <i class="el-icon-info" style="color:#409eef;position: absolute;    top: 14px; right: -22px"
              slot="reference"></i>

          </el-popover>
        </el-form-item>
        <el-form-item label="所属领域">
          <el-select v-model="dwxx.ssly" style="width: 100%;" placeholder="请选择所属领域">
            <el-option v-for="(item, index) in sslyList" :key="index" :value="item.csm" :label="item.csm"></el-option>
          </el-select>
          <el-popover placement="right" width="200" trigger="hover">
            <div>
              <div style="display:flex;margin-bottom:10px">
                <i class="el-icon-info" style="color:#409eef;    position: relative;
                                            top: 2px;"></i>
                <div class="tszt">提示</div>
              </div>
              <div class="smzt">
                从下拉栏中选择：国防军工、外交外事、安全、政法、经济金融、科技、教育、能源、测绘、其他。如涉及多个领域，选择主要领域。
              </div>
            </div>
            <i class="el-icon-info" style="color:#409eef;position: absolute;    top: 14px; right: -22px"
              slot="reference"></i>

          </el-popover>
        </el-form-item>
        <el-form-item label="所属层次">
          <el-select v-model="dwxx.sscc" style="width: 100%;" placeholder="请选择所属层次">
            <el-option v-for="(item, index) in ssccList" :key="index" :value="item.csz" :label="item.csm"></el-option>
          </el-select>
          <el-popover placement="right" width="200" trigger="hover">
            <div>
              <div style="display:flex;margin-bottom:10px">
                <i class="el-icon-info" style="color:#409eef;    position: relative;
                                            top: 2px;"></i>
                <div class="tszt">提示</div>
              </div>
              <div class="smzt">
                从下拉栏中选择：中央机关、企业本级/中央机关、企业下属驻京单位/中央机关、企业京外单位/省直机关、企业本级/省直机关、企业下属单位/市直机关、企业本级/市直机关、企业下属单位/县直和乡镇机关、企业本级/县直和乡镇机关、企业下属单位/私营企业保密资质单位（甲级）/私营企业保密资质单位（乙级）/私营企业保密资格单位（一级）/私营企业保密资格单位（二级）/私营企业保密资格单位（三级）。
              </div>
            </div>
            <i class="el-icon-info" style="color:#409eef;position: absolute;    top: 14px; right: -22px"
              slot="reference"></i>

          </el-popover>
        </el-form-item>
        <el-form-item label="统一社会信用代码">
          <el-input v-model="dwxx.tydm"></el-input>
        </el-form-item>
        <el-form-item label="单位所在行政区域">
          <el-select v-model="dwxx.province" @change="provinceChanged" style="width: 32.8%;">
            <el-option v-for="(item, index) in provinceList" :key="'province' + index" :label="item.name"
              :value="item.name"></el-option>
          </el-select>
          <el-select v-model="dwxx.city" @change="cityChanged" style="width: 32.9%;">
            <el-option v-for="(item, index) in cityList" :key="'city' + index" :label="item.name"
              :value="item.name"></el-option>
          </el-select>
          <el-select v-model="dwxx.district" @change="districtChanged" style="width: 32.8%;">
            <el-option v-for="(item, index) in districtList" :key="'district' + index" :label="item.name"
              :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位联系人">
          <el-input v-model="dwxx.dwlxr"></el-input>
        </el-form-item>
        <el-form-item label="单位联系电话号码">
          <el-input v-model="dwxx.dwlxdh"></el-input>
        </el-form-item>
        <el-form-item label="单位联系邮箱">
          <el-input v-model="dwxx.dwyx"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit('ruleForm')">立即创建</el-button>
          <!-- <el-button @click="resetForm('ruleForm')">取消</el-button> -->
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import {
  getDwxxList,
  getAllDwlx,
  getAllSsly,
  getAllSscj,
  getProvinceList,
  getCityByProvincecode,
  getAreaByCitycode,
  registerDwxx
} from '../../../api/dwzc'
export default {
  data() {
    return {
      // 单位信息
      dwxx: {
        sjdw: '', // 上级单位
        dwzch: '', // 单位注册号
        dwmc: '', // 单位名称
        yhm: '', // 用户名
        dlmm: '', // 登录密码
        qrmm: '', // 确认密码
        dwlx: '', // 单位类型
        ssly: '', // 所属领域
        sscc: '', // 所属层次
        tydm: '', // 统一社会信用代码
        province: "", // 省
        city: "", // 市
        district: "", // 区
        dwlxr: '', // 单位联系人
        dwlxdh: '', // 单位联系电话
        dwyx: '', // 单位联系邮箱
      },
      sjdwList: [],
      dwlxList: [],
      sslyList: [],
      ssccList: [],
      // 省集合
      provinceList: [],
      provincecode: '',
      // 市集合
      cityList: [],
      citycode: '',
      // 区集合
      districtList: [],
      districtcode: '',
    };
  },
  components: {
    hsoft_top_title
  },
  mounted() {
    this.getAllDwlx()
    this.getAllSjdw()
    this.getAllSsly()
    this.getAllSscc()
    // 初始化省市区数据
    this.initSsq()
  },
  // 监听
  watch: {

  },
  methods: {
    // 获取上级单位
    async getAllSjdw() {
      this.sjdwList = await getDwxxList()
    },
    // 获取所有单位类型
    async getAllDwlx() {
      this.dwlxList = await getAllDwlx()
    },
    // 获取所有所属领域
    async getAllSsly() {
      this.sslyList = await getAllSsly()
    },
    // 获取所有所属层次
    async getAllSscc() {
      this.ssccList = await getAllSscj()
    },
    // 省改变事件
    async provinceChanged(province) {
      console.log(province);
      this.provinceList = await getProvinceList();
      console.log('全国各个省份：', this.provinceList);
      this.provinceList.forEach((item, index) => {
        if (item.name == province) {
          console.log('省份item', item);
          this.dwxx.province = item.name
          this.provincecode = item.code
          console.log(this.provincecode);
        }
      })
      // 重置区
      this.districtList = []
      // 重置区划
      this.dwxx.regionalNumber = ''
      // 重置市区数据
      this.dwxx.city = ''
      this.dwxx.district = ''
      this.cityChanged()
    },
    // 市改变事件
    async cityChanged(city) {
      // // 重新初始化区
      //市级
      let cityparam = {
        provincecode: this.provincecode
      }
      this.cityList = await getCityByProvincecode(cityparam)
      console.log('各个省份下属地级市：', this.cityList);
      this.cityList.forEach((item, index) => {
        if (city == item.name) {
          console.log('地级市item', item);
          this.dwxx.city = item.name
          this.citycode = item.code
        }
      })
      // 重置区划
      this.dwxx.regionalNumber = ''
      // 重置区数据
      this.dwxx.district = ''
      this.districtChanged()
    },
    // 区改变事件
    async districtChanged(district) {
      //区级
      let districtparam = {
        citycode: this.citycode
      }
      this.districtList = await getAreaByCitycode(districtparam)
      console.log('地级市下属市区：', this.districtList);
      this.districtList.forEach((item, index) => {
        if (district == item.name) {
          console.log('市区item', item);
          this.dwxx.district = item.name
          this.districtcode = item.code
        }
      })
    },
    // 初始化省市区数据
    async initSsq() {
      //省级
      this.provinceList = await getProvinceList();
      console.log('全国各个省份：', this.provinceList);
      this.provinceList.forEach((item, index) => {
        if (this.dwxx.szsid == item.code) {
          console.log('省份item', item);
          this.dwxx.province = item.name
          this.provincecode = item.code
        }
      })
      //市级
      let cityparam = {
        provincecode: this.provincecode
      }
      this.cityList = await getCityByProvincecode(cityparam)
      console.log('各个省份下属地级市：', this.cityList);
      this.cityList.forEach((item, index) => {
        if (this.dwxx.szsdid == item.code) {
          console.log('地级市item', item);
          this.dwxx.city = item.name
          this.citycode = item.code
        }
      })
      //区级
      let districtparam = {
        citycode: this.citycode
      }
      this.districtList = await getAreaByCitycode(districtparam)
      console.log('地级市下属市区：', this.districtList);
      this.districtList.forEach((item, index) => {
        if (this.dwxx.szqxid == item.code) {
          console.log('市区item', item);
          this.dwxx.district = item.name
          this.districtcode = item.code
        }
      })
    },
    async onSubmit(formName) {
      if (this.dwxx.sjdw != '') {
        if (this.dwxx.dwzch != '') {
          if (this.dwxx.dlmm != '') {
            if (this.dwxx.qrmm != '') {
              if (this.dwxx.dlmm == this.dwxx.qrmm) {
                if (this.dwxx.dwlx != '') {
                  if (this.dwxx.ssly != '') {
                    if (this.dwxx.sscc != '') {
                      if (this.dwxx.tydm != '') {
                        if (this.dwxx.province != '') {
                          if (this.dwxx.city != '') {
                            if (this.dwxx.district != '') {
                              let params = {
                                dwmc: this.dwxx.dwmc,
                                dwid: this.dwxx.sjdw,
                                dwzch: this.dwxx.dwzch,
                                yhm: this.dwxx.yhm,
                                mm: this.dwxx.dlmm,
                                mm: this.dwxx.qrmm,
                                dwlx: this.dwxx.dwlx,
                                ssly: this.dwxx.ssly,
                                sscj: this.dwxx.sscc,
                                tydm: this.dwxx.tydm,
                                szsid: this.provincecode,
                                szsdid: this.citycode,
                                szqxid: this.districtcode,
                                lxr: this.dwxx.dwlxr,
                                lxdh: this.dwxx.dwlxdh,
                                lxyx: this.dwxx.dwyx,
                              }
                              let data = await registerDwxx(params)
                              if (data.code == 10000) {
                                this.cz()
                                this.$message({
                                  message: data.message,
                                  type: 'success'
                                });
                              } else if (data.code == 10000) {
                                this.$message({
                                  message: data.message,
                                  type: 'warning'
                                });
                              }
                            } else {
                              this.$message.warning('请选择所在区县')
                            }
                          } else {
                            this.$message.warning('请选择所在市区')
                          }
                        } else {
                          this.$message.warning('请选择所在省份')
                        }
                      } else {
                        this.$message.warning('请输入统一社会信用代码')
                      }
                    } else {
                      this.$message.warning('请选择所属层次')
                    }
                  } else {
                    this.$message.warning('请选择所属领域')
                  }
                } else {
                  this.$message.warning('请选择单位类型')
                }
              } else {
                this.$message.warning('两次输入密码不相符')
              }
            } else {
              this.$message.warning('请输入确认密码')
            }
          } else {
            this.$message.warning('请输入登录密码')
          }
        } else {
          this.$message.warning('请输入单位注册号')
        }
      } else {
        this.$message.warning('请选择上级单位')
      }

    },
    resetForm(formName) {
      this.cz()
      this.$refs[formName].resetFields();
    },
    cz() {
      this.dwxx.sjdw = '' // 上级单位
      this.dwxx.dwzch = '' // 单位注册号
      this.dwxx.dwmc = '' // 单位名称
      this.dwxx.yhm = '' // 用户名
      this.dwxx.dlmm = '' // 登录密码
      this.dwxx.qrmm = '' // 确认密码
      this.dwxx.dwlx = '' // 单位类型
      this.dwxx.ssly = '' // 所属领域
      this.dwxx.sscc = '' // 所属层次
      this.dwxx.tydm = '' // 统一社会信用代码
      this.dwxx.province = '' // 省
      this.dwxx.city = '' // 市
      this.dwxx.district = '' // 区
      this.dwxx.dwlxr = '' // 单位联系人
      this.dwxx.dwlxdh = '' // 单位联系电话
      this.dwxx.dwyx = '' // 单位联系邮箱
    }
  },


};
</script>

<style scoped>
.con {
  width: 800px;
  height: 100%;
  margin: 0 auto;
}

/deep/.el-form-item {
  margin-bottom: 8px !important;
}
</style>
