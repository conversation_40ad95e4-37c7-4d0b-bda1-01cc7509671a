<template>
  <div>
    <hsoft_top_title>
      <template #left>数据导入</template>
    </hsoft_top_title>
    <!---->
    <el-button type="primary" @click="generateSbsj()">生成上报数据</el-button>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'

import { getWindowLocation } from '../../../utils/windowLocation'

import { dateFormatChinese } from '../../../utils/moment'

import { errorProcessor } from '../../../utils/errorProcessor'

import { getZczpPath } from '../../../utils/pathUtil'

import { encryptAes, decryptAes } from '../../../utils/aesUtils'

export default {
  data () {
    return {}
  },
  components: {
    hsoft_top_title
  },
  methods: {
    generateSbsj () {
      let FS = require('fs')
      // 弹出dialog选择上报数据保存文件位置
      let currentYear = new Date().getFullYear()
      let sbsjFileName = '上报数据-' + currentYear + '-' + (new Date().getTime()) + '.json'
      const { dialog } = require('electron').remote
      let options = {
        title: '选择上报数据保存位置',
        defaultPath: sbsjFileName
      }
      dialog.showSaveDialog(options, result => {
        // 获取文件路径
        let zczpJsonPath = getZczpPath()
        if (!zczpJsonPath) {
          this.$notify({
            title: '系统异常',
            message: '[' + '上报数据文件路径获取失败' + ']\n',
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // 获取文件内容
        let zczpJsonStr
        try {
          zczpJsonStr = FS.readFileSync(zczpJsonPath, {encoding: 'utf8'})
        } catch (error) {
          error = errorProcessor(error)
          let errObj = JSON.parse(error.message)
          this.$notify({
            title: '系统异常',
            message: '[' + errObj.mark + ']\n',
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // 简单校验数据是否完整
        let zczpJsonObj
        try {
          zczpJsonObj = JSON.parse(zczpJsonStr)
        } catch (error) {
          this.$notify({
            title: '系统异常',
            message: '[' + '上报数据文件内容异常' + ']\n',
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // // 筛选今年数据
        // console.log('筛选今年数据')
        // let zczpCurrentJsonObj = {}
        // Object.keys(zczpJsonObj).forEach(table => {
        //   console.log('table', table)
        // })
        // return
        // 生成密钥
        let keyStr = 'hsoftBanner' + currentYear
        // 数据加密
        let encryptStr
        try {
          encryptStr = encryptAes(zczpJsonStr, keyStr)
        } catch (error) {
          this.$notify({
            title: '系统异常',
            message: '[' + '上报数据加密异常' + ']\n' + error.message,
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // 测试解密
        try {
          JSON.parse(decryptAes(encryptStr, keyStr))
        } catch (error) {
          this.$notify({
            title: '系统异常',
            message: '[' + '上报数据测试解密异常' + ']\n' + error.message,
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // 生成上报数据文件
        try {
          FS.writeFileSync(result, encryptStr, { encoding: 'utf8' })
        } catch (error) {
          error = errorProcessor(error)
          let errObj = JSON.parse(error.message)
          this.$notify({
            title: '系统异常',
            message: '[' + errObj.mark + ']\n',
            type: 'error',
            offset: 100,
            duration: 0
          })
          return
        }
        // 上报数据生成成功
        this.$message.success('上报数据生成成功=>'+result)
      })
    }
  },
  mounted () {
  }
}
</script>

<style scoped>
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}
/**单位信息区域**/
.out-card .out-card-div {
  font-size: 13px;
  padding: 5px 20px;
}
.out-card .out-card-div div {
  padding: 10px 5px;
  display: flex;
}
.out-card .dwxx div:hover {
  background: #f4f4f5;
  border-radius: 20px;
}
.out-card .dwxx div label {
  /* background-color: red; */
  width: 125px;
  display: inline-block;
  text-align: right;
  font-weight: 600;
  color: #909399;
}
.out-card .dwxx div span {
  /* background-color: rgb(33, 92, 79); */
  flex: 1;
  display: inline-block;
  padding-left: 20px;
}
/**操作区域**/
.out-card .user-options {
  /* background: red; */
  height: 500px;
  text-align: center;
}
.out-card .card-option {
  width: 150px;
  height: 150px;
  background: var(--background);
  font-weight: 700;
  opacity: 0.85;
  display: inline-block;
}
.out-card .card-option:hover {
  cursor: pointer;
  opacity: 1;
}
</style>
