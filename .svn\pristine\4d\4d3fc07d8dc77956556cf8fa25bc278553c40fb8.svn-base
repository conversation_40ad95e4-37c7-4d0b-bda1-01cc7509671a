{"version": 3, "sources": ["webpack:///src/renderer/view/zczp/childPage/ccdryDjXqxx.vue", "webpack:///./src/renderer/view/zczp/childPage/ccdryDjXqxx.vue?af8d", "webpack:///./src/renderer/view/zczp/childPage/ccdryDjXqxx.vue"], "names": ["ccdryDjXqxx", "data", "dialogObj", "zw", "xm", "bm", "showDxList", "spanArr", "reverse", "activities", "showMdmenu", "dwxx", "ryid", "rwid", "djzt", "rwmc", "dlzt", "jcjd", "computed", "components", "mounted", "this", "$route", "query", "console", "log", "getDwxxList", "getRyzcxxjl", "methods", "submit", "$router", "push", "path", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "Object", "dwzc", "sent", "stop", "_this2", "_callee2", "data1", "params", "_context2", "zczp", "for<PERSON>ach", "item", "item1", "zcxid", "dxmc", "zcxmc", "getSpanArr", "returnSy", "go", "getCcdryxxByCcdryid", "ccdryxx", "selectCcdryxxByCcdryid", "assign_default", "JSON", "parse", "stringify_default", "list", "i", "length", "pos", "objectSpanMethod", "_ref", "row", "column", "rowIndex", "columnIndex", "rowspan", "scid", "colspan", "getZD", "zdList", "getRyzcxxjlZD", "nr", "undefined", "sffh", "mouseoverMdMenu", "mouseoutMenu", "watch", "handler", "newVal", "oldVal", "_this3", "dxMdIdArr", "nrIndex", "mdIndex", "indexOf", "children", "href", "scnr", "ykf", "deep", "childPage_ccdryDjXqxx", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "width", "height", "staticClass", "float", "attrs", "inline", "size", "_v", "_s", "type", "on", "click", "clear", "span-method", "border", "label", "scopedSlots", "_u", "key", "fn", "scope", "id", "$index", "disabled", "model", "value", "callback", "$$v", "$set", "expression", "rows", "trim", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "uNAiFAA,wBACAC,KADA,WAEA,OACAC,WAGAC,GAAA,GACAC,GAAA,GACAC,GAAA,IAGAC,cAEAC,WAEAC,SAAA,EAEAC,cAEAC,YAAA,EACAC,QACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,KAGAC,YACAC,cAEAC,QAhCA,WAiCAC,KAAAR,KAAAQ,KAAAC,OAAAC,MAAAV,KACAQ,KAAAP,KAAAO,KAAAC,OAAAC,MAAAT,KACAO,KAAAT,KAAAS,KAAAC,OAAAC,MAAAX,KACAS,KAAAL,KAAAK,KAAAC,OAAAC,MAAAP,KACAQ,QAAAC,IAAA,YAAAJ,KAAAR,MACAQ,KAAAJ,KAAAI,KAAAC,OAAAC,MAAAN,KACAI,KAAAnB,UAAAC,GAAAkB,KAAAC,OAAAC,MAAApB,GACAkB,KAAAnB,UAAAE,GAAAiB,KAAAC,OAAAC,MAAAnB,GACAiB,KAAAnB,UAAAG,GAAAgB,KAAAC,OAAAC,MAAAlB,GACAgB,KAAAN,KAAAM,KAAAC,OAAAC,MAAAR,KAEAM,KAAAK,cAUAL,KAAAM,eAOAC,SACAC,OADA,WAEA,GAAAR,KAAAL,KACAK,KAAAS,QAAAC,MACAC,KAAA,SACAT,OACAV,KAAAQ,KAAAR,KACAE,KAAAM,KAAAN,KACAE,KAAAI,KAAAJ,QAGA,GAAAI,KAAAL,MACAK,KAAAS,QAAAC,MACAC,KAAA,QACAT,OACAV,KAAAQ,KAAAR,KACAE,KAAAM,KAAAN,KACAE,KAAAI,KAAAJ,SAMAS,YAvBA,WAuBA,IAAAO,EAAAZ,KAAA,OAAAa,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAArC,EAAA,OAAAkC,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACA1C,EADAuC,EAAAK,KAEArB,QAAAC,IAAAxB,GACAgC,EAAAtB,KAAAV,EAHA,wBAAAuC,EAAAM,SAAAR,EAAAL,KAAAC,IASAP,YAhCA,WAgCA,IAAAoB,EAAA1B,KAAA,OAAAa,IAAAC,EAAAC,EAAAC,KAAA,SAAAW,IAAA,IAAA/C,EAAAgD,EAAAC,EAAA,OAAAf,EAAAC,EAAAG,KAAA,SAAAY,GAAA,cAAAA,EAAAV,KAAAU,EAAAT,MAAA,cAAAS,EAAAT,KAAA,EACAC,OAAAS,EAAA,EAAAT,GADA,cACA1C,EADAkD,EAAAN,KAEAI,KACAC,GACArC,KAAAkC,EAAAlC,KACAD,KAAAmC,EAAAnC,MALAuC,EAAAT,KAAA,EAOAC,OAAAS,EAAA,EAAAT,CAAAO,GAPA,QAOAD,EAPAE,EAAAN,MAQAQ,QAAA,SAAAC,GACArD,EAAAoD,QAAA,SAAAE,GACAA,EAAAC,OAAAF,EAAAE,QACAF,EAAAG,KAAAF,EAAAG,WAIAX,EAAAzC,WAAA2C,EACAF,EAAAxC,QAAAwC,EAAAY,WAAAV,GACAzB,QAAAC,IAAAsB,EAAAzC,YACAkB,QAAAC,IAAAxB,GACAuB,QAAAC,IAAAwB,GAnBA,yBAAAE,EAAAL,SAAAE,EAAAD,KAAAb,IAsBA0B,SAtDA,WAuDAvC,KAAAS,QAAA+B,IAAA,IAEAC,oBAzDA,WA0DA,IAAAC,EAAAC,uBAAA3C,KAAAnB,WACAsB,QAAAC,IAAA,UAAAsC,GACME,IAAN5C,KAAAnB,UAAA6D,GACA1C,KAAAnB,UAAAgE,KAAAC,MAAAC,IAAA/C,KAAAnB,aAGAyD,WAhEA,SAgEAU,GAEA,IADA,IAAA9D,KACA+D,EAAA,EAAAA,EAAAD,EAAAE,OAAAD,IACA,IAAAA,GACA/D,EAAAwB,KAAA,GACAV,KAAAmD,IAAA,GAGAH,EAAAC,GAAAd,OAAAa,EAAAC,EAAA,GAAAd,OACAjD,EAAAc,KAAAmD,MAAA,EACAjE,EAAAwB,KAAA,KAEAxB,EAAAwB,KAAA,GACAV,KAAAmD,IAAAF,GAIA,OAAA/D,GAEAkE,iBAnFA,SAAAC,GAmFA,IAAAC,EAAAD,EAAAC,IAAAD,EAAAE,OAAAF,EAAAG,SACA,OADAH,EAAAI,YAIA,OACAC,QAFA1D,KAAAd,QAAAoE,EAAAK,KAAA,GAGAC,QAAA,IAOAC,MAhGA,WAiGA1D,QAAAC,IAAA,SAEA,IAAA0D,EAAAC,gBASA,OARA5D,QAAAC,IAAA0D,GACAA,EAAA9B,QAAA,SAAAgC,QACAC,IAAAD,EAAAE,OACAF,EAAAE,MAAA,KAGAlE,KAAAd,QAAAc,KAAAsC,WAAAwB,GAEAA,GAGAK,gBA/GA,WAgHAnE,KAAAX,YAAA,GAGA+E,aAnHA,WAoHApE,KAAAX,YAAA,IAGAgF,OAOApF,YACAqF,QADA,SACAC,EAAAC,GAAA,IAAAC,EAAAzE,KAGAA,KAAAZ,cAEA,IAAAsF,KAGAH,EAAAvC,QAAA,SAAAgC,EAAAW,GAGAX,EAAAY,QAAA,KAAAD,EACAX,EAAAE,OACAF,EAAAY,QAAA,KAAAD,GAEA,GAAAD,EAAAG,QAAAb,EAAA7B,QACAuC,EAAAhE,KAAAsD,EAAA7B,QAEA,GAAAuC,EAAAG,QAAAb,EAAA7B,SACAsC,EAAArF,WAAAsF,EAAAG,QAAAb,EAAA7B,SACAsC,EAAArF,WAAAsB,MACA0B,KAAA4B,EAAA5B,KACA0C,cAGAL,EAAArF,WAAAsF,EAAAG,QAAAb,EAAA7B,QAAA2C,SAAApE,MACAqE,KAAA,IAAAf,EAAAY,QACAZ,KAAAgB,KACAC,IAAAjB,EAAAiB,WAMAC,MAAA,MC5SeC,GADEC,OAFjB,WAA0B,IAAAC,EAAArF,KAAasF,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,MAAA,OAAAC,OAAA,UAAgCJ,EAAA,OAAYK,YAAA,SAAmBL,EAAA,WAAgBK,YAAA,mBAAAH,aAA4CI,MAAA,QAAeC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,UAAgBN,EAAA,OAAAH,EAAAa,GAAA,MAAAb,EAAAc,GAAAd,EAAAxG,UAAAE,SAAAsG,EAAAa,GAAA,KAAAV,EAAA,gBAAsFE,aAAaI,MAAA,UAAgBN,EAAA,OAAAH,EAAAa,GAAA,MAAAb,EAAAc,GAAAd,EAAAxG,UAAAG,SAAAqG,EAAAa,GAAA,KAAAV,EAAA,gBAAsFE,aAAaI,MAAA,UAAgBN,EAAA,OAAAH,EAAAa,GAAA,MAAAb,EAAAc,GAAAd,EAAAxG,UAAAC,UAAA,GAAAuG,EAAAa,GAAA,KAAAV,EAAA,WAAqFK,YAAA,mBAAAH,aAA4CI,MAAA,SAAgBC,OAAQC,QAAA,EAAAC,KAAA,YAA+BT,EAAA,gBAAqBE,aAAaI,MAAA,WAAiBN,EAAA,aAAkBO,OAAOK,KAAA,UAAAH,KAAA,UAAiCI,IAAKC,MAAAjB,EAAA7E,UAAoB6E,EAAAa,GAAA,sCAAAb,EAAAa,GAAA,KAAAV,EAAA,OAAqEE,aAAaa,MAAA,WAAgB,GAAAlB,EAAAa,GAAA,KAAAV,EAAA,YAAiCO,OAAOnH,KAAAyG,EAAApG,WAAAuH,cAAAnB,EAAAjC,iBAAAqD,OAAA,GAAAb,OAAA,6BAAyGJ,EAAA,mBAAwBO,OAAOW,MAAA,MAAAf,MAAA,OAA4BgB,YAAAtB,EAAAuB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAvB,EAAA,OAAAA,EAAA,QAA6BO,OAAOiB,GAAA3B,EAAApG,WAAA8H,EAAAE,QAAArC,WAA2CS,EAAAa,GAAAb,EAAAc,GAAAd,EAAApG,WAAA8H,EAAAE,QAAA7E,MAAA,yBAA0EiD,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOW,MAAA,QAAeC,YAAAtB,EAAAuB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAvB,EAAA,OAAAA,EAAA,QAA6BO,OAAOiB,GAAA3B,EAAApG,WAAA8H,EAAAE,QAAArC,WAA2CS,EAAAa,GAAAb,EAAAc,GAAAd,EAAApG,WAAA8H,EAAAE,QAAAjC,MAAA,uBAAwEK,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOW,MAAA,SAAAf,MAAA,OAA+BgB,YAAAtB,EAAAuB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAvB,EAAA,kBAA6BO,OAAOmB,SAAA,IAAcC,OAAQC,MAAA/B,EAAApG,WAAA8H,EAAAE,QAAA,KAAAI,SAAA,SAAAC,GAAmEjC,EAAAkC,KAAAlC,EAAApG,WAAA8H,EAAAE,QAAA,OAAAK,IAAoDE,WAAA,mCAA6ChC,EAAA,YAAiBO,OAAOW,OAAA,KAAcrB,EAAAa,GAAA,OAAAb,EAAAa,GAAA,KAAAV,EAAA,YAA2CO,OAAOW,OAAA,KAAerB,EAAAa,GAAA,kBAAyBb,EAAAa,GAAA,KAAAV,EAAA,mBAAoCO,OAAOW,MAAA,QAAeC,YAAAtB,EAAAuB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAvB,EAAA,YAAuBO,OAAOK,KAAA,WAAAqB,KAAA,EAAAP,SAAA,IAAyCC,OAAQC,MAAA/B,EAAApG,WAAA8H,EAAAE,QAAA,KAAAI,SAAA,SAAAC,GAAmEjC,EAAAkC,KAAAlC,EAAApG,WAAA8H,EAAAE,QAAA,wBAAAK,IAAAI,OAAAJ,IAA2FE,WAAA,2CAAoD,QAEp+EG,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACEnJ,EACAwG,GATF,EAVA,SAAA4C,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/155.67cf22e28f9c28f6d7f7.js", "sourcesContent": ["<template>\r\n  <div style=\"width: 100%;height: 100%;\">\r\n    <!---->\r\n    <div class=\"mhcx\">\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:left\">\r\n        <!-- <el-form-item style=\"float: left;\">\r\n          <div>当前审查任务：{{dialogObj.rwmc}}</div>\r\n        </el-form-item> -->\r\n        <el-form-item style=\"float: left;\">\r\n          <div>姓名：{{ dialogObj.xm }}</div>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: left;\">\r\n          <div>部门：{{ dialogObj.bm }}</div>\r\n        </el-form-item>\r\n        <el-form-item style=\"float: left;\">\r\n          <div>职务：{{ dialogObj.zw }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!---->\r\n      <el-form :inline=\"true\" size=\"medium\" class=\"demo-form-inline\" style=\"float:right\">\r\n        <el-form-item style=\"float: right;\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"submit\">\r\n            返回\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div style=\"clear: both;\"></div>\r\n    </div>\r\n    <!---->\r\n    <el-table :data=\"showDxList\" :span-method=\"objectSpanMethod\" border height=\"calc(100% - 58px - 5px)\">\r\n      <el-table-column label=\"自查类\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <span :id=\"showDxList[scope.$index].mdIndex\"></span>{{ showDxList[scope.$index].dxmc }}\r\n            <!-- {{ showDxList[scope.$index].dxmc }} -->\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"自查内容\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <span :id=\"showDxList[scope.$index].mdIndex\"></span>{{ showDxList[scope.$index].scnr }}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"是否符合要求\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-radio-group v-model=\"showDxList[scope.$index].sffh\" disabled>\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注说明\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input type=\"textarea\" v-model.trim=\"showDxList[scope.$index].bzsm\" :rows=\"3\" disabled></el-input>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getRyjczcx,\r\n  getAllRyjczcx,\r\n  getAllRyjcnr,\r\n  getRyjcnr,\r\n  deleteRyjcpfjl,\r\n  addRyjcpfjl,\r\n  updateDjztByRyid,\r\n  getLsRyzcpfjl,\r\n} from '../../../../api/zczp'\r\nimport {\r\n  // 获取注册信息\r\n  getDwxx,\r\n} from '../../../../api/dwzc'\r\nimport { getZczpIdsObj } from '../../../../utils/windowLocation'\r\n\r\nimport { writeOptionsLog } from '../../../../utils/logUtils'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogObj: {\r\n        // rwid: 'D66215EC-1B9A-408A-BEE8-7A9396165EA7',\r\n        // ccdryid: 'F9E1E031-3649-42C4-879E-5297B14B36D6'\r\n        zw: '',\r\n        xm: '',\r\n        bm: '',\r\n      },\r\n      //页面实际操作的评分数据[dx:{scnr:[]}]\r\n      showDxList: [],\r\n      //单元格合并规则\r\n      spanArr: [],\r\n      // 时间线排序方向\r\n      reverse: true,\r\n      // 锚点菜单集合\r\n      activities: [],\r\n      // 锚点菜单显隐\r\n      showMdmenu: false,\r\n      dwxx: {},\r\n      ryid: '',\r\n      rwid: '',\r\n      djzt: '',\r\n      rwmc: '',\r\n      dlzt: '',\r\n      jcjd:'',\r\n    }\r\n  },\r\n  computed: {},\r\n  components: {\r\n  },\r\n  mounted() {\r\n    this.rwid = this.$route.query.rwid\r\n    this.djzt = this.$route.query.djzt\r\n    this.ryid = this.$route.query.ryid\r\n    this.dlzt = this.$route.query.dlzt\r\n    console.log('this.rwid', this.rwid);\r\n    this.jcjd = this.$route.query.jcjd\r\n    this.dialogObj.zw = this.$route.query.zw\r\n    this.dialogObj.xm = this.$route.query.xm\r\n    this.dialogObj.bm = this.$route.query.bm\r\n    this.rwmc = this.$route.query.rwmc\r\n\r\n    this.getDwxxList()\r\n    // let params = this.$route.query\r\n    // let params = getZczpIdsObj()\r\n    // if (params && Object.keys(params).length > 0) {\r\n    //   console.log('抽查的人员登记', params)\r\n    //   this.dialogObj.rwid = params.rwid\r\n    //   this.dialogObj.ccdryid = params.ccdryid\r\n    //   // 获取抽查的人员信息\r\n    //   this.getCcdryxxByCcdryid()\r\n    //   //\r\n    this.getRyzcxxjl()\r\n    //   return\r\n    // }\r\n    // this.$message.warning('未能正确获取抽查的人员ID，请关闭页面重新进入')\r\n    // // 获取字典\r\n    // this.showDxList = this.getZD()\r\n  },\r\n  methods: {\r\n    submit() {\r\n      if (this.dlzt == 1) {\r\n        this.$router.push({\r\n          path: '/ccdry',\r\n          query: {\r\n            rwid: this.rwid,\r\n            rwmc: this.rwmc,\r\n            jcjd: this.jcjd,\r\n          }\r\n        })\r\n      } else if (this.dlzt == 2) {\r\n        this.$router.push({\r\n          path: '/jczj',\r\n          query: {\r\n            rwid: this.rwid,\r\n            rwmc: this.rwmc,\r\n            jcjd: this.jcjd,\r\n          }\r\n        })\r\n      }\r\n    },\r\n    // 获取单位信息集合（默认选中最后一个，也就是最新的单位信息）\r\n    async getDwxxList() {\r\n      let data = await getDwxx()\r\n      console.log(data);\r\n      this.dwxx = data\r\n    },\r\n\r\n    /**\r\n     * 获取人员详细自查记录\r\n     */\r\n    async getRyzcxxjl() {\r\n      let data = await getAllRyjczcx()\r\n      let data1 = []\r\n      let params = {\r\n        rwid: this.rwid,\r\n        ryid: this.ryid,\r\n      }\r\n      data1 = await getLsRyzcpfjl(params)\r\n      data1.forEach((item) => {\r\n        data.forEach((item1) => {\r\n          if (item1.zcxid == item.zcxid) {\r\n            item.dxmc = item1.zcxmc;\r\n          }\r\n        })\r\n      })\r\n      this.showDxList = data1\r\n      this.spanArr = this.getSpanArr(data1)\r\n      console.log(this.showDxList);\r\n      console.log(data);\r\n      console.log(data1);\r\n    },\r\n\r\n    returnSy() {\r\n      this.$router.go(-1)\r\n    },\r\n    getCcdryxxByCcdryid() {\r\n      let ccdryxx = selectCcdryxxByCcdryid(this.dialogObj)\r\n      console.log('ccdryxx', ccdryxx)\r\n      Object.assign(this.dialogObj, ccdryxx)\r\n      this.dialogObj = JSON.parse(JSON.stringify(this.dialogObj))\r\n    },\r\n    //----------------------------用来返回this.spanArr数组的，定义每一行的 rowspan-----------\r\n    getSpanArr(list) {\r\n      let spanArr = []\r\n      for (var i = 0; i < list.length; i++) {\r\n        if (i === 0) {\r\n          spanArr.push(1)\r\n          this.pos = 0\r\n        } else {\r\n          // 判断当前元素与上一个元素是否相同\r\n          if (list[i].zcxid == list[i - 1].zcxid) {\r\n            spanArr[this.pos] += 1\r\n            spanArr.push(0)\r\n          } else {\r\n            spanArr.push(1)\r\n            this.pos = i\r\n          }\r\n        }\r\n      }\r\n      return spanArr\r\n    },\r\n    objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      if (columnIndex === 0) {\r\n        //\r\n        const _row = this.spanArr[row.scid - 1]\r\n        return {\r\n          rowspan: _row,\r\n          colspan: 1\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * 获取部门详细自查记录字典\r\n     */\r\n    getZD() {\r\n      console.log(\"getZD\")\r\n      //\r\n      const zdList = getRyzcxxjlZD()\r\n      console.log(zdList)\r\n      zdList.forEach((nr) => {\r\n        if (nr.sffh === undefined) {\r\n          nr.sffh = true\r\n        }\r\n      });\r\n      this.spanArr = this.getSpanArr(zdList)\r\n      //\r\n      return zdList\r\n    },\r\n    // 锚点菜单鼠标移入事件\r\n    mouseoverMdMenu() {\r\n      this.showMdmenu = true\r\n    },\r\n    // 锚点菜单鼠标移出事件\r\n    mouseoutMenu() {\r\n      this.showMdmenu = false\r\n    }\r\n  },\r\n  watch: {\r\n    // '$route': {\r\n    //   handler (newVal, oldVal) {\r\n    //     console.log('route changed', newVal)\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    showDxList: {\r\n      handler(newVal, oldVal) {\r\n        ///////\r\n        // 清空锚点，防重复\r\n        this.activities = []\r\n        // 大项ID数组，用以对数据进行分组\r\n        let dxMdIdArr = []\r\n        ///////////\r\n        //\r\n        newVal.forEach((nr, nrIndex) => {\r\n          // nr.dxMdIndex = 'dxMd' + nr.zcxid\r\n          //\r\n          nr.mdIndex = 'md' + nrIndex\r\n          if (!nr.sffh) {\r\n            nr.mdIndex = 'md' + nrIndex\r\n            //\r\n            if (dxMdIdArr.indexOf(nr.zcxid) == -1) {\r\n              dxMdIdArr.push(nr.zcxid)\r\n            }\r\n            if (dxMdIdArr.indexOf(nr.zcxid) != -1) {\r\n              if (!this.activities[dxMdIdArr.indexOf(nr.zcxid)]) {\r\n                this.activities.push({\r\n                  dxmc: nr.dxmc,\r\n                  children: []\r\n                })\r\n              }\r\n              this.activities[dxMdIdArr.indexOf(nr.zcxid)].children.push({\r\n                href: '#' + nr.mdIndex,\r\n                nr: nr.scnr,\r\n                ykf: nr.ykf\r\n              })\r\n            }\r\n          }\r\n        })\r\n      },\r\n      deep: true\r\n    },\r\n  },\r\n\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/zczp/childPage/ccdryDjXqxx.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"}},[_c('div',{staticClass:\"mhcx\"},[_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"left\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"left\"}},[_c('div',[_vm._v(\"姓名：\"+_vm._s(_vm.dialogObj.xm))])]),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"left\"}},[_c('div',[_vm._v(\"部门：\"+_vm._s(_vm.dialogObj.bm))])]),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"float\":\"left\"}},[_c('div',[_vm._v(\"职务：\"+_vm._s(_vm.dialogObj.zw))])])],1),_vm._v(\" \"),_c('el-form',{staticClass:\"demo-form-inline\",staticStyle:{\"float\":\"right\"},attrs:{\"inline\":true,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"float\":\"right\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"medium\"},on:{\"click\":_vm.submit}},[_vm._v(\"\\n          返回\\n        \")])],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"clear\":\"both\"}})],1),_vm._v(\" \"),_c('el-table',{attrs:{\"data\":_vm.showDxList,\"span-method\":_vm.objectSpanMethod,\"border\":\"\",\"height\":\"calc(100% - 58px - 5px)\"}},[_c('el-table-column',{attrs:{\"label\":\"自查类\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('span',{attrs:{\"id\":_vm.showDxList[scope.$index].mdIndex}}),_vm._v(_vm._s(_vm.showDxList[scope.$index].dxmc)+\"\\n          \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"自查内容\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_c('span',{attrs:{\"id\":_vm.showDxList[scope.$index].mdIndex}}),_vm._v(_vm._s(_vm.showDxList[scope.$index].scnr)+\"\\n        \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"是否符合要求\",\"width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-radio-group',{attrs:{\"disabled\":\"\"},model:{value:(_vm.showDxList[scope.$index].sffh),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"sffh\", $$v)},expression:\"showDxList[scope.$index].sffh\"}},[_c('el-radio',{attrs:{\"label\":true}},[_vm._v(\"是\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":false}},[_vm._v(\"否\")])],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"备注说明\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3,\"disabled\":\"\"},model:{value:(_vm.showDxList[scope.$index].bzsm),callback:function ($$v) {_vm.$set(_vm.showDxList[scope.$index], \"bzsm\", (typeof $$v === 'string'? $$v.trim(): $$v))},expression:\"showDxList[scope.$index].bzsm\"}})]}}])})],1)],1)}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-556c0e8d\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/zczp/childPage/ccdryDjXqxx.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-556c0e8d\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ccdryDjXqxx.vue\")\n}\nvar normalizeComponent = require(\"!../../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdryDjXqxx.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ccdryDjXqxx.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-556c0e8d\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ccdryDjXqxx.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-556c0e8d\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/zczp/childPage/ccdryDjXqxx.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}