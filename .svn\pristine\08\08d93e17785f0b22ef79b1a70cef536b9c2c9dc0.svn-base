<template>
	<div class="bg_con">
		<el-button class="fhsmry" type="primary" size="small" @click="fhsmry">返回</el-button>
		<el-tabs v-model="activeName" @tab-click="handleClick" style="height: 100%;z-index: 1;
    position: relative;">
			<el-tab-pane label="设备信息详情" name="jbxx" style="height: 92%;">
				<div class="jbxx">
					<el-form ref="form" :model="jbxx" label-width="152px" size="mini" :label-position="labelPosition"
						disabled>
						<div style="display:flex;justify-content: center;">
							<el-form-item label="移动存储介质名称" prop="jzmc" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="移动存储介质名称" v-model="jbxx.jzmc" clearable disabled>
								</el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="保密编号" prop="bmbh" class="xm"
								style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="保密编号" v-model="jbxx.bmbh" clearable disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="资产编号" prop="zcbh" class="xm"
								style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="资产编号" v-model="jbxx.zcbh" clearable disabled>
								</el-input>
							</el-form-item>
						</div>
						<div style="display:flex;justify-content: center;">
							<el-form-item label="密级" class="xm" style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-radio-group v-model="jbxx.smmj">
									<el-radio v-for="item in sbmjxz" :label="item.id" :value="item.id" :key="item.id">{{
										item.mc }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="启用日期" prop="qyrq" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-date-picker v-model="jbxx.qyrq" clearable type="date" style="width: 100%;"
									placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
								</el-date-picker>
							</el-form-item>
							<el-form-item label="品牌型号" prop="sbxh" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="品牌型号" v-model="jbxx.sbxh" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="序列号" prop="xlh" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="序列号" v-model="jbxx.xlh" clearable></el-input>
							</el-form-item>
							<el-form-item label="存储容量" prop="ccrl" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="存储容量" v-model="jbxx.ccrl" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="存放地点（场所）" prop="cfdd" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="存放地点（场所）" v-model="jbxx.cfdd" clearable></el-input>
							</el-form-item>
							<el-form-item label="使用部门" prop="sybm" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="使用部门" v-model="jbxx.sybm" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="管理部门" prop="glbm" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="管理部门" v-model="jbxx.glbm" clearable></el-input>
							</el-form-item>
							<el-form-item label="责任人" prop="zrr" class="xm one-inpu"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="责任人" v-model="jbxx.zrr" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="使用情况" prop="syqk" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-radio-group v-model="jbxx.syqk">
									<el-radio v-for="item in sbsyqkxz" :label="item.id"
										:value="item.id" :key="item.id">{{ item.mc }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</div>
					</el-form>
				</div>
			</el-tab-pane>
			<el-tab-pane label="设备定密详情" name="sbdm" style="height: 100%;">
				<sbdm :msg="jbxx"></sbdm>
			</el-tab-pane>
			<el-tab-pane label="设备密级变更详情" name="sbdmbg" style="height: 100%;">
				<sbdmbg :msg="jbxx"></sbdmbg>
			</el-tab-pane>
			<el-tab-pane label="设备责任人变更详情" name="zrrbg" style="height: 100%;">
				<zrrbg :msg="jbxx"></zrrbg>
			</el-tab-pane>
			<el-tab-pane label="设备借用详情" name="sbjy" style="height: 100%;">
				<sbjy :msg="jbxx"></sbjy>
			</el-tab-pane>
			<el-tab-pane label="设备外出携带详情" name="sbwcxd" style="height: 100%;">
				<sbwcxd :msg="jbxx"></sbwcxd>
			</el-tab-pane>
			<el-tab-pane label="设备维修详情" name="sbwx" style="height: 100%;">
				<sbwx :msg="jbxx"></sbwx>
			</el-tab-pane>
			<el-tab-pane label="设备信息导入详情" name="sbxxdr" style="height: 100%;">
				<sbxxdr :msg="jbxx"></sbxxdr>
			</el-tab-pane>
			<el-tab-pane label="设备报废详情" name="sbbf" style="height: 100%;">
				<sbbf :msg="jbxx"></sbbf>
			</el-tab-pane>
			<el-tab-pane label="设备销毁详情" name="sbxh" style="height: 100%;">
				<sbxh :msg="jbxx"></sbxh>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
import sbdm from './sbspdxqy/sbdmxqy.vue'
import sbdmbg from './sbspdxqy/sbdmbgxqy.vue'
import zrrbg from './sbspdxqy/sbzrrbgxqy.vue'
import sbjy from './sbspdxqy/sbjyxqy.vue'
import sbwcxd from './sbspdxqy/sbwcxdxqy.vue'
import sbwx from './sbspdxqy/sbwxxqy.vue'
import sbxxdr from './sbspdxqy/sbxxdrxqy.vue'
import sbbf from './sbspdxqy/sbbfxqy.vue'
import sbxh from './sbspdxqy/sbxhxqy.vue'
import {
	getAllSmztYy, //原因
	getAllSyqk, //状态
	getAllSmsbmj //密级
} from '../../../api/xlxz'
export default {
	data() {
		return {
			activeName: 'jbxx',
			jbxx: {},
			gwmc: [],
			jbxxsj: {},
			updateItemOld: {},
			labelPosition: 'right',
			sbmjxz: [],
			regionOption: [], //地域信息
			regionParams: {
				label: 'label', //这里可以配置你们后端返回的属性
				value: 'label',
				children: 'childrenRegionVo',
				expandTrigger: 'click',
				checkStrictly: true,
			},
			smdjxz: [],
			gwqdyjxz: [],
			jbzcxz: [],
			zgxlxz: [],
			sflxxz: [],
			yrxsxz: [],
			ztscyyxz: [], //生产原因
			sbsyqkxz: [],

			imageUrl: '',
			smryid: '',
		};
	},
	computed: {
	},
	components: {
		sbdm,
		sbdmbg,
		sbjy,
		sbwcxd,
		sbwx,
		sbxxdr,
		sbbf,
		sbxh,
		zrrbg
	},
	mounted() {
		this.smdj()
		this.ztzt()
		this.ztyy()

		console.log(this.$route.query.row);
		this.jbxxsj = JSON.parse(JSON.stringify(this.$route.query.row))
		this.jbxx = this.jbxxsj
		// this.smryid = JSON.parse(JSON.stringify(this.$route.query.row.smryid))
		// console.log('this.smryid', this.smryid);
		console.log('this.jbxx', this.jbxx);
	},
	methods: {
		async ztyy() {
			this.ztscyyxz = await getAllSmztYy()
		},
		async ztzt() {
			this.sbsyqkxz = await getAllSyqk()
		},
		//获取涉密等级信息
		async smdj() {
			let data = await getAllSmsbmj()
			this.sbmjxz = data
		},
		handleClick(tab, event) {
			console.log(tab, event);
		},
		//返回涉密人员
		fhsmry() {
			this.$router.push({
				path: '/smydccjz'
			})
		},
	},
	watch: {},

};
</script>
<style scoped>
.bg_con {
	width: 100%;
	height: calc(100% - 38px);
}



>>>.el-tabs__content {
	height: 100%;
}

.jbxx {
	height: 92%;
	display: flex;
	justify-content: center;
	height: 100%;
	overflow-y: scroll;
	background: #fff;
}

.xm {
	background-color: #fff;
}

.container {
	height: 92%;
}

.dabg {
	box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
	border-radius: 8px;
	width: 100%;
}

.item_button {
	height: 100%;
	float: left;
	padding-left: 10px;
	line-height: 50px;
}

.select_wrap {

	.select_wrap_content {
		float: left;
		width: 100%;
		line-height: 50px;
		height: 100%;
		background: rgba(255, 255, 255, 0.7);

		.item_label {
			padding-left: 10px;
			height: 100%;
			float: left;
			line-height: 50px;
			font-size: 1em;
		}
	}
}

.mhcx1 {
	margin-top: 0px;
}

.widthw {
	width: 6vw;
}


/deep/.el-date-editor.el-input,
.el-date-editor.el-input__inner {
	width: 184px;
}

/deep/.el-radio-group {
	width: 570px;
	margin-left: 15px;
}

/deep/.mhcx .el-form-item {
	margin-top: 5px;
	margin-bottom: 5px;
}

/deep/.el-dialog {
	margin-top: 6vh !important;
}

/deep/.inline-inputgw {
	width: 105%;
}

.drfs {
	width: 126px
}

.daochu {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

/deep/.el-select .el-select__tags>span {
	display: flex !important;
	flex-wrap: wrap;
}

/deep/.el-dialog__body .el-form>div .el-form-item__label {
	width: 155px !important;
}

.bz {
	height: 72px !important;
}

/deep/.el-dialog__body .el-form>div>div {
	/* width: auto; */
	max-width: 100%;
}

.el-select__tags {
	white-space: nowrap;
	overflow: hidden;
}

.dialog-footer {
	display: block;
	margin-top: 10px;
}

.xmr /deep/.el-dialog__body .el-form .el-form-item--mini.el-form-item {
	height: 52px;
}

.avatar-uploader .el-upload {
	border: 1px dashed #d9d9d9;
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;

}

.avatar-uploader .el-upload:hover {
	border-color: #409EFF;
}

.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 482px;
	height: 254px;
	line-height: 254px;
	text-align: center;
}

.fhsmry {
	float: right;
	z-index: 99;
	margin-top: 5px;
	position: relative;
}

.avatar {
	width: 400px;
	height: 254px;
}

>>>.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
	margin-bottom: 0px;
}

.xm>>>.el-form-item__label {
	line-height: 50px;
	background-color: #f3f8ff;
}

/deep/.el-form-item--mini .el-form-item__content,
.el-form-item--mini .el-form-item__label {
	line-height: 50px;
	width: 330px !important;
}

/deep/.el-select>.el-input,
.el-color-picker__icon,
.el-input {
	margin-left: 15px;
	width: 300px !important;
}

/deep/.el-textarea {
	margin-left: 15px;
	width: 784px !important;
}

.one-line-bz>>>.el-form-item__content {
	line-height: 50px;
	width: 814px !important;
}

.one-input>>>.el-input {
	width: 784px !important;
}

/deep/.el-cascader--mini {
	margin-left: 15px;
	width: 300px !important;
}

/deep/.el-select .el-tag {
	margin-left: 28px;
}
</style>
