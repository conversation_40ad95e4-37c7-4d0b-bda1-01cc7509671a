webpackJsonp([100],{"AK/S":function(t,e){},zQmN:function(t,e,l){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=l("mvHQ"),i=l.n(s),a=l("Xxa5"),r=l.n(a),o=l("exGp"),n=l.n(o),c=l("bOdI"),u=l.n(c),m=l("gyMJ"),d=l("CjjO"),f=l("rouf"),g=l("G3m0"),p=l("VJZG"),x=l("l3bR"),b=l("TSC9"),v={components:{},props:{},data:function(){var t,e=this;return{yearSelect:[],existDrList:[],dialogVisible_dr_zj:!1,sfzhm:"",pdmsfzhm:0,smdjxz:[],gwqdyjxz:[],jbzcxz:[],zgxlxz:[],sflxxz:[],yrxsxz:[],gwmc:[],xb:[{xb:"男",id:1},{xb:"女",id:2}],sfsc:[{sfscid:1,sfscmc:"是"},{sfscid:0,sfscmc:"否"}],labelPosition:"right",smryList:[],formInline:{xm:void 0,bmmc:void 0,tzsj:(new Date).getFullYear().toString()},tjlist:{xm:"",sfzhm:"",xb:"",nl:"",lxdh:"",bmmc:"",gwmc:"",smdj:"",gwqdyj:"",zgxl:"",zw:"",jbzc:"",zc:"",gwdyjb:"",sflx:"",yrxs:"",sfsc:1,sfcrj:1,sfbgzj:1,yx:"",sgsj:"",bz:""},xglist:{},bmid:"",updateItemOld:{},xgdialogVisible:!1,xqdialogVisible:!1,dialogVisible_dr:!1,dr_cyz_list:[],multipleTable:[],page:1,pageSize:10,total:0,selectlistRow:[],dialogVisible:!1,rules:(t={xm:[{required:!0,message:"请输入姓名",trigger:"blur"}],sfzhm:[{required:!0,message:"身份证不能为空",trigger:"blur"},{validator:function(t,l,s){var i=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2];if(/^\d{17}\d|x$/i.test(l)){for(var a=0,r=0;r<l.length-1;r++)a+=parseInt(l.substr(r,1),10)*i[r];if([1,0,"X",9,8,7,6,5,4,3,2][a%11]==l.substr(17,1).toUpperCase()){if(s(),e.tjlist.sfzhm){var o=e.tjlist.sfzhm.substring(6,14),n=e.tjlist.sfzhm.substring(16,17)%2==1?1:2,c=o.substring(0,4)+"-"+o.substring(4,6)+"-"+o.substring(6,8),u=new Date(c.replace(/-/g,"/")),m=new Date,d=m.getFullYear()-u.getFullYear()-(m.getMonth()<u.getMonth()||m.getMonth()==u.getMonth()&&m.getDate()<u.getDate()?1:0);e.tjlist.xb=n,e.tjlist.nl=d}if(e.xglist.sfzhm){o=e.xglist.sfzhm.substring(6,14),n=e.xglist.sfzhm.substring(16,17)%2==1?1:2,c=o.substring(0,4)+"-"+o.substring(4,6)+"-"+o.substring(6,8),u=new Date(c.replace(/-/g,"/"));var f=new Date,g=f.getFullYear()-u.getFullYear()-(f.getMonth()<u.getMonth()||f.getMonth()==u.getMonth()&&f.getDate()<u.getDate()?1:0);e.xglist.xb=n,e.xglist.nl=g}}else s("身份证格式有误")}else s("身份证格式有误")},trigger:"blur"}],xb:[{required:!0,message:"请选择性别",trigger:"blur"}],nl:[{required:!0,message:"请输入年龄",trigger:"blur"}],bmmc:[{required:!0,message:"请输入部门",trigger:["blur","change"]}],gwmc:[{required:!0,message:"请输入岗位名称",trigger:"blur"}],smdj:[{required:!0,message:"请选择涉密等级",trigger:"blur"}],gwqdyj:[{required:!0,message:"请选择岗位确定依据",trigger:"blur"}],zgxl:[{required:!0,message:"请选择最高学历",trigger:"blur"}],zw:[{required:!0,message:"请输入职务",trigger:"blur"}],jbzc:[{required:!0,message:"请输入职级",trigger:"blur"}],zc:[{required:!0,message:"请选择级别职称",trigger:"blur"}],sflx:[{required:!0,message:"请选择身份类型",trigger:"blur"}],yrxs:[{required:!0,message:"请选择用人形式",trigger:"blur"}],sfsc:[{required:!0,message:"请选择是否审查",trigger:"blur"}],sfcrj:[{required:!0,message:"请选择是否出入境登记备案",trigger:"blur"}]},u()(t,"sfcrj",[{required:!0,message:"请选择是否出入境登记备案",trigger:"blur"}]),u()(t,"sfbgzj",[{required:!0,message:"请选择是否统一保管出入境证件",trigger:"blur"}]),u()(t,"sfbgzj",[{required:!0,message:"请选择是否统一保管出入境证件",trigger:"blur"}]),u()(t,"yx",[{required:!0,message:"请输入邮箱",trigger:"blur"}]),u()(t,"sgsj",[{required:!0,message:"请选择上岗时间（现涉密岗位）",trigger:"blur"}]),u()(t,"lxdh",[{required:!0,message:"请输入联系电话",trigger:"blur"},{validator:function(t,e,l){if(!e)return new Error("请输入电话号码");var s=/^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/.test(e);"number"!=typeof(e=Number(e))||isNaN(e)?l(new Error("请输入电话号码")):(e=e.toString()).length<0||e.length>12||!s?l(new Error("手机号格式:138xxxx8754")):l()},trigger:"blur"}]),t),regionOption:[],regionParams:{label:"label",value:"label",children:"childrenRegionVo",expandTrigger:"click",checkStrictly:!0},dwmc:"",year:"",yue:"",ri:"",Date:"",xh:[],dclist:[],dr_dialog:!1,sjdrfs:"",cxbmsj:""}},computed:{},mounted:function(){for(var t=[],e=(new Date).getFullYear();e>(new Date).getFullYear()-10;e--)t.push({label:e.toString(),value:e.toString()});t.unshift({label:"全部",value:""}),this.yearSelect=t,this.zwmh(),this.smdj(),this.gwqdyjlx(),this.zgxl(),this.jbzc(),this.yrxs(),this.sflx(),this.smry(),this.zzjg(),this.zhsj()},methods:{zzjg:function(){var t=this;return n()(r.a.mark(function e(){var l,s,i,a;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(m._14)();case 2:return l=e.sent,console.log(l),t.zzjgmc=l,s=[],console.log(t.zzjgmc),t.zzjgmc.forEach(function(e){var l=[];t.zzjgmc.forEach(function(t){e.bmm==t.fbmm&&(l.push(t),e.childrenRegionVo=l)}),s.push(e)}),console.log(s),console.log(s[0].childrenRegionVo),i=[],e.next=13,Object(m.U)();case 13:""==(a=e.sent).fbmm&&s.forEach(function(t){""==t.fbmm&&i.push(t)}),""!=a.fbmm&&s.forEach(function(t){console.log(t),t.fbmm==a.fbmm&&i.push(t)}),console.log(i),i[0].childrenRegionVo.forEach(function(e){t.regionOption.push(e)});case 18:case"end":return e.stop()}},e,t)}))()},zhsj:function(){var t=this;return n()(r.a.mark(function e(){var l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(x.i)();case 2:l=e.sent,console.log(l),""!=l&&(t.tjlist=l),t.tjlist.xm="",t.tjlist.sfzhm="",t.tjlist.xb=0,t.tjlist.nl="",t.tjlist.lxdh="",t.tjlist.bz="",t.tjlist.yx="",t.tjlist.bmmc=t.tjlist.bmmc.split("/");case 13:case"end":return e.stop()}},e,t)}))()},smdj:function(){var t=this;return n()(r.a.mark(function e(){var l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.e)();case 2:l=e.sent,t.smdjxz=l;case 4:case"end":return e.stop()}},e,t)}))()},gwqdyjlx:function(){var t=this;return n()(r.a.mark(function e(){var l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.b)();case 2:l=e.sent,console.log(l),t.gwqdyjxz=l;case 5:case"end":return e.stop()}},e,t)}))()},zgxl:function(){var t=this;return n()(r.a.mark(function e(){var l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.j)();case 2:l=e.sent,console.log(l),t.zgxlxz=l;case 5:case"end":return e.stop()}},e,t)}))()},jbzc:function(){var t=this;return n()(r.a.mark(function e(){var l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.c)();case 2:l=e.sent,console.log(l),t.jbzcxz=l;case 5:case"end":return e.stop()}},e,t)}))()},yrxs:function(){var t=this;return n()(r.a.mark(function e(){var l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.k)();case 2:l=e.sent,console.log(l),t.yrxsxz=l;case 5:case"end":return e.stop()}},e,t)}))()},sflx:function(){var t=this;return n()(r.a.mark(function e(){var l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.d)();case 2:l=e.sent,console.log(l),t.sflxxz=l;case 5:case"end":return e.stop()}},e,t)}))()},formatTime:function(t){},Radio:function(t){},mbxzgb:function(){},mbdc:function(){},morenzhi:function(){},xz:function(){this.dialogVisible=!0},fgDr:function(){},chooseFile:function(){},handleSelectionChange:function(t){},drcy:function(){},readExcel:function(t){},onSubmit:function(){this.page=1,this.smry()},returnSy:function(){this.$router.push("/tzglsy")},smry:function(){var t=this;return n()(r.a.mark(function e(){var l,s;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return l={page:t.page,pageSize:t.pageSize,xm:t.formInline.xm,bmmc:t.cxbmsj},t.formInline.tzsj&&(l.tznf=t.formInline.tzsj),""==t.cxbmsj&&(l.bmmc=t.formInline.bmmc),e.next=5,Object(d.q)(l);case 5:s=e.sent,t.smryList=s.records,t.total=s.total;case 8:case"end":return e.stop()}},e,t)}))()},shanchu:function(t){var e=this,l=this;this.$confirm("是否继续删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.selectlistRow.forEach(function(t){var e={smryid:t.smryid};Object(m._38)(e).then(function(){l.smry()})}),e.$message({message:"删除成功",type:"success"})}).catch(function(){e.$message("已取消删除")})},showDialog:function(){this.dialogVisible=!0},fh:function(){this.$router.go(-1)},resetForm:function(){this.tjlist.xm="",this.tjlist.sfzhm="",this.tjlist.xb="",this.tjlist.nl="",this.tjlist.lxdh="",this.tjlist.bmmc="",this.tjlist.gwmc="",this.tjlist.smdj="",this.tjlist.gwqdyj="",this.tjlist.zgxl="",this.tjlist.zw="",this.tjlist.jbzc="",this.tjlist.zc="",this.tjlist.sflx="",this.tjlist.yrxs="",this.tjlist.sfsc="是",this.tjlist.sfcrj="是",this.tjlist.sfbgzj="是",this.tjlist.yx="",this.tjlist.sgsj=this.Date,this.tjlist.bz=""},submitTj:function(t){var e=this;this.$refs[t].validate(function(t){if(!t)return console.log("error submit!!"),!1;var l={xm:e.tjlist.xm,sfzhm:e.tjlist.sfzhm,dwid:"901",bmid:e.bmid,xb:e.tjlist.xb,nl:e.tjlist.nl,lxdh:e.tjlist.lxdh,bmmc:e.tjlist.bmmc.join("/"),gwmc:e.tjlist.gwmc,smdj:e.tjlist.smdj,gwqdyj:e.tjlist.gwqdyj,zgxl:e.tjlist.zgxl,zw:e.tjlist.zw,zj:e.tjlist.zj,jbzc:e.tjlist.jbzc,sflx:e.tjlist.sflx,yrxs:e.tjlist.yrxs,sfsc:e.tjlist.sfsc,sfbgzj:e.tjlist.sfbgzj,sfcrj:e.tjlist.sfcrj,sgsj:e.tjlist.sgsj,bz:e.tjlist.bz,cjrid:"901"};if(e.onInputBlur(1),1e4==e.pdmsfzhm.code){var s=e;Object(m._65)(l).then(function(){s.resetForm(),s.smry(),s.zwmh(),s.zjmh(),s.morenzhi()}),e.dialogVisible=!1,e.$message({message:"添加成功",type:"success"})}})},updataDialog:function(t){var e=this;this.$refs[t].validate(function(t){if(!t)return console.log("error submit!!"),!1;var l=e;e.xglist.bmmc=e.xglist.bmmc.join("/"),Object(m._102)(e.xglist).then(function(){l.smry(),l.zwmh(),l.zjmh()}),e.$message.success("修改成功"),e.xgdialogVisible=!1})},xqyl:function(t){this.updateItemOld=JSON.parse(i()(t)),this.xglist=JSON.parse(i()(t)),console.log("old",t),console.log("this.xglist.ywlx",this.xglist),this.xglist.bmmc=this.xglist.bmmc.split("/"),this.xqdialogVisible=!0},updateItem:function(t){var e=this;return n()(r.a.mark(function l(){var s,a;return r.a.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return e.updateItemOld=JSON.parse(i()(t)),e.xglist=JSON.parse(i()(t)),console.log("old",t),console.log("this.xglist.ywlx",e.xglist),s={bmmc:e.xglist.bmmc},l.next=7,Object(p.c)(s);case 7:a=l.sent,e.restaurants=a,e.gwmc=a,console.log(e.restaurants),e.xglist.bmmc=e.xglist.bmmc.split("/"),e.xgdialogVisible=!0;case 13:case"end":return l.stop()}},l,e)}))()},deleteTkglBtn:function(t){},selectRow:function(t){this.selectlistRow=t},handleCurrentChange:function(t){this.page=t,this.smry()},handleSizeChange:function(t){this.page=1,this.pageSize=t,this.smry()},handleClose:function(t){this.resetForm(),this.dialogVisible=!1},close:function(t){this.$refs[t].resetFields()},close1:function(t){this.$refs[t].resetFields()},exportList:function(){var t=this;return n()(r.a.mark(function e(){var l,s,i,a;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0==t.formInline.bmmc){e.next=7;break}return l={bmmc:t.formInline.bmmc,gwmc:t.formInline.xm,tznf:t.formInline.tzsj},e.next=4,Object(f.I)(l);case 4:s=e.sent,e.next=10;break;case 7:return e.next=9,Object(f.I)({nf:(new Date).getFullYear().toString()});case 9:s=e.sent;case 10:i=new Date,a=i.getFullYear()+""+(i.getMonth()+1)+i.getDate(),t.dom_download(s,"在岗涉密人员信息表-"+a+".xls");case 13:case"end":return e.stop()}},e,t)}))()},dom_download:function(t,e){var l=new Blob([t]),s=window.URL.createObjectURL(l),i=document.createElement("a");i.style.display="none",i.href=s,i.setAttribute("download",e),document.body.appendChild(i),i.click()},querySearch1:function(t,e){},querySearch:function(t,e){var l=this.restaurants;console.log("restaurants",l);var s=t?l.filter(this.createFilter(t)):l;console.log("results",s),e(s),console.log("cb(results.dwmc)",s)},createFilter:function(t){return function(e){return e.gwmc.toLowerCase().indexOf(t.toLowerCase())>-1}},smbm:function(){},handleSelect:function(t){var e=this,l=[],s=[],i=[],a=[];t.forEach(function(t){e.gwmc.forEach(function(e){t==e.gwmc&&l.push(e)})}),console.log(l),l.forEach(function(t){console.log(t),1==t.smdj?s.push(t):2==t.smdj?i.push(t):a.push(t)}),s.length>0?(this.tjlist.smdj=s[0].smdj,this.tjlist.gwqdyj=s[0].gwqdyj):i.length>0?(this.tjlist.smdj=i[0].smdj,this.tjlist.gwqdyj=i[0].gwqdyj):a.length>0&&(this.tjlist.smdj=a[0].smdj,this.tjlist.gwqdyj=a[0].gwqdyj)},handleSelect1:function(t){var e=this,l=[],s=[],i=[],a=[];t.forEach(function(t){e.gwmc.forEach(function(e){t==e.gwmc&&l.push(e)})}),console.log(l),l.forEach(function(t){"核心"==t.smdj?s.push(t):"重要"==t.smdj?i.push(t):a.push(t)}),console.log(s),console.log(i),console.log(a),s.length>0?(this.xglist.smdj=s[0].smdj,this.xglist.gwqdyj=s[0].gwqdyj):i.length>0?(this.xglist.smdj=i[0].smdj,this.xglist.gwqdyj=i[0].gwqdyj):a.length>0&&(this.xglist.smdj=a[0].smdj,this.xglist.gwqdyj=a[0].gwqdyj)},handleChange:function(t){var e=this;return n()(r.a.mark(function l(){var s,i,a,o;return r.a.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:if(s=e.$refs.cascaderArr.getCheckedNodes()[0].data,console.log(s),e.bmid=s.bmm,i=void 0,1!=t){l.next=11;break}return a={bmmc:e.tjlist.bmmc.join("/")},l.next=8,Object(p.c)(a);case 8:i=l.sent,l.next=16;break;case 11:if(2!=t){l.next=16;break}return o={bmmc:e.xglist.bmmc.join("/")},l.next=15,Object(p.c)(o);case 15:i=l.sent;case 16:console.log(i),e.restaurants=i,e.gwmc=i,0==e.gwmc.length&&e.$message.error("该部门没有添加岗位"),console.log(e.gwmc),e.tjlist.gwmc="",e.tjlist.smdj="",e.tjlist.gwqdyj="",e.xglist.gwmc="",e.xglist.smdj="",e.xglist.gwqdyj="";case 27:case"end":return l.stop()}},l,e)}))()},querySearchzw:function(t,e){var l=this.restaurantszw;console.log("restaurants",l);var s=t?l.filter(this.createFilterzw(t)):l;console.log("results",s);for(var i=0;i<s.length;i++)for(var a=i+1;a<s.length;a++)s[i].zw===s[a].zw&&(s.splice(a,1),a--);e(s),console.log("cb(results.zw)",s)},createFilterzw:function(t){return function(e){return e.zw.toLowerCase().indexOf(t.toLowerCase())>-1}},zwmh:function(){var t=this;return n()(r.a.mark(function e(){var l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(m.x)();case 2:l=e.sent,t.restaurantszw=l,t.restaurantszj=l;case 5:case"end":return e.stop()}},e,t)}))()},querySearchzj:function(t,e){var l=this.restaurantszj;console.log("restaurants",l);var s=t?l.filter(this.createFilterzj(t)):l;console.log("results",s);for(var i=0;i<s.length;i++)for(var a=i+1;a<s.length;a++)s[i].zj===s[a].zj&&(s.splice(a,1),a--);e(s),console.log("cb(results.zw)",s)},createFilterzj:function(t){return function(e){return e.zj.toLowerCase().indexOf(t.toLowerCase())>-1}},zjmh:function(){},onInputBlur:function(t){var e=this;return n()(r.a.mark(function l(){var s;return r.a.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:if(1!=t){l.next=7;break}return s={sfzhm:e.tjlist.sfzhm},l.next=4,Object(b.k)(s);case 4:e.pdmsfzhm=l.sent,console.log(e.pdsmzt),20008==e.pdmsfzhm.code&&e.$message.error("人员已存在");case 7:case"end":return l.stop()}},l,e)}))()},cz:function(){this.cxbmsj="",this.formInline={}},cxbm:function(t){console.log(t),void 0!=t&&(this.cxbmsj=t.join("/"))},forsmdj:function(t){var e=void 0;return this.smdjxz.forEach(function(l){t.smdj==l.id&&(e=l.mc)}),e},forzc:function(t){var e=void 0;return this.jbzcxz.forEach(function(l){t.jbzc==l.id&&(e=l.mc)}),e},forsc:function(t){var e=void 0;return this.sfsc.forEach(function(l){t.sfsc==l.sfscid&&(e=l.sfscmc)}),e}},watch:{}},h={render:function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"bg_con",staticStyle:{height:"calc(100% - 38px)"}},[l("div",{staticStyle:{width:"100%",position:"relative",overflow:"hidden",height:"100%"}},[l("div",{staticClass:"dabg",staticStyle:{height:"100%"}},[l("div",{staticClass:"content",staticStyle:{height:"100%"}},[l("div",{staticClass:"table",staticStyle:{height:"100%"}},[l("div",{staticClass:"mhcx"},[l("el-form",{staticClass:"demo-form-inline",staticStyle:{float:"left"},attrs:{inline:!0,model:t.formInline,size:"medium"}},[l("el-form-item",{staticStyle:{"font-weight":"700"},attrs:{label:"台账时间"}},[l("el-select",{attrs:{placeholder:"台账时间"},model:{value:t.formInline.tzsj,callback:function(e){t.$set(t.formInline,"tzsj",e)},expression:"formInline.tzsj"}},t._l(t.yearSelect,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),l("el-form-item",{staticStyle:{"font-weight":"700"},attrs:{label:"部门"}},[l("el-cascader",{ref:"cascaderArr",attrs:{options:t.regionOption,props:t.regionParams,filterable:"",clearable:""},on:{change:t.cxbm},model:{value:t.formInline.bmmc,callback:function(e){t.$set(t.formInline,"bmmc",e)},expression:"formInline.bmmc"}})],1),t._v(" "),l("el-form-item",{staticStyle:{"font-weight":"700"},attrs:{label:"姓名"}},[l("el-input",{staticClass:"widthw",attrs:{clearable:"",placeholder:"姓名"},model:{value:t.formInline.xm,callback:function(e){t.$set(t.formInline,"xm",e)},expression:"formInline.xm"}})],1),t._v(" "),l("el-form-item",[l("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSubmit}},[t._v("查询")])],1),t._v(" "),l("el-form-item",[l("el-button",{attrs:{type:"warning",icon:"el-icon-circle-close"},on:{click:t.cz}},[t._v("重置")])],1)],1),t._v(" "),l("el-form",{staticClass:"demo-form-inline",staticStyle:{float:"right"},attrs:{inline:!0,model:t.formInline,size:"medium"}},[l("el-form-item",{staticStyle:{float:"right"}},[l("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(e){return t.fh()}}},[t._v("返回\n                  ")])],1),t._v(" "),l("el-form-item",{staticStyle:{float:"right"}},[l("el-button",{attrs:{type:"primary",size:"medium",icon:"el-icon-download"},on:{click:t.exportList}},[t._v("\n                    导出\n                  ")])],1)],1)],1),t._v(" "),l("div",{staticClass:"table_content_padding",staticStyle:{height:"100%"}},[l("div",{staticClass:"table_content",staticStyle:{height:"100%"}},[l("el-table",{staticStyle:{width:"100%",border:"1px solid #EBEEF5"},attrs:{data:t.smryList,border:"","header-cell-style":{background:"#EEF7FF",color:"#4D91F8"},height:"calc(100% - 34px - 44px - 10px)",stripe:""},on:{"selection-change":t.selectRow}},[l("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),t._v(" "),l("el-table-column",{attrs:{type:"index",width:"60",label:"序号",align:"center"}}),t._v(" "),l("el-table-column",{attrs:{prop:"xm",label:"姓名"}}),t._v(" "),l("el-table-column",{attrs:{prop:"bmmc",label:"部门"}}),t._v(" "),l("el-table-column",{attrs:{prop:"gwmc",label:"岗位名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("div",[t._v("\n                        "+t._s(e.row.gwmc.join(","))+"\n                      ")])]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"smdj",label:"涉密等级",formatter:t.forsmdj}}),t._v(" "),l("el-table-column",{attrs:{prop:"zw",label:"职务"}}),t._v(" "),l("el-table-column",{attrs:{prop:"zj",label:"职级"}}),t._v(" "),l("el-table-column",{attrs:{prop:"jbzc",label:"职称",formatter:t.forzc}}),t._v(" "),l("el-table-column",{attrs:{prop:"sfsc",label:"是否审查",formatter:t.forsc}}),t._v(" "),l("el-table-column",{attrs:{prop:"sgsj",label:"上岗时间"}}),t._v(" "),l("el-table-column",{attrs:{prop:"tznf",label:"台账时间"}}),t._v(" "),l("el-table-column",{attrs:{prop:"",label:"操作",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-button",{attrs:{size:"medium",type:"text"},on:{click:function(l){return t.xqyl(e.row)}}},[t._v("详情\n                      ")])]}}])})],1),t._v(" "),l("div",{staticStyle:{border:"1px solid #ebeef5"}},[l("el-pagination",{attrs:{background:"","pager-count":5,"current-page":t.page,"page-sizes":[5,10,20,30],"page-size":t.pageSize,layout:"total, prev, pager, sizes,next, jumper",total:t.total},on:{"current-change":t.handleCurrentChange,"size-change":t.handleSizeChange}})],1)],1)])])]),t._v(" "),l("el-dialog",{staticClass:"scbg-dialog",attrs:{width:"1000px",height:"800px",title:"导入涉密人员汇总情况",visible:t.dialogVisible_dr,"show-close":""},on:{"update:visible":function(e){t.dialogVisible_dr=e}}},[l("div",{staticStyle:{height:"600px"}},[l("el-table",{ref:"multipleTable",staticStyle:{width:"100%",border:"1px solid #EBEEF5"},attrs:{data:t.dr_cyz_list,height:"100%",stripe:""},on:{"selection-change":t.handleSelectionChange}},[l("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),t._v(" "),l("el-table-column",{attrs:{prop:"姓名",label:"姓名"}}),t._v(" "),l("el-table-column",{attrs:{prop:"部门",label:"部门"}}),t._v(" "),l("el-table-column",{attrs:{prop:"岗位名称",label:"岗位名称"}}),t._v(" "),l("el-table-column",{attrs:{prop:"涉密等级",label:"涉密等级"}}),t._v(" "),l("el-table-column",{attrs:{prop:"职务",label:"职务"}}),t._v(" "),l("el-table-column",{attrs:{prop:"职级",label:"职级"}}),t._v(" "),l("el-table-column",{attrs:{prop:"级别职称",label:"级别职称"}}),t._v(" "),l("el-table-column",{attrs:{prop:"是否审查",label:"是否审查"}}),t._v(" "),l("el-table-column",{attrs:{prop:"上岗时间",label:"上岗时间"}}),t._v(" "),l("el-table-column",{attrs:{prop:"备注",label:"备注"}})],1)],1),t._v(" "),l("div",{staticStyle:{height:"30px",display:"flex","align-items":"center","justify-content":"center",margin:"10px 0"}},[l("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.drcy}},[t._v("导 入")]),t._v(" "),l("el-button",{attrs:{type:"warning",size:"mini"},on:{click:function(e){t.dialogVisible_dr=!1}}},[t._v("关 闭")])],1)]),t._v(" "),l("el-dialog",{staticClass:"scbg-dialog",attrs:{width:"1000px",height:"800px",title:"导入[追加模式]已存在涉密人员汇总情况",visible:t.dialogVisible_dr_zj,"show-close":""},on:{"update:visible":function(e){t.dialogVisible_dr_zj=e}}},[l("div",{staticStyle:{height:"600px"}},[l("el-table",{ref:"multipleTable",staticStyle:{width:"100%",border:"1px solid #EBEEF5"},attrs:{data:t.existDrList,height:"100%",stripe:""},on:{"selection-change":t.handleSelectionChange}},[l("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),t._v(" "),l("el-table-column",{attrs:{prop:"姓名",label:"姓名"}}),t._v(" "),l("el-table-column",{attrs:{prop:"部门",label:"部门"}}),t._v(" "),l("el-table-column",{attrs:{prop:"岗位名称",label:"岗位名称"}}),t._v(" "),l("el-table-column",{attrs:{prop:"涉密等级",label:"涉密等级"}}),t._v(" "),l("el-table-column",{attrs:{prop:"职务",label:"职务"}}),t._v(" "),l("el-table-column",{attrs:{prop:"职级",label:"职级"}}),t._v(" "),l("el-table-column",{attrs:{prop:"级别职称",label:"级别职称"}}),t._v(" "),l("el-table-column",{attrs:{prop:"是否审查",label:"是否审查"}}),t._v(" "),l("el-table-column",{attrs:{prop:"上岗时间",label:"上岗时间"}}),t._v(" "),l("el-table-column",{attrs:{prop:"备注",label:"备注"}})],1)],1),t._v(" "),l("div",{staticStyle:{height:"30px",display:"flex","align-items":"center","justify-content":"center",margin:"10px 0"}},[l("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.fgDr}},[t._v("覆 盖")]),t._v(" "),l("el-button",{attrs:{type:"warning",size:"mini"},on:{click:function(e){t.dialogVisible_dr=!1}}},[t._v("关 闭")])],1)]),t._v(" "),l("el-dialog",{staticClass:"scbg-dialog",attrs:{title:"开始导入",width:"600px",visible:t.dr_dialog,"show-close":""},on:{close:t.mbxzgb,"update:visible":function(e){t.dr_dialog=e}}},[l("div",{staticStyle:{padding:"20px"}},[l("div",{staticClass:"daochu"},[l("div",[t._v("一、请点击“导出模板”，并参照模板填写信息。")]),t._v(" "),l("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.mbdc}},[t._v("\n                模板导出\n              ")])],1),t._v(" "),l("div",{staticClass:"daochu"},[l("div",{staticClass:"drfs"},[t._v("二、数据导入方式：")]),t._v(" "),l("el-radio-group",{on:{change:function(e){return t.Radio(e)}},model:{value:t.sjdrfs,callback:function(e){t.sjdrfs=e},expression:"sjdrfs"}},[l("el-radio",{attrs:{label:"1"}},[t._v("追加（导入时已有的记录信息不变，只添加新的记录）")]),t._v(" "),l("el-radio",{attrs:{label:"2"}},[t._v("覆盖（导入时更新已有的记录信息，并添加新的记录）")])],1)],1),t._v(" "),l("div",{staticClass:"daochu"},[l("div",[t._v("三、将按模板填写的文件，导入到系统中。")]),t._v(" "),l("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.chooseFile}},[t._v("\n                上传导入\n              ")])],1)])]),t._v(" "),l("el-dialog",{staticClass:"xg",attrs:{title:"新增涉密人员","close-on-click-modal":!1,visible:t.dialogVisible,width:"50%","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e},close:function(e){return t.close("formName")}}},[l("el-form",{ref:"formName",attrs:{model:t.tjlist,rules:t.rules,size:"mini","label-width":"152px","label-position":t.labelPosition}},[l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"姓名",prop:"xm"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"姓名",clearable:""},model:{value:t.tjlist.xm,callback:function(e){t.$set(t.tjlist,"xm",e)},expression:"tjlist.xm"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"身份证号码",prop:"sfzhm"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"身份证号码",clearable:""},on:{blur:function(e){return t.onInputBlur(1)}},model:{value:t.tjlist.sfzhm,callback:function(e){t.$set(t.tjlist,"sfzhm",e)},expression:"tjlist.sfzhm"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"性别",prop:"xb"}},[l("el-radio-group",{model:{value:t.tjlist.xb,callback:function(e){t.$set(t.tjlist,"xb",e)},expression:"tjlist.xb"}},t._l(t.xb,function(e){return l("el-radio",{key:e.id,attrs:{"v-model":t.tjlist.xb,label:e.id,value:e.id}},[t._v("\n                    "+t._s(e.xb))])}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"年龄",prop:"nl"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{oninput:"value=value.replace(/[^\\d.]/g,'')",placeholder:"年龄",clearable:""},on:{blur:function(e){t.nl=e.target.value}},model:{value:t.tjlist.nl,callback:function(e){t.$set(t.tjlist,"nl",e)},expression:"tjlist.nl"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"联系电话"}},[l("el-input",{attrs:{placeholder:"联系电话",clearable:"",oninput:"value=value.replace(/[^\\d.]/g,'')"},on:{blur:function(e){t.lxdh=e.target.value}},model:{value:t.tjlist.lxdh,callback:function(e){t.$set(t.tjlist,"lxdh",e)},expression:"tjlist.lxdh"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"部门",prop:"bmmc"}},[l("el-cascader",{ref:"cascaderArr",staticStyle:{width:"100%"},attrs:{options:t.regionOption,props:t.regionParams,filterable:""},on:{change:function(e){return t.handleChange(1)}},model:{value:t.tjlist.bmmc,callback:function(e){t.$set(t.tjlist,"bmmc",e)},expression:"tjlist.bmmc"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"岗位名称",prop:"gwmc"}},[l("el-select",{staticStyle:{height:"32px",width:"100%"},attrs:{multiple:"",placeholder:"请选择岗位"},on:{change:t.handleSelect},model:{value:t.tjlist.gwmc,callback:function(e){t.$set(t.tjlist,"gwmc",e)},expression:"tjlist.gwmc"}},t._l(t.gwmc,function(t,e){return l("el-option",{key:e,attrs:{label:t.gwmc,value:t.gwmc}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"涉密等级",prop:"smdj"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择涉密等级"},model:{value:t.tjlist.smdj,callback:function(e){t.$set(t.tjlist,"smdj",e)},expression:"tjlist.smdj"}},t._l(t.smdjxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"岗位确定依据",prop:"gwqdyj"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择岗位确定依据"},model:{value:t.tjlist.gwqdyj,callback:function(e){t.$set(t.tjlist,"gwqdyj",e)},expression:"tjlist.gwqdyj"}},t._l(t.gwqdyjxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"最高学历",prop:"zgxl"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择最高学历"},model:{value:t.tjlist.zgxl,callback:function(e){t.$set(t.tjlist,"zgxl",e)},expression:"tjlist.zgxl"}},t._l(t.zgxlxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"职务"}},[l("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%"},attrs:{"value-key":"zw","fetch-suggestions":t.querySearchzw,placeholder:"请输入职务名称"},model:{value:t.tjlist.zw,callback:function(e){t.$set(t.tjlist,"zw","string"==typeof e?e.trim():e)},expression:"tjlist.zw"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"职级"}},[l("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%"},attrs:{"value-key":"zj","fetch-suggestions":t.querySearchzj,placeholder:"请输入职级名称"},model:{value:t.tjlist.zj,callback:function(e){t.$set(t.tjlist,"zj","string"==typeof e?e.trim():e)},expression:"tjlist.zj"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"级别职称"}},[l("el-select",{staticStyle:{width:"calc(100% - 20px)"},attrs:{placeholder:"请选择级别职称"},model:{value:t.tjlist.jbzc,callback:function(e){t.$set(t.tjlist,"jbzc",e)},expression:"tjlist.jbzc"}},t._l(t.jbzcxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1),t._v(" "),l("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[l("div",[l("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),l("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),l("div",{staticClass:"smzt"},[t._v("\n                      （1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。\n                    ")])]),t._v(" "),l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),l("el-form-item",{attrs:{label:"身份类型",prop:"sflx"}},[l("el-select",{staticStyle:{width:"calc(100% - 20px)"},attrs:{placeholder:"请选择身份类型"},model:{value:t.tjlist.sflx,callback:function(e){t.$set(t.tjlist,"sflx",e)},expression:"tjlist.sflx"}},t._l(t.sflxxz,function(t){return l("el-option",{key:t.csz,attrs:{label:t.csm,value:t.csz}})}),1),t._v(" "),l("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[l("div",[l("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),l("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),l("div",{staticClass:"smzt"},[t._v("\n                      根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。\n                    ")])]),t._v(" "),l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"用人形式",prop:"yrxs"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择用人形式"},model:{value:t.tjlist.yrxs,callback:function(e){t.$set(t.tjlist,"yrxs",e)},expression:"tjlist.yrxs"}},t._l(t.yrxsxz,function(t){return l("el-option",{key:t.csz,attrs:{label:t.csm,value:t.csz}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"是否审查",prop:"sfsc"}},[l("el-radio-group",{model:{value:t.tjlist.sfsc,callback:function(e){t.$set(t.tjlist,"sfsc",e)},expression:"tjlist.sfsc"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.tjlist.sfsc,callback:function(e){t.$set(t.tjlist,"sfsc",e)},expression:"tjlist.sfsc"}},[t._v(t._s(e.sfscmc))])}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"出入境登记备案",prop:"sfcrj"}},[l("el-radio-group",{model:{value:t.tjlist.sfcrj,callback:function(e){t.$set(t.tjlist,"sfcrj",e)},expression:"tjlist.sfcrj"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.tjlist.sfcrj,callback:function(e){t.$set(t.tjlist,"sfcrj",e)},expression:"tjlist.sfcrj"}},[t._v(t._s(e.sfscmc))])}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"统一保管出入境证件",prop:"sfbgzj"}},[l("el-radio-group",{model:{value:t.tjlist.sfbgzj,callback:function(e){t.$set(t.tjlist,"sfbgzj",e)},expression:"tjlist.sfbgzj"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.tjlist.sfbgzj,callback:function(e){t.$set(t.tjlist,"sfbgzj",e)},expression:"tjlist.sfbgzj"}},[t._v(t._s(e.sfscmc))])}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{staticClass:"one-line",attrs:{label:"邮箱"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"邮箱",clearable:""},model:{value:t.tjlist.yx,callback:function(e){t.$set(t.tjlist,"yx",e)},expression:"tjlist.yx"}})],1),t._v(" "),l("el-form-item",{staticClass:"one-line",attrs:{label:"上岗时间",prop:"sgsj"}},[l("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",placeholder:"选择日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.tjlist.sgsj,callback:function(e){t.$set(t.tjlist,"sgsj",e)},expression:"tjlist.sgsj"}})],1)],1),t._v(" "),l("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"备注",prop:"bz"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea"},model:{value:t.tjlist.bz,callback:function(e){t.$set(t.tjlist,"bz",e)},expression:"tjlist.bz"}})],1)],1),t._v(" "),l("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitTj("formName")}}},[t._v("保 存")]),t._v(" "),l("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.handleClose()}}},[t._v("关 闭")])],1)],1),t._v(" "),l("el-dialog",{staticClass:"xg",attrs:{title:"修改涉密人员","close-on-click-modal":!1,visible:t.xgdialogVisible,width:"50%"},on:{"update:visible":function(e){t.xgdialogVisible=e},close:function(e){return t.close1("form")}}},[l("el-form",{ref:"form",attrs:{model:t.xglist,rules:t.rules,"label-width":"152px",size:"mini","label-position":t.labelPosition}},[l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"姓名",prop:"xm"}},[l("el-input",{attrs:{placeholder:"姓名",clearable:""},model:{value:t.xglist.xm,callback:function(e){t.$set(t.xglist,"xm",e)},expression:"xglist.xm"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"身份证号码",prop:"sfzhm"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"身份证号码",clearable:""},on:{blur:function(e){return t.onInputBlur(1)}},model:{value:t.xglist.sfzhm,callback:function(e){t.$set(t.xglist,"sfzhm",e)},expression:"xglist.sfzhm"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"性别",prop:"xb"}},[l("el-radio-group",{model:{value:t.xglist.xb,callback:function(e){t.$set(t.xglist,"xb",e)},expression:"xglist.xb"}},t._l(t.xb,function(e){return l("el-radio",{key:e.id,attrs:{"v-model":t.xglist.xb,label:e.id,value:e.id}},[t._v("\n                    "+t._s(e.xb))])}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"年龄",prop:"nl"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{oninput:"value=value.replace(/[^\\d.]/g,'')",placeholder:"年龄",clearable:""},on:{blur:function(e){t.nl=e.target.value}},model:{value:t.xglist.nl,callback:function(e){t.$set(t.xglist,"nl",e)},expression:"xglist.nl"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"联系电话"}},[l("el-input",{attrs:{placeholder:"联系电话",clearable:"",oninput:"value=value.replace(/[^\\d.]/g,'')"},on:{blur:function(e){t.lxdh=e.target.value}},model:{value:t.xglist.lxdh,callback:function(e){t.$set(t.xglist,"lxdh",e)},expression:"xglist.lxdh"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"部门",prop:"bmmc"}},[l("el-cascader",{ref:"cascaderArr",staticStyle:{width:"100%"},attrs:{options:t.regionOption,props:t.regionParams,filterable:""},on:{change:function(e){return t.handleChange(2)}},model:{value:t.xglist.bmmc,callback:function(e){t.$set(t.xglist,"bmmc",e)},expression:"xglist.bmmc"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"岗位名称",prop:"gwmc"}},[l("el-select",{staticStyle:{height:"32px",width:"100%"},attrs:{multiple:"",placeholder:"请选择岗位"},on:{change:t.handleSelect1},model:{value:t.xglist.gwmc,callback:function(e){t.$set(t.xglist,"gwmc",e)},expression:"xglist.gwmc"}},t._l(t.gwmc,function(t,e){return l("el-option",{key:e,attrs:{label:t.gwmc,value:t.gwmc}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"涉密等级",prop:"smdj"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择涉密等级"},model:{value:t.xglist.smdj,callback:function(e){t.$set(t.xglist,"smdj",e)},expression:"xglist.smdj"}},t._l(t.smdjxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"岗位确定依据",prop:"gwqdyj"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择岗位确定依据"},model:{value:t.xglist.gwqdyj,callback:function(e){t.$set(t.xglist,"gwqdyj",e)},expression:"xglist.gwqdyj"}},t._l(t.gwqdyjxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"最高学历",prop:"zgxl"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择最高学历"},model:{value:t.xglist.zgxl,callback:function(e){t.$set(t.xglist,"zgxl",e)},expression:"xglist.zgxl"}},t._l(t.zgxlxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"职务"}},[l("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%"},attrs:{"value-key":"zw","fetch-suggestions":t.querySearchzw,placeholder:"请输入职务名称"},model:{value:t.xglist.zw,callback:function(e){t.$set(t.xglist,"zw","string"==typeof e?e.trim():e)},expression:"xglist.zw"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"职级"}},[l("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%"},attrs:{"value-key":"zj","fetch-suggestions":t.querySearchzj,placeholder:"请输入职级名称"},model:{value:t.xglist.zj,callback:function(e){t.$set(t.xglist,"zj","string"==typeof e?e.trim():e)},expression:"xglist.zj"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"级别职称"}},[l("el-select",{staticStyle:{width:"calc(100% - 20px)"},attrs:{placeholder:"请选择级别职称"},model:{value:t.xglist.jbzc,callback:function(e){t.$set(t.xglist,"jbzc",e)},expression:"xglist.jbzc"}},t._l(t.jbzcxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1),t._v(" "),l("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[l("div",[l("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),l("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),l("div",{staticClass:"smzt"},[t._v("\n                      （1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。\n                    ")])]),t._v(" "),l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),l("el-form-item",{attrs:{label:"身份类型",prop:"sflx"}},[l("el-select",{staticStyle:{width:"calc(100% - 20px)"},attrs:{placeholder:"请选择身份类型"},model:{value:t.xglist.sflx,callback:function(e){t.$set(t.xglist,"sflx",e)},expression:"xglist.sflx"}},t._l(t.sflxxz,function(t){return l("el-option",{key:t.csz,attrs:{label:t.csm,value:t.csz}})}),1),t._v(" "),l("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[l("div",[l("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),l("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),l("div",{staticClass:"smzt"},[t._v("\n                      根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。\n                    ")])]),t._v(" "),l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"用人形式",prop:"yrxs"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择用人形式"},model:{value:t.xglist.yrxs,callback:function(e){t.$set(t.xglist,"yrxs",e)},expression:"xglist.yrxs"}},t._l(t.yrxsxz,function(t){return l("el-option",{key:t.csz,attrs:{label:t.csm,value:t.csz}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"是否审查",prop:"sfsc"}},[l("el-radio-group",{model:{value:t.xglist.sfsc,callback:function(e){t.$set(t.xglist,"sfsc",e)},expression:"xglist.sfsc"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.xglist.sfsc,callback:function(e){t.$set(t.xglist,"sfsc",e)},expression:"xglist.sfsc"}},[t._v(t._s(e.sfscmc))])}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"出入境登记备案",prop:"sfcrj"}},[l("el-radio-group",{model:{value:t.xglist.sfcrj,callback:function(e){t.$set(t.xglist,"sfcrj",e)},expression:"xglist.sfcrj"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.xglist.sfcrj,callback:function(e){t.$set(t.xglist,"sfcrj",e)},expression:"xglist.sfcrj"}},[t._v(t._s(e.sfscmc))])}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"统一保管出入境证件",prop:"sfbgzj"}},[l("el-radio-group",{model:{value:t.xglist.sfbgzj,callback:function(e){t.$set(t.xglist,"sfbgzj",e)},expression:"xglist.sfbgzj"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.xglist.sfbgzj,callback:function(e){t.$set(t.xglist,"sfbgzj",e)},expression:"xglist.sfbgzj"}},[t._v(t._s(e.sfscmc))])}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{staticClass:"one-line",attrs:{label:"邮箱"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"邮箱",clearable:""},model:{value:t.xglist.yx,callback:function(e){t.$set(t.xglist,"yx",e)},expression:"xglist.yx"}})],1),t._v(" "),l("el-form-item",{staticClass:"one-line",attrs:{label:"上岗时间",prop:"sgsj"}},[l("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",placeholder:"选择日期",format:"yyyy-MM-dd日","value-format":"yyyy-MM-dd"},model:{value:t.xglist.sgsj,callback:function(e){t.$set(t.xglist,"sgsj",e)},expression:"xglist.sgsj"}})],1)],1),t._v(" "),l("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"备注",prop:"bz"}},[l("el-input",{attrs:{type:"textarea"},model:{value:t.xglist.bz,callback:function(e){t.$set(t.xglist,"bz",e)},expression:"xglist.bz"}})],1)],1),t._v(" "),l("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.updataDialog("form")}}},[t._v("保 存")]),t._v(" "),l("el-button",{attrs:{type:"warning"},on:{click:function(e){t.xgdialogVisible=!1}}},[t._v("关 闭")])],1)],1),t._v(" "),l("el-dialog",{staticClass:"xg",attrs:{title:"涉密人员详情","close-on-click-modal":!1,visible:t.xqdialogVisible,width:"50%"},on:{"update:visible":function(e){t.xqdialogVisible=e}}},[l("el-form",{ref:"form",attrs:{model:t.xglist,"label-width":"152px",size:"mini","label-position":t.labelPosition,disabled:""}},[l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"姓名",prop:"xm"}},[l("el-input",{attrs:{placeholder:"姓名",clearable:""},model:{value:t.xglist.xm,callback:function(e){t.$set(t.xglist,"xm",e)},expression:"xglist.xm"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"身份证号码",prop:"sfzhm"}},[l("el-input",{attrs:{placeholder:"身份证号码",clearable:""},on:{blur:function(e){return t.onInputBlur(1)}},model:{value:t.xglist.sfzhm,callback:function(e){t.$set(t.xglist,"sfzhm",e)},expression:"xglist.sfzhm"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"性别",prop:"xb"}},[l("el-radio-group",{model:{value:t.xglist.xb,callback:function(e){t.$set(t.xglist,"xb",e)},expression:"xglist.xb"}},t._l(t.xb,function(e){return l("el-radio",{key:e.id,attrs:{"v-model":t.xglist.xb,label:e.id,value:e.id}},[t._v("\n                    "+t._s(e.xb))])}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"年龄",prop:"nl"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{oninput:"value=value.replace(/[^\\d.]/g,'')",placeholder:"年龄",clearable:""},on:{blur:function(e){t.nl=e.target.value}},model:{value:t.xglist.nl,callback:function(e){t.$set(t.xglist,"nl",e)},expression:"xglist.nl"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"联系电话"}},[l("el-input",{attrs:{placeholder:"联系电话",clearable:"",oninput:"value=value.replace(/[^\\d.]/g,'')"},on:{blur:function(e){t.lxdh=e.target.value}},model:{value:t.xglist.lxdh,callback:function(e){t.$set(t.xglist,"lxdh",e)},expression:"xglist.lxdh"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"部门",prop:"bmmc"}},[l("el-cascader",{ref:"cascaderArr",staticStyle:{width:"100%"},attrs:{options:t.regionOption,props:t.regionParams,filterable:""},on:{change:function(e){return t.handleChange(2)}},model:{value:t.xglist.bmmc,callback:function(e){t.$set(t.xglist,"bmmc",e)},expression:"xglist.bmmc"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"岗位名称",prop:"gwmc"}},[l("el-select",{staticStyle:{height:"32px",width:"100%"},attrs:{placeholder:"请选择岗位",multiple:""},on:{change:t.handleSelect},model:{value:t.xglist.gwmc,callback:function(e){t.$set(t.xglist,"gwmc",e)},expression:"xglist.gwmc"}},t._l(t.gwmc,function(t,e){return l("el-option",{key:e,attrs:{label:t.gwmc,value:t.gwmc}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"涉密等级",prop:"smdj"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择涉密等级"},model:{value:t.xglist.smdj,callback:function(e){t.$set(t.xglist,"smdj",e)},expression:"xglist.smdj"}},t._l(t.smdjxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"岗位确定依据",prop:"gwqdyj"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择岗位确定依据"},model:{value:t.xglist.gwqdyj,callback:function(e){t.$set(t.xglist,"gwqdyj",e)},expression:"xglist.gwqdyj"}},t._l(t.gwqdyjxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"最高学历",prop:"zgxl"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择最高学历"},model:{value:t.xglist.zgxl,callback:function(e){t.$set(t.xglist,"zgxl",e)},expression:"xglist.zgxl"}},t._l(t.zgxlxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"职务"}},[l("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%"},attrs:{"value-key":"zw","fetch-suggestions":t.querySearchzw,placeholder:"请输入职务名称"},model:{value:t.xglist.zw,callback:function(e){t.$set(t.xglist,"zw","string"==typeof e?e.trim():e)},expression:"xglist.zw"}})],1),t._v(" "),l("el-form-item",{attrs:{label:"职级"}},[l("el-autocomplete",{staticClass:"inline-input",staticStyle:{width:"100%"},attrs:{"value-key":"zj","fetch-suggestions":t.querySearchzj,placeholder:"请输入职级名称"},model:{value:t.xglist.zj,callback:function(e){t.$set(t.xglist,"zj","string"==typeof e?e.trim():e)},expression:"xglist.zj"}})],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"级别职称"}},[l("el-select",{staticStyle:{width:"cacl(100% - 20px)"},attrs:{placeholder:"请选择级别职称"},model:{value:t.xglist.jbzc,callback:function(e){t.$set(t.xglist,"jbzc",e)},expression:"xglist.jbzc"}},t._l(t.jbzcxz,function(t){return l("el-option",{key:t.id,attrs:{label:t.mc,value:t.id}})}),1),t._v(" "),l("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[l("div",[l("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),l("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),l("div",{staticClass:"smzt"},[t._v("\n                      （1）机关、参公事业单位、事业单位、国有企业管理岗位人员填写行政级别，即省部级、厅局级、县处级、乡科级及以下、试用期；事业单位、国有企业技术岗位人员填写职称，即高级（含正高、副高）、中级、初级及以下、试用期；机关、参公事业单位、事业单位工勤人员填写工勤人员；企业人员无行政级别的，填写企业职员。（2）既有职务、又有职级的，按照对应级别较高的填写；既有级别、又有职称的，机关按照级别填写，事业单位、国有企业按照职称填写。个别机关、单位人员难以明确对应的，根据干部管理权限尽量准确。\n                    ")])]),t._v(" "),l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),l("el-form-item",{attrs:{label:"身份类型",prop:"sflx"}},[l("el-select",{staticStyle:{width:"calc(100% - 20px)"},attrs:{placeholder:"请选择身份类型"},model:{value:t.xglist.sflx,callback:function(e){t.$set(t.xglist,"sflx",e)},expression:"xglist.sflx"}},t._l(t.sflxxz,function(t){return l("el-option",{key:t.csz,attrs:{label:t.csm,value:t.csz}})}),1),t._v(" "),l("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[l("div",[l("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),l("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),l("div",{staticClass:"smzt"},[t._v("\n                      根据涉密人员所在单位类型和人员类型对应填写，从下拉栏中选择：公务员、参公人员、事业人员、企业人员、其他。\n                    ")])]),t._v(" "),l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"10px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})])],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"用人形式",prop:"yrxs"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择用人形式"},model:{value:t.xglist.yrxs,callback:function(e){t.$set(t.xglist,"yrxs",e)},expression:"xglist.yrxs"}},t._l(t.yrxsxz,function(t){return l("el-option",{key:t.csz,attrs:{label:t.csm,value:t.csz}})}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"是否审查",prop:"sfsc"}},[l("el-radio-group",{model:{value:t.xglist.sfsc,callback:function(e){t.$set(t.xglist,"sfsc",e)},expression:"xglist.sfsc"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.xglist.sfsc,callback:function(e){t.$set(t.xglist,"sfsc",e)},expression:"xglist.sfsc"}},[t._v(t._s(e.sfscmc))])}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"出入境登记备案",prop:"sfcrj"}},[l("el-radio-group",{model:{value:t.xglist.sfcrj,callback:function(e){t.$set(t.xglist,"sfcrj",e)},expression:"xglist.sfcrj"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.xglist.sfcrj,callback:function(e){t.$set(t.xglist,"sfcrj",e)},expression:"xglist.sfcrj"}},[t._v(t._s(e.sfscmc))])}),1)],1),t._v(" "),l("el-form-item",{attrs:{label:"统一保管出入境证件",prop:"sfbgzj"}},[l("el-radio-group",{model:{value:t.xglist.sfbgzj,callback:function(e){t.$set(t.xglist,"sfbgzj",e)},expression:"xglist.sfbgzj"}},t._l(t.sfsc,function(e){return l("el-radio",{key:e.id,attrs:{label:e.sfscid,value:e.sfscmc},model:{value:t.xglist.sfbgzj,callback:function(e){t.$set(t.xglist,"sfbgzj",e)},expression:"xglist.sfbgzj"}},[t._v(t._s(e.sfscmc))])}),1)],1)],1),t._v(" "),l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{staticClass:"one-line",attrs:{label:"邮箱"}},[l("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"邮箱",clearable:""},model:{value:t.xglist.yx,callback:function(e){t.$set(t.xglist,"yx",e)},expression:"xglist.yx"}})],1),t._v(" "),l("el-form-item",{staticClass:"one-line",attrs:{label:"上岗时间",prop:"sgsj"}},[l("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",placeholder:"选择日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.xglist.sgsj,callback:function(e){t.$set(t.xglist,"sgsj",e)},expression:"xglist.sgsj"}})],1)],1),t._v(" "),l("el-form-item",{staticClass:"one-line-textarea",attrs:{label:"备注",prop:"bz"}},[l("el-input",{attrs:{type:"textarea"},model:{value:t.xglist.bz,callback:function(e){t.$set(t.xglist,"bz",e)},expression:"xglist.bz"}})],1)],1),t._v(" "),l("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"warning"},on:{click:function(e){t.xqdialogVisible=!1}}},[t._v("关 闭")])],1)],1)],1)])])},staticRenderFns:[]};var j=l("VU/8")(v,h,!1,function(t){l("AK/S")},"data-v-7dbac3ea",null);e.default=j.exports}});
//# sourceMappingURL=100.565bcc203011bf646c86.js.map