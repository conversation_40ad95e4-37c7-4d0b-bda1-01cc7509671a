<template>
  <div class="sec-container">
    <!-- 标题 -->
    <p class="sec-title">涉密场所审定审批</p>
    <div class="sec-form-container">
      <el-form ref="formName" :model="tjlist" label-width="225px">
        <div class="sec-header-section">
          <div class="sec-form-left">
            <el-form-item label="申请部门">
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.sqbm" clearable disabled></el-input>
              </template>
            </el-form-item>
            <el-form-item label="申请人">
              <el-input placeholder="" v-model="tjlist.xqr" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="场所名称">
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.csmc" clearable disabled></el-input>
              </template>
            </el-form-item>
            <el-form-item label="涉密程度" class="longLabel">
              <el-radio-group v-model="tjlist.smcd" disabled>
                <el-radio v-for="item in sbmjxz" :v-model="tjlist.smcd" :label="item.id" :value="item.id"
                  :key="item.id">{{ item.mc }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="责任人部门">
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.zrbm" clearable disabled></el-input>
              </template>
            </el-form-item>
            <el-form-item label="责任人">
              <el-input placeholder="" v-model="tjlist.zrr" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="填写日期">
              <el-date-picker v-model="tjlist.sqrq" type="date" class="rip" placeholder="选择日期" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" disabled>
              </el-date-picker>
            </el-form-item>
            <el-form-item label="责任人电话">
              <el-input placeholder="" v-model="tjlist.zrrdh" clearable></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="用途">
              <el-input placeholder="" v-model="tjlist.yt" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="所在位置">
              <div style="display: flex;justify-content:space-between">
                <el-input placeholder="" v-model="tjlist.szwz" clearable disabled></el-input>
                <el-button slot="trigger" type="primary" @click="yl">预览</el-button>
                <el-dialog :visible.sync="dialogVisible_scyl" style="z-index:2099;">
                  <img :src="scylImageUrl" alt="" style="width: 100%;">
                  <div slot="footer" class="dialog-footer">
                    <el-button size="small" @click="dialogVisible_scyl = false">取 消</el-button>
                  </div>
                </el-dialog>
              </div>
            </el-form-item>
          </div>
          <p class="sec-title">已采取防护措施情况</p>
          <div class="sec-form-third haveBorderTop">
            <div class="sec-left-text">
              <div>
                人工防护措施：<el-checkbox-group v-model="tjlist.fhcs" class="checkbox" disabled>
                  <el-checkbox v-for="item in rgfhcslist" :label="item.xdfsmc" :value="item.xdfsmc"
                    :key="item.xdfsid"></el-checkbox>
                </el-checkbox-group>
              </div>
              <div style="margin-top: 10px;">物理防护措施 <el-checkbox-group v-model="tjlist.fhcs" class="checkbox" disabled>
                  <el-checkbox v-for="item in wlfhcslist" :label="item.xdfsmc" :value="item.xdfsmc"
                    :key="item.xdfsid"></el-checkbox>
                </el-checkbox-group>
              </div>
              <div style="margin-top: 10px;">技术防护措施 <el-checkbox-group v-model="tjlist.fhcs" class="checkbox" disabled>
                  <el-checkbox v-for="item in jsfhcslist" :label="item.xdfsmc" :value="item.xdfsmc"
                    :key="item.xdfsid"></el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
          <p class="sec-title">责任部门领导意见</p>
          <div class="sec-form-second haveBorderTop longLabel">
            <el-form-item label="信息属实，拟" prop="bmsc">
              <el-radio v-model="tjlist.zrbmsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                :disabled="disabled1" :key="item.id">{{
                  item.sfty }}</el-radio>
            </el-form-item>
            <el-form-item label="场所审定" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
          </div>
          <div class="sec-form-second haveBorderTop longLabel">
            <el-form-item label="责任部门领导意见" prop="bmspr">
              <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
              <el-input placeholder="" v-model="tjlist.zrbmscxm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="日期" prop="bmscrq">
              <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
              <el-date-picker :disabled="disabled1" v-model="tjlist.zrbmscsj" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </div>
          <p class="sec-title">分管领导意见</p>
          <div class="sec-form-second haveBorderTop longLabel">
            <el-form-item label="信息属实，拟" prop="bmsc">
              <el-radio v-model="tjlist.fgldsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                :disabled="disabled2" :key="item.id">{{
                  item.sfty }}</el-radio>
            </el-form-item>
            <el-form-item label="场所审定" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
          </div>
          <div class="sec-form-second haveBorderTop longLabel">
            <el-form-item label="分管领导意见" prop="bmspr">
              <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
              <el-input placeholder="" v-model="tjlist.fgldscxm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="日期" prop="bmscrq">
              <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
              <el-date-picker :disabled="disabled2" v-model="tjlist.fgldscsj" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </div>
          <p class="sec-title">保密办审查</p>
          <div class="sec-form-second haveBorderTop longLabel">
            <el-form-item label="信息属实，拟" prop="bmsc">
              <el-radio v-model="tjlist.bmbsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                :disabled="disabled3" :key="item.id">{{
                  item.sfty }}</el-radio>
            </el-form-item>
            <el-form-item label="场所审定" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
          </div>
          <div class="sec-form-second haveBorderTop longLabel">
            <el-form-item label="保密办审批人" prop="bmspr">
              <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
              <el-input placeholder="" v-model="tjlist.bmbscxm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="日期" prop="bmscrq">
              <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
              <el-date-picker :disabled="disabled3" v-model="tjlist.bmbscsj" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                type="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </div>
          <p class="sec-title">保密办领导小组审查</p>
          <div class="sec-form-second haveBorderTop longLabel">
            <el-form-item label="信息属实，拟" prop="bmsc">
              <el-radio v-model="tjlist.bmgzldsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                :disabled="disabled4" :key="item.id">{{
                  item.sfty }}</el-radio>
            </el-form-item>
            <el-form-item label="场所审定" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
          </div>
          <div class="sec-form-second haveBorderTop longLabel">
            <el-form-item label="保密办领导小组审查" prop="bmspr">
              <!-- <el-input placeholder="" :disabled="disabled2" v-model="tjlist.bmspr" clearable></el-input> -->
              <el-input placeholder="" v-model="tjlist.bmgzldscxm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="日期" prop="bmscrq">
              <!-- <el-date-picker disabled v-model="tjlist.bmscrq" format="yyyy-MM-dd" -->
              <el-date-picker :disabled="disabled4" v-model="tjlist.bmgzldscsj" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </div>
          <!-- <p class="sec-title">备注：本表只适用于涉密人员由高涉密等级调整到低涉密等级</p> -->
        </div>
        <!-- <p class="sec-title">备注：涉密人员上岗审查、在岗复审均填本表</p> -->

        <p class="sec-title">轨迹处理</p>
        <el-table border class="sec-el-table" :data="gjclList"
          :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
          <el-table-column prop="hjmc" label="办理环节"></el-table-column>
          <el-table-column prop="clrid" label="办理人"></el-table-column>
          <el-table-column prop="bllx" label="办理类型"></el-table-column>
          <el-table-column prop="clyj" label="办理意见"></el-table-column>
          <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
          <el-table-column prop="clsj" label="办理时间"></el-table-column>
        </el-table>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getFwdyidByFwlx,
} from '../../../../api/index'
import {
  //审批指南
  getBlzn,
  //判断实例所处环节
  getSchj,
  //事项审核
  getSxsh,
  //查询审批用户列表
  getSpUserList,
  //非第一环节选择审批人
  tjclr,
  //流程跟踪
  getSpGjxx,

} from '../../../../api/wdgz'
import {
  getAllSmsbmj
} from '../../../../api/xlxz'
import {
  getJlidcssdsc,
  getCssdInfo,
  updateCssd
} from '../../../../api/cssdsc'
import {
  saveCsdj
} from '../../../../api/index'
import {
  verifySfjshj
} from '../../../../api/djgwbg'
import { getUserInfo } from '../../../../api/dwzc'

import AddLineTable from "../../../components/common/addLineTable.vue";   //人工纠错组件
export default {
  components: {
    AddLineTable
  },
  props: {
    msg: {
      type: Object,
      require: false,
      default: ""
    }
  },
  data() {
    return {
      rgfhcslist: [
        {
          xdfsid: '1',
          xdfsmc: '保护守卫'
        },
        {
          xdfsid: '2',
          xdfsmc: '安保巡逻'
        },
        {
          xdfsid: '3',
          xdfsmc: '公司员工值班'
        },
      ],
      wlfhcslist: [
        {
          xdfsid: '1',
          xdfsmc: '铁门'
        },
        {
          xdfsid: '2',
          xdfsmc: '铁窗'
        },
        {
          xdfsid: '3',
          xdfsmc: '密码保险柜'
        },
        {
          xdfsid: '4',
          xdfsmc: '密码文件柜'
        },
        {
          xdfsid: '5',
          xdfsmc: '手机信号屏蔽柜'
        },
      ],
      jsfhcslist: [
        {
          xdfsid: '1',
          xdfsmc: '门禁系统'
        },
        {
          xdfsid: '2',
          xdfsmc: '红外报警器'
        },
        {
          xdfsid: '3',
          xdfsmc: '视频监控'
        },
        {
          xdfsid: '4',
          xdfsmc: '视频干扰器'
        },
        {
          xdfsid: '5',
          xdfsmc: '碎纸机'
        },
        {
          xdfsid: '6',
          xdfsmc: '手机信号屏蔽器'
        },
      ],
      scylImageUrl: '',
      dialogVisible_scyl: false,
      checkList: [],
      zzhmList: [
        {
          zzid: 1,
          fjlb: '信息输出专用红盘',
          zjhm: '',
          yxq: '',
          checked: 0
        },
        {
          zzid: 2,
          fjlb: '信息输出专用单导盒',
          zjhm: '',
          yxq: '',
          checked: 0
        },
        {
          zzid: 3,
          fjlb: '公司专用涉密信息输出机',
          zjhm: '',
          yxq: '',
          checked: 0
        },
        {
          zzid: 4,
          fjlb: '其他',
          zjhm: '',
          yxq: '',
          checked: 0
        }
      ],
      radio: '',
      // 载体详细信息
      ztqsQsscScjlList: [],
      sbmjxz: [],//设备密级
      ztlxList: [
        {
          lxid: 1,
          lxmc: '纸介质'
        },
        {
          lxid: 2,
          lxmc: '光盘'
        },
        {
          lxid: 3,
          lxmc: '电磁介质'
        },
      ],
      smdjList: [
        {
          smdjid: 1,
          smdjmc: '绝密'
        },
        {
          smdjid: 2,
          smdjmc: '机密'
        },
        {
          smdjid: 3,
          smdjmc: '秘密'
        },
        {
          smdjid: 4,
          smdjmc: '内部'
        },
      ],
      // table 行样式
      headerCellStyle: {
        background: '#EEF7FF',
        color: '#4D91F8'
      },
      fwdyid: '',
      slid: '',
      activeName: 'second',
      //审批指南
      spznList: [],
      // form表单提交数据
      // 持有因公出入境证件情况
      ryglRyscSwzjList: [{
        'zjmc': '涉密载体（含纸质、光盘等）',
        'fjlb': 1,
        'cyqk': '0',
        'zjhm': '',
        'yxq': '',
        'qzmc': '部门保密员核定签字：'
      }, {
        'zjmc': '信息设备（含计算机、存储介质等）',
        'fjlb': 2,
        'cyqk': '0',
        'zjhm': '',
        'yxq': '',
        'qzmc': '部门保密员核定签字：'
      }, {
        'zjmc': '涉密信息系统访问权限回收情况',
        'fjlb': 3,
        'cyqk': '0',
        'zjhm': '',
        'yxq': '',
        'qzmc': '系统管理员(三员)核定签字：'
      }, {
        'zjmc': '涉密场所出入权限回收情况',
        'fjlb': 4,
        'cyqk': '0',
        'zjhm': '',
        'yxq': '',
        'qzmc': '涉密场所管理员核定签字：  '
      }],
      //审批信息
      tjlist: {
        fhcs: [],
        cnsrq: '',
        bmscrq: '',
        rlscrq: '',
        bmbscrq: '',
        // 主要学习及工作经历
        xxjlList: [],
        // 家庭成员及社会关系
        cyjshgxList: [],
        // 持有因公出入境证件情况
        ygrjzjqkList: [],
        // 持有因私出入境证件情况
        ysrjzjqkList: [],
        // 因私出国(境)情况
        yscgqkList: [],
        // 接受境外资助情况
        jsjwzzqkList: [],
        // 处分或者违法犯罪情况
        clhwffzqkList: [],
        value1: [],
      },
      //轨迹处理
      gjclList: [],
      upccLsit: {},
      //判断实例所处环节
      disabled1: true,
      disabled2: true,
      disabled3: true,
      disabled4: true,
      btnsftg: true,
      btnsfth: true,
      yldis: false,
      jgyf: '',
      //性别
      xb: [{
        xb: '男',
        id: 1
      },
      {
        xb: '女',
        id: 2
      },
      ],
      //移居国(境)外情况
      yjgwqk: [{
        yw: '有',
        id: 1
      },
      {
        yw: '无',
        id: 0
      },
      ],
      //上岗保密教育、签订保密承诺书
      bmjysfwc: [
        {
          sfwc: '已完成',
          id: 1
        },
        {
          sfwc: '未完成',
          id: 0
        },
      ],
      scqk: [
        {
          sfty: '同意',
          id: 1
        },
        {
          sfty: '不同意',
          id: 0
        },
      ],
      // 政治面貌下拉选项
      zzmmoptions: [],
      sltshow: '', // 文档的缩略图显示
      fileList: [],
      dialogVisible: false,
      fileRow: '',
      //人员任用
      smryList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      formInline: {
        'bmmc': '',
        'xm': ''
      }, // 搜索条件
      selectlistRow: [], //列表的值
      xsyc: true,
      mbhjid: '',
      imageUrl: '',
      imageUrlbrcn: '',
      ylxy: true,
      file: {},
      bmcnssmj: '',
      bmxyssmj: '',
      //本人承诺
      dialogVisible_brcn: false,
      //保密承诺书预览
      dialogVisible_bmcns: false,
      bmcnsImageUrl: '',
      //保密承诺书预览
      dialogVisible_bmxys: false,
      bmxysImageUrl: '',
      //审批状态码 1 2 3 4
      zplcztm: '',
      //上传扫描件按钮显示隐藏
      show: true,
      show1: true,
      xm: '',
      jbxx: {},
      //通过
      tgdis: false,
      //流程跟踪
      lcgzList: [],

    }
  },
  computed: {},
  mounted() {
    this.onfwid()
    this.getYbsx()
    setTimeout(() => {
      this.dqlogin()
      //审批指南初始化列表
      //审批信息初始化列表
      this.spxx()
      //判断实例所处环节
      // //事项审核
      // this.sxsh()
      //初始化el-dialog列表数据
      this.splist()
      //流程跟踪初始化列表
      this.lcgz()
      this.smmjxz()
    }, 1000)
  },
  methods: {
    async onfwid() {
      let params = {
        fwlx: 5
      }
      let data = await getFwdyidByFwlx(params)
      console.log(data);
      this.fwdyid = data.data.fwdyid
      console.log(this.fwdyid);
    },
    async getYbsx() {
      this.$nextTick(async () => {
        this.jbxx = JSON.parse(JSON.stringify(this.msg))
        this.slid = this.jbxx.slid
        console.log('===========================', this.slid);
      })
    },
    //图片预览
    yl() {
      let zpxx
      zpxx = this.zpzm(this.file)
      this.scylImageUrl = zpxx
      this.dialogVisible_scyl = true
    },
    zpzm(zp) {
      const iamgeBase64 = "data:image/jpeg;base64," + zp;
      let zpxx
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          // let that = this;

          function previwImg(item) {
            zpxx = item;
          }
          previwImg(iamgeBase64);
        }
      }
      return zpxx
    },
    getNowTime() {
      let now = new Date();
      let year = now.getFullYear(); //得到年份
      let month = now.getMonth(); //得到月份
      let date = now.getDate(); //得到日期
      month = month + 1;
      month = month.toString().padStart(2, "0");
      date = date.toString().padStart(2, "0");
      let defaultDate = `${year}-${month}-${date}`;
      console.log(defaultDate)
      return defaultDate;
      this.$set(this.info, "stockDate", defaultDate);
    },
    //当前登录用户
    async dqlogin() {
      let data = await getUserInfo()
      this.xm = data.xm
    },
    //审批指南

    //审批信息
    sjcf(val) {
      console.log(val)

      console.log(this.tjlist.cnsrq);
      console.log(typeof (this.tjlist.cnsrq));
    },
    async spxx() {
      let jlid = await getJlidcssdsc({
        slid: this.slid
      });
      this.jlid = jlid.data;
      let params = {
        jlid: this.jlid
      }
      let data = await getCssdInfo(params)
      this.tjlist = data
      console.log(this.tjlist);
      this.tjlist.fhcs = this.tjlist.fhcs.split('/')
      this.file = this.tjlist.smjlj
    },
    // 预览
    yulan() {
      this.dialogVisible_brcn = true
      // this.ylxy = false
      const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.brcn;
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          let that = this;

          function previwImg(item) {
            that.imageUrlbrcn = item;
          }
          previwImg(iamgeBase64);
        }
      }
    },
    // 删除
    shanchu() {
      this.tjlist.brcn = ''
      this.sltshow = ''
    },
    chRadio(val) {

    },
    xzbmcns(val) {

    },
    xzbmxys(val) {

    },

    //立即办理
    ljbl() {
      this.activeName = 'second'
    },
    //事项审核
    async sxsh() {
      let params = {
        fwdyid: this.fwdyid,
        slid: this.slid,
        jg: this.jgyf,
        smryid: ''
      }
      let data = await getSxsh(params)
      if (data.code == 10000) {
        this.tgdis = false
        if (data.data.zt == 0) {
          this.$message({
            message: data.data.msg,
            type: 'success'
          });
          // this.smryList = data.data.blrarr
          this.mbhjid = data.data.mbhjid
          this.splist()
          this.dialogVisible = true
        } else if (data.data.zt == 1) {
          this.$message({
            message: data.data.msg,
            type: 'success'
          });
          // setTimeout(() => {
          //     this.$router.push('/dbsx')
          // }, 500)
          this.$router.push('/dbsx')
        } else if (data.data.zt == 2) {
          this.$message({
            message: data.data.msg
          });
          // setTimeout(() => {
          //     this.$router.push('/dbsx')
          // }, 500)
          this.$router.push('/dbsx')
        } else if (data.data.zt == 3) {
          this.$message({
            message: data.data.msg
          });
          // setTimeout(() => {
          //     this.$router.push('/dbsx')
          // }, 500)
          this.$router.push('/dbsx')
        }
        else if (data.data.zt == 4) {
          this.$message({
            message: data.data.msg
          });
          console.log(1111111111111);
          // setTimeout(() => {
          //     this.$router.push('/dbsx')
          // }, 500)
          this.$router.push('/dbsx')
        }
      }
    },
    //初始化el-dialog列表数据
    async splist() {
      let params = {
        fwdyid: this.fwdyid,
        'xm': this.formInline.xm,
        'bmmc': this.formInline.bmmc,
        page: this.page,
        pageSize: this.pageSize,
        qshjid: this.mbhjid,
      }
      let data = await getSpUserList(params)
      this.smryList = data.records
      this.total = data.total


    },
    onSubmit() {
      this.splist()
    },
    selectRow(selection) {
      if (selection.length <= 1) {
        console.log('点击选中数据：', selection);
        this.selectlistRow = selection
        this.xsyc = true
      } else if (selection.length > 1) {
        this.$message.warning('只能选中一条数据')
        this.xsyc = false
      }

    },
    handleSelect(selection, val) {
      //只能选择一行，选择其他，清除上一行
      if (selection.length > 1) {
        let del_row = selection.shift()
        this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中
      }
    },
    // 点击行触发，选中或不选中复选框
    handleRowClick(row, column, event) {
      this.$refs.multipleTable.toggleRowSelection(row)
      this.selectChange(this.selectlistRow)
    },
    async submit() {
      let params = {
        fwdyid: this.fwdyid,
        slid: this.slid,
        shry: this.selectlistRow[0].yhid,
        mbhjid: this.mbhjid,
      }
      let data = await tjclr(params)
      if (data.code == 10000) {
        this.$message({
          message: data.message,
          type: 'success'
        });
        this.dialogVisible = false
        setTimeout(() => {
          this.$router.push('/dbsx')
        }, 500)
      }
    },
    //上传文件
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      if (!isJPG && !isPNG) {
        this.$message.error('上传缩略图只能是 JPG/PNG 格式!');
      }
      return isJPG || isPNG;
    },
    // 64码
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    //保密承诺书预览
    bmcnsyl() {
      this.dialogVisible_bmcns = true
      const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.cnssmj;
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          let that = this;

          function previwImg(item) {
            that.bmcnsImageUrl = item;
          }
          previwImg(iamgeBase64);
        }
      }
    },
    //
    bmxysyl() {
      this.dialogVisible_bmxys = true
      const iamgeBase64 = "data:image/jpeg;base64," + this.tjlist.xyssmj;
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          let that = this;

          function previwImg(item) {
            that.bmxysImageUrl = item;
          }
          previwImg(iamgeBase64);
        }
      }
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val
      this.splist()
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.splist()
    },
    //流程跟踪
    //流程跟踪初始化列表
    async lcgz() {
      let params = {
        fwdyid: this.fwdyid,
        slid: this.slid
      }
      let data = await getSpGjxx(params)
      if (data.code == 10000) {
        // this.lcgzList = data.data.content
        this.gjclList = data.data.content
        console.log(this.gjclList);
      }
    },
    //设备密级获取
    async smmjxz() {
      this.sbmjxz = await getAllSmsbmj()
    },
    formj(row) {
      console.log(row);
      let smmj
      this.sbmjxz.forEach(item => {
        if (row.mj == item.id) {
          smmj = item.mc
        }
      })
      return smmj
    }
  },
  watch: {
    msg: {
      handler(newVal, oldVal) {
        console.log(newVal, '这样也可以得到数据~~~');
        // this.list = newVal
      },
      immediate: true,
      deep: true,
    },
  }
}

</script>

<style scoped>
.sec-container {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: overlay;
}

.sec-title {
  border-left: 5px solid #1b72d8;
  color: #1b72d8;
  font-size: 20px;
  font-weight: 700;
  text-indent: 10px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.sec-form-container {
  width: 100%;
  height: 100%;
}

.sec-form-left {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  /* height: 40px; */
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
}

.sec-form-left:not(:first-child) {
  border-top: 0;
}

.sec-form-left .el-form-item {
  float: left;
  width: 100%;
}

.sec-header-section {
  width: 100%;
  position: relative;
}

.tb-container {
  height: 300px;
  /* overflow-y: scroll; */
}



.sec-header-pic {
  width: 258px;
  position: absolute;
  right: 0px;
  top: 0;
  height: 245px;
  border: 1px solid #CDD2D9;
  border-left: 0;
  background: #ffffff;
}

.sec-header-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sec-header-mar {
  margin-right: 10px;
}

.sec-form-second {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
  border-top: 0;
  position: relative;
}

.sec-form-fddw {
  height: 100%;
  position: absolute;
  top: 0;
  right: 40%;
}

.sec-form-third {
  border: 1px solid #CDD2D9;
  /* height: 40px;  */
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-four {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-five {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  overflow: hidden;
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.yulan {
  text-align: center;
  cursor: pointer;
  color: #3874D5;
  font-weight: 600;
  float: left;
  margin-left: 10px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 2px solid #EBEBEB;
}

.sec-form-six {
  border: 1px solid #CDD2D9;
  overflow: hidden;
  background: #ffffff;
  padding: 10px;
}

.ml10 {
  margin-left: 10px;
}

.sec-footer {
  margin-top: 10px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
  border: none;
}

.sec-left-text {
  float: left;
  margin-right: 130px;
}

.haveBorderTop {
  border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
  width: 225px !important;
}

>>>.longLabel .el-form-item__content {
  margin-left: 225px !important;
  padding-left: 20px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

.gtzzsmgwgz {
  border-right: 1px solid #CDD2D9;
}

.hyzk {
  padding-left: 15px;
  background-color: #F5F7FA;
  width: calc(100% - 16px);
  border-right: 1px solid #CDD2D9;
  color: #000;
}

.gtzzsmgwgz>>>.el-form-item__content {
  display: none !important;
}

.gtzzsmgwgz>>>.el-form-item__label {
  border: none;
  text-align: left !important;
}

/* .sec-form-second:not(:first-child){
  border-top: 0;
} */
.sec-form-second .el-form-item {
  float: left;
  width: 100%;
}

.sec-el-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

>>>.sec-el-table .el-input__inner {
  border: none !important;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
  width: 200px;
  text-align: center;
  font-size: 16px;
}

.gtzzsmgwgz {
  text-align: left !important;
}

>>>.sec-form-container .el-input__inner {
  border: none;
  border-right: 1px solid #CDD2D9;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item {
  margin-bottom: 0px;
}

/* >>>.el-form > div {
  border: 1px solid #CDD2D9;;
} */
>>>.el-form-item__label {
  border-right: 1px solid #CDD2D9;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
  margin-bottom: 10px;
}

/deep/ .el-input.is-disabled .el-input__inner {
  color: #000 !important;
}

>>>.brno .el-input__inner {
  border-right: none;
}

>>>.wd .el-radio {
  display: block;
  margin: 10px 0;
}

>>>.lh .el-radio {
  line-height: 48px;
}

>>>.wd .el-form-item__label {
  height: 184px;
  line-height: 184px;
}

>>>.cs .el-input__inner {
  border-right: 0 !important;
  width: 100%;
}

.rip {
  width: 100% !important;
}
</style>
