<template>
    <div class="sec-container" v-loading="loading">
        <!-- 标题 -->
        <el-button class="fhry" v-show="deb" type="primary" size="small" @click="fhry">返回</el-button>
        <el-tabs v-model="activeName">
            <el-tab-pane label="审批指南" name="first">
                <div class="sec-form-six haveBorderTop sec-footer">
                    <el-button @click="ljbl" class="fr" type="success">立即办理</el-button>
                </div>
                <el-table border class="sec-el-table" :data="spznList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                    <el-table-column prop="hjmc" label="办理流程"></el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="审批信息" name="second">
                <p class="sec-title">基本信息</p>
                <div class="sec-form-container">
                    <el-form ref="formName" :model="tjlist" label-width="225px">
                        <!-- 第一部分包括姓名到常住地公安start -->
                        <div class="sec-header-section">
                            <div class="sec-form-left">
                                <el-form-item label="所在部门">
                                    <template slot-scope="scope">
                                        <el-input placeholder="" v-model="tjlist.szbm" clearable disabled></el-input>
                                    </template>
                                </el-form-item>
                                <el-form-item label="申请人">
                                    <el-input placeholder="" v-model="tjlist.xqr" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="接收传递期限">
                                    <el-date-picker v-model="tjlist.jscdqx" disabled class="riq" type="daterange"
                                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                        format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                                    </el-date-picker>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="知悉范围">
                                    <el-input placeholder="" v-model="tjlist.zxfw" clearable disabled></el-input>
                                </el-form-item>
                                <!-- <el-button type="success" @click="zxfw()">添加</el-button> -->
                            </div>
                            <div class="sec-form-left sec-form-left-textarea">
                                <el-form-item label="用途">
                                    <el-input placeholder="" type="textarea" v-model="tjlist.yt" clearable
                                        disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="制发单位">
                                    <el-input placeholder="" v-model="tjlist.zfdw" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="移交人">
                                    <el-input placeholder="" v-model="tjlist.yjr" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="传递起始地点">
                                    <el-input placeholder="" v-model="tjlist.qsdd" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="传递目的地点">
                                    <el-input placeholder="" v-model="tjlist.mddd" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <!-- 载体详细信息start -->
                            <p class="sec-title">载体详细信息</p>
                            <el-table border class="sec-el-table" :data="ztqsQsscScjlList"
                                :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                                <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                                <el-table-column prop="ztmc" label="载体名称"></el-table-column>
                                <el-table-column prop="xmbh" label="项目编号"> </el-table-column>
                                <el-table-column prop="yztbh" label="原载体编号"> </el-table-column>
                                <el-table-column prop="ztbh" label="载体编号"> </el-table-column>
                                <el-table-column prop="lx" label="载体类型"> </el-table-column>
                                <el-table-column prop="smmj" label="密级"> </el-table-column>
                                <el-table-column prop="bmqx" label="保密期限"> </el-table-column>
                                <el-table-column prop="ys" label="页数/大小"> </el-table-column>
                                <el-table-column prop="fs" label="份数"></el-table-column>
                            </el-table>
                            <!-- 载体详细信息end -->
                            <p class="sec-title">采取防护措施</p>
                            <div class="sec-form-third haveBorderTop">
                                <div class="sec-left-text">
                                    <div>
                                        防护措施：<el-checkbox-group v-model="tjlist.fhcs" class="checkbox" disabled>
                                            <el-checkbox v-for="item in xdfsList" :label="item.xdfsmc" :value="item.xdfsid"
                                                :key="item.xdfsid"></el-checkbox>
                                        </el-checkbox-group>
                                    </div>
                                    <div style="margin-top: 10px;">交通工具： <el-checkbox-group disabled v-model="tjlist.jtgj"
                                            class="checkbox">
                                            <el-checkbox v-for="item in jtgjList" :label="item.jtgjmc" :value="item.jtgjid"
                                                :key="item.jtgjid"></el-checkbox>
                                        </el-checkbox-group>
                                    </div>
                                    <div style="display: flex;align-items: center;" class="brno">交通路线：<el-input
                                            v-model="tjlist.jtxl" style="width: 500px;border-right: none;"
                                            disabled></el-input>
                                    </div>
                                    <p>注：传递绝密级文件，实行二人护送制。</p>
                                </div>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="接收部门">
                                    <el-input placeholder="" v-model="tjlist.jsrszbm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="接收人">
                                    <el-input placeholder="" v-model="tjlist.jsr" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="传递部门">
                                    <el-input placeholder="" v-model="tjlist.cdrszbm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="传递人">
                                    <el-input placeholder="" v-model="tjlist.cdr" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="项目经理部门">
                                    <el-input placeholder="" v-model="tjlist.xmjlszbm" clearable disabled></el-input>
                                </el-form-item>
                                <el-form-item label="项目经理">
                                    <el-input placeholder="" v-model="tjlist.xmjl" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">
                                <el-form-item label="保管部门">
                                    <el-input placeholder="" v-model="tjlist.bgbm" clearable disabled></el-input>
                                    <!-- <el-cascader v-model="tjlist.bgbm" style="width: 100%;" :options="regionOption"
                                        disabled :props="regionParams" filterable clearable ref="cascaderArr"
                                        @change="handleChange"></el-cascader> -->
                                </el-form-item>
                                <el-form-item label="归档人">
                                    <el-input placeholder="" v-model="tjlist.gdr" clearable disabled>
                                    </el-input>
                                    <!-- <el-autocomplete class="inline-input" disabled value-key="xm"
                                        v-model.trim="tjlist.gdr" :fetch-suggestions="querySearch" placeholder="请输入归档人"
                                        style="width:100%">
                                    </el-autocomplete> -->
                                </el-form-item>
                            </div>
                            <div class="sec-form-left">

                                <el-form-item label="保存位置">
                                    <el-input placeholder="" v-model="tjlist.bcwz" clearable disabled></el-input>
                                </el-form-item>
                            </div>
                        </div>
                        <p class="sec-title">部门保密员审核</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmbmysc">
                                <el-radio v-model="tjlist.bmbmysc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="载体接收传递" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门保密员审批人" prop="bmbmyscxm">
                                <el-input placeholder="" disabled v-model="tjlist.bmbmyscxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmbmyscsj">
                                <el-date-picker disabled v-model="tjlist.bmbmyscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">部门领导审批</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmldsc">
                                <el-radio v-model="tjlist.bmldsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="载体接收传递" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="部门领导审批人" prop="bmldscxm">
                                <el-input placeholder="" disabled v-model="tjlist.bmldscxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmldscsj">
                                <el-date-picker disabled v-model="tjlist.bmldscsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">保密办意见</p>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="信息属实，拟" prop="bmbsc">
                                <el-radio v-model="tjlist.bmbsc" v-for="item in scqk" :label="item.id" @change="chRadio"
                                    disabled :key="item.id">{{
                                        item.sfty }}</el-radio>
                            </el-form-item>
                            <el-form-item label="载体接收传递" prop="gtzzsmgwgz" class="gtzzsmgwgz"></el-form-item>
                        </div>
                        <div class="sec-form-second haveBorderTop longLabel">
                            <el-form-item label="保密办领导审批人" prop="bmbxm">
                                <el-input placeholder="" disabled v-model="tjlist.bmbxm" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="日期" prop="bmbsj">
                                <el-date-picker disabled v-model="tjlist.bmbsj" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <p class="sec-title">轨迹处理</p>
                        <el-table border class="sec-el-table" :data="gjclList"
                            :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                            <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                            <el-table-column prop="clrid" label="办理人"></el-table-column>
                            <el-table-column prop="bllx" label="办理类型"></el-table-column>
                            <el-table-column prop="clyj" label="办理意见"></el-table-column>
                            <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                            <el-table-column prop="clsj" label="办理时间"></el-table-column>
                        </el-table>
                        <!-- 底部操作按钮start -->
                        <!-- <div class="sec-form-six haveBorderTop sec-footer">
                            <el-dropdown class="fr ml10">
                                <el-button type="primary">退回</el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item @click.native="save(3)">至上步办理人</el-dropdown-item>
                                    <el-dropdown-item @click.native="save(2)">至发起人</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <el-button @click="save(1)" class="fr" :disabled="tgdis" type="success">通过</el-button>
                        </div> -->
                        <!-- 底部操作按钮end -->

                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="流程跟踪" name="third">
                <el-table border class="sec-el-table" :data="lcgzList"
                    :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
                    <el-table-column prop="hjmc" label="办理环节"></el-table-column>
                    <el-table-column prop="clrid" label="办理人"></el-table-column>
                    <el-table-column prop="bllx" label="办理类型"></el-table-column>
                    <el-table-column prop="clyj" label="办理意见"></el-table-column>
                    <el-table-column prop="xybclr" label="下一步办理人"></el-table-column>
                    <el-table-column prop="clsj" label="办理时间"></el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
        <!-- 发起申请弹框start -->
        <el-dialog title="人员选择" :close-on-click-modal="false" :visible.sync="dialogVisible" width="40%">
            <div class="dlFqsqContainer">
                <label for="">部门:</label>
                <el-input class="input1" v-model="formInline.bmmc" clearable placeholder="部门"></el-input>
                <label for="">姓名:</label>
                <el-input class="input2" v-model="formInline.xm" clearable placeholder="姓名"></el-input>
                <el-button class="searchButton" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                <el-table class="tb-container" ref="multipleTable" :data="smryList" border
                    :header-cell-style="headerCellStyle" stripe @selection-change="selectRow" @select="handleSelect"
                    @row-click="handleRowClick" height="300px">
                    <el-table-column type="selection" width="55" align="center"> </el-table-column>
                    <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                    <el-table-column prop="xm" label="姓名"></el-table-column>
                    <el-table-column prop="bmmc" label="部门"></el-table-column>
                    <el-table-column prop="gwmc" label="岗位"></el-table-column>
                </el-table>
                <el-pagination class="paginationContainer" background @current-change="handleCurrentChange"
                    @size-change="handleSizeChange" :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]"
                    :page-size="pageSize" layout="total, prev, pager, sizes,next, jumper" :total="total">
                </el-pagination>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" v-if="xsyc" @click="submit('formName')">确 定</el-button>
                <el-button type="warning" @click="dialogVisible = false">关 闭</el-button>
            </span>
        </el-dialog>
        <!-- 发起申请弹框end -->
    </div>
</template>
<script>
import {
    getSpUserList,
    updateYhxx,
    getZzjgList,
    getLoginInfo,
    saveZtgl,//载体管理添加
    getAllYhxx,
} from '../../../../api/index'
import {
    verifySfjshj,
} from '../../../../api/djgwbg'
import {
    updateZtJscd,
    getZtJscdBySlid,
    getZtqdListByYjlid,
    getJlidBySlid,
    addZtJscdDj
} from '../../../../api/ztjs'
import {
    //审批指南
    getBlzn,
    //审批信息
    //判断实例所处环节
    getSchj,
    //事项审核
    getSxsh,
    //非第一环节选择审批人
    tjclr,
    //流程跟踪
    getSpGjxx,
} from '../../../../api/wdgz'
import { getUserInfo } from '../../../../api/dwzc'
import AddLineTable from "../../../components/common/addLineTable.vue"; //人工纠错组件
export default {
    components: {
        AddLineTable,
    },
    props: {},
    data() {
        return {
            activeName: 'second',
            // table 行样式
            headerCellStyle: {
                background: '#EEF7FF',
                color: '#4D91F8'
            },
            //审批指南
            spznList: [],
            formInline: {
                'bmmc': '',
                'xm': ''
            }, // 搜索条件
            loading: false,
            page: 1, // 审批人弹框当前页
            pageSize: 10, // 审批人弹框每页条数
            radioIdSelect: '', // 审批人弹框人员单选
            total: 0, // 弹框人员总数
            regionParams: {
                label: 'label', //这里可以配置你们后端返回的属性
                value: 'label',
                children: 'childrenRegionVo',
                expandTrigger: 'click',
                checkStrictly: true
            }, //地域信息配置参数
            selectlistRow: [], //列表的值
            mbhjid: '',
            regionOption: [], // 部门下拉
            regionParams: {
                label: 'label', //这里可以配置你们后端返回的属性
                value: 'label',
                children: 'childrenRegionVo',
                expandTrigger: 'click',
                checkStrictly: true
            }, //地域信息配置参数
            // form表单提交数据
            tjlist: {
                xqr: '',
                szbm: '',
                jscdqx: [],
                ztqsQsscScjlList: [],
                zxfw: '',
                yt: '',
                yjr: '',
                zfdw: '',
                yztbh: '',
                qsdd: '',
                mddd: '',
                fhcs: [],
                jtgj: [],
                jtxl: '',
                gdr: '',
                bgbm: '',
                bcwz: ''
            },
            ztqsQsscScjlList: [],

            scqk: [
                {
                    sfty: '同意',
                    id: 1
                },
                {
                    sfty: '不同意',
                    id: 0
                },
            ],
            ztlxList: [
                {
                    lxid: '1',
                    lxmc: '纸介质'
                },
                {
                    lxid: '2',
                    lxmc: '光盘'
                },
                {
                    lxid: '3',
                    lxmc: '电磁介质'
                },
            ],
            smdjList: [
                {
                    smdjid: '1',
                    smdjmc: '绝密'
                },
                {
                    smdjid: '2',
                    smdjmc: '机密'
                },
                {
                    smdjid: '3',
                    smdjmc: '秘密'
                },
                {
                    smdjid: '4',
                    smdjmc: '内部'
                },
            ],
            xdfsList: [
                {
                    xdfsid: '1',
                    xdfsmc: '包装密封，封口处加盖密封章'
                },
                {
                    xdfsid: '2',
                    xdfsmc: '指派专人传递'
                },
                {
                    xdfsid: '3',
                    xdfsmc: '密码箱防护'
                },
            ],
            jtgjList: [
                {
                    jtgjid: '1',
                    jtgjmc: '飞机'
                },
                {
                    jtgjid: '2',
                    jtgjmc: '火车'
                },
                {
                    jtgjid: '3',
                    jtgjmc: '专车'
                },
            ],
            //轨迹处理
            gjclList: [],
            //人员任用
            smryList: [],
            disabled2: false,
            disabled3: false,
            disabled4: false,
            //通过
            tgdis: false,
            dialogVisible: false,
            fileRow: '',
            fwdyid: '',
            slid: '',
            jlid: '',
            xsyc: true,
            zhsp: true,
            jgyf: '',
            xm: '',
            //审批状态码 1 2 3 4
            zplcztm: null,
            //流程跟踪
            lcgzList: [],
            deb: true,
            typezt: '',
        }
    },
    computed: {

    },
    mounted() {
        this.typezt = this.$route.query.typezt
        if (this.typezt != 'fhxq') {
            this.deb = false
        }
        this.getNowTime()
        console.log(this.$route.query.list);
        this.fwdyid = this.$route.query.fwdyid
        console.log("this.fwdyid", this.fwdyid);
        this.slid = this.$route.query.slid
        console.log("this.slid", this.slid);
        this.getjlid()
        this.dqlogin()
        this.getOrganization()
        //判断实例所处环节
        // this.pdschj()
        //审批指南初始化列表
        this.spzn()
        // //审批信息初始化列表
        // this.spxxxgcc()
        setTimeout(() => {
            this.spxx()
        }, 500)
        // // //事项审核
        this.sxsh()
        // //初始化el-dialog列表数据
        this.splist()
        //流程跟踪初始化列表
        this.lcgz()
    },
    methods: {
        async handleChange(index) {
            let resList
            let params = {
                bmmc: this.tjlist.bgbm.join('/')
            }
            resList = await getAllYhxx(params)
            this.tjlist.gdr = "";

            this.restaurants = resList;
        },
        //人员获取
        querySearch(queryString, cb) {
            var restaurants = this.restaurants;
            console.log("restaurants", restaurants);
            var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
            console.log("results", results);
            // 调用 callback 返回建议列表的数据
            cb(results);
            console.log("cb(results.dwmc)", results);
        },
        createFilter(queryString) {
            return (restaurant) => {
                return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
            };
        },
        //全部组织机构List
        async getOrganization() {
            let zzjgList = await getZzjgList()
            this.zzjgmc = zzjgList
            let shu = []
            this.zzjgmc.forEach(item => {
                let childrenRegionVo = []
                this.zzjgmc.forEach(item1 => {
                    if (item.bmm == item1.fbmm) {
                        childrenRegionVo.push(item1)
                        item.childrenRegionVo = childrenRegionVo
                    }
                });
                shu.push(item)
            })
            let shuList = []
            let list = await getLoginInfo()
            if (list.fbmm == '') {
                shu.forEach(item => {
                    if (item.fbmm == '') {
                        shuList.push(item)
                    }
                })
            }
            if (list.fbmm != '') {
                shu.forEach(item => {
                    console.log(item);
                    if (item.fbmm == list.fbmm) {
                        shuList.push(item)
                    }
                })
            }
            shuList[0].childrenRegionVo.forEach(item => {
                this.regionOption.push(item)
            })
        },
        async getjlid() {
            let params = {
                slid: this.slid
            }
            let data = await getJlidBySlid(params)
            console.log(data);
            this.jlid = data
        },
        getNowTime() {
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            console.log(defaultDate)
            return defaultDate;
            this.$set(this.info, "stockDate", defaultDate);
        },

        //当前登录用户
        async dqlogin() {
            let data = await getUserInfo()
            this.xm = data.xm
            console.log('this.dqlogin', this.xm);
        },
        //立即办理
        ljbl() {
            this.activeName = 'second'
        },
        //审批指南
        //审批指南初始化列表
        async spzn() {
            let params = {
                fwdyid: this.fwdyid,
            }
            let data = await getBlzn(params)
            if (data.code == 10000) {
                this.spznList = data.data.content
            }
        },
        //审批信息
        async spxx() {
            let params = {
                slid: this.slid
            }
            let data = await getZtJscdBySlid(params)
            console.log(data);
            let Array = []
            Array.push(data.jsqsrq, data.jsjzrq)
            console.log(Array);
            this.tjlist = data
            this.tjlist.jscdqx = Array
            let zt = {
                yjlid: this.jlid
            }
            let ztqd = await getZtqdListByYjlid(zt)
            if (this.tjlist.bgbm != '' && this.tjlist.bgbm != undefined) {
                this.tjlist.bgbm = this.tjlist.bgbm.split('/')
            }
            this.ztqsQsscScjlList = ztqd
            this.ztqsQsscScjlList.forEach((item) => {
                console.log(item);
                if (item.lx == 1) {
                    item.lx = '纸介质'
                } else if (item.lx == 2) {
                    item.lx = '光盘'
                } else if (item.lx == 3) {
                    item.lx = '电磁介质'
                }
                if (item.smmj == 1) {
                    item.smmj = '绝密'
                } else if (item.smmj == 2) {
                    item.smmj = '机密'
                } else if (item.smmj == 3) {
                    item.smmj = '秘密'
                } else if (item.smmj == 4) {
                    item.smmj = '内部'
                }
            })
            let now = new Date();
            let year = now.getFullYear(); //得到年份
            let month = now.getMonth(); //得到月份
            let date = now.getDate(); //得到日期
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            let defaultDate = `${year}-${month}-${date}`;
            console.log('this.spxx', this.xm);
            if (this.zplcztm == 1) {
                this.tjlist.bmbmyscxm = this.xm
                this.$set(this.tjlist, 'bmbmyscsj', defaultDate)
                console.log(this.tjlist.bmbmyscxm);

            } else if (this.zplcztm == 2) {
                this.tjlist.bmbmyscxm = this.tjlist.bmbmyscxm
                this.tjlist.bmldscxm = this.xm
                console.log(this.tjlist.bmldscxm);

                this.$set(this.tjlist, 'bmldscsj', defaultDate)
            } else if (this.zplcztm == 3) {
                this.tjlist.bmbmyscxm = this.tjlist.bmbmyscxm
                this.tjlist.bmldscxm = this.tjlist.bmldscxm
                this.tjlist.bmbxm = this.xm
                this.tjlist.bgbm = this.tjlist.szbm.split('/')
                let resList
                let params = {
                    bmmc: this.tjlist.bgbm.join('/')
                }
                resList = await getAllYhxx(params)
                this.tjlist.gdr = "";

                this.restaurants = resList;
                console.log(this.tjlist.bmbxm);

                this.$set(this.tjlist, 'bmbsj', defaultDate)
            }
        },
        //判断实例所处环节
        async pdschj() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSchj(params)
            this.zplcztm = data.data.content
            console.log('this.zplcztm', this.zplcztm);
            if (data.code == 10000) {
                if (data.data.content == 1) {
                    this.disabled3 = true
                    this.disabled4 = true
                }
                if (data.data.content == 2) {
                    this.disabled2 = true
                    this.disabled4 = true
                }
                if (data.data.content == 3) {
                    this.disabled2 = true
                    this.disabled3 = true
                }
            }
        },
        chRadio() { },
        //初始化el-dialog列表数据
        async splist() {
            let params = {
                fwdyid: this.fwdyid,
                'xm': this.formInline.xm,
                'bmmc': this.formInline.bmmc,
                page: this.page,
                pageSize: this.pageSize,
                qshjid: this.mbhjid,
            }
            let data = await getSpUserList(params)
            this.smryList = data.records
            this.total = data.total


        },
        onSubmit() {
            this.splist()
        },
        async submit() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                shry: this.selectlistRow[0].yhid,
                mbhjid: this.mbhjid,
            }
            let data = await tjclr(params)
            if (data.code == 10000) {
                this.$message({
                    message: data.message,
                    type: 'success'
                });
                this.dialogVisible = false
                setTimeout(() => {
                    this.$router.push('/dbsx')
                }, 500)
            }
        },
        handleSelectionChange(index, row) {
            this.radioIdSelect = row
        },
        // 保存
        async save(index) {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
            }
            let data = await verifySfjshj(params)
            if (data == true) {
                this.ztqsQsscScjlList.forEach((item) => {
                    if (item.lx == '纸介质') {
                        item.lx = 1
                    } else if (item.lx == '光盘') {
                        item.lx = 2
                    } else if (item.lx == '电磁介质') {
                        item.lx = 3
                    }
                    if (item.smmj == '绝密') {
                        item.smmj = 1
                    } else if (item.smmj == '机密') {
                        item.smmj = 2
                    } else if (item.smmj == '秘密') {
                        item.smmj = 3
                    } else if (item.smmj == '内部') {
                        item.smmj = 4
                    }
                    let params = {
                        ztid: item.ztid,
                        ztmc: item.ztmc,
                        ztbh: item.ztbh,
                        xmbh: item.xmbh,
                        scyy: 3,
                        smmj: item.smmj,
                        bmqx: item.bmqx,
                        lx: item.lx,
                        fs: item.fs,
                        ys: item.ys,
                        zxfw: this.tjlist.zxfw,
                        scrq: this.getNowTime(),
                        scbm: this.tjlist.szbm,
                        zrr: '111',
                        bgwz: '111',
                        zt: 1,
                        // ztbgsj: this.tjlist.ztbgsj,
                    }
                    saveZtgl(params)
                })
            }
            console.log('==============', data);
            // if (data == true) {
            //     let params = new FormData();
            //     params.append('gwmc', this.tjlist.bgsmgw)
            //     params.append('smryid', this.tjlist.smryid)
            //     params.append('smdj', this.tjlist.bgsmdj)
            //     let list = await updateYhxx(params)
            // }
            let jgbz = index
            if (jgbz == 1) {
                console.log(this.tjlist.bmbmysc);
                console.log(this.tjlist.bmldsc);
                console.log(this.tjlist.bmbsc);
                if (this.zplcztm == 1) {
                    if (this.tjlist.bmbmysc != undefined) {
                        if (this.tjlist.bmbmyscsj != undefined) {
                            this.tgdis = true
                            let obj = {
                                bmbmysc: this.tjlist.bmbmysc,
                                bmbmyscsj: this.tjlist.bmbmyscsj,
                                bmbmyscxm: this.tjlist.bmbmyscxm,
                            }
                            let params = Object.assign(this.tjlist, obj)
                            let data = await updateZtJscd(params)
                            if (data.code == 10000) {
                                this.jgyf = 1
                                this.sxsh()
                                this.spxx()
                            } else {
                                this.spxx()
                            }
                        } else { this.$message.warning('请选择日期') }
                    } else { this.$message.warning('是否同意') }
                }
                else if (this.zplcztm == 2) {
                    if (this.tjlist.bmldsc != undefined) {
                        if (this.tjlist.bmldscsj != undefined) {
                            this.tgdis = true
                            let obj = {
                                bmldsc: this.tjlist.bmldsc,
                                bmldscsj: this.tjlist.bmldscsj,
                                bmldscxm: this.tjlist.bmldscxm,
                            }
                            let params = Object.assign(this.tjlist, obj)
                            let data = await updateZtJscd(params)
                            if (data.code == 10000) {
                                this.jgyf = 1
                                this.sxsh()
                                this.spxx()
                            } else {
                                this.spxx()
                            }
                        } else { this.$message.warning('请选择日期') }
                    } else { this.$message.warning('是否同意') }
                }
                else if (this.zplcztm == 3) {
                    if (this.tjlist.bmbsc != undefined) {
                        if (this.tjlist.bmbsj != undefined) {
                            this.tgdis = true
                            let obj = {
                                bmbsc: this.tjlist.bmbsc,
                                bmbsj: this.tjlist.bmbsj,
                                bmbxm: this.tjlist.bmbxm,
                                bgbm: this.tjlist.bgbm.join('/'),
                            }
                            let params = Object.assign(this.tjlist, obj)
                            let data = await updateZtJscd(params)
                            if (data.code == 10000) {
                                this.ztqsQsscScjlList.forEach(async (item) => {
                                    if (item.lx == '纸介质') {
                                        item.lx = 1
                                    } else if (item.lx == '光盘') {
                                        item.lx = 2
                                    } else if (item.lx == '电磁介质') {
                                        item.lx = 3
                                    }
                                    if (item.smmj == '绝密') {
                                        item.smmj = 1
                                    } else if (item.smmj == '机密') {
                                        item.smmj = 2
                                    } else if (item.smmj == '秘密') {
                                        item.smmj = 3
                                    } else if (item.smmj == '内部') {
                                        item.smmj = 4
                                    }
                                    let spd = Object.assign(item, params)
                                    console.log('添加审批单子', spd);
                                    let jscd = await addZtJscdDj(spd)
                                    if (jscd.code == 10000) {
                                        this.jgyf = 1
                                        this.sxsh()
                                        this.spxx()
                                    }
                                })
                            } else {
                                this.spxx()
                            }
                        } else { this.$message.warning('请选择日期') }
                    } else { this.$message.warning('是否同意') }
                }
            } else if (jgbz == 2) {
                this.jgyf = 2
                this.sxsh()
                this.spxx()
            } else if (jgbz == 3) {
                this.jgyf = 3
                this.sxsh()
                this.spxx()
            }
        },
        //事项审核
        async sxsh() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid,
                jg: this.jgyf,
                smryid: ''
            }
            let data = await getSxsh(params)
            if (data.code == 10000) {
                this.tgdis = false
                if (data.data.zt == 0) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // this.smryList = data.data.blrarr
                    this.mbhjid = data.data.mbhjid
                    this.splist()
                    this.dialogVisible = true
                } else if (data.data.zt == 1) {
                    this.$message({
                        message: data.data.msg,
                        type: 'success'
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 2) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                } else if (data.data.zt == 3) {
                    this.$message({
                        message: data.data.msg
                    });
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
                else if (data.data.zt == 4) {
                    this.$message({
                        message: data.data.msg
                    });
                    console.log(1111111111111);
                    // setTimeout(() => {
                    //     this.$router.push('/dbsx')
                    // }, 500)
                    this.$router.push('/dbsx')
                }
            }
        },
        //列表分页--跳转页数
        handleCurrentChange(val) {
            this.page = val
            this.splist()
        },
        //列表分页--更改每页显示个数
        handleSizeChange(val) {
            this.page = 1
            this.pageSize = val
            this.splist()
        },
        // 点击行触发，选中或不选中复选框
        handleRowClick(row, column, event) {
            this.$refs.multipleTable.toggleRowSelection(row)
            this.selectChange(this.selectlistRow)
        },
        handleSelect(selection, val) {
            // //只能选择一行，选择其他，清除上一行
            if (selection.length > 1) {
                let del_row = selection.shift()
                this.$refs.multipleTable.toggleRowSelection(del_row, false) //设置这一行取消选中
            }
        },
        selectRow(selection) {
            if (selection.length <= 1) {
                console.log('点击选中数据：', selection);
                this.selectlistRow = selection
                this.xsyc = true
            } else if (selection.length > 1) {
                this.$message.warning('只能选中一条数据')
                this.xsyc = false
            }
        },
        // 返回
        returnIndex() {
            this.$router.push('/gwbgscb')
        },
        //流程跟踪
        //流程跟踪初始化列表
        async lcgz() {
            let params = {
                fwdyid: this.fwdyid,
                slid: this.slid
            }
            let data = await getSpGjxx(params)
            if (data.code == 10000) {
                this.lcgzList = data.data.content
                this.gjclList = data.data.content
                console.log(this.gjclList);
            }
        },
        fhry() {
            this.$router.push({
                path: '/ztglxqy',
                query: {
                    row: this.$route.query.row
                }
            })
        },
    },
    watch: {

    }
}

</script>
  
<style scoped>
.sec-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: overlay;
}

.sec-title {
    border-left: 5px solid #1b72d8;
    color: #1b72d8;
    font-size: 20px;
    font-weight: 700;
    text-indent: 10px;
    margin-bottom: 20px;
    margin-top: 10px;
}

.sec-form-container {
    width: 100%;
    height: 100%;
}

.sec-form-left {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
}

.sec-form-left:not(:first-child) {
    border-top: 0;
}

.sec-form-left .el-form-item {
    float: left;
    width: 100%;
}

.sec-header-section {
    width: 100%;
    position: relative;
}

.sec-header-pic {
    width: 258px;
    position: absolute;
    right: 0px;
    top: 0;
    height: 163px;
    border: 1px solid #CDD2D9;
    border-left: 0;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sec-form-second {
    /* width: 100%; */
    border: 1px solid #CDD2D9;
    height: 40px;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
    border-right: 0px;
    border-top: 0;
}

.sec-form-third {
    border: 1px solid #CDD2D9;
    /* height: 40px;  */
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-four {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    /* display: flex;
    justify-content: space-evenly; */
    overflow: hidden;
    /* border-right: 0px; */
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.sec-form-five {
    border: 1px solid #CDD2D9;
    height: auto;
    min-height: 100px;
    overflow: hidden;
    border-top: 0;
    background: #ffffff;
    padding: 10px;
}

.yulan {
    text-align: center;
    cursor: pointer;
    color: #3874D5;
    font-weight: 600;
    float: left;
    margin-left: 10px;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 2px solid #EBEBEB;
}

.sec-form-six {
    border: 1px solid #CDD2D9;
    overflow: hidden;
    background: #ffffff;
    padding: 10px;
}

.ml10 {
    margin-left: 10px;
}

.sec-footer {
    margin-top: 10px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
    border: none;
}

.sec-left-text {
    float: left;
    margin-right: 130px;
}

.haveBorderTop {
    border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__content {
    padding-left: 20px;
    border-right: 1px solid #CDD2D9;
    background: #ffffff;
}

/* .sec-form-second:not(:first-child){
    border-top: 0;
  } */
.sec-form-second .el-form-item {
    float: left;
    width: 100%;
}

.sec-el-table {
    width: 100%;
    border: 1px solid #EBEEF5;
    height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
    padding-left: 15px;
    background-color: #F5F7FA;
    width: calc(100% - 16px);
    border-right: 1px solid #CDD2D9;
    color: #C0C4CC;
}

>>>.sec-el-table .el-input__inner {
    border: none !important;
    border-radius: 0;
}

.riq {
    width: 100% !important;
}

>>>.sec-form-container .el-form-item__label {
    width: 200px;
    text-align: center;
    font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
    border: none;
    border-right: 1px solid #CDD2D9;
    border-radius: 0;
}

>>>.sec-form-container .el-form-item {
    margin-bottom: 0px;
}

/* >>>.el-form > div {
    border: 1px solid #CDD2D9;;
  } */
>>>.el-form-item__label {
    border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
    margin-top: 5px;
    margin-bottom: 5px;
  } */

.widthw {
    width: 6vw;
}

.dlFqsqContainer {
    width: 100%;
    height: 100%;
}

.dlFqsqContainer label {
    font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
    width: 150px;
    margin-left: 10px;
}

.dlFqsqContainer .searchButton {
    margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
    height: 40px;
}

.dlFqsqContainer .input1 {
    margin-right: 20px;
}

.dlFqsqContainer .tb-container {
    margin-top: 20px;
}

.gtzzsmgwgz>>>.el-form-item__label {
    text-align: left !important;
}

.dlFqsqContainer .paginationContainer {
    margin-top: 20px;
}

.fhry {
    float: right;
    z-index: 99;
    margin-top: 5px;
    position: relative;
}
</style>
  