<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div class="bg_con_top">
      <img src="./img/title.png" alt="" />
      <span class="title">变更记录</span>
    </div>
    <div style="height: 100%">
      <el-table
        :data="xjList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <!-- <el-table-column prop="mc" label="名称"></el-table-column> -->
        <el-table-column
          prop="computerRoomCode"
          label="机房编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="computerRoomName"
          label="机房名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentCode"
          label="设备编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentName"
          label="设备名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentType"
          label="设备类型"
          sortable
        ></el-table-column>
        <el-table-column
          prop="destroyStatus"
          label="变更状态"
          sortable
        ></el-table-column>
        <el-table-column
          prop="destroyTime"
          label="变更时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="destroyCode"
          label="变更人员编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="destroyName"
          label="变更人员名称"
          sortable
        ></el-table-column>
        <el-table-column prop="" label="操作" width="120">
          <template slot-scope="scoped">
            <el-button size="medium" type="text" @click="xqyl(scoped.row)"
              >详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import {
  sbxhqueryPage, //故障处理分页查询
} from "../../../../api/index";

export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        id: "",
        computerRoomCode: "",
        computerRoomName: "",
        cabinetNumber: "",
        serverNumber: "",
        switchNumber: "",
        routerNumber: "",
        address: "",
        lastInspectionTime: "",
        lastInspectionResult: "",
        remark: "",
        useStatus: 1,
        approveStatus: "",
      },
      tjType: "",
      id: "",
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" },
      ],
      spztList: [
        { id: 1, mc: "正在审批" },
        { id: 2, mc: "审批通过" },
        { id: 3, mc: "审批拒绝" },
      ],
      sbxxQhVal: 1,

      xjList: [],
      ksList: [],
      UwycList: [],
      McycList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      disabled: false,
      show: true, //是否显示
      isPrinting: false, //是否正在打印
      dialogVisible: false, //弹框
      img: "",
      xjgddyQbj: {},
    };
  },
  computed: {},
  mounted() {
    this.tjType = this.$route.query.routeType;
    this.aqcp();
  },
  methods: {
    //获取列表的值
    async aqcp() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        equipmentCode: this.$route.query.equipmentCode,
      };
      let resList = await sbxhqueryPage(params);
      console.log("params", params);
      this.xjList = resList.data.records;
      this.total = resList.data.total;
    },
    //详情弹框
    xqyl(row) {
      console.log(row,'row');
      this.$router.push({
        path: "/sbxhsjtjXqy",
        query: {
          id: row.id,
          routeType: "3",
          equipmentCode: row.equipmentCode,
        },
      });
    },
    // //详情弹框
    // xqyl(row) {
    //   this.$router.push({
    //     path: "/gzclglxxmx",
    //     query: {
    //       id: row.id,
    //     },
    //   });
    // },
    sbxxClick(index) {
      console.log(index);
      this.sbxxQhVal = index;
      if (this.sbxxQhVal == 2) {
      } else if (this.sbxxQhVal == 3) {
      }
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      this.aqcp();
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.aqcp();
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  padding: 10px;
}
.bg_con_top {
  width: 100%;
  height: 35px;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 20px;
}
/deep/.el-form-item .el-form-item__label {
  font-family: SourceHanSansSC-Regular !important;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
  text-align: left;
}
.flex {
  width: 950px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/deep/.el-input .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-select .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-textarea .el-textarea__inner {
  width: 830px !important;
  height: 129px !important;
  border-radius: 2px;
}
.ml-10 {
  background: #20bdd1;
  border: 1px solid #20bdd1;
  color: #ffffff;
}
/deep/.el-dialog__wrapper .el-dialog {
  background-image: linear-gradient(180deg, #f0f7fe 0%, #ffffff 32%);
  border: 1px solid rgba(151, 151, 151, 1);
  border-radius: 4px;
  padding-left: 6px;
  padding-right: 6px;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__header {
  padding: 15px 14.5px 13.5px 14px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  display: flex;
  align-items: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__body {
  padding: 0;
}
.formDialog {
  padding: 23.5px 45px 25px 45px;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #000116;
  font-weight: 400;
  position: relative;
}
.formDialog1 {
  padding: 38px 45px 49px 45px;
  margin-bottom: 10px;
}
.flexAlign {
  display: flex;
  align-items: center;
}
.formDialogItem {
  width: 100px;
  height: 24px;
  margin-right: 10px;
}
.formDialogCon {
  width: 320px;
  height: 24px;
}
.formDialogCon1 {
  width: 168px;
  height: 24px;
}
.fjx {
  width: 510px;
  height: 1px;
  background-color: rgba(229, 229, 229, 1);
  margin: 0 auto;
}
.ewm {
  position: absolute;
  width: 145px;
  height: 145px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 18px;
  right: 31.54px;
  border: 4px solid #d8e4fa;
  border-radius: 10px;
}
.btnRight {
  height: 40px;
  position: relative;
}
.btnRightItem {
  position: absolute;
  right: 31.54px;
}
.title {
  margin-left: 10px;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  position: relative;
}

.title1::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: -15px;
  background-color: #0077d2;
}
</style>
