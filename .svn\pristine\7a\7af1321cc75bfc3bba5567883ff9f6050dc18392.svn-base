{"version": 3, "sources": ["webpack:///./src/renderer/view/bggl/img/vip.png", "webpack:///./src/renderer/view/bggl/img/pdf.png", "webpack:///./src/renderer/view/bggl/img/word.png", "webpack:///src/renderer/view/bggl/bgglsy.vue", "webpack:///./src/renderer/view/bggl/bgglsy.vue?b76c", "webpack:///./src/renderer/view/bggl/bgglsy.vue"], "names": ["module", "exports", "bgglsy", "components", "props", "data", "bgAllLoading", "formInline", "wdmc", "wdlx", "wdList", "listDatas", "page", "pageSize", "total", "encryptList", "computed", "watch", "methods", "getWdList", "_this", "this", "params", "Object", "bggl", "then", "res", "code", "catch", "err", "$message", "error", "onSubmit", "onBgClick", "$router", "push", "quickLook", "item", "_this2", "fileType", "split", "pop", "_ref", "hyklDatas", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "resLoginInfo", "dwzch", "keyStr", "backStr", "bsItem", "wrap", "_context", "prev", "next", "kl", "dwzc", "sent", "md5_default", "substring", "console", "log", "path", "query", "stringify_default", "klIsRight", "type", "aes<PERSON><PERSON><PERSON>", "isZcStatus", "yxrq", "zcDate", "qfrq", "yxq", "Date", "now", "yxqqz", "id", "stop", "_x", "apply", "arguments", "handleCurrentChange", "val", "handleSizeChange", "created", "mounted", "beforeCreated", "beforeMount", "beforUpdate", "updated", "beforeDestory", "destoryed", "activated", "bggl_bgglsy", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "name", "rawName", "value", "expression", "staticClass", "attrs", "inline", "model", "size", "staticStyle", "font-weight", "label", "clearable", "placeholder", "callback", "$$v", "$set", "_v", "icon", "on", "click", "length", "_l", "key", "src", "__webpack_require__", "width", "alt", "_e", "_s", "wdslt", "height", "wdzy", "ys", "$event", "staticRenderFns", "Component", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "wCAAAA,EAAAC,QAAA,6jaCAAD,EAAAC,QAAA,oiLCAAD,EAAAC,QAAA,y1MC0EAC,GAEAC,cACAC,SACAC,KAJA,WAMA,OACAC,cAAA,EACAC,YACAC,KAAA,GACAC,KAAA,IAEAC,UACAC,aACAC,KAAA,EACAC,SAAA,GACAC,MAAA,EACAC,iBAIAC,YAEAC,SAEAC,SAEAC,UAFA,SAEAX,EAAAC,GAAA,IAAAW,EAAAC,KACAA,KAAAf,cAAA,EAEA,IAAAgB,GACAd,OACAC,QAEYc,OAAAC,EAAA,EAAAD,CAAZD,GAAAG,KAAA,SAAAC,GACA,KAAAA,EAAAC,OACAP,EAAAV,OAAAgB,EAAArB,KACAe,EAAAd,cAAA,KAEAsB,MAAA,SAAAC,GACAT,EAAAU,SAAAC,MAAA,gBAIAC,SAnBA,WAoBAX,KAAAF,UAAAE,KAAAd,WAAAC,KAAAa,KAAAd,WAAAE,OAGAwB,UAvBA,WAwBAZ,KAAAa,QAAAC,KAAA,YAGAC,UA3BA,SA2BAC,GAAA,IAAAC,EAAAjB,KACAgB,EAAAE,SAAA,IAAAF,EAAA7B,KAAAgC,MAAA,KAAAC,MAEA,IACAC,EADAC,EAAA,GACYpB,OAAAC,EAAA,EAAAD,GAAZE,MAAAiB,EAAAE,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,EAAAtB,GAAA,IAAAuB,EAAAC,EAAAC,EAAAC,EAAA9B,EAAA+B,EAAA,OAAAR,EAAAC,EAAAQ,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,UACA,KAAA/B,EAAAC,KADA,CAAA4B,EAAAE,KAAA,eAEAd,EAAAjB,EAAArB,KAAAqD,GAFAH,EAAAE,KAAA,EAIAlC,OAAAoC,EAAA,EAAApC,GAJA,OAIA0B,EAJAM,EAAAK,KAKAV,EAAA,GACAD,EAAAC,QACAA,EAAAD,EAAAC,OAGAC,EAAAU,IAAAX,GAAAY,UAAA,MAEA,IAAAnB,GACAoB,QAAAC,IAAA,uBAEA1B,EAAAJ,QAAAC,MAAA8B,KAAA,QAAAC,OAAA7B,KAAA8B,IAAA9B,GAAA+B,WAAA,EAAAzB,YAAA0B,KAAAhC,EAAAE,cAEAwB,QAAAC,IAAA,qBAEAZ,EAAA7B,OAAA+C,EAAA,EAAA/C,CAAAoB,EAAAQ,GACAY,QAAAC,IAAAZ,GAEAA,EAAAF,OAEAA,GACAa,QAAAC,IAAA,0BACAZ,EAAAmB,YA0BAR,QAAAC,IAAA,QACAX,EAAAD,EAAAoB,KAAA,GACApB,EAAAqB,OAAArB,EAAAsB,MAAAtB,EAAAqB,OAAApB,EAAAsB,KACAZ,QAAAC,IAAA,aAAA3B,GACA0B,QAAAC,IAAA,8BAEA1B,EAAAJ,QAAAC,MAAA8B,KAAA,QAAAC,OAAA7B,KAAA8B,IAAA9B,GAAA+B,WAAA,EAAAzB,YAAA0B,KAAAhC,EAAAE,cAEAwB,QAAAC,IAAA,4BAEA1B,EAAAJ,QAAAC,MAAA8B,KAAA,QAAAC,OAAA7B,KAAA8B,IAAA9B,GAAA+B,WAAA,EAAAzB,YAAA0B,KAAAhC,EAAAE,eAlCAwB,QAAAC,IAAA,OACAY,KAAAC,MAAAzB,EAAAsB,MAAAE,KAAAC,MAAAzB,EAAAsB,KAAA,SAAAtB,EAAA0B,OAEA1B,EAAAmB,WAAA,EACAnB,EAAAqB,OAAAG,KAAAC,MACAtD,OAAA+C,EAAA,EAAA/C,CAAA6B,EAAAD,GACA7B,GACAyD,GAAA,IACArB,GAAAnC,OAAA+C,EAAA,EAAA/C,CAAA4C,IAAAf,GAAAD,IAEA5B,OAAAC,EAAA,EAAAD,CAAAD,GAAAG,KAAA,SAAAC,GACA,KAAAA,EAAAC,OACAoC,QAAAC,IAAA,cAEA1B,EAAAJ,QAAAC,MAAA8B,KAAA,QAAAC,OAAA7B,KAAA8B,IAAA9B,GAAA+B,WAAA,EAAAzB,YAAA0B,KAAAhC,EAAAE,eAEAX,MAAA,SAAAC,GACAS,EAAAR,SAAAC,MAAA,iBAGAgC,QAAAC,IAAA,WACA1B,EAAAR,SAAAC,MAAA,eAkBAgC,QAAAC,IAAA,SACA1B,EAAAJ,QAAAC,MAAA8B,KAAA,QAAAC,OAAA7B,KAAA8B,IAAA9B,GAAA+B,WAAA,EAAAzB,YAAA0B,KAAAhC,EAAAE,cApEA,wBAAAgB,EAAAyB,SAAAhC,EAAAV,MAAA,SAAA2C,GAAA,OAAAvC,EAAAwC,MAAA7D,KAAA8D,cAwEAvD,MAAA,SAAAC,GACAkC,QAAAC,IAAAnC,GACAS,EAAAJ,QAAAC,MAAA8B,KAAA,QAAAC,OAAA7B,KAAA8B,IAAA9B,GAAA+B,WAAA,EAAAzB,YAAA0B,KAAAhC,EAAAE,eAIA6C,oBA7GA,SA6GAC,GACAhE,KAAAT,KAAAyE,GAIAC,iBAlHA,SAkHAD,GACAhE,KAAAT,KAAA,EACAS,KAAAR,SAAAwE,IAKAE,QAlJA,aAsJAC,QAtJA,WAuJAnE,KAAAF,UAAAE,KAAAd,WAAAC,KAAAa,KAAAd,WAAAE,OAGAgF,cA1JA,aA4JAC,YA5JA,aA8JAC,YA9JA,aAgKAC,QAhKA,aAkKAC,cAlKA,aAoKAC,UApKA,aAsKAC,UAtKA,cCvEeC,GADEC,OAFjB,WAA0B,IAAAC,EAAA7E,KAAa8E,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAaC,KAAA,UAAAC,QAAA,YAAAC,MAAAR,EAAA,aAAAS,WAAA,iBAAsFC,YAAA,gBAA4BP,EAAA,OAAYO,YAAA,kBAA4BP,EAAA,WAAgBO,YAAA,sBAAAC,OAAyCC,QAAA,EAAAC,MAAAb,EAAA3F,WAAAyG,KAAA,YAAsDX,EAAA,gBAAqBY,aAAaC,cAAA,OAAoBL,OAAQM,MAAA,MAAYd,EAAA,YAAiBO,YAAA,SAAAC,OAA4BO,UAAA,GAAAC,YAAA,WAAuCN,OAAQL,MAAAR,EAAA3F,WAAA,KAAA+G,SAAA,SAAAC,GAAqDrB,EAAAsB,KAAAtB,EAAA3F,WAAA,OAAAgH,IAAsCZ,WAAA,sBAA+B,GAAAT,EAAAuB,GAAA,KAAApB,EAAA,gBAAAA,EAAA,aAAqDQ,OAAOxC,KAAA,UAAAqD,KAAA,kBAAyCC,IAAKC,MAAA1B,EAAAlE,YAAsBkE,EAAAuB,GAAA,YAAAvB,EAAAuB,GAAA,KAAApB,EAAA,gBAAoDO,YAAA,4BAAsCP,EAAA,aAAkBQ,OAAOxC,KAAA,UAAAqD,KAAA,mBAA0CC,IAAKC,MAAA1B,EAAAjE,aAAuBiE,EAAAuB,GAAA,cAAAvB,EAAAuB,GAAA,KAAApB,EAAA,OAA6CO,YAAA,eAAwB,GAAAV,EAAAuB,GAAA,KAAApB,EAAA,OAA4BO,YAAA,eAAwB,GAAAV,EAAAuB,GAAA,KAAAvB,EAAAxF,OAAAmH,OAAA,EAAAxB,EAAA,OAAoDO,YAAA,eAAyBP,EAAA,OAAYO,YAAA,UAAqBV,EAAA4B,GAAA5B,EAAA,gBAAA7D,GAAoC,OAAAgE,EAAA,OAAiB0B,IAAA1F,EAAA0C,GAAA6B,YAAA,cAAoCP,EAAA,OAAYO,YAAA,aAAuB,IAAAvE,EAAA7B,KAAAgC,MAAA,KAAAC,OAAA,QAAA4D,EAAA,OAA0DQ,OAAOmB,IAAMC,EAAQ,QAAgBC,MAAA,OAAAC,IAAA,MAA2BjC,EAAAkC,KAAAlC,EAAAuB,GAAA,SAAApF,EAAA7B,KAAAgC,MAAA,KAAAC,OAAA,OAAA4D,EAAA,OAA8EQ,OAAOmB,IAAMC,EAAQ,QAAeC,MAAA,OAAAC,IAAA,MAA2BjC,EAAAkC,KAAAlC,EAAAuB,GAAA,KAAApB,EAAA,OAAiCO,YAAA,UAAoBV,EAAAuB,GAAAvB,EAAAmC,GAAAhG,EAAA7B,SAAA0F,EAAAuB,GAAA,KAAApB,EAAA,OAAoDO,YAAA,MAAAC,OAAyBmB,IAAMC,EAAQ,QAAeC,MAAA,OAAAC,IAAA,QAA2BjC,EAAAuB,GAAA,KAAApB,EAAA,OAA0BO,YAAA,WAAqBP,EAAA,OAAYQ,OAAOmB,IAAA,yBAAsB3F,EAAAiG,MAAAJ,MAAA,OAAAK,OAAA,OAAAJ,IAAA,MAA+DjC,EAAAuB,GAAA,KAAApB,EAAA,OAAwBO,YAAA,aAAuBP,EAAA,OAAYO,YAAA,WAAqBV,EAAAuB,GAAAvB,EAAAmC,GAAAhG,EAAAmG,MAAA,SAAAtC,EAAAuB,GAAA,KAAApB,EAAA,OAA0DO,YAAA,WAAqBP,EAAA,OAAAH,EAAAuB,GAAA,KAAAvB,EAAAmC,GAAAhG,EAAAoG,IAAA,kDAAApG,EAAA5B,KAAA4F,EAAA,QAAAH,EAAAuB,GAAA,QAAAvB,EAAAkC,KAAAlC,EAAAuB,GAAA,QAAApF,EAAA5B,KAAA4F,EAAA,QAAAH,EAAAuB,GAAA,gBAAAvB,EAAAkC,KAAAlC,EAAAuB,GAAA,QAAApF,EAAA5B,KAAA4F,EAAA,QAAAH,EAAAuB,GAAA,kBAAAvB,EAAAkC,KAAAlC,EAAAuB,GAAA,QAAApF,EAAA5B,KAAA4F,EAAA,QAAAH,EAAAuB,GAAA,gBAAAvB,EAAAkC,WAAAlC,EAAAuB,GAAA,KAAApB,EAAA,OAAiYO,YAAA,SAAmBP,EAAA,OAAYO,YAAA,wBAAAe,IAAwCC,MAAA,SAAAc,GAAyB,OAAAxC,EAAA9D,UAAAC,OAA6B6D,EAAAuB,GAAA,kBAAyB,KAAApB,EAAA,OAAiBO,YAAA,YAAsBV,EAAAuB,GAAA,aAEhmFkB,oBCCjB,IAcAC,EAdyBX,EAAQ,OAcjCY,CACE3I,EACA8F,GATF,EAVA,SAAA8C,GACEb,EAAQ,SAaV,kBAEA,MAUec,EAAA,QAAAH,EAAiB", "file": "js/13.2f993720c1c4654e63f9.js", "sourcesContent": ["module.exports = \"data:image/png;base64,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\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/bggl/img/vip.png\n// module id = +84p\n// module chunks = 13", "module.exports = \"data:image/png;base64,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\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/bggl/img/pdf.png\n// module id = JPeP\n// module chunks = 13", "module.exports = \"data:image/png;base64,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\"\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/bggl/img/word.png\n// module id = bj+G\n// module chunks = 13", "<template>\n    <div class=\"bgContainer\" v-loading=\"bgAllLoading\">\n        <div class=\"con positionR\">\n            <el-form :inline=\"true\" :model=\"formInline\" size=\"medium\" class=\"demo-form-inline fl\">\n                <el-form-item label=\"\" style=\"font-weight: 700;\">\n                    <el-input v-model=\"formInline.wdmc\" clearable placeholder=\"请输入报告名称\" class=\"widthw\">\n                    </el-input>\n                </el-form-item>\n                <el-form-item>\n                    <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"onSubmit\">查询</el-button>\n                </el-form-item>\n                <el-form-item class=\"positionA bgBtnPosition\">\n                    <el-button type=\"primary\" icon=\"el-icon-s-order\" @click=\"onBgClick\">文档管理</el-button>\n                </el-form-item>\n                <div class=\"clearBoth\"></div>\n            </el-form>\n            <div class=\"clearBoth\"></div>\n        </div>\n        <div class=\"con-center\" v-if=\"wdList.length > 0\">\n            <div class=\"center\">\n                <div class=\"border-wz\" v-for=\"item in wdList\" :key=\"item.id\">\n                    <div class=\"wz-title\">\n                        <img v-if=\"'.' + item.wdmc.split('.').pop() == '.docx'\" src=\"./img/word.png\" width=\"30px\" alt=\"\">\n                        <img v-if=\"'.' + item.wdmc.split('.').pop() == '.pdf'\" src=\"./img/pdf.png\" width=\"30px\" alt=\"\">\n                        <div class=\"wenzi\">{{ item.wdmc }}</div>\n                        <img class=\"vip\" src=\"./img/vip.png\" width=\"30px\" alt=\"\" />\n                    </div>\n                    <div class=\"wz-con\">\n                        <img :src=\"'data:image/jpg;base64,' + item.wdslt\" width=\"60px\" height=\"80px\" alt=\"\">\n                        <div class=\"maxWidth\">\n                            <div class=\"wenzi1\">{{ item.wdzy }}...</div>\n                            <div class=\"wz-btn\">\n                                <div>共 {{ item.ys }} 页&nbsp;&nbsp;&nbsp;&nbsp;类型:\n                                    <span v-if=\"item.wdlx == 1\">通用</span>\n                                    <span v-if=\"item.wdlx == 2\">涉密信息系统集成资质</span>\n                                    <span v-if=\"item.wdlx == 3\">武器装备科研生产单位资格</span>\n                                    <span v-if=\"item.wdlx == 4\">国家秘密载体印刷资质</span>\n                                </div>\n                            </div>\n                        </div>\n                        <div class=\"andw\">\n                            <div @click=\"quickLook(item)\" class=\"quickLook cursorClick\">快速预览</div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <!-- -------------------------分页区域---------------------------- -->\n            <!-- <div style=\"border: 1px solid #ebeef5;\">\n                <el-pagination background @current-change=\"handleCurrentChange\" @size-change=\"handleSizeChange\"\n                    :pager-count=\"5\" :current-page=\"page\" :page-sizes=\"[5, 10, 20, 30]\" :page-size=\"pageSize\"\n                    layout=\"total, prev, pager, sizes,next, jumper\" :total=\"total\">\n                </el-pagination>\n            </div> -->\n        </div>\n        <div class=\"noDatas\" v-else>暂无数据</div>\n    </div>\n</template>\n\n<script>\n// import { getDeportConfigPathDev } from '../../../utils/pathUtil'\n// // import { encryptAes,decryptAes } from '../../../utils/aesUtils'\nimport { decryptAesCBCHy, encryptAesCBCHy } from '../../../utils/aesUtils'\n// import {\n//   getWdInfoDatas,\n//   getHykl,\n//   deleteFileItem,\n//   reviseHyKl\n// } from \"../../../db/bgwdgldb.js\";\nimport MD5 from 'md5'\nimport {\n  getDwxx\n} from '../../../api/dwzc'\n// import unzipper from 'unzipper'\nimport { getAllWdList, getHyklDatas,reviseHyKl } from '../../../api/bggl'\nexport default {\n    //import引入的组件需要注入到对象中才能使用\n    components: {},\n    props: {},\n    data() {\n        //这里存放数据\n        return {\n            bgAllLoading: false,\n            formInline: {\n                wdmc: '',\n                wdlx: ''\n            },\n            wdList: [],\n            listDatas: [],\n            page: 1,\n            pageSize: 10,\n            total: 0,\n            encryptList: []\n        }\n    },\n    //计算属性 类似于data概念\n    computed: {},\n    //监控data中数据变化\n    watch: {},\n    //方法集合\n    methods: {\n        // 获取文档列表数据\n        getWdList(wdmc, wdlx) {\n            this.bgAllLoading = true\n            // 获取文档信息\n            let params = {\n                wdmc,\n                wdlx\n            }\n            getAllWdList(params).then(res => {\n                if (res.code == 10000) {\n                    this.wdList = res.data\n                    this.bgAllLoading = false\n                }\n            }).catch(err => {\n                this.$message.error('文档列表获取失败！')\n            })\n        },\n        //查询方法\n        onSubmit() {\n            this.getWdList(this.formInline.wdmc, this.formInline.wdlx)\n        },\n        // 报告管理\n        onBgClick() {\n            this.$router.push(\"/bgglgl\")\n        },\n        // 快速预览\n        quickLook(item) {\n            item.fileType = '.' + item.wdmc.split('.').pop()\n            // 数据库中是否有数据\n            let hyklDatas = ''\n            getHyklDatas().then(async res => {\n                if (res.code == 10000) {\n                    hyklDatas = res.data.kl\n                    // 单位注册号\n                    let resLoginInfo = await getDwxx()\n                    let dwzch = ''\n                    if(resLoginInfo.dwzch) {\n                        dwzch = resLoginInfo.dwzch\n                    }\n                    // let dwzch = '12345678'\n                    let keyStr = MD5(dwzch).substring(8, 24)\n                    // 数据库中无会员口令提示输入密钥串\n                    if (hyklDatas == \"\") {\n                        console.log('数据库里没有key，非会员----带锁')\n                        // 如果没有密钥或者密钥不匹配，查看首页并提示提示相关信息\n                        this.$router.push({ path: '/bggl', query: { item:JSON.stringify(item), klIsRight: false,hyklDatas,type:item.fileType } })\n                    } else { // 数据库中存在密钥串 验证库中密钥串是否有效\n                        console.log('数据库里有key------待验证')\n                        // 如果有，则验证密钥是否有效或是否在有限期内\n                        let backStr = decryptAesCBCHy(hyklDatas, keyStr)\n                        console.log(backStr)\n                        // 获取口令里的单位注册号\n                        let mDwzch = backStr.dwzch\n                        // 验证本地单位注册号与解密单位注册号是否相同\n                        if (mDwzch == dwzch) {\n                            console.log('验证此key中单位注册号与本地单位注册号一致')\n                            if (!backStr.isZcStatus) {\n                                // 未注册\n                                console.log('未注册')\n                                if (Date.now() > backStr.qfrq && Date.now() < backStr.qfrq + backStr.yxqqz * 86399915) {\n                                    // 当前系统时间是否在签发日期+注册阈值内\n                                    backStr.isZcStatus = 1\n                                    backStr.zcDate = Date.now()\n                                    encryptAesCBCHy(backStr, keyStr)\n                                    let params = {\n                                        \"id\": \"1\",\n                                        \"kl\": encryptAesCBCHy(JSON.stringify(backStr), keyStr)\n                                    }\n                                    reviseHyKl(params).then(res => {\n                                        if (res.code == 10000) {\n                                            console.log('注册成功可访问全部！')\n                                            // 如果正确并且在有效期内  可查看全部文章并可以下载全文\n                                            this.$router.push({ path: '/bggl', query: { item:JSON.stringify(item), klIsRight: true,hyklDatas,type:item.fileType } })\n                                        }\n                                    }).catch(err => {\n                                        this.$message.error('文档列表获取失败！')\n                                    })\n                                } else {\n                                    console.log('密钥串已经失效')\n                                    this.$message.error('密钥串已经失效')\n                                }\n                            } else {\n                                console.log('已经注册')\n                                let bsItem = backStr.yxrq[0]\n                                if (backStr.zcDate > backStr.qfrq && backStr.zcDate < bsItem.yxq) {\n                                    console.log('asdadadada',item);\n                                    console.log('已经注册 在有效期内  可查看全部文章并可以下载全文')\n                                    // 如果正确并且在有效期内  可查看全部文章并可以下载全文\n                                    this.$router.push({ path: '/bggl', query: { item:JSON.stringify(item), klIsRight: true,hyklDatas,type:item.fileType } })\n                                } else {\n                                    console.log('已经注册 密钥不匹配，查看首页并提示提示相关信息')\n                                    // 如果没有密钥或者密钥不匹配，查看首页并提示提示相关信息\n                                    this.$router.push({ path: '/bggl', query: { item:JSON.stringify(item), klIsRight: false,hyklDatas,type:item.fileType } })\n                                }\n                            }\n\n                        } else { // 提示非法密钥串\n                            console.log('非法密钥串')\n                            this.$router.push({ path: '/bggl', query: { item:JSON.stringify(item), klIsRight: false,hyklDatas,type:item.fileType } })\n                        }\n                    }\n                }\n            }).catch(err => {\n                console.log(err)\n                this.$router.push({ path: '/bggl', query: { item:JSON.stringify(item), klIsRight: false,hyklDatas,type:item.fileType } })\n            })\n        },\n        //列表分页--跳转页数\n        handleCurrentChange(val) {\n            this.page = val\n            // this.smgwgl()\n        },\n        //列表分页--更改每页显示个数\n        handleSizeChange(val) {\n            this.page = 1\n            this.pageSize = val\n            // this.smgwgl()\n        }\n    },\n    //生命周期 - 创建完成（可以访问当前this实例）\n    created() {\n\n    },\n    //生命周期 - 挂载完成（可以访问DOM元素）\n    mounted() {\n        this.getWdList(this.formInline.wdmc, this.formInline.wdlx)\n    },\n    //生命周期-创建之前\n    beforeCreated() { },\n    //生命周期-挂载之前\n    beforeMount() { },\n    //生命周期-更新之前\n    beforUpdate() { },\n    //生命周期-更新之后\n    updated() { },\n    //生命周期-销毁之前\n    beforeDestory() { },\n    //生命周期-销毁完成\n    destoryed() { },\n    //如果页面有keep-alive缓存功能，这个函数会触发\n    activated() { }\n}\n</script>\n<style scoped>\n.bgContainer {\n    width: 96%;\n    margin: 0 auto;\n    height: 100%;\n}\n\n.con {\n    width: 100%;\n    height: 8%;\n    display: flex;\n}\n\n.con-center {\n    width: 100%;\n    height: 92%;\n}\n\n.center {\n    width: 100%;\n    height: 100%;\n    overflow-y: scroll;\n}\n\n.border-wz {\n    width: 100%;\n    height: 26%;\n    background: rgb(255, 255, 255);\n    margin-bottom: 1%;\n    border-radius: 5px;\n    padding: 10px;\n    box-shadow: 0px 2px 10px 0px rgba(107, 117, 134, 0.15)\n}\n\n.wz-title {\n    width: 98%;\n    height: 25%;\n    display: flex;\n    align-items: center;\n    padding: 0px 1%;\n    margin-bottom: 7px;\n}\n\n/deep/.el-input__inner {\n    font-family: 'SourceHanSansSCziti';\n}\n\n/deep/.el-button span {\n    font-family: 'SourceHanSansSCziti';\n}\n\n.wz-title .vip {\n    margin-top: 5px;\n}\n\n.wenzi {\n    /* font-size: 14px; */\n    font-weight: 700;\n    margin-left: 10px;\n    margin-right: 10px;\n    font-family: 'SourceHanSansSCziti';\n    font-size: 20px;\n}\n\n.wz-con {\n    width: 98%;\n    height: 75%;\n    display: flex;\n    align-items: center;\n    padding: 0px 1%;\n    position: relative;\n}\n\n.wenzi1 {\n    font-family: 'SourceHanSansSCziti';\n    font-size: 16px;\n    margin-left: 15px;\n    margin-right: .5%;\n    height: 63px;\n    line-height: 25px;\n    color: grey;\n    width: calc(100% - 30px);\n}\n\n.wz-btn {\n    font-family: 'SourceHanSansSCziti';\n    width: 100%;\n    font-size: 16px;\n    margin-left: 15px;\n    margin-right: .5%;\n    /* margin-top: 1%; */\n    color: rgb(166, 175, 175);\n}\n\n.wz-btn div {\n    font-family: 'SourceHanSansSCziti';\n}\n\n.andw {\n    position: absolute;\n    right: 30px;\n    bottom: 10%;\n}\n\n.quickLook {\n    font-family: 'SourceHanSansSCziti';\n    font-size: 16px;\n    border-radius: 5px;\n    background: #409EFF;\n    color: #FFFFFF;\n    padding: 5px 10px;\n}\n\n.bgBtnPosition {\n    right: 0px;\n    top: 0px;\n}\n\n.noDatas {\n    height: calc(100% - 50px);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    font-size: 20px;\n    font-family: 'SourceHanSansSCziti';\n    color: #a3a6ab;\n}\n</style>\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/bggl/bgglsy.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.bgAllLoading),expression:\"bgAllLoading\"}],staticClass:\"bgContainer\"},[_c('div',{staticClass:\"con positionR\"},[_c('el-form',{staticClass:\"demo-form-inline fl\",attrs:{\"inline\":true,\"model\":_vm.formInline,\"size\":\"medium\"}},[_c('el-form-item',{staticStyle:{\"font-weight\":\"700\"},attrs:{\"label\":\"\"}},[_c('el-input',{staticClass:\"widthw\",attrs:{\"clearable\":\"\",\"placeholder\":\"请输入报告名称\"},model:{value:(_vm.formInline.wdmc),callback:function ($$v) {_vm.$set(_vm.formInline, \"wdmc\", $$v)},expression:\"formInline.wdmc\"}})],1),_vm._v(\" \"),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.onSubmit}},[_vm._v(\"查询\")])],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"positionA bgBtnPosition\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-s-order\"},on:{\"click\":_vm.onBgClick}},[_vm._v(\"文档管理\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"clearBoth\"})],1),_vm._v(\" \"),_c('div',{staticClass:\"clearBoth\"})],1),_vm._v(\" \"),(_vm.wdList.length > 0)?_c('div',{staticClass:\"con-center\"},[_c('div',{staticClass:\"center\"},_vm._l((_vm.wdList),function(item){return _c('div',{key:item.id,staticClass:\"border-wz\"},[_c('div',{staticClass:\"wz-title\"},[('.' + item.wdmc.split('.').pop() == '.docx')?_c('img',{attrs:{\"src\":require(\"./img/word.png\"),\"width\":\"30px\",\"alt\":\"\"}}):_vm._e(),_vm._v(\" \"),('.' + item.wdmc.split('.').pop() == '.pdf')?_c('img',{attrs:{\"src\":require(\"./img/pdf.png\"),\"width\":\"30px\",\"alt\":\"\"}}):_vm._e(),_vm._v(\" \"),_c('div',{staticClass:\"wenzi\"},[_vm._v(_vm._s(item.wdmc))]),_vm._v(\" \"),_c('img',{staticClass:\"vip\",attrs:{\"src\":require(\"./img/vip.png\"),\"width\":\"30px\",\"alt\":\"\"}})]),_vm._v(\" \"),_c('div',{staticClass:\"wz-con\"},[_c('img',{attrs:{\"src\":'data:image/jpg;base64,' + item.wdslt,\"width\":\"60px\",\"height\":\"80px\",\"alt\":\"\"}}),_vm._v(\" \"),_c('div',{staticClass:\"maxWidth\"},[_c('div',{staticClass:\"wenzi1\"},[_vm._v(_vm._s(item.wdzy)+\"...\")]),_vm._v(\" \"),_c('div',{staticClass:\"wz-btn\"},[_c('div',[_vm._v(\"共 \"+_vm._s(item.ys)+\" 页    类型:\\n                                \"),(item.wdlx == 1)?_c('span',[_vm._v(\"通用\")]):_vm._e(),_vm._v(\" \"),(item.wdlx == 2)?_c('span',[_vm._v(\"涉密信息系统集成资质\")]):_vm._e(),_vm._v(\" \"),(item.wdlx == 3)?_c('span',[_vm._v(\"武器装备科研生产单位资格\")]):_vm._e(),_vm._v(\" \"),(item.wdlx == 4)?_c('span',[_vm._v(\"国家秘密载体印刷资质\")]):_vm._e()])])]),_vm._v(\" \"),_c('div',{staticClass:\"andw\"},[_c('div',{staticClass:\"quickLook cursorClick\",on:{\"click\":function($event){return _vm.quickLook(item)}}},[_vm._v(\"快速预览\")])])])])}),0)]):_c('div',{staticClass:\"noDatas\"},[_vm._v(\"暂无数据\")])])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-b21c383c\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/bggl/bgglsy.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-b21c383c\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./bgglsy.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./bgglsy.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./bgglsy.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-b21c383c\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./bgglsy.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-b21c383c\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/bggl/bgglsy.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}