<template>
  <div class="content">
    <div
      ref="charts"
      style="width: calc(100vw * 0.427); height: calc(100vh * 0.479)"
    ></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getMaintenanceStatus } from "../../../../api/dpzs";
import hlj from "../../../assets/mapJson/230000.js";

export default {
  data() {
    return {
      dtList: []
    };
  },
  mounted() {
    this.getQxMap();
  },
  methods: {
    // 异步获取地图数据
    async getQxMap() {
      let data = await getMaintenanceStatus();
      console.log(data, "-------------");
      this.dtList = data.data.map(item => {

        item.value = item.count;
        item.maintenanceStatus = item.maintenanceStatus;
        item.code = item.code;
        if (item.name == '哈尔滨市') {
          item.longitude = 127.966759;
          item.latitude = 44.548633;
        } else if (item.name == '齐齐哈尔市') {
          item.longitude = 123.95;
          item.latitude = 47.63;
        } else if (item.name == '牡丹江市') {
          item.longitude = 130.58;
          item.latitude = 43.58;
        } else if (item.name == '佳木斯市') {
          item.longitude = 133.37;
          item.latitude = 46.82;
        } else if (item.name == '大庆市') {
          item.longitude = 124.03;
          item.latitude = 46.58;
        } else if (item.name == '鸡西市') {
          item.longitude = 132.97;
          item.latitude = 45;
        } else if (item.name == '鹤岗市') {
          item.longitude = 131.3;
          item.latitude = 47.33;
        } else if (item.name == '双鸭山市') {
          item.longitude = 133.15;
          item.latitude = 45.83;
        } else if (item.name == '伊春市') {
          item.longitude = 128.9;
          item.latitude = 47.73;
        } else if (item.name == '七台河市') {
          item.longitude = 131.0;
          item.latitude = 45.11;
        } else if (item.name == '黑河市') {
          item.longitude = 127.48;
          item.latitude = 49.25;
        } else if (item.name == '绥化市') {
          item.longitude = 126.98;
          item.latitude = 46.63;
        } else if (item.name == '大兴安岭地区') {
          item.longitude = 124.12;
          item.latitude = 52.72;
        }
        return item;
      });

      console.log(this.dtList);
      this.$nextTick(() => {
        this.initCharts();
      });
    },

    // async getQxMap() {
    //   // let params = {
    //   //   citycode: "230100"
    //   // };
    //   let data = await getMaintenanceStatus();
    //   console.log(data,"-------------");
    //   this.dtList = data.map(item => {
    //     item.value = item.count;
    //     return item;
    //   });
    //   // this.dtList = [
    //   //   {
    //   //     name: "哈尔滨",
    //   //     longitude: 127.966759,
    //   //     latitude: 44.548633,
    //   //     value: 100,
    //   //     code: "230100"
    //   //   },
    //   //   {
    //   //     name: "齐齐哈尔",
    //   //     longitude: 123.95,
    //   //     latitude: 47.63,
    //   //     value: 80,
    //   //     code: "230200"
    //   //   },
    //   //   {
    //   //     name: "牡丹江",
    //   //     longitude: 130.58,
    //   //     latitude: 43.58,
    //   //     value: 60,
    //   //     code: "231000"
    //   //   },
    //   //   {
    //   //     name: "佳木斯",
    //   //     longitude: 133.37,
    //   //     latitude: 46.82,
    //   //     value: 40,
    //   //     code: "230800"
    //   //   },
    //   //   {
    //   //     name: "大庆",
    //   //     longitude: 124.03,
    //   //     latitude: 46.58,
    //   //     value: 90,
    //   //     code: "230600"
    //   //   },
    //   //   {
    //   //     name: "鸡西",
    //   //     longitude: 132.97,
    //   //     latitude: 45,
    //   //     value: 50,
    //   //     code: "230300"
    //   //   },
    //   //   {
    //   //     name: "鹤岗",
    //   //     longitude: 131.3,
    //   //     latitude: 47.33,
    //   //     value: 30,
    //   //     code: "230400"
    //   //   },
    //   //   {
    //   //     name: "双鸭山",
    //   //     longitude: 133.15,
    //   //     latitude: 45.83,
    //   //     value: 20,
    //   //     code: "230500"
    //   //   },
    //   //   {
    //   //     name: "伊春",
    //   //     longitude: 128.9,
    //   //     latitude: 47.73,
    //   //     value: 70,
    //   //     code: "230700"
    //   //   },
    //   //   {
    //   //     name: "七台河",
    //   //     longitude: 131.0,
    //   //     latitude: 45.11,
    //   //     value: 35,
    //   //     code: "230900"
    //   //   },
    //   //   {
    //   //     name: "黑河",
    //   //     longitude: 127.48,
    //   //     latitude: 49.25,
    //   //     value: 25,
    //   //     code: "231100"
    //   //   },
    //   //   {
    //   //     name: "绥化",
    //   //     longitude: 126.98,
    //   //     latitude: 46.63,
    //   //     value: 55,
    //   //     code: "231200"
    //   //   },
    //   //   {
    //   //     name: "大兴安岭",
    //   //     longitude: 124.12,
    //   //     latitude: 52.72,
    //   //     value: 15,
    //   //     code: "232700"
    //   //   }
    //   // ];
    //   console.log(this.dtList);
    //   this.$nextTick(() => {
    //     this.initCharts();
    //   });
    // },

    initCharts() {
      const charts = echarts.init(this.$refs["charts"]);

      // 注册地图
      echarts.registerMap("uploadedDataURL", hlj);


      // 生成散点数据，根据地区数据设置颜色
      const scatterData = this.dtList
        .filter(item => item.maintenanceStatus !== 3) // 过滤掉 maintenanceStatus 为 3 的项
        .map(item => {
          return {
            name: item.name,
            value: [item.longitude, item.latitude, item.value], // 经纬度和值
            itemStyle: {
              color: item.maintenanceStatus !== 1 ? "red" : "green" // 根据值设置颜色
            }
          };
        });
        
      // // 生成散点数据，根据地区数据设置颜色
      // const scatterData = this.dtList.map(item => {
      //   return {
      //     name: item.name,
      //     value: [item.longitude, item.latitude, item.value], // 经纬度和值
      //     itemStyle: {
      //       color: item.maintenanceStatus !== 1 ? "red" : "green" // 根据值设置颜色
      //     }
      //   };
      // });

      const option = {
        geo: {
          map: "uploadedDataURL",
          roam: false,
          // zoom: 1.25, // 调整缩放级别
          // center: [126.7, 45.7], // 调整地图中心位置
          itemStyle: {
            normal: {
              areaColor: "#3894ec",
              borderColor: "#3fdaff",
              borderWidth: 2,
              shadowColor: "rgba(63, 218, 255, 0.5)",
              shadowBlur: 30
            },
            emphasis: {
              areaColor: "#2b91b7"
            }
          }
        },
        visualMap: {
          min: this.dtList[0].value,
          max: this.dtList[this.dtList.length - 1].value,
          left: "5%",
          text: ["高", "低"],
          textStyle: {
            color: "#fff"
          },
          realtime: false,
          calculable: true,
          inRange: {
            color: [
              "#052570",
              "#063B98",
              "#1760E4",
              "#0793FA",
              "#00BDFF",
              "#07DDF5"
            ]
          }
        },
        series: [
          {
            name: "黑龙江地图全览",
            type: "map",
            map: "uploadedDataURL",
            roam: false,
            zoom: 1.25,
            label: {
              normal: {
                show: true,
                color: "#fff",
                fontSize: 12
              },
              emphasis: {
                show: true,
                color: "#fff",
                fontSize: 12
              }
            },
            emphasis: {
              itemStyle: {
                areaColor: "#70EAF4",
                borderWidth: 1
              },
              label: {
                fontSize: 12,
                color: "#fff"
              }
            },
            itemStyle: {
              normal: {
                areaColor: "#3894ec",
                borderColor: "#3fdaff",
                borderWidth: 2,
                shadowColor: "rgba(63, 218, 255, 0.5)",
                shadowBlur: 30
              },
              emphasis: {
                areaColor: "#2b91b7",
                color: "#000",
                label: {
                  show: true
                }
              }
            },
            data: this.dtList
          },
          {
            name: "散点",
            type: "effectScatter",
            coordinateSystem: "geo",
            symbolSize: 4,
            itemStyle: {
              color: function(params) {
                return params.data.itemStyle.color;
              }
            },
            data: scatterData,
            rippleEffect: {
              brushType: "stroke",
              scale: 2.5,
              period: 4
            }
          }
        ]
      };

      charts.setOption(option);

      let clickName = "";
      let Num = 1;
      charts.on("click", params => {
        console.log("点击事件", params);
        if (params.name == clickName) {
          console.log("同一区域第二次点击", params.name);
          Num++;
          console.log("点击次数", Num);
        } else {
          clickName = params.name;
          Num = 1;
        }
        if (params.componentType == "series") {
          const Code = params.data.code;
          const Name = params.data.name;
          this.$emit("cityCodeSelected", Code, Name, Num);
        }
      });
    }
  }
};
</script>
