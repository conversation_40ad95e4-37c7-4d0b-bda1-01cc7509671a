<template>
  <div style="height: 100%;">
    <hsoft_top_title>
      <template #left>数据迁移配置文件生成</template>
    </hsoft_top_title>
    <!---->
    <div class="div-out">
      <!---->
      <div class="article">
        <!--左侧区域-->
        <div class="left out-div">
          <!---->
          <div>
            <p>数据来源文件位置</p>
            <div class="child-div">
              <div>
                <el-button type="primary" size="mini" @click="chooseFromFile">选择来源文件</el-button>
                <div>{{fromJsonPath}}</div>
              </div>
            </div>
          </div>
          <!--数据预览区域-->
          <div>
            <p>源文件数据</p>
            <pre>{{fromJsonObj}}</pre>
          </div>
          <!---->
        </div>
        <!--右侧区域-->
        <div class="right out-div">
          <!---->
          <div>
            <p>数据目标文件位置</p>
            <div class="child-div">
              <div>
                <el-button type="primary" size="mini" @click="chooseToFile">选择目标文件</el-button>
                <div>{{toJsonPath}}</div>
              </div>
            </div>
          </div>
          <!--数据预览区域-->
          <div>
            <p>目标文件数据预览</p>
            <pre>{{toJsonObj}}</pre>
          </div>
          <!---->
        </div>
      </div>
      <!--操作区域-->
      <div class="options-out">
        <!--表操作区域-->
        <div class="options-left">
          <div>
            <p>操作表</p>
            <div class="child-div">
              <div>
                <el-button type="primary" @click="showTableAddBtn" size="mini">新增表</el-button>
                <el-button type="primary" @click="showTableDeleteBtn" size="mini">删除表</el-button>
                <el-button type="primary" @click="showTableUpdateBtn" size="mini">修改表</el-button>
                <el-button type="primary" @click="showTableAddRecordBtn" size="mini">新增表数据</el-button>
                <el-button type="primary" @click="showTableDeleteRecordBtn" size="mini">删除表数据</el-button>
              </div>
            </div>
          </div>
          <!--字段操作区域-->
          <div>
            <p>操作字段</p>
            <div class="child-div">
              <div>
                <el-button type="primary" @click="showTableAddFieldBtn" size="mini">新增字段</el-button>
                <el-button type="primary" @click="showTableDeleteFieldBtn" size="mini">删除字段</el-button>
                <el-button type="primary" @click="showTableUpdateFieldBtn" size="mini">修改字段</el-button>
              </div>
            </div>
          </div>
          <!--即将执行操作区域-->
          <div class="div-future-options">
            <p>即将执行操作</p>
            <div class="child-div">
              <div v-for="(item,index) in optionsList" :key="index" class="future-options-div">
                <i class="el-icon-info"></i>
                <span>{{formatTime(item.time)}}</span>
                <span>{{item.option}}</span>
              </div>
            </div>
          </div>
          <!--确认执行操作区域-->
          <div>
            <div class="child-div">
              <div>
                <el-button type="success" @click="sureDataMigration" size="mini">确认迁移至目标文件</el-button>
                <el-button type="warning" @click="generateDataMigrationConfig" size="mini">生成配置文件</el-button>
                <el-button type="warning" @click="exeDataMigrationConfig" size="mini">读取配置文件并解析</el-button>
              </div>
            </div>
          </div>
        </div>
        <!---->
        <div class="options-right">
          <!--新增表-->
          <div v-show="controlFlags.showTableAddFlag" class="div-child">
            <p>新增表</p>
            <div class="div-select div-child-card">
              <span class="label-left-select">表名</span>
              <el-input v-model="tableAddObj.tableName" size="mini"></el-input>
            </div>
            <div style="height: calc(100% - 22px - 28px - 10px);">
              <el-table :data="tableAddObj.fieldList" height="calc(100% - 26px - 38px)">
                <el-table-column label="字段名">
                  <template slot-scope="scoped">
                    <el-input v-model="scoped.row.fieldName" size="mini" clearable></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="默认值">
                  <template slot-scope="scoped">
                    <el-input v-model="scoped.row.fieldDefaultValue" size="mini" clearable></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="60">
                  <template slot-scope="scoped">
                    <el-button type="text" @click="deleteTableRow(tableAddObj.fieldList,scoped.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-add" @click="addTableRow(tableAddObj.fieldList)">
                <i class="el-icon-plus"></i>
              </div>
              <div class="table-button-div">
                <el-button type="primary" @click="addTable" size="mini">确定</el-button>
                <el-button size="mini">取消</el-button>
              </div>
            </div>
          </div>
          <!--删除表-->
          <div v-show="controlFlags.showTableDeleteFlag" class="div-child">
            <p>删除表</p>
            <div style="height: calc(100% - 22px - 10px);">
              <el-table :data="toJsonTables" height="calc(100% - 38px)">
                <el-table-column prop="tableName" label="表名"></el-table-column>
                <el-table-column label="操作" align="center" width="60">
                  <template slot-scope="scoped">
                    <el-button type="text" @click="deleteTableCheck(scoped.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-button-div">
                <el-button type="primary" @click="deletaTable" size="mini">确定</el-button>
                <el-button size="mini">取消</el-button>
              </div>
            </div>
          </div>
          <!--修改表-->
          <div v-show="controlFlags.showTableUpdateFlag" class="div-child">
            <p>修改表</p>
            <div style="height: calc(100% - 22px - 10px);">
              <el-table :data="toJsonTables" height="calc(100% - 38px)">
                <el-table-column prop="tableName" label="原表名"></el-table-column>
                <el-table-column label="新表名">
                  <template slot-scope="scoped">
                    <el-input v-model="scoped.row.tableNameNew" size="mini" clearable></el-input>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-button-div">
                <el-button type="primary" @click="updateTable" size="mini">确定</el-button>
                <el-button size="mini">取消</el-button>
              </div>
            </div>
          </div>
          <!--新增表字段-->
          <div v-show="controlFlags.showTableAddFieldFlag" class="div-child">
            <p>新增表字段</p>
            <div class="div-select div-child-card">
              <span class="label-left-select">操作表</span>
              <el-select v-model="tableAddFieldObj.tableName" size="mini">
                <el-option v-for="(item,index) in toJsonTables" :key="index" :label="item.tableName" :value="item.tableName"></el-option>
              </el-select>
            </div>
            <div style="height: calc(100% - 22px - 28px - 10px);">
              <el-table :data="tableAddFieldObj.fieldList" height="calc(100% - 26px - 38px)">
                <el-table-column label="字段名">
                  <template slot-scope="scoped">
                    <el-input v-model="scoped.row.fieldName" size="mini" clearable></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="默认值">
                  <template slot-scope="scoped">
                    <el-input v-model="scoped.row.fieldDefaultValue" size="mini" clearable></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="60">
                  <template slot-scope="scoped">
                    <el-button type="text" @click="deleteTableAddField(scoped.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-add" @click="addTableRow(tableAddFieldObj.fieldList)">
                <i class="el-icon-plus"></i>
              </div>
              <div class="table-button-div">
                <el-button type="primary" @click="addTableFields" size="mini">确定</el-button>
                <el-button size="mini">取消</el-button>
              </div>
            </div>
          </div>
          <!--删除表字段-->
          <div v-show="controlFlags.showTableDeleteFieldFlag" class="div-child">
            <p>删除表字段</p>
            <div class="div-select div-child-card">
              <span class="label-left-select">操作表</span>
              <el-select v-model="tableDeleteFieldObj.tableName" @change="handDeleteFieldTable" size="mini">
                <el-option v-for="(item,index) in toJsonTables" :key="index" :label="item.tableName" :value="item.tableName"></el-option>
              </el-select>
            </div>
            <div style="height: calc(100% - 22px - 10px);">
              <el-table :data="tableDeleteFieldObj.fieldList" height="calc(100% - 26px - 38px)">
                <el-table-column prop="fieldName" label="字段名"></el-table-column>
                <el-table-column label="操作" align="center" width="60">
                  <template slot-scope="scoped">
                    <el-button type="text" @click="checkTableDeleteField(scoped.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-button-div">
                <el-button type="primary" @click="deleteTableFields" size="mini">确定</el-button>
                <el-button size="mini">取消</el-button>
              </div>
            </div>
          </div>
          <!--修改表字段-->
          <div v-show="controlFlags.showTableUpdateFieldFlag" class="div-child">
            <p>修改表字段</p>
            <div class="div-select div-child-card">
              <span class="label-left-select">操作表</span>
              <el-select v-model="tableUpdateFieldObj.tableName" @change="handUpdateFieldTable" size="mini">
                <el-option v-for="(item,index) in toJsonTables" :key="index" :label="item.tableName" :value="item.tableName"></el-option>
              </el-select>
            </div>
            <div style="height: calc(100% - 22px - 10px);">
              <el-table :data="tableUpdateFieldObj.fieldList" height="calc(100% - 26px - 38px)">
                <el-table-column prop="fieldName" label="字段名"></el-table-column>
                <el-table-column label="新字段名">
                  <template slot-scope="scoped">
                    <el-input type="text" v-model="scoped.row.fieldNameNew" size="mini"></el-input>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-button-div">
                <el-button type="primary" @click="updateTableFields" size="mini">确定</el-button>
                <el-button size="mini">取消</el-button>
              </div>
            </div>
          </div>
          <!--新增表数据-->
          <div v-show="controlFlags.showTableAddRecordFlag" class="div-child">
            <p>新增表数据</p>
            <div class="div-select div-child-card">
              <span class="label-left-select">操作表</span>
              <el-select v-model="tableAddRecordObj.tableName" size="mini">
                <el-option v-for="(item,index) in toJsonTables" :key="index" :label="item.tableName" :value="item.tableName"></el-option>
              </el-select>
            </div>
            <div style="height: calc(100% - 22px - 28px - 10px);">
              <el-table :data="tableAddRecordObj.fieldList" height="calc(100% - 26px - 38px)">
                <el-table-column label="字段名">
                  <template slot-scope="scoped">
                    <el-input v-model="scoped.row.fieldName" size="mini" clearable></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="默认值">
                  <template slot-scope="scoped">
                    <el-input v-model="scoped.row.fieldDefaultValue" size="mini" clearable></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="60">
                  <template slot-scope="scoped">
                    <el-button type="text" @click="deleteTableRow(tableAddRecordObj.fieldList,scoped.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-add" @click="addTableRow(tableAddRecordObj.fieldList)">
                <i class="el-icon-plus"></i>
              </div>
              <div class="table-button-div">
                <el-button type="primary" @click="addTableRecord" size="mini">确定</el-button>
                <el-button size="mini">取消</el-button>
              </div>
            </div>
          </div>
          <!--删除表数据-->
          <div v-show="controlFlags.showTableDeleteRecordFlag" class="div-child">
            <p>删除表数据</p>
            <div class="div-select div-child-card">
              <span class="label-left-select">操作表</span>
              <el-select v-model="tableDeleteRecordObj.tableName" size="mini">
                <el-option v-for="(item,index) in toJsonTables" :key="index" :label="item.tableName" :value="item.tableName"></el-option>
              </el-select>
            </div>
            <div style="height: calc(100% - 22px - 28px - 10px);">
              <span class="label-left-select">字段名</span>
              <el-input v-model="tableDeleteRecordObj.fieldName" size="mini"></el-input>
              <span class="label-left-select">字段值</span>
              <el-input v-model="tableDeleteRecordObj.fieldDefaultValue" size="mini"></el-input>
              <div class="table-button-div">
                <el-button type="primary" @click="deleteTableRecord" size="mini">确定</el-button>
                <el-button size="mini">取消</el-button>
              </div>
            </div>
          </div>
          <!---->
          <!---->
          <!---->
          <!---->
          <!---->
          <!---->
        </div>
        <!---->
      </div>
    </div>
    <!---->
  </div>
</template>

<script>
import hsoft_top_title from '../../../components/hsoft-top-title/hsoft-top-title.vue'

import { getWindowLocation } from '../../../../utils/windowLocation'

import { dateFormatChinese } from '../../../../utils/moment'

import { errorProcessor } from '../../../../utils/errorProcessor'

import { getBackFileSavePath, getFileDirectory, createDirectory, getFileNameByDirectory, getDataMigrationConfigPath, getDataMigrationConfigPathDev } from '../../../../utils/pathUtil'

import { dataMigration } from '../../../../utils/dataMigrationUtil'

const { dialog } = require('electron').remote
import FS from 'fs'

export default {
  data () {
    return {
      /**
       * 来源
      */
      // 来源json对象
      fromJsonObj: {},
      // 来源json对象路径
      fromJsonPath: '',
      /**
       * 目标
      */
      // 目标json对象
      toJsonObj: {},
      // 目标文件路径
      toJsonPath: '',
      // 目标json对象表名集合
      toJsonTables: [],
      /**
       * 新增表
      */
      // 新增表配置对象
      tableAddObj: {
        fieldList: [{}]
      },
      /**
       * 删除表
      */
      // 删除表配置集合
      tableDeleteList: [],
      /**
       * 修改表
      */
      // 修改表配置集合
      tableUpdateList: [],
      /**
       * 新增字段
      */
      // 新增字段配置对象
      tableAddFieldObj: {
        fieldList: [{}]
      },
      /**
       * 删除字段
      */
      // 删除字段配置对象
      tableDeleteFieldObj: {
        fieldList: [],
        deleteFieldList: []
      },
      /**
       * 修改字段
      */
      // 修改字段配置对象
      tableUpdateFieldObj: {
        fieldList: []
      },
      /**
       * 新增表数据
      */
      // 新增表数据配置对象
      tableAddRecordObj: {
        fieldList: []
      },
      /**
       * 删除表数据
      */
      // 删除表数据配置对象
      tableDeleteRecordObj: {},
      /**
       * 规则
      */
      // 规则集合
      roleList: [{}],
      /**
       * 右下角显隐控制
      */
      controlFlags: {
        showTableAddFlag: false,
        showTableDeleteFlag: false,
        showTableUpdateFlag: false,
        showTableAddFieldFlag: false,
        showTableDeleteFieldFlag: false,
        showTableUpdateFieldFlag: false,
        showTableAddRecordFlag: false,
        showTableDeleteRecordFlag: false
      },
      /**
       * 操作集合
      */
      optionsList: [],
      /**
       * 操作类型集合
      */
      optionsTypeObj: {
        addTableOption: 1,
        deleteTableOption: 2,
        updateTableOption: 3,
        addTableFieldOption: 4,
        deleteTableFieldOption: 5,
        updateTableFieldOption: 6,
        addTableRecordOption: 7,
        deleteTableRecordOption: 8,
      },
      /**
       * 配置文件数据集合
      */
      dataMigrationConfig: {
        options: []
      }
    }
  },
  components: {
    hsoft_top_title
  },
  methods: {
    // 时间格式化
    formatTime (time) {
      if (time) {
        return dateFormatChinese(new Date(time))
      }
      return time
    },
    /**
     * 字段相关
    */
    // 显示新增字段
    showTableAddFieldBtn () {
      // 清空
      this.tableAddFieldObj = {
        fieldList: [{}]
      }
      this.toJsonTables = []
      // 获取表名list
      let toJsonObj = this.toJsonObj
      if (toJsonObj) {
        console.log(Object.keys(toJsonObj))
        Object.keys(toJsonObj).forEach(item => {
          this.toJsonTables.push({
            tableName: item
          })
        })
        this.controllerFlag('showTableAddFieldFlag')
      }
    },
    // 新增字段
    addTableFields () {
      let toJsonObj = this.toJsonObj
      let tableAddFieldObj = this.tableAddFieldObj
      let tableName
      let fieldList
      if (tableAddFieldObj) {
        tableName = tableAddFieldObj.tableName
        fieldList = tableAddFieldObj.fieldList
      }
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      // 表名校验
      if (!tableName || tableName == '') {
        this.$message.warning('尚未选择需要操作的表')
        return
      }
      //
      toJsonObj[tableName].forEach((tableItem, tableIndex) => {
        console.log(tableItem)
        fieldList.forEach(item => {
          if (tableItem[item.fieldName]) {
            // 放入操作区域（支持操作回退）
            this.optionsList.push({
              time: new Date().getTime(),
              option: '新增表[' + tableName + ']第[' + (tableIndex + 1) + ']条记录字段[' + item.fieldName + ']失败，原表中已存在该字段，如需修改请使用修改字段',
              type: this.optionsTypeObj.addTableFieldOption,
              params: {
                tableName: tableName,
                fieldName: item.fieldName,
                fieldDefaultValue: item.fieldDefaultValue
              },
              isSuccess: false
            })
          } else {
            // 放入操作区域（支持操作回退）
            this.optionsList.push({
              time: new Date().getTime(),
              option: '新增表[' + tableName + ']第[' + (tableIndex + 1) + ']条记录字段[' + item.fieldName + ']',
              type: this.optionsTypeObj.addTableFieldOption,
              params: {
                tableName: tableName,
                fieldName: item.fieldName,
                fieldDefaultValue: item.fieldDefaultValue
              },
              isSuccess: true
            })
            if (!item.fieldDefaultValue) {
              tableItem[item.fieldName] = ''
            } else {
              tableItem[item.fieldName] = item.fieldDefaultValue
            }
          }
        })
      })
      // 放入配置文件数据集合中
      this.dataMigrationConfig.options.push({
        type: this.optionsTypeObj.addTableFieldOption,
        option: tableAddFieldObj
      })
      // 提示
      this.$message.success('表字段新增成功')
      // 清空
      this.tableAddFieldObj = {
        fieldList: [{}]
      }
      //
      this.controlFlags.showTableAddFieldFlag = false
    },
    // 显示删除字段
    showTableDeleteFieldBtn () {
      // 清空
      this.toJsonTables = []
      this.tableDeleteFieldObj = {
        fieldList: [],
        deleteFieldList: []
      }
      // 获取表名list
      let toJsonObj = this.toJsonObj
      if (toJsonObj) {
        console.log(Object.keys(toJsonObj))
        Object.keys(toJsonObj).forEach(item => {
          this.toJsonTables.push({
            tableName: item
          })
        })
        this.controllerFlag('showTableDeleteFieldFlag')
      }
    },
    // 删除字段下拉框改变事件
    handDeleteFieldTable (tableName) {
      let toJsonObj = this.toJsonObj
      // 清空
      this.tableDeleteFieldObj.fieldList = []
      this.tableDeleteFieldObj.deleteFieldList = []
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      let fieldArr = []
      toJsonObj[tableName].forEach(item => {
        Object.keys(item).forEach(key => {
          if (fieldArr.indexOf(key) == -1) {
            fieldArr.push(key)
            this.tableDeleteFieldObj.fieldList.push({
              fieldName: key
            })
          }
        })
      })
    },
    // 删除字段（添加到删除列表中）
    checkTableDeleteField (row) {
      this.tableDeleteFieldObj.deleteFieldList.push(row.fieldName)
      this.tableDeleteFieldObj.fieldList.splice(row, 1)
    },
    // 删除字段
    deleteTableFields () {
      let toJsonObj = this.toJsonObj
      let tableDeleteFieldObj = this.tableDeleteFieldObj
      let deleteFieldList
      let tableName
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      // 删除数据校验
      if (!tableDeleteFieldObj) {
        this.$message.warning('删除配置数据异常')
        return
      }
      deleteFieldList = tableDeleteFieldObj.deleteFieldList
      tableName = tableDeleteFieldObj.tableName
      // 准备删除字段
      if (toJsonObj[tableName]) {
        toJsonObj[tableName].forEach((item, index) => {
          Object.keys(item).forEach(key => {
            if (deleteFieldList.indexOf(key) != -1) {
              // 放入操作区域（支持操作回退）
              this.optionsList.push({
                time: new Date().getTime(),
                option: '删除表[' + tableName + ']第[' + (index + 1) + ']条记录字段[' + key + ']',
                type: this.optionsTypeObj.deleteTableFieldOption,
                params: {
                  tableName: tableName,
                  fieldName: key
                },
                isSuccess: true
              })
              Reflect.deleteProperty(item, key)
            }
          })
        })
      }
      // 放入配置文件数据集合中
      this.dataMigrationConfig.options.push({
        type: this.optionsTypeObj.deleteTableFieldOption,
        option: tableDeleteFieldObj
      })
      // 提示
      this.$message.success(deleteFieldList + '表字段删除成功')
      // 清空
      this.tableDeleteFieldObj = {
        fieldList: [],
        deleteFieldList: []
      }
      // 隐藏
      this.controlFlags.showTableDeleteFieldFlag = false
    },
    // 显示修改字段
    showTableUpdateFieldBtn () {
      // 清空
      this.toJsonTables = []
      this.tableUpdateFieldObj = {
        fieldList: []
      }
      // 获取表名list
      let toJsonObj = this.toJsonObj
      if (toJsonObj) {
        console.log(Object.keys(toJsonObj))
        Object.keys(toJsonObj).forEach(item => {
          this.toJsonTables.push({
            tableName: item
          })
        })
        this.controllerFlag('showTableUpdateFieldFlag')
      }
    },
    // 修改字段下拉框改变事件
    handUpdateFieldTable (tableName) {
      let toJsonObj = this.toJsonObj
      // 清空
      this.tableUpdateFieldObj.fieldList = []
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      let fieldArr = []
      toJsonObj[tableName].forEach(item => {
        Object.keys(item).forEach(key => {
          if (fieldArr.indexOf(key) == -1) {
            fieldArr.push(key)
            this.tableUpdateFieldObj.fieldList.push({
              fieldName: key
            })
          }
        })
      })
    },
    // 修改表字段
    updateTableFields () {
      let toJsonObj = this.toJsonObj
      let tableUpdateFieldObj = this.tableUpdateFieldObj
      let fieldList
      let tableName
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      // 修改数据校验
      if (!tableUpdateFieldObj) {
        this.$message.warning('修改配置数据异常')
        return
      }
      fieldList = tableUpdateFieldObj.fieldList
      tableName = tableUpdateFieldObj.tableName
      //
      toJsonObj[tableName].forEach((item, index) => {
        fieldList.forEach(field => {
          if (item[field.fieldName] && field.fieldNameNew && field.fieldNameNew != '') {
            // 放入操作区域（支持操作回退）
            this.optionsList.push({
              time: new Date().getTime(),
              option: '修改表[' + tableName + ']字段[' + field.fieldName + ']为[' + field.fieldNameNew + ']',
              type: this.optionsTypeObj.updateTableOption,
              params: {
                tableName: tableName,
                fieldName: field.fieldName,
                fieldNameNew: field.fieldNameNew
              }
            })
            item[field.fieldNameNew] = item[field.fieldName]
            Reflect.deleteProperty(item, field.fieldName)
          }
        })
      })
      // 放入配置文件数据集合中
      this.dataMigrationConfig.options.push({
        type: this.optionsTypeObj.updateTableFieldOption,
        option: tableUpdateFieldObj
      })
      // 提示成功
      this.$message.success('表[' + tableName + ']字段修改成功')
      // 清空数据并隐藏
      this.tableUpdateFieldObj = {
        fieldList: []
      }
      this.controlFlags.showTableUpdateFieldFlag = false
      //
      this.toJsonObj = JSON.parse(JSON.stringify(toJsonObj))
    },
    /**
     * 表相关
    */
    // 表格添加新行（公用）
    addTableRow (targetList) {
      targetList.push({})
    },
    // 表格删除行（公用）
    deleteTableRow (targetList, row) {
      targetList.splice(row, 1)
    },
    // 显示表修改
    showTableUpdateBtn () {
      // 清空数据
      this.toJsonTables = []
      // 获取表名list
      let toJsonObj = this.toJsonObj
      if (toJsonObj) {
        Object.keys(toJsonObj).forEach(item => {
          this.toJsonTables.push({
            tableName: item
          })
        })
        this.controllerFlag('showTableUpdateFlag')
      }
    },
    // 修改表
    updateTable () {
      let toJsonObj = this.toJsonObj
      let toJsonTables = this.toJsonTables
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      let mess = []
      toJsonTables.forEach(item => {
        if (toJsonObj[item.tableName] && item.tableNameNew && item.tableNameNew != '') {
          // 放入操作区域（支持操作回退）
          this.optionsList.push({
            time: new Date().getTime(),
            option: '修改表[' + item.tableName + ']为[' + item.tableNameNew + ']',
            type: this.optionsTypeObj.updateTableOption,
            params: item
          })
          toJsonObj[item.tableNameNew] = toJsonObj[item.tableName]
          Reflect.deleteProperty(toJsonObj, item.tableName)
          mess.push(item.tableName)
        }
      })
      // 放入配置文件数据集合中
      this.dataMigrationConfig.options.push({
        type: this.optionsTypeObj.updateTableOption,
        option: toJsonTables
      })
      // 提示成功
      this.$message.success(mess + '表修改成功')
      // 清空数据并隐藏
      this.toJsonTables = []
      this.controlFlags.showTableUpdateFlag = false
      //
      this.toJsonObj = JSON.parse(JSON.stringify(toJsonObj))
    },
    // 显示表删除
    showTableDeleteBtn () {
      // 清空数据
      this.tableDeleteList = []
      this.toJsonTables = []
      // 获取表名list
      let toJsonObj = this.toJsonObj
      if (toJsonObj) {
        Object.keys(toJsonObj).forEach(item => {
          this.toJsonTables.push({
            tableName: item
          })
        })
        this.controllerFlag('showTableDeleteFlag')
      }
    },
    // 删除表（添加到删除列表中）
    deleteTableCheck (row) {
      this.tableDeleteList.push(row.tableName)
      this.toJsonTables.splice(row, 1)
    },
    // 删除表
    deletaTable () {
      let toJsonObj = this.toJsonObj
      let tableDeleteList = this.tableDeleteList
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      tableDeleteList.forEach(item => {
        if (toJsonObj[item]) {
          // 放入操作区域（支持操作回退）
          this.optionsList.push({
            time: new Date().getTime(),
            option: '删除表[' + item + ']',
            type: this.optionsTypeObj.deleteTableOption,
            params: toJsonObj[item]
          })
          Reflect.deleteProperty(toJsonObj, item)
        }
      })
      // 放入配置文件数据集合中
      this.dataMigrationConfig.options.push({
        type: this.optionsTypeObj.deleteTableOption,
        option: tableDeleteList
      })
      // 提示成功
      this.$message.success(tableDeleteList + '表删除成功')
      // 清空数据并隐藏
      this.tableDeleteList = []
      this.toJsonTables = []
      this.controlFlags.showTableDeleteFlag = false
      //
      this.toJsonObj = JSON.parse(JSON.stringify(toJsonObj))
    },
    // 新增表数据(按钮点击事件)
    showTableAddRecordBtn () {
      // 清空
      this.tableAddRecordObj = {
        fieldList: [{}]
      }
      this.toJsonTables = []
      // 获取表名list
      let toJsonObj = this.toJsonObj
      if (toJsonObj) {
        console.log(Object.keys(toJsonObj))
        Object.keys(toJsonObj).forEach(item => {
          this.toJsonTables.push({
            tableName: item
          })
        })
        this.controllerFlag('showTableAddRecordFlag')
      }
    },
    // 新增表数据
    addTableRecord () {
      let toJsonObj = this.toJsonObj
      let tableAddRecordObj = this.tableAddRecordObj
      let tableName
      let fieldList
      if (tableAddRecordObj) {
        tableName = tableAddRecordObj.tableName
        fieldList = tableAddRecordObj.fieldList
      }
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      // 表名校验
      if (!tableName || tableName == '') {
        this.$message.warning('尚未选择需要操作的表')
        return
      }
      // 加工列表数据为对象数据
      let record = {}
      fieldList.forEach(item => {
        switch (item.fieldName) {
          case 'yhlx':
            record[item.fieldName] = parseInt(item.fieldDefaultValue)
            break
          case 'cjsj':
            record[item.fieldName] = parseInt(item.fieldDefaultValue)
            break
          case 'gxsj':
            record[item.fieldName] = parseInt(item.fieldDefaultValue)
            break
          default:
            record[item.fieldName] = item.fieldDefaultValue
            break
        }
      })
      //
      console.log(record,Object.keys(record),Object.keys(record).length)
      if(Object.keys(record).length > 0) {
        toJsonObj[tableName].push(record)
      }
      // 放入操作区域（支持操作回退）
      this.optionsList.push({
        time: new Date().getTime(),
        option: '新增表数据[' + tableName + ']',
        type: this.optionsTypeObj.addTableRecordOption,
        params: record,
        isSuccess: true
      })
      // toJsonObj[tableName].forEach((tableItem, tableIndex) => {
      //   console.log(tableItem)
      //   fieldList.forEach(item => {
      //     if (tableItem[item.fieldName]) {
      //       // 放入操作区域（支持操作回退）
      //       this.optionsList.push({
      //         time: new Date().getTime(),
      //         option: '新增表[' + tableName + ']第[' + (tableIndex + 1) + ']条记录字段[' + item.fieldName + ']失败，原表中已存在该字段，如需修改请使用修改字段',
      //         type: this.optionsTypeObj.addTableFieldOption,
      //         params: {
      //           tableName: tableName,
      //           fieldName: item.fieldName,
      //           fieldDefaultValue: item.fieldDefaultValue
      //         },
      //         isSuccess: false
      //       })
      //     } else {
      //       // 放入操作区域（支持操作回退）
      //       this.optionsList.push({
      //         time: new Date().getTime(),
      //         option: '新增表[' + tableName + ']第[' + (tableIndex + 1) + ']条记录字段[' + item.fieldName + ']',
      //         type: this.optionsTypeObj.addTableFieldOption,
      //         params: {
      //           tableName: tableName,
      //           fieldName: item.fieldName,
      //           fieldDefaultValue: item.fieldDefaultValue
      //         },
      //         isSuccess: true
      //       })
      //       if (!item.fieldDefaultValue) {
      //         tableItem[item.fieldName] = ''
      //       } else {
      //         tableItem[item.fieldName] = item.fieldDefaultValue
      //       }
      //     }
      //   })
      // })

      // 放入配置文件数据集合中
      this.dataMigrationConfig.options.push({
        type: this.optionsTypeObj.addTableRecordOption,
        option: tableAddRecordObj
      })
      // 提示
      this.$message.success('表数据新增成功')
      // 清空
      this.tableAddRecordObj = {
        fieldList: [{}]
      }
      //
      this.controlFlags.showTableAddRecordFlag = false
    },
    // 删除表数据(按钮点击事件)
    showTableDeleteRecordBtn () {
      // 清空
      this.tableAddRecordObj = {
        fieldList: [{}]
      }
      this.toJsonTables = []
      // 获取表名list
      let toJsonObj = this.toJsonObj
      if (toJsonObj) {
        console.log(Object.keys(toJsonObj))
        Object.keys(toJsonObj).forEach(item => {
          this.toJsonTables.push({
            tableName: item
          })
        })
        this.controllerFlag('showTableDeleteRecordFlag')
      }
    },
    // 删除表数据
    deleteTableRecord () {
      let toJsonObj = this.toJsonObj
      let tableDeleteRecordObj = this.tableDeleteRecordObj
      let tableName
      let fieldName
      let fieldDefaultValue
      if (tableDeleteRecordObj) {
        tableName = tableDeleteRecordObj.tableName
        fieldName = tableDeleteRecordObj.fieldName
        fieldDefaultValue = tableDeleteRecordObj.fieldDefaultValue
      }
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      // 校验
      if (!tableName || tableName == '') {
        this.$message.warning('尚未选择需要操作的表')
        return
      }
      if (!fieldName || fieldName == '') {
        this.$message.warning('尚未设置需要操作的表的字段名')
        return
      }
      if (!fieldDefaultValue || fieldDefaultValue == '') {
        this.$message.warning('尚未选择需要操作的表的字段值')
        return
      }
      let newTableList = toJsonObj[tableName].filter(item => {
        if (item[fieldName] != fieldDefaultValue) {
          return item
        }
      })
      // 覆盖原数据
      toJsonObj[tableName] = newTableList
      // 放入操作区域（支持操作回退）
      this.optionsList.push({
        time: new Date().getTime(),
        option: '删除表数据[' + tableName + ']',
        type: this.optionsTypeObj.deleteTableRecordOption,
        params: tableDeleteRecordObj,
        isSuccess: true
      })

      // 放入配置文件数据集合中
      this.dataMigrationConfig.options.push({
        type: this.optionsTypeObj.deleteTableRecordOption,
        option: tableDeleteRecordObj
      })
      // 提示
      this.$message.success('表数据删除成功')
      // 清空
      this.tableDeleteRecordObj = {}
      //
      this.controlFlags.showTableDeleteRecordFlag = false
    },
    // 删除新增表数据
    // 增加表（按钮点击事件）
    showTableAddBtn () {
      // 清空数据
      this.tableAddObj = {
        fieldList: [{}]
      }
      this.controllerFlag('showTableAddFlag')
    },
    // 右下角显隐控制
    controllerFlag (target) {
      Object.keys(this.controlFlags).forEach(item => {
        console.log(item, target, item == target)
        if (item == target) {
          this.controlFlags[item] = true
        } else {
          this.controlFlags[item] = false
        }
      })
    },
    // 增加表
    addTable () {
      let toJsonObj = this.toJsonObj
      let tableAddObj = this.tableAddObj
      console.log('增加表', tableAddObj)
      let tableName
      let fieldList
      if (tableAddObj) {
        tableName = tableAddObj.tableName
        fieldList = tableAddObj.fieldList
      }
      /**数据校验**/
      // 表名校验
      if (tableName === undefined || tableName == '') {
        this.$message.warning('新增表表名为空')
        return
      }
      // 目标文件校验
      if (!toJsonObj) {
        this.$message.warning('目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}')
        return
      }
      // 判断目标文件是否存在该表
      if (toJsonObj[tableName]) {
        // 提示已有表
        this.$message.warning('目标文件中已存在[' + tableName + ']表，如需更改表请使用修改表')
        return
      }
      // 插入表及字段
      let params = {}
      // 去除空的表字段配置后的配置
      let fieldTempList = []
      //
      fieldList.forEach(item => {
        if(Object.keys(item).length > 0) {
          params[item.fieldName] = item.fieldDefaultValue
          fieldTempList.push(params)
        }
      })
      // 重新赋值表字段配置
      tableAddObj.fieldList = fieldTempList
      console.log('fieldTempList', JSON.parse(JSON.stringify(fieldTempList)), JSON.parse(JSON.stringify(tableAddObj)))
      //
      let tableList = []
      console.log(params,Object.keys(params),Object.keys(params).length,Object.keys(params).length > 0)
      if(Object.keys(params).length > 0) {
        tableList.push(params)
      }
      toJsonObj[tableName] = tableList
      this.toJsonObj = JSON.parse(JSON.stringify(toJsonObj))
      // 放入操作区域（支持操作回退）
      this.optionsList.push({
        time: new Date().getTime(),
        option: '增加表[' + tableName + ']',
        type: this.optionsTypeObj.addTableOption,
        params: tableAddObj
      })
      // 放入配置文件数据集合中
      this.dataMigrationConfig.options.push({
        type: this.optionsTypeObj.addTableOption,
        option: tableAddObj
      })
      // 提示成功
      this.$message.success('[' + tableName + ']表增加成功')
      // 清空数据并隐藏
      this.tableAddObj = {
        fieldList: [{}]
      }
      this.controlFlags.showTableAddFlag = false
    },
    // 选择来源文件
    chooseFromFile () {
      let options = {
        title: "请选择迁移源文件",
        properties: ['openFile']
      }
      dialog.showOpenDialog(options, result => {
        console.log('来源文件', result)
        const utf8Str = FS.readFileSync(result[0], { encoding: 'utf8' })
        let utf8Json
        try {
          utf8Json = JSON.parse(utf8Str)
          this.fromJsonObj = utf8Json
          this.fromJsonPath = result[0]
        } catch (error) {
          console.log(error)
          this.$message.warning('所选文件不是一个json文件')
        }
      })
    },
    // 选择目标文件
    chooseToFile () {
      // 校验来源文件是否已成功导入
      if (Object.keys(this.fromJsonObj).length <= 0 && this.fromJsonPath == '') {
        this.$message.warning('数据来源文件未导入')
        return
      }
      if (Object.keys(this.fromJsonObj).length <= 0 && this.fromJsonPath != '') {
        this.$message.warning('数据来源文件数据为空，请正确导入数据来源文件（备注：来源文件不能为空）')
        return
      }
      //
      let options = {
        title: "请选择迁移目标文件",
        properties: ['openFile']
      }
      try {
        dialog.showOpenDialog(options, result => {
          console.log('目标文件', result)
          this.toJsonPath = result[0]
          // 拷贝来源数据给目标数据
          this.toJsonObj = JSON.parse(JSON.stringify(this.fromJsonObj))
          /**
           * 目标文件不进行解析，由来源文件决定目标文件
          */
          // const utf8Str = FS.readFileSync(result[0], { encoding: 'utf8' })
          // let utf8Json
          // try {
          //   utf8Json = JSON.parse(utf8Str)
          //   this.toJsonObj = utf8Json
          //   this.toJsonPath = result[0]
          // } catch (error) {
          //   console.log(error)
          //   this.$message.warning('所选目标文件不是一个json文件')
          // }
        })
      } catch (error) {
        error = errorProcessor(error)
        let errObj = JSON.parse(error.message)
        this.$notify({
          title: '系统异常',
          message: '[' + errObj.mark + ']\n',
          type: 'error',
          offset: 100,
          duration: 0
        })
        return
      }
    },
    // 确认数据迁移
    sureDataMigration () {
      // 数据校验
      if (this.fromJsonPath == '') {
        this.$message.warning('数据来源文件未导入')
        return
      }
      if (this.toJsonPath == '') {
        this.$message.warning('数据目标文件未选择')
        return
      }
      // 获取备份文件存储路径
      let backPath = getBackFileSavePath()
      // 加上一层json的文件夹
      backPath += 'json\\' + '备份数据库文件-' + new Date().getTime() + '-' + getFileNameByDirectory(this.fromJsonPath)
      // 路径系统适配
      backPath = getFileDirectory(backPath)
      // 创建路径（因为多加了一个json的文件夹）
      try {
        createDirectory(backPath)
      } catch (error) {
        error = errorProcessor(error)
        let errObj = JSON.parse(error.message)
        this.$notify({
          title: '系统异常',
          message: '[' + errObj.mark + ']\n',
          type: 'error',
          offset: 100,
          duration: 0
        })
        return
      }
      // 将来源文件进行备份
      console.log('backPath', backPath)
      try {
        FS.writeFileSync(backPath, JSON.stringify(this.fromJsonObj), { encoding: 'utf8' })
      } catch (error) {
        error = errorProcessor(error)
        let errObj = JSON.parse(error.message)
        this.$notify({
          title: '系统异常',
          message: '[' + errObj.mark + ']\n',
          type: 'error',
          offset: 100,
          duration: 0
        })
        return
      }
      this.$message.success('来源文件备份成功')
      // 覆盖目标文件内容
      try {
        FS.writeFileSync(this.toJsonPath, JSON.stringify(this.toJsonObj), { encoding: 'utf8' })
      } catch (error) {
        error = errorProcessor(error)
        let errObj = JSON.parse(error.message)
        this.$notify({
          title: '系统异常',
          message: '[' + errObj.mark + ']\n',
          type: 'error',
          offset: 100,
          duration: 0
        })
        return
      }
      this.$message.success('目标文件生成成功')
      // 提示重启生效
      this.$confirm('数据库文件迁移成功，是否立即重启？', '重要提示', {
        confirmButtonText: '立即重启',
        cancelButtonText: '稍后自行重启',
        type: 'warning'
      }).then(() => {
        this.$electron.ipcRenderer.send("reset-window")
      }).catch(() => {
        this.$notify({
          title: '系统提示',
          message: '数据库文件内容变动，请重启程序以生效',
          type: 'warning',
          offset: 100,
          duration: 0
        })
      })
    },
    // 生成配置文件，配置文件位置在根目录下dataMigaration文件夹下
    generateDataMigrationConfig () {
      let savePath = getDataMigrationConfigPathDev()
      this.dataMigrationConfig.isExec = false
      FS.writeFileSync(savePath, JSON.stringify(this.dataMigrationConfig), { encoding: 'utf8' })
    },
    /**
     * 读取文件并解析迁移
    */
    exeDataMigrationConfig () {
      let resObj = dataMigration()
      if (resObj.code == 200) {
        // 提示重启生效
        this.$confirm('数据库文件迁移成功，是否立即重启？', '重要提示', {
          confirmButtonText: '立即重启',
          cancelButtonText: '稍后自行重启',
          type: 'warning'
        }).then(() => {
          this.$electron.ipcRenderer.send("reset-window")
        }).catch(() => {
          this.$notify({
            title: '系统提示',
            message: '数据库文件内容变动，请重启程序以生效',
            type: 'warning',
            offset: 100,
            duration: 0
          })
        })
      } else if (resObj.code == 300) {
        // 当前迁移配置文件已迁移完成
      } else {
        this.$notify({
          title: '系统异常',
          message: resObj.message,
          type: 'error',
          offset: 100,
          duration: 0
        })
        return
      }
    }
  },
  mounted () { }
}
</script>

<style scoped>
.div-out {
  height: calc(100% - 32px);
  /* overflow-y: scroll; */
}
.out-card {
  /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
}
/**内容区域**/
.article {
  display: flex;
}
.article .out-div {
  flex: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
  padding: 10px 5px;
}
.article .out-div .child-div {
  padding: 5px;
}
.article .out-div .child-div > div {
  display: flex;
}
.article .out-div .child-div > div > div {
  flex: 1;
  padding-left: 1em;
  box-sizing: border-box;
  font-size: 12px;
  display: flex;
  align-items: center;
}
/****/
.div-select {
  margin-bottom: 5px;
}
.div-select .label-left-select {
  font-size: 14px;
  text-align: right;
  width: 5em;
  display: inline-block;
  line-height: 28px;
}
.div-child {
  /* background: whitesmoke; */
  /* padding: 10px; */
  height: calc(100%);
}
.div-child .div-child-card {
  display: flex;
}
.div-child .div-child-card > span {
  margin-right: 10px;
}
/**表格下方增加按钮区域样式**/
.table-add {
  background: white;
  text-align: center;
  padding: 2px 0;
}
.table-add:hover {
  /* background: #e8e8e8; */
  color: #409eff;
  cursor: pointer;
}
/**表格下方按钮样式**/
.table-button-div {
  text-align: right;
  padding: 5px;
  background: white;
  margin-top: 5px;
}
/**左侧区域**/
.article .left {
  margin-right: 1em;
  width: 50%;
}
/**pre样式**/
pre {
  height: 278px;
  overflow-y: scroll;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
/**操作区域**/
.options-out {
  display: flex;
  height: calc(100% - 380px - 25px);
  margin-top: 10px;
}
.options-left {
  flex: 1;
  height: calc(100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
  padding: 10px 5px;
  margin-right: 1em;
}
.options-right {
  flex: 1;
  height: calc(100%);
  overflow-y: scroll;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
  padding: 10px 5px;
}
/**即将执行的操作区域**/
.div-future-options {
  padding: 5px 0;
  height: calc(100% - 58px - 58px - 28px);
  overflow-y: scroll;
}
.div-future-options .future-options-div {
  font-size: 12px;
  border-bottom: 1px dotted #dcdfe6;
  padding: 2px;
}
.div-future-options .future-options-div span {
  margin-right: 5px;
  color: #909399;
}
.div-future-options .future-options-div i {
  cursor: pointer;
  color: #e6a23c;
  font-size: 14px;
}
</style>