<template>
  <div class="sec-container" v-loading="loading">
    <!-- 标题 -->
    <p class="sec-title">基本信息</p>
    <div class="sec-form-container">
      <el-form ref="formName" :model="tjlist" label-width="225px">
        <!-- 第一部分包括姓名到常住地公安start -->
        <div class="sec-header-section">
          <div class="sec-form-left">
            <el-form-item label="姓名">
              <el-input placeholder="" v-model="tjlist.xm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="性别">
              <template slot-scope="scope">
                <el-input placeholder="" v-model="tjlist.xb" clearable disabled></el-input>
              </template>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="部门">
              <el-input placeholder="" v-model="tjlist.bmmc" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="职务（职称）">
              <el-input placeholder="" v-model="tjlist.zw" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="进入公司日期">
              <el-input placeholder="" v-model="tjlist.rzsj" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="入涉密岗位日期">
              <el-input placeholder="" v-model="tjlist.sgsj" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="身份证号">
              <el-input placeholder="" v-model="tjlist.sfzhm" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="联系电话">
              <el-input placeholder="" v-model="tjlist.lxdh" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="原涉密岗位">
              <el-input placeholder="" v-model="tjlist.gwmc" clearable disabled></el-input>
            </el-form-item>
            <el-form-item label="原涉密等级">
              <el-input placeholder="" v-model="tjlist.smdj" clearable disabled></el-input>
            </el-form-item>
          </div>
          <div class="sec-form-left">
            <el-form-item label="变更后岗位名称" prop="bgsmgw" class="one-line">
              <el-select v-model="tjlist.bgsmgw" placeholder="请选择变更后岗位名称" style="width:100%;" multiple
                @change="handleSelectBghgwmc(tjlist, $event)">
                <el-option v-for="(item, label) in gwmclist" :label="item.gwmc" :value="item.gwmc" :key="label">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="变更后涉密等级" prop="bgsmdj" class="one-line">
              <el-select v-model="tjlist.bgsmdj" placeholder="请选择变更后涉密等级" style="width:100%;" @change="xzsmdj">
                <el-option v-for="item in smdjxz" :label="item.mc" :value="item.id" :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <p class="sec-title">备注：本表只适用于涉密人员由高涉密等级调整到低涉密等级</p>
        </div>

        <!-- 底部操作按钮start -->
        <div class="sec-form-six haveBorderTop sec-footer">
          <el-button @click="returnIndex" class="fr ml10" plain>返回</el-button>
          <el-button @click="chooseApproval" class="fr" type="success">保存并提交</el-button>
          <!-- <el-button @click="saveAndSubmit" class="fr" type="success">保存并提交</el-button>
          <el-button @click="save" class="fr" type="primary">保存</el-button> -->
          <el-button @click="save" class="fr" type="primary">临时保存</el-button>
        </div>
        <!-- 底部操作按钮end -->

      </el-form>
    </div>
    <!-- 发起申请弹框start -->
    <el-dialog title="选择审批人" :close-on-click-modal="false" :visible.sync="approvalDialogVisible" width="40%" :destroy-on-close="true">
      <div class="dlFqsqContainer">
        <label for="">部门:</label>
        <el-cascader v-model="ryChoose.bm" :options="regionOption" :props="regionParams" filterable clearable
          ref="cascaderArr" @change="bmSelectChange"></el-cascader>
        <label for="">姓名:</label>
        <el-input class="input2" v-model="ryChoose.xm" clearable placeholder="姓名"></el-input>
        <el-button class="searchButton" type="primary" icon="el-icon-search" @click="searchRy">查询</el-button>
        <BaseTable class="baseTable" :tableHeight="'300'" :key="tableKey" :showIndex=true :tableData="ryDatas" :columns="applyColumns"
          :showSingleSelection="true" :handleColumn="handleColumnApply" :showPagination=true :currentPage="page"
          :pageSize="pageSize" :totalCount="total" @handleCurrentChange="handleCurrentChangeRy"
          @handleSizeChange="handleSizeChangeRy" @handleSelectionChange="handleSelectionChange">
        </BaseTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" class="fr ml10" @click="approvalDialogVisible = false">关 闭</el-button>
        <el-button @click="saveAndSubmit" class="fr" type="success">提交</el-button>
        <!-- <el-button @click="save" class="fr" type="primary">保存</el-button> -->
        <div style="clear:both"></div>
      </span>
    </el-dialog>
    <!-- 发起申请弹框end -->
  </div>
</template>
<script>
import {
  getLcSLid,
  updateZgfs,
  updateSlzt,
  getZzjgList,
  getSpUserList,
  getCurZgfsjl,
  getFwdyidByFwlx,
  getLoginInfo,
  deleteSlxxBySlid
} from '../../../api/index'
import {
  getDjgwbgInfo,
  submitDjgwbg,
  getDjgwbgInfoByLcsllid,
  updateDjgwbg
} from '../../../api/djgwbg'
import { getAllGwxx } from '../../../api/qblist'
import { getAllSmdj } from '../../../api/xlxz'
import BaseTable from '../../components/common/baseTable.vue'
import AddLineTable from "../../components/common/addLineTable.vue"; //人工纠错组件
export default {
  components: {
    AddLineTable,
    BaseTable
  },
  props: {},
  data() {
    return {
      tableKey:1,
      value1: '',
      loading: false,
      // 弹框人员选择条件
      ryChoose: {
        'bm': '',
        'xm': ''
      },
      gwmclist: [],
      smdjxz: [],
      regionOption: [], // 部门下拉
      page: 1, // 审批人弹框当前页
      pageSize: 10, // 审批人弹框每页条数
      radioIdSelect: '', // 审批人弹框人员单选
      ryDatas: [], // 弹框人员选择
      total: 0, // 弹框人员总数
      regionParams: {
        label: 'label', //这里可以配置你们后端返回的属性
        value: 'label',
        children: 'childrenRegionVo',
        expandTrigger: 'click',
        checkStrictly: true
      }, //地域信息配置参数
      // table 行样式
      headerCellStyle: {
        background: '#EEF7FF',
        color: '#4D91F8'
      },
      // form表单提交数据
      tjlist: {
        smryid: '',
        xm: '',
        xb: '',
        gj: '中国',
        dwzwzc: '',
        yrsmgw: '',
        cym: '',
        mz: '',
        hyzk: '',
        zzmm: '',
        lxdh: '',
        sfzhm: '',
        hjdz: '',
        hjdgajg: '',
        czdz: '',
        czgajg: '',
        imageUrl: '',
        yjqk: '0', // 拥有外籍、境外永久居留权或者长期居留许可情况
        qscfqk: '0', // 配偶子女有关情况
        qtqk: '', // 其他需要说明的情况
        brcn: '',
        smdj: '',
        bgsmdj: '',
      },
      // 主要学习及工作经历
      ryglRyscScjlList: [{
        'qssj': '',
        'zzsj': '',
        'szdw': '',
        'zw': '',
        'zmr': '',
        'czbtn1': '增加行',
        'czbtn2': '',
      }],
      // 家庭成员及社会关系
      ryglRyscJtcyList: [{
        "gxms": "", //关系描述
        "zzmm": "", //政治面貌
        "jwjlqk": '', //是否有外籍、境外居留权、长期居留许可
        "xm": "", //姓名
        "cgszd": "", //工作(学习)单位
        "zw": "", //职务
        'czbtn1': '增加行',
        'czbtn2': ''
      }],
      // 因私出国(境)情况
      ryglRyscYccgList: [{
        "cggj": "", //出国国家
        "sy": "", //事由
        "zzsj": "", //终止时间
        "qssj": "", //起始时间
        // "bz": "",//备注
        'czbtn1': '增加行',
        'czbtn2': ''
      }],
      // 接受境外资助情况
      ryglRyscJwzzqkList: [{
        "zzsj": "", //时间
        "jgmc": "", //机构名称
        // "bz": "",//备注
        "zznr": "", //资助内容
        "gj": "", //国家
        'czbtn1': '增加行',
        'czbtn2': ''
      }],
      // 处分或者违法犯罪情况
      ryglRyscCfjlList: [{
        "cfdw": "", //处罚单位
        "cfsj": "", //处罚时间
        "cfjg": "", //处罚结果
        "cfyy": "", //处罚原因
        'czbtn1': '增加行',
        'czbtn2': '',
      }],
      // 持有因公出入境证件情况
      ryglRyscSwzjList: [{
        'zjmc': '涉密载体（含纸质、光盘等）',
        'fjlb': 1,
        'cyqk': '0',
        'zjhm': '',
        'yxq': '',
        'qzmc': '部门保密员核定签字：'
      }, {
        'zjmc': '信息设备（含计算机、存储介质等）',
        'fjlb': 2,
        'cyqk': '0',
        'zjhm': '',
        'yxq': '',
        'qzmc': '部门保密员核定签字：'
      }, {
        'zjmc': '涉密信息系统访问权限回收情况',
        'fjlb': 3,
        'cyqk': '0',
        'zjhm': '',
        'yxq': '',
        'qzmc': '系统管理员(三员)核定签字：'
      }, {
        'zjmc': '涉密场所出入权限回收情况',
        'fjlb': 4,
        'cyqk': '0',
        'zjhm': '',
        'yxq': '',
        'qzmc': '涉密场所管理员核定签字：  '
      }],
      ryInfo: {},
      // 政治面貌下拉选项
      zzmmoptions: [{
        value: '中央党员',
        label: '中央党员'
      }, {
        value: '团员',
        label: '团员'
      }, {
        value: '民主党派',
        label: '民主党派'
      }, {
        value: '群众',
        label: '群众'
      }],
      ynoptions: [{
        value: '1',
        label: '是'
      }, {
        value: '0',
        label: '否'
      }],
      sltshow: '', // 文档的缩略图显示
      routeType: '',
      pdfBase64: '',
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      approvalDialogVisible: false, // 选择申请人弹框
      fileRow: '',
      // 选择审核人table
      applyColumns: [{
        name: '姓名',
        prop: 'xm',
        scopeType: 'text',
        formatter: false
      },
      {
        name: '部门',
        prop: 'bmmc',
        scopeType: 'text',
        formatter: false
      },
      {
        name: '岗位',
        prop: 'gwmc',
        scopeType: 'text',
        formatter: false
      }
      ],
      handleColumnApply: [],
      scqk: [
        {
          sfty: '同意',
          id: 1
        },
        {
          sfty: '不同意',
          id: 0
        },
      ],
      disabled2: false,
      ysmdj: '',
    }
  },
  computed: {
    // selectedLabel() {
    //   const option = this.ynoptions.find(o => o.value === this.selectedValue);
    //   return option ? option.label : '';
    // }
  },
  mounted() {
    this.onfwid()
    this.smdj()
    this.gwxx()
    this.getOrganization()
    this.yhDatas = this.$route.query.datas
    // console.log('this.radioIdSelect', this.yhDatas);
    // this.ryInfo = this.$route.query.datas.gwbgscb
    this.routeType = this.$route.query.type
    this.routezt = this.$route.query.zt
    console.log(this.routezt);
    let result = {}
    let iamgeBase64 = ''
    let iamgeBase64Brcn = ''
    if (this.$route.query.type == 'add') {
      // 首次发起申请
      result = {
        ...this.tjlist,
        ...this.$route.query.datas
      }
    } else {
      // 保存 继续编辑
      result = {
        ...this.tjlist,
        ...this.$route.query.datas
      }
    }
    this.tjlist = result
    if (this.tjlist.smdj == 1) {
      this.tjlist.smdj = '核心'
    } else if (this.tjlist.smdj == 2) {
      this.tjlist.smdj = '重要'
    } else if (this.tjlist.smdj == 3) {
      this.tjlist.smdj = '一般'
    }
    if (this.tjlist.smdj == '核心') {
      this.ysmdj = 1
    } else if (this.tjlist.smdj == '重要') {
      this.ysmdj = 2
    } else if (this.tjlist.smdj == '一般') {
      this.ysmdj = 3
    }
    if (this.tjlist.xb == 1) {
      this.tjlist.xb = '男'
    } else if (this.tjlist.xb == 2) {
      this.tjlist.xb = '女'
    }
    console.log('this.tjlist', this.tjlist);
    this.tjlist.yjqk = result.yjqk.toString()
    this.tjlist.qscfqk = result.qscfqk.toString()
    console.log(this.tjlist);
    if (typeof iamgeBase64 === "string") {
      // 复制某条消息
      if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
      function validDataUrl(s) {
        return validDataUrl.regex.test(s);
      }
      validDataUrl.regex =
        /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
      if (validDataUrl(iamgeBase64)) {
        let that = this;

        function previwImg(item) {
          that.tjlist.imageUrl = item;
        }
        previwImg(iamgeBase64);
      }
    }

  },
  methods: {
    chRadio() { },
    async gwxx() {
      let param = {
        bmmc: this.tjlist.bmmc
      }
      let data = await getAllGwxx(param)
      this.gwmclist = data
      console.log(data);
    },
    //获取涉密等级信息
    async smdj() {
      let data = await getAllSmdj()
      this.smdjxz = data
    },
    xzsmdj() {
      console.log(this.ysmdj);
      console.log(this.tjlist.bgsmdj);
      if (this.ysmdj >= this.tjlist.bgsmdj) {
        this.$message.warning('本表只适用于涉密人员由高涉密等级调整到低涉密等级!')
      }
    },
    handleSelectBghgwmc(item, i) {
      console.log(i);
      this.gwmclist.forEach(item1 => {
        if (i == item1.gwmc) {
          console.log(item1);
          this.tjlist.bgsmdj = item1.smdj
          if (this.ysmdj >= this.tjlist.bgsmdj) {
            this.$message.warning('本表只适用于涉密人员由高涉密等级调整到低涉密等级!')
          }
        } else if (i.length == 0) {
          this.tjlist.bgsmdj = ''
        }
      })
    },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    handleSelectionChange(index, row) {
      this.radioIdSelect = row
    },
    // 主要学习及工作经历增加行
    addRow(data) {
      data.push({
        'qssj': '',
        'zzsj': '',
        'szdw': '',
        'zw': '',
        'zmr': '',
        'czbtn1': '增加行',
        'czbtn2': '删除',
      })
    },
    // 主要学习及工作经历删除行
    delRow(index, rows) {
      rows.splice(index, 1)
    },
    // 家庭成员及主要社会关系情况 添加行
    cyjshgxAddRow(data) {
      data.push({
        'ybrgx': '',
        'xm': '',
        'sfywjjwjlqcqjlxk': '',
        'dw': '',
        'zw': '',
        'zzmm': '',
        'czbtn1': '增加行',
        'czbtn2': '删除'
      })
    },
    // 家庭成员及主要社会关系情况 删除行
    cyjshgxDelRow(index, rows) {
      rows.splice(index, 1)
    },
    // 因私出国(境)情况 添加行
    yscgqkAddRow(data) {
      data.push({
        'qsrq': '',
        'zzrq': '',
        'jsnsdgjhdq': '',
        'sy': '',
        'czbtn1': '增加行',
        'czbtn2': '删除'
      })
    },
    // 因私出国(境)情况 删除行
    yscgqkDelRow(index, rows) {
      rows.splice(index, 1)
    },
    // 接受境外资助情况 添加行
    jsjwzzqkAddRow(data) {
      data.push({
        'qsrq': '',
        'gjdq': '',
        'jgmc': '',
        'zznr': '',
        'czbtn1': '增加行',
        'czbtn2': '删除'
      })
    },
    // 接受境外资助情况 删除行
    jsjwzzqkDelRow(index, rows) {
      rows.splice(index, 1)
    },
    // 处分或者违法犯罪情况 添加行
    clhwffzqkAddRow(data) {
      data.push({
        'qsrq': '',
        'cljg': '',
        'clyy': '',
        'cljg': '',
        'czbtn1': '增加行',
        'czbtn2': '删除'
      })
    },
    // 处分或者违法犯罪情况 删除行
    clhwffzqkDelRow(index, rows) {
      rows.splice(index, 1)
    },
    // 上传本人承诺凭证
    httpRequest(data) {
      this.sltshow = URL.createObjectURL(data.file);
      this.fileRow = data.file
      // this.tjlist.brcn = URL.createObjectURL(this.fileRow)
      this.blobToBase64(data.file, (dataurl) => {
        this.tjlist.brcn = dataurl.split(',')[1]
      });
    },
    // 预览
    yulan() {
      console.log(this.routeType)
      if (this.routeType == 'add') {
        this.dialogImageUrl = URL.createObjectURL(this.fileRow)
      } else {
        this.dialogImageUrl = this.sltshow
      }
      this.dialogVisible = true
    },
    // 删除
    shanchu() {
      this.tjlist.brcn = ''
      this.sltshow = ''
    },
    async onfwid() {
      let params = {
        fwlx: 3
      }
      let data = await getFwdyidByFwlx(params)
      console.log(data);
      this.fwdyid = data.data.fwdyid
    },
    // 保存
    async save() {
      if (this.ysmdj >= this.tjlist.bgsmdj) {
        this.$message.warning('本表只适用于涉密人员由高涉密等级调整到低涉密等级!')
      } else {
        if (this.tjlist.bgsmgw.length != 0) {
          let param = {
            'fwdyid': this.fwdyid,
            'lcslclzt': 3
          }
          this.ryglRyscJtcyList.forEach((e) => {
            if (e.jwjlqk == '否') {
              e.jwjlqk = 0
            } else if (e.jwjlqk == '是') {
              e.jwjlqk = 1
            }
          })

          param.smryid = this.yhDatas.smryid
          this.tjlist.dwid = this.yhDatas.dwid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.lcslid = res.data.slid
            if (this.tjlist.xb == '男') {
              this.tjlist.xb = 1
            } else if (this.tjlist.xb == '女') {
              this.tjlist.xb = 2
            }
            if (this.tjlist.smdj == '核心') {
              this.tjlist.ysmdj = 1
            } else if (this.tjlist.smdj == '重要') {
              this.tjlist.ysmdj = 2
            } else if (this.tjlist.smdj == '一般') {
              this.tjlist.ysmdj = 3
            }
            console.log(this.tjlist.lcslid, 'this.tjlist.lcslid');
            let params = this.tjlist
            let resDatas = await submitDjgwbg(params)
            if (resDatas.code == 10000) {
              this.$router.push('/gwbgscb')
              this.$message({
                message: '保存成功',
                type: 'success'
              })
            }else {
          deleteSlxxBySlid({ slid: res.data.slid })
        }
          }
        } else {
          this.$message.warning('请选择变更后岗位名称！')
        }
      }
    },
    //全部组织机构List
    async getOrganization() {
      let zzjgList = await getZzjgList()
      this.zzjgmc = zzjgList
      let shu = []
      this.zzjgmc.forEach(item => {
        let childrenRegionVo = []
        this.zzjgmc.forEach(item1 => {
          if (item.bmm == item1.fbmm) {
            childrenRegionVo.push(item1)
            item.childrenRegionVo = childrenRegionVo
          }
        });
        shu.push(item)
      })
      let shuList = []
      let list = await getLoginInfo()
      if (list.fbmm == '') {
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
      }
      if (list.fbmm != '') {
        shu.forEach(item => {
          console.log(item);
          if (item.fbmm == list.fbmm) {
            shuList.push(item)
          }
        })
      }
      shuList[0].childrenRegionVo.forEach(item => {
        this.regionOption.push(item)
      })
    },
    handleSelectionChange1(index, row) {
      this.radioIdSelect = row
    },
    handleCurrentChangeRy(val) {
      this.page = val
      this.chooseApproval()
    },
    //列表分页--更改每页显示个数
    handleSizeChangeRy(val) {
      this.page = 1
      this.pageSize = val
      this.chooseApproval()
    },
    // 人员搜索
    searchRy() {
      this.tableKey++
      this.chooseApproval()
    },
    // 发起申请选择人员 人员下拉
    bmSelectChange(item) {
      if (item != undefined) {
        this.ryChoose.bm = item.join('/')
      }
    },
    // 选择审批人
    async chooseApproval() {
      if (this.ysmdj >= this.tjlist.bgsmdj) {
        this.$message.warning('本表只适用于涉密人员由高涉密等级调整到低涉密等级!')
      } else {
        if (this.tjlist.bgsmgw.length != 0) {
          
          this.approvalDialogVisible = true
          let param = {
            'page': this.page,
            'pageSize': this.pageSize,
            'fwdyid': this.fwdyid,
            'bmmc': this.ryChoose.bm,
            'xm': this.ryChoose.xm
          }
          let resData = await getSpUserList(param)
          if (resData.records) {
            // this.loading = false
            this.ryDatas = resData.records
            this.total = resData.total
          } else {
            this.$message.error('数据获取失败！')
          }
        } else {
          this.$message.warning('请选择变更后岗位名称！')
        }
      }
    },
    // 保存并提交
    async saveAndSubmit() {
      if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {
        let param = {
          'fwdyid': this.fwdyid
        }
        this.ryglRyscJtcyList.forEach((e) => {
          if (e.jwjlqk == '否') {
            e.jwjlqk = 0
          } else if (e.jwjlqk == '是') {
            e.jwjlqk = 1
          }
        })
        if (this.tjlist.xb == '男') {
          this.tjlist.xb = 1
        } else if (this.tjlist.xb == '女') {
          this.tjlist.xb = 2
        }
        if (this.tjlist.smdj == '核心') {
          this.tjlist.smdj = 1
        } else if (this.tjlist.smdj == '重要') {
          this.tjlist.smdj = 2
        } else if (this.tjlist.smdj == '一般') {
          this.tjlist.smdj = 3
        }
        // this.tjlist.dwid = this.ryInfo.dwid
        // this.tjlist.lcslid = this.ryInfo.lcslid
        if (this.routeType == 'update' && this.routezt == undefined) {
          param.lcslclzt = 2
          param.smryid = this.tjlist.smryid
          param.slid = this.tjlist.lcslid
          param.clrid = this.radioIdSelect.yhid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            let params = this.tjlist
            let resDatas = await updateDjgwbg(params)
            if (resDatas.code == 10000) {
              let paramStatus = {
                'fwdyid': this.fwdyid,
                'slid': this.tjlist.lcslid
              }
              let resStatus
              resStatus = await updateSlzt(paramStatus)
              if (resStatus.code == 10000) {
                this.$router.push('/gwbgscb')
                this.$message({
                  message: '保存并提交成功',
                  type: 'success'
                })
              }
            }
          }
        } else {
          param.lcslclzt = 0
          param.clrid = this.radioIdSelect.yhid
          param.smryid = this.yhDatas.smryid
          let res = await getLcSLid(param)
          if (res.code == 10000) {
            this.tjlist.dwid = this.yhDatas.dwid
            this.tjlist.lcslid = res.data.slid


            let params = this.tjlist
            let resDatas = await submitDjgwbg(params)
            if (resDatas.code == 10000) {
              this.$router.push('/gwbgscb')
              this.$message({
                message: '保存并提交成功',
                type: 'success'
              })
            }else {
          deleteSlxxBySlid({ slid: res.data.slid })
        }
          }
        }
      } else {
        this.$message({
          message: '请选择审批人',
          type: 'warning'
        })
      }
    },
    // 返回
    returnIndex() {
      this.$router.push('/gwbgscb')
    }
  },
  watch: {

  }
}

</script>

<style scoped>
.sec-container {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: overlay;
}

.sec-title {
  border-left: 5px solid #1b72d8;
  color: #1b72d8;
  font-size: 20px;
  font-weight: 700;
  text-indent: 10px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.sec-form-container {
  width: 100%;
  height: 100%;
}

.sec-form-left {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
}

.sec-form-left:not(:first-child) {
  border-top: 0;
}

.sec-form-left .el-form-item {
  float: left;
  width: 100%;
}

.sec-header-section {
  width: 100%;
  position: relative;
}

.sec-header-pic {
  width: 258px;
  position: absolute;
  right: 0px;
  top: 0;
  height: 163px;
  border: 1px solid #CDD2D9;
  border-left: 0;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sec-form-second {
  /* width: 100%; */
  border: 1px solid #CDD2D9;
  height: 40px;
  display: flex;
  justify-content: space-evenly;
  overflow: hidden;
  border-right: 0px;
  border-top: 0;
}

.sec-form-third {
  border: 1px solid #CDD2D9;
  /* height: 40px;  */
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-four {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  /* display: flex;
  justify-content: space-evenly; */
  overflow: hidden;
  /* border-right: 0px; */
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.sec-form-five {
  border: 1px solid #CDD2D9;
  height: auto;
  min-height: 100px;
  overflow: hidden;
  border-top: 0;
  background: #ffffff;
  padding: 10px;
}

.yulan {
  text-align: center;
  cursor: pointer;
  color: #3874D5;
  font-weight: 600;
  float: left;
  margin-left: 10px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
  border: 2px solid #EBEBEB;
}

.sec-form-six {
  border: 1px solid #CDD2D9;
  overflow: hidden;
  background: #ffffff;
  padding: 10px;
}

.ml10 {
  margin-left: 10px;
}

.sec-footer {
  margin-top: 10px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

>>>.sec-form-four .el-textarea__inner {
  border: none;
}

.sec-left-text {
  float: left;
  margin-right: 130px;
}

.haveBorderTop {
  border-top: 1px solid #CDD2D9;
}

>>>.longLabel .el-form-item__label {
  width: 500px !important;
}

>>>.longLabel .el-form-item__content {
  margin-left: 500px !important;
  padding-left: 20px;
  border-right: 1px solid #CDD2D9;
  background: #ffffff;
}

/* .sec-form-second:not(:first-child){
  border-top: 0;
} */
.sec-form-second .el-form-item {
  float: left;
  width: 100%;
}

.sec-el-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

.hyzk {
  padding-left: 15px;
  background-color: #F5F7FA;
  width: calc(100% - 16px);
  border-right: 1px solid #CDD2D9;
  color: #C0C4CC;
}

>>>.sec-el-table .el-input__inner {
  border: none !important;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item__label {
  width: 200px;
  text-align: center;
  font-size: 16px;
}

>>>.sec-form-container .el-input__inner {
  border: none;
  border-right: 1px solid #CDD2D9;
  border-radius: 0;
}

>>>.sec-form-container .el-form-item {
  margin-bottom: 0px;
}

/* >>>.el-form > div {
  border: 1px solid #CDD2D9;;
} */
>>>.el-form-item__label {
  border-right: 1px solid #CDD2D9;
}

/* /deep/.sec-form-container .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
} */

.widthw {
  width: 6vw;
}

.dlFqsqContainer {
  width: 100%;
  height: 100%;
}

.dlFqsqContainer label {
  font-weight: 700;
}

.dlFqsqContainer .input1,
.dlFqsqContainer .input2 {
  width: 150px;
  margin-left: 10px;
}

.dlFqsqContainer .searchButton {
  margin-left: 10px;
}

>>>.dlFqsqContainer .input1 .el-input__inner,
>>>.dlFqsqContainer .input2 .el-input__inner {
  height: 40px;
}

.dlFqsqContainer .input1 {
  margin-right: 20px;
}

.dlFqsqContainer .tb-container {
  margin-top: 20px;
}

.dlFqsqContainer .paginationContainer {
  margin-top: 20px;
}
</style>
