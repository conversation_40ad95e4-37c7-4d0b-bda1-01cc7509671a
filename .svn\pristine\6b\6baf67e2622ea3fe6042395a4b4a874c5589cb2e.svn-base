<template>
  <div style="height: 100%;">
    <hsoft_top_title>
      <template #left>流程配置</template>
    </hsoft_top_title>
    <!---->
    <div style="padding: 10px 0;text-align: right;">

      <el-button type="success" @click="showAddDialog">添加</el-button>
      <el-button type="warning" @click="fanhui">返回</el-button>
      <!-- <el-button type="primary" @click="getSettingList()">查询</el-button> -->
    </div>
    <el-table :data="settingList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
      style="width: 100%;border:1px solid #EBEEF5;" height="300px" stripe @row-click="rowclick" class="table">
      <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
      <el-table-column prop="hjmc" label="环节名称" width=""></el-table-column>
      <el-table-column prop="sfky" label="是否可用" :formatter="sfkysjpd"></el-table-column>

      <el-table-column prop="bzxx" label="备注信息" width="" align="center"></el-table-column>
      <el-table-column prop="" label="目标环节" width="">
        <template slot-scope="scoped">
          <el-button size="small" type="text" @click="mbhjSetting(scoped.row)">
            {{scoped.row.mbhj?scoped.row.mbhj:'选择目标环节'}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="pxh" label="排序号" width=""></el-table-column>
      <el-table-column prop="" label="操作" width="">
        <template slot-scope="scoped">
          <el-button size="small" type="text" @click="modifySetting(scoped.row)">编辑</el-button>
          <el-button size="small" type="text" @click="anqxSetting(scoped.row)">按钮配置</el-button>
          <el-button size="small" type="text" @click="lcpzdeleteSetting(scoped.row)" style="color:#F56C6C;">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :pager-count="5"
      :current-page="pageInfo.page" :page-sizes="[5, 10, 20, 30]" :page-size="pageInfo.pageSize"
      layout="total, prev, pager, sizes,next, jumper" :total="pageInfo.total" style="    padding-top: 10px;">
    </el-pagination> -->
    <!---->
    <!-- 添加流程 -->
    <hsoft_top_title>
      <template #left>目标环节</template>
    </hsoft_top_title>
    <el-table :data="mbsettingList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
      style="width: 100%;border:1px solid #EBEEF5;" height="300px" stripe  class="table1">
      <el-table-column prop="qshj" label="起始环节" width=""></el-table-column>
      <el-table-column prop="mbhj" label="目标环节"></el-table-column>

      <el-table-column label="处理目标" width="" align="center">
        <template slot-scope="scoped">
          <el-button size="small" type="text" @click="clmbSetting(scoped.row)">
            {{scoped.row.clmb!='null,'?scoped.row.clmb:'选择处理目标'}}</el-button>
        </template>
      </el-table-column>

      <el-table-column prop="" label="操作" width="305">
        <template slot-scope="scoped">
          <el-button size="small" type="text" @click="clmbdeleteSetting(scoped.row)" style="color:#F56C6C;">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="选择目标环节" :visible.sync="dialogMbhjSetting" width="35%">
      <el-table :data="xzmbhjList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
        @selection-change="selectRow" style="width: 100%;border:1px solid #EBEEF5;" height="300px" stripe ref="mrmbhj">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="hjmc" label="环节名称" width=""></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="xzmbhjaddSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogMbhjSetting = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="按钮权限配置" :visible.sync="dialogAnqxSetting" width="35%">
      <el-table :data="anpzList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
        style="width: 100%;border:1px solid #EBEEF5;" height="300px" stripe>
        <el-table-column prop="anmc" label="按钮名称" width=""></el-table-column>
        <el-table-column label="启用状态">
          <!-- <template slot="header" slot-scope="scope">
            <el-radio-group v-model="resource">
              <el-radio label="启用"></el-radio>
              <el-radio label="禁用"></el-radio>
            </el-radio-group>
          </template> -->
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.kzqx">
              <el-radio v-for="item in kzqx" :v-model="scope.row.kzqx" :label="item.id" :value="item.id" :key="item.id">
                {{ item.mc }}</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column>
          <template slot="header" slot-scope="scope">
            <span>按钮别名</span>
          </template>
          <template slot-scope="scope">
            <el-input v-model="scope.row.anbm"></el-input>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="anpzSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogAnqxSetting = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="新建环节" :visible.sync="dialogVisibleSetting" width="35%">
      <div>
        <el-form :model="settingForm" :label-position="'right'" label-width="120px" size="mini">
          <div style="display:flex">
            <el-form-item label="环节名称" class="one-line">
              <el-input v-model="settingForm.hjmc"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="是否可用" class="one-line-textarea">
            <el-select v-model="settingForm.sfky" placeholder="是否可用">
              <el-option v-for="item in hjsyqk" :label="item.mc" :value="item.id" :key="item.id"></el-option>
            </el-select>
          </el-form-item>
          <div style="display:flex">
            <el-form-item label="备注信息" class="one-line">
              <el-input v-model="settingForm.bzxx"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="排序号" class="one-line-textarea">
            <el-input v-model="settingForm.pxh"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSetting = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 处理目标 -->
    <el-dialog title="选择处理目标" :visible.sync="dialogClmbSetting" width="35%">
      <el-table :data="clmbList" border :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
        @selection-change="clmbselectRow" style="width: 100%;border:1px solid #EBEEF5;" height="300px" stripe
        ref="mrclmb">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="gwmc" label="岗位名称" width=""></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="clmbaddSetting()">保 存</el-button>
        <el-button type="warning" @click="dialogClmbSetting = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 修改系统参数 -->
    <el-dialog title="修改环节" :visible.sync="dialogVisibleSettingModify" width="35%">
      <div>
        <el-form :model="settingForm" :label-position="'right'" label-width="120px" size="mini">
          <div style="display:flex">
            <el-form-item label="环节名称" class="one-line">
              <el-input v-model="settingForm.hjmc"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="是否可用" class="one-line-textarea">
            <el-select v-model="settingForm.sfky" placeholder="是否可用">
              <el-option v-for="item in hjsyqk" :label="item.mc" :value="item.id" :key="item.id"></el-option>
            </el-select>
          </el-form-item>
          <div style="display:flex">
            <el-form-item label="备注信息" class="one-line">
              <el-input v-model="settingForm.bzxx"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="排序号" class="one-line-textarea">
            <el-input v-model="settingForm.pxh"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="xgSettingDialog()">确 定</el-button>
        <el-button type="warning" @click="dialogVisibleSettingModify = false">取 消</el-button>
      </span>
    </el-dialog>
    <!---->
    <!---->
    <!---->
  </div>
</template>

<script>
  import hsoft_top_title from '../../components/hsoft-top-title/hsoft-top-title.vue'
  import {
    gzlLchjFindList, //查询流程配置列表
    gzlLchjAdd, //新增环节
    gzlLchjAddMbhjxx, //新增目标环节配置
    gzlLchjFindMbhjList, //查询可选择目标环节列表
    gzlLchjUpdate, //编辑环节
    gzlLchjFindHjanList, //按钮配置查询列表
    gzlLchjAddHjan, //编辑按钮
    gzlLchjDelete, //删除环节
    gzlLchjAddClmbxx, //添加处理目标
    gzlLchjFindMbhjxxList, //查询目标环节列表
    gzlLchjFindGwlb, //查询处理目标
    gzlLchjDeleteMbhj, //删除目标环节列表
  } from '../../../api/lhpz'
  import {
    getWindowLocation
  } from '../../../utils/windowLocation'

  import {
    dateFormatChinese
  } from '../../../utils/moment'
  import {
    getAllYhxx, //获取全部sm人员
    getZzjgList, //获取全部机构
  } from '../../../api/index'

  // import { writeSystemOptionsLog } from '../../../utils/logUtils'

  // import { checkArr, decideChange } from '../../../utils/utils'

  // // 系统参数设置表
  // import {
  //   // 插入系统参数表
  //   insertSettingList,
  //   // 查询系统参数表
  //   selectSettingList,
  //   // 删除系统参数设置
  //   deleteSettingList,
  //   // 修改系统参数设置
  //   updateSettingList
  // } from '../../../db/zczpSystem/zczpSysyemDb'

  import {
    addXtcs,
    deleteXtcs,
    updateXtcs,
    getXtcsPage,
    getcszjldw
  } from '../../../api/cssz'

  export default {
    data() {
      return {
        //环节可用情况
        hjsyqk: [{
            id: 1,
            mc: '是'
          },
          {
            id: 2,
            mc: '否'
          },
        ],
        resource: '启用',
        //查询
        formInline: {
          fwmc: '',
          fwlx: '',
          fwzt: '',
        },
        // 分页信息
        pageInfo: {
          page: 1,
          pageSize: 10,
          total: 0
        },
        kzqx: [{
            mc: '启用',
            id: 1
          },
          {
            mc: '禁用',
            id: 0
          }
        ],
        anqx: {

        },
        regionOption: [], //部门数据
        //部门tree设置
        regionParams: {
          label: 'label', //这里可以配置你们后端返回的属性
          value: 'label',
          children: 'childrenRegionVo',
          expandTrigger: 'click',
          checkStrictly: true,
        }, //地域信息配置参数
        // 更新系统参数dialog
        dialogVisibleSettingModify: false,
        // 添加系统参数dialog
        dialogVisibleSetting: false,
        // 选择处理目标dialog
        dialogClmbSetting: false,
        //目标环节dialog
        dialogMbhjSetting: false,
        //按钮权限dialog
        dialogAnqxSetting: false,
        settingForm: {
          sfky:1
        },
        settingFormOld: {},
        cszlx: 1,
        // 系统参数设置表格数据
        settingList: [],
        //选择目标环节
        xzmbhjList: [],
        //选择处理目标
        clmbList: [],
        //按钮配置
        anpzList: [],
        //目标环节
        mbsettingList: [],
        pickerOptions: {
          disabledDate: time => {
            if (this.selectDate == null) {
              return false
            } else {
              return (this.selectDate.getFullYear() != time.getFullYear())
            }
          },
          onPick: date => {
            // 如果只选择一个则保存至selectDate 否则selectDate 为空
            if (date.minDate && !date.maxDate) {
              this.selectDate = date.minDate
            } else {
              this.selectDate = null
            }
          }
        },
        jldwList: [],

      }
    },
    components: {
      hsoft_top_title
    },
    methods: {
      //刷新目标环节页面
      async sxmbsj() {
        let params = new FormData()
        params.append('fwdyid', this.fwdyid)
        params.append('hjid', this.rowHjid)
        let data = await gzlLchjFindMbhjxxList(params)
        this.mbsettingList = data.data.content
      },
      //目标环节删除
      clmbdeleteSetting(row) {
        console.log(row);
        let params = {
          fwdyid: this.fwdyid,
          qshjid: row.qshjid,
          mbhjid: row.mbhjid
        }
        gzlLchjDeleteMbhj(params)
        this.getSettingList()
        this.sxmbsj()
      },
      //提交处理目标
      clmbaddSetting() {
        let params = {
          fwdyid: this.fwdyid,
          zhid: this.zhid,
          clmb: [{
            clmbmxList: this.clmbxz,
            fwdyid: this.fwdyid,
            zhid: this.zhid,
            clmblx: 1
          }]
        }
        gzlLchjAddClmbxx(params).then(() => {
          this.getSettingList()
          this.sxmbsj()
          this.dialogClmbSetting = false
        })
      },
      //查询处理目标
      async clmb() {
        this.clmbList = []
        let data = await gzlLchjFindGwlb()
        console.log(data);
        data.data.content.forEach(item => {
          let params = {}
          params.gwmc = item
          this.clmbList.push(params)
        })
        console.log(this.clmbList);
      },
      //获取目标环节
      async rowclick(row) {
        console.log(row);
        this.rowHjid = row.hjid;
        let params = new FormData()
        params.append('fwdyid', this.fwdyid)
        params.append('hjid', row.hjid)
        let data = await gzlLchjFindMbhjxxList(params)
        this.mbsettingList = data.data.content
      },
      //处理目标选择
      clmbSetting(row) {
        console.log(row.clmb.split(','));
        console.log(this.clmbList);
        this.clmb().then(() => {
          this.$nextTick(() => {
            row.clmb.split(',').forEach((item, index) => {
              this.clmbList.forEach((item1, index1) => {
                if (item == item1.gwmc) {
                  console.log(item);
                  this.$refs.mrclmb.toggleRowSelection(item1, true)
                }
              })
            })
          })
        })

        this.zhid = row.zhid
        this.dialogClmbSetting = true
      },
      //按钮配置
      anpzSetting() {
        console.log(this.anpzList);
        let params = {
          fwdyid: this.fwdyid,
          hjid: this.hjid,
          hjan: this.anpzList,
        }
        gzlLchjAddHjan(params)
        this.dialogAnqxSetting = false
        this.getSettingList()
      },
      //全部组织机构List
      async zzjg() {
        let zzjgList = await getZzjgList()
        console.log(zzjgList);
        this.zzjgmc = zzjgList
        let shu = []
        console.log(this.zzjgmc);
        this.zzjgmc.forEach(item => {
          let childrenRegionVo = []
          this.zzjgmc.forEach(item1 => {
            if (item.bmm == item1.fbmm) {
              // console.log(item, item1);
              childrenRegionVo.push(item1)
              // console.log(childrenRegionVo);
              item.childrenRegionVo = childrenRegionVo
            }
          });
          // console.log(item);
          shu.push(item)
        })

        console.log(shu);
        console.log(shu[0].childrenRegionVo);
        let shuList = []
        shu.forEach(item => {
          if (item.fbmm == '') {
            shuList.push(item)
          }
        })
        console.log(shuList);
        shuList[0].childrenRegionVo.forEach(item => {
          this.regionOption.push(item)
        })
      },
      //获取计量单位
      async getjldw() {
        this.jldwList = await getcszjldw()
      },
      showAddDialog() {
        this.settingForm = {
          sfky:1
        }
        console.log(this.settingForm)
        this.dialogVisibleSetting = true
      },
      //返回按钮
      fanhui() {
        this.$router.push('/lhglSetting')
      },
      // 格式化时间
      formatTime(time) {
        return dateFormatChinese(new Date(time))
      },
      //查询按钮
      onSubmit() {},
      //重置按钮
      cz() {
        this.formInline = {}
      },
      handleCurrentChange(val) {
        this.pageInfo.page = val
        this.getSettingList()
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.getSettingList()
      },
      // 修改(表格)
      modifySetting(row) {
        // let csz = row.csz
        // console.log(csz,checkArr(csz))
        // if (checkArr(csz)) {
        //   row.csz[0] = csz[0].month + '-' + csz[0].day
        //   row.csz[1] = csz[1].month + '-' + csz[1].day
        // }
        this.settingForm = JSON.parse(JSON.stringify(row))
        console.log('this.settingForm', this.settingForm);
        this.settingFormOld = JSON.parse(JSON.stringify(row))
        console.log('this.settingFormOld', this.settingFormOld);
        if (this.settingForm.cszlx == 2) {
          //  this.settingForm.cszDate = this.settingForm.cszDate
        }
        if (this.settingForm.cszlx == 3) {

        }
        this.dialogVisibleSettingModify = true
      },
      //目标环节
      async mbhjSetting(row) {
        console.log(row);
        this.hjid = row.hjid
        let params = new FormData()
        params.append('fwdyid', this.fwdyid)
        params.append('hjid', row.hjid)
        let data = await gzlLchjFindMbhjList(params)
        console.log(data.data.content);
        this.xzmbhjList = data.data.content
        this.$nextTick(() => {
          if (row.mbhj != undefined) {
            row.mbhj.split(',').forEach((item, index) => {
              this.xzmbhjList.forEach((item1, index1) => {
                if (item == item1.hjmc) {
                  console.log(item);
                  this.$refs.mrmbhj.toggleRowSelection(item1, true)
                }
              })
            })
          }
        })
        this.dialogMbhjSetting = true
      },
      //按钮配置
      async anqxSetting(row) {
        let params = new FormData()
        params.append('fwdyid', this.fwdyid)
        params.append('hjid', row.hjid)
        this.hjid = row.hjid
        let data = await gzlLchjFindHjanList(params)
        console.log(data);
        this.anpzList = data.data.content
        this.dialogAnqxSetting = true
      },
      // 修改（dialog）
      async xgSettingDialog() {

        let params = JSON.parse(JSON.stringify(this.settingForm))
        console.log(params);
        params.fwdyid = this.fwdyid
        let data = await gzlLchjUpdate(params)
        if (data.code = 10000) {
          this.getSettingList()
        }
        this.dialogVisibleSettingModify = false
      },
      // 删除参数设置
      async lcpzdeleteSetting(row) {
        let params = {
          fwdyid: this.fwdyid,
          hjid: row.hjid
        }
        let data = await gzlLchjDelete(params)
        if (data.code = 10000) {
          this.getSettingList()
        }

      },
      // 获取参数设置集合
      async getSettingList() {
        // this.settingForm = {}
        // let params = {}
        // Object.assign(params, this.pageInfo)
        // let settingPage = selectSettingList(params)
        // this.settingList = settingPage.list
        // this.pageInfo.total = settingPage.total
        // let params = {
        //   page: this.pageInfo.page,
        //   pageSize: this.pageInfo.pageSize
        // }
        let params = new FormData();
        // params.append('add', '111111111111111111111')
        console.log(this.fwdyid);
        params.append('fwdyid', this.fwdyid);
        let settingPage = await gzlLchjFindList(params)
        console.log(settingPage)
        this.settingList = settingPage.data.content
        // console.log(this.settingList);
        // this.pageInfo.total = settingPage.data.content
      },
      //新增目标环节选中的值
      selectRow(row) {
        console.log(row);
        this.bmhjbz = row
      },

      //处理目标环节选中的值
      clmbselectRow(row) {
        console.log(row);
        this.clmbxz = row
      },

      xzmbhjaddSetting() {
        let params = {
          mbhj: this.bmhjbz,
          fwdyid: this.fwdyid,
          hjid: this.hjid
        }
        gzlLchjAddMbhjxx(params).then(() => {
          this.getSettingList()
        })
        this.dialogMbhjSetting = false
      },
      // 添加参数设置
      async addSetting() {
        let params = JSON.parse(JSON.stringify(this.settingForm))
        params.fwdyid = this.fwdyid
        console.log('表单数据', params)
        let data = await gzlLchjAdd(params)
        if (data.code = 10000) {
          this.getSettingList()
        }
        // 写入日志
        // 加入审计日志需要显示的内容
        // let paramsExtra = {
        //   bs: params.csbs,
        //   modifyArr: []
        // }
        // // 判定修改
        // paramsExtra.modifyArr = decideChange({}, params, ['settingid', 'gxsj'])
        // Object.assign(params, paramsExtra)
        // let logParams = {
        //   xyybs: 'yybs_cssz',
        //   ymngnmc: '添加',
        //   extraParams: params
        // }
        // writeSystemOptionsLog(logParams)
        this.dialogVisibleSetting = false
      },
      fordw(row) {
        let hxsj
        this.jldwList.forEach(item => {
          if (row.cszdw == item.id) {
            hxsj = item.mc
          }
        })
        return hxsj
      },
      //模糊匹配姓名
      querySearchxm(queryString, cb) {
        var restaurants = this.restaurantsxm;
        console.log("restaurants", restaurants);
        var results = queryString ? restaurants.filter(this.createFilterzw(queryString)) : restaurants;
        console.log("results", results);
        // 调用 callback 返回建议列表的数据
        // for (var i = 0; i < results.length; i++) {
        //   for (var j = i + 1; j < results.length; j++) {
        //     if (results[i].xm === results[j].xm) {
        //       results.splice(j, 1);
        //       j--;
        //     }
        //   }
        // }
        cb(results);
        console.log("cb(results.zw)", results);
      },
      createFilterxm(queryString) {
        return (restaurant) => {
          return (restaurant.xm.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
        };
      },
      async xmmh() {
        let resList = await getAllYhxx()
        // console.log(resList);
        this.restaurantsxm = resList;
        this.restaurantszj = resList;
        // console.log("this.restaurants", this.restaurantsbm);
        // console.log(resList)
      },
      sfkysjpd(row){
        let sfky = '';
        this.hjsyqk.forEach(item=>{
          if (row.sfky == item.id) {
            sfky = item.mc
          }
        })
        return sfky
      }
    },
    mounted() {
      this.clmb()
      console.log(this.$route.query.fwdyid);
      this.fwdyid = this.$route.query.fwdyid
      this.zzjg()
      this.xmmh()
      this.getjldw()
      //
      this.getSettingList()
      //
      console.log(new Date(2022, 11, 1))
    }
  }

</script>

<style scoped>
  .out-card {
    /* margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); */
  }

  /**单位信息区域**/
  .out-card .out-card-div {
    font-size: 13px;
    padding: 5px 20px;
  }

  .out-card .out-card-div div {
    padding: 10px 5px;
    display: flex;
  }

  .out-card .dwxx div:hover {
    background: #f4f4f5;
    border-radius: 20px;
  }

  .out-card .dwxx div label {
    /* background-color: red; */
    width: 125px;
    display: inline-block;
    text-align: right;
    font-weight: 600;
    color: #909399;
  }

  .out-card .dwxx div span {
    /* background-color: rgb(33, 92, 79); */
    flex: 1;
    display: inline-block;
    padding-left: 20px;
  }
  .table ::-webkit-scrollbar {
  display: block !important;
  width: 8px;
  /*滚动条宽度*/
  height: 8px;
  /*滚动条高度*/
}

.table ::-webkit-scrollbar-track {
  border-radius: 10px;
  /*滚动条的背景区域的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(238, 238, 238, 0.3);
  background-color: #eeeeee;
  /*滚动条的背景颜色*/
}

.table ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /*滚动条的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.3);
  background-color: rgb(145, 143, 143);
  /*滚动条的背景颜色*/
}
  .table1 ::-webkit-scrollbar {
  display: block !important;
  width: 8px;
  /*滚动条宽度*/
  height: 8px;
  /*滚动条高度*/
}

.table1 ::-webkit-scrollbar-track {
  border-radius: 10px;
  /*滚动条的背景区域的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(238, 238, 238, 0.3);
  background-color: #eeeeee;
  /*滚动条的背景颜色*/
}

.table1 ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /*滚动条的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.3);
  background-color: rgb(145, 143, 143);
  /*滚动条的背景颜色*/
}

</style>
