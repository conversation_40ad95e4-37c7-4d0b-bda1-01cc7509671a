<template>
	<div class="bg_con">
		<el-button class="fhsmry" type="primary" size="small" @click="fhsmry">返回</el-button>
		<el-tabs v-model="activeName" @tab-click="handleClick" style="height: 100%;z-index: 1;
    position: relative;">
			<el-tab-pane label="载体信息详情" name="jbxx" style="height: 92%;">
				<div class="jbxx">
					<el-form ref="form" :model="jbxx" label-width="152px" size="mini" :label-position="labelPosition"
						disabled>
						<div style="display:flex;justify-content: center;">
							<el-form-item label="涉密载体名称" class="xm" style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="涉密载体名称" v-model="jbxx.ztmc" clearable disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="载体编号" prop="ztbh" class="xm"
								style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="载体编号" v-model="jbxx.ztbh" clearable disabled>
								</el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="项目编号" prop="xmbh" class="xm"
								style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="项目编号" v-model="jbxx.xmbh" clearable disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="生成原因" prop="scyy" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-select v-model="jbxx.scyy" clearable disabled placeholder="请选择生成原因"
									style="width: 100%;">
									<el-option v-for="item in ztscyyxz" :label="item.mc" :value="item.id"
										:key="item.id"></el-option>
								</el-select>
							</el-form-item>
						</div>
						<div style="display:flex;justify-content: center;">
							<el-form-item label="保密期限" class="xm" style="height:50px;border: 1px solid #ebebeb;">
								<el-input placeholder="责保密期限任人" v-model="jbxx.bmqx">
								</el-input>
							</el-form-item>
							<el-form-item label="密级" class="xm" style="height:50px;border: 1px solid #ebebeb;">
								<el-radio-group v-model="jbxx.smmj">
									<el-radio v-for="item in sbmjxz" :label="item.id" :value="item.id" :key="item.id">{{
										item.mc }}</el-radio>
								</el-radio-group>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="类型" prop="lx" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="类型" v-model="jbxx.lx" clearable></el-input>
							</el-form-item>
							<el-form-item label="份数" prop="fs" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="份数" v-model="jbxx.fs" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="页数" prop="ys" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="页数" v-model="jbxx.ys" clearable></el-input>
							</el-form-item>
							<el-form-item label="知悉范围" prop="zxfw" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="知悉范围" v-model="jbxx.zxfw" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="生成日期" prop="scrq" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-date-picker v-model="jbxx.scrq" clearable type="date" style="width: 100%;"
									placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
								</el-date-picker>
							</el-form-item>
							<el-form-item label="生成部门" prop="scbm" class="xm one-inpu"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="生成部门" v-model="jbxx.scbm" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="责任人" prop="scrq" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="责任人" v-model="jbxx.zrr" clearable></el-input>
							</el-form-item>
							<el-form-item label="保管位置" prop="bgwz" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-input placeholder="保管位置" v-model="jbxx.bgwz" clearable></el-input>
							</el-form-item>
						</div>
						<div style="display:flex;">
							<el-form-item label="状态" prop="zt" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-radio-group v-model="jbxx.zt">
									<el-radio v-for="item in sbsyqkxz" :label="item.id" :value="item.id" :key="item.id">{{
										item.mc }}</el-radio>
								</el-radio-group>
							</el-form-item>
							<el-form-item label="状态变化时间" class="xm"
								style="height:50px;width:100%;border: 1px solid #ebebeb;">
								<el-date-picker v-model="jbxx.ztbgsj" clearable type="date" style="width: 100%;"
									placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
								</el-date-picker>
							</el-form-item>
						</div>
					</el-form>
				</div>
			</el-tab-pane>
			<el-tab-pane label="载体制作详情" name="ztzz" style="height: 100%;">
				<ztzz :msg="jbxx"></ztzz>
			</el-tab-pane>
			<el-tab-pane label="载体复制详情" name="ztfz" style="height: 100%;">
				<ztfz :msg="jbxx"></ztfz>
			</el-tab-pane>
			<el-tab-pane label="载体接收传递详情" name="ztjs" style="height: 100%;">
				<ztjs :msg="jbxx"></ztjs>
			</el-tab-pane>
			<el-tab-pane label="载体外发传递详情" name="ztwf" style="height: 100%;">
				<ztwf :msg="jbxx"></ztwf>
			</el-tab-pane>
			<el-tab-pane label="载体外出携带详情" name="ztwc" style="height: 100%;">
				<ztwc :msg="jbxx"></ztwc>
			</el-tab-pane>
			<el-tab-pane label="载体借阅详情" name="ztjy" style="height: 100%;">
				<ztjy :msg="jbxx"></ztjy>
			</el-tab-pane>
			<el-tab-pane label="载体销毁详情" name="ztxh" style="height: 100%;">
				<ztxh :msg="jbxx"></ztxh>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
import ztzz from './ztspdxqy/ztzzxqy.vue'
import ztfz from './ztspdxqy/ztfzxqy.vue'
import ztjs from './ztspdxqy/ztjsxqy.vue'
import ztwf from './ztspdxqy/ztwfxqy.vue'
import ztwc from './ztspdxqy/ztwcxqy.vue'
import ztjy from './ztspdxqy/ztjyxqy.vue'
import ztxh from './ztspdxqy/ztxhxqy.vue'
import {
  getAllSmztYy, //原因
  getSmztZt, //状态
  getAllSmsbmj //密级
} from '../../../api/xlxz'
export default {
	data() {
		return {
			activeName: 'jbxx',
			jbxx: {},
			gwmc: [],
			jbxxsj: {},
			updateItemOld: {},
			labelPosition: 'right',
			sbmjxz: [],
			regionOption: [], //地域信息
			regionParams: {
				label: 'label', //这里可以配置你们后端返回的属性
				value: 'label',
				children: 'childrenRegionVo',
				expandTrigger: 'click',
				checkStrictly: true,
			},
			smdjxz: [],
			gwqdyjxz: [],
			jbzcxz: [],
			zgxlxz: [],
			sflxxz: [],
			yrxsxz: [],
			ztscyyxz: [], //生产原因
			sbsyqkxz: [],

			imageUrl: '',
			smryid: '',
		};
	},
	computed: {
	},
	components: {
		ztzz,
		ztfz,
		ztjs,
		ztwf,
		ztwc,
		ztjy,
		ztxh
	},
	mounted() {
		this.smdj()
		this.ztzt()
		this.ztyy()

		console.log(this.$route.query.row);
		this.jbxxsj = JSON.parse(JSON.stringify(this.$route.query.row))
		this.jbxx = this.jbxxsj
		// this.smryid = JSON.parse(JSON.stringify(this.$route.query.row.smryid))
		// console.log('this.smryid', this.smryid);
		console.log('this.jbxx', this.jbxx);
	},
	methods: {
		async ztyy() {
			this.ztscyyxz = await getAllSmztYy()
		},
		async ztzt() {
			this.sbsyqkxz = await getSmztZt()
		},
		//获取涉密等级信息
		async smdj() {
			let data = await getAllSmsbmj()
			this.sbmjxz = data
		},
		handleClick(tab, event) {
			console.log(tab, event);
		},
		//返回涉密人员
		fhsmry() {
			this.$router.push({
				path: '/smzttz'
			})
		},
	},
	watch: {},

};
</script>
<style scoped>
.bg_con {
	width: 100%;
	height: calc(100% - 38px);
}



>>>.el-tabs__content {
	height: 100%;
}

.jbxx {
	height: 92%;
	display: flex;
	justify-content: center;
	height: 100%;
	overflow-y: scroll;
	background: #fff;
}

.xm {
	background-color: #fff;
}

.container {
	height: 92%;
}

.dabg {
	box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
	border-radius: 8px;
	width: 100%;
}

.item_button {
	height: 100%;
	float: left;
	padding-left: 10px;
	line-height: 50px;
}

.select_wrap {

	.select_wrap_content {
		float: left;
		width: 100%;
		line-height: 50px;
		height: 100%;
		background: rgba(255, 255, 255, 0.7);

		.item_label {
			padding-left: 10px;
			height: 100%;
			float: left;
			line-height: 50px;
			font-size: 1em;
		}
	}
}

.mhcx1 {
	margin-top: 0px;
}

.widthw {
	width: 6vw;
}


/deep/.el-date-editor.el-input,
.el-date-editor.el-input__inner {
	width: 184px;
}

/deep/.el-radio-group {
	width: 300px;
	margin-left: 15px;
}

/deep/.mhcx .el-form-item {
	margin-top: 5px;
	margin-bottom: 5px;
}

/deep/.el-dialog {
	margin-top: 6vh !important;
}

/deep/.inline-inputgw {
	width: 105%;
}

.drfs {
	width: 126px
}

.daochu {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

/deep/.el-select .el-select__tags>span {
	display: flex !important;
	flex-wrap: wrap;
}

/deep/.el-dialog__body .el-form>div .el-form-item__label {
	width: 155px !important;
}

.bz {
	height: 72px !important;
}

/deep/.el-dialog__body .el-form>div>div {
	/* width: auto; */
	max-width: 100%;
}

.el-select__tags {
	white-space: nowrap;
	overflow: hidden;
}

.dialog-footer {
	display: block;
	margin-top: 10px;
}

.xmr /deep/.el-dialog__body .el-form .el-form-item--mini.el-form-item {
	height: 52px;
}

.avatar-uploader .el-upload {
	border: 1px dashed #d9d9d9;
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;

}

.avatar-uploader .el-upload:hover {
	border-color: #409EFF;
}

.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 482px;
	height: 254px;
	line-height: 254px;
	text-align: center;
}

.fhsmry {
	float: right;
	z-index: 99;
	margin-top: 5px;
	position: relative;
}

.avatar {
	width: 400px;
	height: 254px;
}

>>>.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
	margin-bottom: 0px;
}

.xm>>>.el-form-item__label {
	line-height: 50px;
	background-color: #f3f8ff;
}

/deep/.el-form-item--mini .el-form-item__content,
.el-form-item--mini .el-form-item__label {
	line-height: 50px;
	width: 330px !important;
}

/deep/.el-select>.el-input,
.el-color-picker__icon,
.el-input {
	margin-left: 15px;
	width: 300px !important;
}

/deep/.el-textarea {
	margin-left: 15px;
	width: 784px !important;
}

.one-line-bz>>>.el-form-item__content {
	line-height: 50px;
	width: 814px !important;
}

.one-input>>>.el-input {
	width: 784px !important;
}

/deep/.el-cascader--mini {
	margin-left: 15px;
	width: 300px !important;
}

/deep/.el-select .el-tag {
	margin-left: 28px;
}
</style>
