{"version": 3, "sources": ["webpack:///src/renderer/view/rcgz/ztwcxdsc.vue", "webpack:///./src/renderer/view/rcgz/ztwcxdsc.vue?257b", "webpack:///./src/renderer/view/rcgz/ztwcxdsc.vue"], "names": ["ztwcxdsc", "components", "BaseHeader", "baseHeader", "BaseTable", "baseTable", "props", "data", "_this", "this", "loading", "headerCellStyle", "background", "color", "formInline", "dialogVisible", "ryDatas", "page", "pageSize", "page1", "pageSize1", "ry<PERSON><PERSON>ose", "ztbh", "ztmc", "xmbh", "total", "total1", "radioIdSelect", "smryList", "scjtlist", "mc", "id", "dqztlist", "rowdata", "regionOption", "pdztsp", "regionParams", "label", "value", "children", "expandTrigger", "checkStrictly", "params", "name", "tmjssj", "columns", "type", "placeholder", "startPlaceholder", "rangeSeparator", "endPlaceholder", "format", "disabled", "icon", "mold", "tableColumns", "prop", "scopeType", "formatter", "showOverflowTooltip", "row", "column", "cellValue", "index", "opt", "find", "d", "handleColumn", "show", "Lcfwslzt", "cjrid", "loginName", "handleColumnProp", "width", "align", "applyColumns", "handleColumnApply", "smryColumns", "computed", "mounted", "onfwid", "getLogin<PERSON>hm", "rysclist", "zzjg", "methods", "close", "_this2", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "userInfo", "wrap", "_context", "prev", "next", "Object", "dwzc", "sent", "yhm", "stop", "handleSizeChange", "val", "handleCurrentChange", "selectBtn", "console", "log", "shanchu", "_this3", "length", "$message", "message", "$confirm", "confirmButtonText", "cancelButtonText", "then", "_ref", "for<PERSON>ach", "_callee2", "item", "_context2", "j<PERSON>", "ztwcxd", "code", "_x", "apply", "arguments", "catch", "handleBtnAll", "parameter", "JSON", "parse", "stringify_default", "_this4", "_callee3", "_context3", "xm", "kssj", "jssj", "records", "error", "searchRy", "table<PERSON><PERSON>", "sendApplay", "_this5", "_callee4", "param", "resData", "_context4", "zt", "api", "handleCurrentChangeRy", "handleSizeChangeRy", "selectList", "_ref2", "_this6", "ztwf", "_callee5", "data1", "_context5", "sm<PERSON><PERSON>", "ztid", "_x2", "submitRy", "_this7", "_callee6", "_context6", "keys_default", "$router", "push", "path", "query", "datas", "scjgsj", "dqztsj", "_this8", "_callee7", "_context7", "fwlx", "fwdyid", "operateBtn", "_this9", "_callee8", "res", "res1", "_context8", "yj<PERSON>", "ztqs", "undefined", "lx", "list", "slid", "_this10", "_callee9", "zzjgList", "shu", "shuList", "_context9", "zzjgmc", "childrenRegionVo", "item1", "bmm", "fbmm", "bmSelectChange", "bm", "join", "watch", "rcgz_ztwcxdsc", "render", "_vm", "_h", "$createElement", "_c", "_self", "directives", "rawName", "expression", "staticClass", "attrs", "on", "handleBtn", "_v", "inline", "model", "size", "click", "showSelection", "<PERSON><PERSON><PERSON><PERSON>", "showIndex", "tableData", "showPagination", "currentPage", "totalCount", "title", "close-on-click-modal", "visible", "update:visible", "$event", "for", "clearable", "callback", "$$v", "$set", "tableHeight", "showSingleSelection", "slot", "staticRenderFns", "Component", "__webpack_require__", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "gRAuEAA,GACAC,YACAC,WAAAC,EAAA,EACAC,UAAAC,EAAA,GAEAC,SACAC,KANA,WAMA,IAAAC,EAAAC,KACA,OACAC,SAAA,EAEAC,iBACAC,WAAA,UACAC,MAAA,WAEAC,cACAC,eAAA,EACAC,WACAC,KAAA,EACAC,SAAA,EACAC,MAAA,EACAC,UAAA,GAEAC,UACAC,KAAA,GACAC,KAAA,GACAC,KAAA,IAEAC,MAAA,EACAC,OAAA,EACAC,cAAA,GACAC,YACAC,WAEAC,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAC,WAEAF,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAE,WACAC,gBACAC,OAAA,GACAC,cACAC,MAAA,QACAC,MAAA,QACAC,SAAA,mBACAC,cAAA,QACAC,eAAA,GAGAC,QACAC,KAAA,GACAC,OAAA,IAGAC,UACAC,KAAA,cACAH,KAAA,MACAL,MAAA,OACAS,YAAA,QAGAD,KAAA,YACAH,KAAA,OACAL,MAAA,SACAU,iBAAA,SACAC,eAAA,IACAC,eAAA,SACAC,OAAA,eAGAL,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,iBACAC,KAAA,YAGAR,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,uBACAC,KAAA,YAIAC,eAEAZ,KAAA,MACAa,KAAA,MACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,QACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,EACAC,qBAAA,IAGAhB,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,EACAC,qBAAA,IAGAhB,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,OACAa,KAAA,WACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,IAkBAC,IAhBAlC,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAkC,KAAA,SAAAC,GAAA,OAAAA,EAAAnC,KAAA+B,IACA,OAAAE,IAAAlC,GAAA,MAIAa,KAAA,OACAa,KAAA,WACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,IAkBAC,IAhBAlC,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAkC,KAAA,SAAAC,GAAA,OAAAA,EAAAnC,KAAA+B,IACA,OAAAE,IAAAlC,GAAA,MAKAqC,eAEAxB,KAAA,KACAS,UAAA,EACAgB,MAAA,EACAV,UAAA,SAAAE,EAAAC,GACA,UAAAD,EAAAS,UAAAT,EAAAU,OAAA9D,EAAA+D,UACA,KACA,GAAAX,EAAAS,UAAA,GAAAT,EAAAS,UAAA,GAAAT,EAAAS,SACA,UADA,KAOAG,kBACAnC,MAAA,KACAoC,MAAA,MACAC,MAAA,QAGAC,eAEAhC,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,OACAa,KAAA,KACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,IAkBAC,IAhBAlC,GAAA,MACAC,GAAA,IAGAD,GAAA,MACAC,GAAA,IAGAD,GAAA,OACAC,GAAA,IAGAD,GAAA,OACAC,GAAA,IAGAkC,KAAA,SAAAC,GAAA,OAAAA,EAAAnC,KAAA+B,IACA,OAAAE,IAAAlC,GAAA,MAIAa,KAAA,KACAa,KAAA,OACAC,UAAA,OACAC,UAAA,SAAAE,EAAAC,EAAAC,EAAAC,GACA,IAkBAC,IAhBAlC,GAAA,KACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAD,GAAA,KACAC,GAAA,IAGAkC,KAAA,SAAAC,GAAA,OAAAA,EAAAnC,KAAA+B,IACA,OAAAE,IAAAlC,GAAA,MAIAa,KAAA,OACAa,KAAA,OACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,QACAa,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAf,KAAA,KACAa,KAAA,KACAC,UAAA,OACAC,WAAA,IAGAkB,qBAEAC,cACA/B,KAAA,WACAH,KAAA,KACAL,MAAA,OACAS,YAAA,UAEAD,KAAA,cACAH,KAAA,KACAL,MAAA,OACAS,YAAA,OAGAD,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,iBACAC,KAAA,YAGAR,KAAA,SACAH,KAAA,KACAS,UAAA,EACAC,KAAA,uBACAC,KAAA,YAIAiB,UAAA,KAGAO,YACAC,QAvVA,WAwVAtE,KAAAuE,SACAvE,KAAAwE,cACAxE,KAAAyE,WACAzE,KAAA0E,QAEAC,SACAC,MADA,WAEA5E,KAAAY,SAAAC,KAAA,GACAb,KAAAY,SAAAE,KAAA,GACAd,KAAAY,SAAAG,KAAA,GACAf,KAAAM,eAAA,GAGAkE,YARA,WAQA,IAAAK,EAAA7E,KAAA,OAAA8E,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAAC,EAAA,OAAAJ,EAAAC,EAAAI,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,GADA,OACAL,EADAE,EAAAK,KAEAb,EAAAf,UAAAqB,EAAAQ,IAFA,wBAAAN,EAAAO,SAAAV,EAAAL,KAAAC,IAKAe,iBAbA,SAaAC,GACA9F,KAAAU,MAAA,EACAV,KAAAW,UAAAmF,EACA9F,KAAAyE,YAEAsB,oBAlBA,SAkBAD,GACA9F,KAAAU,MAAAoF,EACA9F,KAAAyE,YAGAuB,UAvBA,SAuBA7C,GACAnD,KAAAwB,QAAA2B,EACA8C,QAAAC,IAAA/C,IAGAgD,QA5BA,WA4BA,IAAAC,EAAApG,KACA,GAAAA,KAAAwB,QAAA6E,OACArG,KAAAsG,UACAC,QAAA,aACAlE,KAAA,YAGArC,KAAAwG,SAAA,2BACAC,kBAAA,KACAC,iBAAA,KACArE,KAAA,YACAsE,KAAA,WACA,IAAAC,EAAAR,EAAA5E,QAAAqF,SAAAD,EAAA9B,IAAAC,EAAAC,EAAAC,KAAA,SAAA6B,EAAAC,GAAA,IAAA9E,EAAA,OAAA8C,EAAAC,EAAAI,KAAA,SAAA4B,GAAA,cAAAA,EAAA1B,KAAA0B,EAAAzB,MAAA,cACAtD,GACAgF,KAAAF,EAAAE,KACApG,KAAAkG,EAAAlG,MAHAmG,EAAAzB,KAAA,EAKAC,OAAA0B,EAAA,EAAA1B,CAAAvD,GALA,OAMA,KANA+E,EAAAtB,KAMAyB,OACAf,EAAAE,UACAC,QAAA,OACAlE,KAAA,YAEA+D,EAAA3B,YAXA,wBAAAuC,EAAApB,SAAAkB,EAAAV,MAAA,SAAAgB,GAAA,OAAAR,EAAAS,MAAArH,KAAAsH,gBAcAC,MAAA,WACAnB,EAAAE,UACAjE,KAAA,OACAkE,QAAA,aAMAiB,aA/DA,SA+DAC,EAAAV,GACA,MAAAA,EAAA7E,MACAlC,KAAAiC,OAAAyF,KAAAC,MAAAC,IAAAH,IACAzH,KAAAU,MAAA,EACAV,KAAAyE,YACA,MAAAsC,EAAA7E,OACAlC,KAAAiC,QACAC,KAAA,GACAC,OAAA,MAKAsC,SA5EA,SA4EAgD,GAAA,IAAAI,EAAA7H,KAAA,OAAA8E,IAAAC,EAAAC,EAAAC,KAAA,SAAA6C,IAAA,IAAA7F,EAAAnC,EAAA,OAAAiF,EAAAC,EAAAI,KAAA,SAAA2C,GAAA,cAAAA,EAAAzC,KAAAyC,EAAAxC,MAAA,cACAtD,GACA+F,GAAAH,EAAA5F,OAAAC,KACA1B,KAAAqH,EAAAnH,MACAD,SAAAoH,EAAAlH,WAEA,MAAAkH,EAAA5F,OAAAE,SACAF,EAAAgG,KAAAJ,EAAA5F,OAAAE,OAAA,GACAF,EAAAiG,KAAAL,EAAA5F,OAAAE,OAAA,IARA4F,EAAAxC,KAAA,EAUAC,OAAA0B,EAAA,EAAA1B,CAAAvD,GAVA,QAUAnC,EAVAiI,EAAArC,MAWAyC,SACAN,EAAA1G,SAAArB,EAAAqI,QACAN,EAAA5G,OAAAnB,EAAAkB,OAEA6G,EAAAvB,SAAA8B,MAAA,WAfA,wBAAAL,EAAAnC,SAAAkC,EAAAD,KAAA/C,IAoBAuD,SAhGA,WAiGArI,KAAAsI,WACAtI,KAAAQ,KAAA,EACAR,KAAAuI,cAGAA,WAtGA,WAsGA,IAAAC,EAAAxI,KAAA,OAAA8E,IAAAC,EAAAC,EAAAC,KAAA,SAAAwD,IAAA,IAAAC,EAAAC,EAAA,OAAA5D,EAAAC,EAAAI,KAAA,SAAAwD,GAAA,cAAAA,EAAAtD,KAAAsD,EAAArD,MAAA,cACAiD,EAAAlI,eAAA,EACAoI,GACAlI,KAAAgI,EAAAhI,KACAC,SAAA+H,EAAA/H,SACAoI,GAAA,GAEA,IAAAL,EAAA5H,SAAAC,OACA6H,EAAA7H,KAAA2H,EAAA5H,SAAAC,MAEA,IAAA2H,EAAA5H,SAAAE,OACA4H,EAAA5H,KAAA0H,EAAA5H,SAAAE,MAEA,IAAA0H,EAAA5H,SAAAG,OACA2H,EAAA3H,KAAAyH,EAAA5H,SAAAG,MAdA6H,EAAArD,KAAA,EAgBAC,OAAAsD,EAAA,IAAAtD,CAAAkD,GAhBA,QAgBAC,EAhBAC,EAAAlD,MAiBAyC,SACAK,EAAAjI,QAAAoI,EAAAR,QACAK,EAAAxH,MAAA2H,EAAA3H,OAEAwH,EAAAlC,SAAA8B,MAAA,WArBA,wBAAAQ,EAAAhD,SAAA6C,EAAAD,KAAA1D,IAyBAiE,sBA/HA,SA+HAjD,GACA9F,KAAAQ,KAAAsF,EACA9F,KAAAuI,cAGAS,mBApIA,SAoIAlD,GACA9F,KAAAQ,KAAA,EACAR,KAAAS,SAAAqF,EACA9F,KAAAuI,cAGAU,WA1IA,SA0IA9F,GAAA,IAKA+F,EALAC,EAAAnJ,KACAiG,QAAAC,IAAA/C,GACAnD,KAAAoJ,KAAA,EAEApJ,KAAAkB,cAAAiC,EACAA,EAAA0D,SAAAqC,EAAApE,IAAAC,EAAAC,EAAAC,KAAA,SAAAoE,EAAAtC,GAAA,IAAA9E,EAAAqH,EAAA,OAAAvE,EAAAC,EAAAI,KAAA,SAAAmE,GAAA,cAAAA,EAAAjE,KAAAiE,EAAAhE,MAAA,cACAtD,GACAuH,OAAAzC,EAAA0C,MAEAxD,QAAAC,IAAAjE,GAJAsH,EAAAhE,KAAA,EAKAC,OAAAsD,EAAA,KAAAtD,CAAAvD,GALA,OAKAqH,EALAC,EAAA7D,KAMAyD,EAAAzH,OAAA4H,EAAAnC,KACA,OAAAmC,EAAAnC,OACAgC,EAAAC,KAAA,EACAD,EAAA7C,UACAC,QAAA,eACAlE,KAAA,aAXA,wBAAAkH,EAAA3D,SAAAyD,EAAAF,MAAA,SAAAO,GAAA,OAAAR,EAAA7B,MAAArH,KAAAsH,eAiBAqC,SAhKA,WAgKA,IAAAC,EAAA5J,KAAA,OAAA8E,IAAAC,EAAAC,EAAAC,KAAA,SAAA4E,IAAA,OAAA9E,EAAAC,EAAAI,KAAA,SAAA0E,GAAA,cAAAA,EAAAxE,KAAAwE,EAAAvE,MAAA,OACA,OAAAqE,EAAAlI,OACAkI,EAAAtD,UACAC,QAAA,eACAlE,KAAA,aAGAuH,EAAA3J,SAAA,EACA,IAAA2J,EAAA1I,eAAA6I,IAAAH,EAAA1I,eAAAmF,OAAA,GACAuD,EAAA3J,SAAA,EAGA2J,EAAAI,QAAAC,MACAC,KAAA,iBACAC,OACA9H,KAAA,MACA+H,MAAAR,EAAA1I,mBAIA0I,EAAAtD,SAAA8B,MAAA,YACAwB,EAAA3J,SAAA,IArBA,wBAAA6J,EAAAlE,SAAAiE,EAAAD,KAAA9E,IA0BAuF,OA1LA,SA0LAlH,GACA,IAAArD,OAAA,EAMA,OALAE,KAAAoB,SAAAyF,QAAA,SAAAE,GACAA,EAAAzF,IAAA6B,EAAAS,WACA9D,EAAAiH,EAAA1F,MAGAvB,GAGAwK,OApMA,SAoMAnH,GACA,IAAArD,OAAA,EAMA,OALAE,KAAAuB,SAAAsF,QAAA,SAAAE,GACAA,EAAAzF,IAAA6B,EAAAS,WACA9D,EAAAiH,EAAA1F,MAGAvB,GAEAyE,OA7MA,WA6MA,IAAAgG,EAAAvK,KAAA,OAAA8E,IAAAC,EAAAC,EAAAC,KAAA,SAAAuF,IAAA,IAAAvI,EAAAnC,EAAA,OAAAiF,EAAAC,EAAAI,KAAA,SAAAqF,GAAA,cAAAA,EAAAnF,KAAAmF,EAAAlF,MAAA,cACAtD,GACAyI,KAAA,IAFAD,EAAAlF,KAAA,EAIAC,OAAAsD,EAAA,EAAAtD,CAAAvD,GAJA,OAIAnC,EAJA2K,EAAA/E,KAKAO,QAAAC,IAAApG,GACAyK,EAAAI,OAAA7K,OAAA6K,OANA,wBAAAF,EAAA7E,SAAA4E,EAAAD,KAAAzF,IASA8F,WAtNA,SAsNAzH,EAAA4D,GAAA,IAAA8D,EAAA7K,KAAA,OAAA8E,IAAAC,EAAAC,EAAAC,KAAA,SAAA6F,IAAA,IAAAC,EAAAC,EAAAL,EAAA,OAAA5F,EAAAC,EAAAI,KAAA,SAAA6F,GAAA,cAAAA,EAAA3F,KAAA2F,EAAA1F,MAAA,UAEA,MAAAwB,EAFA,CAAAkE,EAAA1F,KAAA,gBAGAsF,EAAA5K,SAAA,EAHAgL,EAAA1F,KAAA,EAIAC,OAAA0B,EAAA,EAAA1B,EACAyB,KAAA9D,EAAA8D,OALA,cAIA8D,EAJAE,EAAAvF,KAOAO,QAAAC,IAAA6E,GAPAE,EAAA1F,KAAA,EAQAC,OAAA0B,EAAA,EAAA1B,EACA0F,MAAA/H,EAAA8D,OATA,OAQA+D,EARAC,EAAAvF,KAWAqF,GACAF,EAAA5K,SAAA,EACA4K,EAAAb,QAAAC,MACAC,KAAA,iBACAC,OACA9H,KAAA,SACA+H,MAAAW,EACAI,KAAAH,MAIAH,EAAAvE,SAAA8B,MAAA,UAtBA6C,EAAA1F,KAAA,iBAwBA,MAAAwB,IACA4D,EAAAE,EAAAF,OACA,IAAAE,EAAAF,aAAAS,GAAAP,EAAAF,OACAE,EAAAvE,SAAA8B,MAAA,cAEAyC,EAAAb,QAAAC,MACAC,KAAA,iBACAC,OACAkB,GAAA,SACAC,KAAAnI,EACAwH,SACAY,KAAApI,EAAAoI,SAnCA,yBAAAN,EAAArF,SAAAkF,EAAAD,KAAA/F,IA2CAJ,KAjQA,WAiQA,IAAA8G,EAAAxL,KAAA,OAAA8E,IAAAC,EAAAC,EAAAC,KAAA,SAAAwG,IAAA,IAAAC,EAAAC,EAAAC,EAAAN,EAAA,OAAAvG,EAAAC,EAAAI,KAAA,SAAAyG,GAAA,cAAAA,EAAAvG,KAAAuG,EAAAtG,MAAA,cAAAsG,EAAAtG,KAAA,EACAC,OAAAsD,EAAA,IAAAtD,GADA,cACAkG,EADAG,EAAAnG,KAEA8F,EAAAM,OAAAJ,EACAC,KACAH,EAAAM,OAAAjF,QAAA,SAAAE,GACA,IAAAgF,KACAP,EAAAM,OAAAjF,QAAA,SAAAmF,GACAjF,EAAAkF,KAAAD,EAAAE,OACAH,EAAA9B,KAAA+B,GACAjF,EAAAgF,sBAGAJ,EAAA1B,KAAAlD,KAEA6E,KAdAC,EAAAtG,KAAA,EAeAC,OAAAsD,EAAA,EAAAtD,GAfA,OAgBA,KADA8F,EAfAO,EAAAnG,MAgBAwG,MACAP,EAAA9E,QAAA,SAAAE,GACA,IAAAA,EAAAmF,MACAN,EAAA3B,KAAAlD,KAIA,IAAAuE,EAAAY,MACAP,EAAA9E,QAAA,SAAAE,GACAd,QAAAC,IAAAa,GACAA,EAAAmF,MAAAZ,EAAAY,MACAN,EAAA3B,KAAAlD,KAIA6E,EAAA,GAAAG,iBAAAlF,QAAA,SAAAE,GACAyE,EAAA/J,aAAAwI,KAAAlD,KAhCA,yBAAA8E,EAAAjG,SAAA6F,EAAAD,KAAA1G,IAoCAqH,eArSA,SAqSApF,GACAd,QAAAC,IAAAa,QACAqE,GAAArE,IACA/G,KAAAY,SAAAwL,GAAArF,EAAAsF,KAAA,QAIAC,UC7sBeC,GADEC,OAFjB,WAA0B,IAAAC,EAAAzM,KAAa0M,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,aAAa5K,KAAA,UAAA6K,QAAA,YAAAlL,MAAA4K,EAAA,QAAAO,WAAA,YAA4EC,YAAA,WAAuBL,EAAA,OAAYK,YAAA,cAAwBL,EAAA,cAAmBM,OAAO9K,QAAAqK,EAAArK,QAAAH,OAAAwK,EAAAxK,QAA0CkL,IAAKC,UAAAX,EAAAjF,gBAA8BiF,EAAAY,GAAA,KAAAT,EAAA,WAA4BK,YAAA,KAAAC,OAAwBI,QAAA,EAAAC,MAAAd,EAAApM,WAAAmN,KAAA,YAAsDZ,EAAA,gBAAqBK,YAAA,OAAiBL,EAAA,aAAkBM,OAAO7K,KAAA,SAAAmL,KAAA,SAAA5K,KAAA,wBAA8DuK,IAAKM,MAAAhB,EAAAtG,WAAqBsG,EAAAY,GAAA,kCAAAZ,EAAAY,GAAA,KAAAT,EAAA,gBAA0EK,YAAA,OAAiBL,EAAA,aAAkBM,OAAO7K,KAAA,UAAAmL,KAAA,SAAA5K,KAAA,gBAAuDuK,IAAKM,MAAAhB,EAAAlE,cAAwBkE,EAAAY,GAAA,0CAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA+EM,OAAOQ,eAAA,EAAAC,eAAA,KAAAC,WAAA,EAAAC,UAAApB,EAAAtL,SAAAiB,QAAAqK,EAAA3J,aAAAY,aAAA+I,EAAA/I,aAAAK,iBAAA0I,EAAA1I,iBAAA+J,gBAAA,EAAAC,YAAAtB,EAAA/L,MAAAD,SAAAgM,EAAA9L,UAAAqN,WAAAvB,EAAAxL,QAAuRkM,IAAKvC,WAAA6B,EAAA7B,WAAA5E,UAAAyG,EAAAzG,UAAAD,oBAAA0G,EAAA1G,oBAAAF,iBAAA4G,EAAA5G,oBAA6I4G,EAAAY,GAAA,KAAAT,EAAA,aAA8BM,OAAOe,MAAA,SAAAC,wBAAA,EAAAC,QAAA1B,EAAAnM,cAAA0D,MAAA,OAAwFmJ,IAAKiB,iBAAA,SAAAC,GAAkC5B,EAAAnM,cAAA+N,GAAyBzJ,MAAA6H,EAAA7H,SAAoBgI,EAAA,OAAYK,YAAA,oBAA8BL,EAAA,SAAcM,OAAOoB,IAAA,MAAU7B,EAAAY,GAAA,WAAAZ,EAAAY,GAAA,KAAAT,EAAA,YAA+CK,YAAA,SAAAC,OAA4BqB,UAAA,GAAAjM,YAAA,QAAoCiL,OAAQ1L,MAAA4K,EAAA7L,SAAA,KAAA4N,SAAA,SAAAC,GAAmDhC,EAAAiC,KAAAjC,EAAA7L,SAAA,OAAA6N,IAAoCzB,WAAA,mBAA6BP,EAAAY,GAAA,KAAAT,EAAA,SAA0BM,OAAOoB,IAAA,MAAU7B,EAAAY,GAAA,WAAAZ,EAAAY,GAAA,KAAAT,EAAA,YAA+CK,YAAA,SAAAC,OAA4BqB,UAAA,GAAAjM,YAAA,QAAoCiL,OAAQ1L,MAAA4K,EAAA7L,SAAA,KAAA4N,SAAA,SAAAC,GAAmDhC,EAAAiC,KAAAjC,EAAA7L,SAAA,OAAA6N,IAAoCzB,WAAA,mBAA6BP,EAAAY,GAAA,KAAAT,EAAA,SAA0BM,OAAOoB,IAAA,MAAU7B,EAAAY,GAAA,WAAAZ,EAAAY,GAAA,KAAAT,EAAA,YAA+CK,YAAA,SAAAC,OAA4BqB,UAAA,GAAAjM,YAAA,QAAoCiL,OAAQ1L,MAAA4K,EAAA7L,SAAA,KAAA4N,SAAA,SAAAC,GAAmDhC,EAAAiC,KAAAjC,EAAA7L,SAAA,OAAA6N,IAAoCzB,WAAA,mBAA6BP,EAAAY,GAAA,KAAAT,EAAA,aAA8BK,YAAA,eAAAC,OAAkC7K,KAAA,UAAAO,KAAA,kBAAyCuK,IAAKM,MAAAhB,EAAApE,YAAsBoE,EAAAY,GAAA,QAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA6CK,YAAA,YAAAC,OAA+BQ,eAAA,EAAAiB,YAAA,MAAAf,WAAA,EAAAC,UAAApB,EAAAlM,QAAA6B,QAAAqK,EAAAvI,aAAA0K,qBAAA,EAAAlL,aAAA+I,EAAAtI,kBAAA2J,gBAAA,EAAAC,YAAAtB,EAAAjM,KAAAC,SAAAgM,EAAAhM,SAAAuN,WAAAvB,EAAAzL,OAA0QmM,IAAKpH,oBAAA0G,EAAA1D,sBAAAlD,iBAAA4G,EAAAzD,mBAAAhD,UAAAyG,EAAAxD,eAAsH,GAAAwD,EAAAY,GAAA,KAAAT,EAAA,QAA6BK,YAAA,gBAAAC,OAAmC2B,KAAA,UAAgBA,KAAA,WAAejC,EAAA,aAAkBM,OAAO7K,KAAA,WAAiB8K,IAAKM,MAAA,SAAAY,GAAyB,OAAA5B,EAAA9C,eAAwB8C,EAAAY,GAAA,SAAAZ,EAAAY,GAAA,KAAAT,EAAA,aAA8CM,OAAO7K,KAAA,WAAiB8K,IAAKM,MAAAhB,EAAA7H,SAAmB6H,EAAAY,GAAA,sBAE/rGyB,oBCCjB,IAcAC,EAdyBC,EAAQ,OAcjCC,CACE1P,EACAgN,GATF,EAVA,SAAA2C,GACEF,EAAQ,SAaV,kBAEA,MAUeG,EAAA,QAAAJ,EAAiB", "file": "js/195.838a27ad2ce8e7abfdd4.js", "sourcesContent": ["<template>\r\n  <div class=\"bg_con\" v-loading=\"loading\">\r\n    <div class=\"container\">\r\n      <BaseHeader :columns=\"columns\" :params=\"params\" @handleBtn=\"handleBtnAll\"></BaseHeader>\r\n      <el-form :inline=\"true\" :model=\"formInline\" size=\"medium\" class=\"fr\">\r\n        <el-form-item class=\"fr\">\r\n          <el-button type=\"danger\" size=\"medium\" @click=\"shanchu\" icon=\"el-icon-delete-solid\">\r\n            删除\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item class=\"fr\">\r\n          <el-button type=\"success\" size=\"medium\" @click=\"sendApplay\" icon=\"el-icon-plus\">\r\n            外出携带申请\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!-- 查询条件以及操作按钮end -->\r\n      <!-- 涉密人员任用审查列表start -->\r\n      <BaseTable :showSelection=true :selectionWidth=\"'55'\" :showIndex=true :tableData=\"smryList\" :columns=\"tableColumns\"\r\n        :handleColumn=\"handleColumn\" :handleColumnProp=\"handleColumnProp\" :showPagination=true :currentPage=\"page1\"\r\n        :pageSize=\"pageSize1\" :totalCount=\"total1\" @operateBtn=\"operateBtn\" @selectBtn=\"selectBtn\"\r\n        @handleCurrentChange=\"handleCurrentChange\" @handleSizeChange=\"handleSizeChange\">\r\n      </BaseTable>\r\n      <!-- 涉密人员任用审查列表end -->\r\n      <!-- 发起申请弹框start -->\r\n      <el-dialog title=\"选择载体信息\" :close-on-click-modal=\"false\" :visible.sync=\"dialogVisible\" width=\"70%\" @close=\"close\">\r\n        <div class=\"dlFqsqContainer\">\r\n          <label for=\"\">载体编号:</label>\r\n          <el-input class=\"input2\" v-model=\"ryChoose.ztbh\" clearable placeholder=\"载体编号\"></el-input>\r\n          <label for=\"\">载体名称:</label>\r\n          <el-input class=\"input2\" v-model=\"ryChoose.ztmc\" clearable placeholder=\"载体名称\"></el-input>\r\n          <label for=\"\">项目编号:</label>\r\n          <el-input class=\"input2\" v-model=\"ryChoose.xmbh\" clearable placeholder=\"项目编号\"></el-input>\r\n          <el-button class=\"searchButton\" type=\"primary\" icon=\"el-icon-search\" @click=\"searchRy\">查询</el-button>\r\n          <BaseTable class=\"baseTable\" :showSelection=\"true\" :tableHeight=\"'300'\" :showIndex=true :tableData=\"ryDatas\"\r\n            :columns=\"applyColumns\" :showSingleSelection=\"false\" :handleColumn=\"handleColumnApply\" :showPagination=true\r\n            :currentPage=\"page\" :pageSize=\"pageSize\" :totalCount=\"total\" @handleCurrentChange=\"handleCurrentChangeRy\"\r\n            @handleSizeChange=\"handleSizeChangeRy\" @selectBtn=\"selectList\">\r\n          </BaseTable>\r\n        </div>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitRy()\">保 存</el-button>\r\n          <el-button type=\"warning\" @click=\"close\">关 闭</el-button>\r\n        </span>\r\n      </el-dialog>\r\n      <!-- 发起申请弹框end -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  getRyscInfo,\r\n  getZzjgList,\r\n  getLoginInfo,\r\n  getFwdyidByFwlx,\r\n  getZpBySmryid,\r\n  selectZtglPage,\r\n  verifySfzzsp\r\n} from '../../../api/index'\r\nimport {\r\n  getUserInfo,\r\n} from '../../../api/dwzc'\r\nimport {\r\n  removeZtXdwc,\r\n  selectZtXdwcPage,\r\n  selectZtglZtqdPage,\r\n  getZtXdwcByJlid,\r\n  getZtqdListByYjlid,\r\n} from '../../../api/ztwcxd'\r\nimport BaseHeader from '../../components/common/baseHeader.vue'\r\nimport BaseTable from '../../components/common/baseTable.vue'\r\nexport default {\r\n  components: {\r\n    BaseHeader,\r\n    BaseTable\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      // table 行样式\r\n      headerCellStyle: {\r\n        background: '#EEF7FF',\r\n        color: '#4D91F8'\r\n      },\r\n      formInline: {}, // 搜索条件\r\n      dialogVisible: false, // 发起申请弹框\r\n      ryDatas: [], // 弹框人员选择\r\n      page: 1, // 弹框人员当前页\r\n      pageSize: 5, // 弹框人员每页条数\r\n      page1: 1, // 弹框人员当前页\r\n      pageSize1: 10, // 弹框人员每页条数\r\n      // 弹框人员选择条件\r\n      ryChoose: {\r\n        'ztbh': '',\r\n        'ztmc': '',\r\n        'xmbh': ''\r\n      },\r\n      total: 0, // 弹框人员总数\r\n      total1: 0, // 弹框人员总数\r\n      radioIdSelect: '', // 弹框人员单选\r\n      smryList: [], //页面数据\r\n      scjtlist: [ //审查状态数据\r\n        {\r\n          mc: \"审批中\",\r\n          id: 0\r\n        },\r\n        {\r\n          mc: \"通过\",\r\n          id: 1\r\n        },\r\n        {\r\n          mc: \"已驳回\",\r\n          id: 2\r\n        },\r\n        {\r\n          mc: \"草稿\",\r\n          id: 3\r\n        }\r\n      ],\r\n      dqztlist: [ //当前状态数据\r\n        {\r\n          mc: \"审批中\",\r\n          id: 0\r\n        },\r\n        {\r\n          mc: \"已结束\",\r\n          id: 1\r\n        },\r\n        {\r\n          mc: \"已驳回\",\r\n          id: 2\r\n        },\r\n        {\r\n          mc: \"草稿\",\r\n          id: 3\r\n        }\r\n      ],\r\n      rowdata: [], //列表选中的值\r\n      regionOption: [], // 部门下拉\r\n      pdztsp: '',\r\n      regionParams: {\r\n        label: 'label', //这里可以配置你们后端返回的属性\r\n        value: 'label',\r\n        children: 'childrenRegionVo',\r\n        expandTrigger: 'click',\r\n        checkStrictly: true\r\n      }, //地域信息配置参数\r\n      // 查询条件\r\n      params: {\r\n        name: '',\r\n        tmjssj: ''\r\n      },\r\n      // 查询条件以及功能按钮\r\n      columns: [{\r\n        type: 'searchInput',\r\n        name: '申请人',\r\n        value: 'name',\r\n        placeholder: '申请人',\r\n      },\r\n      {\r\n        type: 'dataRange',\r\n        name: '审查时间',\r\n        value: 'tmjssj',\r\n        startPlaceholder: '审查起始时间',\r\n        rangeSeparator: '至',\r\n        endPlaceholder: '审查结束时间',\r\n        format: 'yyyy-MM-dd'\r\n      },\r\n      {\r\n        type: 'button',\r\n        name: '查询',\r\n        disabled: false,\r\n        icon: 'el-icon-search',\r\n        mold: 'primary'\r\n      },\r\n      {\r\n        type: 'button',\r\n        name: '重置',\r\n        disabled: false,\r\n        icon: 'el-icon-circle-close',\r\n        mold: 'warning'\r\n      }\r\n      ],\r\n      // table项\r\n      tableColumns: [\r\n        {\r\n          name: '申请人',\r\n          prop: 'xqr',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '申请人部门',\r\n          prop: 'szbm',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '载体名称',\r\n          prop: 'ztmc',\r\n          scopeType: 'text',\r\n          formatter: false,\r\n          showOverflowTooltip: true\r\n        },\r\n        {\r\n          name: '载体编号',\r\n          prop: 'ztbh',\r\n          scopeType: 'text',\r\n          formatter: false,\r\n          showOverflowTooltip: true\r\n        },\r\n        {\r\n          name: '审查时间',\r\n          prop: 'cjsj',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '审查结果',\r\n          prop: 'Lcfwslzt',\r\n          scopeType: 'text',\r\n          formatter: (row, column, cellValue, index) => {\r\n            const options = [\r\n              {\r\n                mc: \"审批中\",\r\n                id: 0\r\n              },\r\n              {\r\n                mc: \"通过\",\r\n                id: 1\r\n              },\r\n              {\r\n                mc: \"已驳回\",\r\n                id: 2\r\n              },\r\n              {\r\n                mc: \"草稿\",\r\n                id: 3\r\n              }\r\n            ]\r\n            const opt = options.find(d => d.id === cellValue)\r\n            return opt ? opt.mc : ''\r\n          }\r\n        },\r\n        {\r\n          name: '当前状态',\r\n          prop: 'Lcfwslzt',\r\n          scopeType: 'text',\r\n          formatter: (row, column, cellValue, index) => {\r\n            const options = [\r\n              {\r\n                mc: \"审批中\",\r\n                id: 0\r\n              },\r\n              {\r\n                mc: \"已结束\",\r\n                id: 1\r\n              },\r\n              {\r\n                mc: \"已驳回\",\r\n                id: 2\r\n              },\r\n              {\r\n                mc: \"草稿\",\r\n                id: 3\r\n              }\r\n            ]\r\n            const opt = options.find(d => d.id === cellValue)\r\n            return opt ? opt.mc : ''\r\n          }\r\n        }\r\n      ],\r\n      // table操作按钮\r\n      handleColumn: [\r\n        {\r\n          name: '编辑',\r\n          disabled: false,\r\n          show: true,\r\n          formatter: (row, column) => {\r\n            if (row.Lcfwslzt == 3 && row.cjrid == this.loginName) {\r\n              return '编辑'\r\n            } else if (row.Lcfwslzt == 0 || row.Lcfwslzt == 1 || row.Lcfwslzt == 2) {\r\n              return '查看'\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      // 表格的操作\r\n      handleColumnProp: {\r\n        label: '操作',\r\n        width: '230',\r\n        align: 'left'\r\n      },\r\n      // 发起申请table\r\n      applyColumns: [\r\n        {\r\n          name: '载体编号',\r\n          prop: 'ztbh',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '项目编号',\r\n          prop: 'xmbh',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '载体类型',\r\n          prop: 'lx',\r\n          scopeType: 'text',\r\n          formatter: (row, column, cellValue, index) => {\r\n            const options = [\r\n              {\r\n                mc: \"纸介质\",\r\n                id: 1\r\n              },\r\n              {\r\n                mc: \"光介质\",\r\n                id: 2\r\n              },\r\n              {\r\n                mc: \"电池介质\",\r\n                id: 3\r\n              },\r\n              {\r\n                mc: \"其他介质\",\r\n                id: 4\r\n              }\r\n            ]\r\n            const opt = options.find(d => d.id === cellValue)\r\n            return opt ? opt.mc : ''\r\n          }\r\n        },\r\n        {\r\n          name: '密级',\r\n          prop: 'smmj',\r\n          scopeType: 'text',\r\n          formatter: (row, column, cellValue, index) => {\r\n            const options = [\r\n              {\r\n                mc: \"内部\",\r\n                id: 4\r\n              },\r\n              {\r\n                mc: \"绝密\",\r\n                id: 1\r\n              },\r\n              {\r\n                mc: \"机密\",\r\n                id: 2\r\n              },\r\n              {\r\n                mc: \"秘密\",\r\n                id: 3\r\n              }\r\n            ]\r\n            const opt = options.find(d => d.id === cellValue)\r\n            return opt ? opt.mc : ''\r\n          }\r\n        },\r\n        {\r\n          name: '保密期限',\r\n          prop: 'bmqx',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '页数/大小',\r\n          prop: 'ys',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n        {\r\n          name: '份数',\r\n          prop: 'fs',\r\n          scopeType: 'text',\r\n          formatter: false\r\n        },\r\n      ],\r\n      handleColumnApply: [],\r\n      // 查询条件以及功能按钮\r\n      smryColumns: [{\r\n        type: 'cascader',\r\n        name: '部门',\r\n        value: 'bmmc',\r\n        placeholder: '请选择部门',\r\n      }, {\r\n        type: 'searchInput',\r\n        name: '姓名',\r\n        value: 'name',\r\n        placeholder: '姓名',\r\n      },\r\n      {\r\n        type: 'button',\r\n        name: '查询',\r\n        disabled: false,\r\n        icon: 'el-icon-search',\r\n        mold: 'primary'\r\n      },\r\n      {\r\n        type: 'button',\r\n        name: '重置',\r\n        disabled: false,\r\n        icon: 'el-icon-circle-close',\r\n        mold: 'warning'\r\n      }\r\n      ],\r\n      // 当前登录人的用户名\r\n      loginName: ''\r\n    }\r\n  },\r\n  computed: {},\r\n  mounted() {\r\n    this.onfwid()\r\n    this.getLoginYhm() // 获取当前登录人姓名\r\n    this.rysclist() // 任用审查数据获取\r\n    this.zzjg() // 获取组织机构所有部门下拉\r\n  },\r\n  methods: {\r\n    close(){\r\n      this.ryChoose.ztbh = ''\r\n      this.ryChoose.ztmc = ''\r\n      this.ryChoose.xmbh = ''\r\n      this.dialogVisible = false\r\n    },\r\n    // 获取当前登录人姓名\r\n    async getLoginYhm() {\r\n      let userInfo = await getUserInfo()\r\n      this.loginName = userInfo.yhm\r\n    },\r\n    //分页\r\n    handleSizeChange(val) {\r\n      this.page1 = 1\r\n      this.pageSize1 = val\r\n      this.rysclist()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page1 = val\r\n      this.rysclist()\r\n    },\r\n    // table复选集合\r\n    selectBtn(row) {\r\n      this.rowdata = row\r\n      console.log(row);\r\n    },\r\n    //删除\r\n    shanchu() {\r\n      if (this.rowdata.length == 0) {\r\n        this.$message({\r\n          message: '未选择想要删除的数据',\r\n          type: 'warning'\r\n        });\r\n      } else {\r\n        this.$confirm('此操作将永久删除该申请, 是否继续?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.rowdata.forEach(async (item) => {\r\n            let params = {\r\n              jlid: item.jlid,\r\n              ztbh: item.ztbh,\r\n            }\r\n            let res = await removeZtXdwc(params)\r\n            if (res.code == 10000) {\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.rysclist()\r\n            }\r\n          })\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          });\r\n        });\r\n      }\r\n    },\r\n    // 点击公共头部按钮事件\r\n    handleBtnAll(parameter, item) {\r\n      if (item.name == '查询') {\r\n        this.params = JSON.parse(JSON.stringify(parameter))\r\n        this.page1 = 1\r\n        this.rysclist()\r\n      } else if (item.name == '重置') {\r\n        this.params = {\r\n          name: '',\r\n          tmjssj: ''\r\n        }\r\n      }\r\n    },\r\n    //任用审查数据获取\r\n    async rysclist(parameter) {\r\n      let params = {\r\n        xm: this.params.name,\r\n        page: this.page1,\r\n        pageSize: this.pageSize1\r\n      }\r\n      if (this.params.tmjssj != null) {\r\n        params.kssj = this.params.tmjssj[0]\r\n        params.jssj = this.params.tmjssj[1]\r\n      }\r\n      let data = await selectZtXdwcPage(params)\r\n      if (data.records) {\r\n        this.smryList = data.records\r\n        this.total1 = data.total\r\n      } else {\r\n        this.$message.error('数据获取失败！')\r\n      }\r\n    },\r\n\r\n    // 人员搜索\r\n    searchRy() {\r\n      this.tableKey++\r\n      this.page = 1\r\n      this.sendApplay()\r\n    },\r\n    // 发起申请\r\n    async sendApplay() {\r\n      this.dialogVisible = true\r\n      let param = {\r\n        'page': this.page,\r\n        'pageSize': this.pageSize,\r\n        'zt': 1,\r\n      }\r\n      if (this.ryChoose.ztbh != '') {\r\n        param.ztbh = this.ryChoose.ztbh\r\n      }\r\n      if (this.ryChoose.ztmc != '') {\r\n        param.ztmc = this.ryChoose.ztmc\r\n      }\r\n      if (this.ryChoose.xmbh != '') {\r\n        param.xmbh = this.ryChoose.xmbh\r\n      }\r\n      let resData = await selectZtglPage(param)\r\n      if (resData.records) {\r\n        this.ryDatas = resData.records\r\n        this.total = resData.total\r\n      } else {\r\n        this.$message.error('数据获取失败！')\r\n      }\r\n\r\n    },\r\n    handleCurrentChangeRy(val) {\r\n      this.page = val\r\n      this.sendApplay()\r\n    },\r\n    //列表分页--更改每页显示个数\r\n    handleSizeChangeRy(val) {\r\n      this.page = 1\r\n      this.pageSize = val\r\n      this.sendApplay()\r\n    },\r\n\r\n    selectList(row) {\r\n      console.log(row);\r\n      this.ztwf = 0\r\n\r\n      this.radioIdSelect = row\r\n      row.forEach(async (item) => {\r\n        let params = {\r\n          smryid: item.ztid\r\n        }\r\n        console.log(params);\r\n        let data1 = await verifySfzzsp(params)\r\n        this.pdztsp = data1.code\r\n        if (data1.code == 80003) {\r\n          this.ztwf = 1\r\n          this.$message({\r\n            message: \"载体存在正在审批中的流程\",\r\n            type: 'warning'\r\n          });\r\n        }\r\n      })\r\n    },\r\n    // 选择人员提交\r\n    async submitRy() {\r\n      if (this.pdztsp == 80003) {\r\n        this.$message({\r\n          message: \"载体存在正在审批中的流程\",\r\n          type: 'warning'\r\n        });\r\n      } else {\r\n        this.loading = true\r\n        if (this.radioIdSelect != '' && Object.keys(this.radioIdSelect).length > 0) {\r\n          this.loading = false\r\n          // let zp = await getZpBySmryid({ smryid: this.radioIdSelect.smryid })\r\n          // this.radioIdSelect.zp = zp\r\n          this.$router.push({\r\n            path: '/ztwcxdscTable',\r\n            query: {\r\n              type: 'add',\r\n              datas: this.radioIdSelect\r\n            }\r\n          })\r\n        } else {\r\n          this.$message.error('请选择载体信息！')\r\n          this.loading = false\r\n        }\r\n      }\r\n    },\r\n    //审查状态数据回想\r\n    scjgsj(row) {\r\n      let data;\r\n      this.scjtlist.forEach(item => {\r\n        if (item.id == row.Lcfwslzt) {\r\n          data = item.mc\r\n        }\r\n      })\r\n      return data\r\n    },\r\n    //当前状态数据回想\r\n    dqztsj(row) {\r\n      let data;\r\n      this.dqztlist.forEach(item => {\r\n        if (item.id == row.Lcfwslzt) {\r\n          data = item.mc\r\n        }\r\n      })\r\n      return data\r\n    },\r\n    async onfwid() {\r\n      let params = {\r\n        fwlx: 23\r\n      }\r\n      let data = await getFwdyidByFwlx(params)\r\n      console.log(data);\r\n      this.fwdyid = data.data.fwdyid\r\n    },\r\n    // 功能操作按钮\r\n    async operateBtn(row, item) {\r\n      // 编辑方法\r\n      if (item == '编辑') {\r\n        this.loading = true\r\n        let res = await getZtXdwcByJlid({\r\n          'jlid': row.jlid\r\n        })\r\n        console.log(res)\r\n        let res1 = await getZtqdListByYjlid({\r\n          'yjlid': row.jlid\r\n        })\r\n        if (res) {\r\n          this.loading = false\r\n          this.$router.push({\r\n            path: '/ztwcxdscTable',\r\n            query: {\r\n              type: 'update',\r\n              datas: res,\r\n              ztqs: res1\r\n            }\r\n          })\r\n        } else {\r\n          this.$message.error('任务不匹配！')\r\n        }\r\n      } else if (item == '查看') {  // 查看方法\r\n        let fwdyid = this.fwdyid\r\n        if (this.fwdyid == '' || this.fwdyid == undefined) {\r\n          this.$message.error('请到流程管理进行配置');\r\n        } else {\r\n          this.$router.push({\r\n            path: '/ztwcxdblxxscb',\r\n            query: {\r\n              lx: '载体外出携带',\r\n              list: row,\r\n              fwdyid: fwdyid,\r\n              slid: row.slid\r\n            }\r\n          })\r\n        }\r\n\r\n      }\r\n    },\r\n    //全部组织机构List\r\n    async zzjg() {\r\n      let zzjgList = await getZzjgList()\r\n      this.zzjgmc = zzjgList\r\n      let shu = []\r\n      this.zzjgmc.forEach(item => {\r\n        let childrenRegionVo = []\r\n        this.zzjgmc.forEach(item1 => {\r\n          if (item.bmm == item1.fbmm) {\r\n            childrenRegionVo.push(item1)\r\n            item.childrenRegionVo = childrenRegionVo\r\n          }\r\n        });\r\n        shu.push(item)\r\n      })\r\n      let shuList = []\r\n      let list = await getLoginInfo()\r\n      if (list.fbmm == '') {\r\n        shu.forEach(item => {\r\n          if (item.fbmm == '') {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      if (list.fbmm != '') {\r\n        shu.forEach(item => {\r\n          console.log(item);\r\n          if (item.fbmm == list.fbmm) {\r\n            shuList.push(item)\r\n          }\r\n        })\r\n      }\r\n      shuList[0].childrenRegionVo.forEach(item => {\r\n        this.regionOption.push(item)\r\n      })\r\n    },\r\n    // 发起申请选择人员 人员下拉\r\n    bmSelectChange(item) {\r\n      console.log(item)\r\n      if (item != undefined) {\r\n        this.ryChoose.bm = item.join('/')\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.fl {\r\n  float: left;\r\n}\r\n\r\n.fr {\r\n  float: right;\r\n}\r\n\r\n.container {\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n  height: 100%;\r\n  /* box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13); */\r\n  border-radius: 8px;\r\n}\r\n\r\n.bg_con {\r\n  width: 100%;\r\n  height: calc(100% - 38px);\r\n}\r\n\r\n/deep/.mhcx .el-form-item {\r\n  margin-top: 5px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.widthw {\r\n  width: 6vw;\r\n}\r\n\r\n/* 发起申请弹框 */\r\n.dlFqsqContainer {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.dlFqsqContainer label {\r\n  font-weight: 700;\r\n}\r\n\r\n.dlFqsqContainer .input1,\r\n.dlFqsqContainer .input2 {\r\n  width: 150px;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dlFqsqContainer .searchButton {\r\n  margin-left: 10px;\r\n}\r\n\r\n>>>.dlFqsqContainer .input1 .el-input__inner,\r\n>>>.dlFqsqContainer .input2 .el-input__inner {\r\n  height: 40px;\r\n}\r\n\r\n.dlFqsqContainer .input1 {\r\n  margin-right: 20px;\r\n}\r\n\r\n.dlFqsqContainer .tb-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dlFqsqContainer .paginationContainer {\r\n  margin-top: 20px;\r\n}\r\n\r\n.spImg {\r\n  width: 15px;\r\n}\r\n\r\n.baseTable {\r\n  margin-top: 20px;\r\n  /* height: 400px!important; */\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/rcgz/ztwcxdsc.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],staticClass:\"bg_con\"},[_c('div',{staticClass:\"container\"},[_c('BaseHeader',{attrs:{\"columns\":_vm.columns,\"params\":_vm.params},on:{\"handleBtn\":_vm.handleBtnAll}}),_vm._v(\" \"),_c('el-form',{staticClass:\"fr\",attrs:{\"inline\":true,\"model\":_vm.formInline,\"size\":\"medium\"}},[_c('el-form-item',{staticClass:\"fr\"},[_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"medium\",\"icon\":\"el-icon-delete-solid\"},on:{\"click\":_vm.shanchu}},[_vm._v(\"\\n          删除\\n        \")])],1),_vm._v(\" \"),_c('el-form-item',{staticClass:\"fr\"},[_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"medium\",\"icon\":\"el-icon-plus\"},on:{\"click\":_vm.sendApplay}},[_vm._v(\"\\n          外出携带申请\\n        \")])],1)],1),_vm._v(\" \"),_c('BaseTable',{attrs:{\"showSelection\":true,\"selectionWidth\":'55',\"showIndex\":true,\"tableData\":_vm.smryList,\"columns\":_vm.tableColumns,\"handleColumn\":_vm.handleColumn,\"handleColumnProp\":_vm.handleColumnProp,\"showPagination\":true,\"currentPage\":_vm.page1,\"pageSize\":_vm.pageSize1,\"totalCount\":_vm.total1},on:{\"operateBtn\":_vm.operateBtn,\"selectBtn\":_vm.selectBtn,\"handleCurrentChange\":_vm.handleCurrentChange,\"handleSizeChange\":_vm.handleSizeChange}}),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":\"选择载体信息\",\"close-on-click-modal\":false,\"visible\":_vm.dialogVisible,\"width\":\"70%\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event},\"close\":_vm.close}},[_c('div',{staticClass:\"dlFqsqContainer\"},[_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"载体编号:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"载体编号\"},model:{value:(_vm.ryChoose.ztbh),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"ztbh\", $$v)},expression:\"ryChoose.ztbh\"}}),_vm._v(\" \"),_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"载体名称:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"载体名称\"},model:{value:(_vm.ryChoose.ztmc),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"ztmc\", $$v)},expression:\"ryChoose.ztmc\"}}),_vm._v(\" \"),_c('label',{attrs:{\"for\":\"\"}},[_vm._v(\"项目编号:\")]),_vm._v(\" \"),_c('el-input',{staticClass:\"input2\",attrs:{\"clearable\":\"\",\"placeholder\":\"项目编号\"},model:{value:(_vm.ryChoose.xmbh),callback:function ($$v) {_vm.$set(_vm.ryChoose, \"xmbh\", $$v)},expression:\"ryChoose.xmbh\"}}),_vm._v(\" \"),_c('el-button',{staticClass:\"searchButton\",attrs:{\"type\":\"primary\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.searchRy}},[_vm._v(\"查询\")]),_vm._v(\" \"),_c('BaseTable',{staticClass:\"baseTable\",attrs:{\"showSelection\":true,\"tableHeight\":'300',\"showIndex\":true,\"tableData\":_vm.ryDatas,\"columns\":_vm.applyColumns,\"showSingleSelection\":false,\"handleColumn\":_vm.handleColumnApply,\"showPagination\":true,\"currentPage\":_vm.page,\"pageSize\":_vm.pageSize,\"totalCount\":_vm.total},on:{\"handleCurrentChange\":_vm.handleCurrentChangeRy,\"handleSizeChange\":_vm.handleSizeChangeRy,\"selectBtn\":_vm.selectList}})],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.submitRy()}}},[_vm._v(\"保 存\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"warning\"},on:{\"click\":_vm.close}},[_vm._v(\"关 闭\")])],1)])],1)])}\nvar staticRenderFns = []\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-3a330cbf\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/rcgz/ztwcxdsc.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-3a330cbf\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./ztwcxdsc.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ztwcxdsc.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./ztwcxdsc.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-3a330cbf\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./ztwcxdsc.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-3a330cbf\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/rcgz/ztwcxdsc.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}